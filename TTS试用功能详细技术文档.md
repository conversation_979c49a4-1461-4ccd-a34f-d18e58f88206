# TTS试用功能详细技术文档

## 概述

本文档详细介绍基于讯飞TTS SDK的试用功能实现，包括核心机制、平台差异以及开发者操作指南。该系统提供安全可靠的3天试用期限，通过多重验证机制确保功能的完整性和安全性。

## 核心功能详解

### 1. 3天试用期限

#### 实现机制
- **试用时长**：固定3天（259200秒）
- **计时方式**：基于系统启动时间戳进行精确计算
- **开发测试**：支持5分钟短期测试模式

#### 核心代码逻辑
```cpp
// CTTSTrial.cpp 中的关键实现
static const int TRIAL_DURATION_DAYS = 3;
static const int TRIAL_DURATION_SECONDS = TRIAL_DURATION_DAYS * 24 * 60 * 60;

// 试用时间验证
bool CTTSTrial::IsTrialValid() {
    long long startTime, bootTime;
    if (!LoadTrialInfo(startTime, bootTime)) {
        return false;
    }
    
    long long currentTime = GetCurrentTimestamp();
    long long elapsedTime = currentTime - startTime;
    
    return elapsedTime <= TRIAL_DURATION_SECONDS;
}
```

#### 状态管理
- **0**：未授权状态
- **1**：正式授权
- **2**：试用期内
- **3**：试用期已过期

### 1.1. 核心功能实现

#### 1. 3天试用期限
- **试用时长**: 固定3天（259200秒）
- **时间戳验证**: 使用系统启动时间和当前时间双重验证
- **开发测试模式**: 可通过修改`TRIAL_DURATION_SECONDS`常量调整试用时长
- **时间计算**: 精确到秒级，防止时间篡改

#### 2. 硬件绑定机制
- **硬件标识**: 基于CPU ID、主板序列号、硬盘序列号等生成唯一标识
- **加密处理**: 使用XOR加密和哈希算法处理硬件信息
- **平台适配**: Linux和Windows平台使用不同的硬件获取方法
- **防克隆**: 硬件更换后试用状态自动失效

#### 3. 防篡改机制
- **启动时间验证**: 记录系统启动时间，防止系统时间回调
- **校验和验证**: 使用硬件ID和时间戳生成校验和
- **多重验证**: 结合时间戳、硬件ID、密钥进行多层验证
- **文件完整性**: 验证配置文件的完整性和有效性

#### 4. 跨平台支持
- **Linux平台**: 使用INI配置文件存储试用信息（需要管理员权限）
  - 主配置: `/var/log/rsyslog_XXXX.conf`
  - 备份配置: `/etc/modprobe_XXX.cache`
  - 验证文件: `/tmp/.session_XXXXXXXX`
  - 时间信息: `/etc/systemd_XXXX.conf`
- **Windows平台**: 使用注册表存储试用信息（非管理员权限）
  - 主注册表: `HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced`
  - 备份注册表: `HKEY_CURRENT_USER\Software\Microsoft\Windows\Shell\Associations`
  - 验证文件: `%USERPROFILE%\AppData\Local\Temp\cache_XXXXXXXX.tmp`
  - 时间信息: `HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\UserAssist`

#### 5. 多层验证系统
- **三层存储**: 主配置、备份配置、验证文件
- **冗余验证**: 任一存储位置验证成功即认为已申请试用
- **故障恢复**: 单一存储失败不影响整体功能
- **数据一致性**: 多个存储位置保持数据同步

### 2. 硬件绑定

#### 绑定原理
硬件绑定通过获取计算机唯一硬件标识符实现，确保试用权限与特定设备绑定，防止软件迁移滥用。

#### 实现细节
```cpp
// 硬件ID获取（平台相关）
#ifdef Q_OS_WIN
    // Windows: 使用驱动器序列号
    QString hardwareId = GetVolumeSerialNumber();
#else
    // Linux: 使用系统特征组合
    QString hardwareId = GetLinuxHardwareId();
#endif

// 密钥加密保护
static const char* SECRET_TTS_BASIC_KEY1 = "your_secret_key_1";
static const char* SECRET_TTS_BASIC_KEY2 = "your_secret_key_2";
```

#### 安全特性
- **唯一性**：每台设备生成唯一标识
- **加密存储**：使用密钥对硬件ID进行加密
- **防伪造**：结合多个硬件特征生成指纹

### 3. 防篡改机制

#### 时间验证策略
系统采用启动时间验证机制，有效防止用户通过修改系统时间来延长试用期。

#### 核心实现
```cpp
// 获取系统启动时间（防篡改关键）
long long CTTSTrial::GetSystemBootTime() {
#ifdef Q_OS_WIN
    // Windows: 使用GetTickCount64()
    return GetTickCount64() / 1000;
#else
    // Linux: 读取/proc/uptime
    struct timespec uptime;
    clock_gettime(CLOCK_UPTIME, &uptime);
    return uptime.tv_sec;
#endif
}

// 时间一致性验证
bool ValidateTrialInfo(long long startTime, long long bootTime) {
    long long currentBootTime = GetSystemBootTime();
    long long expectedBootTime = bootTime + (GetCurrentTimestamp() - startTime);
    
    // 允许小幅度时间偏差（考虑系统误差）
    return abs(currentBootTime - expectedBootTime) < TIME_TOLERANCE;
}
```

#### 防护措施
- **启动时间锚定**：以系统启动时间为基准
- **时间差验证**：检测系统时间是否被人为修改
- **容错机制**：允许合理的时间偏差

### 4. 跨平台支持

#### 平台差异处理

##### Linux平台实现
```cpp
// 配置文件存储路径
#define LINUX_CONFIG_PATH "/etc/tts_trial.conf"
#define LINUX_BACKUP_PATH "/var/lib/tts_trial_backup.conf"
#define LINUX_VERIFY_PATH "/tmp/.tts_verify"

// 硬件ID获取
QString GetLinuxHardwareId() {
    // 组合多个系统特征
    QString cpuInfo = ReadFile("/proc/cpuinfo");
    QString machineId = ReadFile("/etc/machine-id");
    QString diskId = GetDiskSerialNumber();
    
    return GenerateHash(cpuInfo + machineId + diskId);
}
```

##### Windows平台实现
```cpp
// 注册表存储路径
#define WIN_REGISTRY_KEY "HKEY_LOCAL_MACHINE\\SOFTWARE\\TTS_Trial"
#define WIN_BACKUP_KEY "HKEY_CURRENT_USER\\SOFTWARE\\TTS_Trial_Backup"

// 硬件ID获取
QString GetWindowsHardwareId() {
    // 使用驱动器序列号
    DWORD serialNumber;
    GetVolumeInformation(L"C:\\", NULL, 0, &serialNumber, NULL, NULL, NULL, 0);
    
    return QString::number(serialNumber, 16);
}
```

#### 统一接口设计
```cpp
class CTTSTrial {
public:
    // 跨平台统一接口
    static bool CanApplyTrial();
    static bool ApplyTrial();
    static bool IsTrialValid();
    static int GetTrialRemainingTime();
    
private:
    // 平台特定实现
    static bool SaveTrialInfo_Platform(const TrialInfo& info);
    static bool LoadTrialInfo_Platform(TrialInfo& info);
    static QString GetHardwareId_Platform();
};
```

### 5. 多重验证

#### 三层存储架构

##### 第一层：主配置存储
- **Linux**：`/etc/tts_trial.conf`
- **Windows**：注册表主键
- **内容**：试用开始时间、硬件ID、校验码

##### 第二层：备份配置存储
- **Linux**：`/var/lib/tts_trial_backup.conf`
- **Windows**：注册表备份键
- **作用**：主配置损坏时的恢复机制

##### 第三层：验证文件
- **Linux**：`/tmp/.tts_verify`
- **Windows**：临时目录验证文件
- **功能**：完整性校验和防篡改验证

#### 校验码算法
```cpp
unsigned long CTTSTrial::CalculateChecksum(long long startTime, long long bootTime) {
    // 组合时间戳和硬件ID
    QString data = QString::number(startTime) + 
                   QString::number(bootTime) + 
                   GetHardwareId_Platform() +
                   SECRET_TTS_BASIC_KEY1;
    
    // 计算CRC32校验码
    return crc32(data.toUtf8().data(), data.length());
}
```

#### 数据一致性验证
```cpp
bool ValidateTrialConsistency() {
    TrialInfo main, backup, verify;
    
    // 读取三层数据
    bool mainValid = LoadMainConfig(main);
    bool backupValid = LoadBackupConfig(backup);
    bool verifyValid = LoadVerifyFile(verify);
    
    // 交叉验证
    if (mainValid && backupValid) {
        return (main.checksum == backup.checksum) && 
               (main.startTime == backup.startTime);
    }
    
    return false;
}
```

## 开发者操作指南：恢复TTS试用权限

### Linux平台恢复操作（需要管理员权限）

#### 方法一：清理配置文件
```bash
# 1. 停止相关服务
sudo systemctl stop networking_service

# 2. 删除伪装的配置文件
sudo rm -f /var/log/rsyslog_*.conf
sudo rm -f /etc/modprobe_*.cache
sudo rm -f /tmp/.session_*
sudo rm -f /etc/systemd_*.conf

# 3. 清理可能的隐藏文件
sudo find /var/log -name "rsyslog_*.conf" -delete
sudo find /etc -name "modprobe_*.cache" -delete
sudo find /tmp -name ".session_*" -delete
sudo find /etc -name "systemd_*.conf" -delete

# 4. 重启服务
sudo systemctl start networking_service
```

#### 方法二：修改系统标识
```bash
# 1. 备份原始machine-id
sudo cp /etc/machine-id /etc/machine-id.backup

# 2. 生成新的machine-id
sudo rm /etc/machine-id
sudo systemd-machine-id-setup

# 3. 清理试用数据
sudo rm -f /var/log/rsyslog_*.conf
sudo rm -f /etc/modprobe_*.cache
sudo rm -f /tmp/.session_*
sudo rm -f /etc/systemd_*.conf

# 4. 重启系统
sudo reboot
```

#### 涉及的关键文件
- `/var/log/rsyslog_*.conf` - 主配置文件（伪装的系统日志配置）
- `/etc/modprobe_*.cache` - 备份配置（伪装的系统缓存）
- `/tmp/.session_*` - 验证文件（伪装的临时会话文件）
- `/etc/systemd_*.conf` - 时间信息（伪装的systemd配置）
- `/etc/machine-id` - 系统标识文件
- `/proc/cpuinfo` - CPU信息（只读）
- `/proc/uptime` - 系统运行时间（只读）

### Windows平台恢复操作（普通用户权限）

#### 方法一：清理注册表
```batch
@echo off
REM 1. 停止相关服务
net stop "TTS Service" 2>nul

REM 2. 删除注册表项（普通用户权限）
reg delete "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v "试用密钥" /f 2>nul
reg delete "HKEY_CURRENT_USER\Software\Microsoft\Windows\Shell\Associations" /v "试用密钥" /f 2>nul
reg delete "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\UserAssist" /v "LastCounterUpdate" /f 2>nul
reg delete "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\UserAssist" /v "SystemBootSequence" /f 2>nul
reg delete "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\UserAssist" /v "CounterChecksum" /f 2>nul

REM 3. 清理临时文件
del /f /q "%USERPROFILE%\AppData\Local\Temp\cache_*.tmp" 2>nul

REM 4. 重启服务
net start "TTS Service" 2>nul

echo 试用权限已重置
pause
```

#### 方法二：PowerShell脚本
```powershell
# 普通用户权限即可运行

# 停止相关进程
Get-Process | Where-Object {$_.ProcessName -like "*TTS*"} | Stop-Process -Force

# 清理注册表
$registryPaths = @(
    "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced",
    "HKCU:\Software\Microsoft\Windows\Shell\Associations",
    "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\UserAssist"
)

# 删除试用相关的注册表值
Remove-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" -Name "试用密钥" -ErrorAction SilentlyContinue
Remove-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\Shell\Associations" -Name "试用密钥" -ErrorAction SilentlyContinue
Remove-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\UserAssist" -Name "LastCounterUpdate" -ErrorAction SilentlyContinue
Remove-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\UserAssist" -Name "SystemBootSequence" -ErrorAction SilentlyContinue
Remove-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\UserAssist" -Name "CounterChecksum" -ErrorAction SilentlyContinue

# 清理文件
Get-ChildItem -Path "$env:USERPROFILE\AppData\Local\Temp" -Filter "cache_*.tmp" | Remove-Item -Force

Write-Host "TTS试用权限重置完成" -ForegroundColor Green
```

#### 涉及的关键位置
- **注册表主键**：`HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced`
- **注册表备份**：`HKEY_CURRENT_USER\Software\Microsoft\Windows\Shell\Associations`
- **时间信息**：`HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\UserAssist`
- **临时文件**：`%USERPROFILE%\AppData\Local\Temp\cache_*.tmp`
- **系统信息**：驱动器序列号（通过WMI获取）

### 高级恢复技巧

#### 虚拟机环境重置
```bash
# 对于VMware/VirtualBox环境
# 1. 关闭虚拟机
# 2. 修改虚拟机硬件配置
# 3. 重新生成虚拟机UUID
vboxmanage modifyvm "VM_Name" --hardwareuuid random

# 4. 启动虚拟机并清理试用数据（Linux需要管理员权限）
sudo rm -rf /var/log/rsyslog_* /etc/modprobe_* /tmp/.session_* /etc/systemd_*
```

#### 容器环境处理
```dockerfile
# Docker环境重置
FROM base_image

# 清理试用相关文件（伪装的系统文件）
RUN rm -rf /var/log/rsyslog_* /etc/modprobe_* /tmp/.session_* /etc/systemd_*

# 重新生成machine-id
RUN rm -f /etc/machine-id && systemd-machine-id-setup

COPY application /app
CMD ["/app/start.sh"]
```

#### 自动化重置脚本
```python
#!/usr/bin/env python3
# TTS试用权限自动重置工具

import os
import sys
import subprocess
import platform
import winreg

def reset_linux_trial():
    """Linux平台试用重置（需要管理员权限）"""
    commands = [
        "sudo find /var/log -name 'rsyslog_*.conf' -delete",
        "sudo find /etc -name 'modprobe_*.cache' -delete", 
        "sudo find /tmp -name '.session_*' -delete",
        "sudo find /etc -name 'systemd_*.conf' -delete",
        "sudo rm -f /etc/machine-id",
        "sudo systemd-machine-id-setup"
    ]
    
    for cmd in commands:
        try:
            subprocess.run(cmd, shell=True, check=True)
            print(f"执行成功: {cmd}")
        except subprocess.CalledProcessError:
            print(f"执行失败: {cmd}")

def reset_windows_trial():
    """Windows平台试用重置（普通用户权限）"""
    try:
        # 删除注册表项
        reg_paths = [
            (winreg.HKEY_CURRENT_USER, "Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced"),
            (winreg.HKEY_CURRENT_USER, "Software\\Microsoft\\Windows\\Shell\\Associations"),
            (winreg.HKEY_CURRENT_USER, "Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\UserAssist")
        ]
        
        for hkey, path in reg_paths:
            try:
                key = winreg.OpenKey(hkey, path, 0, winreg.KEY_SET_VALUE)
                # 删除试用相关的值
                for value_name in ["试用密钥", "LastCounterUpdate", "SystemBootSequence", "CounterChecksum"]:
                    try:
                        winreg.DeleteValue(key, value_name)
                        print(f"删除注册表值: {path}\\{value_name}")
                    except FileNotFoundError:
                        pass
                winreg.CloseKey(key)
            except FileNotFoundError:
                print(f"注册表路径不存在: {path}")
        
        # 清理临时文件
        temp_path = os.path.expandvars("%USERPROFILE%\\AppData\\Local\\Temp")
        for file in os.listdir(temp_path):
            if file.startswith("cache_") and file.endswith(".tmp"):
                try:
                    os.remove(os.path.join(temp_path, file))
                    print(f"删除临时文件: {file}")
                except:
                    pass
                    
    except Exception as e:
        print(f"Windows重置过程中出错: {e}")

if __name__ == "__main__":
    system = platform.system()
    
    if system == "Linux":
        print("检测到Linux系统，开始重置（需要管理员权限）...")
        reset_linux_trial()
    elif system == "Windows":
        print("检测到Windows系统，开始重置（普通用户权限）...")
        reset_windows_trial()
    else:
        print(f"不支持的系统: {system}")
        sys.exit(1)
    
    print("试用权限重置完成！")
```

## 注意事项

### 安全警告
1. **仅限开发测试**：以上操作仅用于开发和测试环境
2. **数据备份**：操作前请备份重要数据
3. **权限要求**：Linux需要管理员/root权限，Windows仅需普通用户权限
4. **合规使用**：请遵守软件许可协议
5. **文件隐蔽性**：使用系统常见文件名和路径提高隐蔽性

### 技术限制
1. **硬件绑定**：更换主要硬件可能影响识别
2. **系统重装**：完全重装系统可重置试用
3. **虚拟化环境**：虚拟机快照恢复可能重置状态
4. **时间同步**：确保系统时间准确性
5. **权限差异**：Linux需要管理员权限，Windows仅需普通用户权限
6. **路径伪装**：配置文件伪装成系统文件提高隐蔽性

### 开发建议
1. **测试环境**：使用独立测试环境进行验证
2. **自动化脚本**：编写自动化重置脚本提高效率
3. **监控日志**：关注试用功能相关日志输出
4. **版本控制**：记录不同版本的重置方法差异
5. **权限管理**：Linux开发环境确保具有管理员权限
6. **路径验证**：定期验证文件路径的有效性和隐蔽性

---

**免责声明**：本文档仅供技术研究和开发测试使用，请勿用于破解商业软件或违反许可协议的行为。