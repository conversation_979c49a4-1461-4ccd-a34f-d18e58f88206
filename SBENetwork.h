#ifndef SBENETWORK_H
#define SBENETWORK_H


/*------------------------- 回调函数 -------------------------*/

// 消息回调函数定义
typedef void(SBE_CALLBACK)(int		msg,		// 消息
                            void*	data1,		// 数据1
                            void*	data2,		// 数据2
                            void*	pDefined);	// 自定义数据


/*
 * 函数功能：开始工作，初始化数据
 *
 * 参数描述：pCallBack → 接收消息的回调函数
 *			 pDefined  → 用户传递进来的自定义数据，回调时会回传给用户
 *
 * 返回值  ：开始工作是否成功
 */
bool    SBE_StartWorking(SBE_CALLBACK* pCallBack = NULL, void* pDefined = NULL);



// 设置选中分区音量
bool    SBE_SetVolume(byte nVolume, unsigned int *pZoneIndex, unsigned int uZoneCount);


/*
 * 函数功能：停止工作，释放资源
 *
 * 参数描述：无参数
 *
 * 返回值  ：停止工作是否成功
 */
bool    SBE_StopWorking(void);



/*
 * 函数功能：新建歌曲列表
 *
 * 参数描述：strListName  →  歌曲列表名称
 *
 * 返回值  ：  0 → 添加成功
 *			   1 → 歌曲列表数量超出上限 （测试版最多1个歌曲列表，正式版最多20个歌曲列表）
 *			   2 → 歌曲列表有重名
 *             3 → 歌曲列表名称为空
 *             4 → 歌曲列表名称长度超过限制（最长24个字符）
 */
int	SBE_AddSongList(string strListName);



/*
 * 函数功能：往指定歌曲列表添加歌曲
 *
 * 参数描述：uListIndex		 → 歌曲列表在g_SBEPlayList中的索引
 *			 vecSongPathName → 歌曲的绝对路径
 *
 * 返回值  ：0 → 添加成功
 *			 1 → 歌曲数量超出上限 （测试版每个歌曲列表最多4首歌曲，正式版每个歌曲列表最多500首歌曲）
 *			 2 → 歌曲格式不对（仅限于mp3/wav）
 *			 3 → 歌曲名称含有非法字符（例如'+'）
 *			 4 → 歌曲参数错误
 */
int		SBE_AddSongsToList(unsigned int uListIndex, vector<string>& vecSongPathName);



/*
 * 函数功能：选中分区播放节目源
 *
 * 参数描述：uListIndex`→ 歌曲列表在g_SBEPlayList中的索引
 *			 uSongIndex`→ 歌曲在g_SBEPlayList[uListIndex]中vecSong的索引
 *			 pZoneIndex	→ 选中分区在g_SBEZones中的索引
 *			 uZoneCount → 选中分区的数量
 *
 * 返回值  ：播放是否成功
 */
bool    SBE_PlaySource(unsigned int uListIndex, unsigned int uSongIndex, unsigned int *pZoneIndex, unsigned int uZoneCount);


// 新建分组
int	    SBE_AddGroup(CMyString strGroupName);

// 设置歌曲播放模式
bool	     SBE_SetPlayMode(int nPlayMode);


// AUX
bool       SBE_AUXPlay();


// BW
bool       SBE_BW();

// download file
void       SBE_CURLDownload();

// CPU ID
void       SBEGetCPUID();

// register
void       SBERegisterServer(string strRegisterNum);

void       BeforeExit();

#endif // SBENETWORK_H



