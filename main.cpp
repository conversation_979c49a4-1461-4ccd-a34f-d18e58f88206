#include "stdafx.h"
#include <iostream>
#include <vector>
#include "Tools/CMyString.h"
#include "Global/GlobalMethod.h"
#include "Network/Network.h"
#include "SBENetwork.h"
#include "webSocketTest.h"
#include "VoipTest.h"
#include <regex>
#include <QCoreApplication>
#include <stdio.h>
#include <sys/types.h>
#include<stdio.h>
#include<QProcess>
#include<QFuture>
#include<QtConcurrent/QtConcurrent>
#include<QStandardPaths>

#include "TinyXml/tinystr.h"
#include "TinyXml/tinyxml.h"

#if defined(Q_OS_WIN32)
#include "windows.h"
#include <Shlobj.h>

int GetTempFolderPath(char* folderPath)
{
	char folderPathANSI[1024]={0};
	if (SHGetSpecialFolderPath(0, folderPathANSI, CSIDL_LOCAL_APPDATA, false))
	{
		// 将 ANSI 编码转换为宽字符（UTF-16 编码），也就是unicode
		int len = MultiByteToWideChar(CP_ACP, 0, folderPathANSI, -1, NULL, 0);
		wchar_t* wideFolderPath = new wchar_t[len];
		MultiByteToWideChar(CP_ACP, 0, folderPathANSI, -1, wideFolderPath, len);

		// 将宽字符（unicode）转换为 UTF-8 编码
		len = WideCharToMultiByte(CP_UTF8, 0, wideFolderPath, -1, NULL, 0, NULL, NULL);
		WideCharToMultiByte(CP_UTF8, 0, wideFolderPath, -1, folderPath, len, NULL, NULL);

		delete[] wideFolderPath;

		return 1;
	}
	return 0;
}
#endif

//#include "Tools/cJSON.h"

/*
bool checkLocalNet()
{
    QString networkCmd = QString("ping www.baidu.com -n 2 -w 500");
    QProcess process;
    process.start(networkCmd);
    process.waitForFinished();
    QString result = process.readAll();

    return result.contains("TTL=");
}
*/
int main(int argc, char *argv[])
{
    QCoreApplication a(argc, argv);

    qRegisterMetaType<string>("string");

    //qDebug()<< QSslSocket::sslLibraryBuildVersionString();

#if defined(Q_OS_WIN32)

    QStringList args = a.arguments();
    if (args.contains("--internal-settime") && args.size() >= 3) {
        time_t t = args.at(2).toLongLong();
        if (SetSystemTime_hardware(t, true) == 0) {
            return 0;          // 成功
        } else {
            return 1;
        }
    }

 qDebug()<<"------------------------------------------------------------";
    if(argc<=1)     //不允许此程序单独启动
    {
        a.exit();
        return -1;
    }

#if 1
    // 遍历所有传入的参数
    for (int i = 1; i < argc; ++i) { // 从 1 开始，因为 argv[0] 是程序名
        if (std::strcmp(argv[i], "-netcard") == 0 && i + 1 < argc) {
            // 检查下一个参数是否存在，并提取IP地址
            g_Global.m_strBindIpAddress = argv[i + 1];
            break;
        }
    }
    // 输出提取到的 MAC 地址
    //std::cout << "绑定的IP地址: " << bindIpAddress << std::endl;
    if(!IsIPFormat(g_Global.m_strBindIpAddress.data()))
    {
       g_Global.m_strBindIpAddress.clear();
    }
#endif

    //Qt5中QString内部采用unicode字符集，如果不设置，默认Latin1，即ISO-8859-1，该编码是单字节编码,向下兼容ASCII,其编码范围是0x00-0xFF。
    //如果不设置,QFile open等操作不能识别带空格的文件名
    QTextCodec *codec = QTextCodec::codecForName("UTF-8");
    QTextCodec::setCodecForLocale(codec);

    //获取运行路径
    QString applicationDirPath = QCoreApplication::applicationDirPath();
    g_Global.m_strRunDirPath.Format("%s",applicationDirPath.toStdString().c_str());
    g_Global.m_strApachePath = g_Global.m_strRunDirPath + "/apache2";
    g_Global.m_strFolderPath = g_Global.m_strApachePath + "/htdocs";


    LOG(FORMAT("==== Bind IP Address: %s  =====\n", g_Global.m_strBindIpAddress.data()), LV_DEADLY);

    #if 0
    //不能用QStandardPaths::writableLocation，它读取系统环境变量Temp
    QString tempFolderPath=QStandardPaths::writableLocation(QStandardPaths::TempLocation);
    g_Global.m_strTempFolderPath.Format("%s",tempFolderPath.toStdString().c_str());
    #endif

    char tempPath[1024]={0};
    GetTempFolderPath(tempPath);
    if(strlen(tempPath)>0)
    {
        g_Global.m_strTempFolderPath.Format("%s\\%s",tempPath,"Temp");
        //判断路径是否存在，不存在，需要创建（有些系统用户目录下没有Temp目录）
        if(!IsExistDir(g_Global.m_strTempFolderPath.Data()))
        {
            CreateDirectoryQ(g_Global.m_strTempFolderPath.Data());
        }
    }
    else
    {
        g_Global.m_strTempFolderPath.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_OTHER);
    }

    printf("RunDirPath=%s,FolderPath=%s,TempPath=%s\n",g_Global.m_strRunDirPath.Data(),g_Global.m_strFolderPath.Data(),g_Global.m_strTempFolderPath.Data());

    QStringList params;

    printf("kill node.exe...\n");
    params<<"/c"<<"taskkill"<<"-f"<<"-im"<<"node.exe";
    QProcess Process_stop_node;
    Process_stop_node.start("cmd.exe",params);
    Process_stop_node.waitForFinished();
    Process_stop_node.close();

    params<<"/c"<<"taskkill"<<"-f"<<"-im"<<"httpd.exe";
    QProcess process;
    process.start("cmd.exe",params);
    process.waitForFinished();
    process.close();

    printf("kill httpd.exe...\n");
    usleep(100000);
    printf("start httpd.exe...\n");
    QString exePath = applicationDirPath + "/apache2/bin/httpd.exe";
    QString workDir = applicationDirPath;   // 必须设置工作目录
    QProcess Process_start_httpd;
    Process_start_httpd.setWorkingDirectory(workDir);
    Process_start_httpd.start(exePath);   //QProcess::execute("notepad.exe");阻塞式启动 //startDetached外部程序启动后，当主程序退出时并不退出。而是继续执行。
    Process_start_httpd.waitForStarted();

    QProcess process_tasklist;
    process_tasklist.start("tasklist");
    process_tasklist.waitForFinished(); //等待命令执行结束
    QByteArray result = process_tasklist.readAllStandardOutput();
    if(-1==result.indexOf("httpd.exe"))
    {
        printf("httpd start error!\n");
    }
    else
    {
        printf("httpd start succeed!\n");
    }
#else
    g_Global.m_strRunDirPath = "/mnt/yaffs2/voip/Networking";
    g_Global.m_strApachePath = "/usr/local/apache2";
    g_Global.m_strFolderPath = "/usr/local/apache2/htdocs";
    g_Global.m_strTempFolderPath = "/tmp";
    
    // 停止包含radio_temp.js的node进程
    printf("kill radio node process...\n");
    QProcess process_kill_radio;
    process_kill_radio.start("pkill", QStringList() << "-f" << "radio_temp.js");
    process_kill_radio.waitForFinished();
    process_kill_radio.close();
    printf("radio node process killed.\n");
#endif

#if defined(Q_OS_LINUX)   //测试主备服务器函数
    #if 0
    CServerSync::getSystemFirstNormalUser();
    CServerSync::checkSystemUserAndPassword("jms","bydzjms");
    CServerSync::read_rootUser_ssh_key();
    CServerSync::GetSSHLoginWithoutPasswordResult("************","mhtech2");
    CServerSync::checkSSHKeyAndWriteNew("jms","12333");
    #endif
#endif

    //20230713 清除临时文件内的音频文件(*.mp3,*.wav)
    RemoveDirectoryAllFilesByExt(g_Global.m_strTempFolderPath.Data(),".mp3",false);
    RemoveDirectoryAllFilesByExt(g_Global.m_strTempFolderPath.Data(),".wav",false);

    //版本切换，需要删除数据库文件
    #if APP_IS_AISP_TZY_ENCRYPTION
    CMyString strLogDBFile;
    strLogDBFile.Format((char*)("%s/%s/%s"), g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, "data.db");
    RemoveFile(strLogDBFile.Data());

    CMyString strUserDBFile;
    strUserDBFile.Format((char*)("%s/%s/%s"), g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, "user.db");
    RemoveFile(strUserDBFile.Data());
    #else
    CMyString strLogDBFile;
    strLogDBFile.Format((char*)("%s/%s/%s"), g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, "log.db");
    RemoveFile(strLogDBFile.Data());

    CMyString strUserDBFile;
    strUserDBFile.Format((char*)("%s/%s/%s"), g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, "account.db");
    RemoveFile(strUserDBFile.Data());
    #endif
	
    if(!SBE_StartWorking())
    {
        LOG("Networking start failed", LV_DEADLY);
        return 0;
    }
    
    #if 0
    int value = 42;
    std::string formattedString = fmt::format("The value is: {}", value);
    std::cout << formattedString << std::endl;
    #endif


    // 导入定时方案
    /*
    string strPathName = "/usr/local/apache2/htdocs/Data/1.xml";
    CScheme scheme;
    g_Global.m_TimerScheme.ImportSchemeFile(scheme, strPathName);
    */

    // 多语言
    /*
    LOG("Test ----------------------", LV_INFO);
    CMyString str = LANG_STR(LANG_SECTION_DIALOG, "Streaming Gateway File", ("监控文件"));
    LOG(str.C_Str(), LV_INFO);
    */

    return a.exec();

}




