#include "stdafx.h"
#include "VoipTest.h"


void voipTest()
{
        //  2.1	获取设备状态
        //strBuf = CVoipProtocol::CmdGetSipStatus();

        // 2.2	对讲
        //strBuf = CVoipProtocol::CmdRequestTalkback("801", "805");

        // 2.3	广播

        //strBuf = CVoipProtocol::CmdRequestPaging()

        //g_Global.m_Voip.SendData(strBuf.data(), strBuf.length());


//        strBuf = CVoipProtocol::CmdRequestHangup("801");
//        getchar();
//        g_Global.m_Voip.SendDataToVOIP(strBuf.data(), strBuf.length());
}
