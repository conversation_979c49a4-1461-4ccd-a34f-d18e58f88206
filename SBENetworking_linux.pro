TEMPLATE = app
CONFIG += console c++14
CONFIG -= app_bundle
QT -= gui

QT      += network
QT      += websockets
QT      += concurrent

#QMAKE_CC += -g
#QMAKE_CXX += -g
#QMAKE_LINK += -g

CONFIG +=debug_and_release

#unix:!macx:CONFIG += ccache

unix:!macx:TARGET = SBENetworking
win32:TARGET = IPNetworking
win32:DEFINES -= UNICODE _UNICODE


CONFIG(debug, debug|release){
    win32:DESTDIR = ../win32_debug/
    unix:!macx:DESTDIR = ../linux_debug/
    win32:OBJECTS_DIR = ../win32_debug/debug
    unix:!macx:OBJECTS_DIR = ../linux_debug/debug
}else{
    win32:DESTDIR = ../win32_release/
    unix:!macx:DESTDIR = ../linux_release/
    win32:OBJECTS_DIR = ../win32_release/release
    unix:!macx:OBJECTS_DIR = ../linux_release/release
}

unix:!macx:{
SOURCES += \
    Model/Device/AudioCall.cpp \
    Model/Device/AudioListen.cpp \
    Network/HPSocket/MyTCPServer.cpp \
    Network/HPSocket/MyUdpNode.cpp \
    Network/Kcp/ikcp.c \
    Network/Kcp/kcp.cpp \
    Network/Web/WebPaging.cpp \
    Tools/G722/g722_decode.c \
    Tools/G722/g722_encode.c \
    main.cpp \
    Global/GlobalMethod.cpp \
    Tools/CMyString.cpp \
    Model/Device/AudioCollector.cpp \
    Network/Protocol/Protocol.cpp \
    Model/Device/AudioForward.cpp \
    Model/Device/DeviceEQ.cpp \
    Model/Device/BlueTooth.cpp \
    Database/LogTable.cpp \
    Model/Other/Group.cpp \
    Model/Device/DeviceMixing.cpp \
    Model/Device/FireCollector.cpp \
    Model/Device/NetworkInfo.cpp \
    Model/Device/PowerOutputInfo.cpp \
    Model/Device/SipInfo.cpp \
    Model/Device/Section.cpp \
    Model/Other/PlayList.cpp \
    Global/GlobalData.cpp \
    Network/Network.cpp \
    Model/Other/TimerScheme.cpp \
    Tools/Language.cpp \
    Player/SongTool.cpp \
    Player/PlayQueue.cpp \
    Tools/Ini.cpp \
    Tools/MyLog.cpp \
    Tools/FileManager.cpp \
    Model/Other/LogFile.cpp \
    Model/Other/SelectedSections.cpp \
    TinyXml/tinystr.cpp \
    TinyXml/tinyxml.cpp \
    TinyXml/tinyxmlerror.cpp \
    TinyXml/tinyxmlparser.cpp \
    stdafx.cpp \
    Tools/CTime.cpp \
    Tools/MyCFile.cpp \
    Player/SongUnit.cpp \
    Tools/LxTimer.cpp \
    Player/SongPlay.cpp \
    Network/Protocol/CommandSend.cpp \
    Network/Protocol/CommandQueue.cpp \
    Network/Protocol/CommandHandle.cpp \
    Database/DataBase.cpp \
    Tools/ComputerInfo.cpp \
    SBENetwork.cpp \
    Tools/cJSON.c \
    Network/VOIP/VoipCommandHandle.cpp \
    webSocketTest.cpp \
    Network/Monitor/MonitorCommandHandle.cpp \
    Network/Monitor/MonitorProtocol.cpp \
    Network/VOIP/VoipProtocol.cpp \
    Network/VOIP/Voip.cpp \
    Tools/base64.cpp \
    Tools/sha1.cpp \
    Network/Web/WebCommandSend.cpp \
    Network/Web/WebSocket.cpp \
    Network/Web/WebProtocol.cpp \
    Network/Web/WebHandle.cpp \
    Network/Web/WebNetwork.cpp \
    Model/Device/WebSection.cpp \
    VoipTest.cpp \
    Model/Other/VoipSipInfo.cpp \
    Network/VOIP/VoipCommandSend.cpp \
    Network/Web/WebQueue.cpp \
    Database/UserTable.cpp \
    Model/User/UserManager.cpp \
    Model/Recv/WebData.cpp \
    Model/Recv/ExtraUser.cpp \
    Model/Recv/ExtraGroup.cpp \
    Model/Recv/ExtraTimer.cpp \
    Model/Recv/ExtraFireCollector.cpp \
    Network/Web/QtWebsocket.cpp \
    Model/Recv/ExtraMonitorEvent.cpp \
    CLI/CLIServer.cpp \
    Tools/CJSONRW.cpp \
    Tools/Control/TTS.cpp \
    Player/AUXPlay.cpp \
    Network/Monitor/MonProc.cpp \
    Model/Device/Monitor.cpp \
    Player/ListenIn.cpp \
    Model/Device/Server.cpp \
    Model/Device/LocalServer.cpp \
    Model/LocalHost.cpp \
    Tools/FileSync.cpp \
    Model/Device/PlayedRecently.cpp \
    Tools/Control/CRegister.cpp \
    Tools/UpgradeManager.cpp \
    Model/Device/StreamingGateway.cpp \
    Tools/Ukey/D8.cpp \
    Tools/Ukey/Softdog.cpp \
    Model/Device/SequencePower.cpp \
    Tools/timespec.c \
    Model/Device/RemoteControl.cpp \
    Tools/buildTime.cpp \
    Tools/Ugps/usbGPS.cpp \
    Network/ServerSync.cpp \
    Model/Device/AudioMixer.cpp \
    Model/Device/PhoneGateway.cpp \
    PasswordMod/PasswordMod.cpp \
    Network/Web/QtClientTransfer.cpp \
    Network/Web/RadioWebSocketClient.cpp \
    Network/Web/NodeJSManager.cpp \
    Model/Device/InformationPublish.cpp \
    Model/Device/AmpControler.cpp \
    Model/Device/NoiseDetector.cpp \
    Database/RecordTable.cpp \
    Player/RecordManager.cpp \
    Model/Other/RadioManager.cpp \
    Model/Other/radio_preset_detail.cpp \
    Model/Other/radio_preset_group.cpp \
    Tools/xxtea.cpp \
    Tools/Control/CTTSTrial.cpp

HEADERS += \
    Global/GlobalMethod.h \
    Model/Device/AudioCall.h \
    Model/Device/AudioListen.h \
    Network/HPSocket/GlobalDef.h \
    Network/HPSocket/GlobalErrno.h \
    Network/HPSocket/HPSocket-SSL.h \
    Network/HPSocket/HPSocket.h \
    Network/HPSocket/HPTypeDef.h \
    Network/HPSocket/MyTCPServer.h \
    Network/HPSocket/MyUdpNode.h \
    Network/HPSocket/SocketInterface.h \
    Network/Kcp/ikcp.h \
    Network/Kcp/kcp.h \
    Network/Web/WebPaging.h \
    Tools/CMyString.h \
    Global/Const.h \
    Model/Device/AudioCollector.h \
    Network/Protocol/Protocol.h \
    Model/Device/AudioForward.h \
    Model/Device/DeviceEQ.h \
    Model/Device/BlueTooth.h \
    Database/LogTable.h \
    Model/Other/Group.h \
    Model/Device/DeviceMixing.h \
    Model/Device/FireCollector.h \
    Model/Device/NetworkInfo.h \
    Global/CType.h \
    Model/Device/PowerOutputInfo.h \
    Model/Device/SipInfo.h \
    Model/Device/Section.h \
    Model/Other/PlayList.h \
    Global/GlobalData.h \
    Network/Network.h \
    Model/Other/TimerScheme.h \
    Tools/G722/g722.h \
    Tools/G722/g722_decoder.h \
    Tools/G722/g722_encoder.h \
    Tools/G722/g722_private.h \
    Tools/Language.h \
    Player/SongTool.h \
    Player/PlayQueue.h \
    Tools/Ini.h \
    Tools/MyLog.h \
    Tools/FileManager.h \
    Model/Other/LogFile.h \
    Model/Other/SelectedSections.h \
    TinyXml/tinystr.h \
    TinyXml/tinyxml.h \
    stdafx.h \
    Tools/CTime.h \
    Tools/MyCFile.h \
    Player/SongUnit.h \
    Tools/tools.h \
    Tools/LxTimer.h \
    Player/SongPlay.h \
    Network/Protocol/CommandSend.h \
    Network/Protocol/CommandQueue.h \
    Network/Protocol/CommandHandle.h \
    Database/DataBase.h \
    Tools/ComputerInfo.h \
    SBENetwork.h \
    Tools/cJSON.h \
    Network/VOIP/VoipCommandHandle.h \
    webSocketTest.h \
    Network/Monitor/MonitorCommandHandle.h \
    Network/Monitor/MonitorProtocol.h \
    Network/VOIP/VoipProtocol.h \
    Network/VOIP/Voip.h \
    Tools/base64.h \
    Tools/sha1.h \
    Network/Web/WebCommandSend.h \
    Network/Web/WebSocket.h \
    Network/Web/WebProtocol.h \
    Network/Web/WebHandle.h \
    Network/Web/WebNetwork.h \
    Model/Device/WebSection.h \
    VoipTest.h \
    Model/Other/VoipSipInfo.h \
    Network/VOIP/VoipCommandSend.h \
    Network/Web/WebQueue.h \
    Database/UserTable.h \
    Model/User/UserManager.h \
    Model/Recv/WebData.h \
    Model/Recv/ExtraUser.h \
    Model/Recv/ExtraGroup.h \
    Model/Recv/ExtraTimer.h \
    Model/Recv/ExtraFireCollector.h \
    Network/Web/QtWebsocket.h \
    Model/Recv/ExtraMonitorEvent.h \
    CLI/CLIServer.h \
    Tools/CJSONRW.h \
    Tools/Control/TTS.h \
    Tools/Control/msp_cmn.h \
    Tools/Control/msp_errors.h \
    Tools/Control/msp_types.h \
    Tools/Control/qtts.h \
    Player/AUXPlay.h \
    Network/Monitor/MonProc.h \
    Model/Device/Monitor.h \
    Player/ListenIn.h \
    Model/Device/Server.h \
    Model/Device/LocalServer.h \
    Model/LocalHost.h \
    Tools/FileSync.h \
    Model/Device/PlayedRecently.h \
    Tools/Control/CRegister.h \
    Tools/UpgradeManager.h \
    Model/Device/StreamingGateway.h \
    Tools/Ukey/D8.h \
    Tools/Ukey/KeyDef.h \
    Tools/Ukey/Softdog.h \
    Tools/Ukey/UKey.h \
    Tools/Ukey/libusb.h \
    Model/Device/SequencePower.h \
    Tools/timespec.h \
    Model/Device/RemoteControl.h \
    Tools/buildTime.h \
    Tools/Ugps/usbGPS.h \
    Network/ServerSync.h \
    Model/Device/AudioMixer.h \
    Model/Device/PhoneGateway.h \
    PasswordMod/PasswordMod.h \
    Network/Web/QtClientTransfer.h \
    Network/Web/RadioWebSocketClient.h \
    Network/Web/NodeJSManager.h \
    Model/Device/InformationPublish.h \
    Model/Device/AmpControler.h \
    Model/Device/NoiseDetector.h \
    Database/RecordTable.h \
    Player/RecordManager.h \
    Model/Other/RadioManager.h \
    Tools/xxtea.h \
    Tools/Control/CTTSTrial.h
}

win32{

SOURCES += \
    Model/Device/AudioCall.cpp \
    Model/Device/AudioListen.cpp \
    Network/HPSocket/MyTCPServer.cpp \
    Network/HPSocket/MyUdpNode.cpp \
    Network/Kcp/ikcp.c \
    Network/Kcp/kcp.cpp \
    Network/SBETcpClientSocket.cpp \
    Network/SBETcpServerSocket.cpp \
    Network/UDPSockets.cpp \
    Network/Web/WebPaging.cpp \
    Tools/G722/g722_decode.c \
    Tools/G722/g722_encode.c \
    main.cpp \
    Global/GlobalMethod.cpp \
    Tools/CMyString.cpp \
    Model/Device/AudioCollector.cpp \
    Network/Protocol/Protocol.cpp \
    Model/Device/AudioForward.cpp \
    Model/Device/DeviceEQ.cpp \
    Model/Device/BlueTooth.cpp \
    Database/LogTable.cpp \
    Model/Other/Group.cpp \
    Model/Device/DeviceMixing.cpp \
    Model/Device/FireCollector.cpp \
    Model/Device/NetworkInfo.cpp \
    Model/Device/PowerOutputInfo.cpp \
    Model/Device/SipInfo.cpp \
    Model/Device/Section.cpp \
    Model/Other/PlayList.cpp \
    Global/GlobalData.cpp \
    Network/Network.cpp \
    Model/Other/TimerScheme.cpp \
    Tools/Language.cpp \
    Player/SongTool.cpp \
    Player/PlayQueue.cpp \
    Tools/Ini.cpp \
    Tools/MyLog.cpp \
    Tools/FileManager.cpp \
    Model/Other/LogFile.cpp \
    Model/Other/SelectedSections.cpp \
    TinyXml/tinystr.cpp \
    TinyXml/tinyxml.cpp \
    TinyXml/tinyxmlerror.cpp \
    TinyXml/tinyxmlparser.cpp \
    stdafx.cpp \
    Tools/CTime.cpp \
    Tools/MyCFile.cpp \
    Player/SongUnit.cpp \
    #Tools/LxTimer.cpp \
    Player/SongPlay.cpp \
    Network/Protocol/CommandSend.cpp \
    Network/Protocol/CommandQueue.cpp \
    Network/Protocol/CommandHandle.cpp \
    Database/DataBase.cpp \
    Tools/ComputerInfo.cpp \
    SBENetwork.cpp \
    Tools/cJSON.c \
    Network/VOIP/VoipCommandHandle.cpp \
    webSocketTest.cpp \
    Network/Monitor/MonitorCommandHandle.cpp \
    Network/Monitor/MonitorProtocol.cpp \
    Network/VOIP/VoipProtocol.cpp \
    Network/VOIP/Voip.cpp \
    Tools/base64.cpp \
    Tools/sha1.cpp \
    Network/Web/WebCommandSend.cpp \
    Network/Web/WebSocket.cpp \
    Network/Web/WebProtocol.cpp \
    Network/Web/WebHandle.cpp \
    Network/Web/WebNetwork.cpp \
    Model/Device/WebSection.cpp \
    VoipTest.cpp \
    Model/Other/VoipSipInfo.cpp \
    Network/VOIP/VoipCommandSend.cpp \
    Network/Web/WebQueue.cpp \
    Database/UserTable.cpp \
    Model/User/UserManager.cpp \
    Model/Recv/WebData.cpp \
    Model/Recv/ExtraUser.cpp \
    Model/Recv/ExtraGroup.cpp \
    Model/Recv/ExtraTimer.cpp \
    Model/Recv/ExtraFireCollector.cpp \
    Network/Web/QtWebsocket.cpp \
    Model/Recv/ExtraMonitorEvent.cpp \
    CLI/CLIServer.cpp \
    Tools/CJSONRW.cpp \
    Tools/Control/TTS.cpp \
    Player/AUXPlay.cpp \
    Network/Monitor/MonProc.cpp \
    Model/Device/Monitor.cpp \
    Player/ListenIn.cpp \
    Model/Device/Server.cpp \
    Model/Device/LocalServer.cpp \
    Model/LocalHost.cpp \
    Tools/FileSync.cpp \
    Model/Device/PlayedRecently.cpp \
    Tools/Control/CRegister.cpp \
    Tools/UpgradeManager.cpp \
    Model/Device/StreamingGateway.cpp \
    Tools/Ukey/D8_win32.cpp \
    Tools/Ukey/Softdog.cpp \
    Model/Device/SequencePower.cpp \
    Model/Device/RemoteControl.cpp \
    Tools/buildTime.cpp \
    Database/RecordTable.cpp \
    Player/RecordManager.cpp

HEADERS += \
    Global/GlobalMethod.h \
    Lib/SBETcpClientSocket.h \
    Lib/SBETcpServerSocket.h \
    Lib/mpg123.h \
    Lib/sqlite3.h \
    Model/Device/AudioCall.h \
    Model/Device/AudioListen.h \
    #Network/HPSocket/GlobalDef.h \
    #Network/HPSocket/GlobalErrno.h \
    #Network/HPSocket/HPSocket-SSL.h \
    Network/HPSocket/HPSocket4C.h \
    Network/HPSocket/HPTypeDef.h \
    Network/HPSocket/MyTCPServer.h \
    Network/HPSocket/MyUdpNode.h \
    Network/HPSocket/SocketInterface.h \
    Network/Kcp/ikcp.h \
    Network/Kcp/kcp.h \
    Network/Web/WebPaging.h \
    Tools/CMyString.h \
    Global/Const.h \
    Model/Device/AudioCollector.h \
    Network/Protocol/Protocol.h \
    Model/Device/AudioForward.h \
    Model/Device/DeviceEQ.h \
    Model/Device/BlueTooth.h \
    Database/LogTable.h \
    Model/Other/Group.h \
    Model/Device/DeviceMixing.h \
    Model/Device/FireCollector.h \
    Model/Device/NetworkInfo.h \
    Global/CType.h \
    Model/Device/PowerOutputInfo.h \
    Model/Device/SipInfo.h \
    Model/Device/Section.h \
    Model/Other/PlayList.h \
    Global/GlobalData.h \
    Network/Network.h \
    Model/Other/TimerScheme.h \
    Tools/G722/g722.h \
    Tools/G722/g722_decoder.h \
    Tools/G722/g722_encoder.h \
    Tools/G722/g722_private.h \
    Tools/Language.h \
    Player/SongTool.h \
    Player/PlayQueue.h \
    Tools/Ini.h \
    Tools/MyLog.h \
    Tools/FileManager.h \
    Model/Other/LogFile.h \
    Model/Other/SelectedSections.h \
    TinyXml/tinystr.h \
    TinyXml/tinyxml.h \
    stdafx.h \
    Tools/CTime.h \
    Tools/MyCFile.h \
    Player/SongUnit.h \
    Tools/tools.h \
    #Tools/LxTimer.h \
    Player/SongPlay.h \
    Network/Protocol/CommandSend.h \
    Network/Protocol/CommandQueue.h \
    Network/Protocol/CommandHandle.h \
    Database/DataBase.h \
    Tools/ComputerInfo.h \
    SBENetwork.h \
    Tools/cJSON.h \
    Network/VOIP/VoipCommandHandle.h \
    webSocketTest.h \
    Network/Monitor/MonitorCommandHandle.h \
    Network/Monitor/MonitorProtocol.h \
    Network/VOIP/VoipProtocol.h \
    Network/VOIP/Voip.h \
    Tools/base64.h \
    Tools/sha1.h \
    Network/Web/WebCommandSend.h \
    Network/Web/WebSocket.h \
    Network/Web/WebProtocol.h \
    Network/Web/WebHandle.h \
    Network/Web/WebNetwork.h \
    Model/Device/WebSection.h \
    VoipTest.h \
    Model/Other/VoipSipInfo.h \
    Network/VOIP/VoipCommandSend.h \
    Network/Web/WebQueue.h \
    Database/UserTable.h \
    Model/User/UserManager.h \
    Model/Recv/WebData.h \
    Model/Recv/ExtraUser.h \
    Model/Recv/ExtraGroup.h \
    Model/Recv/ExtraTimer.h \
    Model/Recv/ExtraFireCollector.h \
    Network/Web/QtWebsocket.h \
    Model/Recv/ExtraMonitorEvent.h \
    CLI/CLIServer.h \
    Tools/CJSONRW.h \
    Tools/Control/TTS.h \
    Tools/Control/msp_cmn.h \
    Tools/Control/msp_errors.h \
    Tools/Control/msp_types.h \
    Tools/Control/qtts.h \
    Player/AUXPlay.h \
    Network/Monitor/MonProc.h \
    Model/Device/Monitor.h \
    Player/ListenIn.h \
    Model/Device/Server.h \
    Model/Device/LocalServer.h \
    Model/LocalHost.h \
    Tools/FileSync.h \
    Model/Device/PlayedRecently.h \
    Tools/Control/CRegister.h \
    Tools/UpgradeManager.h \
    Model/Device/StreamingGateway.h \
    Tools/Ukey/D8_win32.h \
    Tools/Ukey/hidpi.h \
    Tools/Ukey/hidsdi.h \
    Tools/Ukey/hidusage.h \
    Tools/Ukey/KeyDef.h \
    Tools/Ukey/Softdog.h \
    Tools/Ukey/UKey.h \
    Model/Device/SequencePower.h \
    Model/Device/RemoteControl.h \
    Tools/buildTime.h \
    Database/RecordTable.h \
    Player/RecordManager.h
}


unix:!macx{
LIBS += -luuid -lpthread -lmpg123 -lsqlite3 -lcrypto -lssl -lasound -lcurl -lcrypt -lssh
LIBS += -L$$PWD/Server/ -lmsc
}
win32{
LIBS += -lIPHLPAPI -lpthread -lole32
}


#################################################

INCLUDEPATH += $$PWD/Lib
DEPENDPATH += $$PWD/Lib

#################################################

INCLUDEPATH += $$PWD/Network/HPSocket/
DEPENDPATH += $$PWD/Network/HPSocket/
#################################################

CONFIG(debug,debug|release){

  unix:!macx: LIBS += -L$$PWD/Lib/ -lUDPNetworkLib
  unix:!macx: LIBS += -L$$PWD/Lib/ -lTCPSocketLib
  unix:!macx: LIBS += -L$$PWD/Network/HPSocket/ -lhpsocket
  unix:!macx: LIBS += -L$$PWD/Tools/Ukey/ -lusb-1.0 -ludev
  unix:!macx: LIBS += -L$$PWD/Lib/ -lspeexdsp

  unix:!macx: PRE_TARGETDEPS += $$PWD/Lib/libUDPNetworkLib.a
  unix:!macx: PRE_TARGETDEPS += $$PWD/Lib/libTCPSocketLib.a
  unix:!macx: PRE_TARGETDEPS += $$PWD/Network/HPSocket/libhpsocket.a
  unix:!macx: PRE_TARGETDEPS += $$PWD/Tools/Ukey/libusb-1.0.a
  unix:!macx: PRE_TARGETDEPS += $$PWD/Lib/libspeexdsp.a
  
  win32:LIBS += $$PWD/Network/HPSocket/HPSocket4C_D_x64.lib
  win32:LIBS += $$PWD/Lib/mpg123_x64.dll
  win32:LIBS += $$PWD/Lib/sqlite3_x64.dll
  win32:LIBS += $$PWD/Lib/msc_x64.lib
  win32:LIBS += $$PWD/Lib/libspeexdsp-1.dll
}
CONFIG(release,debug|release){

  unix:!macx: LIBS += -L$$PWD/Lib/ -lUDPNetworkLib_r
  unix:!macx: LIBS += -L$$PWD/Lib/ -lTCPSocketLib_r
  unix:!macx: LIBS += -L$$PWD/Network/HPSocket/ -lhpsocket_r
  unix:!macx: LIBS += -L$$PWD/Tools/Ukey/ -lusb-1.0 -ludev
  unix:!macx: LIBS += -L$$PWD/Lib/ -lspeexdsp

  unix:!macx: PRE_TARGETDEPS += $$PWD/Lib/libUDPNetworkLib_r.a
  unix:!macx: PRE_TARGETDEPS += $$PWD/Lib/libTCPSocketLib_r.a
  unix:!macx: PRE_TARGETDEPS += $$PWD/Network/HPSocket/libhpsocket_r.a
  unix:!macx: PRE_TARGETDEPS += $$PWD/Tools/Ukey/libusb-1.0.a
  unix:!macx: PRE_TARGETDEPS += $$PWD/Lib/libspeexdsp.a
  
  
  win32:LIBS += $$PWD/Network/HPSocket/HPSocket4C_x64.lib
  win32:LIBS += $$PWD/Lib/mpg123_x64.dll
  win32:LIBS += $$PWD/Lib/sqlite3_x64.dll
  win32:LIBS += $$PWD/Lib/msc_x64.lib
  win32:LIBS += $$PWD/Lib/libspeexdsp-1.dll

  #QMAKE_CXXFLAGS_RELEASE = $$QMAKE_CFLAGS_RELEASE_WITH_DEBUGINFO
  #QMAKE_LFLAGS_RELEASE = $$QMAKE_LFLAGS_RELEASE_WITH_DEBUGINFO
}

#################################################

INCLUDEPATH += $$PWD/Server
DEPENDPATH += $$PWD/Server

DISTFILES +=
QMAKE_CXXFLAGS_WARN_ON += -Wno-unused-parameter -Wno-unused-result -Wno-format -Wno-sign-compare
QMAKE_CXXFLAGS_WARN_ON -= -Wall -W
QMAKE_CFLAGS = -Wno-unused-parameter -Wno-unused-result -Wno-format
win32{
QMAKE_CXXFLAGS += -Wl,--stack,10000000
QMAKE_CXXFLAGS += -Wl,--heap,50000000
}


#删除文件
defineTest(delFile) {
    file = $$1
    # 替换Windows源路径中的斜杠
    win32:file ~= s,/,\\,g
    # 删除命令
    QMAKE_POST_LINK += $$QMAKE_DEL_FILE $$shell_quote($$file)  $$escape_expand(\\n\\t)
    export(QMAKE_POST_LINK)
}
#删除文件，实现每次编译都会更新编译时间
delFile($$OBJECTS_DIR/buildTime.o)
