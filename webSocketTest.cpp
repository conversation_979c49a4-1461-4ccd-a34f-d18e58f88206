//#include "webSocketTest.h"
//#include "Network/WebSocekt/websocket_handler.h"
//#include "Network/WebProtocol/WebCommandHandled.h"


//void WebCommandTest()
//{
//    //CWebCommandHandle handle;
//    Websocket_Handler *handle1 = new Websocket_Handler(12);

//    // 3.1 获取分区文件信息
//    //string command = "{\"command\":\"get_zone_file\"}";

//    // 3.5、获取分区状态信息
//    //string command = "{\"command\":\"get_zone_status\",\"zone_mac\":"0"}";

//    // 4.1 设置选中分区
//    string command = "{\"command\":\"set_select_zones\"," \
//                     "\"id\":1," \
//                     "\"page_count\":1," \
//                     "\"page\":1," \
//                     "\"zone_count\":1," \
//                     "\"zones_mac\":" \
//                     "[\"00:66:68:00:48:10\"]"\
//                     "}";


//    printf("command:%s\n", command.data());
//    handle.HandleCommand(handle1, command);

///**/
//    // 4.2 播放节目源
//    command = "{\"command\":\"play_source\"," \
//              "\"source\":1," \
//              "\"list_id\":\"2D9731C6-7C29-41F2-8656-0BDDDBA35635\","\
//              "\"source_name\":\"dd.mp3\""
//              "}";


///*    command = "{\"command\":\"set_volume\","\
//                "\"volume\":20}";*/



//    printf("command:%s\n", command.data());
//    handle.HandleCommand(handle1, command);

//}
