#!/bin/bash

echo "factory.sh"


Shell_PID=$$
trap 'exit 1' TERM

#Update_Path="/usr/local/apache2/htdocs/Update/"
Update_Path="/mnt/ServerBackup/"
Update_Dir="Backup/"

exit_script()
{
  #kill -s TERM $Shell_PID      #can exit while in the pipe
  exit 1			#can't exit while in the pipe	
}


# creat dir if not exist
if [ ! -d $Update_Path ]; then
  mkdir -p $Update_Path
  chmod 0777 $Update_Path
  exit_script
fi

# creat dir if not exist
if [ ! -d $Update_Path$Update_Dir ]; then
  mkdir -p $Update_Path$Update_Dir
  chmod 0777 $Update_Path$Update_Dir
fi
#system language Vision
cp /mnt/yaffs2/voip/Pro/initShell/system.xml /usr/local/apache2/htdocs/js/
cp /mnt/yaffs2/voip/Pro/initShell/Language.conf  /mnt/yaffs2/voip/Pro/
#Flag
FLAG=(0 1 2 3 4)

#压缩包名称数组 一级压缩包
PackageName=("S9100_64L" "F9100LSer" "WB9100" "Ast9100" "Voip9100")

#解压路径
PackagePath=($Update_Path "/mnt/yaffs2/voip/Networking" "/usr/local/apache2/htdocs" "/mnt/yaffs2/voip/asterisk/etc/asterisk" "/mnt/yaffs2/voip/Pro")

matchResult=-1
function compareString
{
	if  [[ $1 =~ $2 && $1 =~ ".tar.gz" ]]; then
    	matchResult=0
  	else
    	matchResult=1
  	fi
}


fileName=""
function getFileName
{
	echo "getFileName"
	files=$(ls $1)
	matchname=""

	if [ "$2" == ${FLAG[0]} ]; then    #整个压缩包
   		matchname=${PackageName[0]}
  	elif [ "$2" == ${FLAG[1]} ]; then  #网络化程序
   		matchname=${PackageName[1]}
  	elif [ "$2" == ${FLAG[2]} ]; then  #网页
   		matchname=${PackageName[2]}
  	elif [ "$2" == ${FLAG[3]} ]; then  #Asterisk配置文件
   		matchname=${PackageName[3]}
  	elif [ "$2" == ${FLAG[4]} ]; then  #VOIP服务器
   		matchname=${PackageName[4]}
  	fi

  	for file in $files
  	do
  	compareString $file $matchname
  	if [ "$matchResult" == "0" ]; then
  		echo $file
  		fileName=$file
  		break;
  	fi
  	done
}


# decompressing file

getFileName $Update_Path ${FLAG[0]}		# 获取整个压缩包文件名称


if [ "$fileName" == "" ]; then
	echo "fileName is null"
else
	pathFile=$Update_Path$fileName
	tar -zxvf $pathFile -C $Update_Path$Update_Dir

	for((i=1;i<5;i++))
	do
		echo "=========== loop ==========="
		getFileName $Update_Path$Update_Dir ${FLAG[i]}
		if [ "$fileName" == "" ]; then
			echo "fileName is null"
		else
			pathFile=$Update_Path$Update_Dir$fileName
			echo pathFile
			echo "解压到: ${PackagePath[i]}"
			tar -zxvf $pathFile -C ${PackagePath[i]}
		fi
	done
fi

echo "================  clear all config   ==============="
rm -rf /usr/local/apache2/htdocs/Data/*      # remove all data
rm -rf /mnt/yaffs2/voip/Networking/Log/*     # remove all log


echo "================  clear all backup file   ==============="
rm -rf $Update_Path$Update_Dir

ldconfig
chmod 0777 /mnt/yaffs2/voip/Networking/SBENetworking
chmod 0777 /usr/local/apache2/htdocs/*
chmod 0777 /usr/local/apache2/htdocs/cgi-bin/*
chmod 0777 /usr/local/apache2/htdocs/css/*
chmod 0777 /usr/local/apache2/htdocs/images/*
chmod 0777 /usr/local/apache2/htdocs/js/*
chmod 0777 /mnt/yaffs2/voip/asterisk/etc/asterisk/*

echo "========= Restore Factory Setting Succeed! reboot... ========="
shutdown -r now



