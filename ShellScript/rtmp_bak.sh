FLAG=$1
RTSP=$2
OPEN="1"
OFF="2"
echo $FLAG
echo $RTSP
if [ $( pgrep -f ffmpeg | wc -l ) -eq 0 ]; then
    echo "进程不存在"
    #ID=`pidof ffmpeg`
	#echo $ID
else
    echo "存在进程"
    ID=`pidof ffmpeg`
	echo $ID
	kill  -9 $ID
fi
if [ "$1"x = "$OPEN"x ]; then 
	echo "开启转换"
	ffmpeg -i "$RTSP" -an -rtsp_transport tcp -q 8 -f mpegts -codec:v mpeg1video -s 1366x768 http://127.0.0.1:8083/supersecret&
else 
	echo "结束转换"
fi

#(ffmpeg -i "rtsp://admin:abc123456@192.168.200.61:554/cam/realmonitor?channel=1&subtype=0" -f flv -r 25 -s 1280*1080 -an "rtmp://127.0.0.1:1935/live/text3")&
#(ffmpeg -i $RTSP -f flv -r 25 -s 1280*1080 -an "rtmp://127.0.0.1:1935/live/text3")&
#ffmpeg -i "rtsp://admin:abc123456@192.168.200.60:554/cam/realmonitor?channel=1&subtype=0" -q 0 -f mpegts -codec:v mpeg1video -s 1366x768 http://127.0.0.1:8083/supersecret
#ffmpeg -i "$RTSP" -q 0 -f mpegts -codec:v mpeg1video -s 1366x768 http://127.0.0.1:8083/supersecret
