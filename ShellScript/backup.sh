#!/bin/bash
#"backup.sh"

#Source_Path="/usr/local/apache2/htdocs/Data/Xml/"
#Backup_Path="/usr/local/apache2/htdocs/Backup/"


# 文件名定义
FILE_SECTION="Zone.xml"
FILE_LIMIT_SECTION="Zone_limit.xml"
FILE_GROUP="Group.xml"
FILE_TIMER="Timer.xml"
FILE_PLAYLIST="Playlist.xml"
FILE_AUDIO="AudioCollector.xml"
FILE_FIRE="FireCollector.xml"
FILE_MONITOR="Monitor.xml"
FILE_SEQUENCE_POWER="SequencePower.xml"
FILE_PAGER="Pager.xml"
FILE_REMOTE_CONTROLER="RemoteControler.xml"
FILE_USER="user.db"
#FILE_VOIP="*"
#FILE_VOIPFILE="asterisk/"

# 文件路径定义
PATH_XML="/usr/local/apache2/htdocs/Data/Xml/"
PATH_USER="/usr/local/apache2/htdocs/Data/"
PATH_BACKUP="/usr/local/apache2/htdocs/Backup/"
PATH_VOIP="/mnt/yaffs2/voip/asterisk/etc/asterisk/"
PATH_SONG="/usr/local/apache2/htdocs/Data/Program/"

########################################################

# creat dir if not exist
if [ ! -d $PATH_BACKUP ]; then
  	mkdir -p $PATH_BACKUP
  	chmod 0777 $PATH_BACKUP 
fi

# creat dir if not exist
if [ ! -d $PATH_BACKUP$2 ]; then
  	mkdir -p $PATH_BACKUP$2
  	chmod 0777 $PATH_BACKUP$2
fi

# 备份配置文件
if [ $1 -eq 1 ]; then

	#VOIP asterisk
	#if [ ! -d $PATH_BACKUP$2$FILE_VOIPFILE ]; then
  		#mkdir -p $PATH_BACKUP$2"/"$FILE_VOIPFILE 
  		#chmod 0777 $PATH_BACKUP$2"/"$FILE_VOIPFILE 
	#fi

#########################################################

	# 复制指定文件到新建的文件夹
	cp $PATH_XML$FILE_SECTION  		$PATH_BACKUP$2/$FILE_SECTION
	cp $PATH_XML$FILE_LIMIT_SECTION $PATH_BACKUP$2/$FILE_LIMIT_SECTION
	cp $PATH_XML$FILE_GROUP    		$PATH_BACKUP$2/$FILE_GROUP 		
	cp $PATH_XML$FILE_TIMER    		$PATH_BACKUP$2/$FILE_TIMER 		
	cp $PATH_XML$FILE_PLAYLIST   	$PATH_BACKUP$2/$FILE_PLAYLIST 		
	cp $PATH_XML$FILE_AUDIO      	$PATH_BACKUP$2/$FILE_AUDIO 		
	cp $PATH_XML$FILE_FIRE       	$PATH_BACKUP$2/$FILE_FIRE
	cp $PATH_XML$FILE_MONITOR		$PATH_BACKUP$2/$FILE_MONITOR
	cp $PATH_XML$FILE_SEQUENCE_POWER $PATH_BACKUP$2/$FILE_SEQUENCE_POWER
	cp $PATH_XML$FILE_PAGER 		$PATH_BACKUP$2/$FILE_PAGER
	cp $PATH_XML$FILE_REMOTE_CONTROLER $PATH_BACKUP$2/$FILE_REMOTE_CONTROLER
	cp $PATH_USER$FILE_USER         $PATH_BACKUP$2/$FILE_USER			
	#cp $PATH_VOIP$FILE_VOIP         $PATH_BACKUP$2/$FILE_VOIPFILE

# 备份歌曲文件
else
	cp $PATH_XML$FILE_PLAYLIST   	$PATH_BACKUP$2/$FILE_PLAYLIST 
	cp -r $PATH_SONG  $PATH_BACKUP$2
	
fi

cd $PATH_BACKUP
tar zcvf $2.tar.gz $2
md5_val=$( md5sum $2.tar.gz | cut -d ' ' -f1)
echo $md5_val > md5
echo $md5_vala
tar zcvf $2.tar.gz_tmp $2.tar.gz md5
mv $2.tar.gz_tmp $2.tar.gz
rm -rf $2
rm -rf md5





