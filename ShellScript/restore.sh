#!/bin/bash
#"Restore.sh"

#Source_Path="/usr/local/apache2/htdocs/Data/Xml/"
#Backup_Path="/usr/local/apache2/htdocs/Backup/"

# 文件名定义
FILE_SECTION="Zone.xml"
FILE_LIMIT_SECTION="Zone_limit.xml"
FILE_GROUP="Group.xml"
FILE_TIMER="Timer.xml"
FILE_PLAYLIST="Playlist.xml"
FILE_AUDIO="AudioCollector.xml"
FILE_FIRE="FireCollector.xml"
FILE_MONITOR="Monitor.xml"
FILE_USER="user.db"
FILE_SEQUENCE_POWER="SequencePower.xml"
FILE_PAGER="Pager.xml"
FILE_REMOTE_CONTROLER="RemoteControler.xml"
#FILE_VOIP="*"
#FILE_VOIPFILE="asterisk/"
FILE_SONG="*"
FILE_PROGRAM="Program"
FILE_PROGRAM_COMMON="Common"
FILE_PROGRAM_OTHER="Other"

# 文件路径定义
PATH_XML="/usr/local/apache2/htdocs/Data/Xml/"
PATH_USER="/usr/local/apache2/htdocs/Data/"
PATH_BACKUP="/usr/local/apache2/htdocs/Backup/"
PATH_VOIP="/mnt/yaffs2/voip/asterisk/etc/asterisk/"
PATH_SONG="/usr/local/apache2/htdocs/Data/"

##############################################################

# 解压文件
cd $PATH_BACKUP/restore
tar zxvf $2

# 移动文件到指定目录
cd $PATH_BACKUP/restore/$(basename $2 .tar.gz)
chmod 0777 ./*

if [ $1 -eq 1 ]; then
	mv ./$FILE_SECTION 		$PATH_XML$FILE_SECTION
	mv ./$FILE_LIMIT_SECTION $PATH_XML$FILE_LIMIT_SECTION
	mv ./$FILE_GROUP 		$PATH_XML$FILE_GROUP
	mv ./$FILE_TIMER 		$PATH_XML$FILE_TIMER
	mv ./$FILE_PLAYLIST 	$PATH_XML$FILE_PLAYLIST
	mv ./$FILE_AUDIO 		$PATH_XML$FILE_AUDIO
	mv ./$FILE_FIRE 		$PATH_XML$FILE_FIRE
	mv ./$FILE_MONITOR		$PATH_XML$FILE_MONITOR
	mv ./$FILE_SEQUENCE_POWER $PATH_XML$FILE_SEQUENCE_POWER
	mv ./$FILE_PAGER 		$PATH_XML$FILE_PAGER
	mv ./$FILE_REMOTE_CONTROLER $PATH_XML$FILE_REMOTE_CONTROLER
	mv ./$FILE_USER			$PATH_USER$FILE_USER
	#mv ./$FILE_VOIPFILE$FILE_VOIP	$PATH_VOIP
	rm $PATH_XML/User.xml

else   
	mv ./$FILE_PLAYLIST 	$PATH_XML$FILE_PLAYLIST	
	cp -r ./$FILE_PROGRAM  	$PATH_SONG

fi

rm -rf $PATH_BACKUP/restore






