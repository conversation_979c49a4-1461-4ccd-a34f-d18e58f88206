#!/bin/bash

echo "upgrade.sh V1.0.1"
Shell_PID=$$
trap 'exit 1' TERM

Update_Path="/usr/local/apache2/htdocs/Update/"
exit_script()
{
  #kill -s TERM $Shell_PID      #can exit while in the pipe
  exit 1			#can't exit while in the pipe	
}


# creat dir if not exist
if [ ! -d $Update_Path ]; then
  mkdir -p $Update_Path
  exit_script
fi




# ************************************************
# get all filename in specified path
path=$Update_Path
matchResult=-1

# match a string 匹配压缩包是否合法，合法matchResult=0；不合法matchResult=1
function matchString
{
  if  [[ $1 =~ $2 && $1 =~ ".tar.gz" ]]; then
    matchResult=0
  else
    matchResult=1
  fi
}



filename=""
function findFile
{
  files=$(ls $1)
  matchname=""
  filename=""

  if [ "$2" == 0 ]; then    #整个压缩包
   matchname=${PackageName[0]}
  elif [ "$2" == 1 ]; then  #网络化程序
   matchname=${PackageName[1]}
  elif [ "$2" == 2 ]; then  #网页
   matchname=${PackageName[2]}
  elif [ "$2" == 3 ]; then  #Monitor
   matchname=${PackageName[3]}
  fi
  
  if [ -z "$matchname" ]; then
	echo "matchname is null,exit!"
	return
  fi

  for file in $files
  do
  #echo $file
  matchString $file $matchname
 # echo $matchResult
  if [ "$matchResult" == "0" ]; then
    echo "find success" 
    filename=$file
    break;
  fi
  done
}

PackageName=("S001All" "S001Ser" "S001Web" "S001Mon")
#解压路径

PackagePath=($path "/mnt/yaffs2/voip/Networking" "/usr/local/apache2/htdocs" "/mnt/yaffs2/voip/Monitor")
#标号(外侧压缩包 网络化程序 网页 监控服务器)

# 第一个压缩包已解压,从第二个开始查找
for((i=0;i<4;i++));     
do
echo ""
echo "===========  loop  =================="
	# Loop through the second layer of folders 
	findFile $path $i 
	if [ "$filename" == "" ]; then
	echo "${PackageName[i]} is null!"
	else
		pathfile=$path$filename
		echo $pathfile
		echo "压缩包路径: $pathfile "
		echo "解压到: ${PackagePath[i]}"
		mkdir -p ${PackagePath[i]}
		
		if [ "$i" == 2 ]; then    #整个压缩包
			rm -f ${PackagePath[i]}/css/app*.css
			rm -f ${PackagePath[i]}/css/chunk*.css
			rm -f ${PackagePath[i]}/fonts/*
			rm -f ${PackagePath[i]}/img/*
			rm -f ${PackagePath[i]}/js/chunk*.js*
			rm -f ${PackagePath[i]}/app*.js*
			rm -f ${PackagePath[i]}/*.ico
			rm -f ${PackagePath[i]}/*.png
		fi
		tar -zxvf $pathfile -C ${PackagePath[i]}
	fi

done

# fi


echo "=======  clear all upgrade file ======="
echo $Update_Path"*"
p=$Update_Path"*"
rm -rf $p


ldconfig  # update config
chmod 0777 /mnt/yaffs2/voip/Networking/SBENetworking
chmod 0777 /usr/local/apache2/htdocs/*
chmod 0777 /usr/local/apache2/htdocs/js/*
chmod 0777 /usr/local/apache2/htdocs/fonts/*
chmod 0777 /usr/local/apache2/htdocs/img/*
chmod 0777 /usr/local/apache2/htdocs/css/*
chmod 0777 /usr/local/apache2/htdocs/cgi-bin/*
chmod 0777 /usr/local/apache2/htdocs/images/*
chmod 0777 /mnt/yaffs2/voip/asterisk/etc/asterisk/*