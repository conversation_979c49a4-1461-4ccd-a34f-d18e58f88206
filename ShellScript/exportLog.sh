#!/bin/bash
echo "exportLog.sh"
if [ $# -eq 5 ]; then
	export_path=/usr/local/apache2/htdocs/Data/Log/$5

	if [ "$3" == 0 ]; then
		if [ -z "$4" ]; then
			sqlite3 -header -column -csv /usr/local/apache2/htdocs/Data/data.db "select * from DeviceLog where Date Between  '$1' and '$2';" > "$export_path"
		else
			sqlite3 -header -column -csv /usr/local/apache2/htdocs/Data/data.db "select * from DeviceLog where Date Between  '$1' and '$2' and Name='$4';" > "$export_path"
		fi
	else
		if [ -z "$4" ]; then
			sqlite3 -header -column -csv /usr/local/apache2/htdocs/Data/data.db "select * from DeviceLog where Date Between  '$1' and '$2' and LogType=$3;" > "$export_path"
		else
			sqlite3 -header -column -csv /usr/local/apache2/htdocs/Data/data.db "select * from DeviceLog where Date Between  '$1' and '$2' and LogType=$3 and Name='$4';" > "$export_path"
		fi
	fi
elif [ $# -eq 6 ]; then
	export_path=/usr/local/apache2/htdocs/Data/Log/$6

	if [ "$3" == 0 ]; then
		if [ -z "$4" ]; then
			if [ -z "$5" ]; then
				sqlite3 -header -column -csv /usr/local/apache2/htdocs/Data/data.db "select * from DeviceLog where Date Between  '$1' and '$2';" > "$export_path"
			else
				sqlite3 -header -column -csv /usr/local/apache2/htdocs/Data/data.db "select * from DeviceLog where Date Between  '$1' and '$2' and Name='$5';" > "$export_path"
			fi
		else
			sqlite3 -header -column -csv /usr/local/apache2/htdocs/Data/data.db "select * from DeviceLog where Date Between  '$1' and '$2' and Mac='$4';" > "$export_path"
		fi
	else
		if [ -z "$4" ]; then
			if [ -z "$5" ]; then
				sqlite3 -header -column -csv /usr/local/apache2/htdocs/Data/data.db "select * from DeviceLog where Date Between  '$1' and '$2' and LogType=$3;" > "$export_path"
			else
				sqlite3 -header -column -csv /usr/local/apache2/htdocs/Data/data.db "select * from DeviceLog where Date Between  '$1' and '$2' and LogType=$3 and Name='$5';" > "$export_path"
			fi
		else
			sqlite3 -header -column -csv /usr/local/apache2/htdocs/Data/data.db "select * from DeviceLog where Date Between  '$1' and '$2' and LogType=$3 and Mac='$4';" > "$export_path"
		fi
	fi
fi
