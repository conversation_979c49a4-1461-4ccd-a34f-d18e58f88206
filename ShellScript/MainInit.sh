#!/bin/bash
#
# MainInit.local
#
# This script is executed at the end of each multiuser runlevel.
# Make sure that the script will "exit 0" on success or any other
# value on error.
#
# In order to enable or disable this script just change the execution
# bits.
#
# By default this script does nothing.

ulimit -c unlimited
ulimit -s 102400
ulimit -SHn 102400
test_mode=0
stop_run_networking=0
export LD_LIBRARY_PATH=/mnt/yaffs2/voip/Networking/Serverlib:$LD_LIBRARY_PATH

echo "start Node for ffmpeg"
export NODE_PATH=/usr/local/apache2/htdocs/node_modules
node /usr/local/apache2/htdocs/websocket-relay.js supersecret 8083&

if [ $test_mode == 1 ]; then
	/etc/frp/updateIPV6.sh &
fi

while [ 1 ]
do
	#循环检测HTTP服务器
	if [ $( pgrep -f httpd | wc -l ) -eq 0 ]; then
		echo "restart web server!"
		rm /usr/local/apache2/logs/httpd.pid
		/usr/local/apache2/bin/apachectl start
	else
		sleep 1
		httpd_pid=$( head -n 1 /usr/local/apache2/logs/httpd.pid )
		if [ ! -e "/usr/local/apache2/logs/cgisock.$httpd_pid" ]; then
		echo "cgisock not exist,restart apache2"
		/usr/local/apache2/bin/apachectl restart
		fi	
	fi
	#循环检测网络化服务器
	if [ $stop_run_networking != 1 ]; then
		if [ $( pgrep -f SBENetworking | wc -l ) -eq 0 ]; then
			echo "restart Networking server!"
			cd /mnt/yaffs2/voip/Networking/
			./SBENetworking >/dev/null &
		fi
		
		if [ $test_mode == 1 ]; then
			stop_run_networking=1
		fi
	fi
	
	sleep 3
	
done
