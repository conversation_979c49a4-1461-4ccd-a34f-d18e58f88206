#!/bin/bash
#
# ServerInit.sh
#
# This script is executed at the end of each multiuser runlevel.
# Make sure that the script will "exit 0" on success or any other
# value on error.
#
# In order to enable or disable this script just change the execution
# bits.
#
# By default this script does nothing.

#设置时区为北京时间
cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
#检测系统升级
/mnt/yaffs2/voip/Networking/ShellScript/upgrade.sh
#初始化网络化服务器及WEB服务器
/mnt/yaffs2/voip/Networking/ShellScript/MainInit.sh &
#初始化监控服务器
/mnt/yaffs2/voip/Monitor/MonInit &
