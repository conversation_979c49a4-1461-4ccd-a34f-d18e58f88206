{"files.associations": {"iostream": "cpp", "qcoreapplication": "cpp", "qtimer": "cpp", "qlist": "cpp", "qtcpsocket": "cpp", "qwebsocket": "cpp", "qmap": "cpp", "qtconcurrent": "cpp", "vector": "cpp", "array": "cpp", "atomic": "cpp", "*.tcc": "cpp", "bitset": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "list": "cpp", "unordered_map": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "istream": "cpp", "limits": "cpp", "memory": "cpp", "new": "cpp", "ostream": "cpp", "numeric": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "cfenv": "cpp", "cinttypes": "cpp", "regex": "cpp", "utility": "cpp", "typeinfo": "cpp", "string": "cpp", "string_view": "cpp", "random": "cpp", "qmutex": "cpp", "bit": "cpp", "chrono": "cpp", "condition_variable": "cpp", "map": "cpp", "set": "cpp", "iterator": "cpp", "memory_resource": "cpp", "optional": "cpp", "ratio": "cpp", "future": "cpp", "mutex": "cpp", "shared_mutex": "cpp", "thread": "cpp", "variant": "cpp", "g722.h": "c", "qdebug": "cpp", "qmutexlocker": "cpp", "codecvt": "cpp", "source_location": "cpp", "qtglobal": "cpp", "qnetworkaccessmanager": "cpp", "qnetworkreply": "cpp", "qthreadpool": "cpp", "qfuture": "cpp", "qfuturewatcher": "cpp", "qnetworkproxy": "cpp", "qbytearray": "cpp"}, "sync.gist": "42bcab19b5fd957f89d5f8cf2e9e102e", "sync.syncExtensions": true, "sync.forceUpload": true, "search.followSymlinks": false, "git.autorefresh": false}