{"configurations": [{"name": "Linux", "includePath": ["${workspaceFolder}/**", "C:/Qt/Qt5.12.9/5.12.9/mingw73_32/include/**", "C:/Qt/Qt5.12.9/Tools/mingw730_32/i686-w64-mingw32/include/**", "C:/Qt/Qt5.12.9/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/**", "C:/Qt/Qt5.12.9/5.12.9/mingw73_32/include/QtCore/**", "C:/Qt/Qt5.12.9/5.12.9/mingw73_32/include/QtConcurrent/**", "/home/<USER>/Qt5.12.9/5.12.9/Src/qtbase/src/corelib/tools/", "/home/<USER>/Qt5.12.9/5.12.9/Src/qtbase/src/corelib/global/", "/home/<USER>/Qt5.12.9/5.12.9/Src/qtbase/include/**", "/home/<USER>/Qt5.12.9/5.12.9/Src/qtwebsockets/include/", "/home/<USER>/Qt5.12.9/5.12.9/gcc_64/include/QtConcurrent/", "/home/<USER>/Qt5.12.9/5.12.9/gcc_64/include/**"], "defines": [], "compilerPath": "/usr/bin/gcc", "cStandard": "gnu17", "cppStandard": "c++14", "intelliSenseMode": "linux-gcc-x64"}], "version": 4}