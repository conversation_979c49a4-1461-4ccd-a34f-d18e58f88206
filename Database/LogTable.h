#ifndef LOGTABLE_H
#define LOGTABLE_H

#include "DataBase.h"
#include <vector>
#include "Tools/tools.h"

typedef enum
{
    LT_ALL_LOG			= 0,    // 全部日志
    LT_RUNNING_STATE,           // 运行日志
    LT_PLAY_PROGRAM,            // 播放日志
    LT_SYNC_FILE,               // 同步日志

    LT_ALARM,                   // 消防日志
    LT_CALL_LOG,                // 通话(对讲)日志
    LT_LISTEN_LOG,              // 监听日志
    LT_PAGING_LOG,              // 广播日志
    LT_ADVANCED_LOG             // 高级操作日志

}LogType;

typedef struct
{
    CMyString  strMac;
    CMyString	strName;
    CMyString	strLogType;
    CMyString	strLogContents;
    CMyString	strDate;
    CMyString	strTime;

}LogTableData;

class CLogTable
{
public:
    CLogTable(void);
    ~CLogTable(void);

public:
    void	SetDbManager(CDataBase* database);

    // 创建表
    bool	Create(void);

    // 向表中插入记录
    bool	InsertLog(CMyString strMac, CMyString strName, LogType logType, CMyString strLogContents);

    // 查询表中的数据
    bool	GetLogData(vector<LogTableData>& vecLogData, CMyString strSQL);

    // 查询表中的数据（根据日志类型、起始日期、结束日期、设备名称）
    bool	GetLogData(vector<LogTableData>& vecLogData, LogType logType, CTime& tDateStart, CTime& tDateEnd, CMyString strName = (""),CMyString strMac = (""));

    // 导出数据到csv文件
    bool ExportLogData(CMyString strSQL);

    // 写入xml
    bool    WriteXmlFile(vector<LogTableData>& vecLogData, CMyString strFileName);

private:
    CMyString GetLogTypeSQL(LogType logType);

private:
    CDataBase *m_DataBase;
};



#endif // LOGTABLE_H
