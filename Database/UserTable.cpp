#include "stdafx.h"
#include "UserTable.h"

CUserTable::CUserTable()
{
    m_DataBase = NULL;
}

CUserTable::~CUserTable()
{

}

void CUserTable::SetDbManager(CDataBase *database)
{
    m_DataBase = database;
}

// 创建表
bool CUserTable::Create()
{
    CMyString strSQL;
    #if APP_IS_AISP_TZY_ENCRYPTION
    strSQL.Format("CREATE TABLE if not exists %s ("
                  "%s TEXT PRIMARY KEY NOT NULL,"
                  "%s TEXT NOT NULL,"
                  "%s TEXT NOT NULL,"
                  "%s TEXT NOT NULL,"   //权限码
                  "%s TEXT NOT NULL,"   //证书
                  "%s TEXT NOT NULL,"
                  "%s INT NOT NULL,"
                  "%s TEXT NOT NULL,"
                  "%s TEXT NOT NULL,"
                  "%s INT NOT NULL,"
                  "%s INT NOT NULL);", USER_TABLE_NAME,USER_TABLE_COLUMN_USERACCOUNT,USER_TABLE_COLUMN_PARUSERACCOUNT,USER_TABLE_COLUMN_USERPASSWORD,USER_TABLE_COLUMN_LIMITCODE,USER_TABLE_COLUMN_CERTIFICATE,\
                  USER_TABLE_COLUMN_USERID,USER_TABLE_COLUMN_PLAYMODE,USER_TABLE_COLUMN_LISTENDEVICE,USER_TABLE_COLUMN_USERNAME,USER_TABLE_COLUMN_USERTYPE,USER_TABLE_COLUMN_STORAGECAPACITY);
    #else
    strSQL.Format("CREATE TABLE if not exists %s ("
                  "%s TEXT PRIMARY KEY NOT NULL,"
                  "%s TEXT NOT NULL,"
                  "%s TEXT NOT NULL,"
                  "%s INT NOT NULL,"
                  "%s TEXT NOT NULL,"
                  "%s INT NOT NULL,"
                  "%s TEXT NOT NULL,"
                  "%s TEXT NOT NULL,"
                  "%s INT NOT NULL,"
                  "%s INT NOT NULL);", USER_TABLE_NAME,USER_TABLE_COLUMN_USERACCOUNT,USER_TABLE_COLUMN_PARUSERACCOUNT,USER_TABLE_COLUMN_USERPASSWORD,USER_TABLE_COLUMN_LIMITCODE,\
                  USER_TABLE_COLUMN_USERID,USER_TABLE_COLUMN_PLAYMODE,USER_TABLE_COLUMN_LISTENDEVICE,USER_TABLE_COLUMN_USERNAME,USER_TABLE_COLUMN_USERTYPE,USER_TABLE_COLUMN_STORAGECAPACITY);
    #endif
    return (m_DataBase != NULL && m_DataBase->DirectExecuteSQL(strSQL.C_Str()));
}


bool CUserTable::InsertUser(UserData_t &userData)
{

    CMyString strSQL;
    #if APP_IS_AISP_TZY_ENCRYPTION
    string strLimitCode=to_string(userData.nLimitCode);
    string encryptPassword=g_Global.m_PasswordMod.sendEncryptRequest(userData.strPassword);
    string encryptLimitCode=g_Global.m_PasswordMod.sendEncryptRequest(strLimitCode);
    #if TZY_ENCRYPTION_LOG
    g_Global.m_Log.WriteLog("Encrypt:user=%s,password=%s,result=%s",userData.strAccount.data(),encryptPassword.data(),encryptPassword.empty()?"failed":"succeed");
    g_Global.m_Log.WriteLog("Encrypt:user=%s,limitCode=%s,result=%s",userData.strAccount.data(),encryptLimitCode.data(),encryptLimitCode.empty()?"failed":"succeed");
    #endif
    strSQL.Format("INSERT INTO %s VALUES('%s','%s','%s','%s', '%s','%s',%d,'%s','%s','%d','%d')", USER_TABLE_NAME, userData.strAccount.data(),
                  userData.strParAccount.data(), encryptPassword.data(), encryptLimitCode.data(), userData.strCertificate.data(), userData.strUserId.data(),userData.nPlayMode,userData.strListenDevice.data(),\
                  userData.strUserName.data(),userData.nUserType,userData.nStorageCapacity);
    #else
    strSQL.Format("INSERT INTO %s VALUES('%s','%s','%s',%d,'%s',%d,'%s','%s','%d','%d')", USER_TABLE_NAME, userData.strAccount.data(),
                userData.strParAccount.data(), userData.strPassword.data(), userData.nLimitCode,userData.strUserId.data(),userData.nPlayMode,userData.strListenDevice.data(),\
                userData.strUserName.data(),userData.nUserType,userData.nStorageCapacity);
    #endif
    return (m_DataBase != NULL && m_DataBase->DirectExecuteSQL(strSQL.C_Str()));
}

#if 0
bool CUserTable::InsertUser(string strAccount, string strParAccount, string strPassword, int nLimitCode,string strUserId,int nPlayMode,
                            string strListenDevice,string strUserName,int nUserType,int nStorageCapacity)
{
    CMyString strSQL;
    #if APP_IS_AISP_TZY_ENCRYPTION
    string strLimitCode=to_string(nLimitCode);
    string encryptPassword=g_Global.m_PasswordMod.sendEncryptRequest(strPassword);
    string encryptLimitCode=g_Global.m_PasswordMod.sendEncryptRequest(strLimitCode);
    strSQL.Format("INSERT INTO %s VALUES('%s','%s','%s',%s,'%s',%d,'%s','%s','%d','%d')", USER_TABLE_NAME, strAccount.data(),
                  strParAccount.data(), encryptPassword.data(), encryptLimitCode.data(),  strUserId.data(),nPlayMode,strListenDevice.data(),strUserName.data(),nUserType,nStorageCapacity);
    #else
    strSQL.Format("INSERT INTO %s VALUES('%s','%s','%s',%d,'%s',%d,'%s','%s','%d','%d')", USER_TABLE_NAME, strAccount.data(),
                strParAccount.data(), strPassword.data(), nLimitCode,strUserId.data(),nPlayMode,strListenDevice.data(),strUserName.data(),nUserType,nStorageCapacity);
    #endif
    return (m_DataBase != NULL && m_DataBase->DirectExecuteSQL(strSQL.C_Str()));
}
#endif

// 根据用户名删除用户
bool CUserTable::RemoveUser(string strAccount)
{
    if(strAccount.empty())
        return false;

    CMyString strSQL;
    strSQL.Format("delete from %s where UserAccount='%s'", USER_TABLE_NAME, strAccount.data());
    return (m_DataBase != NULL && m_DataBase->DirectExecuteSQL(strSQL.C_Str()));
}

// 查询用户数据
bool CUserTable::GetUserData(vector<UserData_t> &vecUserData, string strSQL)
{
    if(m_DataBase == NULL)
        return false;

    BaseData *uData = NULL;
    vecUserData.clear();
    uData = m_DataBase->ExecuteSQL(strSQL);
    if(uData != NULL)
    {
        if(uData->nColumn!=0)
        {
            BaseData *uData2 = NULL;
            //新增用户uuid列及播放模式列
            CMyString strSQL2;

            if( uData->nColumn < 5 )
            {
                strSQL2.Format("ALTER TABLE %s ADD COLUMN %s TEXT NOT NULL DEFAULT \"\"", USER_TABLE_NAME,USER_TABLE_COLUMN_USERID);
                m_DataBase->DirectExecuteSQL(strSQL2.Data());
            }
            if( uData->nColumn < 6 )
            {
                strSQL2.Format("ALTER TABLE %s ADD COLUMN %s INT NOT NULL DEFAULT 0", USER_TABLE_NAME,USER_TABLE_COLUMN_PLAYMODE);
                m_DataBase->DirectExecuteSQL(strSQL2.Data());
            }
            if( uData->nColumn <7 )
            {
                strSQL2.Format("ALTER TABLE %s ADD COLUMN %s TEXT NOT NULL DEFAULT \"\"", USER_TABLE_NAME,USER_TABLE_COLUMN_LISTENDEVICE);
                m_DataBase->DirectExecuteSQL(strSQL2.Data());
            }
            if( uData->nColumn <8 )
            {
                strSQL2.Format("ALTER TABLE %s ADD COLUMN %s TEXT NOT NULL DEFAULT \"\"", USER_TABLE_NAME,USER_TABLE_COLUMN_USERNAME);
                m_DataBase->DirectExecuteSQL(strSQL2.Data());
            }
            if( uData->nColumn <9 )
            {
                strSQL2.Format("ALTER TABLE %s ADD COLUMN %s INT NOT NULL DEFAULT %d", USER_TABLE_NAME,USER_TABLE_COLUMN_USERTYPE,USER_TYPE_LEVEL2);
                m_DataBase->DirectExecuteSQL(strSQL2.Data());
            }
            if( uData->nColumn <10 )
            {
                strSQL2.Format("ALTER TABLE %s ADD COLUMN %s INT NOT NULL DEFAULT %d", USER_TABLE_NAME,USER_TABLE_COLUMN_STORAGECAPACITY,0);
                m_DataBase->DirectExecuteSQL(strSQL2.Data());
            }
        }

        for(int i=uData->nColumn;i<(uData->nRow+1)*uData->nColumn;
            i+=uData->nColumn)
        {
            UserData_t userData;
            char* end = NULL;
            int index = 0;
            userData.strAccount       = uData->szResult[i+(index++)];
            userData.strParAccount       = uData->szResult[i+(index++)];
            string strPassword = uData->szResult[i+(index++)];

            //权限码需要先解密
            #if APP_IS_AISP_TZY_ENCRYPTION
            userData.strPassword = g_Global.m_PasswordMod.sendDecryptRequest(strPassword);
            //g_Global.m_Log.WriteLog("user:%s,DecryptPassword=%s",userData.strAccount.data(),strPassword.data());
            //printf("Read Account=%s,Password=%s\n",userData.strAccount.data(),userData.strPassword.data());

            string strLimitCode = uData->szResult[i+(index++)];
            //解密
            string strLimitCode_decrypt = g_Global.m_PasswordMod.sendDecryptRequest(strLimitCode);
            //g_Global.m_Log.WriteLog("user:%s,DecryptLimitCode=%s",userData.strAccount.data(),strLimitCode.data());
            //printf("strLimitCode=%s\n",strLimitCode.data());
            if(!strLimitCode_decrypt.empty())
                userData.nLimitCode = stoi(strLimitCode_decrypt);
            else
                userData.nLimitCode = 0;
            //printf("Read Account=%s,LimitCode=%d\n",userData.strAccount.data(),userData.nLimitCode);

            #else
            userData.strPassword = strPassword;
            userData.nLimitCode = atoi(uData->szResult[i+(index++)]);
            #endif

            #if APP_IS_AISP_TZY_ENCRYPTION
            //用户证书
            userData.strCertificate = uData->szResult[i+(index++)];
            #endif

            
            userData.strUserId = "";
            userData.nPlayMode = 0;
            userData.strListenDevice = "";
            userData.strUserName = "";
            //兼容以前的服务器版本，将账户类型默认设置为level2,此处先不单独改变admin的账户类型，由InsertSuperUser检测后更新数据库。
            userData.nUserType=USER_TYPE_LEVEL2;
            userData.nStorageCapacity=0;

            if( uData->nColumn > index )
            {
                userData.strUserId = uData->szResult[i+(index++)];
            }
            if( uData->nColumn > index )
            {
                userData.nPlayMode = atoi(uData->szResult[i+(index++)]);
            }
            if( uData->nColumn > index )
            {
                userData.strListenDevice = uData->szResult[i+(index++)];
            }
            if( uData->nColumn > index )
            {
                userData.strUserName = uData->szResult[i+(index++)];
            }
            if( uData->nColumn > index )
            {
                userData.nUserType = atoi(uData->szResult[i+(index++)]);
            }
            if( uData->nColumn > index )
            {
                userData.nStorageCapacity = atoi(uData->szResult[i+(index++)]);
            }

            vecUserData.push_back(userData);
        }
        sqlite3_free_table(uData->szResult);
        free(uData);
        return true;
    }

    return false;
}

// 更新数据，修改父用户
bool CUserTable::UpdateUserParAccount(string strAccount, string strParAccount)
{
    CMyString  strSQL;
    strSQL.Format("update %s set ParUserAccount='%s' where UserAccount='%s'",
                  USER_TABLE_NAME, strParAccount.data(), strAccount.data());

    return (m_DataBase!=NULL && m_DataBase->DirectExecuteSQL(strSQL.Data()));
}

// 更新数据 修改密码
bool CUserTable::UpdateUserPassword(string strAccount, string strPassword)
{
    #if APP_IS_AISP_TZY_ENCRYPTION
    string encryptPassword=g_Global.m_PasswordMod.sendEncryptRequest(strPassword);
    #if TZY_ENCRYPTION_LOG
    g_Global.m_Log.WriteLog("Encrypt:user=%s,password=%s,result=%s",strAccount.data(),encryptPassword.data(),encryptPassword.empty()?"failed":"succeed");
    #endif
    strPassword = encryptPassword;
    #endif
    CMyString  strSQL;
    strSQL.Format("update %s set UserPassword='%s' where UserAccount='%s'",
                  USER_TABLE_NAME, strPassword.data(), strAccount.data());

    return (m_DataBase!=NULL && m_DataBase->DirectExecuteSQL(strSQL.Data()));
}

// 更新数据 修改用户名称
bool CUserTable::UpdateUserName(string  strAccount, string  strUserName)
{
    CMyString  strSQL;
    strSQL.Format("update %s set UserName='%s' where UserAccount='%s'",
                  USER_TABLE_NAME, strUserName.data(), strAccount.data());

    return (m_DataBase!=NULL && m_DataBase->DirectExecuteSQL(strSQL.Data()));
}

// 更新数据 修改权限码
bool CUserTable::UpdateUserAuthority(string strAccount, int nAuthority)
{
    CMyString  strSQL;
    #if APP_IS_AISP_TZY_ENCRYPTION
    string strLimitCode=to_string(nAuthority);
    string encryptLimitCode=g_Global.m_PasswordMod.sendEncryptRequest(strLimitCode);
    #if TZY_ENCRYPTION_LOG
    g_Global.m_Log.WriteLog("Encrypt:user=%s,limitcode=%s,result=%s",strAccount.data(),encryptLimitCode.data(),encryptLimitCode.empty()?"failed":"succeed");
    #endif
    strSQL.Format("update %s set LimitCode='%s' where UserAccount='%s'",
                  USER_TABLE_NAME, encryptLimitCode.data(), strAccount.data());
    #else
    strSQL.Format("update %s set LimitCode='%d' where UserAccount='%s'",
                USER_TABLE_NAME, nAuthority, strAccount.data());
    #endif
    return (m_DataBase!=NULL && m_DataBase->DirectExecuteSQL(strSQL.Data()));
}

#if APP_IS_AISP_TZY_ENCRYPTION
bool  CUserTable::UpdateUserCertificate(string strAccount, string strCertificate)
{
    CMyString  strSQL;
    strSQL.Format("update %s set Certificate='%s' where UserAccount='%s'",
                  USER_TABLE_NAME, strCertificate.data(), strAccount.data());
    return (m_DataBase!=NULL && m_DataBase->DirectExecuteSQL(strSQL.Data()));
}
#endif


// 更新数据 用户uuid
bool CUserTable::UpdateUserId(string strAccount, string  strId)
{
    CMyString  strSQL;
    strSQL.Format("update %s set UserId='%s' where UserAccount='%s'",
                  USER_TABLE_NAME, strId.data(), strAccount.data());

    return (m_DataBase!=NULL && m_DataBase->DirectExecuteSQL(strSQL.Data()));
}

// 更新数据 播放模式
bool CUserTable::UpdateUserPlayMode(string strAccount, int nplayMode)
{
    CMyString  strSQL;
    strSQL.Format("update %s set PlayMode='%d' where UserAccount='%s'",
                  USER_TABLE_NAME, nplayMode, strAccount.data());

    return (m_DataBase!=NULL && m_DataBase->DirectExecuteSQL(strSQL.Data()));
}

// 更新数据 监听设备
bool CUserTable::UpdateUserListenDevice(string strAccount, string nListenDevice)
{
    CMyString  strSQL;
    strSQL.Format("update %s set ListenDevice='%s' where UserAccount='%s'",
                  USER_TABLE_NAME, nListenDevice.data(), strAccount.data());

    return (m_DataBase!=NULL && m_DataBase->DirectExecuteSQL(strSQL.Data()));
}

// 更新数据 用户类型
bool CUserTable::UpdateUserType(string strAccount, int nUserType)
{
    CMyString  strSQL;
    strSQL.Format("update %s set UserType='%d' where UserAccount='%s'",
                  USER_TABLE_NAME, nUserType, strAccount.data());

    return (m_DataBase!=NULL && m_DataBase->DirectExecuteSQL(strSQL.Data()));
}

// 更新数据 子账户存储容量
bool CUserTable::UpdateUserStorageCapacity(string strAccount, int nStorageCapactiy)
{
    CMyString  strSQL;
    strSQL.Format("update %s set StorageCapacity='%d' where UserAccount='%s'",
                  USER_TABLE_NAME, nStorageCapactiy, strAccount.data());

    return (m_DataBase!=NULL && m_DataBase->DirectExecuteSQL(strSQL.Data()));
}

// 创建分区分组文件
bool CUserTable::CreatUserDataTable(string strAccount)
{
    if(strAccount.length() <= 0)
        return false;

    return (CreateSecTable(strAccount));
}

// 删除分区分组数据库文件
bool CUserTable::RemoveUserTable(string strAccount)
{
    if(strAccount.length() <= 0)
        return false;

    string secName = strAccount + TAB_SECTION;
    string groupName = strAccount + TAB_GROUP;
    return (RemoveTable(secName));
}

bool CUserTable::ClearSecData(string strAccount)
{
    if(strAccount.length() <= 0)
        return false;

    string secName = strAccount + TAB_SECTION;
    return ClearTableData(secName);
}

bool CUserTable::ClearGroupData(string strAccount)
{
    if(strAccount.length() <= 0)
        return false;

    string secName = strAccount + TAB_GROUP;
    return ClearTableData(secName);
}

bool CUserTable::SetUserSecData(string strAccount, vector<string> &vecUserSec)
{
    string strTableName = strAccount + TAB_SECTION;
    if(m_DataBase == NULL)
        return false;
    m_DataBase->DirectExecuteSQL("begin;");
    for(int i=0; i<vecUserSec.size(); i++)
    {
        CMyString strSQL;
        strSQL.Format("INSERT INTO %s VALUES('%s')", strTableName.data(), vecUserSec[i].data());
        if(m_DataBase != NULL && m_DataBase->DirectExecuteSQL(strSQL.C_Str()))
        {

        }
        else
        {
            if(m_DataBase != NULL)
                m_DataBase->DirectExecuteSQL("rollback;");
            return false;
        }
    }
    m_DataBase->DirectExecuteSQL("commit;");


    return true;
}

bool CUserTable::SetUserGroupData(string strAccount, vector<string> &vecUserGroup)
{
    string strTableName = strAccount + TAB_SECTION;
    for(int i=0; i<vecUserGroup.size(); i++)
    {
        CMyString strSQL;
        strSQL.Format("INSERT INTO %s VALUES('%s')", strTableName.data(), vecUserGroup[i].data());
        if(m_DataBase != NULL && m_DataBase->DirectExecuteSQL(strSQL.C_Str()))
        {

        }
        else
        {
            return false;
        }
    }

    return true;
}

// 查询用户分区数据
bool CUserTable::GetUserSecData(string strAccount, vector<string> &vecUserSec)
{
    if(m_DataBase == NULL)
        return false;
    BaseData *uData = NULL;

    vecUserSec.clear();
    string strSQL = string("select * from ")+strAccount+ TAB_SECTION;
    uData = m_DataBase->ExecuteSQL(strSQL);

    if(uData != NULL)
    {
        for(int i=uData->nColumn;i<(uData->nRow+1)*uData->nColumn;
            i+=uData->nColumn)
        {
            string strMac  = uData->szResult[i];        //cout<<userData.strUserID<<endl;
            vecUserSec.push_back(strMac);
        }
        sqlite3_free_table(uData->szResult);
        free(uData);
        return true;
    }
    return false;
}

// 查询用户分组数据
bool CUserTable::GetUserGroupData(string strAccount, vector<string> &vecUserGroup)
{
    if(m_DataBase == NULL)
        return false;
    BaseData *uData = NULL;

    vecUserGroup.clear();
    string strSQL = string("select * from ")+strAccount+ TAB_GROUP;
    uData = m_DataBase->ExecuteSQL(strSQL);

    if(uData != NULL)
    {
        for(int i=uData->nColumn;i<(uData->nRow+1)*uData->nColumn;
            i+=uData->nColumn)
        {
            string strID  = uData->szResult[i];        //cout<<userData.strUserID<<endl;
            vecUserGroup.push_back(strID);
        }
        sqlite3_free_table(uData->szResult);
        free(uData);
        return true;
    }
    return false;
}


// 用户 是否存在
bool CUserTable::IsExistUser(string strAccount)
{
    CMyString strSQL;
    strSQL.Format("select * from %s", USER_TABLE_NAME, strAccount.data());

    if(m_DataBase == NULL)
        return false;
    BaseData *uData = NULL;
    uData = m_DataBase->ExecuteSQL(strSQL.Data());

    if(uData != NULL)
    {
        sqlite3_free_table(uData->szResult);
        free(uData);
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}



/***********************************************/

// 根据获取数据类型组合成SQL语句
string CUserTable::GetSQLString(string strAccount, SQLType sqlType)
{
    string strSQL;
    switch(sqlType)
    {
    case SQL_USER:
    {
        strSQL = string("select * from ")+USER_TABLE_NAME;
        break;
    }

    case SQL_SEC:
    {
        string tabName = strAccount + TAB_SECTION;
        strSQL = "select * from "+tabName;
        break;
    }

    case SQL_GROUP:
    {
        string tabName = strAccount + TAB_GROUP;
        strSQL = "select * from "+tabName;
        break;
    }

    default:
        break;
    }

    return strSQL;
}

bool CUserTable::CreateSecTable(string strAccount)
{
    string secName = strAccount + TAB_SECTION;
    string strSQL = "CREATE TABLE if not exists "+secName+"("
                                                          "SecMac  TEXT PRIMARY KEY NOT NULL)";

    return (m_DataBase!=NULL && m_DataBase->DirectExecuteSQL(strSQL));
}

#if 0
bool CUserTable::CreateGroupTable(string strAccount)
{
    string groupName = strAccount + TAB_GROUP;
    string strSQL = "CREATE TABLE if not exists "+groupName+"("
                                                            "GroupID  TEXT PRIMARY KEY NOT NULL)";

    return (m_DataBase!=NULL && m_DataBase->DirectExecuteSQL(strSQL));
}
#endif

bool CUserTable::RemoveTable(string strTableName)
{
    string strSQL = "DROP TABLE if exists "+strTableName;
    return (m_DataBase!=NULL && m_DataBase->DirectExecuteSQL(strSQL));
}

bool CUserTable::ClearTableData(string strTableName)
{
    string strSQL = "DELETE from " + strTableName;
    return (m_DataBase!=NULL && m_DataBase->DirectExecuteSQL(strSQL));
}









