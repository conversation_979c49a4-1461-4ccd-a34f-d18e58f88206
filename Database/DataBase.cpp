#include "stdafx.h"
#include "DataBase.h"
#include <QtConcurrent>
#include <QThreadPool>
#include <QFuture>
#include <QFutureWatcher>
#include <QTextStream>
#include <QStringList>
#include <QTextCodec>
#include <algorithm>
#include <exception>


CDataBase::CDataBase()
{
    m_db = NULL;
    sqlCmdMutex = PTHREAD_MUTEX_INITIALIZER;
}


CDataBase::~CDataBase()
{
    CloseData();
}


void CDataBase::CloseData()
{
    if(m_db != NULL)
    {
        sqlite3_close(m_db);
        m_db = NULL;
    }

}


int CDataBase::BusyHandler(void* ptr, int retry_times) {
  //std::cout << "Retry " << retry_times << " times." << std::endl;
  // 如果返回零则不会继续等待，外部的执行返回SQLITE_BUSY。
  // 如果返回非零则继续循环，等待其他应用释放DB锁。
  if(retry_times >= 300)
  {
    printf("dataBse BusyTimeout.\n");
    return 0;
  }

  sqlite3_sleep(10);
  return 1;
}


bool CDataBase::OpenData(string DataName,bool set_busy_handler)
{
    if(DataName.empty())
        return false;
    const char *strName = DataName.data();
    //设置脏读模式1（允许写的过程中数据库读，不过可能存在读出来数据与写入不一致的情况，可以避免SQLITE_LOCKED：database is locked）
    sqlite3_enable_shared_cache(1);
    int rc = sqlite3_open(strName,&m_db);
    if(rc != SQLITE_OK)
    {
        //printf("Can't open or create file:%s\n",sqlite3_errmsg(m_db));
        LOG(FORMAT("Can't open or create file:%s", sqlite3_errmsg(m_db)), LV_INFO);
        return false;
    }

    if(set_busy_handler)
    {
        sqlite3_busy_handler(m_db, BusyHandler, NULL);
    }

    //设置脏读模式2（允许写的过程中数据库读，不过可能存在读出来数据与写入不一致的情况，可以避免SQLITE_LOCKED：database is locked）
    if(DirectExecuteSQL("PRAGMA read_uncommitted = true;"))
    {
        printf("set dataBase:%s read_uncommitted=true!\n",DataName.data());
    }

    return true;

}


// 添加SQL命令到队列
bool CDataBase::AddSQLCommand(string strSQL)
{
    pthread_mutex_lock(&sqlCmdMutex);
    
    sqlCmdDqueue.push_back(strSQL);

    pthread_mutex_unlock(&sqlCmdMutex);

    return true;
}

#if APP_IS_AISP_TZY_ENCRYPTION
std::string extractStrConvertContents(const std::string& strSQL) {
    // 正则表达式匹配strConvertContents
    std::regex re("insert into \\w+ values\\('[^']*','[^']*',[^,]*,'([^']*)','[^']*','[^']*','[^']*'\\)");
    std::smatch match;
    if (std::regex_search(strSQL, match, re) && match.size() > 1) {
        return match.str(1); // 返回第一个捕获组
    }
    return "";
}
std::string modifyStrHmac(const std::string& strSQL, const std::string& newHmac) {

   // 正则表达式匹配 strHmac
    std::regex re("insert into \\w+ values\\('([^']*)','([^']*)',([^,]*),'([^']*)','([^']*)','([^']*)','([^']*)'\\)");
    std::smatch match;
    
    if (std::regex_search(strSQL, match, re)) {
        // 构建新的SQL语句，将旧的strHmac替换为新的newHmac
        std::string modifiedSQL = "insert into DeviceLog values('"
                                  + match.str(1) + "','" + match.str(2) + "'," + match.str(3) + ",'" + match.str(4) + "','"
                                  + newHmac + "','" + match.str(6) + "','" + match.str(7) + "')";
        return modifiedSQL;
    }
    
    return strSQL; // 如果没有找到匹配项，返回原始SQL
}
#endif

//处理Sql命令线程
void *CDataBase::HandleSqlCommandThread(void *lpParam)
{
    CDataBase* lpDataBase = (CDataBase*)lpParam;
    deque<string> *lpSqlCmdDqueue = &lpDataBase->sqlCmdDqueue;

    #if APP_IS_AISP_TZY_ENCRYPTION
    CPasswordMod passwordMod_sql;
    #endif

    while(1)
    {
        int strSqlCmd_cnt=lpSqlCmdDqueue->size();
        if(strSqlCmd_cnt == 0 || !lpDataBase->m_db)
        {
            usleep(250000);
            continue;
        }
        //printf("lpSqlCmdDqueue:size1=%d\n",lpSqlCmdDqueue->size());

        #if APP_IS_AISP_TZY_ENCRYPTION
        if(passwordMod_sql.getAccessToken()!=g_Global.m_PasswordMod.getAccessToken())
        {
            passwordMod_sql.setAccessToken(g_Global.m_PasswordMod.getAccessToken());
        }
        #endif

        bool bCorrect=true;
        if(lpDataBase->DirectExecuteSQL("begin;"))
        {
            for(int i=0;i<strSqlCmd_cnt;i++)
            {
                string strSQL = (*lpSqlCmdDqueue)[i];
                #if APP_IS_AISP_TZY_ENCRYPTION
                std::string strConvertContents = extractStrConvertContents(strSQL);
                //std::cout << "strConvertContents: " << strConvertContents << std::endl;
                string strHmac = passwordMod_sql.sendHmacRequest(strConvertContents.data());
                #if TZY_ENCRYPTION_LOG
                g_Global.m_Log.WriteLog("HMAC:%s,result=%s",strHmac.data(),strHmac.empty()?"failed":"succeed");
                #endif
                //printf("oldStrSQL=%s,strHmac=%s\n",strSQL.data(),strHmac.data());
                if(strHmac.empty())
                {
                    strHmac = CONNECT_CIPHER_ERROR_HMAC;
                }
                strSQL = modifyStrHmac(strSQL, strHmac);
                //printf("newStrSQL=%s\n",strSQL.data());
                #endif
                if(!lpDataBase->DirectExecuteSQL(strSQL))
                {
                    bCorrect=false;
                    lpDataBase->DirectExecuteSQL("rollback;");
                    break;
                }
            }
            if(bCorrect)
                lpDataBase->DirectExecuteSQL("commit;");
        }
        else
        {
            bCorrect=false;
            lpDataBase->DirectExecuteSQL("commit;");
        }
        //正确才删除，否则下次再进入
        if(bCorrect)
        {
            //删除队列前面已经处理过的字符串
            pthread_mutex_lock(&lpDataBase->sqlCmdMutex);
            lpSqlCmdDqueue->erase(lpSqlCmdDqueue->begin(),lpSqlCmdDqueue->begin()+strSqlCmd_cnt);
            pthread_mutex_unlock(&lpDataBase->sqlCmdMutex);
        }

        //printf("lpSqlCmdDqueue:size2=%d\n",lpSqlCmdDqueue->size());
        usleep(250000);
    }
}


bool CDataBase::StartWorking()
{
    pthread_t pid;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&pid, &attr, HandleSqlCommandThread, (void*)this);
    pthread_attr_destroy(&attr);
    return TRUE;
}

bool CDataBase::DirectExecuteSQL(string strSQL)
{
    if(strSQL.empty())
        return false;

    unique_lock<shared_timed_mutex> ulk_db(dbSharedMutex);              //写锁加锁

    const char* sql = strSQL.data();
    char *Err = NULL;
    int rc = sqlite3_exec(m_db,sql,0,NULL,&Err);

    //避免数据库已存在数据添加时报错，用于避免创建表时表已存在的错误
    if(Err != NULL )  //&& rc!=SQLITE_CONSTRAINT && rc != SQLITE_ERROR)
    {
        LOG(strSQL.data(), LV_ERROR);
        LOG(FORMAT("Err:%s | rc:%d -- %s:%d:%s ", Err, rc, __FILE__, __LINE__, __FUNCTION__), LV_ERROR);
        sqlite3_free(Err);
    }
    if(rc != SQLITE_OK)
    {
        return false;
    }
    
    return true;
}


BaseData* CDataBase::ExecuteSQL(string strSQL)
{
    if(strSQL.empty())
        return NULL;

    shared_lock<shared_timed_mutex> slk_db(dbSharedMutex);              //读锁加锁
    
    BaseData *uData = (BaseData*)malloc(sizeof(BaseData));

    const char* sql = strSQL.data();
    char* Err = NULL;
    int rc = sqlite3_get_table(m_db,sql,&uData->szResult,&uData->nRow,&uData->nColumn,&Err);
    if(Err != NULL)
    {
        //cout<<"Err:"<<Err<<endl;
        LOG(FORMAT("Err:%d -- %s:%d:%s", Err, __FILE__, __LINE__, __FUNCTION__), LV_ERROR);
        sqlite3_free(Err);
    }
    if(rc != SQLITE_OK)
    {
        return NULL;
    }
    return uData;
}



// 转义特殊字符
QString escapeCsvField(const QString& field) {
    QString escapedField = field;
    if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
        escapedField.replace("\"", "\"\"");
        escapedField = "\"" + escapedField + "\"";
    }
    return escapedField;
}

/*
i=78,columns=0,content=admin
i=78,columns=1,content=admin
i=78,columns=2,content=8
i=78,columns=3,content=admin:set_backup_server
i=78,columns=4,content=47ataCrDIxmSvru0tuYLsR+O8J+2I3bCvWEsWDkxD6M=
i=78,columns=5,content=2024-05-28
i=78,columns=6,content=17:38:16
*/
bool CDataBase::ExportSQL_CSV(string csvName, string strSQL)
{
    if(strSQL.empty() || csvName.empty()) {
        LOG("ExportSQL_CSV: SQL语句或文件名为空", LV_ERROR);
        return false;
    }

    // 获取读锁
    shared_lock<shared_timed_mutex> slk_db(dbSharedMutex);
    
    if (!m_db) {
        LOG("ExportSQL_CSV: 数据库连接无效", LV_ERROR);
        return false;
    }

    // 构建CSV文件路径
    CMyString csvPath;
    csvPath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), 
                   HTTP_FOLDER_ADATA, FOLDER_LOG, csvName.c_str());

    // 打开CSV文件，使用QIODevice::Truncate确保文件被清空
    QFile file(csvPath.Data());
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text | QIODevice::Truncate)) {
        LOG(FORMAT("ExportSQL_CSV: 无法打开文件 %s, 错误: %s", 
                   csvPath.Data(), file.errorString().toStdString().c_str()), LV_ERROR);
        return false;
    }

    char** results = nullptr;
    int rows = 0, columns = 0;
    char* errMsg = nullptr;

    // 执行SQL查询
    int rc = sqlite3_get_table(m_db, strSQL.c_str(), &results, &rows, &columns, &errMsg);
    if (rc != SQLITE_OK) {
        if (errMsg) {
            LOG(FORMAT("ExportSQL_CSV: SQL执行错误: %s, SQL: %s", errMsg, strSQL.c_str()), LV_ERROR);
            sqlite3_free(errMsg);
        }
        file.close();
        return false;
    }

    // 检查查询结果
    if (columns == 0) {
        LOG("ExportSQL_CSV: 查询结果列数为0", LV_ERROR);
        sqlite3_free_table(results);
        file.close();
        //没有数据行,直接返回成功
        return true;
    }

    QTextStream out(&file);
    out.setCodec("UTF-8");  // 确保UTF-8编码

    try {
        // 写入CSV文件头
        QStringList headers;
        for (int i = 0; i < columns; i++) {
            headers << escapeCsvField(QString::fromUtf8(results[i] ? results[i] : ""));
        }
        out << headers.join(",") << "\n";

        // 如果没有数据行，直接返回成功
        if (rows == 0) {
            LOG("ExportSQL_CSV: 查询无数据返回", LV_INFO);
            sqlite3_free_table(results);
            file.close();
            return true;
        }

        // 根据加密配置选择处理方式
#if APP_IS_AISP_TZY_ENCRYPTION
        // 加密版本：使用并行处理HMAC验证
        return processRowsWithEncryption(out, results, rows, columns);
#else
        // 标准版本：直接处理数据行
        return processRowsStandard(out, results, rows, columns);
#endif

    } catch (const std::exception& e) {
        LOG(FORMAT("ExportSQL_CSV: 处理数据时发生异常: %s", e.what()), LV_ERROR);
        sqlite3_free_table(results);
        file.close();
        return false;
    }

    sqlite3_free_table(results);
    file.close();
    
    LOG(FORMAT("ExportSQL_CSV: 成功导出 %d 行数据到文件 %s", rows, csvPath.Data()), LV_INFO);
    return true;
}

#if APP_IS_AISP_TZY_ENCRYPTION
// 处理加密版本的数据行
bool CDataBase::processRowsWithEncryption(QTextStream& out, char** results, int rows, int columns)
{
    // 使用线程池进行并行处理
    const int maxThreads = std::min(QThread::idealThreadCount(), std::max(1, rows / 10));
    QThreadPool::globalInstance()->setMaxThreadCount(maxThreads);
    
    std::vector<QFuture<QStringList>> futures;
    std::mutex resultMutex;
    std::vector<QStringList> resultRows(rows);
    
    // 分批处理数据行以提高效率
    const int batchSize = std::max(1, rows / maxThreads);
    
    for (int startRow = 1; startRow <= rows; startRow += batchSize) {
        int endRow = std::min(startRow + batchSize - 1, rows);
        
        futures.push_back(QtConcurrent::run([=, &resultRows, &resultMutex]() -> QStringList {
            QStringList batchResults;
            
            // 为此批次创建密码模块实例
            CPasswordMod passwordMod_csv;
            if (passwordMod_csv.getAccessToken() != g_Global.m_PasswordMod.getAccessToken()) {
                passwordMod_csv.setAccessToken(g_Global.m_PasswordMod.getAccessToken());
            }
            
            for (int i = startRow; i <= endRow; ++i) {
                QStringList rowData;
                string column_content, column_hmac;
                
                for (int j = 0; j < columns; j++) {
                    QString field = QString::fromUtf8(results[i * columns + j] ? results[i * columns + j] : "");
                    
                    if (j == 3) {  // 内容列
                        column_content = field.toStdString();
                    } else if (j == 4) {  // HMAC列
                        column_hmac = field.toStdString();
                        
                        // 验证HMAC
                        string computed_hmac = passwordMod_csv.sendHmacRequest(column_content);
                        if (!computed_hmac.empty() && 
                            computed_hmac != column_hmac && 
                            column_hmac != CONNECT_CIPHER_ERROR_HMAC) {
                            column_content += " (校验错误：内容被篡改)";
                        }
                        
                        rowData << escapeCsvField(QString::fromStdString(column_content));
                        rowData << escapeCsvField(QString::fromStdString(column_hmac));
                    } else {
                        rowData << escapeCsvField(field);
                    }
                }
                
                // 线程安全地存储结果
                std::lock_guard<std::mutex> lock(resultMutex);
                resultRows[i - 1] = rowData;
            }
            
            return QStringList();  // 返回值不重要，我们使用resultRows
        }));
    }
    
    // 等待所有任务完成
    for (auto& future : futures) {
        future.waitForFinished();
    }
    
    // 按顺序写入结果
    for (const QStringList& rowData : resultRows) {
        if (!rowData.isEmpty()) {
            out << rowData.join(",") << "\n";
        }
    }
    
    return true;
}
#endif

// 处理标准版本的数据行
bool CDataBase::processRowsStandard(QTextStream& out, char** results, int rows, int columns)
{
    for (int i = 1; i <= rows; i++) {
        QStringList rowData;
        
        for (int j = 0; j < columns; j++) {
            QString field = QString::fromUtf8(results[i * columns + j] ? results[i * columns + j] : "");
            rowData << escapeCsvField(field);
        }
        
        out << rowData.join(",") << "\n";
        
        // 每处理1000行刷新一次缓冲区，避免内存过度使用
        if (i % 1000 == 0) {
            out.flush();
        }
    }
    
    return true;
}





