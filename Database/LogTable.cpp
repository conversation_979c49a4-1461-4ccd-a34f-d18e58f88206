#include "stdafx.h"
#include "LogTable.h"
#include "TinyXml/tinyxml.h"
#include <algorithm>

#define LOG_TABLE_NAME		("DeviceLog")			// 表名称
#define	LOG_MAX_SAVE_DAYS	90						// 最多保存的天数


CLogTable::CLogTable()
{
    m_DataBase = NULL;
}

CLogTable::~CLogTable()
{

}

void CLogTable::SetDbManager(CDataBase *database)
{
    m_DataBase = database;
}


// 创建表
bool CLogTable::Create(void)
{
    CMyString strSQL;
    #if APP_IS_AISP_TZY_ENCRYPTION
    strSQL.Format((char*)("create table if not exists %s(Mac TEXT,Name TEXT,LogType INT,LogContents TEXT,Hmac TEXT,Date TEXT,Time TEXT)"), LOG_TABLE_NAME);
    #else
    strSQL.Format((char*)("create table if not exists %s(Mac TEXT,Name TEXT,LogType INT,LogContents TEXT,Date TEXT,Time TEXT)"), LOG_TABLE_NAME);
    #endif
    return (m_DataBase != NULL && m_DataBase->DirectExecuteSQL(strSQL.C_Str()));
}


CMyString sqliteEscape(CMyString& strWord)
{
    //strWord.Replace("/", "//");
    strWord.Replace("'", "''");
    //strWord.Replace("[", "/[");
    //strWord.Replace("]", "/]");
    //strWord.Replace("%", "/%");
    //strWord.Replace("&","/&");
    //strWord.Replace("_", "/_");
    //strWord.Replace("(", "/(");
    //strWord.Replace(")", "/)");
    return strWord;
}

// 向表中插入记录
bool CLogTable::InsertLog(CMyString strMac, CMyString strName, LogType logType, CMyString strLogContents)
{
    CTime	ct = CTime::GetCurrentTimeT();
    CMyString	strDate, strTime;
    strDate.Format((char*)("%04d-%02d-%02d"), ct.GetYear(), ct.GetMonth(), ct.GetDay());
    strTime.Format((char*)("%02d:%02d:%02d"), ct.GetHour(), ct.GetMinute(), ct.GetSecond());

    // 转码
    string utf8_Name = StringToUTF8(strName.C_Str());
    CMyString strConvertContents = sqliteEscape(strLogContents);

    CMyString strSQL;
    #if APP_IS_AISP_TZY_ENCRYPTION

    string strHmac;
    //string strHmac = g_Global.m_PasswordMod.sendHmacRequest(strConvertContents.Data());
    strSQL.Format((char*)("insert into %s values('%s','%s',%d,'%s','%s','%s','%s')"),
                LOG_TABLE_NAME, strMac.C_Str(), utf8_Name.data(),
                logType, strConvertContents.C_Str(), strHmac.data(), strDate.C_Str(), strTime.C_Str());
    #else
    strSQL.Format((char*)("insert into %s values('%s','%s',%d,'%s','%s','%s')"),
                  LOG_TABLE_NAME, strMac.C_Str(), utf8_Name.data(),
                  logType, strConvertContents.C_Str(), strDate.C_Str(), strTime.C_Str());
    #endif

    //return (m_DataBase != NULL && m_DataBase->DirectExecuteSQL(strSQL.C_Str()));
    return (m_DataBase != NULL && m_DataBase->AddSQLCommand(strSQL.C_Str()));
}


bool CLogTable::GetLogData(vector<LogTableData> &vecLogData,
                           CMyString strSQL)
{
    if(m_DataBase == NULL)
        return false;

    vecLogData.clear();
    BaseData *uData = NULL;
    uData = m_DataBase->ExecuteSQL(strSQL.C_Str());
    if(uData != NULL)
    {
        for(int i=uData->nColumn;i<(uData->nRow+1)*uData->nColumn;
            i+=uData->nColumn)
        {
            LogTableData tableData;
            tableData.strMac           = CMyString(uData->szResult[i]);
            tableData.strName          = CMyString(uData->szResult[i+1]);
            tableData.strLogType       = CMyString(uData->szResult[i+2]);
            tableData.strLogContents   = CMyString(uData->szResult[i+3]);
            tableData.strDate          = CMyString(uData->szResult[i+4]);
            tableData.strTime          = CMyString(uData->szResult[i+5]);

            vecLogData.push_back(tableData);
        }
        sqlite3_free_table(uData->szResult);
        free(uData);
        return true;
    }

    return false;
}

// 查询表中的数据（根据日志类型、起始日期、结束日期、设备名称）
bool CLogTable::GetLogData(vector<LogTableData>& vecLogData,
                           LogType logType,
                           CTime&  tDateStart,
                           CTime&  tDateEnd,
                           CMyString strName,
                           CMyString strMac)
{
    CTimeSpan span(1, 0, 0, 0);

    vecLogData.clear();

    // 设备名称
    CMyString strNameSQL = ("");
    if (strMac != (""))
    {
        strNameSQL.Format((char*)(" and Mac like '%%%s%%'"), strMac.C_Str());
    }
    else if (strName != (""))
    {
        strNameSQL.Format((char*)(" and Name like '%%%s%%'"), strName.C_Str());
    }

    // 开始日期到结束日期
    for (CTime ct = tDateStart; ct<=tDateEnd;)
    {
        CMyString	strDate, strSQL;
        strDate.Format((char*)("%04d-%02d-%02d"), ct.GetYear(), ct.GetMonth(), ct.GetDay());

        if (logType == LT_ALL_LOG)
        {
            strSQL.Format((char*)("%s where Date='%s'%s"), GetLogTypeSQL(logType).C_Str(), strDate.C_Str(), strNameSQL.C_Str());
        }
        else
        {
            strSQL.Format((char*)("%s and Date='%s'%s"), GetLogTypeSQL(logType).C_Str(), strDate.C_Str(), strNameSQL.C_Str());
        }

        vector<LogTableData> vecData;
        vecData.clear();
        if (GetLogData(vecData, strSQL))
        {
            // 把新查询到的记录添加到后面
            vecLogData.insert(vecLogData.end(), vecData.begin(), vecData.end());
        }

        ct += span;
    }

    return true;
}

bool CLogTable::ExportLogData(CMyString strSQL)
{
    if(m_DataBase == NULL)
        return false;
    return true;
}


bool CLogTable::WriteXmlFile(vector<LogTableData> &vecLogData, CMyString strFileName)
{
    TiXmlDocument xmlLog;
    int nLogCount = vecLogData.size();

    char szPathName[STR_MAX_PATH] = {0};
    // HTTP路径 保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML_OTHER, strFileName.C_Str()); //组成可远端下载的URL

    // 头字段
    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0", "utf-8", "no");
    xmlLog.LinkEndChild(dec);

    // 描述
    TiXmlComment* com = new TiXmlComment("save for log info");
    xmlLog.LinkEndChild(com);

    TiXmlElement* Logs = new TiXmlElement("Logs");
    Logs->SetAttribute("LogCount", nLogCount);
    Logs->SetAttribute("DateTime"    , GetCurrentDateTime().C_Str());
    xmlLog.LinkEndChild(Logs);

    for(int i=0; i<nLogCount; ++i)
    {
        LogTableData log = vecLogData.at(i);

        TiXmlElement* Log = new TiXmlElement("Log");

        Log->SetAttribute("ID", i);
        Log->SetAttribute("Name", log.strName.C_Str());
        Log->SetAttribute("LogType", log.strLogType.C_Str());
        Log->SetAttribute("Contents", log.strLogContents.C_Str());
        Log->SetAttribute("Date", log.strDate.C_Str());
        Log->SetAttribute("Time", log.strTime.C_Str());
        Logs->LinkEndChild(Log);
    }

    if(xmlLog.SaveFile(szPathName))
    {
        xmlLog.Clear();
        return TRUE;
    }
    else
    {
        xmlLog.Clear();
        return FALSE;
    }

}


CMyString CLogTable::GetLogTypeSQL(LogType logType)
{
    CMyString strSQL = ("");

    if (logType == LT_ALL_LOG)
    {
        strSQL.Format((char*)("select * from %s"), LOG_TABLE_NAME);
    }
    else
    {
        strSQL.Format((char*)("select * from %s where LogType=%d"), LOG_TABLE_NAME, logType);
    }

    return strSQL;
}










