#ifndef DATABASE_H
#define DATABASE_H

#include <iostream>
#include "Global/CType.h"
#include <sqlite3.h>
#include <deque>
#include <shared_mutex>

// 前向声明
class QTextStream;

using namespace std;


/*************************************************/
/*****        数据库操作，执行SQL语句          ******/
/*************************************************/


class CDataBase
{
public:
    CDataBase();
    ~CDataBase();
    void CloseData();

    // 打开数据库
    bool OpenData(string DataName,bool set_busy_handler=TRUE);

    static int BusyHandler(void* ptr, int retry_times);

    // 直接执行SQL，不返回记录，用于创建表与查询数据
    bool DirectExecuteSQL(string strSQL);

    // 执行SQL，返回数据
    BaseData* ExecuteSQL(string strSQL);

    bool ExportSQL_CSV(string csvName,string strSQL);

#if APP_IS_AISP_TZY_ENCRYPTION
    bool processRowsWithEncryption(QTextStream& out, char** results, int rows, int columns);
#endif
    bool processRowsStandard(QTextStream& out, char** results, int rows, int columns);

    bool AddSQLCommand(string strSQL);

    static void* HandleSqlCommandThread(void *lpParam);

    bool StartWorking();

    pthread_mutex_t  sqlCmdMutex;
private:
    sqlite3 *m_db;
    
    deque<string> sqlCmdDqueue;

    shared_timed_mutex dbSharedMutex;
};

#endif // DATABASE_H
