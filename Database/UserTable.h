#ifndef USERTABLE_H
#define USERTABLE_H

#include "DataBase.h"
#include <vector>
#include "Tools/tools.h"
#include "Global/Const.h"


#define USER_TABLE_NAME		("UserInfo")			// 表名称
#define TAB_SECTION         ("_Sec")
#define TAB_GROUP           ("_Group")

#define USER_TABLE_COLUMN_USERACCOUNT           ("UserAccount")
#define USER_TABLE_COLUMN_PARUSERACCOUNT        ("ParUserAccount")
#define USER_TABLE_COLUMN_USERPASSWORD          ("UserPassword")
#define USER_TABLE_COLUMN_LIMITCODE             ("LimitCode")
#if APP_IS_AISP_TZY_ENCRYPTION
#define USER_TABLE_COLUMN_CERTIFICATE           ("Certificate")
#endif
#define USER_TABLE_COLUMN_USERID                ("UserId")   
#define USER_TABLE_COLUMN_PLAYMODE              ("PlayMode")
#define USER_TABLE_COLUMN_LISTENDEVICE          ("ListenDevice")
#define USER_TABLE_COLUMN_USERNAME              ("UserName")
#define USER_TABLE_COLUMN_USERTYPE              ("UserType")
#define USER_TABLE_COLUMN_STORAGECAPACITY       ("StorageCapacity")

// 用户数据类型
typedef struct
{
    string strParAccount;         // 父用户名称
    string strAccount;            // 用户账户
    string strPassword;          // 用户密码
    string strUserName;          // 用户名称
    string strUserId;            //用户uuid
    int    nPlayMode;            // 用户播放模式
    string strListenDevice;      //监听设备
    int      nLimitCode;           // 权限码
    int      nZoneCount;          // 分区数量
    int      nUserType;           // 用户类型
    int      nStorageCapacity;    // 存储容量
    #if APP_IS_AISP_TZY_ENCRYPTION    
    string  strCertificate;       // 用户证书
    #endif
    //int      nGroupCount;        // 分组数量
    //vector<string> vecSection;     // 分区容器
    //vector<string> vecGroup;       // 分组容器

}UserData_t;

typedef enum
{
    SQL_USER = 0,       // 用户数据
    SQL_SEC,                // 分区数据
    SQL_GROUP           // 分组数据
}SQLType;


class CUserTable
{
public:
        CUserTable();
        ~CUserTable();

        void        SetDbManager(CDataBase* database);

        // 创建表
        bool        Create(void);

        // 添加用户
        bool        InsertUser(UserData_t& userData);
        #if 0
        bool        InsertUser(string strAccount, string strParAccount, string strPassword, int nLimitCode,string strUserId,int nPlayMode,
                                string strListenDevice,string strUserName,int nUserType,int nStorageCapacity);
        #endif
        // 根据用户账户删除用户
        bool        RemoveUser(string  strAccount);

        // 查询用户数据
        bool        GetUserData(vector<UserData_t>& vecUserData, string strSQL);

        // 更新数据，修改父用户
        bool        UpdateUserParAccount(string strAccount, string strParAccount);
        // 更新数据 修改密码
        bool        UpdateUserPassword(string  strAccount, string  strPassword);
        // 更新数据 修改用户名称
        bool        UpdateUserName(string  strAccount, string  strUserName);
        // 更新数据 修改权限码
        bool        UpdateUserAuthority(string strAccount, int nAuthority);


        #if APP_IS_AISP_TZY_ENCRYPTION
        bool        UpdateUserCertificate(string strAccount, string strCertificate);
        #endif

        // 更新数据 用户uuid
        bool        UpdateUserId(string  strAccount, string  strId);

        // 更新数据 播放模式
        bool        UpdateUserPlayMode(string strAccount, int nplayMode);

        // 更新数据 监听设备MAC
        bool        UpdateUserListenDevice(string strAccount, string nListenDevice);

        // 更新数据 用户类型
        bool        UpdateUserType(string strAccount, int nUserType);

        // 更新数据 子账户存储容量
        bool        UpdateUserStorageCapacity(string strAccount, int nStorageCapactiy);

        // 创建分区分组文件
        bool        CreatUserDataTable(string strAccount);
        // 删除分区分组数据库文件
        bool        RemoveUserTable(string strAccount);

        // 清除分区分组数据库数据
        bool        ClearSecData(string strAccount);
        bool        ClearGroupData(string strAccount);

        // 设置用户分区分组数据
        bool        SetUserSecData(string strAccount, vector<string>& vecUserSec);
        bool        SetUserGroupData(string strAccount, vector<string>& vecUserGroup);

        // 查询用户分区分组数据
        bool        GetUserSecData(string strAccount, vector<string>& vecUserSec);
        bool        GetUserGroupData(string strAccount, vector<string>& vecUserGroup);

        // 用户 是否存在
        bool    IsExistUser(string strAccount);
        // 根据获取数据类型组合成SQL语句
        static  string  GetSQLString(string strAccount, SQLType sqlType);

private:
        bool    CreateSecTable(string strAccount);
        //bool    CreateGroupTable(string strAccount);
        bool    RemoveTable(string strTableName);
        bool    ClearTableData(string strTableName);

private:
        CDataBase *m_DataBase;
};

#endif // USERTABLE_H
