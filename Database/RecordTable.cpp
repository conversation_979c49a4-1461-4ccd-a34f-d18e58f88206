#include "stdafx.h"
#include "RecordTable.h"
#include "Global/GlobalMethod.h"
#include "Tools/CTime.h"
#include <QTextStream>
#include "Global/GlobalData.h"


CRecordTable::CRecordTable(void)
{
    m_DataBase = NULL;
}

CRecordTable::~CRecordTable(void)
{

}

void CRecordTable::SetDbManager(CDataBase* database)
{
    m_DataBase = database;
}

bool CRecordTable::Create(void)
{
    if (m_DataBase == NULL)
    {
        return false;
    }

    CMyString strSQL;
    strSQL.Format("CREATE TABLE IF NOT EXISTS %s ("
                  "record_id INTEGER PRIMARY KEY AUTOINCREMENT,"
                  "call_id VARCHAR(64) NOT NULL UNIQUE,"
                  "caller_mac VARCHAR(18) NOT NULL,"
                  "caller_name VARCHAR(64) NOT NULL,"
                  "callee_list TEXT NOT NULL,"
                  "record_type INTEGER NOT NULL,"
                  "record_status INTEGER NOT NULL,"
                  "start_time VARCHAR(20) NOT NULL,"
                  "end_time VARCHAR(20),"
                  "duration INTEGER DEFAULT 0,"
                  "file_path VARCHAR(256),"
                  "file_size INTEGER DEFAULT 0,"
                  "audio_format INTEGER NOT NULL,"
                  "sample_rate INTEGER NOT NULL,"
                  "channels INTEGER NOT NULL,"
                  "bit_rate INTEGER NOT NULL,"
                  "create_date VARCHAR(12) NOT NULL,"
                  "create_time VARCHAR(10) NOT NULL,"
                  "remark VARCHAR(256)"
                  ");", RECORD_TABLE_NAME);

    if (!m_DataBase->DirectExecuteSQL(strSQL.C_Str()))
    {
        return false;
    }

    // 创建索引以提高查询性能
    strSQL.Format("CREATE INDEX IF NOT EXISTS idx_call_id ON %s (call_id);", RECORD_TABLE_NAME);
    m_DataBase->DirectExecuteSQL(strSQL.C_Str());

    strSQL.Format("CREATE INDEX IF NOT EXISTS idx_caller_mac ON %s (caller_mac);", RECORD_TABLE_NAME);
    m_DataBase->DirectExecuteSQL(strSQL.C_Str());

    strSQL.Format("CREATE INDEX IF NOT EXISTS idx_record_type ON %s (record_type);", RECORD_TABLE_NAME);
    m_DataBase->DirectExecuteSQL(strSQL.C_Str());

    strSQL.Format("CREATE INDEX IF NOT EXISTS idx_create_date ON %s (create_date);", RECORD_TABLE_NAME);
    m_DataBase->DirectExecuteSQL(strSQL.C_Str());

    return true;
}

bool CRecordTable::StartRecord(const CMyString& strCallId, const CMyString& strCallerMac,
                               const CMyString& strCallerName, const CMyString& strCalleeList,
                               RecordType recordType,
                               AudioFormat audioFormat, int nSampleRate, int nChannels, int nBitRate)
{
    if (m_DataBase == NULL)
    {
        return false;
    }

    CTime currentTime = CTime::GetCurrentTimeT();

    CMyString strSQL;
    strSQL.Format("INSERT INTO %s (call_id, caller_mac, caller_name, callee_list, "
                  "record_type, record_status, start_time, audio_format, sample_rate, channels, "
                  "bit_rate, create_date, create_time) VALUES "
                  "('%s', '%s', '%s', '%s', %d, %d, '%s', %d, %d, %d, %d, '%s', '%s');",
                  RECORD_TABLE_NAME,
                  strCallId.C_Str(),
                  strCallerMac.C_Str(),
                  strCallerName.C_Str(),
                  strCalleeList.C_Str(),
                  (int)recordType,
                  (int)RS_RECORDING,
                  currentTime.Format("%Y-%m-%d %H:%M:%S").C_Str(),
                  (int)audioFormat,
                  nSampleRate,
                  nChannels,
                  nBitRate,
                  currentTime.Format("%Y-%m-%d").C_Str(),
                  currentTime.Format("%H:%M:%S").C_Str());

    return m_DataBase->DirectExecuteSQL(strSQL.C_Str());
}

bool CRecordTable::EndRecord(const CMyString& strCallId, const CMyString& strFilePath,
                             int nFileSize, int nDuration, RecordStatus recordStatus)
{
    if (m_DataBase == NULL)
    {
        return false;
    }

    CTime currentTime = CTime::GetCurrentTimeT();

    CMyString strSQL;
    strSQL.Format("UPDATE %s SET record_status = %d, end_time = '%s', duration = %d, "
                  "file_path = '%s', file_size = %d WHERE call_id = '%s';",
                  RECORD_TABLE_NAME,
                  (int)recordStatus,
                  currentTime.Format("%Y-%m-%d %H:%M:%S").C_Str(),
                  nDuration,
                  strFilePath.C_Str(),
                  nFileSize,
                  strCallId.C_Str());

    return m_DataBase->DirectExecuteSQL(strSQL.C_Str());
}

bool CRecordTable::UpdateRecordStatus(const CMyString& strCallId, RecordStatus recordStatus)
{
    if (m_DataBase == NULL)
    {
        return false;
    }

    CMyString strSQL;
    strSQL.Format("UPDATE %s SET record_status = %d WHERE call_id = '%s';",
                  RECORD_TABLE_NAME, (int)recordStatus, strCallId.C_Str());

    return m_DataBase->DirectExecuteSQL(strSQL.C_Str());
}

bool CRecordTable::GetRecordData(vector<RecordTableData>& vecRecordData, const CMyString& strSQL)
{
    if (m_DataBase == NULL)
    {
        return false;
    }

    vecRecordData.clear();

    BaseData* pData = m_DataBase->ExecuteSQL(strSQL.C_Str());
    if (pData == NULL)
    {
        return false;
    }

    for (int i = 0; i < pData->nRow; i++)
    {
        RecordTableData recordData;
        
        recordData.nRecordId = atoi(pData->szResult[(i + 1) * pData->nColumn + 0]);
        recordData.strCallId = pData->szResult[(i + 1) * pData->nColumn + 1];
        recordData.strCallerMac = pData->szResult[(i + 1) * pData->nColumn + 2];
        recordData.strCallerName = pData->szResult[(i + 1) * pData->nColumn + 3];
        recordData.strCalleeList = pData->szResult[(i + 1) * pData->nColumn + 4];
        recordData.recordType = (RecordType)atoi(pData->szResult[(i + 1) * pData->nColumn + 5]);
        recordData.recordStatus = (RecordStatus)atoi(pData->szResult[(i + 1) * pData->nColumn + 6]);
        recordData.strStartTime = pData->szResult[(i + 1) * pData->nColumn + 7];
        
        // 检查end_time是否为NULL，如果为NULL则设置为空字符串
        const char* endTimePtr = pData->szResult[(i + 1) * pData->nColumn + 8];
        recordData.strEndTime = (endTimePtr != NULL) ? endTimePtr : "";
        
        // 安全转换duration字段，如果为NULL则设置为0
        const char* durationPtr = pData->szResult[(i + 1) * pData->nColumn + 9];
        recordData.nDuration = (durationPtr != NULL) ? atoi(durationPtr) : 0;
        
        // 检查file_path是否为NULL，如果为NULL则设置为空字符串
        const char* filePathPtr = pData->szResult[(i + 1) * pData->nColumn + 10];
        recordData.strFilePath = (filePathPtr != NULL) ? filePathPtr : "";
        
        // 安全转换file_size字段，如果为NULL则设置为0
        const char* fileSizePtr = pData->szResult[(i + 1) * pData->nColumn + 11];
        recordData.nFileSize = (fileSizePtr != NULL) ? atoi(fileSizePtr) : 0;
        
        recordData.audioFormat = (AudioFormat)atoi(pData->szResult[(i + 1) * pData->nColumn + 12]);
        recordData.nSampleRate = atoi(pData->szResult[(i + 1) * pData->nColumn + 13]);
        recordData.nChannels = atoi(pData->szResult[(i + 1) * pData->nColumn + 14]);
        recordData.nBitRate = atoi(pData->szResult[(i + 1) * pData->nColumn + 15]);
        recordData.strCreateDate = pData->szResult[(i + 1) * pData->nColumn + 16];
        recordData.strCreateTime = pData->szResult[(i + 1) * pData->nColumn + 17];
        
        // 检查remark是否为NULL，如果为NULL则设置为空字符串
        const char* remarkPtr = pData->szResult[(i + 1) * pData->nColumn + 18];
        recordData.strRemark = (remarkPtr != NULL) ? remarkPtr : "";

        vecRecordData.push_back(recordData);
    }

    delete pData;
    return true;
}

bool CRecordTable::GetRecordData(vector<RecordTableData>& vecRecordData, RecordType recordType,
                                 const CTime& tDateStart, const CTime& tDateEnd,
                                 const CMyString& strCallerMac, const CMyString& strCallerName)
{
    if (m_DataBase == NULL)
    {
        return false;
    }

    CMyString strSQL;
    strSQL.Format("SELECT * FROM %s WHERE create_date >= '%s' AND create_date <= '%s'",
                  RECORD_TABLE_NAME,
                  tDateStart.Format("%Y-%m-%d").C_Str(),
                  tDateEnd.Format("%Y-%m-%d").C_Str());

    if (recordType != RT_TYPE_ALL)
    {
        CMyString strTypeSQL = GetRecordTypeSQL(recordType);
        strSQL += (CMyString)" AND " + strTypeSQL;
    }

    if (!strCallerMac.IsEmpty())
    {
        CMyString strTemp;
        strTemp.Format(" AND caller_mac = '%s'", strCallerMac.C_Str());
        strSQL += strTemp;
    }

    if (!strCallerName.IsEmpty())
    {
        CMyString strTemp;
        strTemp.Format(" AND caller_name LIKE '%%%s%%'", strCallerName.C_Str());
        strSQL += strTemp;
    }

    strSQL += " ORDER BY start_time DESC";

    return GetRecordData(vecRecordData, strSQL);
}

bool CRecordTable::GetRecordByCallId(RecordTableData& recordData, const CMyString& strCallId)
{
    if (m_DataBase == NULL)
    {
        return false;
    }

    CMyString strSQL;
    strSQL.Format("SELECT * FROM %s WHERE call_id = '%s';", RECORD_TABLE_NAME, strCallId.C_Str());

    vector<RecordTableData> vecRecordData;
    if (GetRecordData(vecRecordData, strSQL) && !vecRecordData.empty())
    {
        recordData = vecRecordData[0];
        return true;
    }

    return false;
}

bool CRecordTable::DeleteRecord(const CMyString& strCallId)
{
    if (m_DataBase == NULL)
    {
        return false;
    }

    // 首先获取录音记录信息，以便删除文件
    RecordTableData recordData;
    if (GetRecordByCallId(recordData, strCallId))
    {
        // 删除录音文件
        if (!recordData.strFilePath.IsEmpty())
        {
            // 构建完整的文件路径
            CMyString strFullPath = g_Global.m_strFolderPath + recordData.strFilePath;
            if (IsExistFile(strFullPath.Data()))
            {
                if (remove(strFullPath.Data()) == 0)
                {
                    LOG(FORMAT("Deleted record file: %s", strFullPath.Data()), LV_INFO);
                }
                else
                {
                    LOG(FORMAT("Failed to delete record file: %s", strFullPath.Data()), LV_WARNING);
                }
            }
        }
    }

    // 删除数据库记录
    CMyString strSQL;
    strSQL.Format("DELETE FROM %s WHERE call_id = '%s';", RECORD_TABLE_NAME, strCallId.C_Str());

    return m_DataBase->DirectExecuteSQL(strSQL.C_Str());
}

void CRecordTable::ClearExpiredData(void)
{
    if (m_DataBase == NULL)
    {
        return;
    }

    CTime currentTime = CTime::GetCurrentTimeT();
    CTimeSpan expiredSpan(RECORD_MAX_KEEP_DAYS, 0, 0, 0);
    CTime expiredTime = currentTime - expiredSpan;

    // 首先查询过期的录音记录，获取文件路径
    vector<RecordTableData> expiredRecords;
    CMyString strQuerySQL;
    strQuerySQL.Format("SELECT * FROM %s WHERE create_date < '%s';",
                      RECORD_TABLE_NAME, expiredTime.Format("%Y-%m-%d").C_Str());
    
    if (GetRecordData(expiredRecords, strQuerySQL))
    {
        // 删除过期的录音文件
        for (const auto& record : expiredRecords)
        {
            if (!record.strFilePath.IsEmpty())
            {
                // 构建完整的文件路径
                CMyString strFullPath = g_Global.m_strFolderPath + record.strFilePath;
                if (IsExistFile(strFullPath.Data()))
                {
                    if (remove(strFullPath.Data()) == 0)
                    {
                        LOG(FORMAT("Deleted expired record file: %s", strFullPath.Data()), LV_INFO);
                    }
                    else
                    {
                        LOG(FORMAT("Failed to delete expired record file: %s", strFullPath.Data()), LV_WARNING);
                    }
                }
            }
        }
    }

    // 删除过期的数据库记录
    CMyString strDeleteSQL;
    strDeleteSQL.Format("DELETE FROM %s WHERE create_date < '%s';",
                       RECORD_TABLE_NAME, expiredTime.Format("%Y-%m-%d").C_Str());

    if (m_DataBase->DirectExecuteSQL(strDeleteSQL.C_Str()))
    {
        LOG(FORMAT("Cleared expired record data before %s", expiredTime.Format("%Y-%m-%d").Data()), LV_INFO);
    }
}

bool CRecordTable::ExportRecordData(const CMyString& strSQL)
{
    if (m_DataBase == NULL)
    {
        return false;
    }

    CMyString strFileName;
    strFileName.Format("record_export_%s.csv", GetCurrentDateTime().C_Str());

    return m_DataBase->ExportSQL_CSV(strFileName.C_Str(), strSQL.C_Str());
}

CMyString CRecordTable::GetRecordTypeSQL(RecordType recordType)
{
    CMyString strSQL;
    if (recordType >= 0)
    {
        strSQL.Format("record_type = %d", (int)recordType);
    }
    else
    {
        strSQL = "1=1"; // 全部类型
    }
    return strSQL;
}

CMyString CRecordTable::GetRecordTypeDescription(RecordType recordType)
{
    switch (recordType)
    {
    case RT_BROADCAST_PAGING:
        return "单向广播寻呼";
    case RT_INTERCOM_CALL:
        return "双向对讲通话";
    default:
        return "未知类型";
    }
}

CMyString CRecordTable::GetRecordStatusDescription(RecordStatus recordStatus)
{
    switch (recordStatus)
    {
    case RS_RECORDING:
        return "录音中";
    case RS_COMPLETED:
        return "录音完成";
    case RS_ERROR:
        return "录音错误";
    case RS_INTERRUPTED:
        return "录音中断";
    default:
        return "未知状态";
    }
}

CMyString CRecordTable::GetAudioFormatDescription(AudioFormat audioFormat)
{
    switch (audioFormat)
    {
    case AF_PCM:
        return "PCM";
    case AF_G722:
        return "G.722";
    case AF_MP3:
        return "MP3";
    default:
        return "未知格式";
    }
}

CMyString CRecordTable::GetCurrentDateTime()
{
    CTime currentTime = CTime::GetCurrentTimeT();
    return currentTime.Format("%Y%m%d_%H%M%S");
}