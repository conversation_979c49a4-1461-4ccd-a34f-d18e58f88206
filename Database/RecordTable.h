#ifndef RECORDTABLE_H
#define RECORDTABLE_H

#include "DataBase.h"
#include <vector>
#include "Tools/tools.h"

#define RECORD_MAX_KEEP_DAYS        30          // 录音文件保留天数

#define RECORD_TABLE_NAME                   ("RecordInfo")

// 表字段定义
#define RECORD_TABLE_COLUMN_ID              ("RecordId")
#define RECORD_TABLE_COLUMN_CALL_ID         ("CallId")
#define RECORD_TABLE_COLUMN_CALLER_MAC      ("CallerMac")
#define RECORD_TABLE_COLUMN_CALLER_NAME     ("CallerName")
#define RECORD_TABLE_COLUMN_CALLEE_MACS     ("CalleeMacs")
#define RECORD_TABLE_COLUMN_CALLEE_NAMES    ("CalleeNames")
#define RECORD_TABLE_COLUMN_RECORD_TYPE     ("RecordType")
#define RECORD_TABLE_COLUMN_RECORD_STATUS   ("RecordStatus")
#define RECORD_TABLE_COLUMN_AUDIO_CODEC     ("AudioCodec")
#define RECORD_TABLE_COLUMN_SAMPLE_RATE     ("SampleRate")
#define RECORD_TABLE_COLUMN_BIT_DEPTH       ("BitDepth")
#define RECORD_TABLE_COLUMN_CHANNELS        ("Channels")
#define RECORD_TABLE_COLUMN_START_TIME      ("StartTime")
#define RECORD_TABLE_COLUMN_END_TIME        ("EndTime")
#define RECORD_TABLE_COLUMN_DURATION        ("Duration")
#define RECORD_TABLE_COLUMN_FILE_PATH       ("FilePath")
#define RECORD_TABLE_COLUMN_FILE_SIZE       ("FileSize")
#define RECORD_TABLE_COLUMN_FILE_MD5        ("FileMd5")
#define RECORD_TABLE_COLUMN_CREATE_DATE     ("CreateDate")
#define RECORD_TABLE_COLUMN_CREATE_TIME     ("CreateTime")

// 录音类型
typedef enum
{
    RT_TYPE_ALL = -1,            // 全部类型
    RT_BROADCAST_PAGING = 0,    // 单向广播寻呼
    RT_INTERCOM_CALL = 1,       // 双向对讲通话
} RecordType;

// 录音状态
typedef enum
{
    RS_RECORDING = 0,           // 录音中
    RS_COMPLETED,               // 录音完成
    RS_ERROR,                   // 录音错误
    RS_INTERRUPTED              // 录音中断
} RecordStatus;

// 音频编码格式
typedef enum
{
    AF_PCM = 0,                 // PCM格式
    AF_G722,                    // G.722格式
    AF_MP3                      // MP3格式
} AudioFormat;

// 录音数据结构
typedef struct
{
    int         nRecordId;          // 录音ID
    CMyString   strCallId;          // 通话ID（唯一标识）
    CMyString   strCallerMac;       // 主叫方MAC地址
    CMyString   strCallerName;      // 主叫方名称
    CMyString   strCalleeList;      // 被叫方列表（多个MAC用逗号分隔）
    RecordType  recordType;         // 录音类型
    RecordStatus recordStatus;      // 录音状态
    CMyString   strStartTime;       // 开始时间
    CMyString   strEndTime;         // 结束时间
    int         nDuration;          // 通话时长（秒）
    CMyString   strFilePath;        // 录音文件路径
    int         nFileSize;          // 文件大小（字节）
    AudioFormat audioFormat;        // 音频格式
    int         nSampleRate;        // 采样率
    int         nChannels;          // 声道数
    int         nBitRate;           // 比特率
    CMyString   strCreateDate;      // 创建日期
    CMyString   strCreateTime;      // 创建时间
    CMyString   strRemark;          // 备注信息
} RecordTableData;

class CRecordTable
{
public:
    CRecordTable(void);
    ~CRecordTable(void);

public:
    void SetDbManager(CDataBase* database);

    // 创建表
    bool Create(void);

    // 开始录音记录
    bool StartRecord(const CMyString& strCallId, const CMyString& strCallerMac, 
                     const CMyString& strCallerName, const CMyString& strCalleeList,
                     RecordType recordType,
                     AudioFormat audioFormat, int nSampleRate, int nChannels, int nBitRate);

    // 结束录音记录
    bool EndRecord(const CMyString& strCallId, const CMyString& strFilePath, 
                   int nFileSize, int nDuration, RecordStatus recordStatus = RS_COMPLETED);
    
    // 更新录音状态
    bool UpdateRecordStatus(const CMyString& strCallId, RecordStatus recordStatus);

    // 查询录音数据
    bool GetRecordData(vector<RecordTableData>& vecRecordData, const CMyString& strSQL);

    // 根据条件查询录音数据
    bool GetRecordData(vector<RecordTableData>& vecRecordData, RecordType recordType,
                       const CTime& tDateStart, const CTime& tDateEnd,
                       const CMyString& strCallerMac = "", const CMyString& strCallerName = "");

    // 根据通话ID查询录音数据
    bool GetRecordByCallId(RecordTableData& recordData, const CMyString& strCallId);

    // 删除录音记录
    bool DeleteRecord(const CMyString& strCallId);

    // 导出录音数据到CSV文件
    bool ExportRecordData(const CMyString& strSQL);

    // 清除过期数据（超过最大保存天数的数据）
    void ClearExpiredData(void);

    // 获取录音类型描述
    static CMyString GetRecordTypeDescription(RecordType recordType);

    // 获取录音状态描述
    static CMyString GetRecordStatusDescription(RecordStatus recordStatus);

    // 获取音频格式描述
    static CMyString GetAudioFormatDescription(AudioFormat audioFormat);

    // 获取当前时间（格式为：YYYYMMDD_HHMMSS）
    static CMyString GetCurrentDateTime();

private:
    CMyString GetRecordTypeSQL(RecordType recordType);

private:
    CDataBase *m_DataBase;
};

#endif // RECORDTABLE_H 