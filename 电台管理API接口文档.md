# 电台管理 WebSocket API 接口文档

## 概述

本文档描述了电台信息管理系统的 WebSocket API 接口，用于管理电台数据和分组信息。系统支持预置电台数据和用户自定义电台数据的管理。

## API 接口列表

### 1. 添加电台信息

**命令**: `add_radio_info`

**请求参数**:
```json
{
    "command": "add_radio_info",
    "uuid": "用户UUID",
    "name": "电台名称",
    "url": "电台地址"
}
```

**响应参数**:
```json
{
    "command": "add_radio_info",
    "result": 0,
    "radio_id": 10001,
    "message": "Radio added successfully",
    "uuid": "用户UUID"
}
```

**说明**:
- 仅支持添加到自定义分组（groupId=33）
- 系统会自动分配电台ID（从10000开始）

### 2. 编辑电台信息

**命令**: `edit_radio_info`

**请求参数**:
```json
{
    "command": "edit_radio_info",
    "uuid": "用户UUID",
    "id": 10001,
    "name": "新电台名称",
    "url": "新电台播放地址"
}
```

**响应参数**:
```json
{
    "command": "edit_radio_info",
    "result": 0,
    "message": "Radio updated successfully",
    "uuid": "用户UUID"
}
```

**说明**:
- 仅支持编辑自定义分组中的电台
- 电台ID、名称和URL为必填参数

### 3. 删除电台信息

**命令**: `delete_radio_info`

**请求参数**:
```json
{
    "command": "delete_radio_info",
    "uuid": "用户UUID",
    "id": 10001
}
```

**响应参数**:
```json
{
    "command": "delete_radio_info",
    "result": 0,
    "message": "Radio deleted successfully",
    "uuid": "用户UUID"
}
```

**说明**:
- 仅支持删除自定义分组中的电台
- 电台ID为必填参数

### 4. 获取电台分组列表

**命令**: `get_radio_group_list`

**请求参数**:
```json
{
    "command": "get_radio_group_list",
    "uuid": "用户UUID"
}
```

**响应参数**:
```json
{
    "command": "get_radio_group_list",
    "result": 0,
    "groups": [
        {
            "key": "rg_1",
            "groupId": 1,
            "pid": 0,
            "name": "北京",
            "children": null
        },
        {
            "key": "rg_33",
            "groupId": 33,
            "pid": 0,
            "name": "自定义",
            "children": null
        }
    ],
    "uuid": "用户UUID"
}
```

**说明**:
- 返回所有电台分组的层级结构
- 包含省份和子地区的完整信息

### 5. 通过分组ID获取电台列表

**命令**: `get_radio_list_by_group_id`

**请求参数**:
```json
{
    "command": "get_radio_list_by_group_id",
    "uuid": "用户UUID",
    "groupId": 1
}
```

**响应参数**:
```json
{
    "command": "get_radio_list_by_group_id",
    "result": 0,
    "groupId": 1,
    "radios": [
        {
            "id": 1,
            "groupId": 1,
            "name": "北京新闻广播",
            "url": "ENCRYPT_PREFIX_base64编码的加密URL",
            "forbidden": false,
            "createTime": 1640995200,
            "groupName": "北京",
            "creator": "系统"
        }
    ],
    "uuid": "用户UUID"
}
```

### 6. 播放网络电台

**命令**: `play_radio_source`

**请求参数**:
```json
{
    "command": "play_radio_source",
    "uuid": "用户UUID",
    "radio_name": "电台名称",
    "radio_url": "电台播放地址",
    "zone_macs": ["分区设备MAC1", "分区设备MAC2"]
}
```

**响应参数**:
```json
{
    "command": "play_radio_source",
    "result": 0,
    "session_id": "播放会话ID",
    "message": "Radio playback started successfully"
}
```

**说明**:
- 支持向多个分区设备同时播放网络电台
- 系统会验证分区设备是否在线和可用
- 成功时返回会话ID，用于后续停止播放操作
- 需要互联网连接正常才能播放网络电台

### 7. 停止网络电台播放

**命令**: `stop_radio_source`

**请求参数**:
```json
{
    "command": "stop_radio_source",
    "session_id": "播放会话ID",
    "uuid": "用户UUID"
}
```

**响应参数**:
```json
{
    "command": "stop_radio_source",
    "result": 0,
    "message": "Radio playback stopped successfully",
    "uuid": "用户UUID"
}
```

**说明**:
- 使用播放时返回的会话ID来停止指定的电台播放
- 停止播放会释放相关资源和端口
- 如果会话不存在或已停止，会返回相应错误信息


## 使用示例

### 添加自定义电台

```javascript
// 发送添加电台请求
const addRadioRequest = {
    command: "add_radio_info",
    uuid: "user-uuid-123",
    name: "我的电台",
    url: "http://example.com/radio.m3u8"
};

webSocket.send(JSON.stringify(addRadioRequest));
```

### 获取电台列表

```javascript
// 获取自定义分组的电台列表
const getRadioListRequest = {
    command: "get_radio_list_by_group_id",
    uuid: "user-uuid-123",
    groupId: 33
};

webSocket.send(JSON.stringify(getRadioListRequest));
```

### 播放网络电台

```javascript
// 播放网络电台到指定分区
const playRadioRequest = {
    command: "play_radio_source",
    radio_name: "北京新闻广播",
    radio_url: "http://example.com/radio.m3u8",
    zone_macs: ["AA:BB:CC:DD:EE:FF", "11:22:33:44:55:66"]
};

webSocket.send(JSON.stringify(playRadioRequest));
```

### 停止网络电台播放

```javascript
// 停止指定会话的电台播放
const stopRadioRequest = {
    command: "stop_radio_source",
    session_id: "radio-session-12345",
    uuid: "user-uuid-123"
};

webSocket.send(JSON.stringify(stopRadioRequest));
```

## 注意事项

1. 所有API调用都需要有效的用户UUID
2. 电台管理操作（添加、编辑、删除）仅限于已登录且有权限的用户
3. 系统启动时会自动加载预置电台数据和用户自定义数据
4. 自定义电台数据会实时保存到radio_custom.json文件中
5. URL加密仅用于数据传输安全，不影响实际播放功能
6. 电台播放功能需要目标分区设备在线且互联网连接正常
7. 播放电台时会返回会话ID，请妥善保存用于后续停止操作
8. 同一分区设备同时只支持一个电台播放会话