#ifndef __KCP_H__
#define __KCP_H__
//#include "Lib/SBEUdpSocket.h"
#include "Network/HPSocket/MyUdpNode.h"
#include "ikcp.h"
#include "Tools/CTime.h"
#include <QMutex>
#include <QMutexLocker>
#include <shared_mutex>

class CKCPSocket
{
    public:
        CKCPSocket(void);
        ~CKCPSocket(void);
        void ikcpCreate(unsigned int conv, void *user);
        void ikcpUpdate(ikcpcb *kcp, IUINT32 current);
        void ikcpSetOutput(int (*output)(const char *buf, int len, ikcpcb *kcp,void *user));
        static int OutPutCB(const char *buf, int len, ikcpcb *kcp,void *user);
        void SendData(int channel,const char *buf,int len);
        bool CanIkcpSend(ikcpcb *kcp);
        IUINT32 IkcpCheck(ikcpcb *kcp,IUINT32 current);
        void ikcpInput(const char *buffer, int len);
        void ikcpClean(void);

        void SetDesIP(const char *desIP){ sprintf(m_desIP,"%s",desIP); };
        void SetDesPort(unsigned short	uPort){ m_port=uPort;};
        char *GetDesIP(){ return m_desIP;};
        unsigned short GetDesPort(){ return m_port;};

        void SetSendValid(bool isValid) {m_bSendValid=isValid;};
        bool GetSendValid() {return m_bSendValid;};
        void SetTestKCPValid(bool isValid) {m_bTestKcpValid=isValid;};
        bool GetTestKCPValid() {return m_bTestKcpValid;};

        void SetHeartBeatRecvCnt(int cnt) {kcp_heartBeat_recv_cnt=cnt;};
        int GetHeartBeatRecvCnt() {return kcp_heartBeat_recv_cnt;};
        void SetTestPkgSendCnt(int cnt) {kcp_testPkg_send_cnt=cnt;};
        int GetTestPkgSendCnt() {return kcp_testPkg_send_cnt;};
        void SetTestPkgRecvCnt(int cnt) {kcp_testPkg_recv_cnt=cnt;};
        int GetTestPkgRecvCnt() {return kcp_testPkg_recv_cnt;};

#if 1   //20210706取消KCP心跳超时检测，主动离线无效，应该是本地路由器策略问题
        //20220927 启用超时检测，用来判定开始KCP正常，后来KCP不通的问题。
        void SetHeartBeatTime(ctime_t t)	    {	m_heartBeat_time = t;	}
        ctime_t	GetHeartBeatTime()				{	return m_heartBeat_time;}
#endif
    public:
        //MyUdpNode*         m_socket;             // UDP-KCP(用于TCP优先模式)
        char	            m_desIP[MAX_IP_LEN];  // 目标IP地址
        unsigned short		m_port;				  // UDP-KCP 端口号
        ikcpcb              *ikcp;                 //ikcp_pcb
        IUINT32             DesTime;               //目标时间
        IUINT32             hasSendOrInput;        //kcp是否有动作
        unsigned int        kcp_conv;              //conv
        bool                isSendDataFull;        //发送的数据是否已满
#if 1   //20210706取消KCP心跳超时检测，主动离线无效，应该是本地路由器策略问题
        //20220927 启用超时检测，用来判定开始KCP正常，后来KCP不通的问题。
        ctime_t		        m_heartBeat_time;      //kcp heartBeat
        bool                m_bSendValid;          //发送是否有效
        bool                m_bTestKcpValid;       //是否测试KCP通断（如果测试一次失败后，此次连接不再测试，保持TCP连接）
        unsigned int        kcp_heartBeat_recv_cnt; //kcp心跳接收次数
        unsigned int        kcp_testPkg_send_cnt;   //kcp测试包发送次数
        unsigned int        kcp_testPkg_recv_cnt;   //kcp测试包接收次数
#endif
    public:
        shared_timed_mutex kcpSingleSharedMutex;
};


class CKCPSockets
{
    public:
        CKCPSockets(void);
        ~CKCPSockets(void);

        CKCPSocket* CreateSocket(unsigned short	uBindPort,	  // 创建socket
                        UDP_CALLBACK*	pFunRecvData,  // 收到数据后的回调函数
                        void*		pUserDefined,
                        unsigned int conv,void *user); // 自定义数据
        void CleanSocket(CKCPSocket *mkcpsocket);
        CKCPSocket*	 FindSocketByKcp(unsigned int kcpConv);
        bool StartWorking();
        void	 InputData(const char*       pData,			// 收到的数据
                              unsigned short	   nLen,		// 数据长度
                              const char*       szIP,			// IP地址
                              unsigned short	nPort,
                              unsigned int conv);			// 端口

        void	 HeartBeat(
                            const char*       pData,			// 收到的数据
                            unsigned short	   nLen,		// 数据长度
                            const char*       szIP,			// IP地址
                              unsigned short	nPort,
                              unsigned int conv);			// 端口

        static void *TimerCheckKCPScheme(void *lpParam);
    public:
        //vector<CKCPSocket> m_KcpSockets;       //KCP socket集合
        map<IUINT32,CKCPSocket*> m_KcpAllMap;  

        shared_timed_mutex kcpsSharedMutex;
    private:
        //QMutex    kcpsMutex;
        
};





#endif
