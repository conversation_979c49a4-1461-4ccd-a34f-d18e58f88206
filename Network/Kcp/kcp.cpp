#include "stdafx.h"
#include "kcp.h"
#include "ikcp_test.h"



CKCPSocket::CKCPSocket(void)
{
    //m_socket = new CUDPSocket();
    //m_socket=&g_Global.m_Network.m_MyUdpKCPUnicast;
    memset(m_desIP,0,sizeof(m_desIP));
    ikcp=NULL;
    kcp_conv=0;
    m_port=0;
    DesTime=0;
    hasSendOrInput=0;
    isSendDataFull=false;
    m_bSendValid=true;
    m_bTestKcpValid=true;
    kcp_heartBeat_recv_cnt=0;
    kcp_testPkg_send_cnt=0;
    kcp_testPkg_recv_cnt=0;
#if 1   
    //20210706取消KCP心跳超时检测，主动离线无效，应该是本地路由器策略问题
    //20220927 启用超时检测，用来判定开始KCP正常，后来KCP不通的问题。
    //20230415 还有一点就是，终端心跳包是可以发到服务器的，但是服务器发不到终端上去，这种情况也需要使用TCP传输，重点解决此问题。
    ctime_t	 tNow = CTime::GetCurrentTimeT().GetTime();
    SetHeartBeatTime(tNow);
#endif
}

CKCPSocket::~CKCPSocket(void)
{
    printf("~CKCPSocket,conv=%ul\n",kcp_conv);

}

void CKCPSocket::ikcpClean(void)
{
    if(ikcp)
    {
        printf("ikcpClean.release ikcp,conv=%ul...\n",kcp_conv);
        ikcp_release(ikcp);
        ikcp=NULL;
    }
}


void CKCPSocket::ikcpCreate(unsigned int conv, void *user)
{
    ikcp = ikcp_create(conv,user);
    ikcp_setoutput(ikcp,OutPutCB);
    kcp_conv=conv;

    ikcp_wndsize(ikcp, 128, 128);    //SEND_BUF 128,RECV 128
    ikcp_nodelay(ikcp, 1, 10, 2, 1);    //FAST MODE

    ikcp->interval = 5;

    printf("ikcpCreate:conv=%ul...\n",conv);
}

void CKCPSocket::ikcpUpdate(ikcpcb *kcp, IUINT32 current)
{
    ikcp_update(kcp,current);
}


IUINT32 CKCPSocket::IkcpCheck(ikcpcb *kcp,IUINT32 current)
{
    return ikcp_check(kcp,current);
}


void CKCPSocket::ikcpInput(const char *buffer, int len)
{
    if(ikcp)
    {
        ikcp_input(ikcp,buffer,len);
        hasSendOrInput=1;
        //ikcpUpdate(ikcp,iclock());
    }
}



bool CKCPSocket::CanIkcpSend(ikcpcb *kcp)
{
    int wait_snd=ikcp_waitsnd(kcp);
    //对于寻呼（512bytes/pkg，128pkg=65536bytes=1486ms）  //对于歌曲(mp3,1067bytes/pkg,128pkg=136576bytes=3.4s,wav,744ms)  

    if( wait_snd >= kcp->snd_wnd/2 )    //如果等待发送的包数量>=kcp->snd_wnd/2,即64，那么清空发送缓冲区然后继续发送新一轮数据
    {
        ickp_clear_buf(kcp);
        return false;
    }
    return true;
}


void CKCPSocket::SendData(int channel,const char *buf,int len)
{
    if(len > 1500)
        return;

    if(channel == 0)
    {
        //头4个字节需要为0
        char* cbuf = new char[len];//足够长
        if(cbuf!=NULL)
        {
            if(m_port)
            {
                memcpy(cbuf,buf,len);
                memset(cbuf,0,4);
                //if(m_socket)
                {
                    g_Global.m_Network.m_MyUdpKCPUnicast.SendData(cbuf,len,this->GetDesIP(),this->GetDesPort(),1);
                }
            }
            delete[] cbuf;
        }
    }
    else
    {
        unique_lock<shared_timed_mutex> ulk_kcp_single(kcpSingleSharedMutex);              //写锁加锁
        if(ikcp && m_port)
        {
            if( !CanIkcpSend(ikcp) )
            {
                //printf("cant't send kcp...\n");
                #if STREAM_KCP_TCP_AUTO_SWITCH
                if(GetSendValid())
                {
                    SetSendValid(false);
                    SetHeartBeatRecvCnt(0);
                    SetTestPkgSendCnt(0);
                    SetTestPkgRecvCnt(0);
                }
                #endif
            }
            else
            {
                ikcp_send(ikcp,buf,len);
                hasSendOrInput=1;
                //ikcp_flush(ikcp);
            }
        }
        else
        {
            //printf("CKCPSocket::SendData:ickp=NULL!\n");
        }
    }
}




// KCP的下层协议输出函数，KCP_update需要发送数据时会调用它
// buf/len 表示缓存和长度
// user指针为 kcp对象创建时传入的值，用于区别多个 KCP对象
int CKCPSocket::OutPutCB(const char *buf, int len, ikcpcb *kcp,void *user)
{
    if(kcp == NULL)
    {
        return len;
    }
    if(len >= 1500)
    {
        printf("ERROR:KCP OutPutCB,conv=%ul,len=%d\n",kcp->conv,len);
        return len;
    }
    //ikcp_update前已经加锁，此处无需。ikcp_update调用ikcp_flush，ikcp_flush调用ikcp_output
    //shared_lock<shared_timed_mutex> slk_kcps(g_Global.m_Network.m_kcpSockets.kcpsSharedMutex);              //读锁加锁
    if(g_Global.m_Network.m_kcpSockets.m_KcpAllMap.count(kcp->conv))
    {
        CKCPSocket *pSocket=g_Global.m_Network.m_kcpSockets.m_KcpAllMap[kcp->conv];
        if(pSocket && pSocket->GetDesPort())
        {
            //适配端口受限型NAT，发送接收需要用同一端口
            g_Global.m_Network.m_MyUdpKCPUnicast.SendData(buf,len,pSocket->GetDesIP(),pSocket->GetDesPort(),1);
            //printf("KCP_outputCB:ip=%s,port=%d,len=%d\n",desIP,kcpSocket->GetDesPort(),len);
        }
    }
    return len;
}





CKCPSockets::CKCPSockets(void)
{
}

CKCPSockets::~CKCPSockets(void)
{
    printf("~CKCPSockets...\n");
}




CKCPSocket* CKCPSockets::CreateSocket(unsigned short	uBindPort,	  // 创建socket
                                    UDP_CALLBACK*	pFunRecvData,  // 收到数据后的回调函数
                                    void*		pUserDefined,
                                    unsigned int kcpConv,void *kcpUser) // 自定义数据
{
    unique_lock<shared_timed_mutex> ulk_kcps(kcpsSharedMutex);              //写锁加锁
    if(m_KcpAllMap.count(kcpConv))
    {
        CKCPSocket *pSocket=m_KcpAllMap[kcpConv];
        if(pSocket)
        {
            unique_lock<shared_timed_mutex> ulk_kcp_single(pSocket->kcpSingleSharedMutex);              //写锁加锁
            pSocket->ikcpClean();
            delete pSocket;
            m_KcpAllMap[kcpConv]=NULL;
            m_KcpAllMap.erase(kcpConv);
        }
    }
    CKCPSocket *pSocket = new CKCPSocket();
    pSocket->ikcpCreate(kcpConv,kcpUser);
    //m_KcpSockets.push_back(*pSocket);
    m_KcpAllMap[kcpConv]=pSocket;

    printf("KCP CreateSocket OK...\n");

    return pSocket;
}


// 退出网络
void CKCPSockets::CleanSocket(CKCPSocket *mkcpsocket)
{
    printf("CleanSocket...\n");

    //QMutexLocker locker(&kcpsMutex);

    unique_lock<shared_timed_mutex> ulk_kcps(kcpsSharedMutex);              //写锁加锁

    IUINT32 conv=mkcpsocket->kcp_conv;
    if(m_KcpAllMap.count(conv))
    {
        CKCPSocket *pSocket=m_KcpAllMap[conv];
        if(pSocket)
        {
            unique_lock<shared_timed_mutex> ulk_kcp_single(pSocket->kcpSingleSharedMutex);              //写锁加锁
            pSocket->ikcpClean();
            delete pSocket;
            m_KcpAllMap[conv]=NULL;
            m_KcpAllMap.erase(conv);
        }
    }
}


CKCPSocket*	 CKCPSockets::FindSocketByKcp(unsigned int kcpConv)
{
    #if 0
    int nSocketsCount = m_KcpSockets.size();

    for (int i=0; i<nSocketsCount; ++i)
    {
        CKCPSocket& kcpSocket = m_KcpSockets.at(i);
        if ( kcpSocket.ikcp && kcpSocket.ikcp->conv == kcpConv )
        {
            return &kcpSocket;
        }
    }
    #endif
    return NULL;
}


void	 CKCPSockets::InputData(const char*       pData,			// 收到的数据
                              unsigned short	   nLen,		// 数据长度
                              const  char*       szIP,			// IP地址
                              unsigned short	nPort,
                              unsigned int conv)			// 端口
{
    //QMutexLocker locker(&kcpsMutex);
    shared_lock<shared_timed_mutex> slk_kcps(kcpsSharedMutex);              //读锁加锁

    if(m_KcpAllMap.count(conv))
    {
        CKCPSocket *pSocket=m_KcpAllMap[conv];
        if(pSocket)
        {
            unique_lock<shared_timed_mutex> ulk_kcp_single(pSocket->kcpSingleSharedMutex);              //写锁加锁
            if(strcmp(szIP,pSocket->GetDesIP())!=0 || pSocket->m_port!= nPort)
            {
                pSocket->SetDesIP(szIP);
                pSocket->SetDesPort(nPort);
                printf("KCPInputData:DesIP=%s,port=%d\n",szIP,nPort);
            }
            else
            {
                //printf("IP equal...\n");
            }
            pSocket->ikcpInput(pData,nLen);
            #if STREAM_KCP_TCP_AUTO_SWITCH
            if(!pSocket->GetSendValid() && pSocket->GetTestKCPValid())
            {
                //printf("Input Kcp Data...\n");
                int testPkgRecvCnt=pSocket->GetTestPkgRecvCnt();
                pSocket->SetTestPkgRecvCnt(++testPkgRecvCnt);
                //15次均有应答（小于15分钟，KCP恢复,否则此次连接永不恢复（除非掉线后重新连接）
                //printf("Recv KCPTestPkg=%d\n",testPkgRecvCnt);
                if( testPkgRecvCnt>=15 )
                {
                    if(testPkgRecvCnt >= pSocket->GetTestPkgSendCnt()-1)
                    {
                        ickp_clear_buf(pSocket->ikcp);
                        pSocket->SetSendValid(true);
                        //printf("KCP test OK!Resume\n");
                    }
                    else
                    {
                        pSocket->SetTestKCPValid(false);
                    }
                }
            }
            #endif
        }
    }
}



void	 CKCPSockets::HeartBeat(const char*       pData,			// 收到的数据
                              unsigned short	   nLen,		// 数据长度
                              const char*       szIP,			// IP地址
                              unsigned short	nPort,
                              unsigned int conv)			// 端口
{
    //QMutexLocker locker(&kcpsMutex);
    shared_lock<shared_timed_mutex> slk_kcps(kcpsSharedMutex);              //读锁加锁

    if(m_KcpAllMap.count(conv))
    {
        CKCPSocket *pSocket=m_KcpAllMap[conv];
        if(pSocket)
        {
            #if 1   //20210706取消KCP心跳超时检测，主动离线无效，应该是本地路由器策略问题
                    //20220927 启用超时检测，用来判定开始KCP正常，后来KCP不通的问题。
            ctime_t	 tNow = CTime::GetCurrentTimeT().GetTime();
            pSocket->SetHeartBeatTime(tNow);
            #endif
            if(strcmp(szIP,pSocket->GetDesIP())!=0 || pSocket->m_port!= nPort)
            {
                pSocket->SetDesIP(szIP);
                pSocket->SetDesPort(nPort);
                printf("HeartBeat:DesIP=%s,port=%d\n",szIP,nPort);
            }
            //转发回去
            pSocket->SendData(0,pData,nLen);
            #if STREAM_KCP_TCP_AUTO_SWITCH
            if(!pSocket->GetSendValid() && pSocket->GetTestKCPValid())
            {
                //每收到6个心跳包（一般情况下是60秒，也可能小于），发送一组测试包，验证KCP指令服务器能否收到？
                int heartBeatRecvCnt=pSocket->GetHeartBeatRecvCnt();
                pSocket->SetHeartBeatRecvCnt(++heartBeatRecvCnt);
                //printf("heartBeatRecvCnt=%d\n",heartBeatRecvCnt);
                if( (heartBeatRecvCnt%6) == 0 )
                {
                    int testPkgSendCnt=pSocket->GetTestPkgSendCnt();
                    pSocket->SetTestPkgSendCnt(++testPkgSendCnt);
                    char szBuf[MAX_BUF_LEN] = {0};
                    int	len	= CProtocol::ControlCommand(szBuf,CMD_KCP_TEST,NULL,0);
                    unique_lock<shared_timed_mutex> ulk_kcp_single(pSocket->kcpSingleSharedMutex);              //写锁加锁
                    ickp_clear_buf(pSocket->ikcp);
                    ikcp_send(pSocket->ikcp,szBuf,512);
                    pSocket->hasSendOrInput=1;
                    //printf("Send KCPTestPkg=%d\n",testPkgSendCnt);
                }
            }
            #endif
        }
    }
    else
    {
        printf("KCP HeartBeat:not found...\n");
    }
}





void *CKCPSockets::TimerCheckKCPScheme(void *lpParam)
{
    CKCPSockets* m_kcpSockets = (CKCPSockets*)lpParam;
    while(1)
    {
        shared_lock<shared_timed_mutex> slk_kcps(m_kcpSockets->kcpsSharedMutex);              //读锁加锁

        for(auto it = m_kcpSockets->m_KcpAllMap.begin(); it != m_kcpSockets->m_KcpAllMap.end(); ++it)
        {
            CKCPSocket *pSocket = it->second;
            unique_lock<shared_timed_mutex> ulk_kcp_single(pSocket->kcpSingleSharedMutex);              //写锁加锁
            if(pSocket->ikcp)
            {
                IUINT32 current = iclock();
                pSocket->ikcpUpdate(pSocket->ikcp, current);
            }
        }
        slk_kcps.unlock();
        usleep(5000);
    }
}



bool CKCPSockets::StartWorking()
{
    pthread_t pid;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&pid, &attr, TimerCheckKCPScheme, (void*)this);
    pthread_attr_destroy(&attr);
    return TRUE;
}
