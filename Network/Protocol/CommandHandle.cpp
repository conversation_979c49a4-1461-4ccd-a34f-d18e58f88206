#include "CommandHandle.h"
#include "stdafx.h"
#if SUPPORT_SPEEX_DENOISE
#include "speex/speex_preprocess.h"
#endif


// MAC数据转换为字符串
void SetMac(char* szMac, const char* pData)
{
    sprintf(szMac, "%02X:%02X:%02X:%02X:%02X:%02X",
        (unsigned char)pData[0],  (unsigned char)pData[1],  (unsigned char)pData[2],
        (unsigned char)pData[3],  (unsigned char)pData[4],  (unsigned char)pData[5]);
}

double SetDouble(const char* pData, int nPos)
{
    char	 szData[1500] = {0};
    memcpy(szData, &pData[0], nPos);
    return atof(szData);
}


CCommandHandle::CCommandHandle(void)
{
    m_pNetwork = NULL;
}

CCommandHandle::~CCommandHandle(void)
{

}

void CCommandHandle::SetNetwork(CNetwork* pNetwork)
{
    m_pNetwork = pNetwork;
}

CSection* CCommandHandle::GetOnlineDeviceByIP(const char *szIP)
{
    for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
    {
        CSection *pDevice = g_Global.m_pAllDevices[i]->GetSectionByIP(szIP);

        if (pDevice != NULL && pDevice->IsOnline())
        {
            return pDevice;
        }
    }

    return NULL;
}

CSection* CCommandHandle::GetOnlineDeviceByIPAndModel(const char *szIP, DeviceModel nModel)
{
    //如果不存在这个类型的设备，返回
    if(g_Global.m_ModelToDevices.count(nModel) == 0)
    {
        return NULL;
    }
    CSections *pDevices	= g_Global.m_ModelToDevices[nModel];
    CSection *pDevice = pDevices->GetSectionByIP(szIP);
    if (pDevice != NULL && pDevice->IsOnline())
    {
        return pDevice;
    }
    return NULL;
}

// TCP server 保留，待修改
CSection* CCommandHandle::GetOnlineDeviceBySockObjAndModel(LPS_SOCKET_OBJ SockObj, DeviceModel nModel)
{
    //如果不存在这个类型的设备，返回
    if(g_Global.m_ModelToDevices.count(nModel) == 0)
    {
        return NULL;
    }
    CSections *pDevices	= g_Global.m_ModelToDevices[nModel];
    CSection *pDevice = pDevices->GetSectionBySockObj(SockObj);
    if (pDevice != NULL && pDevice->IsOnline())
    {
        return pDevice;
    }
    return NULL;
}

// TCP server 保留，待修改
CSection* CCommandHandle::GetOnlineDeviceBySockObj(LPS_SOCKET_OBJ SockObj)
{
    for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
    {
        CSection *pDevice = g_Global.m_pAllDevices[i]->GetSectionBySockObj(SockObj);

        if (pDevice != NULL && pDevice->IsOnline())
        {
            return pDevice;
        }
    }

    return NULL;
}

CSection* CCommandHandle::GetOnlineDeviceByMac(const char* szMac)
{
    for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
    {
        CSection *pDevice = g_Global.m_pAllDevices[i]->GetSectionByMac(szMac);

        if (pDevice != NULL && pDevice->IsOnline())
        {
            return pDevice;
        }
    }

    return NULL;
}


// 处理上线恢复播放
void CCommandHandle::HandleOnlineResumePlaying(CSection* pSection, time_t tNow, BYTE nReservedWord)
{
    int nPlayID = -1;

    // 离线到上线，恢复音频采集器播放(放在SetProSource前面)
    if (pSection->m_PlayedRecently.IsNotTimeout(tNow))
    {
        WorkPattern workPattern = WorkPattern(nReservedWord>>2&0x03);

        if (workPattern != WP_UNKNOWN && workPattern != g_Global.m_WorkPattern)
        {
            pSection->m_PlayedRecently.m_isWorkPatternDiff = TRUE;
        }
        else
        {
            // 音频采集音源
            if (CProtocol::IsAudioCollectorSrc(pSection->m_PlayedRecently.m_nRecentSrc))
            {
                CSection *pAudioCol = g_Global.m_AudioCollectors.GetSectionBySrcID(pSection->m_PlayedRecently.m_nRecentSrc);

                if (pAudioCol != NULL && pAudioCol->IsOnline())
                {
                    // 告诉音频采集器与分区设备恢复播放
                    //判断属于哪个音频通道
                    char channel = (pSection->m_PlayedRecently.m_nRecentSrc-PRO_AUDIO_COLLECTOR_MIN)%4 +1;


                    bool isValid=false;
                    unsigned char timing_volume=0xff;
                    //判断是不是定时点采集
                    if(pSection->m_PlayedRecently.m_bAcSourceInTiming)
                    {
                        //是定时点采集，那么需要判断是不是有效定时点时间内
                        //遍历所有有效的指定定时点
                        vector<CTimePoint> timerVec;
                        if(g_Global.m_TimerScheme.GetRunningTimePointAcChannel(timerVec,pAudioCol->GetMac(),channel))
                        {
                            for(int i=0;i<timerVec.size();i++)
                            {
                                CTimePoint &timePoint = timerVec[i];
                                if(timePoint.ContainZone(pSection->GetMac()))
                                {
                                    isValid=true;
                                    timing_volume =  timePoint.GetFollowDevice()?0xff:timePoint.GetVolume();
                                    break;
                                }
                            }
                        } 
                    }
                    //如果是触发采集，判断采集器触发信号是否存在
                    else if(pSection->m_PlayedRecently.m_bAcSourceInTrigger)
                    {
                        if(pAudioCol->m_pAudioCollector->IsAudioCollectorSingalValid())
                        {
                            isValid=true;
                        }
                    }
                    else
                    {
                        //不是定时点采集和触发采集，那么直接发送恢复播放音频采集音源。
                        isValid=true;
                    }

                    if(isValid)
                    {
                        //先发送给采集器
                        g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(*pAudioCol, pAudioCol->m_pAudioCollector,channel,1);

                        if(pSection->m_PlayedRecently.m_bAcSourceInTiming)
                        {
                            g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(*pSection,pAudioCol->m_pAudioCollector,channel,1,true,timing_volume);
                        }
                        if(pSection->m_PlayedRecently.m_bAcSourceInTrigger)
                        {
                            g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(*pSection,pAudioCol->m_pAudioCollector,channel,1,false,0xff,true,pAudioCol->m_pAudioCollector->GetTriggerVolume());
                        }
                        else
                        {
                            g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(*pSection, pAudioCol->m_pAudioCollector,channel,1);
                        }
                    }
                }
            }
        }

        if(g_Global.m_bNeedResumeLocalPlay)
        {
            // (设备掉线后恢复播放)(集中模式) CS 2019-5-7
            if(WP_IS_CENTRALIZED
                && (pSection->m_PlayedRecently.m_nRecentSrc == PRO_LOCAL_PLAY || pSection->m_PlayedRecently.m_nRecentSrc == PRO_TIMING)
                && pSection->m_PlayedRecently.m_nPlayID > 0)
            {
                nPlayID = pSection->m_PlayedRecently.m_nPlayID;
            }
        }
    }
    else if (WP_IS_CENTRALIZED)
    {
        CTimePoint *pTimePoint = g_Global.m_TimerScheme.GetPlayingTimePointByDeviceMac(pSection->GetMac());
        if (pTimePoint != NULL)
        {
            nPlayID = pTimePoint->GetPlayID();
        }
    }

    if (nPlayID > 0)
    {
        // 给分区发送播放命令
        CSongUnit *pSrcUnit = g_Global.m_PlayQueue.m_SongPlayer.GetSongUnitByPlayID(nPlayID);

        if (pSrcUnit != NULL)
        {
            song_header_t	songHeader	= pSrcUnit->GetSongHeader();
            CMyString		strName		= GetNameByPathName(pSrcUnit->GetPathName(), TRUE);

            // 下发播放命令
            g_Global.m_Network.m_CmdSend.CmdNotifyStreamSource( *pSection,
                                            pSrcUnit->GetSource(),
                                            pSrcUnit->GetSongFormat(),
                                            songHeader.samp_freq,
                                            (BYTE)songHeader.bit_samp,
                                            (BYTE)songHeader.channels,
                                            strName.C_Str(),
                                            pSrcUnit->GetPlayIP(),
                                            pSrcUnit->GetPlayPort(),
                                            pSrcUnit->GetVolume(),
                                            pSrcUnit->GetSongLength(),
                                            pSrcUnit->GetTotalFrames(),
                                            pSrcUnit->GetCurrentFrame(),
                                            pSrcUnit->GetSongMd5(),
                                            pSrcUnit->GetIsMulticastUseNewCmd(),
                                            0x00);	// 确认标志 0x00新播放 0x01继续播放（发送继续播放命令时，设备会延迟播放，导致声音不同步）

            pSection->SetPlayID(pSrcUnit->GetPlayID());

            if( pSrcUnit->GetPlayStatus() == SPS_PAUSE_AUTO)
            {
                pSrcUnit->SetPlayStatus(SPS_PLAY);         // 让音频流恢复发送
            }
            else if( pSrcUnit->GetPlayStatus() == SPS_PAUSE_MANUAL)
            {
                g_Global.m_Network.m_CmdSend.CmdSetSingleDevicePlayStatus(PS_PAUSE, *pSection);
            }

            pSection->m_PlayedRecently.ResetTimeout();
        }
    }
}


// 分区节目源改变
void CCommandHandle::SectionChangedSource(CSection& section, ProgramSource newSrc)
{
    if (newSrc == section.GetProSource())
    {
        return;
    }

    // 集中模式
    if (WP_IS_CENTRALIZED && !PLAY_IS_HTTP)
    {
        // 告诉播放队列去处理
        g_Global.m_PlayQueue.SectoinSourceChange(section, newSrc);
    }
    else
    {
        section.SetPlayID(-1);
    }
    
#if 1
    // 之前是音频采集器音源，则恢复播放
    // 20220718 有个问题：定时结束后如何恢复采集音源
    if ( section.NeedResumeAudioCollector(newSrc) )
    {
        //printf("Section NeedResumeAudioCollector:m_PlayedRecently.m_nRecentSrc=%d,m_bSongTimerEndResumeAc=%d...\n",section.m_PlayedRecently.m_nRecentSrc,\
            section.m_PlayedRecently.m_bSongTimerEndResumeAc);
        //当前节目源
        CSection *pAudioCol = NULL;
        int type=0;
        if(CProtocol::IsAudioCollectorSrc(section.m_PlayedRecently.m_nRecentSrc))
        {
            type=0;
            pAudioCol = g_Global.m_AudioCollectors.GetSectionBySrcID(section.m_PlayedRecently.m_nRecentSrc);
        }
        else if(CProtocol::IsAudioCollectorSrc(section.m_PlayedRecently.m_bSongTimerEndResumeAc))
        {
            type=1;
            pAudioCol = g_Global.m_AudioCollectors.GetSectionBySrcID(section.m_PlayedRecently.m_bSongTimerEndResumeAc);
        }

        if (pAudioCol != NULL)
        {
            if(pAudioCol->IsOnline())
            {
                // 告诉音频采集器与分区设备恢复播放
                //判断属于哪个音频通道
                //printf("section.GetProSource()=0x%x\n",section.GetProSource());
                char channel  = 1;
				//char channel = (section.GetProSource()-PRO_AUDIO_COLLECTOR_MIN)%4 +1;
                if(type == 0)
                {
                    channel = (section.m_PlayedRecently.m_nRecentSrc-PRO_AUDIO_COLLECTOR_MIN)%4 +1;
                }
                else if(type == 1)
                {
                    channel = (section.m_PlayedRecently.m_bSongTimerEndResumeAc-PRO_AUDIO_COLLECTOR_MIN)%4 +1;
                }

                bool isValid=false;
                unsigned char timing_volume=0xff;
                if(type == 0)
                {
                    //判断是不是定时点采集
                    if(section.m_PlayedRecently.m_bAcSourceInTiming)
                    {
                        //是定时点采集，那么需要判断是不是有效定时点时间内
                        //遍历所有有效的指定定时点
                        vector<CTimePoint> timerVec;
                        if(g_Global.m_TimerScheme.GetRunningTimePointAcChannel(timerVec,pAudioCol->GetMac(),channel))
                        {
                            for(int i=0;i<timerVec.size();i++)
                            {
                                CTimePoint &timePoint = timerVec[i];
                                if(timePoint.ContainZone(section.GetMac()))
                                {
                                    isValid=true;
                                    timing_volume =  timePoint.GetFollowDevice()?0xff:timePoint.GetVolume();
                                    break;
                                }
                            }
                        } 
                    }
                    else
                    {
                        //不是定时点采集，那么久直接发送恢复播放音频采集音源。
                        isValid=true;
                    }
                }
                //如果是触发采集，判断采集器触发信号是否存在
                else if(section.m_PlayedRecently.m_bAcSourceInTrigger)
                {
                    if(pAudioCol->m_pAudioCollector->IsAudioCollectorSingalValid())
                    {
                        isValid=true;
                    }
                }
                else
                {
                    isValid=true;
                }
                if(isValid)
                {
                    //先发送给采集器
                    g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(*pAudioCol, pAudioCol->m_pAudioCollector,channel,0);

                    if(section.m_PlayedRecently.m_bAcSourceInTiming)
                    {
                        g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(section,pAudioCol->m_pAudioCollector,channel,0,true,timing_volume);
                    }
                    else if(section.m_PlayedRecently.m_bAcSourceInTrigger)
                    {
                        g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(section,pAudioCol->m_pAudioCollector,channel,0,false,0xff,true,pAudioCol->m_pAudioCollector->GetTriggerVolume());
                    }
                    else
                    {
                        g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(section, pAudioCol->m_pAudioCollector,channel,0);
                    }
                }
            }
        }
    }
#endif
    // 更新节目源
    section.SetProSource(newSrc);

    // 如果不是告警，则把触发次数重置为0
    if (newSrc != PRO_ALARM)
    {
        section.SetTriggerCount(0);
    }
}


/***************************************************/
// 处理控制命令
void CCommandHandle::HandleControlCommand(const char*       pData,		// 数据区
                                          unsigned short	nLen,		// 数据长度
                                          const char*       szIP,		// IP地址
                                          unsigned short	uPort,		// 端口（UDP用得到，TCP可以直接设置为0）
                                          LPS_SOCKET_OBJ sockobj)		// 为NULL时是UDP，否则为TCP
{
    ushort  command     = CharsToShort(&pData[0]);      // 命令
    u_char	nReserved	= (unsigned char)pData[3];		// 保留字
    u_char	devModel	= (unsigned char)pData[4];		// 设备型号
    u_char	packAttr	= (unsigned char)pData[5];		// 包属性  3.15
    ushort	uDataLen	= CharsToShort(&pData[6]);		// 数据长度
    u_char	dataPos		= 8;
    CSection *pDevice   = NULL;
    //判断校验码是否正常
    if(nLen<9 || nLen>=1450 || uDataLen!=nLen-9 || pData[nLen-1] != CProtocol::GetChecksum(pData+dataPos,uDataLen))
    {
        return;
    }

    //对于备用服务器：如果没有切换至主服务器模式，那么除了主机发送的命令，其他命令不接收
    #if IS_BACKUP_SERVER
    if(!g_Global.m_serverSync.IfBakcupServerChangeToMaster() && devModel != MODEL_HOST)
    {
        return;
    }
    #endif

    m_pNetwork->m_CmdSend.RemoveCommand(command, szIP);

    //如果是龙之音商用版本，TCP数据是旧设备的上线通知，让其变成CMD_NOTIFY_ONLINE_DEVICE，也就是同时兼容旧设备的TCP连接
    #if APP_IS_LZY_COMMERCE_VERSION
    if(command == 0xFFFA && sockobj)
    {
        command = CMD_NOTIFY_ONLINE_DEVICE;
    }
    #endif

    // 如果不是这三条命令
    if (command != CMD_BROADCAST_HOST_INFO && command != CMD_CONNECT_STANDBY_SERVER  && command != CMD_NOTIFY_ONLINE_DEVICE 
        && command != CMD_TCP_CLIENT_CONNECTED)
    {
        pDevice = (sockobj ? GetOnlineDeviceBySockObjAndModel(sockobj,(DeviceModel)devModel) : GetOnlineDeviceByIPAndModel(szIP,(DeviceModel)devModel));
        
        // 如果不是在线设备发过来的命令，则忽略
        if (pDevice == NULL)
        {
            return;
        }
    }

    switch (command)
    {
        #if SUPPORT_SERVER_SYNC
        case CMD_BROADCAST_HOST_INFO:        // 搜索到其它主机发过来的搜索主机的命令
        {
            //if(!CNetworkTool::IsHostIP(szIP))
            //if( strcmp(g_Global.m_szNetworkIP,szIP) )
            {
                HandleSearchOnlineHost(&pData[dataPos], uDataLen, szIP);
            }
        }
        break;

        case CMD_CONNECT_STANDBY_SERVER:
        {
            //if( strcmp(g_Global.m_szNetworkIP,szIP) )
            {
                HandleConnectStandByServer(&pData[dataPos], uDataLen, szIP);
            }
        }
        break;
        #endif

    case CMD_TCP_CLIENT_CONNECTED:      //TCP
    {
        printf("CMD_TCP_CLIENT_CONNECTED...\n");
        //客户端连接上主机后发送，数据与搜索设备应答一致，先删除原来的kcp socket，然后创建新的。
        //需应答给客户端，否则客户端每隔几秒会发送此连接信息。应答后会转为发送CMD_NOTIFY_ONLINE_DEVICE
        
        //HP-SOCKET 异步处理，需要加锁，否则可能新上线的分区ID错乱
        #if !SUPPORT_AUDIO_MIXER
        if(devModel == MODEL_AUDIO_MIXER_DECODER || devModel == MODEL_AUDIO_MIXER_ENCODER ||
            devModel == MODEL_AUDIO_MIXER_DECODER_C || devModel == MODEL_AUDIO_MIXER_ENCODER_C)
        {
            return;
        }
        #endif
        HandleSearchOnlineDevice(&pData[dataPos], uDataLen, szIP, (DeviceModel)devModel, nReserved, sockobj);

        if (CSections::IsSectionDevice((DeviceModel)devModel)) // 分区设备：IP音箱...
        {
            char			szName[SEC_NAME_LEN+1]		= {0};
            char			szMac[SEC_MAC_LEN]			= {0};
            unsigned short	pos = 8;
            // 别名
            int nameLen = pData[pos++];
            memcpy(szName, &pData[pos], nameLen);
            pos += nameLen;
            printf("nameLen=%d,name=%s\n",nameLen,szName);
            // MAC地址
            unsigned int kcpConv=0;
            int macLen = pData[pos++];
            SetMac(szMac, &pData[pos]);
            pos += macLen;
            printf("szMac=%s\n",szMac);
            CSection *pSection = g_Global.m_Sections.GetSectionByMac(szMac);
            if(pSection!=NULL && sockobj != NULL)
            {
                char* cbuf = new char[nLen];//足够长
                memcpy(cbuf,pData,nLen);
                if(nLen>0)
                {
                    cbuf[4]=0x1;   //server
                    g_Global.m_Network.SendTcpData(cbuf, nLen, sockobj);
                }
                delete[] cbuf;
                printf("found pSection,desIp=%s...\n",szIP);
                
                if(pSection->m_kcpsocket!=NULL)
                {
                    printf("pSection->m_kcpsocket!=NULL,clean...\n");
                    g_Global.m_Network.m_kcpSockets.CleanSocket(pSection->m_kcpsocket);
                    pSection->m_kcpsocket=NULL;
                }
                

                pSection->m_kcpsocket = g_Global.m_Network.m_kcpSockets.CreateSocket(0,NULL,NULL,pSection->GetKcpConv(),NULL);  // 不需要绑定特定端口（随机即可），但是需要使用回调
                //告诉CLIENT UDP端口号+KCP id（4字节，由MAC组成），终端收到后往UDP发送音频流确认包（由KCP自动处理）及心跳（心跳使用普通UDP即可）
            }
        }
    }
        break;
    case CMD_NOTIFY_ONLINE_DEVICE:      // 搜索设备、设备主动通知
    {
        // 其它主机发过来的，重复发过来的不接收
        if(devModel==MODEL_HOST)
        {

        }
        else
        {
            //HP-SOCKET 异步处理，需要加锁，否则可能新上线的分区ID错乱
            #if !SUPPORT_AUDIO_MIXER
            if(devModel == MODEL_AUDIO_MIXER_DECODER || devModel == MODEL_AUDIO_MIXER_ENCODER ||
                devModel == MODEL_AUDIO_MIXER_DECODER_C || devModel == MODEL_AUDIO_MIXER_ENCODER_C)
            {
                return;
            }
            #endif

            HandleSearchOnlineDevice(&pData[dataPos], uDataLen, szIP, (DeviceModel)devModel, nReserved, sockobj);

            char			szName[SEC_NAME_LEN+1]		= {0};
            char			szMac[SEC_MAC_LEN]			= {0};
            unsigned short	pos = 8;
            // 别名
            int nameLen = pData[pos++];
            memcpy(szName, &pData[pos], nameLen);
            pos += nameLen;

            // MAC地址
            unsigned int kcpConv=0;
            int macLen = pData[pos++];
            SetMac(szMac, &pData[pos]);
            pos += macLen;

            if (CSections::IsSectionDevice((DeviceModel)devModel)) // 分区设备：IP音箱...
            {
                CSection *pSection = g_Global.m_Sections.GetSectionByMac(szMac);

                if(pSection!=NULL && sockobj != NULL)
                {
                    
                    if(pSection->m_kcpsocket==NULL) //收到0xfffa但是kcp处于关闭状态，重新创建
                    {
                        printf("pSection->m_kcpsocket == NULL,reCreate!\n");
                    
                        pSection->m_kcpsocket = g_Global.m_Network.m_kcpSockets.CreateSocket(0,NULL,NULL,pSection->GetKcpConv(),NULL);  // 不需要绑定特定端口（随机即可），但是需要使用回调
                    }
                    
                    char* cbuf = new char[nLen];//足够长
                    memcpy(cbuf,pData,nLen);
                    if(nLen>0)
                    {
                        cbuf[4]=0x1;   //server
                        g_Global.m_Network.SendTcpData(cbuf, nLen, sockobj);
                    }
                    delete[] cbuf;
                    
                }
            }
            else
            {
                char* cbuf = new char[nLen];//足够长
                memcpy(cbuf,pData,nLen);
                if(nLen>0)
                {
                    cbuf[4]=0x1;   //server
                    g_Global.m_Network.SendTcpData(cbuf, nLen, sockobj);
                }
                delete[] cbuf;
            }
        }
    }
        break;

    case CMD_NOTIFY_OFFLINE_DEVICE:	// 设备主动通知下线（应用于通过WEB恢复出厂设置后的重启）
    {
        m_pNetwork->DeviceOffline(*pDevice);
    }
        break;

    case CMD_GET_DEVICE_STATUS:		// 获取设备状态、状态改变时设备主动通知
    {
        HandleGetStatus(&pData[dataPos], uDataLen, *pDevice);
    }
        break;

    case CMD_FIRMWARE_UPGRADE:		// 固件升级
    {
        HandleFirmwareUpgrade(pData[dataPos], *pDevice);
    }
        break;

    case CMD_NOTIFY_PROGRESS_RATE:	// 升级进度
    {
        HandleProgressRate(pData[dataPos], *pDevice);
    }
        break;

    case CMD_UPDATE_FILE:			// 更新文件
    {
        if (uDataLen > 0)
        {
            HandleUpdateFileResult((FileType)pData[dataPos], pData[dataPos+1], *pDevice);
        }
    }
        break;

    case CMD_GET_FILE_INFO:			// 获取文件信息
    {
        HandleGetFileInfo(&pData[dataPos], uDataLen, *pDevice);
    }
        break;

    case CMD_SONG_SYNC_PROGRESS:	// 歌曲同步进度
    {
        HandleSyncProgress(&pData[dataPos], uDataLen, *pDevice);
    }
        break;

    case CMD_REQUEST_SYNC_TIME:	// 终端主动请求时间同步
    {
        HandleRequestSyncTime(*pDevice);
    }
        break;

    case CMD_REASSIGN_MAC:	// 重新分配MAC
    {
        HandleReassignMac(*pDevice);
    }
        break;

    case CMD_REBOOT:		// 重启设备
    {
        HandleReboot(*pDevice);
    }
        break;

    case CMD_RESET_DATA:	// 重置设备数据
    {
        HandleResetData(pData[dataPos], *pDevice);
    }
        break;

    case CMD_SPEAKER_OFFLINE_INFO:	// 寻呼台向主机发送终端寻呼中途掉线信息
    {
        // 调试时用，要去掉，否则重置数据时也会莫名地收到这条命令
        //HandleSpeakerOffline(&pData[dataPos], uDataLen, *pDevice);
    }
        break;
#if 0
    case CMD_GET_FLASH_INFO:		// 获取FLASH信息
    {
        if (uDataLen >= 2)
        {
            HandleGetFlashInfo(&pData[dataPos], uDataLen, *pDevice);
        }
    }
#endif
        break;

    case CMD_GET_DATE_TIME:	// 处理设备返回日期时间
    {
        HandleGetDateTime(&pData[dataPos], uDataLen, *pDevice);
    }
        break;

    case CMD_GET_LOG_FILE_LIST: // 处理返回日志文件列表
    {
        HandleGetLogFileList(&pData[dataPos], uDataLen, *pDevice);
    }
        break;

    case CMD_GET_LOG_FILE_DATA:	// 处理返回日志文件
    {
        HandleGetLogFileData(&pData[dataPos], uDataLen, *pDevice);
    }
        break;

    case CMD_GET_ALARM_STATE:		// 处理消防采集器返回通道触发状态
    {
        HandleGetAlarmState(&pData[dataPos], uDataLen, *pDevice, FALSE);
    }
        break;

    case CMD_ALARM_STATE_CHANGED:	// 处理消防采集器通道触发状态改变通知
    {
        HandleGetAlarmState(&pData[dataPos], uDataLen, *pDevice, TRUE);
    }
        break;

    case CMD_ALARM_TRIGGER_MODE:// 处理消防采集器返回触发模式
    {
        if (uDataLen > 0) // 查询
        {
            HandleGetAlarmMode(&pData[dataPos], uDataLen, *pDevice);
        }
    }
        break;

    case CMD_IP_INFO:			// 处理返回网络信息
    {
        HandleNetworkIPInfo(&pData[dataPos], uDataLen);
    }
        break;

    case CMD_NETWORK_MODE:			// 处理返回网络信息
    {
        HandleNetworkModeInfo(&pData[dataPos], uDataLen,*pDevice);
    }
        break;

    case CMD_EQ_MODE:			// 处理返回查询EQ音效
    {
        if (uDataLen > 0)
        {
            HandleGetDeviceEq(&pData[dataPos], uDataLen, *pDevice);
        }
    }
        break;

#if 0
    case CMD_POWER_OUTPUT_MODE:
    {
        if (uDataLen == 3)	// 查询才处理
        {
            HandlePowerOutputMode(&pData[dataPos], uDataLen, *pDevice);
        }
    }
        break;

    case CMD_SIGAL_DETECTION:	// 处理9131/9134网络解码器的回路检测状态返回
    {
        HandleSigalDetection(pData[dataPos]  == 0x01, pData[dataPos+1] == 0x01, *pDevice);
    }
        break;

    case CMD_SPLITTER_STATUS:	// 处理返回分区器状态
    {
        if (uDataLen > 0)
        {
            HandleGetSplitterStatus((unsigned char)pData[dataPos], *pDevice);
        }
    }
        break;

    case CMD_MIC_STATUS:        // 处理返回无线MIC的状态
    {
        if (uDataLen > 0)
        {
            HandleGetDeviceMicStatus(&pData[dataPos], uDataLen, *pDevice);
        }
    }
        break;

    case CMD_EMC_STATUS:		// 处理返回EMC状态
    {
        if (uDataLen > 0)
        {
            HandleGetEmcStatus((unsigned char)pData[dataPos], *pDevice);
        }
    }
        break;

    case CMD_MIXING_MODE:		// 处理返回混音信息
    {
        if (uDataLen > 0)
        {
            HandleGetDeviceMixing(&pData[dataPos], uDataLen, *pDevice);
        }
    }
        break;
#endif
    case CMD_QUERY_DEVICEBW:
    {
        if(uDataLen > 0)
        {
            HandleGetDeviceBW(&pData[dataPos], uDataLen, *pDevice);
        }
    }
        break;

    case CMD_FIRST_BOOT:
    {
        HandleFirstBoot(&pData[dataPos], uDataLen, *pDevice);
    }
        break;

    case CMD_BLUETOOTH_INFO:
    {
        HandleGetDeviceBtInfo(&pData[dataPos], uDataLen, *pDevice);
    }
        break;

    case CMD_SEQUENCE_POWER_INFO:
    {
        HandleGetSequencePowerInfo(&pData[dataPos], uDataLen, *pDevice);
    }
        break;

    case CMD_DEVICE_SUB_VOLUME: //查询/设置设备子音量
    {
        HandleGetSubVolumeInfo(&pData[dataPos], uDataLen, *pDevice);
        break;
    }

    case CMD_DEVICE_VOLUME_ADD_MIN: //主机控制设备音量加/减
    {
        //应答无需处理
        break;
    }
#if SUPPORT_REMOTE_CONTROLER
    case CMD_REMOTE_CONTROLER_KEY: //远程遥控器按键
    {
        HandleRemoteControlerKey(&pData[dataPos], uDataLen, *pDevice);
        break;
    }
#endif
    /**************************************************************/
    // 集中模式下处理与分控设备交互的

    case CMD_SELECTED_SECTIONS:	// 处理分控设备发过来的选中分区
    {
        HandleSelectedSections(nReserved, &pData[dataPos], uDataLen, *pDevice);
    }
        break;

    case CMD_REQUEST_PLAY_SOURCE:// 处理分控设备发过来的点播请求（此条命令只针对集中模式）
    {
        HandleRequestPlaySource(pData, nLen, *pDevice);
    }
        break;

    case CMD_WORK_PATTERN:		// 设置分控设备工作模式
    {
        if(uDataLen == 0)
        {
            // 分控设备中途掉线又上线（过程没小于超时时间），判断工作模式设置完成，就需要把所有分区状态发给分控设备
            if (uDataLen == 0 && pDevice->IsControlDevice() && (NETWORK_IS_TCP || WP_IS_CENTRALIZED))
            {
                m_pNetwork->ForwardSectionsStatusToControlDevices(NULL, pDevice);
                m_pNetwork->ForwardAllCollectorsStatusToControlDevices(NULL, pDevice);
            }
#if 0
            // 如果是工作模式不一样，表明是要恢复播放状态
            if(pDevice->IsSectionDevice() && pDevice->m_PlayedRecently.m_isWorkPatternDiff)
            {
                // 音频采集器
                if (CProtocol::IsAudioCollectorSrc(pDevice->m_PlayedRecently.m_nRecentSrc))
                {
                    CSection *pAudioCol = g_Global.m_AudioCollectors.GetSectionBySrcID(pDevice->m_PlayedRecently.m_nRecentSrc);

                    if (pAudioCol != NULL && pAudioCol->IsOnline())
                    {
                        // 告诉音频采集器与分区设备恢复播放
                        g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(*pDevice, pAudioCol->m_pAudioCollector);
                        g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(*pAudioCol, pAudioCol->m_pAudioCollector);
                    }
                    else if(pAudioCol != NULL && pAudioCol->IsLocal())
                    {
                        g_Global.m_AuxPlay.StartAUXWorking();
                        g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(*pDevice, pAudioCol->m_pAudioCollector);
                    }
                }
            }
#endif
            pDevice->m_PlayedRecently.m_isWorkPatternDiff = FALSE;
        }

    }
        break;

    case CMD_NOTIFY_STREAM_SOURCE: // 处理请求播放节目源返回结果
    {
        HandleNotifyStreamSource(pData[dataPos], *pDevice);
    }
        break;

    case CMD_PLAY_MODE:		// 处理分控设备的设置播放模式
    {
        if (uDataLen == 2)	// 等于2才是分控设备设置播放模式，等于0是主机设置终端（包括分控设备）播放模式的回应命令
        {
            HandleSetPlayMode(pData, nLen, *pDevice);
        }
        else if (pDevice->IsControlDevice()) // 分控设备查询播放模式
        {
            // 回应分控设备
            m_pNetwork->m_CmdSend.CmdResponsePlayMode(*pDevice, g_Global.m_PlayList.GetPlayMode());
        }
    }
        break;

    // 以下命令主机只是对分区设备操作，才能合在一起处理
    case CMD_DEVICE_VOLUME:		// 设置音量
    case CMD_SET_PLAY_STATUS:   // 设置播放状态
    case CMD_MUTE_STATUS:		// 设置静音状态
    case CMD_CONTROL_MODE:		// 设置设备程控状态
    case CMD_PLAY_LOCAL_SONG:	// 点播歌曲（分布模式）
    case CMD_PLAY_RING:			// 播放钟声（分布模式）
    case CMD_IDLE_STATUS:		// 设置空闲状态
    case CMD_SET_AUDIO_INFO:	// 设置音频采集器信息

    #if SUPPORT_TCP_DEFAULT
    case CMD_NOTIFY_PAGING:		// 终端被寻呼通知(TCP模式，转发)
    #endif

    {
        if (pDevice->IsControlDevice())
        {
            HandleForwardControlDevice(command, pData, nLen, *pDevice);
        }
        else if(pDevice->IsSectionDevice())    // 恢复播放拒绝处理
        {
            if(command == CMD_SET_AUDIO_INFO)
            {
                HandleSetAudioCollectSource(pData[dataPos], *pDevice);
            }
        }
    }
        break;

    #if SUPPORT_TCP_DEFAULT
    
    case CMD_PAGING_AGAIN:		// 寻呼台向终端发送掉线再次寻呼指令（TCP模式）
    case CMD_PAGING_STREAM:		// 寻呼数据传输(TCP模式，转发)
    {
        if (pDevice->IsControlDevice())
        {
            HandleForwardPagingDevice(command, pData, nLen, *pDevice);
        }
    }
        break;

    #endif
#if 0
    case CMD_CONFIRM_FORWARD_AUDIO:	// 转发音频流的确认包
    {
        pDevice->m_uAudioPort = uPort;

        char szBuf[MAX_BUF_LEN] = {0};
        memcpy(szBuf, pData, nLen);

        szBuf[4] = MODEL_HOST;	// 设备型号修改为主机

        // UDP点对点回应终端
        // Linux服务器
        m_pNetwork->m_pSocketStream->SendData(szBuf, nLen, szIP, uPort, 1);
    }
        break;
#endif

#if 0
    case CMD_REQUEST_FILE_INFO:		// 终端向主机查询文件信息
        {
            g_Global.m_Network.m_CmdSend.CmdRequestGetFileInfo((FileType)pData[dataPos], *pDevice);
        }
        break;
#endif
    /**************************************************/
        // SIP命令处理

    case CMD_SIP_GET_STATUS: // 获取SIP状态
    {
        HandleSipGetStatus(pData[dataPos], *pDevice);
    }
        break;

    case CMD_SIP_GET_LOG_INFO:	// 获取SIP账号登录信息
    {
        HandleSipGetLogInfo(&pData[dataPos], uDataLen, *pDevice);
    }
        break;
 #if SUPPORT_PAGER_CALL
    case CMD_ALL_PAGER_STATUS:  //获取所有寻呼台设备的状态 
    {
        if(uDataLen>0)
            g_Global.m_Network.m_CmdSend.CmdForwardPagerStatus(pDevice,NULL);
    }
        break;
    
    case CMD_CALLING_INVITATION:  //对讲设备主叫方发起对讲邀请 
    case CMD_CALLED_RESPONSE:     //对讲设备主叫方发起对讲邀请
    case CMD_CALLED_STATUS:       //对讲设备状态反馈
    case CMD_CALLING_AUDIOSTREAM: //对讲设备音频流传输
    
    case CMD_CALL_REQUEST_VIDEO:
    case CMD_CALL_VIDEO_PARM:
    case CMD_CALL_VIDEO_STATUS:
    case CMD_CALL_VIDEO_STREAM:
        if(uDataLen>0)
        {
            HandleForwardCallingCommand(command, pData, nLen, pDevice);
        }
        break;
#endif

#if SUPPORT_LISTEN_FUNCTION
    case CMD_LISTEN_EVENT:              //监听设备发起监听
    case CMD_LISTEN_RESPONSE:           //被监听设备应答
    case CMD_LISTEN_STREAM_UPLOAD:      //监听设备上传音频流
    case CMD_LISTEN_STATUS:             //监听设备发送监听状态
        if(uDataLen>0)
        {
            HandleForwardListenCommand(command, pData, nLen, pDevice);
        }
    break;
#endif

    case CMD_CONTROLER_ACCOUNT_INFO:    //控制设备向主机发送登录账户
    {
        HandleControlerAccountInfo(pData, nLen, *pDevice);
    }
        break;

    case CMD_AUDIO_COLLECTOR_STREAM_TCP:    //音频采集器数据流发送（TCP)
    {
        HandleAudioCollectorStream(pData, nLen, *pDevice);
    }
        break;
    #if SUPPORT_INTERCOM_DEVICE_CONFIG
    case CMD_INTERCOM_BASIC_CONFIG:        //查询/设置对讲终端基础参数
    {
        HandleIntercomBasicConfig(&pData[dataPos],uDataLen,*pDevice);
    }
        break;
    #endif

    case CMD_SET_TRIGGER_CONFIG:
    {
        HandleTriggerConfig(&pData[dataPos],uDataLen,*pDevice);
    }
    break;

    case CMD_REQUEST_TRIGGER_PLAY:
    {
        HandleTriggerRequestPlay(&pData[dataPos],uDataLen,*pDevice);
    }
    break;

    #if SUPPORT_AUDIO_MIXER
    case CMD_AUDIO_MIXER_CONFIG:          //查询/设置音频混音器参数
    {
        HandleAudioMixerConfig(&pData[dataPos],uDataLen,*pDevice);
    }
    break;
    case CMD_AUDIO_MIXER_STREAM:        //混音器音频流
    {
        HandleAudioMixerStream(pData,nLen,*pDevice);
    }
    break;
    case CMD_SET_AUDIO_MIXER_SOURCE:    //主机向混音器/终端设置混音音源(接收应答)
    {
        if(uDataLen>0)
            HandleSetAudioMixerSource(&pData[dataPos],uDataLen,*pDevice);
    }
    break;
    #endif

    case CMD_INFORMATION_PUBLISH_CONFIG:        //查询/设置信息发布参数
    {   
        HandleInformationPublishConfig(&pData[dataPos],uDataLen,*pDevice);
    }
    break;

    #if SUPPORT_PHONE_GATEWAY
    case CMD_PHONE_GATEWAY_CONFIG:          //查询/设置电话网关参数
    {
        HandlePhoneGatewayConfig(&pData[dataPos],uDataLen,*pDevice);
    }
    break;
    case CMD_SET_PHONE_GATEWAY_SOURCE:        //主机向电话网关/终端设置电话网关音源
    {
        HandleSetPhoneGatewaySource(pData,nLen,*pDevice);
    }
    break;
    case CMD_PHONE_GATEWAY_STREAM:    //电话网关音频流
    {
        if(uDataLen>0)
            HandlePhoneGatewayStream(pData,uDataLen,*pDevice);
    }
    break;
    #endif


    #if SUPPORT_AMP_CONTROLER
    case CMD_AMP_CONTROLER_STATUS:    //功放控制器状态
    {
        HandleAmpControlerStatus(&pData[dataPos],uDataLen,*pDevice);
    }
    break;
    #endif

    #if SUPPORT_NOISE_DETECTOR
    case CMD_NOISE_DETECTOR_CONFIG:        //查询/设置噪声检测器参数
    {
        HandleNoiseDetectorConfig(&pData[dataPos],uDataLen,*pDevice);
    }
    break;
    #endif

    case CMD_GPS_SYNC:        //GPS同步时间
    {
        HandleGpsSyncTime(&pData[dataPos],uDataLen,*pDevice);
    }
    break;


    case CMD_RECEIVE_JSON_COMMAND:  //终端向服务器发送JSON命令
    {
        HandleReceiveJsonCommand(pData,uDataLen,*pDevice);
    }
    break;

    case CMD_AUDIO_COLLECTOR_CONFIG:    //查询/设置音频采集器参数
    {
        HandleAudioCollectorConfig(&pData[dataPos],uDataLen,*pDevice);
    }
    break;

    case CMD_SEND_AUDIO_LIST:       //主机向其他控制设备下发音频采集器设备列表
    {
        if(devModel == MODEL_PAGER_A || devModel == MODEL_PAGER_B || devModel == MODEL_PAGER_C)
        {
            pDevice->SetFileDateTime(DT_AUDIO_COLLECTOR, g_Global.m_AudioCollectors.GetDateTime());
        }
    }
    break;

    #if APP_IS_LZY_LIMIT_STORAGE
    case CMD_GET_ACCOUNT_STORAGE_CAPACITY:
    {
        if(uDataLen>0)
            HandleGetAccountStorageCapacity(&pData[dataPos],uDataLen,*pDevice);
    }
    break;
    case CMD_REQUEST_UPLOAD_SONG_FILE:
    {
        if(uDataLen>0)
            HandleRequestUploadSong(&pData[dataPos],uDataLen,*pDevice);
    }
    break;
    case CMD_NOTIFY_UPLOAD_STATUS:
    {
        if(uDataLen>0)
            HandleUploadSongStatus(&pData[dataPos],uDataLen,*pDevice);
    }
    break;
    case CMD_REQUEST_DELETE_SONG:
    {
        if(uDataLen>0)
            HandleRequestDeleteSong(&pData[dataPos],uDataLen,*pDevice);
    }
    break;

    #endif

    case CMD_SET_BROADCAST_PAGING:
        if(uDataLen>0)
            HandleSetBroadcastPaging(&pData[dataPos],uDataLen,*pDevice);
    break;

    default:
        break;
    }

    //


}

// 处理分控设备需要转发的命令
void  CCommandHandle::HandleForwardControlDevice(unsigned short	 command,	 // 命令
                                                 const char*	 pData,		 // 命令缓冲区
                                                 int			 nLen,		 // 数据长度
                                                 CSection&       ctrlDevice) // 分控设备
{
    LOG(FORMAT("------------HandleForwardControlDevice %X", command), LV_INFO);

    if(g_Global.m_Network.m_CmdSend.CmdForwardControlDevice(pData, nLen, ctrlDevice))
    {
        if (command == CMD_DEVICE_VOLUME)		// 设置设备音量
        {
            g_Global.m_Network.m_CmdSend.CmdResponseSetVolume(ctrlDevice);
        }
        else if(command == CMD_SET_PLAY_STATUS) // 设置播放状态
        {
            g_Global.m_Network.m_CmdSend.CmdResponseSetPlayStatus(ctrlDevice);
        }
        else if (command == CMD_MUTE_STATUS)	// 设置静音状态
        {
            g_Global.m_Network.m_CmdSend.CmdResponseSetMuteStatus(ctrlDevice);
        }
        else if (command == CMD_CONTROL_MODE)	// 设置设备程控状态
        {
            g_Global.m_Network.m_CmdSend.CmdResponseSetControlMode(ctrlDevice);
        }
        else if (command == CMD_PLAY_LOCAL_SONG)// 点播歌曲（分布模式）
        {
            g_Global.m_Network.m_CmdSend.CmdResponsePlayLocalSong(ctrlDevice, 0x01);
        }
        else if (command == CMD_PLAY_RING)		// 播放钟声（分布模式）
        {
            g_Global.m_Network.m_CmdSend.CmdResponsePlayRing(ctrlDevice, 0x01);
        }
        else if (command == CMD_IDLE_STATUS)	// 设置空闲状态
        {
            g_Global.m_Network.m_CmdSend.CmdSetIdleStatus(ctrlDevice);	// 设置与回应的命令一样
        }
        else if (command == CMD_SET_AUDIO_INFO)	// 设置音频采集器信息
        {
            #if 0
            unsigned char	dataPos	= 8;
            unsigned char	srcID	= pData[dataPos+2];		// 音频采集器ID

            CSection *pAudioCol = g_Global.m_AudioCollectors.GetSectionBySrcID(srcID);

            if (pAudioCol != NULL)
            {
                // 给音频采集器pAudioCol设置信息，上面的CmdForwardControlDevice是转发到各个分区
                g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(*pAudioCol, pAudioCol->m_pAudioCollector);
            }

            g_Global.m_Network.m_CmdSend.CmdResponseSetAudioInfo(ctrlDevice);
            #endif
        }
        else if (command == CMD_NOTIFY_PAGING)	// 通知寻呼
        {
            g_Global.m_Network.m_CmdSend.CmdResponseNotifyPaging(ctrlDevice);

            char pager_event[64]={0};
            if(pData[8] == 0x08)    //开始寻呼
            {
                // 把选中的分区记下为寻呼列表
                ctrlDevice.m_pAudioForward->SetSections(ctrlDevice.m_pSelectedSections->GetMacs());
                sprintf(pager_event,"%s","开始寻呼");
#if SUPPORT_CALL_RECORD
                if(g_Global.m_bEnableCallRecord)
                {
                    // 开始录音
                    CMyString strCallId;
                    strCallId.Format("PAGING_%s_%ld", ctrlDevice.GetMac(), CTime::GetCurrentTimeT().GetTime());
                    
                    CMyString strCallerMac(ctrlDevice.GetMac());
                    CMyString strCallerName(ctrlDevice.GetName());
                    
                    // 获取被叫方列表
                    CMyString strCalleeList;
                    unsigned int uSecCount = ctrlDevice.m_pSelectedSections->GetCount();
                    for (unsigned int i = 0; i < uSecCount; ++i)
                    {
                        CSection* pSection = g_Global.m_Sections.GetSectionByMac(ctrlDevice.m_pSelectedSections->GetAt(i));
                        if (pSection != NULL)
                        {
                            if (i > 0)
                            {
                                strCalleeList += ",";
                            }
                            strCalleeList += pSection->GetMac();
                        }
                    }
                    
                    // 确定音频格式（根据网络模式）
                    AudioFormat audioFormat = AF_PCM;  // 默认为PCM
                    NetworkMode netMode = ctrlDevice.GetNetworkMode();
                    if (netMode == NETWORK_UDP)
                    {
                        audioFormat = AF_PCM;  // UDP模式使用PCM
                    }
                    else
                    {
                        audioFormat = AF_G722; // TCP模式使用G.722
                    }

                    // 查找该设备的录音会话
                    CMyString strCallIdPattern;
                    strCallIdPattern.Format("PAGING_%s_", ctrlDevice.GetMac());
                    // 获取匹配的录音会话
                    std::vector<CMyString> matchingSessions = g_Global.m_RecordManager.GetRecordSessionsByPrefix(strCallIdPattern);
                    bool bFoundSession = false;
                    
                    // 获取当前时间戳
                    unsigned long currentTime = g_Global.m_RecordManager.GetCurrentTimestamp();
                    
                    // 如果找到了会话，检查是否是同一个设备在短时间内的重复寻呼
                    if (!matchingSessions.empty())
                    {
                        // 获取第一个匹配的会话信息
                        RecordSession session;
                        if (g_Global.m_RecordManager.GetRecordSession(matchingSessions[0], session))
                        {
                            // 计算时间差
                            unsigned long timeDiff = currentTime - session.ulStartTime;
                            
                            // 如果时间差小于20毫秒，则认为是重复的寻呼请求，不处理
                            if (timeDiff <= 20)
                            {
                                LOG(FORMAT("忽略设备 %s 在20ms内的重复寻呼请求", 
                                        ctrlDevice.GetMac()), LV_INFO);
                                return;
                            }
                            else    //如果时间差大于20ms，需要将原来的录音停止
                            {
                                g_Global.m_RecordManager.StopRecordByMacPrefix(strCallIdPattern, RS_INTERRUPTED);
                            }
                        }
                    }

                    // 启动录音
                    g_Global.m_RecordManager.StartRecord(strCallId, strCallerMac, strCallerName,
                                                    strCalleeList, RT_BROADCAST_PAGING, audioFormat);
                    
                    LOG(FORMAT("Started recording for paging session: %s", strCallId.Data()), LV_INFO);
                }
#endif
            }
            else    //停止寻呼
            {
                sprintf(pager_event,"%s","停止寻呼");
#if SUPPORT_CALL_RECORD
                // 停止录音 - 查找该设备的录音会话
                CMyString strCallIdPattern;
                strCallIdPattern.Format("PAGING_%s_", ctrlDevice.GetMac());
                
                // 停止匹配的录音会话
                g_Global.m_RecordManager.StopRecordByMacPrefix(strCallIdPattern, RS_COMPLETED);
                
                //LOG(FORMAT("Stopped recording for paging device: %s", ctrlDevice.GetMac()), LV_INFO);
#endif
            }
            // 加到日志
            CMyString strLogContents;
            strLogContents.Format("%s:%s %s",  CProtocol::GetDescriptionProSource(PRO_PAGING).C_Str(),
                                            CProtocol::GetDescriptionDevice(ctrlDevice.GetDeviceModel()).C_Str(),pager_event);
            g_Global.m_logTable.InsertLog(	CMyString(ctrlDevice.GetMac()),
                                            CMyString(ctrlDevice.GetName()),
                                            LT_PAGING_LOG,
                                            strLogContents);
        }
    }
}

// 处理寻呼设备需要转发的命令
void  CCommandHandle::HandleForwardPagingDevice(unsigned short	command,		// 命令
                                                const char*     pData,			// 命令缓冲区
                                                int             nLen,			// 数据长度
                                                CSection&       pagingDevice)	// 寻呼设备
{
    if (g_Global.m_Network.m_CmdSend.CmdForwardPagingDevice(command, pData, nLen, pagingDevice))
    {
        if (command == CMD_PAGING_AGAIN)		// 再次寻呼，要放在寻呼列表里处理
        {
            printf("response CMD_PAGING_AGAIN\n");
            g_Global.m_Network.m_CmdSend.CmdResponsePagingAgain(pagingDevice);
        }
        else if (command == CMD_PAGING_STREAM)	// 寻呼音频流，不用回应
        {
#if SUPPORT_CALL_RECORD
            if(!g_Global.m_bEnableCallRecord)
            {
                return;
            }
            //加入录音音频流,pData+6开始为实际的音频流，如果pagingDevice的网络模式为UDP模式，那么音频编码为PCM编码
            //如果网络模式为TCP，那么音频编码为G.722
            //32K采样率，16bit，单声道
            
            // 检查数据长度
            if (nLen <= 256)
            {
                //LOG("Invalid paging stream data length", LV_WARNING);
                return;
            }
            
            // 提取音频数据（从第6个字节开始）
            const unsigned char* pAudioData = (const unsigned char*)(pData + 8 + 6 );
            int nAudioLength = nLen - 9 - 6 ;
            
            // 查找该设备的录音会话
            CMyString strCallIdPattern;
            strCallIdPattern.Format("PAGING_%s_", pagingDevice.GetMac());
            
            // 获取匹配的录音会话
            std::vector<CMyString> matchingSessions = g_Global.m_RecordManager.GetRecordSessionsByPrefix(strCallIdPattern);
            bool bFoundSession = false;
            
            // 为每个匹配的会话添加音频数据
            for (const CMyString& strCallId : matchingSessions)
            {
                if (g_Global.m_RecordManager.AddAudioData(strCallId, pAudioData, nAudioLength))
                {
                    bFoundSession = true;
                }
            }
            
            if (!bFoundSession && matchingSessions.size() == 0)
            {
                LOG(FORMAT("No active recording session found for paging device: %s", pagingDevice.GetMac()), LV_DEBUG);
            }
#endif
        }
    }
}

CSection*  CCommandHandle::HandleSearchSectionDevice(char* szName, char* szMac, unsigned char vol, ProgramSource src, char* szProName, PlayStatus playStatus,
                                                     bool isTimerInvalid, char* szVersion, const char* szIP, DeviceModel	nModel, bool& isNewOnline, BYTE nReservedWord, BYTE szDeviceFeature,
                                                     unsigned char module4G_signal_rssi,char* module4G_iccid,char *loginUser,
                                                     bool audioMixerSingalValid,bool audioCollectorSignalValid,bool phoneGatewaySingalValid,
                                                     int nExtraFeature)
{
    CSection	*pDevice	= NULL;
    ctime_t		tNow		= CTime::GetCurrentTimeT().GetTime();
    unsigned char		nameLen		= strlen(szName);

    char *defaultUserName=(char *)SUPER_USER_NAME;

    //判断有没有重复的非分区设备存在?如存在，需要删除
    for (int i=DEVICE_PAGER; i<DEVICE_TYPE_COUNT; ++i)
    {
        CSections* pDevices = g_Global.m_pAllDevices[i];
        if( pDevices->GetSectionByMac(szMac) != NULL )
        {
            pDevices->RemoveSpecDevice(szMac);
        }
    }

    CSection *pSection = g_Global.m_Sections.GetSectionByMac(szMac);
    
    // 新添加的分区
    if (pSection == NULL)
    {
        QMutexLocker locker(&command_HandleSearch_Mutex);

        pSection = g_Global.m_Sections.GetSectionByMac(szMac);
        if (pSection != NULL)
        {
            return pSection;
        }
        #if 0   //20220424只在WEB应答时限制  
        int nMaxSectionCount = MAX_SECTION_COUNT;
        #else
        int nMaxSectionCount = MAX_SECTION_COUNT_FORMAL;
        #endif

        if (g_Global.m_Sections.GetSecCount() >= nMaxSectionCount) // 如果已超过分区最大数
        {
            return NULL;
        }

        //CSection newSection(g_Global.m_Sections.GetSecCount() + 1, nModel, szMac, szIP, vol, src, nameLen == 0 ? szIP : szName);
        CSection* pNewSection = new CSection(g_Global.m_Sections.GetSecCount() + 1, nModel, szMac, szIP, vol, src, nameLen == 0 ? szIP : szName);

        pNewSection->SetVersion(szVersion);
        pNewSection->SetLatestTime(tNow);
        pNewSection->SetProName(szProName);
        pNewSection->SetTimerInvalid(isTimerInvalid);
        pNewSection->SetPlayStatus(playStatus);
        pNewSection->SetDeviceFeature(szDeviceFeature);
    
        pNewSection->SetModule4GCSQ_Rssi(module4G_signal_rssi);
        pNewSection->SetModule4G_ICCID(module4G_iccid);

        pNewSection->SetDeviceExtraFeature(nExtraFeature);

        if(loginUser == NULL)
        {
            loginUser=defaultUserName;
        }
        pNewSection->SetUserAccount(loginUser);

        pNewSection->SetReservedWord(nReservedWord);  // 保留字

        // 到主线程去更新，否则调用滚动条相关的函数会出错
        if (g_Global.m_Sections.AddSection(*pNewSection))	// 加入到内存
        {
            // 加入到文件
            //g_Global.WriteXmlFile(FILE_SECTION);
            string secMac=pNewSection->GetMac();
            g_Global.m_Sections.PushWriteXmlMacTask(secMac);

            // 回调函数 保留，待修改
            //g_Global.m_MsgHandle.OnMsgHandle(UM_DEVICE_ADD, (WPARAM)(pNewSection), nModel);

            // 分区设备新上线，更新账户信息（里面包含了绑定分区数量、mac等等）
            //g_Global.m_WebNetwork.ForwardUserInfo(SUPER_USER_NAME, UD_USER);

            //******** 新分区上线时除发送XML列表改变信息给WEB端外，也发送单个分区状态给WEB端
            g_Global.m_WebNetwork.ForwardSectionInfoToWeb(DEVICE_SECTION, pNewSection, NULL,TRUE);
        }

        pDevice = &g_Global.m_Sections.GetSection(g_Global.m_Sections.GetSecCount() - 1);
        isNewOnline = TRUE;

        delete pNewSection;
        locker.unlock();
    }
    // 后面上线的分区
    else 
    {
        pthread_mutex_lock(&pSection->sectionChangeMutex);

        pSection->SetDeviceExtraFeature(nExtraFeature);

        if(!pSection->IsRestarting(tNow))
        {
            pSection->SetLatestTime(tNow);

            bool bNeedUpdate	= FALSE;
            bool bWriteToFile	= FALSE;
            

            NetworkMode netMode=NetworkMode(nReservedWord>>4&0x03);
            if( pSection->GetNetworkMode() != netMode )                    //网络模式
            {
                bWriteToFile = TRUE;
                bNeedUpdate = TRUE;
            }
            pSection->SetReservedWord(nReservedWord);  // 保留字

            if (strcmp(szProName, pSection->GetProName()) != 0)
            {
                pSection->SetProName(szProName);
                bNeedUpdate = TRUE;
            }

            if (pSection->GetVolume() != vol)
            {
                pSection->SetVolume(vol);
                bNeedUpdate = TRUE;
            }

            if(pSection->GetDeviceFeature() != szDeviceFeature)
            {
                pSection->SetDeviceFeature(szDeviceFeature);
                bNeedUpdate = TRUE;
            }

            if (pSection->GetPlayStatus() != playStatus)
            {
                pSection->SetPlayStatus(playStatus);
                bNeedUpdate = TRUE;

                // 暂停后恢复播放   CS 2019-4-29 （暂停播放功能）
                if(playStatus == PS_PLAY)
                {
                    CSongUnit *pSongUnit = g_Global.m_PlayQueue.m_SongPlayer.GetSongUnitByPlayID(pSection->GetPlayID());
                    if(pSongUnit != NULL && pSongUnit->GetPlayStatus() == SPS_PAUSE_AUTO)
                    {
                        g_Global.m_PlayQueue.m_SongPlayer.SetPlayStatus(pSection->GetPlayID(), SPS_PLAY);         // 让音频流恢复发送
                    }
                }
                else if(playStatus == PS_PAUSE)
                {
                    //如果分区是暂停状态，但是该分区并没有播放任务（具体点的话就是没有手动任务），那么恢复状态，可能是异常错误（比如暂停后服务器中途重启）
                    if(pSection->GetPlayID() == -1 && pSection->GetPrePlayID() == -1 && pSection->m_PlayedRecently.m_nPlayID == -1)
                    {
                        if(src == PRO_LOCAL_PLAY)
                        {
                            g_Global.m_Network.m_CmdSend.CmdSetIdleStatus(*pSection);
                        }
                    }
                }
            }
            if (pSection->IsTimerInvalid() != isTimerInvalid)
            {
                pSection->SetTimerInvalid(isTimerInvalid);

                // 转发给分控设备
                //m_pNetwork->ForwardSectionsStatusToControlDevices(pSection, NULL);
                bNeedUpdate = TRUE;
            }
            if (pSection->GetDeviceModel() != nModel)
            {
                pSection->SetDeviceModel(nModel);
                bWriteToFile = TRUE;
            }
            if (nameLen != 0 && strcmp(szName, pSection->GetName()) != 0)
            {
                pSection->SetName(szName);
                // 回调函数 保留，待修改
                //g_Global.m_MsgHandle.OnMsgHandle(UM_DEVICE_RENAME, (WPARAM)(pSection));
                bWriteToFile	= TRUE;
                bNeedUpdate		= TRUE;
            }
            else if(nameLen == 0 && strcmp(szIP, pSection->GetName()) != 0)   //分区别名长度为0，即没有分区别名的情况下,设置IP为别名
            {
                pSection->SetName(szIP);

                bWriteToFile	= TRUE;
                bNeedUpdate		= TRUE;
            }
            if (strcmp(szIP, pSection->GetIP()) != 0)
            {
                //20230315 TCP模式下也更新内存IP信息，但是不更新XML文件
                //if( !(pSection->GetNetworkMode() == NETWORK_TCP && pSection->m_kcpsocket) )            //已连接TCP的情况下不改变IP，避免内网设备采用TCP连接IP地址变来变去的问题
                {
                    // IP与分区名称一样
                    if (strcmp(pSection->GetName(), pSection->GetIP()) == 0)
                    {
                        pSection->SetName(szIP);

                        if (nameLen != 0)	// nameLen=0表示重命名成功，而不是用IP作为名称
                        {
                            // 回调函数 保留，待修改
                            //g_Global.m_MsgHandle.OnMsgHandle(UM_DEVICE_RENAME, (WPARAM)(pSection));
                        }
                    }

                    if(pSection->GetNetworkMode() != NETWORK_TCP || isIPLAN(szIP) || isIPLAN(pSection->GetIP()))
                    {
                        bWriteToFile = TRUE;
                    }
                    g_Global.m_Sections.UpdateIp(*pSection,szIP);	// IP改变，更新与IP与ID对应关系

                    bNeedUpdate		= TRUE;
                }
            }
            if (strcmp(szVersion, pSection->GetVersion()) != 0)
            {
                pSection->SetVersion(szVersion);
                bNeedUpdate	= TRUE;
            }
            if (pSection->GetProSource() != src)
            {
                // 原为下线
                if (pSection->GetProSource() == PRO_OFFLINE)
                {
                    HandleOnlineResumePlaying(pSection, tNow, nReservedWord);

                    pSection->SetProSource(src, !pSection->m_PlayedRecently.m_isWorkPatternDiff);

                    g_Global.m_Sections.UpdateIp(*pSection,szIP);	// 分区上线，更新IP与ID对应关系，有些离线的分区IP与上线的分区IP一样

                    // 离线到上线，转告分控设备
                    //m_pNetwork->ForwardSectionsStatusToControlDevices(pSection, NULL);

                    // 回调函数 保留，待修改
                    //g_Global.m_MsgHandle.OnMsgHandle(UM_DEVICE_ONLINE, (WPARAM)(pSection));

                    isNewOnline = TRUE;
                    bNeedUpdate = TRUE;	 // 上线的话，进行上线处理即可，不需要再更新分区

                }
                else // 音源改变
                {
                    SectionChangedSource(*pSection, src);
                    bNeedUpdate = TRUE;
                }
            }

            if(pSection->GetModule4GCSQ_Rssi()!=module4G_signal_rssi)
            {
                //等级变化了才刷新
                if(CSection::GetModule4GCSQ_LevelByRssi(pSection->GetModule4GCSQ_Rssi())!=CSection::GetModule4GCSQ_LevelByRssi(module4G_signal_rssi))
                {
                    bNeedUpdate = TRUE;
                }
                pSection->SetModule4GCSQ_Rssi(module4G_signal_rssi);
            }

            if(strcmp(pSection->GetModule4G_ICCID(),module4G_iccid))
            {
                if(strlen(module4G_iccid)>0)
                {
                    bNeedUpdate = TRUE;
                    bWriteToFile = TRUE;
                    pSection->SetModule4G_ICCID(module4G_iccid);

                    CMyString strLogContents;
                    strLogContents.Format("ICCID:%s",  module4G_iccid);
                    
                    g_Global.m_logTable.InsertLog(CMyString(pSection->GetMac()),
                                                CMyString(pSection->GetName()),
                                                LT_RUNNING_STATE,
                                                strLogContents);
                }
            }

            //loginUser为空且当前设备的用户账户为空，才更新
            bool userChangeFlag=false;
            if(loginUser == NULL)
            {
                loginUser=defaultUserName;
                if(strlen(pSection->GetUserAccount().data()) == 0)
                {
                    pSection->SetUserAccount(loginUser);
                    userChangeFlag=true;
                }
            }
            else
            {
                if(strcmp(pSection->GetUserAccount().data(),loginUser))
                {
                    pSection->SetUserAccount(loginUser);
                    userChangeFlag=true;
                }
            }

            // 改变，需要写入文件
            if (bWriteToFile)
            {
                //g_Global.WriteXmlFile(FILE_SECTION);
                string secMac=pSection->GetMac();
                g_Global.m_Sections.PushWriteXmlMacTask(secMac);
            }

            if (bNeedUpdate)
            {
                #if 1
                g_Global.m_Sections.AddVarySection(pSection->GetMac(),pSection->GetID());
                #else
                g_Global.m_WebNetwork.ForwardSectionInfoToWeb(DEVICE_SECTION, pSection, NULL, FALSE);   //TCP发送一次即可
                m_pNetwork->ForwardSectionsStatusToControlDevices(pSection, NULL);
                #endif
            }
            else
            {
                pSection->m_nStatusSameCount++;
            }

            pDevice = pSection;
        }

        pthread_mutex_unlock(&pSection->sectionChangeMutex);
    }

    return pDevice;
}

// 处理查找其它设备
CSection*   CCommandHandle::HandleSearchOtherDevice(char* szName, char* szMac, unsigned char vol, ProgramSource src, char* szProName, PlayStatus playStatus,
                                                    bool isTimerInvalid, char* szVersion, const char* szIP, DeviceModel	nModel, bool& isNewOnline,BYTE nReservedWord, BYTE szDeviceFeature,
                                                    unsigned char module4G_signal_rssi,char* module4G_iccid,char *loginUser,bool audioMixerSingalValid,bool audioCollectorSignalValid,bool phoneGatewaySingalValid,
                                                    int nExtraFeature)
{
    CSection	*pDevice	= NULL;
    ctime_t		tNow		= CTime::GetCurrentTimeT().GetTime();
    unsigned char nameLen	= strlen(szName);

    //如果不存在这个类型的设备，返回
    if(g_Global.m_ModelToDevices.count(nModel) == 0)
    {
        return NULL;
    }

    char *defaultUserName=(char *)SUPER_USER_NAME;

    CSections *pDevices		= g_Global.m_ModelToDevices[nModel];
    CSection  *pOtherDevice	= pDevices->GetSectionByMac(szMac);
    bool	   bWriteToFile = FALSE;
    bool        bNeedUpdate = FALSE;

    //判断有没有重复的其他类型设备存在?如存在，需要删除
    for (int i=DEVICE_SECTION; i<DEVICE_TYPE_COUNT; ++i)
    {
        CSections* tmp_pDevices = g_Global.m_pAllDevices[i];
        if(tmp_pDevices == pDevices)
        {
            continue;
        }
        if( tmp_pDevices->GetSectionByMac(szMac) != NULL )
        {
            tmp_pDevices->RemoveSpecDevice(szMac);
        }
    }


    bool       hasFileModel = CSections::IsControlDevice(nModel) || CSections::IsAudioCollectorDevice(nModel) || CSections::IsFireCollectorDevice(nModel)\
                                || CSections::IsSequencePowerDevice(nModel) || CSections::IsRemoteControlerDevice(nModel) || CSections::IsAudioMixerDevice(nModel)\
                                || CSections::IsPhoneGatewayDevice(nModel) || CSections::IsAmpControlerDevice(nModel) || CSections::IsNoiseDetectorDevice(nModel)\
                                || CSections::IsGpsDevice(nModel);

    if (pOtherDevice == NULL)// 之前没有上过线
    {
        QMutexLocker locker(&command_HandleSearch_Mutex);
        #if 0 //20210929 去除对分区设备与其他设备MAC冲突的判断
        pOtherDevice = g_Global.m_Sections.GetSectionByMac(szMac);
        if (pOtherDevice != NULL)
        {
            return pOtherDevice;
        }
        #endif
         #if 1
        //如果音频采集器数量达到10个，则删除所有离线设备
        if(CSections::IsAudioCollectorDevice(nModel) && pDevices->GetSecCount() == AUDIO_COLLECTOR_MAX)
        {
            pDevices->RemoveOfflineSections();
            #if 0
            //如果删除离线采集器后还是数量还是达到限制，那么不加入新的设备
            if(pDevices->GetSecCount() == AUDIO_COLLECTOR_MAX)
            {
                return NULL;
            }
            #endif
        }
        #endif
        CSection* pNewDevice = new CSection(pDevices->GetSecCount() + 1, nModel, szMac, szIP, vol, src, nameLen == 0 ? szIP : szName);

        pNewDevice->SetVersion(szVersion);
        pNewDevice->SetLatestTime(tNow);

        pNewDevice->SetDeviceFeature(szDeviceFeature);

        pNewDevice->SetModule4GCSQ_Rssi(module4G_signal_rssi);
        pNewDevice->SetModule4G_ICCID(module4G_iccid);

        pNewDevice->SetReservedWord(nReservedWord);  // 保留字
        pNewDevice->SetDeviceExtraFeature(nExtraFeature);

        if(loginUser == NULL)
        {
            loginUser=defaultUserName;
        }
        pNewDevice->SetUserAccount(loginUser);

        pDevices->AddSection(*pNewDevice);

        pDevice = &pDevices->GetSection(pDevices->GetSecCount() - 1);
        isNewOnline = TRUE;

        // 新上线
        bWriteToFile = hasFileModel;
        bNeedUpdate = TRUE;

        delete pNewDevice;
        locker.unlock();
    }
    else
    {
        pOtherDevice->SetDeviceExtraFeature(nExtraFeature);
        if(!pOtherDevice->IsRestarting(tNow))
        {
            pOtherDevice->SetLatestTime(tNow);
            
            pOtherDevice->SetVolume(vol);

            NetworkMode netMode=NetworkMode(nReservedWord>>4&0x03);
            if( pOtherDevice->GetNetworkMode() != netMode )                    //网络模式
            {
                bNeedUpdate = TRUE;
            }
            pOtherDevice->SetReservedWord(nReservedWord);  // 保留字

            if (pOtherDevice->GetDeviceModel() != nModel)
            {
                pOtherDevice->SetDeviceModel(nModel);
                bWriteToFile = TRUE;
            }

            // 名称改变
            if (nameLen != 0 && strcmp(szName, pOtherDevice->GetName()) != 0)
            {
                pOtherDevice->SetName(szName);

                bWriteToFile = hasFileModel;
                bNeedUpdate = TRUE;
            }
            else if(nameLen == 0 && strcmp(szIP, pOtherDevice->GetName()) != 0)   //分区别名长度为0，即没有分区别名的情况下,设置IP为别名
            {
                pOtherDevice->SetName(szIP);

                bWriteToFile	= hasFileModel;
                bNeedUpdate		= TRUE;
            }
            if (strcmp(szIP, pOtherDevice->GetIP()) != 0)
            {
                // IP与分区名称一样
                if (strcmp(pOtherDevice->GetName(), pOtherDevice->GetIP()) == 0)
                {
                    pOtherDevice->SetName(szIP);
                }

                if(pOtherDevice->GetNetworkMode() != NETWORK_TCP || isIPLAN(szIP) || isIPLAN(pOtherDevice->GetIP()))
                {
                    bWriteToFile = hasFileModel;
                }
                pDevices->UpdateIp(*pOtherDevice,szIP);	// IP改变，更新与IP与ID对应关系

                bNeedUpdate		= TRUE;
            }

            if (pOtherDevice->GetProSource() != src)
            {
                // 原为下线
                if (pOtherDevice->GetProSource() == PRO_OFFLINE)
                {
                    isNewOnline = TRUE;

                    pDevices->UpdateIp(*pOtherDevice,szIP);	// 设备上线，更新IP与ID对应关系，有些离线的设备IP与上线的设备IP一样
                }
                pOtherDevice->SetProSource(src);
                bNeedUpdate = TRUE;
            }
            if (strcmp(szVersion, pOtherDevice->GetVersion()) != 0)
            {
                pOtherDevice->SetVersion(szVersion);
                bNeedUpdate = TRUE;
            }

            if(pOtherDevice->GetModule4GCSQ_Rssi()!=module4G_signal_rssi)
            {
                //等级变化了才刷新
                if(CSection::GetModule4GCSQ_LevelByRssi(pOtherDevice->GetModule4GCSQ_Rssi())!=CSection::GetModule4GCSQ_LevelByRssi(module4G_signal_rssi))
                {
                    bNeedUpdate = TRUE;
                }
                pOtherDevice->SetModule4GCSQ_Rssi(module4G_signal_rssi);
            }

            if(strcmp(pOtherDevice->GetModule4G_ICCID(),module4G_iccid))
            {
                if(strlen(module4G_iccid)>0)
                {
                    bNeedUpdate = TRUE;
                    pOtherDevice->SetModule4G_ICCID(module4G_iccid);

                    CMyString strLogContents;
                    strLogContents.Format("ICCID:%s",  module4G_iccid);
                    
                    g_Global.m_logTable.InsertLog(CMyString(pOtherDevice->GetMac()),
                                                CMyString(pOtherDevice->GetName()),
                                                LT_RUNNING_STATE,
                                                strLogContents);
                }
            }

            pOtherDevice->SetDeviceFeature(szDeviceFeature);

            //loginUser为空且当前设备的用户账户为空，才更新
            bool userChangeFlag=false;
            if(loginUser == NULL)
            {
                loginUser=defaultUserName;
                if(strlen(pOtherDevice->GetUserAccount().data()) == 0)
                {
                    pOtherDevice->SetUserAccount(loginUser);
                    userChangeFlag=true;
                }
            }
            else
            {
                if(strcmp(pOtherDevice->GetUserAccount().data(),loginUser))
                {
                    pOtherDevice->SetUserAccount(loginUser);
                    userChangeFlag=true;
                }
            }
            if(userChangeFlag && CSections::IsControlDevice(nModel))
            {
                //如果登录用户有效，发送属于该用户的所有分区信息
                if(strlen(loginUser)>0)
                {
                    //加入日志
                    CMyString strContents;
                    strContents.Format("%s:%s", loginUser,"user_login");

                    g_Global.m_logTable.InsertLog(CMyString(pOtherDevice->GetMac()),
                                            CMyString(pOtherDevice->GetName()),
                                            LT_RUNNING_STATE,
                                            strContents);
                
                    //收到控制设备的登录信息后，先发送一次该账户的拥有的所有分区信息给该控制设备
                    g_Global.m_Network.ForwardSectionsStatusToControlDevices(NULL,pOtherDevice);
                    
                    //龙之音V1版本，在收到寻呼台账户登录变更后，先重置设备的DateTime，以便服务器重新发送相关列表文件。
                    //******** 暂时只需要重置playlist即可，因为其他文件还是所有账户共用
                    #if APP_IS_LZY_LIMIT_STORAGE
                    //pOtherDevice->ResetFileDateTime();
                    pOtherDevice->SetFileDateTime(DT_PLAYLIST,"");
                    #endif

                    #if SUPPORT_USER_SECTION_XML
                    pOtherDevice->SetFileDateTime(DT_SECTION,"");
                    #endif
                }
            }

            #if SUPPORT_AUDIO_MIXER
            if (CSections::IsAudioMixerDevice(nModel))
            {
                if(pOtherDevice->m_pAudioMixer->IsAudioMixerSingalValid()!=audioMixerSingalValid)
                {
                    pOtherDevice->m_pAudioMixer->SetAudioMixerSingalValid(audioMixerSingalValid);
                    if(audioMixerSingalValid)
                    {
                        //有信号，如果混音主开关开启，那么通知相关绑定的设备
                        if(pOtherDevice->m_pAudioMixer->GetMasterSwitch())
                        {
                            printf("AudioMixer:%s,valid...\n",pOtherDevice->GetMac());
                            //先通知混音器本身
                            g_Global.m_Network.m_CmdSend.CmdSetAudioMixerSourceInfo(*pOtherDevice,*pOtherDevice,AUDIO_MIXER_EVENT_START);
                            //通知绑定的分区开始
                            for(int i=0;i<g_Global.m_Sections.GetSecCount();i++)
                            {
                                CSection &Section = g_Global.m_Sections.GetSection(i);
                                if(Section.IsOnline() && (Section.GetDeviceModel() == MODEL_IP_SPEAKER_D || Section.GetDeviceModel() == MODEL_IP_SPEAKER_E || Section.GetDeviceModel() == MODEL_IP_SPEAKER_F\
                                    || Section.GetDeviceModel() == MODEL_IP_SPEAKER_G) )
                                {
                                    if(pOtherDevice->m_pAudioMixer->HasSectionMac(Section.GetMac()))
                                    {
                                        g_Global.m_Network.m_CmdSend.CmdSetAudioMixerSourceInfo(Section,*pOtherDevice,AUDIO_MIXER_EVENT_START);
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        printf("AudioMixer:%s,inValid...\n",pOtherDevice->GetMac());
                        //现在没信号了,通知绑定的分区停止
                        for(int i=0;i<g_Global.m_Sections.GetSecCount();i++)
                        {
                            CSection &Section = g_Global.m_Sections.GetSection(i);
                            if(Section.IsOnline() && (Section.GetDeviceModel() == MODEL_IP_SPEAKER_D || Section.GetDeviceModel() == MODEL_IP_SPEAKER_E || Section.GetDeviceModel() == MODEL_IP_SPEAKER_F\
                                || Section.GetDeviceModel() == MODEL_IP_SPEAKER_G) )
                            {
                                if(pOtherDevice->m_pAudioMixer->HasSectionMac(Section.GetMac()))
                                {
                                    g_Global.m_Network.m_CmdSend.CmdSetAudioMixerSourceInfo(Section,*pOtherDevice,AUDIO_MIXER_EVENT_STOP);
                                }
                            }
                        }
                    }
                }
            }
            #endif


            if (CSections::IsAudioCollectorDevice(nModel))
            {
                if(pOtherDevice->m_pAudioCollector->IsAudioCollectorSingalValid()!=audioCollectorSignalValid)
                {
                    pOtherDevice->m_pAudioCollector->SetAudioCollectorSingalValid(audioCollectorSignalValid);
                    if(audioCollectorSignalValid)
                    {
                        //有信号，如果混音主开关开启，那么通知相关绑定的设备
                        if(pOtherDevice->m_pAudioCollector->GetTriggerSwitch())
                        {
                            printf("AudioCollector:%s,valid...\n",pOtherDevice->GetMac());
                            //先通知采集器本身
                            g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(*pOtherDevice, pOtherDevice->m_pAudioCollector,pOtherDevice->m_pAudioCollector->GetTriggerChannelId(),0);
                            //通知绑定的分区开始
                            for(int i=0;i<g_Global.m_Sections.GetSecCount();i++)
                            {
                                CSection &Section = g_Global.m_Sections.GetSection(i);
                                if(Section.IsOnline())
                                {
                                    if(pOtherDevice->m_pAudioCollector->HasSectionMac(Section.GetMac()))
                                    {
                                        BYTE acSrc=pOtherDevice->m_pAudioCollector->GetSourceID();
                                        if (g_Global.m_PlayQueue.GetProSourcePriority(acSrc) >= g_Global.m_PlayQueue.GetProSourcePriority(Section.GetProSource()))
                                        {
                                            g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(Section,pOtherDevice->m_pAudioCollector,pOtherDevice->m_pAudioCollector->GetTriggerChannelId(),
                                                0,false,0xff,true,pOtherDevice->m_pAudioCollector->GetTriggerVolume());
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        printf("AudioCollector:%s,inValid...\n",pOtherDevice->GetMac());
                        //现在没信号了,通知绑定的分区停止
                        for(int i=0;i<g_Global.m_Sections.GetSecCount();i++)
                        {
                            CSection &Section = g_Global.m_Sections.GetSection(i);
                            if(Section.IsOnline())
                            {
                                if(pOtherDevice->m_pAudioCollector->HasSectionMac(Section.GetMac()))
                                {
                                    if(Section.GetProSource() == pOtherDevice->m_pAudioCollector->GetSourceID()+pOtherDevice->m_pAudioCollector->GetTriggerChannelId()-1)
                                    {
                                        //停止，重置最近音源
                                        Section.m_PlayedRecently.m_nRecentSrc = PRO_IDLE;
                                        Section.m_PlayedRecently.m_bSongTimerEndResumeAc = PRO_IDLE;

                                        //判断该分区还有没有其他播放？
                                        bool canStopPlay=true;
                                        int playID=Section.GetPlayID();
                                        if(playID>0)
                                        {
                                            CSongUnit *pSongUnit = g_Global.m_PlayQueue.m_SongPlayer.GetSongUnitByPlayID(playID);
                                            if(pSongUnit != NULL)
                                            {
                                                if(pSongUnit->GetPlayStatus() == SPS_PAUSE_AUTO)
                                                {
                                                    g_Global.m_PlayQueue.m_SongPlayer.SetPlayStatus(playID, SPS_PLAY);         // 让音频流恢复发送
                                                }
                                                canStopPlay=false;
                                            }
                                        }

                                        if(canStopPlay)
                                        {
                                            //设置分区停止
                                            g_Global.m_Network.m_CmdSend.CmdSetIdleStatus(Section);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }


            #if SUPPORT_PHONE_GATEWAY
            if (CSections::IsPhoneGatewayDevice(nModel))
            {
                if(pOtherDevice->m_pPhoneGateway->IsPhoneGatewaySingalValid()!=phoneGatewaySingalValid)
                {
                    pOtherDevice->m_pPhoneGateway->SetPhoneGatewaySingalValid(phoneGatewaySingalValid);
                    if(phoneGatewaySingalValid)
                    {
                        //有信号，如果混音主开关开启，那么通知相关绑定的设备
                        if(pOtherDevice->m_pPhoneGateway->GetMasterSwitch())
                        {
                            printf("PhoneGateway:%s,valid...\n",pOtherDevice->GetMac());
                            //先通知混音器本身
                            g_Global.m_Network.m_CmdSend.CmdSetPhoneGatewaySourceInfo(*pOtherDevice,*pOtherDevice,AUDIO_PHONE_GATEWAY_EVENT_START);
                            //通知绑定的分区开始
                            for(int i=0;i<g_Global.m_Sections.GetSecCount();i++)
                            {
                                CSection &Section = g_Global.m_Sections.GetSection(i);
                                if(Section.IsOnline() && (Section.GetDeviceModel() == MODEL_IP_SPEAKER_D || Section.GetDeviceModel() == MODEL_IP_SPEAKER_E || Section.GetDeviceModel() == MODEL_IP_SPEAKER_F\
                                    || Section.GetDeviceModel() == MODEL_IP_SPEAKER_G) )
                                {
                                    if(pOtherDevice->m_pPhoneGateway->HasSectionMac(Section.GetMac()))
                                    {
                                        g_Global.m_Network.m_CmdSend.CmdSetPhoneGatewaySourceInfo(Section,*pOtherDevice,AUDIO_PHONE_GATEWAY_EVENT_START);
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        printf("PhoneGateway:%s,inValid...\n",pOtherDevice->GetMac());
                        //现在没信号了,通知绑定的分区停止
                        for(int i=0;i<g_Global.m_Sections.GetSecCount();i++)
                        {
                            CSection &Section = g_Global.m_Sections.GetSection(i);
                            if(Section.IsOnline() && (Section.GetDeviceModel() == MODEL_IP_SPEAKER_D || Section.GetDeviceModel() == MODEL_IP_SPEAKER_E || Section.GetDeviceModel() == MODEL_IP_SPEAKER_F\
                                || Section.GetDeviceModel() == MODEL_IP_SPEAKER_G) )
                            {
                                if(pOtherDevice->m_pPhoneGateway->HasSectionMac(Section.GetMac()))
                                {
                                    g_Global.m_Network.m_CmdSend.CmdSetPhoneGatewaySourceInfo(Section,*pOtherDevice,AUDIO_PHONE_GATEWAY_EVENT_STOP);
                                }
                            }
                        }
                    }
                }
            }
            #endif





            pDevice = pOtherDevice;
        }
    }

    // 更新文件
    if (bWriteToFile)
    {
        pDevices->WriteFile(TRUE);

        FileType ft = FILE_SECTION;
        if(CSections::IsAudioCollectorDevice(nModel))     ft = FILE_AUDIO_COLLECTOR;
        if(CSections::IsFireCollectorDevice(nModel))      ft = FILE_FIRE_COLLECTOR;
        if(CSections::IsSequencePowerDevice(nModel))      ft = FILE_SEQUENCE_POWER;
        if(CSections::IsRemoteControlerDevice(nModel))    ft = FILE_REMOTE_CONTROLER;
        if(CSections::IsControlDevice(nModel))            ft = FILE_PAGER;
        #if SUPPORT_AUDIO_MIXER
        if(CSections::IsAudioMixerDevice(nModel))         ft = FILE_AUDIO_MIXER;
        #endif
        #if SUPPORT_PHONE_GATEWAY
        if(CSections::IsPhoneGatewayDevice(nModel))       ft = FILE_PHONE_GATEWAY;
        #endif
        #if SUPPORT_AMP_CONTROLER
        if(CSections::IsAmpControlerDevice(nModel))       ft = FILE_AMP_CONTROLER;
        #endif
        #if SUPPORT_NOISE_DETECTOR
        if(CSections::IsNoiseDetectorDevice(nModel))      ft = FILE_NOISE_DETECTOR;
        #endif
        if(CSections::IsGpsDevice(nModel))                ft = FILE_GPS;

        if(ft != FILE_SECTION && ft != FILE_PAGER)
        {
            g_Global.m_WebNetwork.ForwardUpdateFileInfo(ft);
        }
    }

    if(bNeedUpdate)
    {
        // 加入web回调通知分区更新，保留,待处理
        g_Global.m_WebNetwork.ForwardSectionInfoToWeb(pDevice->GetDevType(), pDevice, NULL);
 #if SUPPORT_PAGER_CALL
        if(nModel == MODEL_PAGER_A || nModel == MODEL_PAGER_B || nModel == MODEL_PAGER_C)
        {
            g_Global.m_Network.m_CmdSend.CmdForwardPagerStatus(NULL,pDevice);
        }
#endif
    }

    return pDevice;
}


// 处理搜索在线设备
void CCommandHandle::HandleSearchOnlineDevice(const char*		pData,		// 数据区
                                              unsigned short    nLen,		// 数据长度
                                              const char*       szIP,		// IP地址
                                              DeviceModel       nModel,		// 设备型号
                                              unsigned char     nReserved,	// 保留字
                                              LPS_SOCKET_OBJ    sockobj)	// 为NULL时是UDP，否则为TCP
{
    char			szName[SEC_NAME_LEN+1]		= {0};
    char			szMac[SEC_MAC_LEN]			= {0};
    char			szVersion[SEC_VERSION_LEN]	= {0};
    char			szProName[MAX_PATH]			= {0};
    unsigned char	vol	= 0;
    PlayStatus		playStatus;
    ProgramSource	src;
    bool			isTimerInvalid = FALSE;
    unsigned short	pos = 0;
    bool			isNewOnline = FALSE;	// 上线

    // 别名
    int nameLen = pData[pos++];
    memcpy(szName, &pData[pos], nameLen);
    pos += nameLen;

    // MAC地址
    int macLen = pData[pos++];
    SetMac(szMac, &pData[pos]);
    pos += macLen;

    // 音量
    vol = pData[pos++];

    // 节目源
    src = (ProgramSource)(unsigned char)pData[pos++];

    // 节目名称
    int proNameLen = pData[pos++];
    memcpy(szProName, &pData[pos], proNameLen);
    pos += proNameLen;

    // 播放状态
    playStatus = (PlayStatus)pData[pos++];

    // 定时器是否有效
    isTimerInvalid = (pData[pos++] == 0x10);

    // 版本号
    int versionLen = pData[pos++];
    memcpy(szVersion, &pData[pos], versionLen);
    pos+=versionLen;
    //20201216,协议新增设备功能特征字段，判断是否存在功能特征字段
    BYTE device_feature=0;
    if(pos<nLen)    //如果当前pos比负载数据长度小，证明存在此字段
    {
        device_feature = pData[pos++];
    }

    //20220708,协议新增混音器信号字段(仅针对混音器，其余设备固定为false)
    bool bAudioMixerSingalValid=false;
    if(pos<nLen)    //如果当前pos比负载数据长度小，证明存在此字段
    {
        bAudioMixerSingalValid = pData[pos++];
    }
    
    //20230601,协议新增4G信号质量及4G ICCID卡号
    int module4G_signal_rssi=0;     //信号质量rssi
    char module4G_iccid[32]={0};    //20位数字
    char userLogin[33]={0};         //用户名
    char *userLoginPointer=userLogin;
    //4G信号质量
    if(pos<nLen)    //如果当前pos比负载数据长度小，证明存在此字段
    {
        module4G_signal_rssi = pData[pos++];
    }
    //4G ICCID
    if(pos<nLen)    //如果当前pos比负载数据长度小，证明存在此字段
    {
        int iccid_length = pData[pos++];
        memcpy(module4G_iccid,pData+pos,iccid_length);
        pos+=iccid_length;
    }
    //登录用户
    bool isExistUserInfo=false;
    if(pos<nLen)    //如果当前pos比负载数据长度小，证明存在此字段
    {
        int user_len = pData[pos++];
        memcpy(userLogin,pData+pos,user_len);
        pos+=user_len;
        isExistUserInfo=true;
    }
    if(!isExistUserInfo)    //如果不存在用户信息，将指针变成空
    {
        userLoginPointer=NULL;
    }

    //20230914,协议新增音频采集器触发信号字段(仅针对音频采集器，其余设备固定为false)
    bool bAudioCollectorSingalValid=false;
    if(pos<nLen)    //如果当前pos比负载数据长度小，证明存在此字段
    {
        bAudioCollectorSingalValid = pData[pos++];
    }

    //20240306,协议新增电话网关设备信号字段(仅针对电话网关，其余设备固定为false)
    bool bphoneGatewaySingalValid=false;
    if(pos<nLen)    //如果当前pos比负载数据长度小，证明存在此字段
    {
        bphoneGatewaySingalValid = pData[pos++];
    }

    //20240306,协议新增扩展特性字段（如果没有此字段，分区扩展特性字段的初值是-1，不是0）
    int nExtraFeature=-1;
    if(pos<nLen)    //如果当前pos比负载数据长度小，证明存在此字段
    {
        nExtraFeature = pData[pos++];
    }
    

    CSection* pDevice = NULL;

    //QMutexLocker newSectionLocker(&command_HandleSearch_Mutex);
    if(sockobj) //TCP
    {
        //存在这个类型的设备才处理
        if(g_Global.m_ModelToDevices.count(nModel) >0)
        {
            CSections *pDevices	= g_Global.m_ModelToDevices[nModel];
            pDevice	= pDevices->GetSectionByMac(szMac);
            if( pDevice !=NULL )
            {
                pDevice->m_pSocketObj = sockobj;    //先赋值，如果终端原来在播放，重启后应答状态为空闲，HandleSearchSectionDevice中的SectionChangedSource函数会立即下发播放指令，避免没有赋值而接收不到。
                #if SUPPORT_AUDIO_MIXER
                if(pDevice->IsAudioMixerDevice())
                {
                    //更改MAC为解码器的MAC
                    string strAudioMixerDecoderMac = szMac;
                    strAudioMixerDecoderMac.replace(0, 2, "2B");
                    CSection* pAudioMixerDecoder = g_Global.m_Sections.GetSectionByMac(strAudioMixerDecoderMac);
                    if(pAudioMixerDecoder!=NULL)
                    {
                        pAudioMixerDecoder->m_pSocketObj = sockobj;    //先赋值
                    }
                }
                else if(pDevice->IsAudioMixerDecoderDevice())
                {
                    //更改MAC为编码器的MAC
                    string strAudioMixerEncoderMac = szMac;
                    strAudioMixerEncoderMac.replace(0, 2, "2C");
                    CSection* pAudioMixerEncoder = g_Global.m_AudioMixers.GetSectionByMac(strAudioMixerEncoderMac);
                    if(pAudioMixerEncoder!=NULL)
                    {
                        pAudioMixerEncoder->m_pSocketObj = sockobj;    //先赋值
                    }
                }
                #endif
            }
        }
    }
    if (CSections::IsSectionDevice(nModel)) // 分区设备：IP音箱...
    {
        pDevice = HandleSearchSectionDevice (szName, szMac, vol, src, szProName, playStatus, isTimerInvalid, szVersion, szIP, nModel, isNewOnline, nReserved,device_feature,
                                             module4G_signal_rssi,module4G_iccid,userLoginPointer,bAudioMixerSingalValid,bAudioCollectorSingalValid,bphoneGatewaySingalValid,nExtraFeature);
    }
    else if (CSections::IsNotSectionDevice(nModel))	// 其它设备...
    {
        if (nModel != MODEL_INTERCOM_STATION) // not handle DSP9312
        {
            pDevice = HandleSearchOtherDevice(szName, szMac, vol, src, szProName, playStatus, isTimerInvalid, szVersion, szIP, nModel, isNewOnline,nReserved,device_feature,
                                            module4G_signal_rssi,module4G_iccid,userLoginPointer,bAudioMixerSingalValid,bAudioCollectorSingalValid,bphoneGatewaySingalValid,nExtraFeature);
        }
    }

    if (pDevice == NULL)
    {
        return;
    }

    //pDevice->SetReservedWord(nReserved);  // 保留字       20210310 放到上面处理
    if(sockobj) //TCP才赋值
    {
        pDevice->m_pSocketObj = sockobj;

        #if SUPPORT_AUDIO_MIXER
        if(pDevice->IsAudioMixerDevice())
        {
            //更改MAC为解码器的MAC
            string strAudioMixerDecoderMac = szMac;
            strAudioMixerDecoderMac.replace(0, 2, "2B");
            CSection* pAudioMixerDecoder = g_Global.m_Sections.GetSectionByMac(strAudioMixerDecoderMac);
            if(pAudioMixerDecoder!=NULL)
            {
                pAudioMixerDecoder->m_pSocketObj = sockobj;    //先赋值
            }
        }
        else if(pDevice->IsAudioMixerDecoderDevice())
        {
            //更改MAC为编码器的MAC
            string strAudioMixerEncoderMac = szMac;
            strAudioMixerEncoderMac.replace(0, 2, "2C");
            CSection* pAudioMixerEncoder = g_Global.m_AudioMixers.GetSectionByMac(strAudioMixerEncoderMac);
            if(pAudioMixerEncoder!=NULL)
            {
                pAudioMixerEncoder->m_pSocketObj = sockobj;    //先赋值
            }
        }
        #endif
    }
    if(isNewOnline)
    {
        pDevice->SetRestartTime(0);

        CMyString strTip;
        strTip.Format(("%s: %s is on line."), nameLen == 0 ? szIP : pDevice->GetUTFName().data(), CProtocol::GetDescriptionDevice(nModel).C_Str());
        m_pNetwork->AddLog(strTip);

        CMyString strLogContents;
        strLogContents.Format("%s,version=%s",  LANG_STR(LANG_SECTION_ZONE_GROUP, "Online", ("上线")).C_Str(),szVersion);
        // Linux服务器
        g_Global.m_logTable.InsertLog(CMyString(pDevice->GetMac()),
                                      CMyString(nameLen == 0 ? szIP : szName),
                                      LT_RUNNING_STATE,
                                      strLogContents);

        m_pNetwork->m_CmdSend.CmdSyncronizeTime(*pDevice); // 新上线的要同步时间

        if (CSections::IsSectionDevice(nModel))
        {
#if 0   //jms
            m_pNetwork->m_CmdSend.CmdGetFileInfo(FILE_PLAYLIST, *pDevice);
            m_pNetwork->m_CmdSend.CmdGetFileInfo(FILE_TIMER, *pDevice);
            m_pNetwork->m_CmdSend.CmdGetFileInfo(FILE_GROUP, *pDevice);
            
            // DSP9131/DSP9134-网络解码播放器(机柜式)
            if (nModel == MODEL_DECODE_PLAYER_CABINET || nModel == MODEL_DECODE_PLAYER_WITH_SPLITTER)
            {
                // 查询回路检测
                m_pNetwork->m_CmdSend.CmdSigalDetection(*pDevice);
            }
#endif
            // SIP音箱新上线需要获取状态和登录信息
            if (pDevice->IsSupportSipDevice())
            {
                //m_pNetwork->m_CmdSend.CmdSipGetStatus(*pDevice);
                //m_pNetwork->m_CmdSend.CmdSipGetLogInfo(*pDevice);
            }
#if 0
            m_pNetwork->m_CmdSend.CmdGetFlashInfo(*pDevice);
#endif
           //查询EQ信息
           g_Global.m_Network.m_CmdSend.CmdDeviceEq(*pDevice);
           
            //支持蓝牙的分区需要查询蓝牙信息
           if (pDevice->IsBlueToothDevice())
           {
                LOG(FORMAT("Section: %s support BlueTooth!\n", nameLen == 0 ? szIP : pDevice->GetUTFName().data()), LV_INFO);
                g_Global.m_Network.m_CmdSend.CmdBlueToothInfo(*pDevice);
           }
           #if SUPPORT_INTERCOM_DEVICE_CONFIG
           if (pDevice->IsSupportCallDevice())
           {
                LOG(FORMAT("Section: %s support Call!\n", nameLen == 0 ? szIP : pDevice->GetUTFName().data()), LV_INFO);
                g_Global.m_Network.m_CmdSend.CmdIntercomBasicConfig(*pDevice);
           }
           #endif
        }
        else if(CSections::IsControlDevice(nModel))
        {
            // 获取播放列表和分组文件信息
            m_pNetwork->m_CmdSend.CmdGetFileInfo(FILE_PLAYLIST, *pDevice);
            m_pNetwork->m_CmdSend.CmdGetFileInfo(FILE_GROUP, *pDevice);

            // 获取采集器列表
            m_pNetwork->m_CmdSend.CmdGetFileInfo(FILE_AUDIO_COLLECTOR, *pDevice);

            // 定时点信息
            m_pNetwork->m_CmdSend.CmdGetFileInfo(FILE_TIMER, *pDevice);

            // 获取用户信息
            m_pNetwork->m_CmdSend.CmdGetFileInfo(FILE_USER, *pDevice);

            #if SUPPORT_PAGER_CALL
            if (nModel == MODEL_PAGER_A || nModel == MODEL_PAGER_B || nModel == MODEL_PAGER_C)
            {
                // 寻呼台文件
                m_pNetwork->m_CmdSend.CmdGetFileInfo(FILE_PAGER, *pDevice);
            }
            #endif

            // 集中模式
            if (WP_IS_CENTRALIZED)
            {
                // 获取分区信息
                m_pNetwork->m_CmdSend.CmdGetFileInfo(FILE_SECTION, *pDevice);

                // 需要设置播放模式
                m_pNetwork->m_CmdSend.CmdSetPlayMode(g_Global.m_PlayList.GetPlayMode(), *pDevice);
            }

        }
        else if (CSections::IsFireCollectorDevice(nModel))
        {
            // 获取消防报警触发状态和模式
            m_pNetwork->m_CmdSend.CmdAlarmMode(*pDevice, 0xFF, 0);
            m_pNetwork->m_CmdSend.CmdGetAlarmState(*pDevice);

            // 消防列表信息
            m_pNetwork->m_CmdSend.CmdGetFileInfo(FILE_FIRE_COLLECTOR, *pDevice);
        }
        else if (CSections::IsSequencePowerDevice(nModel))
        {
            // 获取通道信息
            m_pNetwork->m_CmdSend.CmdGetSequencePowerInfo(*pDevice);

            // 时序器文件
            //m_pNetwork->m_CmdSend.CmdGetFileInfo(FILE_SEQUENCE_POWER, *pDevice);
        }
        #if SUPPORT_AUDIO_MIXER
        else if (CSections::IsAudioMixerDevice(nModel))
        {
            // 获取混音器参数
            m_pNetwork->m_CmdSend.CmdAudioMixerConfig(*pDevice);
        }
        #endif
        #if SUPPORT_PHONE_GATEWAY
        else if (CSections::IsPhoneGatewayDevice(nModel))
        {
            // 获取电话网关参数
            m_pNetwork->m_CmdSend.CmdPhoneGatewayConfig(*pDevice,"");
        }
        #endif

        // 工作模式不一样（之前放在判断新上线判断外面，是防止运行过程中，由于设备重启，可能工作模式也会变化）
        // 重启过程中工作模式改变的检测放到DevicesCheckOffline去判断了
        WorkPattern workPattern = pDevice->GetWorkPattern();
        if (workPattern != WP_UNKNOWN && workPattern != g_Global.m_WorkPattern)
        {
            // 需要设置工作模式
            m_pNetwork->m_CmdSend.CmdSetWorkPattern(*pDevice, g_Global.m_WorkPattern);
        }

        Audiocast auiocast = pDevice->GetAudicast();
        if (auiocast != AUDIOCAST_UNKNOWN && auiocast != g_Global.m_Audiocast)
        {
            // 需要设置音频传输方式
            //m_pNetwork->m_CmdSend.CmdAudiocastMode(*pDevice, g_Global.m_Audiocast);
        }
    }
}

// 处理获取终端状态命令
void CCommandHandle::HandleGetStatus(const char*	pData,	// 数据区
                                     unsigned short	nLen,	// 数据长度
                                     CSection&      device)	// 设备
{
    pthread_mutex_lock(&device.sectionChangeMutex);

    unsigned short pos			= 0;
    unsigned char volume		= pData[pos++];
    unsigned char src			= pData[pos++];
    unsigned char playStatus	= pData[pos++];
    unsigned char proNameLen	= pData[pos++];
    char szProName[STR_MAX_PATH]	= {0};

    memcpy(szProName, &pData[pos], proNameLen);
    pos+=proNameLen;

    int playType=1; //1 网络点播 2网络点播（缓存）
    string radioSessionId;  //网络电台sessionId
    if(pos<nLen)    //如果当前pos比负载数据长度小，证明存在播放类型字段
    {
        playType = pData[pos++];
        if(pos<nLen)    //如果当前pos比负载数据长度小，证明存在radioSessionId长度和radioSessionId
        {
            int radioSessionIdLen = pData[pos++];
            if(radioSessionIdLen>=0 && radioSessionIdLen<64)
            {
                radioSessionId.assign(&pData[pos], radioSessionIdLen);
                pos+=radioSessionIdLen;
            }
            //printf("radioSessionIdLen=%d,radioSessionId=%s\n",radioSessionIdLen,radioSessionId.data());
        }
    }

    // 还在重启中，无法更新状态！
    ctime_t tNow = CTime::GetCurrentTimeT().GetTime();
    if (device.IsRestarting(tNow))
    {
        pthread_mutex_unlock(&device.sectionChangeMutex);
        return;
    }

    // 分区设备
    if (device.IsSectionDevice())
    {
        unsigned char preSrc = device.GetProSource();
        if (device.GetVolume() == volume
            && device.GetPlayStatus() == playStatus
            && preSrc == src
            && strcmp(szProName, device.GetProName()) == 0)
        {
            // 全部状态相同，不需要更新
            pthread_mutex_unlock(&device.sectionChangeMutex);
            return;
        }

        device.m_nStatusSameCount = 0;

        device.SetVolume(volume);

        device.SetPlayStatus((PlayStatus)playStatus);

        // 写到日志
        //printf("src=%d,preSrc=%d,szProName=%s,PreProName=%s\n",src,preSrc,szProName,device.GetProName());
        if (src == PRO_LOCAL_PLAY || src == PRO_TIMING )
        {
            // 节目名称不为空而且与上一次不相同时，再加入日志
            if (strlen(szProName) > 0 && strcmp(szProName, device.GetProName()) != 0)
            {
                CMyString strLogContents;
                CMyString strProName(szProName);
                if(device.GetIsExistLocalSong() || playType == 2)
                {
                    strLogContents.Format(("(缓存)%s:%s"), CProtocol::GetDescriptionProSource((ProgramSource)src).C_Str(), strProName.C_Str());
                }
                else
                {
                    strLogContents.Format(("%s:%s"), CProtocol::GetDescriptionProSource((ProgramSource)src).C_Str(), strProName.C_Str());
                }

                // Linux服务器
                g_Global.m_logTable.InsertLog(CMyString(device.GetMac()),
                                              CMyString(device.GetName()),
                                              LT_PLAY_PROGRAM,
                                              strLogContents);
            }
        }
        else if (src == PRO_PAGING && preSrc != PRO_PAGING)
        {
            // 加到日志
            CMyString strLogContents;
            strLogContents.Format("%s:音量-%d",  CProtocol::GetDescriptionProSource(PRO_PAGING).C_Str(), device.GetVolume());
            g_Global.m_logTable.InsertLog(	CMyString(device.GetMac()),
                                            CMyString(device.GetName()),
                                            LT_PAGING_LOG,
                                            strLogContents);
        }
        else if (CProtocol::IsAudioCollectorSrc((ProgramSource)src) && preSrc != src)
        {
            CMyString strLogContents;
            CMyString strProName(szProName);
        
            //根据音源获取音频采集器
            LPCSection lpSection = g_Global.m_AudioCollectors.GetAudioCollectorByAcSource((ProgramSource)src);
            if(lpSection)
            {
                strLogContents.Format("%s:%s(CH%d)", CProtocol::GetDescriptionProSource((ProgramSource)src).C_Str(),lpSection->GetName(),CProtocol::GetAudioCollectorChannelBySrc((ProgramSource)src));
            }
            else
            {
                strLogContents.Format("%s:CH%d", CProtocol::GetDescriptionProSource((ProgramSource)src).C_Str(),CProtocol::GetAudioCollectorChannelBySrc((ProgramSource)src));
            }
            

            // Linux服务器
            g_Global.m_logTable.InsertLog(CMyString(device.GetMac()),
                                            CMyString(device.GetName()),
                                            LT_PLAY_PROGRAM,
                                            strLogContents);
        }


        device.SetProName(szProName);

        if(device.GetNetRadioSessionId() != radioSessionId)
        {
            //保存原来的sessionId
            string oldSessionId = device.GetNetRadioSessionId();
            device.SetNetRadioSessionId(radioSessionId);
            #if 0
            //判断oldSessionId是否还有其他分区使用，如果都没有了，需要退出radioClient
            if(!oldSessionId.empty())
            {
                bool bHasOther = false;
                for(int i=0;i<g_Global.m_Sections.GetSecCount();i++)
                {
                    CSection &Section = g_Global.m_Sections.GetSection(i);
                    if(Section.GetNetRadioSessionId() == oldSessionId)
                    {
                        bHasOther = true;
                        break;
                    }
                }
                if(!bHasOther)
                {
                    //需要退出radioClient
                    g_Global.m_Network.m_CmdSend.CmdExitNetRadio(oldSessionId);
                }
            }
            #endif
        }

        // 音源改变处理
        SectionChangedSource(device, (ProgramSource)src);
        // 如果分区状态改变，则转发给分控设备
        #if 1
        g_Global.m_Sections.AddVarySection(device.GetMac(),device.GetID());
        #else
        m_pNetwork->ForwardSectionsStatusToControlDevices(&device, NULL);
        g_Global.m_WebNetwork.ForwardSectionInfoToWeb(DEVICE_SECTION, &device, NULL, FALSE);
        #endif
    }
    else
    {
        device.SetVolume(volume);
    }
    pthread_mutex_unlock(&device.sectionChangeMutex);
}

// 处理固件升级
void CCommandHandle::HandleFirmwareUpgrade(	char	stateCode,			// 状态码
                                            CSection& device)			// 设备
{
    if(stateCode == 0x01)
    {
        device.SetUpgrading(TRUE);
    }
    else
    {
        device.SetUpgrading(FALSE);
    }

    if (stateCode == 0x05)
    {
        device.SetRestartTime(CTime::GetCurrentTimeT().GetTime());
        m_pNetwork->DeviceOffline(device);
    }
    
    g_Global.m_UpgradeManager.HandleUpgrade(g_Global.m_Sections.GetUpgradeSectionCount());

    CMyString strShow;
    strShow.Format(("%s：%s"), device.GetUTFName().data(), CProtocol::GetDescriptionUpgradeStatus(stateCode).C_Str());
    m_pNetwork->AddLog(strShow);

    // 回调函数 保留，待修改
    //g_Global.m_MsgHandle.OnMsgHandle(UM_UPGRADE_STATUS, (LPARAM)(&device), stateCode);
    g_Global.m_WebNetwork.ForwardUpgradeDeviceStatus(NULL, device.GetMac(), stateCode, 0);
}

// 处理升级进度
void CCommandHandle::HandleProgressRate(char	  rate,					// 升级进度
                                        CSection& device)				// 设备
{
    if(rate >= 100)
    {
        device.SetUpgrading(FALSE);

        g_Global.m_UpgradeManager.HandleUpgrade(g_Global.m_Sections.GetUpgradeSectionCount());
    }

    // 回调函数 保留，待修改
    //g_Global.m_MsgHandle.OnMsgHandle(UM_UPGRADE_PROGRESS, (LPARAM)(&device), rate);

    g_Global.m_WebNetwork.ForwardUpgradeDeviceStatus(NULL, device.GetMac(), 1, rate);
}

// 处理获取文件信息
void  CCommandHandle::HandleGetFileInfo(const char*     pData,			// 数据区
                                        unsigned short	nLen,			// 数据长度
                                        CSection&       device)			// 设备
{
    FileType fileType		= (FileType)pData[0];
    int		 nDateTimeLen	= pData[1];
    char	 szDateTime[20] = {0};

    memcpy(szDateTime, &pData[2], nDateTimeLen >= 20 ? 19 : nDateTimeLen);

    CMyString		strDateTime = (nDateTimeLen == 0 ? DEFAULT_DATETIME : CMyString(szDateTime));
    bool		isLatest = TRUE;	// 文件更新时间是不是最新的

    if (fileType == FILE_GROUP) // 分组文件
    {
        if (strDateTime != g_Global.m_Groups.GetDateTime())
        {
            isLatest = FALSE;
        }

        device.SetFileDateTime(DT_GROUP, strDateTime);
    }
    else if (fileType == FILE_PLAYLIST)	// 播放列表文件
    {
        if (device.IsSectionDevice())
        {
            if (strDateTime != device.GetPlaylist()->GetDateTime())
            {
                isLatest = FALSE;
            }
            // 如果是最新，而且正在同步（防止同步成功没返回）
            else if (device.IsSyncSong())
            {
                // 同步标志
                device.SetSyncSong(FALSE);

                // TODO: 通知同步窗口去更新状态，现在暂时不需要这么做
            }
        }

        device.SetFileDateTime(DT_PLAYLIST, strDateTime);
    }
    else if (fileType == FILE_TIMER)	// 定时文件
    {
        if (strDateTime != g_Global.m_TimerScheme.GetDateTime())
        {
            isLatest = FALSE;
        }

        device.SetFileDateTime(DT_TIMER, strDateTime);
    }
    else if (fileType == FILE_SECTION)	// 分区文件
    {
        if (strDateTime != g_Global.m_Sections.GetDateTime())
        {
            isLatest = FALSE;
        }
        else
        {
            // 分控设备获取到最新的文件，把所有设备的状态下发
            // 如果不是最新文件，等到同步文件完成后再下发
            // 可能有多次获取，所以要判断是不是第一次获取
            if (device.IsControlDevice() && device.NeedGetFileInfo(DT_SECTION))
            {
                m_pNetwork->ForwardSectionsStatusToControlDevices(NULL, &device);
            }
        }

        device.SetFileDateTime(DT_SECTION, strDateTime);
    }
    else if (fileType == FILE_AUDIO_COLLECTOR)	// 音频采集信息
    {
        //20230919不处理
        #if 0
        if (strDateTime != g_Global.m_AudioCollectors.GetDateTime())
        {
            isLatest = FALSE;
        }
        else
        {
            // 分控设备获取到最新的文件，把所有音频采集器的状态下发
            if (device.IsControlDevice() && device.NeedGetFileInfo(DT_AUDIO_COLLECTOR))
            {
                m_pNetwork->ForwardAllCollectorsStatusToControlDevices(NULL, &device);
            }
        }
        #endif
        device.SetFileDateTime(DT_AUDIO_COLLECTOR, strDateTime);
        m_pNetwork->m_CmdSend.CmdSendAudioList(device);
        printf("HandleGetFileInfo:FILE_AUDIO_COLLECTOR,deviceMac=%s\n",device.GetMac());
    }
    else if (fileType == FILE_FIRE_COLLECTOR)	//消防采集信息
    {
        if (strDateTime != g_Global.m_FireCollectors.GetDateTime())
        {
            isLatest = FALSE;
        }

        device.SetFileDateTime(DT_FIRE_COLLECTOR, strDateTime);
    }
    else if (fileType == FILE_USER)	//账户信息
    {
        if (strDateTime != g_Global.m_Users.GetDateTime())
        {
            isLatest = FALSE;
        }

        device.SetFileDateTime(DT_USER, strDateTime);
    }
    else if (fileType == FILE_PAGER)	//账户信息
    {
        if (strDateTime != g_Global.m_Pagers.GetDateTime())
        {
            isLatest = FALSE;
        }

        device.SetFileDateTime(DT_PAGER, strDateTime);
    }
}

// 处理文件更新返回结果
void	CCommandHandle::HandleUpdateFileResult(	FileType	ft,				// 文件类型
                                                char		result,			// 更新结果
                                                CSection&	device)			// 设备
{
    CMyString strResult	= ("");
    CMyString strShow	= ("");
    bool	bShowTip	= TRUE;

    strResult = CProtocol::GetDescriptionFileUpdateResult(result);

    if (result == UFR_LATEST)
    {
        device.SetDateTimeFile(ft);
    }
    else if (result == UFR_SUCCESS)
    {
        if (device.IsSectionDevice())
        {
            // 播放列表文件不需要同步日期，等歌曲同步完了再更新
            if (ft != FILE_PLAYLIST)
            {
                device.SetDateTimeFile(ft);
                bool bNeedUpdate = device.NeedUpdateSectionFile();

                if (bNeedUpdate != device.IsNeedUpdate())
                {
                    device.SetNeedUpdate(bNeedUpdate);

                    // 转发给分控设备,告知分区已经同步
                    //m_pNetwork->ForwardSectionsStatusToControlDevices(&device, NULL);
                }
            }
            else
            {
                bShowTip = FALSE;
            }

            // 设备Xml更新结果返回到所有Web端

        }
        else
        {
            device.SetDateTimeFile(ft);

            // 分控设备
            if (device.IsControlDevice())
            {
                // 同步完分区文件，需要把所有分区状态下发
                if (ft == FILE_SECTION)
                {
                    m_pNetwork->ForwardSectionsStatusToControlDevices(NULL, &device);
                }
                // 同步完音频采集器文件，需要把采集器状态下发
                else if (ft == FILE_AUDIO_COLLECTOR)
                {
                    m_pNetwork->ForwardAllCollectorsStatusToControlDevices(NULL, &device);
                }
            }
        }
    }

    // 每次同步前，设备都会先回一条命令（立即应答），result=0，所以这里要过滤掉
    if (result != 0)
    {
        // 如果播放列表文件同步有问题
        if (ft == FILE_PLAYLIST && result != UFR_SUCCESS)
        {
            // 同步歌曲之前，设备会返回UFR_NOT_IDLE，可能是重发同步命令的原因，此时设备还在解析XML文件
            device.SetSyncSong(FALSE);
        }

        if (bShowTip)	// 同步音箱的播放列表不用提示同步成功
        {
            strShow.Format(("%s：%s，%s"), device.GetUTFName().data(), CProtocol::GetDescriptionFileType(ft).C_Str(), strResult.C_Str());
            m_pNetwork->AddLog(strShow);
        }
    }

    CMyString strContents;

    // 同步播放列表在HandleSyncProgress里处理，
    if ((ft == FILE_PLAYLIST && result == UFR_SUCCESS))
    {
        strContents.Format(("%s:%s"), CProtocol::GetDescriptionFileType(ft).C_Str(),
                           LANG_STR(LANG_SECTION_ZONE_GROUP, "Sync Start", ("同步开始")).C_Str());
    }
    // 其它的显示同步结果
    else
    {
        strContents.Format(("%s:%s"), CProtocol::GetDescriptionFileType(ft).C_Str(),
                           CProtocol::GetDescriptionFileUpdateResult(result).C_Str());
    }

    // Linux服务器
    g_Global.m_logTable.InsertLog(CMyString(device.GetMac()),
                                                         CMyString(device.GetName()),
                                                         LT_SYNC_FILE,
                                                         strContents);

    // 转发状态到Web设备  zhuyg
    //g_Global.m_WebNetwork.ForwardDeviceFileStatus(NULL, device.GetDeviceModel(), ft, device.GetMac(),result);
}

// 歌曲文件同步进度上传
void	CCommandHandle::HandleSyncProgress(	const char*	pData,				// 数据区
                                            unsigned short	nLen,				// 数据长度
                                            CSection&	device)			// 设备
{
    char	status  = pData[0];
    unsigned short	allSongCount		= CharsToShort(&pData[1]);
    unsigned short	finishedSongCount	= CharsToShort(&pData[3]);
    unsigned char	percentage          = (nLen == 6 ? pData[5] : 100);			// 歌曲同步的百分比

    if (status == 0x02)	// 由于某种原因终止同步
    {
        device.SetSyncSong(FALSE);
        // 回调函数 保留，待修改
        //g_Global.m_MsgHandle.OnMsgHandle(UM_SYNC_SONG_FAILED, (LPARAM)(&device));

        CMyString strTip;
        strTip.Format(("%s : Sync abort."), device.GetUTFName().data());
        m_pNetwork->AddLog(strTip);

        CMyString strContents;
        strContents.Format(("%s:%s"), CProtocol::GetDescriptionFileType(FILE_PLAYLIST).C_Str(),
            LANG_STR(LANG_SECTION_ZONE_GROUP, "Sync Abort", ("同步中止")).C_Str());

        // Linux服务器
        g_Global.m_logTable.InsertLog(CMyString(device.GetMac()),
                                                             CMyString(device.GetName()),
                                                             LT_SYNC_FILE,
                                                             strContents);
    }
    else if (status == 0x01)
    {
        device.SetSyncSong(TRUE);	// 有同步进度上传，表示正在同步，所以把标志位设为TRUE

        device.SetFinishedSong(finishedSongCount);
        device.SetSyncSongCount(allSongCount);

        if (finishedSongCount == allSongCount && percentage == 100)
        {
            device.SetSyncSong(FALSE);

            CMyString strTip;
            strTip.Format(("%s : %s"), device.GetUTFName().data(), LANG_STR(LANG_SECTION_ZONE_GROUP, "Sync Finished", ("同步完成")).C_Str());
            m_pNetwork->AddLog(strTip);

            // 播放列表文件日期更新
            device.SetDateTimeFile(FILE_PLAYLIST);
            bool bNeedUpdate = device.NeedUpdateSectionFile();

            if (bNeedUpdate != device.IsNeedUpdate())
            {
                device.SetNeedUpdate(bNeedUpdate);

                // 状态改变告诉分控设备
                m_pNetwork->ForwardSectionsStatusToControlDevices(&device, NULL);
            }

            CMyString strContents;
            strContents.Format(("%s:%s"), CProtocol::GetDescriptionFileType(FILE_PLAYLIST).C_Str(),
                CProtocol::GetDescriptionFileUpdateResult(UFR_SUCCESS).C_Str());

            // Linux服务器
            g_Global.m_logTable.InsertLog(CMyString(device.GetMac()),
                                          CMyString(device.GetName()),
                                          LT_SYNC_FILE,
                                          strContents);
        }
        else if (percentage == 100)
        {
            CMyString strTip;
            strTip.Format(("%s : %s %d/%d"),
                          device.GetUTFName().data(),
                          LANG_STR(LANG_SECTION_DIALOG, "Synchronized", "已同步").C_Str(),
                          finishedSongCount,
                          allSongCount);
            m_pNetwork->AddLog(strTip);
        }
    }

    // 转发状态到Web设备  zhuyg
    g_Global.m_WebNetwork.ForwardUpdateSongStatus(NULL, device.GetDeviceModel(), FILE_PLAYLIST, device.GetMac(), status, allSongCount, finishedSongCount, percentage);
}

// 终端主动请求同步时间
void	CCommandHandle::HandleRequestSyncTime(CSection&	device)		// 设备
{
    m_pNetwork->m_CmdSend.CmdSyncronizeTime(device);
}

// 处理主机请求终端重新分配MAC地址
void	CCommandHandle::HandleReassignMac(CSection&	device)		// 设备
{
    m_pNetwork->DeviceOffline(device);

    // 回调函数 保留，待修改
    //g_Global.m_MsgHandle.OnMsgHandle(UM_REASSIGN_MAC, (LPARAM)(&device));

    CMyString strTip;
    strTip.Format(("%s：Redestributing MAC..."), device.GetUTFName().data());
    m_pNetwork->AddLog(strTip);
}

// 处理主机向设备请求重启
void	CCommandHandle::HandleReboot(CSection&	device)			// 设备
{
    device.SetRestartTime(CTime::GetCurrentTimeT().GetTime());
    m_pNetwork->DeviceOffline(device);

    // 回调函数 保留，待修改
    //g_Global.m_MsgHandle.OnMsgHandle(UM_REBOOT, (LPARAM)(&device));

    CMyString strTip;

    // 当LANG_STR获取语言文件 放置在前面时会导致乱码，放置在后面是正常，原因不详
    //strTip.Format((char*)("%s : %s..."), LANG_STR(LANG_SECTION_ZONE_GROUP, "Restarting", ("正在重启")).C_Str(), device.GetName(TRUE));
    strTip.Format((char*)("%s : %s..."), device.GetUTFName().data(), LANG_STR(LANG_SECTION_ZONE_GROUP, "Restarting", ("正在重启")).C_Str());
    m_pNetwork->AddLog(strTip);

    // 回复Web终端
    //g_Global.m_WebNetwork.ForwardSectionInfoToWeb();
}

// 主机请求终端重置数据
void	CCommandHandle::HandleResetData(char		type,		// 数据类型
                                        CSection&	device)		// 设备
{
    // 0xFF：恢复出厂设置   0x01：分区数据  0x02：分组数据  0x03：播放列表（包括已同步文件）   0x04：定时数据
    unsigned char  fileType = (unsigned char)type;

    if (fileType == 0xFF)
    {
        if (device.IsSectionDevice())
        {
            device.SetFileDateTime(DT_TIMER, DEFAULT_DATETIME);
            device.SetFileDateTime(DT_PLAYLIST, DEFAULT_DATETIME);
            device.SetFileDateTime(DT_GROUP, DEFAULT_DATETIME);

            g_Global.m_WebNetwork.ResetUserFileDateTime(NULL, device.GetMac(), FILE_TIMER);
            g_Global.m_WebNetwork.ResetUserFileDateTime(NULL, device.GetMac(), FILE_PLAYLIST);
            g_Global.m_WebNetwork.ResetUserFileDateTime(NULL, device.GetMac(), FILE_GROUP);
        }
        else if (device.IsControlDevice())
        {
            device.SetFileDateTime(DT_GROUP, DEFAULT_DATETIME);
            device.SetFileDateTime(DT_PLAYLIST, DEFAULT_DATETIME);
            device.SetFileDateTime(DT_SECTION, DEFAULT_DATETIME);
            device.SetFileDateTime(DT_AUDIO_COLLECTOR, DEFAULT_DATETIME);
            device.SetFileDateTime(DT_USER, DEFAULT_DATETIME);
            device.SetFileDateTime(DT_PAGER, DEFAULT_DATETIME);
        }
        else if (device.IsFireCollectorDevice())
        {
            device.SetFileDateTime(DT_FIRE_COLLECTOR, DEFAULT_DATETIME);
        }
    }
    else if (fileType == 0x02)
    {
        device.SetFileDateTime(DT_GROUP, DEFAULT_DATETIME);

        if(device.IsSectionDevice())
        {
            g_Global.m_WebNetwork.ResetUserFileDateTime(NULL, device.GetMac(),FILE_GROUP);
        }
    }
    else if (fileType == 0x03)
    {
        device.SetFileDateTime(DT_PLAYLIST, DEFAULT_DATETIME);

        if(device.IsSectionDevice())
        {
            g_Global.m_WebNetwork.ResetUserFileDateTime(NULL, device.GetMac(),FILE_PLAYLIST);
        }
    }
    else if (fileType == 0x04)
    {
        device.SetFileDateTime(DT_TIMER, DEFAULT_DATETIME);

        if(device.IsSectionDevice())
        {
            g_Global.m_WebNetwork.ResetUserFileDateTime(NULL, device.GetMac(),FILE_TIMER);
        }
    }
    else if (fileType == 0x05)
    {
        device.SetFileDateTime(DT_AUDIO_COLLECTOR, DEFAULT_DATETIME);
    }
    else if (fileType == 0x06)
    {
        device.SetFileDateTime(DT_FIRE_COLLECTOR, DEFAULT_DATETIME);
    }

    // 回调函数 保留，待修改
    //g_Global.m_MsgHandle.OnMsgHandle(UM_RESET_DATA, (LPARAM)(&device), fileType);

    CMyString strTip;
    strTip.Format(("%s：Finished reseting data %d"), device.GetUTFName().data(), fileType);
    m_pNetwork->AddLog(strTip);

    // 同步信息到Web客户端
    //LOG("******        终端重置数据        *********", LV_INFO);
    g_Global.m_WebNetwork.m_WebSend.WebResponseResetDeviceData(device.GetMac(), NULL);
}

// 处理获取FLASH信息
void	CCommandHandle::HandleGetFlashInfo(	const char*		pData,			// 数据区
                                            unsigned short	nLen,			// 数据长度
                                            CSection&	device)				// 设备
{
    char	szBrandModel[64]	= {0};
    char	szMemory[64]		= {0};
    unsigned char	pos			= 0;

    // FLASH信息

    // 品牌型号(合在一起，跟协议有出入)
    int brandModelLen = pData[pos++];
    memcpy(szBrandModel, &pData[pos], brandModelLen);
    pos += brandModelLen;

    // 可用容量/总容量（合在一起，跟协议有出入）
    int memoryLen = pData[pos++];
    memcpy(szMemory, &pData[pos], memoryLen);
    pos += memoryLen;

    // 表示有EMMC
    if (nLen - pos > 2)
    {
        char	szBrand[64]			= {0};	// 型号长度
        char	szModel[64]			= {0};	// 型号长度
        char	szTotalMemory[64]	= {0};	// 总容量
        char	szAvaMemory[64]		= {0};	// 可用容量

        // 品牌
        int brandLen = pData[pos++];
        memcpy(szBrand, &pData[pos], brandLen);
        pos += brandLen;

        // 型号
        int modelLen = pData[pos++];
        memcpy(szModel, &pData[pos], modelLen);
        pos += modelLen;

        // 总容量
        int totalMemLen = pData[pos++];
        memcpy(szTotalMemory, &pData[pos], totalMemLen);
        pos += totalMemLen;

        // 可用容量
        int avaMemLen = pData[pos++];
        memcpy(szAvaMemory, &pData[pos], avaMemLen);
        pos += avaMemLen;

        device.m_strMemory.Format(("%s/%s"), szAvaMemory, szTotalMemory);
    }
    else
    {
        device.m_strMemory = szMemory;
    }


    string::size_type index = device.m_strMemory.Find("/");
    if(index != string::npos)
    {
        CMyString strTotalMemory = device.m_strMemory.Right(index);

        // CS 2019-5-9 (兼容不支持同步文件的设备)
        if(strTotalMemory.Find('G') != string::npos)           // 根据设备发过来的容量信息来判断设备是否支持同步文件，1G以上为支持，反之不支持
        {
            device.m_bSupportSyncFile = TRUE;
        }
        else
        {
            device.m_bSupportSyncFile = FALSE;
        }
    }

#if 0   //jms
    // 406N应答包格式与协议有出入，待固件修改，这里掩饰其错误
    if(device.GetDeviceModel() == MODEL_DECODE_PLAYER_LITE)
    {
        device.m_strMemory = "0M/0M";
    }
#endif

}

// 寻呼台向主机发送终端寻呼中途掉线信息
void	CCommandHandle::HandleSpeakerOffline(const char*	pData,					// 数据区
                                            unsigned short	nLen,					// 数据长度
                                            CSection& device)				// 设备
{
    char	szIP[MAX_IP_LEN]	= {0};
    char	szStartTime[200]	= {0};
    char	szEndTime[200]		= {0};

    unsigned char	pos			= 0;

    // 日期
    int ipLen = pData[pos++];
    memcpy(szIP, &pData[pos], ipLen);
    pos += ipLen;

    // 开始时间
    int startTimeLen = pData[pos++];
    memcpy(szStartTime, &pData[pos], startTimeLen);
    pos += startTimeLen;

    // 结束时间
    int endTimeLen = pData[pos++];
    memcpy(szEndTime, &pData[pos], endTimeLen);
    pos += endTimeLen;

    CMyString strTip;
    strTip.Format(("%s：has dropped，start time：%s，end time：%s"), szIP,
                  szStartTime, szEndTime);
}

// 处理设备返回日期时间
void	CCommandHandle::HandleGetDateTime(	const char*	pData,					// 数据区
                                            unsigned short	nLen,					// 数据长度
                                            CSection& device)				// 设备
{
    char	szDate[32]	= {0};
    char	szTime[32]	= {0};

    unsigned char	pos			= 0;

    // 日期
    int dateLen = pData[pos++];
    memcpy(szDate, &pData[pos], dateLen);
    pos += dateLen;

    // 时间
    int timeLen = pData[pos++];
    memcpy(szTime, &pData[pos], timeLen);
    pos += timeLen;

    // 是否连接上GPS
    bool bConnectedGPS = (pData[pos] == 0 ? FALSE : TRUE);
    device.SetConnectedGPS(bConnectedGPS);
    pos++;

    CMyString strDateTime;
    strDateTime.Format(("%s %s"), szDate, szTime);
    device.SetCurDateTime(strDateTime);

    // 回调函数 保留，待修改
    //g_Global.m_MsgHandle.OnMsgHandle(UM_GET_DATE_TIME, (WPARAM)(&device));
}

// 处理返回日志文件列表
void	CCommandHandle::HandleGetLogFileList(	const char*	pData,				// 数据区
                                                unsigned short	nLen,				// 数据长度
                                                CSection& device)			// 设备
{
    unsigned short	pos				= 0;	// pos类型为unsigned short，而不是unsigned char
    unsigned char	nFileCount = pData[pos++];

    device.m_LogFiles.Clear();

    m_pNetwork->AddLog(("HandleGetLogFileList"));

    // 把文件列表保存下来
    for (int i=0; i<nFileCount; ++i)
    {
        unsigned char	 nameLen = 0;
        CLogFile logFile;

        // 文件名称
        nameLen = pData[pos++];
        memcpy(logFile.m_szName, &pData[pos], nameLen);
        pos += nameLen;

        // 文件大小
        logFile.m_nSize = CharsToInt(&pData[pos]);
        pos += 4;

        // 总包数
        logFile.m_nPackCount = CharsToShort(&pData[pos]);
        pos += 2;

        device.m_LogFiles.AddLogFile(logFile);
    }

    // 回调函数 保留，待修改
    //g_Global.m_MsgHandle.OnMsgHandle(UM_GET_LOG_FILE_LIST, (WPARAM)(&device));
    g_Global.m_WebNetwork.ForwardDevceLogList(device);
}

// 处理返回日志文件
void	CCommandHandle::HandleGetLogFileData(	const char*	pData,			// 数据区
                                                unsigned short	nLen,		// 数据长度
                                                CSection& device)			// 设备
{
    unsigned char	pos				= 0;
    unsigned char	nameLen			= 0;
    unsigned short	nPackID			= 0;
    unsigned short	nFileDataLen	= 0;
    char	szFileData[MAX_BUF_LEN] = {0};
    char	szFileName[LOG_FILE_NAME_MAX_LEN] = {0};

    // 文件名
    nameLen = pData[pos++];
    memcpy(szFileName, &pData[pos], nameLen);
    pos += nameLen;

    // 包ID
    nPackID = CharsToShort(&pData[pos]);
    pos += 2;

    // 文件内容长度
    nFileDataLen = CharsToShort(&pData[pos]);
    pos += 2;

    // 文件内容
    memcpy(szFileData, &pData[pos], nFileDataLen);

    CLogFile *pLogFile = device.m_LogFiles.FindLogFile(szFileName);

    if (pLogFile != NULL)
    {
        if (nPackID - pLogFile->m_nRecvPackID == 1)
        {
            // 第一个包，需要分配内存
            if (pLogFile->m_nRecvPackID == 0)
            {
                if (pLogFile->m_pData != NULL)
                {
                    delete[] pLogFile->m_pData;
                    pLogFile->m_pData = NULL;
                }

                // 分配内存，在CDlgViewLog::OnBnClickedBtnClose()中释放
                pLogFile->m_pData = new char[pLogFile->m_nSize];
            }

            // 写到内存
            pLogFile->m_nRecvPackID += 1;
            memcpy(pLogFile->m_pData + pLogFile->m_nRecvSize, szFileData, nFileDataLen);
            pLogFile->m_nRecvSize += nFileDataLen;

            // 通知窗口 回调函数 保留，待修改
            //g_Global.m_MsgHandle.OnMsgHandle(UM_GET_LOG_FILE_DATA, (WPARAM)(&device));
            g_Global.m_WebNetwork.ForwardDevceLogDownSche(device, pLogFile);
        }
    }
}

// 处理消防采集器返回通道触发状态
void	CCommandHandle::HandleGetAlarmState(const char*     pData,			// 数据区
                                            unsigned short	nLen,			// 数据长度
                                            CSection&       device,			// 设备
                                            bool            bResponse)		// 回应消防采集器
{
    if (!device.IsFireCollectorDevice())
    {
        return;
    }

    BYTE	channelCount	= pData[0];

    // 第一次搜索到消防采集器
    if (device.m_pFireCollector->GetChannelCount() == 0)
    {
        for (int i=0; i<channelCount; ++i)
        {
            CFireChannel fireChannel(i + 1);

            CHAR name[SEC_NAME_LEN] = {0};
            sprintf(name, "Channel %d", i + 1);
            fireChannel.SetName(name);

            device.m_pFireCollector->AddChannel(fireChannel);
        }

        g_Global.m_FireCollectors.WriteFile(TRUE);
    }
    else
    {
        int	nTriggerState = CharsToInt(&pData[1]);

        if (!device.m_pFireCollector->HasInitTriggerState() || device.m_pFireCollector->GetTriggerState() != nTriggerState)
        {
            CMyString strTip;
            strTip.Format("Recv Alarm %s : The Different State %s", device.GetIP(), nTriggerState == TRIG_STATE_OFF ? "Off" : "On");
            m_pNetwork->AddLog(strTip);

            CFireChannel *pChannels[FIRE_CHANNEL_COUNT] = {0};
            int	nTriggerChannesCount = device.m_pFireCollector->GetTriggerChannels(nTriggerState, pChannels);

            device.m_pFireCollector->SetTriggerState(nTriggerState);

            // 可以一次发送多个通道变化来
            for (int chn = 0; chn < nTriggerChannesCount; ++chn)
            {
                CFireChannel *pChannel = pChannels[chn];

                CMyString strTip;
                strTip.Format("Channel %d : %s", pChannel->GetID(), pChannel->GetTriggerState() == TRIG_STATE_OFF ? "Off" : "On");
                m_pNetwork->AddLog(strTip);

                            // 加到日志
                CMyString strLogContents;

                strLogContents.Format("%s:%s%d%s", CProtocol::GetDescriptionProSource(PRO_ALARM).C_Str(),"通道",pChannel->GetID(), \
                                        pChannel->GetTriggerState() == TRIG_STATE_OFF ? "停止" : "触发");
                g_Global.m_logTable.InsertLog(	CMyString(device.GetMac()),
                                            CMyString(device.GetName()),
                                            LT_ALARM,
                                            strLogContents);

                UINT	uSecCount = pChannel->GetSectionCount();

                if (WP_IS_CENTRALIZED)	// 集中模式
                {
                    if (pChannel->GetTriggerState() == TRIG_STATE_OFF) // 关闭
                    {
                        UINT	uCount		= 0;
                        UINT	pSecIndexs[MAX_SECTION_COUNT_FORMAL] = {0};

                        for (UINT i=0; i<uSecCount; ++i)
                        {
                            CSection* pSection = g_Global.m_Sections.GetSectionByMac(pChannel->GetSectionMac(i));

                            if (pSection != NULL && pSection->IsOnline())
                            {
                                // 如果大于0，则计数减1
                                if (pSection->GetTriggerCount() > 0)
                                {
                                    pSection->SetTriggerCount(0);

                                    pSecIndexs[uCount++] = pSection->GetID() - 1;
                                    m_pNetwork->m_CmdSend.CmdSetIdleStatus(*pSection);

                                    // 加到日志
                                    CMyString strLogContents;
                                    strLogContents.Format("%s:%s", CProtocol::GetDescriptionProSource(PRO_ALARM).C_Str(),"关闭");
                                    g_Global.m_logTable.InsertLog(	CMyString(pSection->GetMac()),
                                                                    CMyString(pSection->GetName()),
                                                                    LT_ALARM,
                                                                    strLogContents);
                                }
                            }
                        }

                        // 告警停止也加到任务队列，这样能保证最后才执行停止播放的操作，起到停止播放任务的作用
                        CPlayTask playTask(SOURCE_STOP, {""}, pSecIndexs, uCount);
                        g_Global.m_PlayQueue.PushPlayTask(playTask);
                    }
                    else	// 开启
                    {
                        CMyString strSound	= pChannel->GetSoundPathName();
                        CMyString strPathName	= g_Global.m_strHttpRootDir + strSound;
                        UINT	uCount		= 0;
                        UINT	pSecIndexs[MAX_SECTION_COUNT_FORMAL] = {0};

                        strPathName.Replace("amp;", "");

                        if (g_Global.m_SongTool.IsPathExist(strPathName) && g_Global.m_SongTool.IsStreamFormat(strPathName))
                        {
                            for (UINT i=0; i<uSecCount; ++i)
                            {
                                CSection* pSection = g_Global.m_Sections.GetSectionByMac(pChannel->GetSectionMac(i));

                                if (pSection != NULL && pSection->IsOnline())
                                {
                                    // 触发计数为0，而且不是告警音源，才会发送触发命令
                                    if (pSection->GetTriggerCount() == 0)
                                    {
                                        // 触发计数加1
                                        pSection->SetTriggerCount(1);

                                        pSecIndexs[uCount++] = pSection->GetID() - 1;

                                        // 记录触发的消防采集器的MAC
                                        pSection->SetTriggerFireColMac(device.m_netInfo.m_szMac);


                                        // 加到日志
                                        CMyString strLogContents;
                                        strLogContents.Format("%s:%s", CProtocol::GetDescriptionProSource(PRO_ALARM).C_Str(),"开启");
                                        g_Global.m_logTable.InsertLog(	CMyString(pSection->GetMac()),
                                                                    CMyString(pSection->GetName()),
                                                                    LT_ALARM,
                                                                    strLogContents);

                                    }
                                }
                            }

                            // 告警加入播放队列中
                            CPlayTask playTask(SOURCE_ALARM, {strPathName}, pSecIndexs, uCount);
                            g_Global.m_PlayQueue.PushPlayTask(playTask);
                        }
                        else
                        {
                            CMyString strTip;
                            strTip.Format("Alarm Sound is not exist : %s", strPathName.C_Str());
                            m_pNetwork->AddLog(strTip);
                        }
                    }
                }
                else// 分布模式
                {
                    int	nSecCount = pChannel->GetSectionCount();

                    for (int i=0; i<nSecCount; ++i)
                    {
                        CSection* pSection = g_Global.m_Sections.GetSectionByMac(pChannel->GetSectionMac(i));

                        if (pSection != NULL && pSection->IsOnline())
                        {
                            if (pChannel->GetTriggerState() == TRIG_STATE_OFF)	// 关闭
                            {
                                // 如果大于0，则置为0
                                if (pSection->GetTriggerCount() > 0)
                                {
                                    pSection->SetTriggerCount(0);
                                    m_pNetwork->m_CmdSend.CmdSetAlarmSwitch(*pSection,
                                        pChannel->GetID(),
                                        TRIG_STATE_OFF,
                                        pChannel->GetSoundPathName());
                                }
                            }
                            else // 开启
                            {
                                if (pSection->GetTriggerCount() == 0)
                                {
                                    pSection->SetTriggerCount(1);

                                    m_pNetwork->m_CmdSend.CmdSetAlarmSwitch(*pSection,
                                                                            pChannel->GetID(),
                                                                            TRIG_STATE_ON,
                                                                            pChannel->GetSoundPathName());

                                    // 记录触发的消防采集器的MAC
                                    pSection->SetTriggerFireColMac(device.m_netInfo.m_szMac);
                                }
                            }
                        }
                    }
                }
            }

            //通知WEB
            g_Global.m_WebNetwork.m_WebSend.WebSendFireCollectorInfo(NULL);
        }
    }

    if (bResponse)
    {
        m_pNetwork->m_CmdSend.CmdResponseAlarmStateChanged(device);
    }
}

// 处理消防采集器返回触发模式
void	CCommandHandle::HandleGetAlarmMode(	const char*	pData,				// 数据区
                                            unsigned short	nLen,				// 数据长度
                                            CSection& device)			// 设备
{
    if (!device.IsFireCollectorDevice())
    {
        return;
    }

    unsigned char	channelCount	= pData[0];
    bool	bWriteToFile	= FALSE;

    // 第一次搜索到消防采集器
    if (device.m_pFireCollector->GetChannelCount() == 0)
    {
        for (int i=0; i<channelCount; ++i)
        {
            CFireChannel fireChannel(i + 1);

            char name[SEC_NAME_LEN] = {0};
            sprintf(name, "Channel %d", i + 1);
            fireChannel.SetName(name);

            device.m_pFireCollector->AddChannel(fireChannel);
        }

        bWriteToFile = TRUE;
    }
    else
    {
        int	nTriggerMode = CharsToInt(&pData[1]);

        // 触发模式未初始化，或者发生改变
        if (!device.m_pFireCollector->HasInitTriggerMode()
            || device.m_pFireCollector->GetTriggerMode() != nTriggerMode)
        {
            device.m_pFireCollector->SetTriggerMode(nTriggerMode);
            bWriteToFile = TRUE;
        }
    }

    if (bWriteToFile)
    {
        //g_Global.m_FireCollectors.WriteFile(TRUE);
        g_Global.WriteXmlFile(FILE_FIRE_COLLECTOR);
    }
}

// 处理返回网络信息
void	CCommandHandle::HandleNetworkIPInfo(	const char*	pData,					// 数据区
                                            unsigned short	nLen)					// 数据长度
{
    //ERROR_LOG("HandleNetworkIPInfo");
    int		pos = 0;
    char	szMac[SEC_MAC_LEN] = {0}; 
    SetMac(szMac, &pData[pos]);
    pos += 6;
    //printf("szMac : %s\n", szMac);

    CSection* pDevice = GetOnlineDeviceByMac(szMac);

    if (pDevice == NULL)
    {
        return;
    }

    if (nLen == 6)	// 设置
    {
        // 设置成功后，设备后重启
        pDevice->SetRestartTime(CTime::GetCurrentTimeT().GetTime());
        m_pNetwork->DeviceOffline(*pDevice);
    }
    else	// 查询
    {
        pDevice->m_netInfo.m_IpAccessMode = pData[pos++];

        // 自动获取IP
        if (pDevice->m_netInfo.m_IpAccessMode == IP_ACCESS_MODE_DHCP)
        {
            pDevice->m_netInfo.ResetPropterty();
        }
        // 静态分配IP
        else
        {
            // IP地址
            unsigned char ipLen = pData[pos++];
            memset(pDevice->m_netInfo.m_szIP,0, MAX_IP_LEN);
            memcpy(pDevice->m_netInfo.m_szIP, &pData[pos], ipLen);
            pos += ipLen;

            // 子网掩码
            unsigned char subnetLen = pData[pos++];
            memset(pDevice->m_netInfo.m_szSubnetMask,0, MAX_IP_LEN);
            memcpy(pDevice->m_netInfo.m_szSubnetMask, &pData[pos], subnetLen);
            pos += subnetLen;

            // 网关
            unsigned char gatewayLen = pData[pos++];
            memset(pDevice->m_netInfo.m_szGateway, 0,MAX_IP_LEN);
            memcpy(pDevice->m_netInfo.m_szGateway, &pData[pos], gatewayLen);
            pos += gatewayLen;

            // DNS1
            unsigned char dns1Len = pData[pos++];
            memset(pDevice->m_netInfo.m_szDNS1, 0,MAX_IP_LEN);
            memcpy(pDevice->m_netInfo.m_szDNS1, &pData[pos], dns1Len);
            pos += dns1Len;

            // DNS2
            unsigned char dns2Len = pData[pos++];
            memset(pDevice->m_netInfo.m_szDNS2,0, MAX_IP_LEN);
            memcpy(pDevice->m_netInfo.m_szDNS2, &pData[pos], dns2Len);
            pos += dns2Len;
        }

        // 回调函数 保留，待修改
        //g_Global.m_MsgHandle.OnMsgHandle(UM_GET_IP_INFO, (LPARAM)pDevice);

        //g_Global.m_WebNetwork.m_WebQueues.HandleCommand(CMD_IP_INFO, pDevice->GetMac(), pData);
        g_Global.m_WebNetwork.ForwardDeviceNetInfo(NULL, *pDevice);
    }
}



// 处理返回网络信息
void	CCommandHandle::HandleNetworkModeInfo(	const char*	pData,					// 数据区
                                            unsigned short	nLen,                   // 数据长度
                                            CSection& device)					    // 设备
{
    int		pos = 0;
    char	szMac[SEC_MAC_LEN] = {0}; 

    int  networkMode = pData[pos++];
    char serverIP[64]={0};
    int    serverPort=0;
    char serverIP2[64]={0};
    int    serverPort2=0;
    if(networkMode == NETWORK_TCP)
    {
        int serverIP_len=pData[pos++];
        memcpy(serverIP,pData+pos,serverIP_len);
        pos+=serverIP_len;
        serverPort = (((BYTE)pData[pos])<<8)+((BYTE)pData[pos+1]);
        pos+=2;

        if(pos<nLen)    //如果当前pos比负载数据长度小，证明存在此字段
        {
            serverIP_len=pData[pos++];
            memcpy(serverIP2,pData+pos,serverIP_len);
            pos+=serverIP_len;
            serverPort2 = (((BYTE)pData[pos])<<8)+((BYTE)pData[pos+1]);
            pos+=2;
        }

        device.SetNetworkMode((NetworkMode)networkMode,serverIP,serverPort,serverIP2,serverPort2);
    }
    else
    {
        device.SetNetworkMode((NetworkMode)networkMode,serverIP,serverPort,serverIP2,serverPort2);
    }

    FORMAT("HandleNetworkModeInfo:Mode=%d,serverIP=%s,serverPort=%d,serverIP2=%s,serverPort2=%d\n",networkMode,serverIP,serverPort,serverIP2,serverPort2);

    g_Global.m_WebNetwork.ForwardDeviceNetModeInfo(NULL, device);
}

#if 0
// 处理9131/9134网络解码器的电源输出模式返回
void	CCommandHandle::HandlePowerOutputMode(	const char*	pData,				// 数据区
                                                unsigned short	nLen,				// 数据长度
                                                CSection& device)			// 设备
{
    if (!device.IsLoopDetectDevice())
    {
        return;
    }

    unsigned char	powerMode	= pData[0];
    unsigned short	timeout		= CharsToShort(&pData[1]);

    device.m_pPowerInfo->m_powerMode	= powerMode;
    device.m_pPowerInfo->m_timeout		= timeout;

    // 回调函数 保留，待修改
    //g_Global.m_MsgHandle.OnMsgHandle(UM_POWER_OUTPUT_MODE, (LPARAM)(&device));
    g_Global.m_WebNetwork.m_WebSend.WebResponsePowerOutputMode(device, NULL);
}

// 处理9131/9134网络解码器的回路检测状态返回（主机主动查询或者设备状态变化才会返回这条命令）
void	CCommandHandle::HandleSigalDetection(	bool	bLoop,				// 是否有回路检测功能
                                                bool	bSigal,				// 是否有信号
                                                CSection& device)			// 设备
{
    if (!device.IsLoopDetectDevice())
    {
        return;
    }

    if (bLoop && device.m_pPowerInfo != NULL)
    {
        bool bChanged = FALSE;

        // 第一次获取（默认为FALSE）
        if (!device.m_pPowerInfo->m_bLoop)
        {
            device.m_pPowerInfo->m_bLoop = bLoop;

            // 查询电源输出模式
            m_pNetwork->m_CmdSend.CmdPowerOutputMode(device);

            bChanged = TRUE;
        }

        // 信号检测有变化
        if (device.m_pPowerInfo->m_bSigal != bSigal)
        {
            device.m_pPowerInfo->m_bSigal = bSigal;

            bChanged = TRUE;
        }

        if (bChanged)
        {
            // 需要更新分区图标
            //g_Global.m_MsgHandle.OnMsgHandle(UM_DEVICE_UPDATE, (WPARAM)(&device), 0);

            // 状态改变告诉分控设备
            m_pNetwork->ForwardSectionsStatusToControlDevices(&device, NULL);
            g_Global.m_WebNetwork.ForwardSectionInfoToWeb(DEVICE_SECTION, &device, NULL);
        }
    }
}
#endif

// 处理返回查询EQ音效
void	CCommandHandle::HandleGetDeviceEq(	const char*		pData,		// 数据区
                                            unsigned short	nLen,       // 数据长度
                                            CSection&	device)			// 设备
{
    CDeviceEQ deviceEQ;
 
    char	szMac[SEC_MAC_LEN] = {0};
    int pos = 0;
    SetMac(szMac, pData+pos);
    pos+=6;
    if( memcmp( device.GetMac(),szMac,6) !=0)   //判断MAC是否一致,不一致则返回
    {

        return;
    }
    deviceEQ.m_eqMode	= (EqMode)pData[pos++];
    memcpy(deviceEQ.m_eqGain,pData+pos,10);

    if (device.m_DeviceEQ != deviceEQ)
    {
        device.m_DeviceEQ = deviceEQ;
        //g_Global.m_WebNetwork.m_WebSend.WebResponseDeviceEQ(device, NULL);    //不需要主动发送EQ信息给WEB，因为WEB每次进入音效设置都会主动查询。现在发送反而导致WEB自动弹出音效设置对话框。
    }
    //LOG(FORMAT("HandleGetDeviceEq:Section: %s,EQ_MODE=%d!\n", strlen(device.GetName()) > 0 ? device.GetUTFName().data():device.GetIP(),deviceEQ.m_eqMode), LV_INFO);
}


// 处理返回查询蓝牙信息
void	CCommandHandle::HandleGetDeviceBtInfo(	const char*		pData,		// 数据区
                                            unsigned short	nLen,       // 数据长度
                                            CSection&	device)			// 设备
{
    CBlueTooth deviceBT;
 
    char	szMac[SEC_MAC_LEN] = {0};
    int pos = 0;
    SetMac(szMac, pData+pos);
    pos+=6;
    if( memcmp( device.GetMac(),szMac,6) !=0)   //判断MAC是否一致,不一致则返回
    {
        return;
    }
    int btName_len=pData[pos++];
    memset(deviceBT.m_BlueTooth_Name,0,sizeof(deviceBT.m_BlueTooth_Name));
    memcpy(deviceBT.m_BlueTooth_Name,pData+pos,btName_len);
    pos+=btName_len;
    deviceBT.m_BlueTooth_encryption=pData[pos++];
    memset(deviceBT.m_BlueTooth_Pin,0,sizeof(deviceBT.m_BlueTooth_Pin));
    memcpy(deviceBT.m_BlueTooth_Pin,pData+pos,4);
    pos+=4;

    if (device.m_Bluetooth != deviceBT)
    {
        device.m_Bluetooth = deviceBT;

        g_Global.m_WebNetwork.m_WebSend.WebResponseDeviceBTinfo(device, NULL);
    }
    LOG(FORMAT("HandleGetDeviceBtInfo:Section: %s,name=%s,len=%d,encryption=%d,pin=%s!\n", strlen(device.GetName()) > 0 ? device.GetUTFName().data():device.GetIP(),deviceBT.m_BlueTooth_Name, \
    btName_len,deviceBT.m_BlueTooth_encryption,deviceBT.m_BlueTooth_Pin), LV_INFO);

}


// 处理返回查询电源时序器信息
void	CCommandHandle::HandleGetSequencePowerInfo(	const char*		pData,		// 数据区
                                            unsigned short	nLen,       // 数据长度
                                            CSection&	device)			// 设备
{
    CSequencePower seqPwr;
    //判断是查询还是设置的应答
    int pos=0;
    bool bSet = ((BYTE)pData[pos++]) == OPER_SET ? TRUE:FALSE;
    seqPwr.SetControlMode(pData[pos++]);
    unsigned short openDelay = (((BYTE)pData[pos++])<<8)+((BYTE)pData[pos++]);
    seqPwr.SetRealChannelCnt(pData[pos++]);
    seqPwr.SetAllSwitchState( ( ((BYTE)pData[pos++])<<8)+(BYTE)pData[pos++] );

    printf("SeqPwr:mode=%d,channelNum=%d,switchStatus=0x%x\n",seqPwr.GetControlMode(),seqPwr.GetRealChannelCnt(),seqPwr.GetAllSwitchState());

    if( *(device.m_pSequencePower) != seqPwr  )
    {
        bool update_file=false;
        printf("SeqPwr:not equal...\n");
        //逐一判断
        if( device.m_pSequencePower->GetControlMode() != seqPwr.GetControlMode() )
        {
            update_file=true;
            device.m_pSequencePower->SetControlMode(seqPwr.GetControlMode());
        }
        if( device.m_pSequencePower->GetRealChannelCnt() != seqPwr.GetRealChannelCnt() )
        {
            update_file=true;
            device.m_pSequencePower->SetRealChannelCnt(seqPwr.GetRealChannelCnt());
        }
        if( device.m_pSequencePower->GetAllSwitchState() != seqPwr.GetAllSwitchState() )
        {
            device.m_pSequencePower->SetAllSwitchState(seqPwr.GetAllSwitchState());
        }
        
        if(update_file)
        {
            g_Global.m_SequencePower.WriteFile(TRUE);
        }

        //变化后通知WEB
        g_Global.m_WebNetwork.m_WebSend.WebSendSequencePowerInfo(NULL);
    }

    device.m_pSequencePower->SetInitOK(TRUE);
}


// 处理返回查询设备子音量信息
void	CCommandHandle::HandleGetSubVolumeInfo(	const char*		pData,		// 数据区
                                            unsigned short	nLen,       // 数据长度
                                            CSection&	device)			// 设备
{
    //判断是查询还是设置的应答
    int pos=0;
    bool bSet = ((BYTE)pData[pos++]) == OPER_SET ? TRUE:FALSE;
    BYTE subVol = (BYTE)pData[pos++];
    BYTE auxVol = 100;
    if(pos<nLen)    //如果当前pos比负载数据长度小，证明存在本地音量字段
    {
        auxVol = (BYTE)pData[pos++];
    }
    if(subVol<=100 && auxVol<=100)
    {
        device.SetSubVolume(subVol);
        device.SetAuxVolume(auxVol);
        //通知WEB
        string buf = CWebProtocol::CmdResponseSetSubVolume(bSet,subVol,auxVol);
        g_Global.m_WebNetwork.m_WebSend.ForwardDataToWeb(NULL, buf, false);
    }
}
#if SUPPORT_REMOTE_CONTROLER
// 处理远程遥控器按键
void	CCommandHandle::HandleRemoteControlerKey(	const char*		pData,		// 数据区
                                            unsigned short	nLen,       // 数据长度
                                            CSection&	device)			// 设备
{
    if( !device.IsRemoteControlerDevice() )
        return;
    int pos=0;
    BYTE keyID = (BYTE)pData[pos++];
    if(keyID>=1 && keyID<=12)
    {
        // 加到日志
        CMyString strLogContents;
        strLogContents.Format("keyID=%d",keyID);
        g_Global.m_logTable.InsertLog(	CMyString(device.GetMac()),
                                        CMyString(device.GetName()),
                                        LT_RUNNING_STATE,
                                        strLogContents);

        //todo 响应按键
        int keyEvent = device.m_pRemoteControler->GetKeyEvent(keyID);
        if(keyEvent == 0)
        {
            return;
        }

        //获取正在执行的所有任务

        if(keyEvent<=20)
        {
            g_Global.m_PlayQueue.m_SongPlayer.SetRemoteControlerTask(device.GetMac(),keyEvent);
        }
        else
        {
            int taskID=keyEvent-20;
            //传进来的taskId比当前任务数还多，代表有错误
            if(taskID>device.m_pRemoteControler->GetTaskCnt())
            {
                return;
            }
            CRemoteControlTask *lpRemoteTask = device.m_pRemoteControler->lpGetTaskDetail(taskID);
            //todo 如何判断该TASK是否有效?需要加个成员函数判断

            if(lpRemoteTask!=NULL)
            {
                vector<CMyString> vecMac;
                unsigned int uSecCount = lpRemoteTask->GetSelectedSections(vecMac);
                unsigned int pSecIndexs[MAX_SECTION_COUNT_FORMAL] = {0};

                // 获取到分区ID
                unsigned int uCount = 0;
                for (unsigned int i=0; i<uSecCount; ++i)
                {
                    CSection* pSection = g_Global.m_Sections.GetSectionByMac(CMyString(vecMac[i]));

                    if (pSection != NULL && pSection->IsOnline())
                    {
                        pSecIndexs[uCount++] = pSection->GetID() - 1;

                        //停止，重置最近音源
                        if(pSection->GetProSource() == PRO_LOCAL_PLAY || pSection->GetProSource() == PRO_TIMING || CProtocol::IsAudioCollectorSrc(pSection->GetProSource()))
                        {
                            pSection->m_PlayedRecently.m_nRecentSrc = PRO_IDLE;
                        }
                        pSection->m_PlayedRecently.m_bSongTimerEndResumeAc = PRO_IDLE;
                    }

                    if(uCount >= MAX_SECTION_COUNT_FORMAL)
                        break;
                }

                CPlayTask playTask(SOURCE_REMOTE_PLAY, {""}, pSecIndexs, uCount,"admin",lpRemoteTask->GetPlayMode(),-1,-1,lpRemoteTask->GetVolume());
                stRemoteTaskInfo taskInfo={device.GetMac(),taskID};
                playTask.SetRemoteControlTask(taskInfo);
                g_Global.m_PlayQueue.PushPlayTask(playTask);
            }
        }
    }
}
#endif

#if 0
// 处理返回分区器状态
void	CCommandHandle::HandleGetSplitterStatus(unsigned char	status,			// 状态
                                                CSection&	device)			// 设备
{
    // 回调函数 保留，待修改
    //g_Global.m_MsgHandle.OnMsgHandle(UM_GET_SPLITTER_STATUS, (WPARAM)(&device), status);
    g_Global.m_WebNetwork.m_WebSend.WebResponseSplitterStatus(device.GetMac(), status, NULL);
}

// 处理返回无线MIC的状态
void	CCommandHandle::HandleGetDeviceMicStatus(const char*	pData,      // 数据区
                                                 unsigned short	nLen,		// 数据长度
                                                CSection&        device)   // 设备
{
    int volume	= (unsigned char)pData[0];
    int power   = (unsigned char)pData[1];
    int channel	= (unsigned char)pData[2];

    g_Global.m_WebNetwork.m_WebSend.WebResponseMicStatus(device.GetMac(), volume, power, channel, NULL);
}

// 处理返回EMC状态
void	CCommandHandle::HandleGetEmcStatus(	unsigned char		status,		// 状态
                                            CSection&	device)				// 设备
{
    // 回调函数 保留，待修改
    //g_Global.m_MsgHandle.OnMsgHandle(UM_GET_EMC_STATUS, (WPARAM)(&device), status);
    //g_Global.m_WebNetwork.m_WebQueues.HandleCommand(UM_GET_EMC_STATUS, device.GetMac());

    g_Global.m_WebNetwork.m_WebSend.WebResponseEmcStatus(device.GetMac(), (bool)status, NULL);
}

// 处理返回查询混音模式
void	CCommandHandle::HandleGetDeviceMixing(	const char*	pData,				// 数据区
                                                unsigned short	nLen,			// 数据长度
                                                CSection& device)				// 设备
{
    CDeviceMixing deviceMix;
    deviceMix.m_nLineChn	= (unsigned char)pData[0];
    deviceMix.m_nAUX		= (unsigned char)pData[1];
    deviceMix.m_nMixing		= (unsigned char)pData[2];

    // AUX混合DAC输出
    if (deviceMix.m_nMixing	 == AUX_AND_DAC)
    {
        deviceMix.m_nDAC	= (unsigned char)pData[3];
    }

    if (device.m_DeviceMixing != deviceMix)
    {
        device.m_DeviceMixing = deviceMix;

        // 回调函数 保留，待修改
        //g_Global.m_MsgHandle.OnMsgHandle(UM_GET_MIXING_MODE, (WPARAM)(&device), 0);

        //g_Global.m_WebNetwork.m_WebQueues.HandleCommand(CMD_MIXING_MODE, device.GetMac(), pData);
        g_Global.m_WebNetwork.m_WebSend.WebResponseDeviceMixing(device, NULL);
    }

}
#endif

// 处理返回终端网速带宽
void CCommandHandle::HandleGetDeviceBW(const char *pData, unsigned short nLen, CSection &device)
{
    int nNetType = pData[0];
    double dBandWidth = SetDouble(&pData[1], 5);
    double dDelayed = SetDouble(&pData[6], 5);
    double dLossRate = SetDouble(&pData[11], 5);

    //device.m_netInfo.SetBWPropterty(nNetType, dBandWidth, dDelayed, dLossRate);
    g_Global.m_WebNetwork.ForwardNetQuality(device.GetMac(), device.GetName(), nNetType, dBandWidth, dDelayed, dLossRate);
}

// 终端启动通知
void CCommandHandle::HandleFirstBoot(const char *pData, unsigned short nLen, CSection &device)  // 设备
{
    unsigned short	pos = 0;
    time_t tNow = CTime::GetCurrentTimeT().GetTime();

    int bootType=0; //系统启动类型（1：开门狗重启 2：系统复位 3：断电重启)
    if(pos<nLen)    //如果当前pos比负载数据长度小，证明存在此字段
    {
        bootType = pData[pos++];
    }
    
    //如果之前处于升级状态，这里把它置为非升级
    device.SetUpgrading(false);
    #if 0
    CMyString strTip;
    strTip.Format(("Device %s: is on line."), strlen(device.GetName()) == 0 ? device.GetIP() : device.GetName());
    m_pNetwork->AddLog(strTip);
    #endif
    
    if(bootType!=0)
    {
        LOG(FORMAT("Device %s: is first boot,type=%d\n", strlen(device.GetName()) == 0 ? device.GetIP() : device.GetName(),bootType), LV_ERROR);

        CMyString strLogContents;
        strLogContents.Format("Boot Type=%d",  bootType);
        g_Global.m_logTable.InsertLog(CMyString(device.GetMac()),
            strlen(device.GetName()) == 0 ? device.GetIP() : device.GetName(),
            LT_RUNNING_STATE,
            strLogContents);
    }
    else
    {
        LOG(FORMAT("Device %s: is first boot\n", strlen(device.GetName()) == 0 ? device.GetIP() : device.GetName()), LV_ERROR);
    }

    // 恢复之前播放的分区
    #if 0
    if (device.IsAudioCollectorDevice())
    {
        CSection*	pAudioCol = &device;
        UINT		pIndexs[MAX_SECTION_COUNT_FORMAL] = {0};
        UINT		uCount = g_Global.m_Sections.GetSectionsByPreSource(pAudioCol->m_pAudioCollector->GetSourceID(), pIndexs);

        if (uCount > 0)
        {
            CHAR szBuf[MAX_BUF_LEN] = {0};
            int	len	= CProtocol::SetAudioInfo(szBuf, pAudioCol->m_pAudioCollector);

            // 发到音频采集器
            m_pNetwork->SendData(szBuf, len, *pAudioCol, pAudioCol->GetIP(), UDP_PORT, 1);
            m_pNetwork->m_CmdSend.AddCommand(szBuf, len, device);

            // 发到原来的分区
            for (UINT i=0; i<uCount; ++i)
            {
                CSection& section = g_Global.m_Sections.GetSection(pIndexs[i]);

                // 只有处于空闲状态而且持续时间不长（5分钟）的情况下才发送命令
                if (section.IsIdle() && (tNow - section.GetIdleTime() < 5*60))
                {
                    m_pNetwork->SendData(szBuf, len, section, section.GetIP(), UDP_PORT, 1);
                    m_pNetwork->m_CmdSend.AddCommand(szBuf, len, device);
                }
            }
        }
    }
    else if (device.IsSectionDevice())
    {
        // 音频采集器(分区掉线时间小于40S，走这里)
        if (CProtocol::IsAudioCollectorSrc(device.GetPreSource())
            && device.IsIdle() && (tNow - device.GetIdleTime() < 5*60))
        {
            CSection *pAudioCol = g_Global.m_AudioCollectors.GetSectionBySrcID(device.GetPreSource());

            if (pAudioCol != NULL && pAudioCol->IsOnline())
            {
                // 告诉音频采集器与分区设备恢复播放
                g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(device, pAudioCol->m_pAudioCollector);
                g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(*pAudioCol, pAudioCol->m_pAudioCollector);
            }
            else if(pAudioCol != NULL && pAudioCol->IsLocal())
            {
                g_Global.m_AuxPlay.StartAUXWorking();
                g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(device, pAudioCol->m_pAudioCollector);
            }
        }
    }
    else if (device.IsFireCollectorDevice())
    {
        device.m_pFireCollector->SetTriggerState(TRIG_STATE_OFF);
    }
    #else
    if (device.IsFireCollectorDevice())
    {
        device.m_pFireCollector->SetTriggerState(TRIG_STATE_OFF);
    }
    #endif
}



/****************************************************************/

// 处理分控设备发过来的选中分区
void	CCommandHandle::HandleSelectedSections(	unsigned char	selectedSecID,		// 选中分区的标识
                                                const char*     pData,				// 数据区
                                                unsigned short	nLen,				// 数据长度
                                                CSection& device)			// 设备
{
    device.m_pSelectedSections->SetID(selectedSecID);

    int		pos	= 0;
    char	szMac[SEC_MAC_LEN] = {0};
    device.m_pSelectedSections->SetPackCount(pData[pos++]);
    device.m_pSelectedSections->SetPackID(pData[pos++]);

    // 第一个包要把之前的数据清除掉
    if (device.m_pSelectedSections->GetPackID() == 1)
    {
        device.m_pSelectedSections->Clear();
    }

    unsigned char secCount = pData[pos++];

    for (int i=0; i<secCount; ++i)
    {
        SetMac(szMac, &pData[pos]);
        device.m_pSelectedSections->Add(CMyString(szMac));
        pos += 6;
    }

    // 回应给分控设备
    m_pNetwork->m_CmdSend.CmdResponseSelectedSections(device, selectedSecID, device.m_pSelectedSections->GetPackID());
}

#define RESULT_PLAY_SUCCESS		0x01
#define RESULT_ERROR_FORMAT		0x02
#define RESULT_NOT_EXIST		0x03
#define RESULT_ERROR_PARSING	0x04
#define RESULT_FAIL				0x0F	// 失败

// 处理分控设备发过来的点播请求
void	CCommandHandle::HandleRequestPlaySource(const char*	pData,		// 命令内容
                                                unsigned short	nLen,	// 命令长度
                                                CSection& device)		// 设备
{
    printf("HandleRequestPlaySource\n");
    unsigned char	selectedSecID = pData[3];	// 选中分区的标识
    int             pos         = 8;			// 数据开始位置
    unsigned char	src         = pData[pos++];
    bool            bSuccess	= FALSE;
    unsigned char	result		= RESULT_FAIL;

    // 集中模式
    if (WP_IS_CENTRALIZED && device.m_pSelectedSections->CanHandleCmd(selectedSecID))
    {
        CMyString strPathName;
        unsigned char	source;
        int		nListIndex = -1;
        int		nSongIndex = -1;

        printf("selectedSecID : %d\n", (int)selectedSecID);
        // 播放歌曲
        if (src == 0x01)
        {
            unsigned char	idLen			= pData[pos++];
            char	szListID[STR_MAX_PATH]		= {0};
            char	szSongName[STR_MAX_PATH]	= {0};	// 带扩展名的歌曲名称
            unsigned char	nameLen;

            memcpy(szListID, &pData[pos], idLen);
            pos += idLen;

            nameLen = pData[pos++];
            memcpy(szSongName, &pData[pos], nameLen);

            //CMyString strSongName = ConvertUTF8toGB2312(szSongName, nameLen);
            CMyString strSongName(szSongName);
            LOG(FORMAT("szSongName %s", szSongName), LV_ERROR);

            // 播放歌曲，参考m_paneWork里的，但不能有提示信息
            CSong* pSong = g_Global.m_PlayList.FindSongByListAndName(CMyString(szListID), strSongName, nListIndex, nSongIndex);

            if (pSong == NULL || !pSong->IsExist())
            {
                result = RESULT_NOT_EXIST;
            }
            else if (!pSong->IsStreamFormat())
            {
                result = RESULT_ERROR_FORMAT;
            }
            else
            {
                strPathName = g_Global.m_strHttpRootDir + pSong->GetPathName();
                source = SOURCE_PLAY;

                result = RESULT_PLAY_SUCCESS;
            }
        }
        // 播放钟声
        else if (src == 0x02)
        {
            CMyString strBell = ("");
            g_Global.m_IniConfig.GetValue(CONFIG_FILE_SECTION_SECTION, CONFIG_FILE_ITEM_BELL, strBell);
            strPathName = g_Global.m_strHttpRootDir + strBell;

            if (strBell == ("") || !g_Global.m_SongTool.IsPathExist(strPathName))
            {
                result = RESULT_NOT_EXIST;
            }
            else if (!g_Global.m_SongTool.IsStreamFormat(strPathName))
            {
                result = RESULT_ERROR_FORMAT;
            }
            else
            {
                source = SOURCE_BELL;
                result = RESULT_PLAY_SUCCESS;
            }
        }

        if (result == RESULT_PLAY_SUCCESS)
        {

            // 加到日志
            CMyString strLogContents;
            strLogContents.Format("%s:%s(%s)", CProtocol::GetDescriptionProSource(PRO_LOCAL_PLAY).C_Str(),
                                                        CProtocol::GetDescriptionDevice(device.GetDeviceModel()).C_Str(),
                                                        device.GetMac());
            g_Global.m_logTable.InsertLog(	CMyString(device.GetMac()),
                                            CMyString(device.GetName()),
                                            LT_PLAY_PROGRAM,
                                            strLogContents);

            unsigned int uSecCount = device.m_pSelectedSections->GetCount();
            unsigned int pSecIndexs[MAX_SECTION_COUNT_FORMAL] = {0};

            // 获取到分区ID
            unsigned int uCount = 0;
            for (unsigned int i=0; i<uSecCount; ++i)
            {
                CSection* pSection = g_Global.m_Sections.GetSectionByMac(device.m_pSelectedSections->GetAt(i));

                if (pSection != NULL && pSection->IsOnline())
                {
                    pSecIndexs[uCount++] = pSection->GetID() - 1;
                }
            }

            // 分控的请求加入播放队列
            CPlayTask playTask(source, {strPathName}, pSecIndexs, uCount, device.GetUserAccount(),0,nListIndex, nSongIndex);
            if (!g_Global.m_PlayQueue.PushPlayTask(playTask))
            {
                result = RESULT_ERROR_PARSING;
            }

        }

        bSuccess = TRUE;
    }

    if (bSuccess)
    {
        m_pNetwork->m_CmdSend.CmdResponsePlaySource(device, device.m_pSelectedSections->GetID(), result);
    }
}

// 处理分控设备的设置播放模式
void	CCommandHandle::HandleSetPlayMode(	const char*		pData,				// 命令全部内容
                                            int			nLen,				// 命令长度
                                            CSection&	device)				// 设备
{
    unsigned char	dataPos		= 8;
    unsigned char	playMode	= pData[dataPos+1];		// 播放模式
    bool	bSuccess	= FALSE;

    // 集中模式，和主机主动设置一样的处理
    if (WP_IS_CENTRALIZED)
    {
        //::SendMessage(g_Global.m_hWndFrame, UM_PLAY_MODE_CHANGED, (WPARAM)playMode, 0);
        // 回调函数 保留，待修改
        //g_Global.m_MsgHandle.OnMsgHandle(UM_PLAY_MODE_CHANGED, (WPARAM)playMode, 0);


        // 播放模式不一样
        if(device.GetUserAccount() == SUPER_USER_NAME)
        {
            if (g_Global.m_PlayList.GetPlayMode() != playMode)
            {
                g_Global.m_PlayList.SetPlayMode((PlayMode)playMode);
                g_Global.m_IniConfig.SetValue(CONFIG_FILE_SECTION_SECTION, CONFIG_FILE_ITEM_PLAY_MODE, playMode);
                g_Global.m_IniConfig.Write();

                g_Global.m_Users.SetUserPlayMode(SUPER_USER_NAME,(PlayMode)playMode);
                vector<string> vecUID;
                g_Global.m_Users.GetUIDArrayByAccount(SUPER_USER_NAME,vecUID);
                if(vecUID.size()>1)        //admin账户有两个uuid，默认添加一个超级权限uuid
                {
                    //默认的map是按照key值从小到大排序。所以此处不能确认到底是哪个
                    int real_uuid_index=0;
                    if(vecUID[0] == GUID_KEY)
                    {
                        real_uuid_index=1;
                    }
                    else
                    {
                        real_uuid_index=0;
                    }
                    LPCWebSection websection=g_Global.m_WebSections.GetWebSectionBySocketID(vecUID[real_uuid_index]);
                    if(websection!=NULL)
                    {
                        //printf("PlayMode change to %d,Notify websection...",playMode);
                        g_Global.m_WebNetwork.ForwardPlayMode(websection, playMode); // 转发到WEB客户端
                    }
                }
                // 集中模式
                if (WP_IS_CENTRALIZED)
                {
                    g_Global.m_Network.DeviceChangePlayMode((PlayMode)playMode);
                }
            }
        }
        else
        {
            g_Global.m_Users.SetUserPlayMode(device.GetUserAccount(),(PlayMode)playMode);

            string uid=g_Global.m_Users.GetUIDByAccount(device.GetUserAccount());
            LPCWebSection websection=g_Global.m_WebSections.GetWebSectionBySocketID(uid);
            if(websection!=NULL)
            {
                //printf("PlayMode change to %d,Notify websection...",playMode);
                g_Global.m_WebNetwork.ForwardPlayMode(websection, playMode); // 转发到WEB客户端
            }
        }

        bSuccess = TRUE;
    }
    // 分布模式，要根据分控设备选中的分区去下发播放模式
    else
    {
        bSuccess = m_pNetwork->m_CmdSend.CmdForwardControlDevice(pData, nLen, device);
    }

    if (bSuccess)
    {
        // 回应分控设备
        m_pNetwork->m_CmdSend.CmdResponsePlayMode(device);
    }
}

// 处理请求播放节目源返回结果
void	CCommandHandle::HandleNotifyStreamSource(	unsigned char	result,			// 结果
                                                    CSection& device)		// 设备
{
    if (result == 0x02)	// 拒绝播放，停止该分区的播放任务
    {
        //LOG("拒绝播放，停止该分区的播放任务", LV_ERROR);
        //NOTIFY("ip : %s, %d - %d", device.GetIP(), device.GetPreSource(), device.GetProSource());

        CMyString strLog;
        strLog.Format("%s 拒绝播放, preSrc = %s, src = %s, proName = %s",
                device.GetName(), (CProtocol::GetDescriptionProSource(device.GetPreSource())).C_Str(),
                (CProtocol::GetDescriptionProSource(device.GetProSource())).C_Str(), device.GetProName());
        g_Global.m_Network.AddLog(strLog);
        #if 0  //已经加入log并打印，无需再打印一次
        NOTIFY(strLog.C_Str());
        #endif
        if(CProtocol::IsAudioPlayStreamSrc(device.GetProSource()) || CProtocol::IsIdleSrc(device.GetProSource()) || CProtocol::IsLocalSrc(device.GetProSource()))
        {
            g_Global.m_PlayQueue.StopSectionPlaySource(&device);
        }
    }
    else if(result == 0x01)    // 设备接受播放 CS 2019-4-25 （暂停播放功能）恢复发送音频流
    {
        CSongUnit *pSongUnit = g_Global.m_PlayQueue.m_SongPlayer.GetSongUnitByPlayID(device.GetPlayID());

        if(pSongUnit != NULL && pSongUnit->GetPlayStatus() == SPS_PAUSE_AUTO)
        {
            g_Global.m_PlayQueue.m_SongPlayer.SetPlayStatus(device.GetPlayID(), SPS_PLAY);     // 让音频流恢复发送

            if(device.GetPlayStatus() == PS_PAUSE)
            {
                device.SetPlayStatus(PS_PLAY);

                // 如果分区状态改变，则转发给分控设备
                m_pNetwork->ForwardSectionsStatusToControlDevices(&device, NULL);
                g_Global.m_WebNetwork.ForwardSectionInfoToWeb(DEVICE_SECTION, &device, NULL);
            }
        }
        
    }
    #if SUPPORT_LOCAL_SONG_CHECK
    else if(result == 0x03)    // 设备存在本地歌曲文件，停止发流
    {
        if(device.GetDeviceModel() == MODEL_IP_SPEAKER_D || device.GetDeviceModel() == MODEL_IP_SPEAKER_E || device.GetDeviceModel() == MODEL_IP_SPEAKER_F || device.GetDeviceModel() == MODEL_AUDIO_MIXER_DECODER\
            || device.GetDeviceModel() == MODEL_IP_SPEAKER_G)
        {
            if(!device.GetIsExistLocalSong())
            {
                #if 0
                CMyString strLogContents;
                ProgramSource proSource = PRO_LOCAL_PLAY;
                CMyString     proMediaName;
 
                CSongUnit *pSongUnit = g_Global.m_PlayQueue.m_SongPlayer.GetSongUnitByPlayID(device.GetPlayID());
                if(pSongUnit != NULL)
                {
                    switch(pSongUnit->GetSource())
                    {
                        case SOURCE_PLAY:
                        proSource = PRO_LOCAL_PLAY;
                        break;
                        case SOURCE_TIMER:
                        proSource = PRO_TIMING;
                        break;
                        case SOURCE_ALARM:
                        proSource = PRO_ALARM;
                        break;
                    }

                    proMediaName = pSongUnit->GetPathName();
                    //找到最后一个/
                    int pos = proMediaName.ReverseFind('/');
                    if(pos>=0)
                    {
                        proMediaName=proMediaName.Mid(pos+1);
                    }

                    strLogContents.Format(("(缓存)%s:%s"), CProtocol::GetDescriptionProSource((ProgramSource)proSource).C_Str(), proMediaName.C_Str());
                }
                //网络点播:ring.mp3
                if(strLogContents.GetLength()>0)
                {
                    g_Global.m_logTable.InsertLog(CMyString(device.GetMac()),
                                                CMyString(device.GetName()),
                                                LT_PLAY_PROGRAM,
                                                strLogContents);
                }
                #endif
                device.SetIsExistLocalSong(true);
            }
        }
    }
    #endif
}

// 处理请求播放音频采集音源
void CCommandHandle::HandleSetAudioCollectSource(unsigned char result, CSection &device)
{
    if (result == 0x02)	// 拒绝播放，停止该分区的播放任务
    {
        LOG("拒绝播放，停止该分区的音频采集音源", LV_ERROR);
        NOTIFY("ip : %s, %d - %d", device.GetIP(), device.GetPreSource(), device.GetProSource());
        if(CProtocol::IsAudioCollectorSrc(device.GetProSource()) || CProtocol::IsIdleSrc(device.GetProSource()) || CProtocol::IsLocalSrc(device.GetProSource()))
        {
            g_Global.m_PlayQueue.StopSectionPlaySource(&device);
        }
    }
    //20220721 新增result=0x03,代表终端接受定时音频采集，用于后面的定时恢复
    //20230914 新增result=0x04,代表终端接受触发音频采集
    if (result == 0x03)	// 接受定时音频采集
    {
        device.m_PlayedRecently.m_bAcSourceInTiming = true;
        device.m_PlayedRecently.m_bAcSourceInTrigger = false;
    }
    else if(result == 0x04) //接受触发音频采集
    {
        device.m_PlayedRecently.m_bAcSourceInTrigger = true;
        device.m_PlayedRecently.m_bAcSourceInTiming = false;
    }
    else
    {
        device.m_PlayedRecently.m_bAcSourceInTiming = false;
        device.m_PlayedRecently.m_bAcSourceInTrigger = false;
    }
}



/*
// 4.78  分控设备向主机上报分区对讲
void CCommandHandle::HandleReportTalk(const char* pData,       // 命令内容
                                      unsigned short nLen,     // 命令长度
                                      CSection &device)        // 分区

{
    char szMac1[SEC_MAC_LEN] = {0};
    char szMac2[SEC_MAC_LEN] = {0};

    //memcpy(szMac1, pData, sizeof(szMac1));
    //memcpy(szMac2, &pData[sizeof(szMac1)], sizeof(szMac2));
    SetMac(szMac1, &pData[0]);
    SetMac(szMac2, &pData[6]);

    // 通知分区对讲
    CSection* pMainCaller = g_Global.m_Sections.GetSectionByMac(szMac1);    // 主叫方
    CSection* pCaller = g_Global.m_Sections.GetSectionByMac(szMac1);        // 被叫方

    if((pMainCaller->IsSipDevice() && pMainCaller->IsOnline()) &&
       (pCaller->IsSipDevice() && pCaller->IsOnline()))
    {
        g_Global.m_Voip.m_vHandle.HandleTalkback(pMainCaller->m_SipInfo.m_szAccount,
                                                 pCaller->m_SipInfo.m_szAccount);
    }

    // 回复分控设备 success or failed 待处理
    //m_pNetwork->m_CmdSend.CmdResponeTalk(device);
    LOG("talk is recv", LV_INFO);
    printf("szMac1:%s  \n szMac2", szMac1, szMac2);

}


// 4.77 处理 主机下发监控设备的信息回复
void CCommandHandle::HandleControlResponeMonitorInfo(const char* pData,         // 命令内容
                                                     unsigned short nLen,       // 命令长度
                                                     CSection& device)          // 分区
{
    //puts(" 4.77 主机下发监控设备的信息回复");
    //printf("monitor info recv, IP: %s\n",device.GetIP());
}


// 4.79 主机下发监控设备的上报事件的信息回复
void CCommandHandle::HandleControlResponMonitorEvent(const char* pData,         // 命令内容
                                                     unsigned short nLen,       // 命令长度
                                                     CSection& device)          // 分区
{
    //puts(" 4.79 主机下发监控设备的上报事件的信息回复");
    //printf("monitor events recv, IP: %s\n",device.GetIP());
}

*/

/**************************************************************/

// 处理获取SIP状态命令
void	CCommandHandle::HandleSipGetStatus(	unsigned char	status,					// SIP状态
                                            CSection& device)				// 设备
{
    if (device.m_SipInfo.m_nSipStaus != (SipStatus)status)
    {
        LOG(FORMAT("------------recv from %s : extern = %s status = %d", device.GetName(), device.m_SipInfo.m_szAccount, status), LV_INFO);

        device.m_SipInfo.m_nSipStaus = (SipStatus)status;

        g_Global.m_WebNetwork.ForwardSipStatus(device, NULL);
    }
}

// 处理获取SIP账号登录信息命令
void	CCommandHandle::HandleSipGetLogInfo(const char*	pData,				// 数据区
                                            unsigned short	nLen,			// 数据长度
                                            CSection& device)				// 设备
{
    char	szServerAddr[MAX_ADDR_LEN]	= {0};
    int		nServerPort					= 0;
    char	szAccount[SEC_NAME_LEN+1]	= {0};
    char	szPassword[SEC_NAME_LEN+1]	= {0};
    unsigned char	pos	= 0;

    //SIP注册开关
    int isEnable=pData[pos++];

    //Output输出音量
    int outputVol=pData[pos++];

    //MIC输入增益，暂未用到
    int micLevel=pData[pos++];

    // 服务器地址
    int addrLen = pData[pos++];
    memcpy(szServerAddr, &pData[pos], addrLen > MAX_ADDR_LEN ? MAX_ADDR_LEN : addrLen);
    pos += addrLen;

    // 端口
    nServerPort = CharsToShort(&pData[pos]);
    pos += 2;

    // 账号
    int accLen = pData[pos++];
    accLen = (accLen > SEC_NAME_LEN ? SEC_NAME_LEN : accLen);
    memcpy(szAccount, &pData[pos], accLen);
    szAccount[accLen] = 0;
    pos += accLen;

    // 密码
    int pwdLen = pData[pos++];
    pwdLen = (pwdLen > SEC_NAME_LEN ? SEC_NAME_LEN : pwdLen);
    memcpy(szPassword, &pData[pos], pwdLen);
    szPassword[pwdLen] = 0;
    pos += pwdLen;

    //协议
    //需要判断有没有（旧版本没有协议字段）
    int nServerProtocol = SIP_TRANSFER_PROTOCOL_UDP;
    if(pos<nLen)    //如果当前pos比负载数据长度小，证明存在此字段
    {
        nServerProtocol = pData[pos++];
    }

    //判断是否改变
    if(device.m_SipInfo.m_bEnableSIP != isEnable ||\
        device.m_SipInfo.m_nSipOutputVol != outputVol ||\
        device.m_SipInfo.m_nSipMicLevel != micLevel ||\
        strcmp(device.m_SipInfo.m_szAccount, szAccount) ||\
        strcmp(device.m_SipInfo.m_szPassword, szPassword) ||\
        strcmp(device.m_SipInfo.m_szServerAddr, szServerAddr) ||\
        device.m_SipInfo.m_nServerPort != nServerPort ||\
        device.m_SipInfo.m_nServerProtocol != nServerProtocol
    )
    {
        device.m_SipInfo.m_bEnableSIP = isEnable; 
        sprintf(device.m_SipInfo.m_szServerAddr, szServerAddr);
        sprintf(device.m_SipInfo.m_szAccount, szAccount);
        sprintf(device.m_SipInfo.m_szPassword, szPassword);
        device.m_SipInfo.m_nServerPort = nServerPort;
        device.m_SipInfo.m_nServerProtocol = nServerProtocol;

        device.m_SipInfo.m_nSipOutputVol = outputVol;
        device.m_SipInfo.m_nSipMicLevel = micLevel;

        g_Global.m_WebNetwork.ForwardSipStatus(device, NULL);
    }
}


/*******************************************************/
// 处理GPS发过来的校时信息
void	CCommandHandle::HandleGpsSyncTime(	const char*	pData,		// 数据区
                                            unsigned short	nLen,	// 数据长度
                                            CSection& gps)          // 设备
{
    /*
        time(NULL)	UTC 时间戳（秒数）	❌ 无关
        localtime(&time_t)	本地时间	✔️ 依赖时区
        gmtime(&time_t)	UTC 时间	    ❌ 无关
        mktime(&struct tm)	与时区有关   ✔️ 依赖时区   

        如果当前 ​​UTC 时间是 2025-07-12 10:14:50​​，而你的系统时区是 ​​东八区（UTC+8）​​，那么当你使用 mktime 时，它的行为如下：

        ​​1. 关键逻辑​​
        mktime ​​假设输入的 struct tm 是本地时间​​（即东八区时间）。关键所在
        由于你的时区是 ​​UTC+8​​，本地时间比 UTC 时间 ​​快 8 小时​​。
        因此，2025-07-12 10:14:50 UTC 对应的 ​​本地时间是 2025-07-12 18:14:50 CST（东八区）​​。
        ​​2. 具体计算​​
        ​​(1) 如果你直接传入 2025-07-12 10:14:50 给 mktime​​
        mktime 会认为这是 ​​本地时间（东八区）​​，即 2025-07-12 10:14:50 CST。
        它会自动减去 8 小时，得到 ​​UTC 时间 2025-07-12 02:14:50​​，并返回对应的时间戳。
        ​​(2) 如果你传入 2025-07-12 18:14:50（即 UTC+8 的本地时间）​​
        mktime 会认为这是 ​​本地时间 2025-07-12 18:14:50 CST​​。
        它会减去 8 小时，得到 ​​UTC 时间 2025-07-12 10:14:50​​，并返回正确的时间戳。

    */
    ctime_t	tNow = CTime::GetCurrentTimeT().GetTime();  //返回UTC时间戳（会自动计算时区）

    char	szDate[16]	= {0};
    char	szTime[16]	= {0};
    unsigned char	pos			= 0;

    // 日期
    int dateLen = pData[pos++];
    memcpy(szDate, &pData[pos], dateLen);
    pos += dateLen;

    // 时间
    int timeLen = pData[pos++];
    memcpy(szTime, &pData[pos], timeLen);
    pos += timeLen;

    int nYear, nMonth, nDay, nHour, nMinute, nSecond;
    sscanf(szDate, "%04d-%02d-%02d", &nYear, &nMonth, &nDay);
    sscanf(szTime, "%02d:%02d:%02d", &nHour, &nMinute, &nSecond);

    // 设置系统时间
    struct tm gpsTmTime;
    gpsTmTime.tm_year = nYear - 1900;
    gpsTmTime.tm_mon = nMonth - 1;
    gpsTmTime.tm_mday = nDay;
    gpsTmTime.tm_hour = nHour;
    gpsTmTime.tm_min = nMinute;
    gpsTmTime.tm_sec = nSecond;
    gpsTmTime.tm_isdst = -1;

    //转换为格林威治UTC时间
    #ifdef _WIN32
        std::time_t gpsTimestamp = _mkgmtime(&gpsTmTime);
    #else
        std::time_t gpsTimestamp = timegm(&gpsTmTime);
    #endif

   //如果与当前系统UTC时间误差不超过1秒，则不更新时间
    //printf("gpsTimestamp - tNow = %lld\n",gpsTimestamp - tNow);
    if(abs(gpsTimestamp - tNow) <= 1)
    {
        return;
    }
    printf("tGpsTime:%4d-%02d-%02d %02d:%02d:%02d\n",nYear,nMonth,nDay,nHour,nMinute,nSecond);
#if 0
    struct timeval tv;
    tv.tv_sec = gpsTimestamp;
    tv.tv_usec = 0;
    
    if (settimeofday(&tv, NULL) == 0) {
        printf("set gpsTime ok!\n");
        // 设置硬件时间
        char* hw_param[] = { (char *)"hwclock", (char *)"-w", NULL};
        ExecuteShell("hwclock", hw_param);
    } else {
        printf("set gpsTime error!\n");
    }
#endif

    SetSystemTime_hardware(gpsTimestamp);

    //通知WEB更新时间
    string strDateTime = GetCurrentDateTime().C_Str();
    string strDataBootTime = Get_system_boot_time().C_Str();
    string strBuf = CWebProtocol::CmdResponseSetSystemDateTime(0, strDateTime,strDataBootTime);
    g_Global.m_WebNetwork.m_WebSend.ForwardDataToWeb(NULL, strBuf, false, false);

    //同步设备时间
    m_pNetwork->m_CmdSend.CmdSyncronizeTime();
}


#if SUPPORT_PAGER_CALL


// 处理对讲相关命令需要转发的命令
void  CCommandHandle::HandleForwardCallingCommand(unsigned short	 command,	 // 命令
                                                 const char*	 pData,		 // 命令缓冲区
                                                 int			 nLen,		 // 数据长度
                                                 CSection        *oriDevice)     // 设备
{
    int pos = 8;
    char szMac_Calling[SEC_MAC_LEN]			= {0};      //主叫方MAC
    char szMac_Called[SEC_MAC_LEN]			= {0};      //被叫方MAC

    char szBuf[MAX_BUF_LEN] = {0};
    memcpy(szBuf, pData, nLen);
    szBuf[4] = MODEL_HOST;	// 设备型号修改为主机 

    //printf("HandleForwardCallingCommand:0x%x\n",command);

    switch( command )
    {
        case CMD_CALLING_INVITATION:
        {
            char callingMac[6]={0};
            char calledMac[6]={0};
            memcpy(callingMac,pData+pos,6);	    //主叫方MAC
            memcpy(calledMac,pData+pos+6,6);	//被叫方MAC
            pos+=12;
            
             unsigned char  audioCoding=pData[pos++];          //语音编码
             unsigned char  enableNat=pData[pos++];            //开启NAT后由主机转发双方音频流
             unsigned short audioPort=(pData[pos]<<8)+(pData[pos+1]);            //关闭NAT后有效

             SetMac(szMac_Calling, callingMac);   //主叫方MAC 字符串
             SetMac(szMac_Called, calledMac);   //被叫方MAC 字符串

             printf("CMD_CALLING_INVITATION1:calling_MAC=%s,called_MAC=%s\n",szMac_Calling,szMac_Called);
             CSection *pDevice = NULL;
             //找到被叫MAC
            for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
            {
                pDevice = g_Global.m_pAllDevices[i]->GetSectionByMac(szMac_Called);

                //20221205被叫处于非对讲状态时才允许呼叫，否则立即挂断
                if (pDevice != NULL && pDevice->IsOnline() && pDevice->IsSupportCallDevice() && pDevice->GetProSource() != PRO_CALL)
                {
                    break;
                }
                else
                {
                    pDevice = NULL;
                }
            }

            if(pDevice!=NULL)           
            {
                oriDevice->m_pAduioCall->SetCallMac(szMac_Called);         //主叫方设置被叫MAC
                oriDevice->m_pAduioCall->SetCallParty(CALLING_PARTY);      //主叫方设置角色
                pDevice->m_pAduioCall->SetCallMac(szMac_Calling);          //被叫方设置主叫MAC
                pDevice->m_pAduioCall->SetCallParty(CALLED_PARTY);         //被叫方设置角色

                //printf("CMD_CALLING_INVITATION2:calling_MAC=%s,called_MAC=%s\n",szMac_Calling,szMac_Called);

                //转发主叫方的对讲邀请指令给被叫方
                m_pNetwork->SendData(szBuf, nLen, *pDevice, pDevice->GetIP(), UDP_PORT, 1);

                //将对讲状态推送给WEB端
                g_Global.m_WebNetwork.ForwardIntercomStatusToWeb(NULL,*oriDevice,*pDevice,1);
            }
            else
            {
                //如果被叫设备不存在，那么应答给原设备,以便其立即挂断
                m_pNetwork->SendData(szBuf, nLen, *oriDevice, oriDevice->GetIP(), UDP_PORT, 1);
            }
        }
        break;
        case CMD_CALLED_RESPONSE:                   //对讲设备被叫方应答邀请
        {
            char callingMac[6]={0};
            char calledMac[6]={0};
            memcpy(callingMac,pData+pos,6);	    //主叫方MAC
            memcpy(calledMac,pData+pos+6,6);	//被叫方MAC
            pos+=12;
            
            unsigned char  audioCoding=pData[pos++];          //语音编码
            unsigned char  enableNat=pData[pos++];            //开启NAT后由主机转发双方音频流
            unsigned short audioPort=(pData[pos]<<8)+(pData[pos+1]);            //关闭NAT后有效

            SetMac(szMac_Calling, callingMac);   //主叫方MAC 字符串
            SetMac(szMac_Called, calledMac);   //被叫方MAC 字符串

            CSection *pDevice = NULL;
             //找到主叫MAC
            for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
            {
                pDevice = g_Global.m_pAllDevices[i]->GetSectionByMac(szMac_Calling);

                if (pDevice != NULL && pDevice->IsOnline())
                {
                    break;
                }
            }
            if(pDevice!=NULL)
            {
                //转发被叫方的应答指令给主叫方
                printf("called:%s response to calling:%s...\n",szMac_Called,szMac_Calling);
                m_pNetwork->SendData(szBuf, nLen, *pDevice, pDevice->GetIP(), UDP_PORT, 1); 
            }
        }
        break;
        case CMD_CALLED_STATUS:                   //对讲设备状态反馈
        {
            unsigned char callStatus = szBuf[8+6];
            //转发对讲设备的对讲状态给对方
            printf("foward CMD_CALLED_STATUS1:%d to %s\n",callStatus,oriDevice->m_pAduioCall->GetCallMac());
             //找到对讲MAC
            CSection *pDevice = NULL;
            for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
            {
                pDevice = g_Global.m_pAllDevices[i]->GetSectionByMac(oriDevice->m_pAduioCall->GetCallMac());

                if (pDevice != NULL && pDevice->IsOnline())
                {
                    break;
                }
            }
            if(pDevice!=NULL)
            {
                //printf("foward CMD_CALLED_STATUS2:%d to %s\n",callStatus,pDevice->GetIP());
                m_pNetwork->SendData(szBuf, nLen, *pDevice, pDevice->GetIP(), UDP_PORT, 1); 

                //将对讲状态推送给WEB端，被叫的状态变成了通话中，认为对讲双方处于通话状态
                if(callStatus == CALL_STATUS_CONNECT && oriDevice->m_pAduioCall->GetCallParty() == CALLING_PARTY)
                {   
                    g_Global.m_WebNetwork.ForwardIntercomStatusToWeb(NULL,*oriDevice,*pDevice,2);
                }
                else if(callStatus == CALL_STATUS_FREE && oriDevice->m_pAduioCall->GetCallParty() == CALLING_PARTY)
                {
                    g_Global.m_WebNetwork.ForwardIntercomStatusToWeb(NULL,*oriDevice,*pDevice,3);
                }
            }

            //如果对讲状态变成了空闲，那么需要重置自己的呼叫对象MAC
            if(callStatus == CALL_STATUS_FREE)
            {
                oriDevice->m_pAduioCall->SetCallMac("00:00:00:00:00:00");
            }
        }
        break;
        case CMD_CALLING_AUDIOSTREAM:                   //对讲音频流传输
        {
            //转发音频流给对方
            //printf("foward CMD_CALLING_AUDIOSTREAM to %s\n",oriDevice->m_pAduioCall->GetCallMac());
             //找到对讲MAC
            CSection *pDevice = NULL;
            for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
            {
                pDevice = g_Global.m_pAllDevices[i]->GetSectionByMac(oriDevice->m_pAduioCall->GetCallMac());

                if (pDevice != NULL && pDevice->IsOnline())
                {
                    break;
                }
            }
            if(pDevice!=NULL)
                m_pNetwork->SendData(szBuf, nLen, *pDevice, pDevice->GetIP(), UDP_PORT, 1); 
        }
        break;

        //下面是视频相关
        case CMD_CALL_REQUEST_VIDEO:
        case CMD_CALL_VIDEO_PARM:
        case CMD_CALL_VIDEO_STATUS:
        case CMD_CALL_VIDEO_STREAM:
        {
            #if 0
            if(command == CMD_CALL_VIDEO_STREAM)
            {
                if(strcmp(oriDevice->GetIP(),"*************") == 0)
                {
                    int pos=8;
                    //MAC
                    unsigned char calledMac[6]={0};
                    memcpy(calledMac,pData+pos,6);			//MAC
                    pos+=6;

                    //数据流长度
                    int streamLen = (pData[pos]<<8)+pData[pos+1];
                    pos+=2;
                    //数据流偏移量
                    unsigned char streamOffset=pData[pos++];
                    //包id
                    unsigned int pkgId = (pData[pos]<<24) + (pData[pos+1]<<16) + (pData[pos+2]<<8) + pData[pos+3] ;
                    pos+=4;
                    //分包id
                    unsigned char subPkgId = pData[pos++];
                    //包数据类型
                    unsigned char pkgType = pData[pos++];

                    printf("Recv:pkgId=%d,subPkgId=%d,Len=%d,Offset=%d\n",pkgId,subPkgId,streamLen,streamOffset);
                }
            }
            #endif
            CSection *pDevice = NULL;
            for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
            {
                pDevice = g_Global.m_pAllDevices[i]->GetSectionByMac(oriDevice->m_pAduioCall->GetCallMac());

                if (pDevice != NULL && pDevice->IsOnline())
                {
                    break;
                }
            }
            if(pDevice!=NULL)
            {
                m_pNetwork->SendData(szBuf, nLen, *pDevice, pDevice->GetIP(), UDP_PORT, 1); 
            }
        }
        break;
    }
}




#endif








#if SUPPORT_LISTEN_FUNCTION

#include "Network/Kcp/ikcp_test.h"
unsigned int prelisten_time=0;

// 处理监听相关命令
void  CCommandHandle::HandleForwardListenCommand(unsigned short	 command,	 // 命令
                                                 const char*	 pData,		 // 命令缓冲区
                                                 int			 nLen,		 // 数据长度
                                                 CSection        *oriDevice)     // 设备
{
    int pos = 8;
    char szMac_listening[SEC_MAC_LEN]		= {0};      //监听设备MAC
    char szMac_listened[SEC_MAC_LEN]			= {0};      //被监听设备MAC

    char szBuf[MAX_BUF_LEN] = {0};
    memcpy(szBuf, pData, nLen);
    szBuf[4] = MODEL_HOST;	// 设备型号修改为主机 

    //printf("HandleForwardListenCommand:0x%x\n",command);

    switch( command )
    {
        case CMD_LISTEN_EVENT:  	        //监听设备发起监听
        {
            char listeningMac[6]={0};
            char listenedMac[6]={0};
            memcpy(listeningMac,pData+pos,6);	    //主叫方MAC
            memcpy(listenedMac,pData+pos+6,6);	//被叫方MAC

            SetMac(szMac_listening, listeningMac);   //监听设备MAC  字符串
            SetMac(szMac_listened, listenedMac);    //被监听设备MAC 字符串
            pos+=12;

            unsigned char event=pData[pos++];
            if(event == 1)      //开始监听
            {
                unsigned char type=pData[pos++];                                    //节目源监听或者现场监听
                unsigned char  audioCoding=pData[pos++];                            //语音编码
                unsigned char  enableNat=pData[pos++];                              //开启NAT后由主机转发双方音频流
                unsigned short audioPort=(pData[pos]<<8)+(pData[pos+1]);            //关闭NAT后有效
            }
            

            CSection *pDevice = NULL;

             //找到被监听设备MAC
            for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
            {
                pDevice = g_Global.m_pAllDevices[i]->GetSectionByMac(szMac_listened);

                if (pDevice != NULL && pDevice->IsOnline())
                {
                    break;
                }
            }

            if(pDevice!=NULL)
            {
                oriDevice->m_pAduioListen->SetListenMac(szMac_listened);         //监听设备设置被监听设备MAC
                oriDevice->m_pAduioListen->SetListenParty(LISTENING_PARTY);      //监听设备设置角色
                pDevice->m_pAduioListen->SetListenMac(szMac_listening);          //被监听设备设置监听设备MAC
                pDevice->m_pAduioListen->SetListenParty(LISTENED_PARTY);         //被监听设备设置角色

                //转发监听指令给被监听方
                m_pNetwork->SendData(szBuf, nLen, *pDevice, pDevice->GetIP(), UDP_PORT, 1);
            }

        }
        break;
        case CMD_LISTEN_RESPONSE:           //被监听设备应答
        {
            prelisten_time=0;
            //转发被监听设备的应答到监听设备
            CSection *pDevice = NULL;
            for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
            {
                pDevice = g_Global.m_pAllDevices[i]->GetSectionByMac(oriDevice->m_pAduioListen->GetListenMac());

                if (pDevice != NULL && pDevice->IsOnline())
                {
                    break;
                }
            }
             if(pDevice!=NULL)
                m_pNetwork->SendData(szBuf, nLen, *pDevice, pDevice->GetIP(), UDP_PORT, 1); 
        }
        break;
        case CMD_LISTEN_STREAM_UPLOAD:  	//监听设备上传音频流
        {
            //转发音频流给监听设备
            //printf("foward CMD_LISTEN_STREAM_UPLOAD to %s\n",oriDevice->m_pAduioListen->GetListenMac());
            CSection *pDevice = NULL;
            #if 0
            unsigned int listen_time=iclock();
            printf("listen_Time=%ul\r\n",listen_time-prelisten_time);
            prelisten_time=listen_time;
            #endif
            for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
            {
                pDevice = g_Global.m_pAllDevices[i]->GetSectionByMac(oriDevice->m_pAduioListen->GetListenMac());

                if (pDevice != NULL && pDevice->IsOnline())
                {
                    break;
                }
            }
            if(pDevice!=NULL)
                m_pNetwork->SendData(szBuf, nLen, *pDevice, pDevice->GetIP(), UDP_PORT, 1);
        }
        break;
        case CMD_LISTEN_STATUS:            //监听设备发送监听状态
        {
            //发送状态给被监听设备
            //printf("foward CMD_LISTEN_STATUS to %s\n",oriDevice->m_pAduioListen->GetListenMac());
            CSection *pDevice = NULL;
            for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
            {
                pDevice = g_Global.m_pAllDevices[i]->GetSectionByMac(oriDevice->m_pAduioListen->GetListenMac());

                if (pDevice != NULL && pDevice->IsOnline())
                {
                    break;
                }
            }
            if(pDevice!=NULL)
                m_pNetwork->SendData(szBuf, nLen, *pDevice, pDevice->GetIP(), UDP_PORT, 1);
        }
        break;
    }

}

#endif




#if SUPPORT_WEB_PAGING

// 处理app寻呼相关命令
void  CCommandHandle::HandleWebPagerCommand(const char*       pData,		// 数据区
                                          unsigned short	nLen,		// 数据长度
                                          const char*       szIP,		// IP地址
                                          unsigned short	uPort)     // 端口
{
    ushort  command     = CharsToShort(&pData[0]);      // 命令
    u_char	nReserved	= (unsigned char)pData[3];		// 保留字
    u_char	devModel	= (unsigned char)pData[4];		// 设备型号
    u_char	packAttr	= (unsigned char)pData[5];		// 包属性  3.15
    ushort	uDataLen	= CharsToShort(&pData[6]);		// 数据长度
    u_char	dataPos		= 8;
    CSection *pDevice   = NULL;
    //也可以用流长度判断，如果流长度为1024，代表是PCM流，如果是320，代表是opus流
    int audioCodecs = packAttr;
    //printf("HandleWebPagerCommand:cmd=0x%04x...\n",CMD_WEB_PAGING_STREAM);
    switch(command)
    {
        case CMD_WEB_PAGING_STREAM:
        case CMD_SOUNDCARD_PAGING_STREAM:
        {
            //提取pagingId
            char pagingId[64]={0};	
            int pagingId_len = pData[dataPos++];
            memcpy(pagingId, &pData[dataPos], pagingId_len);
            dataPos+=pagingId_len;

            const char *dataStream=pData+dataPos;
            int StreamLen=uDataLen-pagingId_len-1;

            //查找websocketid
            LPCWebSection CWebSection=g_Global.m_WebSections.GetWebSectionBySocketID(pagingId);
             //此处需要加锁
            QMutexLocker locker(&g_Global.m_WebSections.websections_mutex);
            bool isValid=false;
            if(CWebSection!=NULL)
            {
                CWebPaging *webpPaging = CWebSection->m_webPaging;
                if( webpPaging!=NULL)
                {
                    isValid=true;
                    webpPaging->webPagingLock();
                    int samples=StreamLen/2;
                    int16_t processAudioBuf[512]={0};
                    memcpy(processAudioBuf,dataStream,StreamLen);

                    char sendBuf[MAX_BUF_LEN] = {0};
                    int len = 0;
                    
                    if(audioCodecs == ALGORITHM_PCM)    //如果源码流是PCM，需要压缩成G.722
                    {
                        #if SUPPORT_SPEEX_DENOISE
                        SpeexPreprocessState  *speexSt_appPagingMic=webpPaging->Get_speex_st_appPaging();
                        speex_preprocess_run(speexSt_appPagingMic, processAudioBuf);
                        #endif
#if !APP_IS_LZY_CLOUD_VERSION
                        //20230225 将APP寻呼数据降低,2050为-6dB，1295为-10dB，1030为-12dB,820为-14dB（-5倍）
                        if(command == CMD_WEB_PAGING_STREAM)    //APP喊话
                        {
                            for(int i=0; i<samples; i++)
                            {
                                processAudioBuf[i] = limit_value_16bit( ((((int32_t)processAudioBuf[i]) * 1295 + 2048) >> 12) );
                            }
                        }
                        else    //声卡采集
                        {
                            for(int i=0; i<samples; i++)
                            {
                                processAudioBuf[i] = limit_value_16bit( ((((int32_t)processAudioBuf[i]) * 1030 + 2048) >> 12) );
                            }
                        }
#endif
                        G722_ENC_CTX  *g722_enc_ctx=webpPaging->Get_G722_enc_ctx();
                        unsigned char outbuf[256]={0};
                        int encoder_bytes=g722_encode(g722_enc_ctx, (short*)processAudioBuf, 512, outbuf);

                        //加入由UUID组成的MAC
                        unsigned char dataBuf[256+6]={0};
                        memcpy(dataBuf,webpPaging->getMAC(),6);
                        memcpy(dataBuf+6,outbuf,256);
                        //发送音频流到相应的终端
                        len = CProtocol::ControlCommand(sendBuf,CMD_WEB_PAGING_STREAM,(const char*)dataBuf,256+6);
                    }
                    else    //如果源码流是OPUS，则直接发送，但是需要由终端主动降低10dB增益
                    { 
                        //加入由UUID组成的MAC
                        unsigned char dataBuf[320+6]={0};
                        memcpy(dataBuf,webpPaging->getMAC(),6);
                        memcpy(dataBuf+6,processAudioBuf,StreamLen);
                        len = CProtocol::ControlCommand(sendBuf,CMD_SOUNDCARD_PAGING_STREAM,(const char*)dataBuf,StreamLen+6);
                    }
                    
                    //组播到**************,52052
                    //g_Global.m_Network.m_MySocketMulticast.SendData(sendBuf, len, MULTICAST_PAGING_IP, MULTICAST_PAGING_PORT, 1);
                    g_Global.m_Network.SendUdpData(sendBuf, len, MULTICAST_PAGING_IP, MULTICAST_PAGING_PORT, 1);

                    unsigned int*   pCheckedIndexs = webpPaging->GetSecIndexs();
                    for (unsigned int i=0; i<webpPaging->GetSecCount(); ++i)
                    {
                        CSection& section = g_Global.m_Sections.GetSection(pCheckedIndexs[i]);

                        // 只有在线的分区才发送命令
                        if (section.IsOnline() && section.IsTcpMode())
                        {
                            if(section.m_kcpsocket && section.m_kcpsocket->GetDesPort() && section.m_kcpsocket->GetSendValid() && !STREAM_ALLWAYS_USED_TCP)
                            {
                                section.m_kcpsocket->SendData(1,sendBuf,len);
                            }
                            else
                            {
                                char* cbuf = new char[len];//足够长
                                memcpy(cbuf,sendBuf,len);
                                g_Global.m_Network.SendTcpData(cbuf, len, section.m_pSocketObj);
                                delete[] cbuf;
                            }
                        }
                    }
                    webpPaging->webPagingUnLock();
                }
            }

            //发送应答
            char szBuf[MAX_BUF_LEN] = {0};
            char data[MAX_BUF_LEN]	= {0};
            if(isValid)
            {
                data[0]=1;  //1-有效 2-拒绝(uuid已断开)
            }
            else
            {
                data[0]=2;  //1-有效 2-拒绝(uuid已断开)
                printf("inValid pagingId:%s\n",pagingId);
            }
            int sendLen=CProtocol::ControlCommand(szBuf,command,data,1);
            g_Global.m_Network.m_MyUdpWebPagingUnicast.SendData(szBuf, sendLen,szIP,uPort,1);
        }
        break;
    }
}

#endif





// 处理控制设备向主机发送登录账户
void  CCommandHandle::HandleControlerAccountInfo(const char*	pData,		// 数据区
                                     unsigned short	nLen,		// 数据长度
                                     CSection& device)			// 设备
{
    u_char	dataPos		= 8;
    int account_len=pData[dataPos];
    char account[64]={0};
    memcpy(account,&pData[dataPos+1],account_len);
    printf("HandleControlerAccountInfo:%s\n",account);
    if( strcmp(account,device.GetUserAccount().data()) )
    {
        device.SetUserAccount(account);

        if(strlen(account)>0)
        {
            //加入日志
            CMyString strContents;
            strContents.Format("%s:%s", account,"user_login");

            g_Global.m_logTable.InsertLog(CMyString(device.GetMac()),
                                    CMyString(device.GetName()),
                                    LT_RUNNING_STATE,
                                    strContents);
            
            //龙之音V1版本，在收到寻呼台账户登录变更后，先重置设备的DateTime，以便服务器重新发送相关列表文件。
            //******** 暂时只需要重置playlist即可，因为其他文件还是所有账户共用
            #if APP_IS_LZY_LIMIT_STORAGE
            //pOtherDevice->ResetFileDateTime();
            device.SetFileDateTime(DT_PLAYLIST,"");
            #endif

            #if SUPPORT_USER_SECTION_XML
            device.SetFileDateTime(DT_SECTION,"");
            #endif
        }
    }
    if(strlen(account)>0)
    {
        //收到控制设备的登录信息后，发送一次该用户的所有分区信息给该控制设备
        g_Global.m_Network.ForwardSectionsStatusToControlDevices(NULL,&device);
    }
}



// 处理音频采集器数据流发送（TCP)
void  CCommandHandle::HandleAudioCollectorStream(const char*	pData,		// 数据区
                                     unsigned short	nLen,		// 数据长度
                                     CSection& device)			// 设备
{
    u_char	dataPos		= 8;
    int channelId=pData[dataPos++];
    int audioCoding = pData[dataPos++];
    if(device.m_pAudioCollector == NULL)
        return;
    G722_ENC_CTX *g722_enc_ctx = device.m_pAudioCollector->Get_G722_enc_ctx(channelId);

    unsigned char outbuf[256]={0};
    int encoder_bytes=g722_encode(g722_enc_ctx, (short*)(pData+dataPos), 512, outbuf);

    int uDataLen = 2+256;
    char* cbuf = new char[1500];//足够长
    memcpy(cbuf,pData,10);
    memcpy(cbuf+10,outbuf,256);
    cbuf[4] = MODEL_HOST;	// 设备型号修改为主机
    cbuf[6] = uDataLen/256;
    cbuf[7] = uDataLen%256;
    cbuf[8+uDataLen] = CProtocol::GetChecksum(cbuf+8, uDataLen);
    int packageLen = 9+uDataLen;
#if 0
    timeval tv;
    gettimeofday(&tv, NULL);
    ctime_t time = (ctime_t)(tv.tv_sec)*1000 + tv.tv_usec/1000;
    printf("AudioStream:%lu\n",time);
#endif
    //将音频流转发至指定的设备中
    for (unsigned int i=0; i<g_Global.m_Sections.GetSecCount(); ++i)
    {
        CSection &pSection = g_Global.m_Sections.GetSection(i);
        if(!pSection.IsOnline())
        {
            continue;
        }
        //printf("pSection:name=%s,GetProSource()=0x%x\n",pSection.GetName(),pSection.GetProSource());
        //if(CProtocol::GetAudioCollectorChannelBySrc(pSection.GetProSource()) == channelId
        if(pSection.GetProSource() == device.m_pAudioCollector->GetSourceID()+channelId-1)
        {
            //printf("device:%s,GetSourceID()=0x%x\n",pSection.GetName(),device.m_pAudioCollector->GetSourceID()+channelId-1);
            //下发音频流
            //printf("AudioCollector:channelId %d to Mac:%s\n",channelId,pSection.GetMac());
            if( pSection.IsTcpMode() )
            {
                #if 1
                if(pSection.m_kcpsocket && pSection.m_kcpsocket->GetDesPort() && pSection.m_kcpsocket->GetSendValid() && !STREAM_ALLWAYS_USED_TCP)
                {
                    pSection.m_kcpsocket->SendData(1,cbuf,packageLen);
                }
                else
                #endif
                {
                    //char* cbuf = new char[nLen];//足够长
                    //memcpy(cbuf,pData,nLen);
                    //cbuf[4] = MODEL_HOST;	// 设备型号修改为主机
                    g_Global.m_Network.SendTcpData(cbuf, packageLen, pSection.m_pSocketObj);
                    //delete[] cbuf;
                }
            }
        }
    }
    delete[] cbuf;
}





// 处理对讲设备基础配置
void  CCommandHandle::HandleIntercomBasicConfig(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device)			// 设备
{
    if(!device.IsSupportCallDevice())
        return;
    //判断是查询还是设置的应答
    u_char	dataPos		= 0;
    bool bSet = ((BYTE)pData[dataPos++]) == OPER_SET ? TRUE:FALSE;

    int parmIndex=0;
    char key1_mac[6]={0};
    char key2_mac[6]={0};

    int count=0;

    #if 0
        printf("HandleIntercomBasicConfig:payload_Length=%d\n",nDataLen);
        for(int i=0;i<nDataLen;i++)
        {
            printf("%x ",(unsigned char)pData[i]);
        }
        printf("\n");
    #endif

    while( dataPos < nDataLen && ++count<MAX_BUF_SIZE )
    {
        int parmId = pData[dataPos++];
        int parmLength=pData[dataPos++];
        
        for(parmIndex=0;parmIndex<parmLength && dataPos < nDataLen;)
        {
            if(parmId == 1)
            {
                if(parmIndex == 0)
                {
                    memcpy(key1_mac,pData+dataPos,6);
                    dataPos+=6;
                    parmIndex+=6;

                    SetMac(device.m_pAduioCall->m_stCallDeviceConfig.Key1_mac, key1_mac);
                }
                else if(parmIndex == 6)
                {
                    memcpy(key2_mac,pData+dataPos,6);
                    dataPos+=6;
                    parmIndex+=6;

                    SetMac(device.m_pAduioCall->m_stCallDeviceConfig.Key2_mac, key2_mac);
                }
                else
                {
                    dataPos++;			//如果该参数还有其他字段，暂时不处理
                    parmIndex++;	//如果该参数还有其他字段，暂时不处理
                }
            }
            else if(parmId == 2)
            {
                if(parmIndex == 0)
                {
                    device.m_pAduioCall->m_stCallDeviceConfig.AutoAnswerTime = pData[dataPos++];
                    parmIndex++;
                }
                else if(parmIndex == 1)
                {
                    device.m_pAduioCall->m_stCallDeviceConfig.micVol = pData[dataPos++];
                    parmIndex++;
                }
                else if(parmIndex == 2)
                {
                    device.m_pAduioCall->m_stCallDeviceConfig.farOutVol = pData[dataPos++];
                    parmIndex++;
                }
                else
                {
                    dataPos++;			//如果该参数还有其他字段，暂时不处理
                    parmIndex++;	//如果该参数还有其他字段，暂时不处理
                }
            }
            else
            {
                dataPos += parmLength;	//不是预设的参数id,跳过
                break;
            }
        }
    }

    printf("key1Mac=%s,key2Mac=%s\n",device.m_pAduioCall->m_stCallDeviceConfig.Key1_mac,device.m_pAduioCall->m_stCallDeviceConfig.Key2_mac);
    //通知WEB
    string buf = CWebProtocol::CmdResponseSetIntercomBasic(bSet,device.GetMac(),&device.m_pAduioCall->m_stCallDeviceConfig);
    g_Global.m_WebNetwork.m_WebSend.ForwardDataToWeb(NULL, buf, false);
}




// 处理触发配置
void  CCommandHandle::HandleTriggerConfig(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device)			// 设备
{
    printf("HandleTriggerConfig1...\n");
    //判断是查询还是设置的应答
    u_char	dataPos		= 0;
    bool bSet = ((BYTE)pData[dataPos++]) == OPER_SET ? TRUE:FALSE;

    int parmIndex=0;

    int count=0;

    char trigger_switch=0;
    char trigger_mode=0;
    int trigger_name_len=0;
    char trigger_song_name[128]={0};
    int trigger_md5_len=0;
    char trigger_song_md5[32+1]={0};
    int  trigger_playTimes=1;
    int  trigger_volume=0;

    #if 0
        printf("HandleTriggerConfig:payload_Length=%d\n",nDataLen);
        for(int i=0;i<nDataLen;i++)
        {
            printf("%x ",(unsigned char)pData[i]);
        }
        printf("\n");
    #endif

     printf("HandleTriggerConfig2...\n");
    while( dataPos < nDataLen && ++count<MAX_BUF_SIZE )
    {
        int parmId = pData[dataPos++];
        int parmLength=pData[dataPos++];
        
        for(parmIndex=0;parmIndex<parmLength && dataPos < nDataLen;)
        {
            if(parmId == 1)
            {
                if(parmIndex == 0)
                {
                    trigger_switch=pData[dataPos++];
                    parmIndex++;
                }
                else if(parmIndex == 1)
                {
                    trigger_mode=pData[dataPos++];
                    parmIndex++;
                }
                else
                {
                    dataPos++;			//如果该参数还有其他字段，暂时不处理
                    parmIndex++;	//如果该参数还有其他字段，暂时不处理
                }
            }
            else if(parmId == 2)
            {
                if(parmIndex == 0)
                {
                    trigger_name_len = pData[dataPos++];
                    parmIndex++;
                }
                else if(trigger_name_len>0 && parmIndex == 1)
                {
                    memset(trigger_song_name,0,sizeof(trigger_song_name));
                    memcpy(trigger_song_name,pData+dataPos,trigger_name_len);
                    dataPos+=trigger_name_len;
                    parmIndex+=trigger_name_len;
                }
                else if(parmIndex == 1+trigger_name_len)
                {
                    trigger_md5_len = pData[dataPos++];
                    parmIndex++;
                }
                else if(trigger_md5_len>0 && parmIndex == 1+trigger_name_len+1)
                {
                    memset(trigger_song_md5,0,sizeof(trigger_song_md5));
                    memcpy(trigger_song_md5,pData+dataPos,trigger_md5_len);
                    dataPos+=trigger_md5_len;
                    parmIndex+=trigger_md5_len;
                }
                else if(parmIndex == 1+trigger_name_len+1+trigger_md5_len)
                {
                    trigger_playTimes = pData[dataPos++];
                    parmIndex++;
                }
                else if(parmIndex == 1+trigger_name_len+1+trigger_md5_len+1)
                {
                    trigger_volume = pData[dataPos++];
                    parmIndex++;
                }
                else
                {
                    dataPos++;			//如果该参数还有其他字段，暂时不处理
                    parmIndex++;	//如果该参数还有其他字段，暂时不处理
                }
            }
            else
            {
                dataPos += parmLength;	//不是预设的参数id,跳过
                break;
            }
        }
    }
     printf("HandleTriggerConfig3...\n");
    printf("trigger_switch=%d,trigger_mode=%d,trigger_song_name=%s,trigger_volume=%d\n",trigger_switch,trigger_mode,trigger_song_name,trigger_volume);

    //通知WEB
    char trigger_song_path_name[256]={0};

    CMyString cstrPathName, cstrPathNameM;
    if(strlen(trigger_song_name)>0)
    {
        cstrPathName.Format("/%s/%s/%s",HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_OTHER, trigger_song_name);
        cstrPathNameM.Format("/%s/%s/%s",HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_MUSIC, trigger_song_name);

        CSong tsong;
        if(g_Global.m_SongManager.GetSongByPathName(cstrPathName,tsong))
        {
            sprintf(trigger_song_path_name,"%s",cstrPathName.Data());
        }
        else if(g_Global.m_SongManager.GetSongByPathName(cstrPathNameM,tsong))
        {
            sprintf(trigger_song_path_name,"%s",cstrPathNameM.Data());
        }
        else
        {
            
        }
    }

    string buf = CWebProtocol::CmdResponseTriggerBasic(bSet,device.GetMac(),trigger_switch,trigger_mode,trigger_song_path_name,trigger_volume);
    g_Global.m_WebNetwork.m_WebSend.ForwardDataToWeb(NULL, buf, false);
}



// 处理触发请求播放
void  CCommandHandle::HandleTriggerRequestPlay(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device)			// 设备
{
    printf("HandleTriggerRequestPlay...\n");
    u_char	dataPos		= 0;

    char trigger_switch=0;
    int trigger_song_name_len=0;
    char trigger_song_name[128]={0};
    int  trigger_playTimes=1;
    int  trigger_volume=0;

    trigger_song_name_len = pData[dataPos++];
    memcpy(trigger_song_name,pData+dataPos,trigger_song_name_len);
    dataPos+=trigger_song_name_len;
    trigger_playTimes = pData[dataPos++];
    trigger_volume = pData[dataPos++];

    printf("trigger_song_name=%s,trigger_volume=%d\n",trigger_song_name,trigger_volume);

    //判断歌曲是否存在
    CMyString cstrPathName, cstrPathNameM;
    char trigger_song_path_name[256]={0};
    if(strlen(trigger_song_name)>0)
    {
        cstrPathName.Format("/%s/%s/%s",HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_OTHER, trigger_song_name);
        cstrPathNameM.Format("/%s/%s/%s",HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_MUSIC, trigger_song_name);

        CSong tsong;
        if(g_Global.m_SongManager.GetSongByPathName(cstrPathName,tsong))
        {
            sprintf(trigger_song_path_name,"%s",cstrPathName.Data());
        }
        else if(g_Global.m_SongManager.GetSongByPathName(cstrPathNameM,tsong))
        {
            sprintf(trigger_song_path_name,"%s",cstrPathNameM.Data());
        }
        printf("ready trigger0\n");
        if(strlen(trigger_song_path_name)>0)
        {
                unsigned int uCount = 1;
                unsigned int pSecIndexs[1] = {0};
                pSecIndexs[0] = device.GetID() - 1;

                //找到播放列表的对应歌曲
                int nList, nSong;
                g_Global.m_PlayList.FindSongInPlayList(trigger_song_path_name, nList, nSong);
                printf("ready trigger1\n");
                if (nList >= 0 && nSong >= 0)
                {
                    printf("ready trigger2\n");
                    CMyString strSongPathName = g_Global.m_strHttpRootDir + trigger_song_path_name;
                    CPlayTask playTask(SOURCE_PLAY, {strSongPathName}, pSecIndexs, uCount,"admin",PM_SINGLE,nList,nSong,trigger_volume == 0?-1:trigger_volume);
                    g_Global.m_PlayQueue.PushPlayTask(playTask);
                }
        }
    }
}



#if SUPPORT_AUDIO_MIXER

// 查询/设置音频协处理器参数
void  CCommandHandle::HandleAudioMixerConfig(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device)			// 设备
{
    if(!device.IsAudioMixerDevice())
        return;
    //判断是查询还是设置的应答
    u_char	dataPos		= 0;
    bool bSet = ((BYTE)pData[dataPos++]) == OPER_SET ? TRUE:FALSE;

    int nMasterSwitch = pData[dataPos++];
    int nPriority = pData[dataPos++];
    int nTriggerType = pData[dataPos++];
    int nTriggerSensitivity=pData[dataPos++];
    int nVolumeFadeLevel = pData[dataPos++];
    int nZoneVolume = (BYTE)pData[dataPos++];   //一定要用BYTE类型转换，否则异常
    int nDelayMode = pData[dataPos++];
    vector<CMyString> vecSecMacs;
    device.m_pAudioMixer->GetSelectedSections(vecSecMacs);

    CAudioMixer mixerNew;
    mixerNew.SetMasterSwitch(nMasterSwitch);
    mixerNew.SetPriority(nPriority);
    mixerNew.SetTriggerType(nTriggerType);
    mixerNew.SetTriggerSensitivity(nTriggerSensitivity);
    mixerNew.SetVolumeFadeLevel(nVolumeFadeLevel);
    mixerNew.SetSection(vecSecMacs);
    //20230906 分区音量由服务器自身存储决定，不由混音器决定
    nZoneVolume=device.m_pAudioMixer->GetVolume();
    mixerNew.SetVolume(nZoneVolume);

    printf("HandleAudioMixerConfig:bSet=%d\n",bSet);
    printf("nMasterSwitch=%d,nPriority=%d,nTriggerType=%d,nTriggerSensitivity=%d,nVolumeFadeLevel=%d,nZoneVolume=%d,nDelayMode=%d\n",
            nMasterSwitch,nPriority,nTriggerType,nTriggerSensitivity,nVolumeFadeLevel,nZoneVolume,nDelayMode);

    //判断是否改变，注意改变后如果设备已经处于混音状态，如何处理？这个让音频混音器自身去处理。
    //常规做法：音频混音器收到后判断参数是否变化，如果变化：
    //1、混音器原主开关打开，现在主开关关闭，如果原信号=1，那么将信号设置为0，并立即发送send_online_info，由服务器终端通知绑定的终端退出混音器音源
    //   特别的，如果当前信号为0，不需要处理。
    //2、混音器原主开关关闭，现在主开关打开，不需要特殊处理
    if(!(*device.m_pAudioMixer==mixerNew))
    {
        device.m_pAudioMixer->SetMasterSwitch(mixerNew.GetMasterSwitch());
        device.m_pAudioMixer->SetPriority(mixerNew.GetPriority());
        device.m_pAudioMixer->SetTriggerType(mixerNew.GetTriggerType());
        device.m_pAudioMixer->SetTriggerSensitivity(mixerNew.GetTriggerSensitivity());
        device.m_pAudioMixer->SetVolumeFadeLevel(mixerNew.GetVolumeFadeLevel());
        device.m_pAudioMixer->SetVolume(mixerNew.GetVolume());
        
        printf("device.m_pAudioMixer!=&mixerNew...\n");
        //保存xml文件
        g_Global.WriteXmlFile(FILE_AUDIO_MIXER);
    }
    
    //通知WEB
    string strBuf = CWebProtocol::CmdSetAudioMixerParm(device.GetMac(), bSet, nMasterSwitch, nPriority, nTriggerType,
                                        nTriggerSensitivity, nVolumeFadeLevel, nZoneVolume, vecSecMacs);
    g_Global.m_WebNetwork.m_WebSend.ForwardDataToWeb(NULL, strBuf, false);
}




// 处理音频混音器数据流发送（TCP)
void  CCommandHandle::HandleAudioMixerStream(const char*	pData,		// 数据区
                                     unsigned short	nLen,		// 数据长度
                                     CSection& device)			// 设备
{
    if(!device.IsAudioMixerDevice())
        return;
    u_char	dataPos		= 8;
    
    //前6个字节为混音器的mac地址，跳过
    dataPos+=6;
    int audioCoding = pData[dataPos++];
    int signalType = pData[dataPos++];

    unsigned char *stremBuf=(unsigned char *)(pData+dataPos);    //音频流缓冲区

    ushort	uDataLen	= CharsToShort(&pData[6]);		// 数据长度
    int streamLen = uDataLen-6-2;

    bool bNeedTranscoding=false;    //是否需要服务器进行转码
    if(audioCoding!=ALGORITHM_722)
    {
        bNeedTranscoding=true;
    }
    if(bNeedTranscoding)
    {
        G722_ENC_CTX *g722_enc_ctx = device.m_pAudioMixer->Get_G722_enc_ctx();
        unsigned char outbuf[256]={0};
        int encoder_bytes=g722_encode(g722_enc_ctx, (short*)(pData+dataPos), streamLen/2, outbuf);
        stremBuf=outbuf;
        streamLen=streamLen/4;
    }

    uDataLen = 6+2+streamLen; //6个字节的MAC地址+1个字节的音频编码+1个字节的信号类型+码流G722码流;

    char* cbuf = new char[1500];//足够长
    int streamPos=8+6+2;
    memcpy(cbuf,pData,streamPos);   //先复制原始包前面16个字节数据
    cbuf[4] = MODEL_HOST;	// 设备型号修改为主机
    cbuf[6] = uDataLen/256;
    cbuf[7] = uDataLen%256;
    if(bNeedTranscoding)
    {
        cbuf[8+6] = ALGORITHM_722;
    }
    memcpy(cbuf+streamPos,stremBuf,streamLen);
    cbuf[8+uDataLen] = CProtocol::GetChecksum(cbuf+8, uDataLen);
    int packageLen = 9+uDataLen;

    //将音频流转发至指定的设备中
    //通知绑定的分区
    for(int i=0;i<g_Global.m_Sections.GetSecCount();i++)
    {
        CSection &Section = g_Global.m_Sections.GetSection(i);
        if(Section.IsOnline() && (Section.GetDeviceModel() == MODEL_IP_SPEAKER_D || Section.GetDeviceModel() == MODEL_IP_SPEAKER_E || Section.GetDeviceModel() == MODEL_IP_SPEAKER_F\
            || Section.GetDeviceModel() == MODEL_IP_SPEAKER_G) )
        {
            if(device.m_pAudioMixer->HasSectionMac(Section.GetMac()))
            {
                if(!Section.IsMixedIn() && Section.GetAudioMixedSourceValid())
                {
                    ProgramSource secSrc = Section.GetProSource();
                    if (g_Global.m_PlayQueue.GetProSourcePriority(PRO_AUDIO_MIXED) >= g_Global.m_PlayQueue.GetProSourcePriority(secSrc))
                        g_Global.m_Network.m_CmdSend.CmdSetAudioMixerSourceInfo(Section,device,AUDIO_MIXER_EVENT_RETRY);
                }
                else if( Section.IsTcpMode() )
                {
                    #if 1
                    if(Section.m_kcpsocket && Section.m_kcpsocket->GetDesPort() && Section.m_kcpsocket->GetSendValid() && !STREAM_ALLWAYS_USED_TCP)
                    {
                        Section.m_kcpsocket->SendData(1,cbuf,packageLen);
                    }
                    else
                    #endif
                    {
                        g_Global.m_Network.SendTcpData(cbuf, packageLen, Section.m_pSocketObj);
                    }
                }
            }
        }
    }

    delete[] cbuf;
}




// 处理主机向混音器/终端设置混音音源时，终端的应答
void  CCommandHandle::HandleSetAudioMixerSource(const char*	pData,		// 数据区
                                     unsigned short	nLen,		// 数据长度
                                     CSection& device)			// 设备
{
    int result=pData[0];
    if (result == 0x02)	// 设备拒绝播放，重发的时候不再播放
    {
        printf("HandleSetAudioMixerSource:MAC=%s,Refuse!\n",device.GetMac());
        device.SetAudioMixedSourceValid(false);
    }
    else if(result == 0x01)    // 设备接受播放
    {
        printf("HandleSetAudioMixerSource:MAC=%s,Accept!\n",device.GetMac());
        device.SetAudioMixedSourceValid(true);
    }
}

#endif




// 查询/设置信息发布参数
void  CCommandHandle::HandleInformationPublishConfig(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device)			// 设备
{
    //判断是查询还是设置的应答
    u_char	dataPos		= 0;
    bool bSet = ((BYTE)pData[dataPos++]) == OPER_SET ? TRUE:FALSE;

    int nEnableDisplay = pData[dataPos++];
    int nTextLen = (BYTE)pData[dataPos++];
    char strText[MAX_INFORMATION_PUBLISH_TEXT_BYTES]={0};
    memcpy(strText,pData+dataPos,nTextLen);
    dataPos+=nTextLen;
    int nEffects = (BYTE)pData[dataPos++];
    int nMoveSpeed = (BYTE)pData[dataPos++];
    int nStayTime = (BYTE)pData[dataPos++];


    device.m_pInformationPublish.m_bEnableDisplay = nEnableDisplay;
    sprintf(device.m_pInformationPublish.m_szText,"%s",strText);
    device.m_pInformationPublish.m_nEffects = nEffects;
    device.m_pInformationPublish.m_nMoveSpeed = nMoveSpeed;
    device.m_pInformationPublish.m_nStayTime = nStayTime;
    printf("HandleInformationPublishConfig:m_szText=%s,len=%d\n",device.m_pInformationPublish.m_szText,strlen(device.m_pInformationPublish.m_szText));
    
    //通知WEB
    string strBuf = CWebProtocol::CmdResponseInformationPublishInfo(device.GetMac(), bSet, 
                                            device.m_pInformationPublish.m_bEnableDisplay,
                                            device.m_pInformationPublish.m_szText,
                                            device.m_pInformationPublish.m_nEffects,
                                            device.m_pInformationPublish.m_nMoveSpeed,
                                            device.m_pInformationPublish.m_nStayTime);
    g_Global.m_WebNetwork.m_WebSend.ForwardDataToWeb(NULL, strBuf, false);
}


#if SUPPORT_PHONE_GATEWAY

// 查询/设置电话网关参数
void  CCommandHandle::HandlePhoneGatewayConfig(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device)			// 设备
{
    if(!device.IsPhoneGatewayDevice())
        return;
    //判断是查询还是设置的应答
    u_char	dataPos		= 0;
    bool bSet = ((BYTE)pData[dataPos++]) == OPER_SET ? TRUE:FALSE;

    int nMasterSwitch = pData[dataPos++];
    int nZoneVolume = (BYTE)pData[dataPos++];   //一定要用BYTE类型转换，否则异常

    printf("nZoneVolume=%d\n",nZoneVolume);

    vector<CMyString> vecSecMacs;
    device.m_pPhoneGateway->GetSelectedSections(vecSecMacs);

    int strTelWhitelistLen=(BYTE)pData[dataPos++];   //一定要用BYTE类型转换，否则异常
    char telWhitelist[MAX_PHONE_GATEWAY_WHITELIST_LENGTH]={0};
    if(strTelWhitelistLen>MAX_PHONE_GATEWAY_WHITELIST_LENGTH-1)
        strTelWhitelistLen=MAX_PHONE_GATEWAY_WHITELIST_LENGTH;
    memcpy(telWhitelist,pData+dataPos,strTelWhitelistLen);
    printf("strTelWhitelistLen=%d,strTelWhitelist=%s\n",strTelWhitelistLen,telWhitelist);

    CPhoneGateway phoneGatewayNew;
    phoneGatewayNew.SetMasterSwitch(nMasterSwitch);
    phoneGatewayNew.SetSection(vecSecMacs);
    phoneGatewayNew.SetTelWhitelist(telWhitelist);
    //20230906 分区音量由服务器自身存储决定，不由混音器决定
    nZoneVolume=device.m_pPhoneGateway->GetVolume();
    phoneGatewayNew.SetVolume(nZoneVolume);

    printf("HandlePhoneGatewayConfig:bSet=%d\n",bSet);
    printf("nMasterSwitch=%d,nZoneVolume=%d\n",
            nMasterSwitch,nZoneVolume);

    //判断是否改变，注意改变后如果设备已经处于混音状态，如何处理？这个让音频混音器自身去处理。
    //常规做法：音频混音器收到后判断参数是否变化，如果变化：
    //1、混音器原主开关打开，现在主开关关闭，如果原信号=1，那么将信号设置为0，并立即发送send_online_info，由服务器终端通知绑定的终端退出混音器音源
    //   特别的，如果当前信号为0，不需要处理。
    //2、混音器原主开关关闭，现在主开关打开，不需要特殊处理
    if(!(*device.m_pPhoneGateway==phoneGatewayNew))
    {
        device.m_pPhoneGateway->SetMasterSwitch(phoneGatewayNew.GetMasterSwitch());
        device.m_pPhoneGateway->SetVolume(phoneGatewayNew.GetVolume());
        device.m_pPhoneGateway->SetTelWhitelist(telWhitelist);
        
        printf("device.m_pPhoneGateway!=&phoneGatewayNew...\n");
        //保存xml文件
        g_Global.WriteXmlFile(FILE_PHONE_GATEWAY);
    }
    
    //通知WEB
    string strBuf = CWebProtocol::CmdSetPhoneGatewayParm(device.GetMac(), bSet, nMasterSwitch, nZoneVolume, vecSecMacs, telWhitelist );
    g_Global.m_WebNetwork.m_WebSend.ForwardDataToWeb(NULL, strBuf, false);
}




// 处理电话网关数据流发送（TCP)
void  CCommandHandle::HandlePhoneGatewayStream(const char*	pData,		// 数据区
                                     unsigned short	nLen,		// 数据长度
                                     CSection& device)			// 设备
{
    if(!device.IsPhoneGatewayDevice())
        return;
    u_char	dataPos		= 8;
    
    //前6个字节为电话网关的mac地址，跳过
    dataPos+=6;
    int audioCoding = pData[dataPos++];

    unsigned char *stremBuf=(unsigned char *)(pData+dataPos);    //音频流缓冲区

    ushort	uDataLen	= CharsToShort(&pData[6]);		// 数据长度
    int streamLen = uDataLen-6-1;

    bool bNeedTranscoding=false;    //是否需要服务器进行转码
    if(audioCoding!=ALGORITHM_722)
    {
        bNeedTranscoding=true;
    }
    if(bNeedTranscoding)
    {
        G722_ENC_CTX *g722_enc_ctx = device.m_pPhoneGateway->Get_G722_enc_ctx();
        unsigned char outbuf[256]={0};
        int encoder_bytes=g722_encode(g722_enc_ctx, (short*)(pData+dataPos), streamLen/2, outbuf);
        stremBuf=outbuf;
        streamLen=streamLen/4;
    }

    uDataLen = 6+1+streamLen; //6个字节的MAC地址+1个字节的音频编码+码流G722码流;
    //printf("streamLen=%d,uDataLen=%d\n",streamLen,uDataLen);
    char* cbuf = new char[1500];//足够长
    int streamPos=8+6+1;
    memcpy(cbuf,pData,streamPos);   //先复制原始包前面16个字节数据
    cbuf[4] = MODEL_HOST;	// 设备型号修改为主机
    cbuf[6] = uDataLen/256;
    cbuf[7] = uDataLen%256;
    if(bNeedTranscoding)
    {
        cbuf[8+6] = ALGORITHM_722;
    }
    memcpy(cbuf+streamPos,stremBuf,streamLen);
    cbuf[8+uDataLen] = CProtocol::GetChecksum(cbuf+8, uDataLen);
    int packageLen = 9+uDataLen;

    //将音频流转发至指定的设备中
    //通知绑定的分区
    for(int i=0;i<g_Global.m_Sections.GetSecCount();i++)
    {
        CSection &Section = g_Global.m_Sections.GetSection(i);
        if(Section.IsOnline() && (Section.GetDeviceModel() == MODEL_IP_SPEAKER_D || Section.GetDeviceModel() == MODEL_IP_SPEAKER_E || Section.GetDeviceModel() == MODEL_IP_SPEAKER_F\
            || Section.GetDeviceModel() == MODEL_IP_SPEAKER_G))
        {
            if(device.m_pPhoneGateway->HasSectionMac(Section.GetMac()))
            {
                if(!Section.IsPhoneGatewayIn() && Section.GetPhoneGatewaySourceValid())
                {
                    ProgramSource secSrc = Section.GetProSource();
                    if (g_Global.m_PlayQueue.GetProSourcePriority(PRO_PHONE_GATEWAY) >= g_Global.m_PlayQueue.GetProSourcePriority(secSrc))
                        g_Global.m_Network.m_CmdSend.CmdSetPhoneGatewaySourceInfo(Section,device,AUDIO_PHONE_GATEWAY_EVENT_RETRY);
                }
                else if( Section.IsTcpMode() )
                {
                    #if 1
                    if(Section.m_kcpsocket && Section.m_kcpsocket->GetDesPort() && Section.m_kcpsocket->GetSendValid() && !STREAM_ALLWAYS_USED_TCP)
                    {
                        Section.m_kcpsocket->SendData(1,cbuf,packageLen);
                    }
                    else
                    #endif
                    {
                        g_Global.m_Network.SendTcpData(cbuf, packageLen, Section.m_pSocketObj);
                    }
                }
            }
        }
    }

    delete[] cbuf;
}




// 处理主机向电话网关/终端设置混音音源时，终端的应答
void  CCommandHandle::HandleSetPhoneGatewaySource(const char*	pData,		// 数据区
                                     unsigned short	nLen,		// 数据长度
                                     CSection& device)			// 设备
{
    int result=pData[0];
    if (result == 0x02)	// 设备拒绝播放，重发的时候不再播放
    {
        printf("HandleSetAudioMixerSource:MAC=%s,Refuse!\n",device.GetMac());
        device.SetPhoneGatewaySourceValid(false);
    }
    else if(result == 0x01)    // 设备接受播放
    {
        printf("HandleSetAudioMixerSource:MAC=%s,Accept!\n",device.GetMac());
        device.SetPhoneGatewaySourceValid(true);
    }
}

#endif



#if SUPPORT_AMP_CONTROLER

// 查询/设置功放控制参数
void  CCommandHandle::HandleAmpControlerStatus(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device)			// 设备
{
    if(!device.IsAmpControlerDevice())
        return;
    //判断是查询还是设置的应答
    u_char	dataPos		= 0;
    bool bSet = ((BYTE)pData[dataPos++]) == OPER_SET ? TRUE:FALSE;

    CAmpControler ampControler;

    bool bEnable = pData[dataPos++];

    int nMasterChannelNum = pData[dataPos++];
    int nBackupChannelNum = pData[dataPos++];
    #if 0
    for(int i=0;i<nMasterChannelNum;i++)
    {
    	ampControler.masterChannelStatusArray[i] = pData[dataPos++];
        printf("HandleAmpControlerStatus:masterChannelStatusArray[%d]=%d\n",i,ampControler.masterChannelStatusArray[i]);
    }
    #endif
    ampControler.backupChannelStatus = pData[dataPos++];
    ampControler.backupChannelId = pData[dataPos++];
    if(!(*device.m_pAmpControler==ampControler))
    {
        memcpy(device.m_pAmpControler->masterChannelStatusArray,ampControler.masterChannelStatusArray,sizeof(ampControler.masterChannelStatusArray));
        device.m_pAmpControler->backupChannelStatus = ampControler.backupChannelStatus;
        device.m_pAmpControler->backupChannelId = ampControler.backupChannelId;
    }
    #if 0
    printf("HandleAmpControlerStatus:backupChannelStatus=%d,backupChannelId=%d\n",
            device.m_pAmpControler->backupChannelStatus,device.m_pAmpControler->backupChannelId);
    #endif
    //通知WEB
    string strBuf = CWebProtocol::CmdSetAmpControler(device.GetMac(), bSet, device.m_pAmpControler->masterChannelStatusArray, device.m_pAmpControler->backupChannelStatus, device.m_pAmpControler->backupChannelId );
    g_Global.m_WebNetwork.m_WebSend.ForwardDataToWeb(NULL, strBuf, false);
}
#endif


#if SUPPORT_NOISE_DETECTOR

// 查询/设置功放控制参数
void  CCommandHandle::HandleNoiseDetectorConfig(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device)			// 设备
{
    if(!device.IsNoiseDetectorDevice())
        return;
    //判断是查询还是设置的应答
    u_char	dataPos		= 0;
    bool bSet = ((BYTE)pData[dataPos++]) == OPER_SET ? TRUE:FALSE;

    CNoiseDetector noiseDetector;

    noiseDetector.isEnable = pData[dataPos++];

    for(int i=0;i<NOISE_NUM_CHANNELS;i++)
    {
        noiseDetector.channelVal[i] = (pData[dataPos]<<8) + pData[dataPos+1];
        dataPos += 2;
    }
    for(int i=0;i<NOISE_NUM_SEGMENTS;i++)
    {
        noiseDetector.segmentVol[i] = pData[dataPos++];
    }

    //不改变enable状态，以服务器为准
    #if 1
    noiseDetector.isEnable = device.m_pNoiseDetector->isEnable;
    #endif

    if(!(*device.m_pNoiseDetector==noiseDetector))
    {
        memcpy(device.m_pNoiseDetector->channelVal,noiseDetector.channelVal,sizeof(noiseDetector.channelVal));
        memcpy(device.m_pNoiseDetector->segmentVol,noiseDetector.segmentVol,sizeof(noiseDetector.segmentVol));
        device.m_pNoiseDetector->isEnable = noiseDetector.isEnable;
    }
    //通知WEB
    string strBuf = CWebProtocol::CmdSetNoiseDetector(device.GetMac(), bSet, *device.m_pNoiseDetector );
    g_Global.m_WebNetwork.m_WebSend.ForwardDataToWeb(NULL, strBuf, false);
}
#endif


// 查询/设置音频采集器参数
void  CCommandHandle::HandleAudioCollectorConfig(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device)			// 设备
{
    if(!device.IsAudioCollectorDevice())
        return;
    //判断是查询还是设置的应答
    u_char	dataPos		= 0;
    bool bSet = ((BYTE)pData[dataPos++]) == OPER_SET ? TRUE:FALSE;

    int nTriggerSwitch = pData[dataPos++];
    int nTriggerChannelId = pData[dataPos++];
    int nZoneVolume = (BYTE)pData[dataPos++];   //一定要用BYTE类型转换，否则异常

    vector<CMyString> vecSecMacs;
    device.m_pAudioCollector->GetSelectedSections(vecSecMacs);

    CAudioCollector audioCollectorNew(device.m_pAudioCollector->GetId(),device.GetDeviceModel());
    audioCollectorNew.SetTriggerSwitch(nTriggerSwitch);
    audioCollectorNew.SetTriggerChannelId(nTriggerChannelId);
    audioCollectorNew.SetTriggerVolume(nZoneVolume);
    audioCollectorNew.SetSection(vecSecMacs);

    //不改变优先级，以服务器为准
    unsigned char nPriority = device.m_pAudioCollector->GetPriority();
    audioCollectorNew.SetPriority(nPriority);
    audioCollectorNew.SetChannelNameArray(device.m_pAudioCollector->GetChannelNameArray());

    printf("HandleAudioCollectorConfig:bSet=%d\n",bSet);
    printf("nTriggerSwitch=%d,nTriggerChannelId=%d,nZoneVolume=%d\n",
            nTriggerSwitch,nTriggerChannelId,nZoneVolume);

    //判断是否改变，注意改变后如果设备已经处于混音状态，如何处理？这个让音频混音器自身去处理。
    //常规做法：音频混音器收到后判断参数是否变化，如果变化：
    //1、混音器原主开关打开，现在主开关关闭，如果原信号=1，那么将信号设置为0，并立即发送send_online_info，由服务器终端通知绑定的终端退出混音器音源
    //   特别的，如果当前信号为0，不需要处理。
    //2、混音器原主开关关闭，现在主开关打开，不需要特殊处理
    if(!(*device.m_pAudioCollector==audioCollectorNew))
    {
        device.m_pAudioCollector->SetTriggerSwitch(audioCollectorNew.GetTriggerSwitch());
        device.m_pAudioCollector->SetTriggerChannelId(audioCollectorNew.GetTriggerChannelId());
        device.m_pAudioCollector->SetTriggerVolume(audioCollectorNew.GetTriggerVolume());
        
        printf("device.m_pAudioCollector!=&audioCollectorNew...\n");
        //保存xml文件
        g_Global.WriteXmlFile(FILE_AUDIO_COLLECTOR);
    }

    //通知WEB
    string strBuf = CWebProtocol::CmdSetAudioCollectorParm(device.GetMac(), bSet, nTriggerSwitch, nTriggerChannelId, nZoneVolume, nPriority, vecSecMacs,device.m_pAudioCollector->GetChannelNameArray());
    g_Global.m_WebNetwork.m_WebSend.ForwardDataToWeb(NULL, strBuf, false);

}



#if APP_IS_LZY_LIMIT_STORAGE
// 处理寻呼台获取账户存储容量
void  CCommandHandle::HandleGetAccountStorageCapacity(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device)			// 设备
{
    printf("HandleGetAccountStorageCapacity...\n");
    u_char	dataPos		= 0;

    int account_len=0;
    char account[64]={0};
    UINT64 storage_capacity=0;
    UINT64 storage_used=0;
    UINT64 storage_remaining=0;
    int compress_bitrate=0;

    account_len = pData[dataPos++];
    memcpy(account,pData+dataPos,account_len);
    dataPos+=account_len;

    LPCUserInfo  pUser = g_Global.m_Users.GetUserByAccount(account);
    if(!pUser)
    {
        printf("HandleGetAccountStorageCapacity error:userAccount not exist!\n");
        return;
    }

#if 0
    //判断当前账号有没有发送过账户信息，没有的话，则将该账户作为登录账户。
    if( strcmp(account,device.GetUserAccount().data()) )
    {
        device.SetUserAccount(account);
    }
#endif

    //计算该账户的总存储空间
    storage_capacity = ((UINT64)pUser->GetStorageCapacity())*1024*1024;
    //计算该账户的已用存储空间（字节）
    storage_used = pUser->GetStorageUsedSpace();
    //计算该账户的剩余存储空间（字节）
    storage_remaining = pUser->GetStorageRemainingSpace();

    //压缩比特率
    compress_bitrate = SONG_LOW_BITRATE;

    printf("HandleGetAccountStorageCapacity:user=%s\n",account);
    printf("capacity=%lld,used=%lld,remaining=%lld\n",storage_capacity,storage_used,storage_remaining);

    g_Global.m_Network.m_CmdSend.CmdAccountStorageCapacity(device,account,storage_capacity,storage_used,storage_remaining,compress_bitrate);
}

// 处理寻呼台请求上传歌曲文件
void  CCommandHandle::HandleRequestUploadSong(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device)			// 设备
{
    printf("HandleRequestUploadSong...\n");
    u_char	dataPos		= 0;

    int uploadSongFileLength=0;
    char uploadSongFileName[128]={0};
    int  uploadListIDLength=0;
    char uploadListID[64]={0};
    int  songSize=0;            //歌曲文件大小
    int  compressSongSize=0;    //预估压缩后歌曲大小
    int  result=0;

    uploadSongFileLength = pData[dataPos++];
    memcpy(uploadSongFileName,pData+dataPos,uploadSongFileLength);
    dataPos+=uploadSongFileLength;

    uploadListIDLength = pData[dataPos++];
    memcpy(uploadListID,pData+dataPos,uploadListIDLength);
    dataPos+=uploadListIDLength;

    songSize=CharsToInt(&pData[dataPos]);
    dataPos+=4;
    compressSongSize=CharsToInt(&pData[dataPos]);
    dataPos+=4;

    LPCUserInfo  pUser = g_Global.m_Users.GetUserByAccount(device.GetUserAccount());
    if(!pUser)
    {
        printf("HandleRequestUploadSong error:userAccount not exist!\n");
        return;
    }

    //判断当前账户是否存在歌曲列表，如果没有，需要提示创建
    int firstIndexOfAccountList=-1;
    if( (firstIndexOfAccountList=g_Global.m_PlayList.FindFirstIndexOfAccountList(pUser->GetAccount())) == -1 )
    {
        result=1;   //无歌曲列表，需要创建
        //创建歌曲列表
        //********添加用户成功后添加一个歌曲列表，命名为Music List(账号名)
        CMyString strListID=GetGUID();
        CMyString cstrListName;
        cstrListName.Format("%s (%s)","Music List",pUser->GetAccount());
        g_Global.m_PlayList.AddList(CMyString(strListID), cstrListName,pUser->GetAccount());
        // 写入文件
        int nListIndex = g_Global.m_PlayList.FindListByIdAndName(CMyString(strListID),cstrListName);
        if(nListIndex>=0)
        {
            g_Global.m_PlayList.SetListDateTime(nListIndex, GetCurrentDateTime());
            g_Global.m_PlayList.PushWriteXmlTask(pUser->GetAccount());
        }
    }

    //判断歌曲是否已经存在
    if(result == 0)
    {
        //判断歌曲是否已经存在
        CMyString cstrPathName,cstrPathName2;
        cstrPathName.Format("/%s/%s/%s",HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_OTHER, uploadSongFileName);

        //下面两种情况效果一样
#if 0
        CMyString songName = uploadSongFileName;
        string::size_type extDogIndex=songName.ReverseFind(('.'));
        CMyString extWithDot = songName.Mid(extDogIndex);
        CMyString songNameWithoutDot = songName.Left(extDogIndex);
        printf("extWithDot=%s,songNameWithoutDot=%s\n",extWithDot.Data(),songNameWithoutDot.Data());
        CMyString destNameWithExt;
        destNameWithExt.Format("%s(%s)%s",songNameWithoutDot.Data(),pUser->GetAccount(),extWithDot.Data());
        cstrPathName2.Format("/%s/%s/%s",HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_OTHER, destNameWithExt.Data());
#else
        CMyString songName=GetNameByHttpPathName(cstrPathName);
        CMyString extWithDot = cstrPathName.Mid(cstrPathName.ReverseFind(('.')));
        CMyString destNameWithExt;
        destNameWithExt.Format("%s(%s)%s",songName.Data(),pUser->GetAccount(),extWithDot.Data());
        cstrPathName2.Format("/%s/%s/%s", HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_OTHER, destNameWithExt.Data());
#endif

        printf("cstrPathName=%s,cstrPathName2=%s\n",cstrPathName.Data(),cstrPathName2.Data());
        CSong song;

        //先判断原文件名是否存在
        if(g_Global.m_SongManager.GetSongByPathName(cstrPathName,song))
        {
            //歌曲存在,判断这首歌的所有者是不是该账户，如果不是的话，还要继续上传
            if(song.GetUserAccount() == pUser->GetAccount())
            {
                //是当前账户，判断有没有加入到该账户的列表中，如果没有，那么加入到第一个列表，如果有，那么返回1，代表歌曲已上传
                if(g_Global.m_PlayList.IsSongInPlayListBySpecAccount(cstrPathName,pUser->GetAccount()))
                {
                    result=2;   //歌曲已上传
                }
                else
                {
                    result=3;   //歌曲存在媒体库，但未加入到歌曲列表，现在将其加入
                    //todo 将其加入到指定用户的第一个列表

                    printf("InterMove...\n");
                    g_Global.m_PlayList.AddListSong(firstIndexOfAccountList, song);
                    g_Global.m_PlayList.SetListDateTime(firstIndexOfAccountList, GetCurrentDateTime());
                    g_Global.m_PlayList.PushWriteXmlTask(pUser->GetAccount());
                }
            }
        }
        //如果原文件名没有找到，继续以填充账户名结尾的歌曲名去查找
        if(result==0)
        {
            if(g_Global.m_SongManager.GetSongByPathName(cstrPathName2,song))
            {
                //歌曲存在,判断这首歌的所有者是不是该账户，如果不是的话，还要继续上传
                if(song.GetUserAccount() == pUser->GetAccount())
                {
                    //是当前账户，判断有没有加入到该账户的列表中，如果没有，那么加入到第一个列表，如果有，那么返回1，代表歌曲已上传
                    if(g_Global.m_PlayList.IsSongInPlayListBySpecAccount(cstrPathName2,pUser->GetAccount()))
                    {
                        result=2;   //歌曲已上传
                    }
                    else
                    {
                        result=3;   //歌曲存在媒体库，但未加入到歌曲列表，现在将其加入
                        //todo 将其加入到指定用户的第一个列表

                        printf("InterMove...\n");
                        g_Global.m_PlayList.AddListSong(firstIndexOfAccountList, song);
                        g_Global.m_PlayList.SetListDateTime(firstIndexOfAccountList, GetCurrentDateTime());
                        g_Global.m_PlayList.PushWriteXmlTask(pUser->GetAccount());
                    }
                }
            }
        }
        

        //判断存储空间是否足够存下此歌曲
        if(result == 0)
        {
            if(pUser->GetStorageRemainingSpace()<compressSongSize)
            {
                result=4;   //存储空间超出限制
            }
        }
    }

    //确认上传地址
    char szcgiAddress[STR_MAX_PATH] = {0};
    // HTTP路径 保留，待修改
    sprintf(szcgiAddress,"/cgi-bin/Addsong.cgi"); //组成可远端下载的URL
    printf("result=%d,szcgiAddress=%s\n",result,szcgiAddress);
    g_Global.m_Network.m_CmdSend.CmdRequestUploadSong(device,result,CNetwork::GetHostIP(),g_Global.m_HTTP_PORT,szcgiAddress);
}



// 处理寻呼台通知上传状态
void  CCommandHandle::HandleUploadSongStatus(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device)			// 设备
{
    printf("HandleUploadSongStatus...\n");
    u_char	dataPos		= 0;

    int uploadSongFileLength=0;
    char uploadSongFileName[128]={0};
    int  uploadListIDLength=0;
    char uploadListID[64]={0};
    int  event=0;            //上传事件状态
    int  result=0;

    event = pData[dataPos++];

    uploadListIDLength = pData[dataPos++];
    memcpy(uploadListID,pData+dataPos,uploadListIDLength);
    dataPos+=uploadListIDLength;

    uploadSongFileLength = pData[dataPos++];
    memcpy(uploadSongFileName,pData+dataPos,uploadSongFileLength);
    dataPos+=uploadSongFileLength;

    LPCUserInfo  pUser = g_Global.m_Users.GetUserByAccount(device.GetUserAccount());
    if(!pUser)
    {
        printf("HandleRequestUploadSong error:userAccount not exist!\n");
        return;
    }

    int listIdIndex=-1;

    string strSongName = uploadSongFileName;
    // 组合歌曲绝对路径
    CMyString strSongPath,strSongPathTTS;
    strSongPath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_OTHER, strSongName.data());

    if(event == 1)  //上传完成
    {
        //判断listID是否存在，且列表是否属于管理员，或者该账户是管理员账户
        //如果列表id为空，那么使用账户的第一个列表
        if(strlen(uploadListID) == 0)
        {
            listIdIndex=g_Global.m_PlayList.FindFirstIndexOfAccountList(pUser->GetAccount());
        }
        else
        {
            listIdIndex=g_Global.m_PlayList.GetListByID(uploadListID);
        }
        if( listIdIndex == -1 )
        {
            printf("uloadListID=%s\n",uploadListID);
            result=1;   //上传失败，列表不存在
        }
        else if(!(g_Global.m_PlayList.GetSongList(listIdIndex).GetUserAccount() == pUser->GetAccount() || pUser->IsSuperUser()))
        {
            result=2;   //上传失败，列表权限不符
        }
        else
        {
            //判断上传的歌曲是否存在(临时文件夹中获取)
            #if defined(Q_OS_LINUX)
            //******** cgi将文件保存到临时目录下（linux:/tmp)，需要判断歌曲目录下是否已经存在此歌曲，
            //如果存在，与原歌曲同账户则覆盖移动，不同账户则重命名移动
            //如果不存在，直接拷贝到歌曲目录
            CMyString strTmpSongPath;
            strTmpSongPath.Format("/tmp/%s",strSongName.data());
            if(IsExistFile(strTmpSongPath.C_Str()))
            {
                if(IsExistFile(strSongPath.C_Str()))
                {
                    //判断之前的用户和现在的用户是否一致，admin以及歌曲本身的用户可以直接覆盖，其他的要改名：原歌曲名(用户名)
                    CSong tsong;
                    CMyString strHttpPathName = GetHttpURLPathByPathName(strSongPath);
                    bool must_changeName=false;
                    if(g_Global.m_SongManager.GetSongByPathName(strHttpPathName,tsong))
                    {
                        //******** 即使是管理员，如果歌曲存在，也要改名，避免管理员重新上传后用户更改成admin，子用户找不到
                        if(!(tsong.GetUserAccount() == pUser->GetAccount())) //|| pUser->IsSuperUser()))
                        {
                            must_changeName=true;
                        }
                    }
                    if(!must_changeName)    //同账户或者管理员上传，那么直接覆盖
                    {
                        if(FileMove(strTmpSongPath.Data(),strSongPath.Data()))
                        {
                            printf("FileMove succeed1!\n");
                        }
                        else
                        {
                            printf("FileMove failed1!\n");
                            //移动失败，应该是目的文件夹已经存在同名文件，此时应该删除旧的，然后再次移动
                            RemoveFile(strSongPath.C_Str());
                            if(FileMove(strTmpSongPath.Data(),strSongPath.Data()))
                            {
                                printf("FileMove succeed11!\n");
                            }
                        }
                    }
                    else    //其他情况需要改变并上传
                    {
                        CMyString songName=GetNameByHttpPathName(strHttpPathName);
                        CMyString extWithDot = strSongPath.Mid(strSongPath.ReverseFind(('.')));
                        CMyString destNameWithExt;
                        destNameWithExt.Format("%s(%s)%s",songName.Data(),pUser->GetAccount(),extWithDot.Data());
                        strSongPath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_OTHER, destNameWithExt.Data());
                        
                        if(FileMove(strTmpSongPath.Data(),strSongPath.Data()))
                        {
                            printf("FileMove succeed2!\n");
                        }
                        else
                        {
                            printf("FileMove failed2!\n");
                            //移动失败，应该是目的文件夹已经存在同名文件，此时应该删除旧的，然后再次移动
                            RemoveFile(strSongPath.C_Str());
                            if(FileMove(strTmpSongPath.Data(),strSongPath.Data()))
                            {
                                printf("FileMove succeed22!\n");
                            }
                        } 
                    }
                }
                else    //歌曲目录没有这首歌，直接移动
                {
                    if(FileMove(strTmpSongPath.Data(),strSongPath.Data()))
                    {
                        printf("FileMove succeed3!\n");
                    }
                    else
                    {
                        printf("FileMove failed3!\n");
                            //移动失败，应该是目的文件夹已经存在同名文件，此时应该删除旧的，然后再次移动
                        RemoveFile(strSongPath.C_Str());
                        if(FileMove(strTmpSongPath.Data(),strSongPath.Data()))
                        {
                            printf("FileMove succeed33!\n");
                        }
                    }
                }
            }   //为了兼容意外情况（cgi没有更新的情况）,没有在临时文件夹找到歌曲文件也没关系，下面进一步判断
            #endif
            if(IsExistFile(strSongPath.C_Str()))
            {
                song_header_t  songHeader=g_Global.m_SongTool.GetSongInfo(strSongPath);
                int		nDuration	  = songHeader.nDuration;
                int		nSize		  = g_Global.m_SongTool.GetSize(strSongPath);
                if(nDuration >0 && nSize >0)
                {
                    CMyString strHttpPathName = GetHttpURLPathByPathName(strSongPath);
                    CSong	song;
                    song.SetPathName(strHttpPathName);
                    song.SetDuration(nDuration);
                    song.SetSize(nSize);
                    song.SetBitRate(songHeader.bitrate);
                    song.SetAlive(true);
                    song.SetMd5(GetFileMd5(strSongPath.Data()));

                    //song.SetLowRateFile(song.GetPathName());

                    song.SetUserAccount(pUser->GetAccount());

                    //判断是否属于低码率歌曲，如果不是，需要进行转换
                    double lbrcSize = nSize;
                    if( song.GetBitRate() > SONG_LOW_BITRATE )
                    {
                        //如果是龙之音V1版本，将lbrc歌曲文件的大小预置
                        #if APP_IS_LZY_LIMIT_STORAGE
                        double lbrcDiv=song.GetBitRate()/SONG_LOW_BITRATE;
                        lbrcSize=nSize/lbrcDiv;
                        song.SetLowRateSize(lbrcSize);
                        #endif
                    }

                    //******** 判断账户空间是否足够存下此歌曲
                    if(pUser->GetStorageRemainingSpace()<lbrcSize)
                    {
                        result = 5; //上传失败，存储空间不足
                    }
                    else
                    {
                        if( song.GetBitRate() > SONG_LOW_BITRATE )
                        {
                            CMyString brcSongPath=strSongPath+SONG_LOW_RATE_EXT_NAME;
                            RemoveFile(brcSongPath.C_Str());
                            g_Global.m_PlayList.AddLowRateSong(song.GetPathName());
                        }

                        g_Global.m_SongManager.AddSong(song);
                        //加入到播放列表
                        //判断列表里面有没有相应歌曲，已经有的话就不要再加入了
                        if(!g_Global.m_PlayList.IsSongInPlayList(listIdIndex,song.GetPathName()))
                            g_Global.m_PlayList.AddListSong(listIdIndex, song);
                    }
                }
                else
                {
                    result = 4; //上传失败，格式不符
                    //歌曲错误，删除之
                    printf("Song:%s error,delete!\n",strSongPath.Data());
                    RemoveFile(strSongPath.C_Str());
                }
            }
            else
            {
                result = 3; //上传失败，歌曲不存在
            }
        }
    }
    else if(event == 2) //取消上传
    {
        //将歌曲从临时文件夹中删除
        #if defined(Q_OS_LINUX)
        CMyString strTmpSongPath;
        strTmpSongPath.Format("/tmp/%s",strSongName.data());
        RemoveFile(strTmpSongPath.C_Str());
        #else
        RemoveFile(strSongPath.C_Str());
        #endif

        result = 6; //回应用户取消上传
    }

    printf("HandleUploadSongStatus:result=%d\n",result);
    if(result == 0)
    {
        //上传完成，需要先通知本地歌曲变更，然后再变更列表
        g_Global.m_WebNetwork.ForwardLocalSongInfoToWeb(NULL);

        g_Global.m_PlayList.SetListDateTime(listIdIndex, GetCurrentDateTime());
        g_Global.m_PlayList.PushWriteXmlTask(pUser->GetAccount());
    }
   
    g_Global.m_Network.m_CmdSend.CmdUploadSongStatus(device,result);
}



// 处理寻呼台请求删除歌曲
void  CCommandHandle::HandleRequestDeleteSong(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device)			// 设备
{
    printf("HandleRequestDeleteSong...\n");
    u_char	dataPos		= 0;

    int uploadSongFileLength=0;
    char uploadSongFileName[128]={0};
    int  uploadListIDLength=0;
    char uploadListID[64]={0};
    int  event=0;            //上传事件状态
    int  result=0;

    event = pData[dataPos++];

    uploadListIDLength = pData[dataPos++];
    memcpy(uploadListID,pData+dataPos,uploadListIDLength);
    dataPos+=uploadListIDLength;

    uploadSongFileLength = pData[dataPos++];
    memcpy(uploadSongFileName,pData+dataPos,uploadSongFileLength);
    dataPos+=uploadSongFileLength;

    LPCUserInfo  pUser = g_Global.m_Users.GetUserByAccount(device.GetUserAccount());
    if(!pUser)
    {
        printf("HandleRequestDeleteSong error:userAccount not exist!\n");
        return;
    }

    CMyString noExtName=GetNameByHttpPathName(uploadSongFileName);

    //判断列表ID和列表歌曲是否存在且对应
    int listIndex=g_Global.m_PlayList.GetListByID(uploadListID);
    if(listIndex == -1)
    {
        result=1;   //删除失败
    }
    else
    {
        //判断此列表下是否存在对应歌曲
        bool found_song=false;
        for(int i=0;i<g_Global.m_PlayList.GetSongList(listIndex).GetSongCount();i++)
        {
            CSong song=g_Global.m_PlayList.GetSongList(listIndex).GetSong(i);
            if(song.GetName() == noExtName)
            {
                found_song=true;
                break;
            }
        }
        if(!found_song)
        {
            result=1;   //删除失败
        }
        else
        {
            //删除歌曲
            int nSongCount=1;
            int del_result=EC_SUCCESS;
            for(int i=0;i<nSongCount;i++)
            {
                del_result = EC_SUCCESS;
                CMyString cstrPathName, cstrPathNameM;
                cstrPathName.Format("/%s/%s/%s",HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_OTHER, uploadSongFileName);
                cstrPathNameM.Format("/%s/%s/%s",HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_MUSIC, uploadSongFileName);

                //如果listID为空字符，那么删除此歌曲文件
                //if(strListID == "")
                if(1)
                {
                    //判断权限
                    int operOk=1;
                    //找到上传此歌曲的用户名
                    CSong tsong;
                    if(g_Global.m_SongManager.GetSongByPathName(cstrPathName,tsong))
                    {
                        
                    }
                    else if(g_Global.m_SongManager.GetSongByPathName(cstrPathNameM,tsong))
                    {

                    }
                    else
                    {
                        operOk=0;
                        del_result = EC_TARGET_NOTEXIST;
                    }
                    if(operOk)
                    {
                        if( strcmp(pUser->GetAccount(),SUPER_USER_NAME) )
                        {
                            if( tsong.GetUserAccount() != CMyString(pUser->GetAccount()) && ((pUser->GetLimitCode() & USER_LIMITS_PLAYLIST) == 0) )
                            {
                                del_result = EC_NO_LIMIT;
                                operOk=0;
                            }
                        }
                        
                        if(operOk)
                        {
                            g_Global.m_SongManager.DeleteSong(tsong.GetPathName(),true);
                        }
                    }
                }
            }
            if(del_result!=EC_SUCCESS)
            {
                result=1;
            }
        }
    }


    printf("HandleRequestDeleteSong:result=%d\n",result);
   
    g_Global.m_Network.m_CmdSend.CmdRequestDeleteSong(device,result);
}

#endif



// 处理控制寻呼台向指定分区设备发起广播寻呼的应答
void  CCommandHandle::HandleSetBroadcastPaging(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device)			// 设备
{
    u_char	dataPos		= 0;
    int result = pData[dataPos++];
    int web_result = EC_SUCCESS;
    printf("HandleSetBroadcastPaging:result=%d\n",result);
    //告知WEB客户端
    if(result == 0)
    {
        web_result = EC_SUCCESS;
    }
    else if(result == 1 || result == 2)    //用户未登录
    {
        web_result = EC_USER_NOTLOGIN;
    }
    else
    {
        web_result = EC_TARGET_PARM_ERROR;
    }

    //成功的话就不需要再次告知了，因为收到WEB指令的时候已经应答过成功了。
    //if(web_result!=EC_SUCCESS)
    {
        string buf = CWebProtocol::CmdResponseSetBroadcastPaging(web_result);
        g_Global.m_WebNetwork.m_WebSend.ForwardDataToWeb(NULL, buf, false);
    }
}




// 处理终端向服务器发送json命令
void  CCommandHandle::HandleReceiveJsonCommand(const char*	pData,		// 数据区
                                     unsigned short	nLen,		// 数据长度
                                     CSection& device)			// 设备
{
    u_char	dataPos		= 8;
    ushort	uJsonLen	= CharsToShort(&pData[8]);		// 数据长度
    printf("HandleReceiveJsonCommand:uJsonLen=%d\n",uJsonLen);
    cJSON  *json= cJSON_ParseWithLength(pData+10,uJsonLen);
    char *cCommand=NULL;

    int  result = EC_SUCCESS;

    if(json)
    {
        cJSON* jsCommand = cJSON_GetObjectItem(json, "command");      //命令
        if(jsCommand)
        {
            cCommand = jsCommand->valuestring;
            printf("HandleReceiveJsonCommand:command=%s\n",cCommand);
            if(strcmp(cCommand,"play_tts_or_music") == 0)
            {
                cJSON* jsPlayType = cJSON_GetObjectItem(json, "play_type");      //播放类型， 1：歌曲播放 2： TTS
                cJSON* jsSourceType = cJSON_GetObjectItem(json, "source_type");  //音源类型， 1: 网络点播 2：消防告警
                cJSON* jsVolume = cJSON_GetObjectItem(json, "volume");           //播放音量，0-100
                cJSON* jsSongNames = cJSON_GetObjectItem(json, "song_names");    //歌曲名称数组集合（仅play_type=1播放歌曲有效,需包含扩展名）
                cJSON* jsPlayMode = cJSON_GetObjectItem(json, "play_mode");      // 播放模式 3：顺序播放 4：循环播放 5：随机播放，如果播放类型为TTS，则播放模式需固定为顺序播放
                cJSON* jsPlayCount = cJSON_GetObjectItem(json, "play_count");    // 播放次数，取值范围:1~999，仅play_mode=3顺序播放时有效
                

                cJSON* jsTTSPitch = cJSON_GetObjectItem(json, "tts_pitch");      // tts音调： 0-100(默认50)
                cJSON* jsTTSSpeed = cJSON_GetObjectItem(json, "tts_speed");      // tts语速： 0-100(默认50)
                cJSON* jsVoiceName = cJSON_GetObjectItem(json, "tts_voice_name"); // tts发音人： 男生：xiaofeng 女生：xiaoyan
                cJSON* jsTTSText = cJSON_GetObjectItem(json, "tts_text");       // tts文本


                vector<string> vecSecMacs;
                vecSecMacs.push_back(device.GetMac());
                unsigned int pSecIndexs[MAX_SECTION_COUNT_FORMAL] = {0};
                unsigned int uSecCount = 0;
                pSecIndexs[uSecCount++] = device.GetID() - 1;

                vector<CMyString> vecSongPaths;
                
                int nPlayType = jsPlayType->valueint;
                int nSourceType = jsSourceType->valueint;
                int nPlayVolume = jsVolume->valueint;
                int nPlayMode = jsPlayMode->valueint;
                int nPlayCount = 1;

                string strTTSName;
                int nTTSVolume = 100;
                int nTTSPitch = 50;
                int nTTSSpeed = 50;
                string strTTSVoiceName;
                string strTTSText;

                unsigned char source=SOURCE_SPECIFIED_PLAY;
                if(nSourceType == 1)    //网络点播
                {
                    source=SOURCE_SPECIFIED_PLAY;
                }
                else if(nSourceType == 2)   //消防告警
                {
                    source=SOURCE_SPECIFIED_ALARM;
                }

                
                if(nPlayType == 2)  //TTS，播放模式固定为3
                {
                    nPlayMode=3;
                }

                if(nPlayMode == 3) //顺序播放
                {   
                    nPlayCount = jsPlayCount?jsPlayCount->valueint:1;
                }
                
                if(nPlayType == 1)
                {
                    if(!jsSongNames)
                    {
                        free(json);
                        return;
                    }

                    int nSongPathCount=cJSON_GetArraySize(jsSongNames);
                    for(int i=0; i<nSongPathCount; i++)
                    {
                        cJSON* jsSongName = cJSON_GetArrayItem(jsSongNames, i);
                        if(jsSongName && jsSongName->valuestring != NULL)
                        {
                            string strSongName = jsSongName->valuestring;
                            if(strSongName.length() == 0)
                            {
                                continue;
                            }

                            CMyString strSongPathNameMusic,strSongPathNameTTS;
                            CMyString strAbsolutePathNameMusic,strAbsolutePathNameTTS;
                            strSongPathNameMusic.Format("/%s/%s/%s", HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_OTHER,strSongName.data());
                            strSongPathNameTTS.Format("/%s/%s/%s", HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_MUSIC,strSongName.data());    
                            strAbsolutePathNameMusic.Format("%s/%s/%s/%s", g_Global.m_strHttpRootDir.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_OTHER,strSongName.data());
                            strAbsolutePathNameTTS.Format("%s/%s/%s/%s", g_Global.m_strHttpRootDir.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_MUSIC,strSongName.data());    
                            
                            //printf("strSongPathNameMusic=%s\n",strSongPathNameMusic.Data());
                            //printf("strSongPathNameTTS=%s\n",strSongPathNameTTS.Data());

                            CSong song;
                            if(g_Global.m_SongManager.GetSongByPathName(strSongPathNameMusic,song))
                            {
                                vecSongPaths.push_back(strAbsolutePathNameMusic);
                                //printf("vecSongPaths=%s\n",strAbsolutePathNameMusic.Data());
                            }
                            else if(g_Global.m_SongManager.GetSongByPathName(strSongPathNameTTS,song))
                            {
                                vecSongPaths.push_back(strAbsolutePathNameTTS);
                                //printf("vecSongPaths=%s\n",strAbsolutePathNameTTS.Data());
                            }
                        }
                    }
                }
                else if(nPlayType == 2)
                {
                    strTTSName = "device";
                    nTTSPitch = jsTTSPitch->valueint;
                    nTTSSpeed = jsTTSSpeed->valueint;
                    strTTSVoiceName = jsVoiceName->valuestring;
                    strTTSText = jsTTSText->valuestring;

                    CMyString strTmpSongPath;
                    int tryCnt=100;
                    //判断文件名是否重复，如果重复，需要重新生成随机数
                    while(tryCnt--)
                    {
                        int key = rand() % 900 + 100;  //此时随机数生成区间为100-999(1000取不到)
                        string keyStr = to_string(key);
                        strTTSName+="_tts_";
                        strTTSName+=keyStr;
                        strTmpSongPath.Format("%s/%s.wav",g_Global.m_strTempFolderPath.Data(),strTTSName.data());

                        if(!IsExistFile(strTmpSongPath.C_Str()))
                            break;
                    }
                    
                    vecSongPaths.push_back(strTmpSongPath);
                }

                //歌曲数量或者分区数量为空，返回参数错误
                if(vecSongPaths.size() == 0 || vecSecMacs.size() == 0)
                {
                    result = EC_TARGET_PARM_ERROR;
                }
                //nSourceType不是网络点播且不是消防告警，返回参数错误
                if(!(nSourceType == 1 || nSourceType == 2))
                {
                    result = EC_TARGET_PARM_ERROR;
                }
                //playType不是播放音乐且playType不是播放TTS，返回参数错误
                if(!(nPlayType == 1 || nPlayType == 2))
                {
                    result = EC_TARGET_PARM_ERROR;
                }
                //音量错误，返回参数错误
                if(!(nPlayVolume>=0 && nPlayVolume<=100))
                {
                    result = EC_TARGET_PARM_ERROR;
                }
                //播放模式，返回参数错误
                if(!(nPlayMode>=PM_ORDER && nPlayMode<=PM_RANDOM))
                {
                    result = EC_TARGET_PARM_ERROR;
                }
                //播放次数，返回参数错误
                if(!(nPlayCount>=1 && nPlayCount<=999))
                {
                    result = EC_TARGET_PARM_ERROR;
                }

                if(result == EC_SUCCESS)
                {
                    #if 0
                    // 加到日志
                    CMyString strLogContents;
                    strLogContents.Format("%s:%s",  CProtocol::GetDescriptionProSource(PRO_LOCAL_PLAY).C_Str(),
                                                                strSourceName.data());
                    g_Global.m_logTable.InsertLog(	CMyString(pUser->GetAccount()),
                                                    CMyString(pUser->GetAccount()),
                                                    LT_PLAY_PROGRAM,
                                                    strLogContents);
                    #endif

                    //创建任务
                    CPlayTask playTask(source, vecSongPaths, pSecIndexs, uSecCount, SUPER_USER_NAME, nPlayMode, -1, -1, nPlayVolume, nPlayType, nPlayCount);
                    
                    if(nPlayType == 1)  //播放歌曲
                    {
                        if (!g_Global.m_PlayQueue.PushPlayTask(playTask))
                        {
                            result = EC_TARGET_FORMAT_ERROR;
                        }
                    }
                    #if 0
                    else if(nPlayType == 2)  //TTS
                    {
                        //固定nOnline=2 离线
                        int nOnline=2;
                        CTTSTask* tts= new CTTSTask(nOnline,SUPER_USER_NAME,strTTSName, strTTSVoiceName, nTTSVolume, nTTSPitch, nTTSSpeed, 0, Rate_16K, &playTask);
                        tts->SetVoiceName(strTTSVoiceName);
                        tts->SetSpeechText(strTTSText);
                        tts->SetUID("");
                        tts->SetCommand("play_specified_source");
                        //创建TTS线程，播放任务将在tts生成成功后创建
                        result = g_Global.m_TTSManager.TextToSpeech(*tts);
                    }
                    #endif
                }
            }
            else if(strcmp(cCommand,"set_idle") == 0)
            {
                m_pNetwork->m_CmdSend.CmdSetIdleStatus(device);
            }
        }

        free(json);
    }
}







#if SUPPORT_SERVER_SYNC
// 处理查找在线主机
void CCommandHandle::HandleSearchOnlineHost(const char*		pData,		// 数据区
                                            unsigned short    nLen,		// 数据长度
                                            const char*       szIP)		// IP地址	
{
    u_char	dataPos	= 0;
    //printf("HandleSearchOnlineHost...\n");
    //服务器mac地址
    char szMac[SEC_MAC_LEN]			= {0};
    SetMac(szMac, &pData[dataPos]);
    dataPos+=6;

    //如果mac与本机相等，不处理
    if( strcmp(szMac,CNetwork::GetHostMac()) == 0 )
    {
        return;
    }
    
    //服务器类型，备用
    dataPos++;

    //是否为备用服务器
    bool bIsBackupServer=pData[dataPos++];

    //服务器状态
    int serverStatus=pData[dataPos++];

    //服务器版本号长度
    int serverVerisonLen=pData[dataPos++];
    //服务器版本号
    char szServerVersion[32]={0};
    memcpy(szServerVersion,pData+dataPos,serverVerisonLen);
    dataPos+=serverVerisonLen;

    //更新服务器信息
    st_netServer m_netServer;
    m_netServer.m_bIsBackupServer = bIsBackupServer;
    m_netServer.m_nServerStatus = serverStatus;
    m_netServer.m_bServerOnline = true;
    m_netServer.m_nServerTimeoutCnt = 0;
    sprintf(m_netServer.m_szMac,"%s",szMac);
    sprintf(m_netServer.m_szVersion,"%s",szServerVersion);
    m_netServer.m_strServerIP=szIP;
    

    g_Global.m_serverSync.Update_NetServer_Info(m_netServer);


    #if 0   //不需要回应，两边服务器都会主动发送
    m_pNetwork->m_CmdSend.CmdResponseOnlineDevice(szIP);
    #endif
}



// 处理连接备用服务器
void CCommandHandle::HandleConnectStandByServer(const char*		pData,		// 数据区
                                            unsigned short    nLen,		// 数据长度
                                            const char*       szIP)		// IP地址	
{
    unsigned short	dataPos	= 0;
     //Event：1：主服务器请求连接备用服务器 2：备用服务器应答OK（已连接）  3：备用服务器应答失败（拒绝）
    int event = pData[dataPos++]; 

    //服务器mac地址
    char szMac[SEC_MAC_LEN]			= {0};
    SetMac(szMac, &pData[dataPos]);
    dataPos+=6;

    #if IS_BACKUP_SERVER
    //如果当前是备用服务器,代表收到时是主服务器请求连接
    
    char szDestMac[SEC_MAC_LEN]			= {0};
    SetMac(szDestMac, &pData[dataPos]);
    dataPos+=6;
    printf("HandleConnectStandByServer:szDestMac=%s,CNetwork::GetHostMac()=%s\n",szDestMac,CNetwork::GetHostMac());
    //判断目的mac是不是自身
    if(strcasecmp(CNetwork::GetHostMac(),szDestMac) == 0)
    {
        //获取备用服务器的用户名
        static string userName=g_Global.m_serverSync.getSystemFirstNormalUser();
        //将公钥加入到SSH认证列表内
        int sshPublicKeyLen=(pData[dataPos]<<8)+pData[dataPos+1];
        dataPos+=2;
        char publicKey[1024]={0};
        memcpy(publicKey,pData+dataPos,sshPublicKeyLen);
        dataPos+=sshPublicKeyLen;
        //printf("sshPublicKeyLen=%d\n,sshPublicKey=%s\n",sshPublicKeyLen,publicKey);
        printf("SSH PublicKey ensure ok!\n");
        //将主服务器的公钥加入信任列表
        if(CServerSync::checkSSHKeyAndWriteNew(userName,publicKey))
        {
            //保存主服务器IP
            g_Global.m_serverSync.m_strMasterServerHostIp = szIP;

            g_Global.m_Network.m_CmdSend.CmdStandByServerResponseConnect(2,szIP,szMac,userName.data(),1);
            //此处不直接设置为已连接，等待服务器验证可以免密码登录后再根据主服务器的广播信息得到状态
            //g_Global.m_serverSync.m_nServerStatus = STANDBY_SERVER_CONNECTED_MASTER_SERVER;
        }
        else
        {
            g_Global.m_serverSync.m_nServerStatus = STANDBY_REFUSED_TO_BE_CONNECTED;
        }
    }
    else
    {
        g_Global.m_Network.m_CmdSend.CmdStandByServerResponseConnect(3,szIP,szMac,NULL,1);
        g_Global.m_serverSync.m_nServerStatus = STANDBY_REFUSED_TO_BE_CONNECTED;
    }
    #else
    //如果是主服务器，代表收到的是应答
    printf("HandleConnectStandByServer:event=%d\n",event);
    if(event == 2)
    {
        char szDestMac[SEC_MAC_LEN]			= {0};
        SetMac(szDestMac, &pData[dataPos]);
        dataPos+=6;
        printf("HandleConnectStandByServer:szDestMac=%s,CNetwork::GetHostMac()=%s\n",szDestMac,CNetwork::GetHostMac());
        //判断目的mac是不是自身
        if(strcasecmp(CNetwork::GetHostMac(),szDestMac) == 0)
        {
            //提取用户名
            int userNameLen=pData[dataPos++];
            char uerName[128]={0};
            memcpy(uerName,pData+dataPos,userNameLen);
            dataPos+=userNameLen;

            if(strcmp(uerName,g_Global.m_serverSync.m_strBackupServerUser.data()))
            {
                g_Global.m_serverSync.m_strBackupServerUser = uerName;
                g_Global.m_serverSync.SaveIniConfig();  //保存
            }
            printf("userName=%s\n",uerName);

            //todo 此处还需要用GetSSHLoginWithoutPasswordResult检测是否可以免密码登录
            //进行连接尝试
            int ssh_login_result=CServerSync::GetSSHLoginWithoutPasswordResult(g_Global.m_serverSync.m_strBackupServerHostIp.c_str(),g_Global.m_serverSync.m_strBackupServerUser.c_str());
            if(ssh_login_result == SSH_ACCESS_AUTH_SUCCEED)    //授权成功
            {
                g_Global.m_serverSync.m_nServerStatus = MASTER_SERVER_CONNECTED_STANDBY_SERVER;
            }
            else
            {
                g_Global.m_serverSync.m_nServerStatus = MASTER_SERVER_CONNECT_STANDBY_SERVER_FAILED_BY_AUTH;
            }
        }
        else
        {
            g_Global.m_serverSync.m_nServerStatus = MASTER_SERVER_CONNECT_STANDBY_SERVER_FAILED_BY_AUTH;
        }
    }
    else
    {
        g_Global.m_serverSync.m_nServerStatus = MASTER_SERVER_CONNECT_STANDBY_SERVER_FAILED_BY_AUTH;
    }
    #endif


}
#endif