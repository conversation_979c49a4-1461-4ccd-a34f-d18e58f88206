#include "CommandSend.h"

#include "stdafx.h"
#include "Network/Network.h"
#include "Model/Device/Section.h"
#include "Model/Other/SelectedSections.h"
#include "Tools/tools.h"
#include "Tools/Language.h"
#include "Network/Web/WebProtocol.h"

void CCommandSend::SetMac(char* pData, const char* szMac)
{
    sscanf(szMac, "%02x:%02x:%02x:%02x:%02x:%02x",
           &pData[0],  &pData[1],  &pData[2],
           &pData[3],  &pData[4],  &pData[5]);
}

void    Print(char* szBuf, int len)
{
    for(int i=0; i<len; i++)
    {
        //LOG(FORMAT("%d : %d | %c\n", i, szBuf[i], szBuf[i]), LV_INFO);
        printf("%d ", szBuf[i]);
    }
}

CCommandSend::CCommandSend()
{
    m_pNetwork = NULL;
}

CCommandSend::~CCommandSend()
{

}

void CCommandSend::SetNetwork(CNetwork* pNetwork)
{
    m_pNetwork = pNetwork;
}

void CCommandSend::StartCommand(CUDPSocket* pSocket)
{
    m_commandQueue.StartWorking(pSocket);
}

// 添加到命令到队列
bool CCommandSend::AddCommand(char* szBuf, int nLen, CSection& device)
{
    if (NETWORK_IS_TCP || device.IsTcpMode())
    {
        return FALSE;
    }

    return m_commandQueue.AddCommand(szBuf, nLen, device.GetIP());
}


// 移除队列中的命令
bool CCommandSend::RemoveCommand(unsigned short uCmd, const char* szIP)
{
    if (NETWORK_IS_TCP)
    {
        return FALSE;
    }

    return m_commandQueue.RemoveCommand(uCmd, szIP);
}


// 常用命令
/***************************************************/

// 控制所有分区
void CCommandSend::CmdControlAllSection(char*	szBuf,				// 命令缓冲区
                                        int		len)				// 数据长度

{
    unsigned int uCount = g_Global.m_Sections.GetSecCount();

    for (unsigned int i=0; i<uCount; ++i)
    {
        CSection& section = g_Global.m_Sections.GetSection(i);

        // 只有在线的分区才发送命令
        if (section.IsOnline())
        {
            m_pNetwork->SendData(szBuf, len, section, section.GetIP(), UDP_PORT, 1);

            // 添加到命令队列中
            AddCommand(szBuf, len, section);
        }
    }
}


// 控制选中分区
bool CCommandSend::CmdControlCheckedSection(char*	szBuf,		    // 命令缓冲区
                                            int		len,		    // 数据长度
                                            CMyString strCmdText,	// 操作描述
                                            unsigned int*   pCheckedIndexs, // 被选中的分区
                                            unsigned int	uCheckedCount,	// 被选中的分区数量
                                            bool    bShowMsg)		// 显示提示信息
{
    unsigned short	uCommand = CharsToShort(&szBuf[0]);

    if (uCheckedCount == 0 && bShowMsg)
    {
        return FALSE;
    }

    for (unsigned int i=0; i<uCheckedCount; ++i)
    {
        CSection& section = g_Global.m_Sections.GetSection(pCheckedIndexs[i]);

        // 只有在线的分区才发送命令
        if (section.IsOnline())
        {
            bool bSendData = TRUE;

            // 分区处于告警时，不能调节音量
            if (section.GetProSource() == PRO_ALARM && (uCommand == CMD_DEVICE_VOLUME))
            {
                bSendData = FALSE;
            }

            if (bSendData)
            {
                m_pNetwork->SendData(szBuf, len, section, section.GetIP(), UDP_PORT, 1);

                // 添加到命令队列中
                AddCommand(szBuf, len, section);
            }
        }
    }

    return (uCheckedCount > 0);
}


// 转发分控设备的命令
bool  CCommandSend::CmdForwardControlDevice( const char*	pData,			// 命令缓冲区
                                             int			nLen,			// 数据长度
                                             CSection&      ctrlDevice)		// 分控设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    memcpy(szBuf, pData, nLen);

    // 如果不是需要主机转发，则直接返回
    if (szBuf[5] != PACK_ATTR_CTRL_TO_HOST)
    {
        return FALSE;
    }

    if (ctrlDevice.GetDeviceModel() == MODEL_PAGER_A || ctrlDevice.GetDeviceModel() == MODEL_PAGER_B || ctrlDevice.GetDeviceModel() == MODEL_PAGER_C)
    {
        szBuf[5] = PACK_ATTR_HOST_FOWARD_PAGER;
    }
    else if (ctrlDevice.GetDeviceModel() == MODEL_MOBILE)
    {
        szBuf[5] = PACK_ATTR_HOST_FOWARD_MOBILE;
    }
    else
    {
        return FALSE;
    }

    // 判断选中分区ID
    unsigned char selectedSecID = pData[3];	// 选中分区的标识
    if (!ctrlDevice.m_pSelectedSections->CanHandleCmd(selectedSecID))
    {
        return FALSE;
    }

    szBuf[3] = 0;			// 保留字为0
    szBuf[4] = MODEL_HOST;	// 设备型号修改为主机

    unsigned int uSecCount = ctrlDevice.m_pSelectedSections->GetCount();

    LOG(FORMAT("uSecCount = %d",uSecCount), LV_INFO);

    // 如果是通知分区寻呼，那把保留位改成组播或者单播标志位
    USHORT	command	= CharsToShort(&pData[0]);		// 命令
    if (command == CMD_NOTIFY_PAGING)
    {
        szBuf[3] = g_Global.m_Audiocast;
    }

    if(command == CMD_NOTIFY_PAGING)            //寻呼通知指令
    {
        if(szBuf[8] == 0x08)                    //开始寻呼，只通知TCP模式下的，因为寻呼台已经用组播通知寻呼开始
        {
            for (unsigned int i=0; i<uSecCount; ++i)
            {
                CSection* pSection = g_Global.m_Sections.GetSectionByMac(ctrlDevice.m_pSelectedSections->GetAt(i));

                if (pSection != NULL && pSection->IsOnline() && pSection->IsTcpMode())
                {
                    m_pNetwork->SendData(szBuf, nLen, *pSection, pSection->GetIP(), UDP_PORT, 1);
                }
            }
        }
        else                            //(szBuf[8] == 0x80)        //停止寻呼
        {
            //停止的是之前发起寻呼所选中的分区，只停止TCP模式下的，因为寻呼台已经用组播通知寻呼停止
            uSecCount = ctrlDevice.m_pAudioForward->GetCount();
            for (UINT i=0; i<uSecCount; ++i)
            {
                CSection* pSection = g_Global.m_Sections.GetSectionByMac(ctrlDevice.m_pAudioForward->GetAt(i));

                if (pSection != NULL && pSection->IsOnline() && pSection->IsTcpMode())
                {
                    m_pNetwork->SendData(szBuf, nLen, *pSection, pSection->GetIP(), UDP_PORT, 1);
                }
            }
        }
        
    }
    else        //不是寻呼通知命令，照常所有转发
    {
        for (unsigned int i=0; i<uSecCount; ++i)
        {
            CSection* pSection = g_Global.m_Sections.GetSectionByMac(ctrlDevice.m_pSelectedSections->GetAt(i));

            if (pSection != NULL && pSection->IsOnline())
            {
                // 分区处于告警时，不能调节音量和停止
                if (pSection->GetProSource() == PRO_ALARM)
                {
                    if(command == CMD_IDLE_STATUS || command == CMD_DEVICE_VOLUME)
                        continue;
                }
                m_pNetwork->SendData(szBuf, nLen, *pSection, pSection->GetIP(), UDP_PORT, 1);
        
                // 添加到命令队列中
                AddCommand(szBuf, nLen, *pSection);

                if (command == CMD_IDLE_STATUS)
                {
                    //停止，重置最近音源
                    if(pSection->GetProSource() == PRO_LOCAL_PLAY || pSection->GetProSource() == PRO_TIMING || CProtocol::IsAudioCollectorSrc(pSection->GetProSource()))
                    {
                        pSection->m_PlayedRecently.m_nRecentSrc = PRO_IDLE;
                    }
                    pSection->m_PlayedRecently.m_bSongTimerEndResumeAc = PRO_IDLE;
                    
                    g_Global.m_Sections.ClearListenedSpecSection(pSection->GetMac());
                }
            }
        }
    }
    
    

    return TRUE;
}


// 转发寻呼设备的命令
bool  CCommandSend::CmdForwardPagingDevice(	unsigned short		command,// 命令
                                            const char*		pData,		// 命令缓冲区
                                            int			nLen,			// 数据长度
                                            CSection&	pagingDevice)	// 寻呼设备
{
    CHAR szBuf[MAX_BUF_LEN] = {0};
    memcpy(szBuf, pData, nLen);

    // 如果不是需要主机转发，则直接返回
    if (szBuf[5] != PACK_ATTR_CTRL_TO_HOST)
    {
        return FALSE;
    }

    if (pagingDevice.GetDeviceModel() == MODEL_PAGER_A || pagingDevice.GetDeviceModel() == MODEL_PAGER_B || pagingDevice.GetDeviceModel() == MODEL_PAGER_C)
    {
        szBuf[5] = PACK_ATTR_HOST_FOWARD_PAGER;
    }
    else if (pagingDevice.GetDeviceModel() == MODEL_MOBILE)
    {
        szBuf[5] = PACK_ATTR_HOST_FOWARD_MOBILE;
    }
    else
    {
        return FALSE;
    }

    //szBuf[3] = 0;			// 保留字为0
    szBuf[4] = MODEL_HOST;	// 设备型号修改为主机

    UINT uSecCount = pagingDevice.m_pAudioForward->GetCount();
    
    for (UINT i=0; i<uSecCount; ++i)
    {
        CSection* pSection = g_Global.m_Sections.GetSectionByMac(pagingDevice.m_pAudioForward->GetAt(i));

        if (command == CMD_PAGING_STREAM)
        {
            if (pSection != NULL && pSection->IsOnline() && pSection->IsTcpMode())
            {
                if(pSection->m_kcpsocket && pSection->m_kcpsocket->GetDesPort() && pSection->m_kcpsocket->GetSendValid() && !STREAM_ALLWAYS_USED_TCP)
                {
                    pSection->m_kcpsocket->SendData(1,szBuf,nLen);
                }
                else
                {
                    char* cbuf = new char[nLen];//足够长
                    memcpy(cbuf,szBuf,nLen);
                    g_Global.m_Network.SendTcpData(cbuf, nLen, pSection->m_pSocketObj);
                    delete[] cbuf;
                }
            }
        }
        else if(command == CMD_PAGING_AGAIN)
        {
            if (pSection != NULL && pSection->IsOnline())
            {
                m_pNetwork->SendData(szBuf, nLen, *pSection, pSection->GetIP(), UDP_PORT, 1);
            }
        }
    }
    return TRUE;
}

#if SUPPORT_SERVER_SYNC
// 广播主机信息
void CCommandSend::CmdBoradcasHostInfo(int nRepeatCount)		// 重复发送次数
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::BroadcastHostInfo(szBuf);
    m_pNetwork->SendUdpData(szBuf, len, MULTICAST_RECV_IP, MULTICAST_RECV_PORT, nRepeatCount);
}

// 主服务器连接备用服务器
void CCommandSend::CmdConnectStandByServer(const char* szIP,         // 服务器IP
                                    const char* szMac,              // 服务器MAC
                                    const char* szSSHPublicKey,     // 公钥（主服务器主动连接才需要）
                                    int nRepeatCount)		        // 重复发送次数		
{
    //printf("CmdConnectStandByServer:szSSHPublicKey_len=%d,str=%s\n",strlen(szSSHPublicKey),szSSHPublicKey);
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::ConnectStandByServer(szBuf,1,szMac,szSSHPublicKey,"");
    m_pNetwork->SendUdpData(szBuf, len, szIP, UDP_PORT, 1);
}

// 备用服务器应答主服务器连接
void CCommandSend::CmdStandByServerResponseConnect(unsigned char event,                // 应答事件
                                    const char* szIP,         // 服务器IP
                                    const char* szMac,               // 服务器MAC
                                    const char* userName,            // 用户名 
                                    int nRepeatCount)		         // 重复发送次数		
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::ConnectStandByServer(szBuf,event,szMac,NULL,userName);
    
    m_pNetwork->SendUdpData(szBuf, len, szIP, UDP_PORT, 1);
}
#endif

#if 0
// 回应其它主机搜索设备（把本身当成设备回应给其它主机）
void CCommandSend::CmdResponseOnlineDevice(const char*	szIP)
{
    char szBuf[MAX_BUF_LEN] = {0};

    int	len	= CProtocol::ControlCommand(szBuf,
                                        CMD_NOTIFY_ONLINE_DEVICE,
                                        NULL,
                                        0);

    m_pNetwork->SendUdpData(szBuf, len, szIP, UDP_PORT, 1);
}
#endif


// 查找在线设备
void CCommandSend::CmdSearchOnlineDevice(int nRepeatCount)	// 重复发送次数
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::SearchOnlineDevice(szBuf);

    if (NETWORK_IS_TCP)
    {
        // 不用发送主动搜索（让设备来主动发）
        m_pNetwork->DevicesAllSendTcpData(szBuf, len);
    }
    else
    {
        m_pNetwork->SendUdpData(szBuf, len, MULTICAST_SEND_IP, MULTICAST_SEND_PORT, nRepeatCount);
    }
}


// 全部设备时间同步
void CCommandSend::CmdSyncronizeTime()
{
    CTime	t = CTime::GetCurrentTimeT();

    char	szDate[11]  = {0};
    char	szTime[9]	= {0};

    sprintf(szDate, "%04d-%02d-%02d", t.GetYear(), t.GetMonth(), t.GetDay());
    sprintf(szTime, "%02d:%02d:%02d", t.GetHour(), t.GetMinute(), t.GetSecond());

    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::SyncronizeTime(szBuf, szDate, szTime);

    if (NETWORK_IS_TCP)
    {
        m_pNetwork->DevicesAllSendTcpData(szBuf, len);
    }
    else
    {
        m_pNetwork->SendUdpData(szBuf, len, MULTICAST_SEND_IP, MULTICAST_SEND_PORT, 1);
    }
}

// 时间同步
void CCommandSend::CmdSyncronizeTime(CSection& device)
{
    CTime	t = CTime::GetCurrentTimeT();

    char	szDate[11]  = {0};
    char	szTime[9]	= {0};

    sprintf(szDate, "%04d-%02d-%02d", t.GetYear(), t.GetMonth(), t.GetDay());
    sprintf(szTime, "%02d:%02d:%02d", t.GetHour(), t.GetMinute(), t.GetSecond());

    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::SyncronizeTime(szBuf, szDate, szTime);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}

// 设置设备名称
void CCommandSend::CmdSetName(	CMyString strName,					// 别名
                                CSection& device)					// 设备
{
    char	szBuf[MAX_BUF_LEN] = {0};
    int		len		 = CProtocol::SetName(szBuf, strName.Data());

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);

    CMyString strTip;
    strTip.Format((char*)("%s Rename : %s"), device.GetIP(), StringToUTF8(strName.C_Str()).data());
    m_pNetwork->AddLog(strTip);
}


// 设置音量
void CCommandSend::CmdSetVolume(char     volume,		    // 音量值
                                unsigned int*    pCheckedIndexs,    // 被选中的分区
                                unsigned int	 uCheckedCount)	    // 被选中的分区数量
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SetVolume(szBuf, volume);
    CMyString strCmdText = LANG_STR(LANG_SECTION_ZONE_GROUP, "Set Volume", ("设置音量"));
    CmdControlCheckedSection(szBuf, len, strCmdText, pCheckedIndexs,uCheckedCount);

    CMyString strTip;
    strTip.Format((char*)("%s : %d"), strCmdText.C_Str(), volume);
    m_pNetwork->AddLog(strTip);
}

// 设置音量（单个分区设备）
void CCommandSend::CmdSetVolume(char     volume,		    // 音量值
                                CSection& device)	        // 单个分区设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SetVolume(szBuf, volume);
    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}

// 设置子音量
void CCommandSend::CmdSetSubVolume(bool	bSet,                       // 是否为设置
                                    char     subVolume,                    // 子音量
                                    char     auxVolume,                    // 本地音量
                                    CSection& device)					// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SetSubVolume(szBuf, subVolume, auxVolume, bSet);
    
    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}

// 设置设备音量加/减
void CCommandSend::CmdSetVolumeAddMin(bool isUp,                          //是否为增加
                                    unsigned char step,                 // 步进
                                    CSection& device)					// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SetVolumeAddMin(szBuf, isUp,step);
    
    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}

// 回应分控设备设置音量
void CCommandSend::CmdResponseSetVolume(CSection& device)	// 分控设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::ResponseSetVolume(szBuf);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}

// 获取终端状态
void CCommandSend::CmdGetStatus()
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::GetStatus(szBuf);

    if (NETWORK_IS_TCP)
    {
        m_pNetwork->DevicesAllSendTcpData(szBuf, len);
    }
    else
    {
        m_pNetwork->SendUdpData(szBuf, len, MULTICAST_SEND_IP, MULTICAST_SEND_PORT, 1);
    }
}


// 固件升级
void CCommandSend::CmdUpgradeFirmware(	char		model,				// 设备型号
                                        CMyString		strVersion,			// 版本号
                                        CMyString		strAbsolutePath,	// 绝对路径
                                        CMyString		strRelativePath,	// 相对路径
                                        const char*		lpServerIP,			// 服务器IP
                                        unsigned short		uPort,				// 服务器端口
                                        CSection&	device,                 // 设备
                                        string md5)				
{

    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::UpgradeFirmware(szBuf, model, strVersion.Data(), lpServerIP, uPort, strRelativePath.Data(),md5.c_str());

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);

    CMyString strTip;
    strTip.Format((char*)("%s Upgrade Firmware : %s"), device.GetIP(), strAbsolutePath.C_Str());
    m_pNetwork->AddLog(strTip);
}

// HTTP播放歌曲
void CCommandSend::CmdWebcasting(CMyString	strSongPathName,		// 歌曲路径名
                                 unsigned int*      pCheckedIndexs,         // 被选中的分区
                                 unsigned int	    uCheckedCount)	        // 被选中的分区数量
{
    char	szServerIP[MAX_IP_LEN] = {0};
    int	    nPort;

    // 来自HTTP服务器上的歌曲
    if (g_Global.m_nSongsFrom == SONGS_FROM_NETWORK)
    {
        strcpy(szServerIP, g_Global.m_szSongServerIP);
        nPort = g_Global.m_nSongServerPort;
    }
    // 来自本地的歌曲
    else
    {
        strcpy(szServerIP, g_Global.m_szNetworkIP);
        nPort = g_Global.m_HTTP_PORT;
    }

    CMyString strURL = GetHttpURLByPath(strSongPathName, szServerIP, nPort);

    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::Webcasting(szBuf, strURL.Data());

    CMyString strCmdText;
    strCmdText.Format(("HTTP %s"),
                      LANG_STR(LANG_SECTION_CONTROL_PANE, "Play", ("播放")).C_Str());

    CmdControlCheckedSection(szBuf, len, strCmdText, pCheckedIndexs, uCheckedCount);

    CMyString strTip;
    strTip.Format((char*)("%s : %s"), strCmdText.C_Str(), GetNameByPathName(strSongPathName).C_Str());
    m_pNetwork->AddLog(strTip);
}

// 设置单个设备的播放状态
void CCommandSend::CmdSetSingleDevicePlayStatus(PlayStatus status,   // 播放状态
                                                CSection& device)    // 分区
{
    CHAR szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SetPlayStatus(szBuf, status);

    //CMyString strCmdText = LANG_STR(LANG_SECTION_ZONE_GROUP, "Set Play Status", ("设置播放状态"));

    // 只有在线的分区才发送命令
    //20211228 如果任务之前是暂停状态的话，上线后需要下发暂停（如果任务之前是暂停状态的话），HandleOnlineResumePlaying函数调用的时候分区还不是上线状态，所以先取消在线才发送的限制。
    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
    if (device.IsOnline())
    {
        // 添加到命令队列中
        AddCommand(szBuf, len, device);
    }

    /*
    CMyString strTip;
    strTip.Format(("%s %s : %s"), device.GetUTFName().data(), strCmdText.C_Str(), CProtocol::GetDescriptionPlayStatus(status).C_Str());
    m_pNetwork->AddLog(strTip);
    */
}

// 设置播放状态
void	CCommandSend::CmdSetPlayStatus(	PlayStatus      status,			        // 播放状态
                                        unsigned int*   pCheckedIndexs,         // 被选中的分区
                                        unsigned int	uCheckedCount)	        // 被选中的分区数量
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SetPlayStatus(szBuf, status);

    CMyString strCmdText = LANG_STR(LANG_SECTION_ZONE_GROUP, "Set Play Status", ("设置播放状态"));
    CmdControlCheckedSection(szBuf, len, strCmdText, pCheckedIndexs, uCheckedCount);

    CMyString strTip;
    strTip.Format(("%s : %s"), strCmdText.C_Str(), CProtocol::GetDescriptionPlayStatus(status).C_Str());
    m_pNetwork->AddLog(strTip);
}

// 设置播放状态
void CCommandSend::CmdResponseSetPlayStatus(CSection& device)             // 分控设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::ResponseSetPlayStatus(szBuf);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}

// 设置静音状态
bool	CCommandSend::CmdSetMuteStatus(	MuteStatus      status,			        // 静音状态
                                        unsigned int*   pCheckedIndexs,         // 被选中的分区
                                        unsigned int	uCheckedCount)	        // 被选中的分区数量
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SetMuteStatus(szBuf, status);

    CMyString strCmdText = LANG_STR(LANG_SECTION_ZONE_GROUP, "Set Mute Status", ("设置静音状态"));
    bool bSuccess = CmdControlCheckedSection(szBuf, len, strCmdText, pCheckedIndexs, uCheckedCount);

    CMyString strTip;
    strTip.Format(("%s : %d"), strCmdText.C_Str(), status);
    m_pNetwork->AddLog(strTip);

    return bSuccess;
}

// 回应分控设备设置静音状态
void	CCommandSend::CmdResponseSetMuteStatus(CSection& device)		// 分控设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::ResponseSetMuteStatus(szBuf);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}

// 设置控制模式
void	CCommandSend::CmdSetControlMode(ControlMode mode,			        // 控制模式
                                        unsigned int*       pCheckedIndexs,         // 被选中的分区
                                        unsigned int	    uCheckedCount)	        // 被选中的分区数量
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SetControlMode(szBuf, mode);

    CMyString strCmdText = LANG_STR(LANG_SECTION_ZONE_GROUP, "Set Program Control", ("设置程控"));
    CmdControlCheckedSection(szBuf, len, strCmdText, pCheckedIndexs, uCheckedCount);

    CMyString strTip;
    strTip.Format(("%s : %d"), strCmdText.C_Str(), mode);
    m_pNetwork->AddLog(strTip);
}

// 回应分控设备设置程控模式
void	CCommandSend::CmdResponseSetControlMode(CSection& device)		// 分控设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::ResponseSetControlMode(szBuf);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}


// 查询文件信息
void CCommandSend::CmdGetFileInfo(	FileType	ft,					// 文件类型
                                    CSection&	device)				// 设备
{
    unsigned int count = device.GetFileInfoCount((DATETIME_FILE)(ft - 1));

    // 最多获取GET_FILE_INFO_COUNT次，如果没响应就不会继续获取了，这样对低版本设备进行获取文件信息，如果不存在该文件就会一直不返回的问题
    if (count < GET_FILE_INFO_COUNT)
    {
        char szBuf[MAX_BUF_LEN] = {0};
        int len = CProtocol::GetFileInfo(szBuf, ft);

        m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

        // 添加到命令队列中(有定时器检测，不需要加入到队列)
        //AddCommand(szBuf, len, device);

        device.SetFileInfoCount((DATETIME_FILE)(ft - 1), ++count);
    }
    // 超过次数，加到日志（只添加一次）
    else if (count == GET_FILE_INFO_COUNT)
    {
        CMyString strTip;
        strTip.Format(("--------%s Get %s count：%d"), device.GetUTFName().data(), CProtocol::GetDescriptionFileType(ft).C_Str(), count);
        m_pNetwork->AddLog(strTip);

        device.SetFileInfoCount((DATETIME_FILE)(ft - 1), ++count);
    }

}


// 更新文件
void CCommandSend::CmdUpdateFile(	FileType	ft,					// 文件类型
                                    CSection& device)				// 设备
{
    CMyString strDateTime;
    CMyString strFileHttpPath;

    if (ft == FILE_PLAYLIST)
    {
        #if APP_IS_LZY_LIMIT_STORAGE
        string loginAccount=device.GetUserAccount();
        if(loginAccount.length() > 0)
        {
            //判断登录账户是否存在
            LPCUserInfo  pUser = g_Global.m_Users.GetUserByAccount(loginAccount);
            if(pUser)
            {
                if(pUser->IsSuperUser())
                {
                    strDateTime = g_Global.m_PlayList.GetDateTime();
                }
                else
                {
                    strDateTime = pUser->GetPlaylistDateTime();
                }
            }
        }
        #else
        strDateTime = g_Global.m_PlayList.GetDateTime();
        #endif
    }
    else if (ft == FILE_TIMER)
    {
        strDateTime = g_Global.m_TimerScheme.GetDateTime();
    }
    else if (ft == FILE_GROUP)
    {
        strDateTime = g_Global.m_Groups.GetDateTime();
    }
    else if (ft == FILE_SECTION)
    {
        #if SUPPORT_USER_SECTION_XML
        string loginAccount=device.GetUserAccount();
        if(loginAccount.length() > 0)
        {
            //判断登录账户是否存在
            LPCUserInfo  pUser = g_Global.m_Users.GetUserByAccount(loginAccount);
            if(pUser)
            {
                if(pUser->IsSuperUser())
                {
                    strDateTime = g_Global.m_Sections.GetDateTime();
                }
                else
                {
                    strDateTime = pUser->GetSectionDateTime();
                }
            }
        }
        #else
        strDateTime = g_Global.m_Sections.GetDateTime();
        #endif
    }
    else if (ft == FILE_AUDIO_COLLECTOR)
    {
        strDateTime = g_Global.m_AudioCollectors.GetDateTime();
    }
    else if (ft == FILE_FIRE_COLLECTOR)
    {
        strDateTime = g_Global.m_FireCollectors.GetDateTime();
    }
    else if (ft == FILE_USER)
    {
        strDateTime = g_Global.m_Users.GetDateTime();
    }
    else if (ft == FILE_SEQUENCE_POWER)
    {
        strDateTime = g_Global.m_SequencePower.GetDateTime();
    }
    else if (ft == FILE_PAGER)
    {
        strDateTime = g_Global.m_Pagers.GetDateTime();
    }
    else
    {
        return;
    }

    strFileHttpPath = g_Global.m_Sections.GetDateTimeFileHttpPath(device.GetUserAccount(),ft);

    // 分区设备可能有自己的播放列表，路径和时间不一样
    if (ft == FILE_PLAYLIST && device.IsSectionDevice())
    {
        strDateTime		= device.GetPlaylist()->GetDateTime();
        strFileHttpPath = device.GetPlaylist()->GetFileHttpPath();
    }

    //LOG(lpszHttpPath, LV_INFO);

    int  len = 0;
    char szBuf[MAX_BUF_LEN] = {0};

    // 分区设备，而且设置了歌曲来自于网络
    if (ft == FILE_PLAYLIST
        && device.IsSectionDevice()
        && g_Global.m_nSongsFrom == SONGS_FROM_NETWORK)
    {
        len = CProtocol::UpdateFile(szBuf, ft, strDateTime.Data(), g_Global.m_szNetworkIP, g_Global.m_HTTP_PORT, strFileHttpPath.Data(),
                                    g_Global.m_szSongServerIP, g_Global.m_nSongServerPort);
    }
    else
    {
        len = CProtocol::UpdateFile(szBuf, ft, strDateTime.Data(), g_Global.m_szNetworkIP, g_Global.m_HTTP_PORT, strFileHttpPath.Data());
        //Print(szBuf, len);
    }

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);


    // 添加到命令队列中(不添加到队列中，解析文件需要一定的时间（大于重发时间），导致重发现象)
    //AddCommand(szBuf, len, device);
}

#if 0
// 请求获取文件信息
void CCommandSend::CmdRequestGetFileInfo(	FileType	ft,				// 文件类型
                                            CSection&	device)		// 设备
{
    CMyString strDateTime;
    CMyString strFileHttpPath;

    if (ft == FILE_PLAYLIST)
    {
        strDateTime = g_Global.m_PlayList.GetDateTime();
    }
    else if (ft == FILE_TIMER)
    {
        strDateTime = g_Global.m_TimerScheme.GetDateTime();
    }
    else if (ft == FILE_GROUP)
    {
        strDateTime = g_Global.m_Groups.GetDateTime();
    }
    else if (ft == FILE_SECTION)
    {
        strDateTime = g_Global.m_Sections.GetDateTime();
    }
    else if (ft == FILE_AUDIO_COLLECTOR)
    {
        strDateTime = g_Global.m_AudioCollectors.GetDateTime();
    }
    else if (ft == FILE_FIRE_COLLECTOR)
    {
        strDateTime = g_Global.m_FireCollectors.GetDateTime();
    }
    else if (ft == FILE_SEQUENCE_POWER)
    {
        strDateTime = g_Global.m_SequencePower.GetDateTime();
    }
    else if (ft == FILE_PAGER)
    {
        strDateTime = g_Global.m_Pagers.GetDateTime();
    }
    else
    {
        return;
    }

    strFileHttpPath = g_Global.m_Sections.GetDateTimeFileHttpPath(ft);

    // 分区设备可能有自己的播放列表，路径和时间不一样
    if (ft == FILE_PLAYLIST && device.IsSectionDevice())
    {
        strDateTime		= device.GetPlaylist()->GetDateTime();
        strFileHttpPath = device.GetPlaylist()->GetFileHttpPath();
    }

    char*	lpszDateTime	= CStringToChar(strDateTime);
    char*	lpszHttpPath	= CStringToChar(strFileHttpPath);

    int  len = 0;
    char szBuf[MAX_BUF_LEN] = {0};

    len = CProtocol::RequestGetFileInfo(szBuf, ft, lpszDateTime, g_Global.m_szNetworkIP, g_Global.m_HTTP_PORT, lpszHttpPath);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    delete[] lpszDateTime;
    delete[] lpszHttpPath;
}
#endif

// 中止同步歌曲文件
void CCommandSend::CmdStopSyncSongFile(CSection&	device)				// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::StopSyncSongFile(szBuf);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    AddCommand(szBuf, len, device);

    CMyString strTip;
    strTip.Format(("%s : Stop Sync Song File"), device.GetIP());
    m_pNetwork->AddLog(strTip);
}

// 设置钟声
void CCommandSend::CmdSetRing(	int		   nList,				    // 播放列表索引
                                int		   nSong,				    // 歌曲名称索引
                                bool	   bAllZones,               // 是否为全部分区
                                CPlayList* pPlaylistShow,
                                unsigned int*       pCheckedIndexs,         // 被选中的分区
                                unsigned int	    uCheckedCount)	        // 被选中的分区数量
{
    CMyString	strListID		= pPlaylistShow->GetListID(nList);
    CMyString	strSongName		= GetNameWithExtensionByHttpPathName(pPlaylistShow->GetListSongPathName(nList, nSong));

    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::SetRing(szBuf, strListID.Data(), strSongName.Data());

    CMyString strCmdText = LANG_STR(LANG_SECTION_PLAY_LIST, "Set Bell", ("设为钟声"));

    if (bAllZones)
    {
        CmdControlAllSection(szBuf, len);
    }
    else
    {
        strCmdText.Format(("%s(%s)"), strCmdText.C_Str(),
            LANG_STR(LANG_SECTION_DIALOG, "Selected Zones", ("选中分区")).C_Str());

        CmdControlCheckedSection(szBuf, len, strCmdText, pCheckedIndexs, uCheckedCount);
    }

    CMyString strTip;
    strTip.Format(("%s : %s"), strCmdText.C_Str(), strSongName.C_Str());
    m_pNetwork->AddLog(strTip);
}

// 播放钟声
void CCommandSend::CmdPlayRing(unsigned int*       pCheckedIndexs,          // 被选中的分区
                               unsigned int	       uCheckedCount)	        // 被选中的分区数量
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::PlayRing(szBuf);

    CMyString strCmdText = LANG_STR(LANG_SECTION_ZONE_GROUP, "Play The Bells", ("播放钟声"));
    CmdControlCheckedSection(szBuf, len, strCmdText, pCheckedIndexs, uCheckedCount);

    m_pNetwork->AddLog(strCmdText.C_Str());
}


void CCommandSend::CmdResponsePlayRing(CSection&	device,			// 设备
                                        char		result)			// 播放结果
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::ResponsePlayRing(szBuf, result);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}

// 播放本地歌曲
void CCommandSend::CmdPlayLocalSong(int	nList,					       // 播放列表索引
                                    int	nSong,					       // 歌曲名称索引
                                    CPlayList*  pPlaylistShow,
                                    unsigned int*       pCheckedIndexs,        // 被选中的分区
                                    unsigned int	    uCheckedCount)	       // 被选中的分区数量
{
    // 先设置播放模式
    CmdSetPlayMode(g_Global.m_PlayList.GetPlayMode(), pCheckedIndexs, uCheckedCount);

    // 再播放歌曲
    CMyString	strListID		= pPlaylistShow->GetListID(nList);
    CMyString	strSongName		= GetNameWithExtensionByHttpPathName(pPlaylistShow->GetListSongPathName(nList, nSong));

    char	szBuf[MAX_BUF_LEN]	= {0};
    int		len					= CProtocol::PlayLocalSong(szBuf, strListID.Data(), strSongName.Data());

    CMyString strCmdText = LANG_STR(LANG_SECTION_ZONE_GROUP, "Play Audio Source", ("播放音源"));
    CmdControlCheckedSection(szBuf, len, strCmdText, pCheckedIndexs, uCheckedCount);


    CMyString strTip;
    strTip.Format(("%s : %s"), strCmdText.C_Str(), strSongName.C_Str());
    m_pNetwork->AddLog(strTip);

}

void CCommandSend::CmdResponsePlayLocalSong(CSection&	device,		// 设备
                                            char		result)		// 播放结果
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::ResponsePlayLocalSong(szBuf, result);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}

// 设置播放模式（选中分区）
void CCommandSend::CmdSetPlayMode(	PlayMode    mode,				  // 播放状态
                                    unsigned int*       pCheckedIndexs,       // 被选中的分区
                                    unsigned int	    uCheckedCount)	      // 被选中的分区数量
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SetPlayMode(szBuf, mode);

    CMyString strCmdText = LANG_STR(LANG_SECTION_ZONE_GROUP, "Set Play Mode", ("设置播放模式"));
    CmdControlCheckedSection(szBuf, len, strCmdText, pCheckedIndexs, uCheckedCount, FALSE);

    CMyString strTip;
    strTip.Format(("%s : %s"), strCmdText.C_Str(), CProtocol::GetDescriptionPlayMode(mode).C_Str());
    m_pNetwork->AddLog(strTip);
}

// 设置播放模式（一般是设定分控设备的播放模式）
void CCommandSend::CmdSetPlayMode(	PlayMode mode,				    // 播放状态
                                                                        CSection&	device)				// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SetPlayMode(szBuf, mode);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);

    // 不需要加入日志信息
}

// 回应分控设备查询/设置播放模式
void CCommandSend::CmdResponsePlayMode(CSection&	device,			// 设备
                                        int			mode )			// 播放状态
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::ResponsePlayMode(szBuf, mode);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}


// 切换到空闲状态
void CCommandSend::CmdSetIdleStatus( unsigned int*       pCheckedIndexs,       // 被选中的分区
                                     unsigned int	    uCheckedCount)	       // 被选中的分区数量
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	 len = CProtocol::SetIdleStatus(szBuf);

    CMyString strCmdText = LANG_STR(LANG_SECTION_CONTROL_PANE, "Stop", ("停止"));
    CmdControlCheckedSection(szBuf, len, strCmdText, pCheckedIndexs, uCheckedCount);

    //m_pNetwork->AddLog(strCmdText);
    for (unsigned int i=0; i<uCheckedCount; ++i)
    {
        CSection& section = g_Global.m_Sections.GetSection(pCheckedIndexs[i]);
        g_Global.m_Sections.ClearListenedSpecSection(section.GetMac());
    }
}

void  CCommandSend::CmdSetIdleStatus(CSection&	device,
                                     unsigned char	reserve,		// 保留位
                                     int			nRepeatCount)			// 重复次数
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SetIdleStatus(szBuf);

    szBuf[3] = reserve;

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, nRepeatCount);

#if 0   //转发控制命令无需加入队列
    // 添加到命令队列中
    AddCommand(szBuf, len, device);
#endif
}

// 全部分区置为空闲状态
void CCommandSend::CmdSetIdleStatus()
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	 len = CProtocol::SetIdleStatus(szBuf);

    //CMyString strCmdText = LANG_STR(LANG_SECTION_CONTROL_PANE, "Stop", ("停止"));
    //CmdControlCheckedSection(szBuf, len, strCmdText, pCheckedIndexs, uCheckedCount);
    CmdControlAllSection(szBuf, len);
}

// 主机请求终端重新分配MAC地址
void CCommandSend::CmdReassignMac(CSection&	device)		// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::ReassignMac(szBuf);

    device, m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);

    CMyString strTip;
    strTip.Format(("%s : Reassign Mac"), device.GetIP());
    m_pNetwork->AddLog(strTip);
}

// 主机向设备请求重启
void CCommandSend::CmdReboot(	CSection&	device,						// 设备
                                unsigned short		uPort)			// UDP端口
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::Reboot(szBuf);

    device, m_pNetwork->SendData(szBuf, len, device, device.GetIP(), uPort, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);

    CMyString strTip;
    strTip.Format(("%s : Reboot"), device.GetIP());
    m_pNetwork->AddLog(strTip);
}

// 主机请求终端重置数据
void CCommandSend::CmdResetData(char type,					// 数据类型
                                CSection&	device)				// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::ResetData(szBuf, type);
    //Print(szBuf, len);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);

    CMyString strTip;
    strTip.Format(("%s(name:%s,sip:%s,mac:%s) : ResestData"), device.GetIP(), device.GetUTFName().data(),
                  device.m_SipInfo.m_szAccount, device.GetMac());
    m_pNetwork->AddLog(strTip);
}

// 查询FLASH信息
void CCommandSend::CmdGetFlashInfo(CSection&	device)
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::GetFlashInfo(szBuf);
    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}


// 获取设备日期时间
void CCommandSend::CmdGetDateTime(CSection&	device)				// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::GetDateTime(szBuf);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}

// 主机向控制设备发送终端文件信息
void CCommandSend::CmdSendFileInfo( CSection&	device,				// 设备
                                    const char*	szSecMac,				// 终端MAC地址
                                    char	fileType,				// 文件类型
                                    CMyString	strDateTime)			// 文件更新日期时间
{
    char	szTempMac[20] = {0};
    char	szMac[6] = {0};

    //  00:00:00:00:00:00
    strcpy(szTempMac, szSecMac);

    // 去掉冒号
    char *p = strtok(szTempMac, ":");
    int i = 0;
    while(p)
    {
        szMac[i++] = atoi(p);
        p = strtok(NULL, ":");
    }

    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::SendFileInfo(szBuf, szMac, fileType, strDateTime.Data());

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}

// 4.46 主机向终端设置警报声
void CCommandSend::CmdSetAlarmSound(unsigned char	channelID,				// 通道ID
                                    int		nList,					// 播放列表索引
                                    int		nSong,					// 歌曲名称索引
                                    CSection&	device)				// 设备
{
    CMyString	strListID		= g_Global.m_PlayList.GetListID(nList);
    CMyString	strSongName		= GetNameWithExtensionByHttpPathName(g_Global.m_PlayList.GetListSongPathName(nList, nSong));

    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::SetAlarmSound(szBuf, channelID, strListID.Data(), strSongName.Data());

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);

    CMyString strCmdText = LANG_STR(LANG_SECTION_DIALOG, "Alarm Sound", ("告警音效"));
    CMyString strTip;
    strTip.Format(("%s %s : %s"), device.GetIP(), strCmdText.C_Str(), strSongName.C_Str());
    m_pNetwork->AddLog(strTip);
}

// 4.47 主机向终端设置开启/关闭警报
void CCommandSend::CmdSetAlarmSwitch(	CSection&	device,			// 设备
                                        unsigned char		channelID,		// 通道ID
                                        unsigned char		switcher,		// 警报开关
                                        CMyString		strPathName)	// 报警音效路径名
{
    int nList, nSong;
    g_Global.m_PlayList.FindSongInPlayList(strPathName, nList, nSong);

    // 找到告警音效
    if (nList >= 0 && nSong >= 0)
    {
        CSongList&	songList	= g_Global.m_PlayList.GetSongList(nList);
        CSong&		song		= songList.GetSong(nSong);
        CMyString	strSongName	= GetNameWithExtensionByHttpPathName(song.GetPathName());
        CMyString   strID       = songList.GetID();

        char szBuf[MAX_BUF_LEN] = {0};
        int	len	= CProtocol::SetAlarmSwitch(szBuf, channelID, switcher, strID.Data(), strSongName.Data());
        m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
        AddCommand(szBuf, len, device);

        CMyString strCmdText = LANG_STR(LANG_SECTION_DIALOG, "Set Alarm Switch", ("设置告警开关"));
        CMyString strTip;
        strTip.Format(("%s : %s"), strCmdText.C_Str(), (switcher == TRIG_STATE_ON ? ("On") : ("Off")));
        m_pNetwork->AddLog(strTip);
    }
}

// 4.48主机向消防采集器查询通道触发状态
void CCommandSend::CmdGetAlarmState(CSection&	device)	// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::GetAlarmState(szBuf);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中(有定时器检测，不需要加入到队列)
    //AddCommand(szBuf, len, device);
}

// 4.49 主机向消防采集器查询/设置触发模式
void CCommandSend::CmdAlarmMode(CSection&	device,		// 设备
                                unsigned char	channels,		// 通道数，0xFF为查询
                                int		mode)			// 触发模式
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::AlarmMode(szBuf, channels, mode);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中(有定时器检测，不需要加入到队列)
    //AddCommand(szBuf, len, device);
}

// 4.50 主机向音频采集器/终端设置音频采集音源(选中分区)
void CCommandSend::CmdSetAudioInfo(std::shared_ptr<CAudioCollector> pAudioCollector,  // 采集器
                                    unsigned char channel,
                                   unsigned int*     pCheckedIndexs,       // 被选中的分区
                                   unsigned int	     uCheckedCount
                                   )	    
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SetAudioInfo(szBuf, pAudioCollector,channel);

    CMyString strCmdText = LANG_STR(LANG_SECTION_ZONE_GROUP, "Play Audio Source", ("播放音源"));
    CmdControlCheckedSection(szBuf, len, strCmdText, pCheckedIndexs, uCheckedCount);

    CMyString strTip;
    strTip.Format(("%s : %d"), strCmdText.C_Str(), pAudioCollector->GetSourceID());
    m_pNetwork->AddLog(strTip);
}

// 4.50 主机向音频采集器/终端设置音频采集音源(采集器)
void CCommandSend::CmdSetAudioInfo(CSection&	device,				  // 设备
                                  std::shared_ptr<CAudioCollector> pAudioCollector,  // 采集器
                                   unsigned char channel,
                                   unsigned char	reserve,          // 保留位,
                                    bool IsTiming,        //是否定时
                                    unsigned char timingVolume,   //音量
                                    bool IsTrigger,        //是否触发
                                    unsigned char triggerVolume)   //触发音量       
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SetAudioInfo(szBuf, pAudioCollector,channel,IsTiming,timingVolume,IsTrigger,triggerVolume);
    szBuf[3] = reserve;

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}


#if SUPPORT_AUDIO_MIXER
//主机向混音器/终端设置混音音源
void CCommandSend::CmdSetAudioMixerSourceInfo(CSection&	notifydevice,		  // 通知的设备
                                             CSection&	mixerDevice,		  // 混音器设备
                                             int event                       //事件类型：1为启动，0为停止，2为重发
                                            )
{
    char szBuf[MAX_BUF_LEN] = {0};
    unsigned char audioCodecs=ALGORITHM_PCM;      //音频编码
    if(notifydevice.IsTcpMode())
    {
        audioCodecs=ALGORITHM_722;
    }

    int	len	= CProtocol::SetAudioMixerSourceInfo(szBuf,mixerDevice.GetMac(),event,mixerDevice.m_pAudioMixer->GetPriority(),mixerDevice.m_pAudioMixer->GetVolume(),\
                                                AUDIO_MIXER_RATE_32K,AUDIO_MIXER_FMT,AUDIO_MIXER_MONO_CHANNEL,\
                                                (char*)AUDIO_MIXER_BROADCAST_ADDR,AUDIO_MIXER_BROADCAST_PORT,audioCodecs);

    m_pNetwork->SendData(szBuf, len, notifydevice, notifydevice.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, notifydevice);
}


// 主机查询/设置音频混音器参数
void  CCommandSend::CmdAudioMixerConfig(CSection&	device,			    // 设备
                                        bool	bSet                   // 是否为设置
                                        )
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::AudioMixerConfig(szBuf,bSet,device.m_pAudioMixer->GetMasterSwitch(),device.m_pAudioMixer->GetPriority(),device.m_pAudioMixer->GetTriggerType(),\
                                            device.m_pAudioMixer->GetTriggerSensitivity(),device.m_pAudioMixer->GetVolumeFadeLevel(),device.m_pAudioMixer->GetVolume()
                                        );
    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

#if 0
    // 添加到命令队列中
    AddCommand(szBuf, len, device);
#endif
}


#endif


#if SUPPORT_PHONE_GATEWAY
//主机向电话网关/终端设置电话网关音源
void CCommandSend::CmdSetPhoneGatewaySourceInfo(CSection&	notifydevice,		  // 通知的设备
                                             CSection&	phoneGatewayDevice,		  // 电话网关设备
                                             int event                       //事件类型：1为启动，0为停止，2为重发
                                            )
{
    char szBuf[MAX_BUF_LEN] = {0};
    unsigned char audioCodecs=ALGORITHM_PCM;      //音频编码
    if(notifydevice.IsTcpMode())
    {
        audioCodecs=ALGORITHM_722;
    }

    int	len	= CProtocol::SetPhoneGatewaySourceInfo(szBuf,phoneGatewayDevice.GetMac(),event,phoneGatewayDevice.m_pPhoneGateway->GetVolume(),\
                                                AUDIO_PHONE_GATEWAY_RATE_32K,PHONE_GATEWAY_FMT,AUDIO_PHONE_GATEWAY_MONO_CHANNEL,\
                                                (char*)PHONE_GATEWAY_BROADCAST_ADDR,PHONE_GATEWAY_BROADCAST_PORT,audioCodecs);

    m_pNetwork->SendData(szBuf, len, notifydevice, notifydevice.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, notifydevice);
}


// 主机查询/设置电话网关参数
void  CCommandSend::CmdPhoneGatewayConfig(CSection&	device,			   // 设备
                                        string telWhitelist,           //电话白名单
                                        bool	bSet                   // 是否为设置
                                        )
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::PhoneGatewayConfig(szBuf,bSet,device.m_pPhoneGateway->GetMasterSwitch(),device.m_pPhoneGateway->GetVolume(),telWhitelist);
    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

#if 0
    // 添加到命令队列中
    AddCommand(szBuf, len, device);
#endif
}


#endif


// 主机向终端设置网络电台音源
void CCommandSend::CmdSetNetRadioSource(CSection&	notifydevice,		   // 通知的设备
                                        const char *radioName,        //电台名称
                                        char *sessionId,                   //电台的sessionId
                                        int multicastPort,                 //组播端口
                                        bool IsTiming,        //是否定时
                                        unsigned char volume)   //音量	    
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SetNetRadioSourceInfo(szBuf,radioName,sessionId,NET_RADIO_SAMPLE_RATE,NET_RADIO_FMT,NET_RADIO_CHANNELS,(char *)NET_RADIO_BROADCAST_ADDR,multicastPort,IsTiming,volume);

    m_pNetwork->SendData(szBuf, len, notifydevice, notifydevice.GetIP(), UDP_PORT, 1);
    // 添加到命令队列中
    AddCommand(szBuf, len, notifydevice);
}


#if SUPPORT_AMP_CONTROLER
//功放控制器配置
void  CCommandSend::CmdAmpControlerConfig(CSection&	device,			   // 设备
                                        bool	bSet                   // 是否为设置
                                        )
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::AmpControlerConfig(szBuf,bSet);
    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}

#endif


#if SUPPORT_NOISE_DETECTOR
//噪声自适应器配置
void  CCommandSend::CmdNoiseDetectorConfig(CSection&	device,			   // 设备
                                        bool	bSet                   // 是否为设置
                                        )
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::NoiseDetectorConfig(szBuf,bSet,*device.m_pNoiseDetector);
    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}

#endif


// 主机查询/设置信息发布参数
void  CCommandSend::CmdInformationPublish(CSection&	device,			   // 设备
                                bool	bSet,                  // 是否为设置
                                bool bEnableDisplay,           // 是否启用显示
                                string strText,                // 输出文本
                                int nEffects,                  // 特效
                                int nMoveSpeed,                // 移动速度
                                int nStayTime                 // 停留时间 
                                )
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::InformationPublish(szBuf,bSet,bEnableDisplay,strText,nEffects,nMoveSpeed,nStayTime);
    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

#if 0
    // 添加到命令队列中
    AddCommand(szBuf, len, device);
#endif
}


// 主机查询/设置音频采集器参数
void    CCommandSend::CmdAudioCollectorConfig(CSection&	device,			    // 设备
                                                bool	bSet              // 是否为设置
                                              )
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::AudioCollectorConfig(szBuf,bSet,device.m_pAudioCollector->GetTriggerSwitch(),device.m_pAudioCollector->GetTriggerChannelId(),
                                                device.m_pAudioCollector->GetTriggerVolume());
    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}



// 回应分控向音频采集器/终端设置音频采集音源
void CCommandSend::CmdResponseSetAudioInfo(CSection&	device)		// 分控设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::ResponseSetAudioInfo(szBuf, 0x01);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}


// 4.48主机向电源时序器查询参数
void CCommandSend::CmdGetSequencePowerInfo(CSection&	device)	// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::SequencePowerInfo(szBuf,NULL,FALSE);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中(有定时器检测，不需要加入到队列)
    //AddCommand(szBuf, len, device);
}


// 4.48主机向电源时序器设置参数
void CCommandSend::CmdSetSequencePowerInfo(CSection&	device,std::shared_ptr<CSequencePower> pSequencePwrInfo)	// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::SequencePowerInfo(szBuf,pSequencePwrInfo,true);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中(有定时器检测，不需要加入到队列)
    //AddCommand(szBuf, len, device);
}


// 4.48主机向电源时序器发送定时信息
void CCommandSend::CmdSetSequencePowerTiming(CSection&	device,std::shared_ptr<CSequencePower> pSequencePwrInfo,bool IsQuickResponse)	// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::SequencePowerTimingInfo(szBuf,pSequencePwrInfo,device.GetMac(),IsQuickResponse);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中(有定时器检测，不需要加入到队列)
    //AddCommand(szBuf, len, device);
}

// 4.51 主机向终端设置网络模式
void CCommandSend::CmdSetNetworkMode(	CSection&	device,				// 设备
                                        bool	bSet,                   // 是否为设置
                                        unsigned char	mode,			// 网络模式
                                        const char*	szServerIP,			// 服务器IP
                                        unsigned short	nServerPort,	// 服务器端口
                                        const char*	szServerIP2,	    // 备用服务器IP
                                        unsigned short	nServerPort2)	// 备用服务器端口
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SetNetworkMode(szBuf, bSet,mode, szServerIP, nServerPort,szServerIP2,nServerPort2);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    //AddCommand(szBuf, len, device);

    if(bSet)
    {
        CMyString strTip;
        strTip.Format(("%s : network mode = %d, server ip = %s, port = %d"), device.GetIP(), mode, szServerIP, nServerPort);
        m_pNetwork->AddLog(strTip);
    }
}


// 4.52 主机向终端查询/设置IP属性
// 组播：防止设置错了IP，会查询不到；设置时mac会标识某一个终端，让终端去解析是不是自己设置
void CCommandSend::CmdNetworkInfo(	CSection&		device,				// 设备
                                    CNetworkInfo&	netInfo,			// IP属性
                                    bool			bSet)				// 是否为设置
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::NetworkInfo(szBuf, &netInfo, bSet);
    //Print(szBuf, len);

    //m_pNetwork->SendData(szBuf, len, device, MULTICAST_SEND_IP, MULTICAST_SEND_PORT, 1);

    // 设置，加入队列进行发送，防止批量设置IP，组播太多
    if (bSet)
    {
        //m_commandQueue.AddSendCmd(szBuf, len, m_pNetwork->m_pSocketMulticast, MULTICAST_SEND_IP, MULTICAST_SEND_PORT, 1);
        m_pNetwork->SendData(szBuf, len, device, MULTICAST_SEND_IP, MULTICAST_SEND_PORT, 1);
    }
    else
    {
        m_pNetwork->SendData(szBuf, len, device, MULTICAST_SEND_IP, MULTICAST_SEND_PORT, 1);
    }
}

// 4.53 主机向设备获取记录文件列表
void CCommandSend::CmdGetLogFileList(CSection&		device)			// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::GetLogFileList(szBuf);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}

// 4.54 主机向设备获取记录文件内容
void CCommandSend::CmdGetLogFileData(	CSection&	device,				// 设备
                                        const char*	szFileName,				// 文件名称
                                        unsigned short	packID)					// 包ID
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::GetLogFileData(szBuf, szFileName, packID);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}

// 4.55 主机向采集器主动发送终端音源选择状态
void CCommandSend::CmdSendAudioState(	CSection&	device,			// 设备
                                        bool	bUsed,              // 是否被选择
                                        unsigned char channelSelect)			
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SendAudioState(szBuf, bUsed,channelSelect);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中(有定时器检测，不需要加入到队列)
    //AddCommand(szBuf, len, device);
}

// 4.56 主机向其他控制设备下发音频采集器设备列表
void CCommandSend::CmdSendAudioList(CSection&		device)			// 设备
{
    unsigned char nAudioColCount = g_Global.m_AudioCollectors.GetSecCount();

    // 有采集器才会下发
    if (nAudioColCount <= 0)
    {
        return;
    }

    char szData[MAX_BUF_LEN] = {0};
    int  pos = 0;

    // 把所有的音频采集器信息发送到控制设备
    szData[pos++] = nAudioColCount;
    for (int i=0; i<nAudioColCount; ++i)
    {
        CSection& audioCollector = g_Global.m_AudioCollectors.GetSection(i);
        szData[pos++] = audioCollector.m_pAudioCollector->GetSourceID();
        unsigned char nameLen = strlen(audioCollector.GetName());
        szData[pos++] = nameLen;
        memcpy(&szData[pos], audioCollector.GetName(), nameLen);
        pos += nameLen;
        //原来寻呼台程序有误，多了一个pos，所以此处人为加上一个pos
        pos++;
    }

    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SendAudioList(szBuf, szData, pos);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}


// 4.58 主机向网络解码播放器查询/设置电源输出模式
void CCommandSend::CmdPowerOutputMode(	CSection&	device,				// 设备
                                        unsigned char	mode,					// 电源输出模式，为0时，是查询
                                        unsigned short	timteout)				// 超时时间
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::PowerOutputMode(szBuf, mode, timteout);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);

    if (mode != 0)
    {
        CMyString strTip;
        strTip.Format(("%s : mode = %d, timeout = %d"), device.GetIP(), mode, timteout);
        m_pNetwork->AddLog(strTip);
    }
}

// 4.59 主机向网络解码播放器查询回路检测状态
void CCommandSend::CmdSigalDetection(CSection&		device)			// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SigalDetection(szBuf);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}

// 4.61 主机向终端查询/设置EQ音效
void CCommandSend::CmdDeviceEq(CSection&	device,					// 设备
                                char		mode,					// EQ模式，为-1时，是查询
                                unsigned char gain[10])				// 低音值
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::DeviceEq(szBuf,device.GetMac(),mode, gain);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}


// 4.61 主机向终端查询/设置蓝牙信息
void CCommandSend::CmdBlueToothInfo(CSection&	device,					// 设备
                                const char*	btName,					        // 蓝牙名称,为NULL时为查询
                                unsigned char btencryption,             // 蓝牙加密方式
                                const char*   btPin)			                // 蓝牙密码
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::BlueToothInfo(szBuf,device.GetMac(),btName, btencryption,btPin);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}


// 主机向对讲终端查询/设置基础配置
void  CCommandSend::CmdIntercomBasicConfig(CSection&	device,					// 设备
                              CALLDEVICECONFIG *callBasicConfig)	//  对讲基础配置,为空代表查询
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::CallBasicConfig(szBuf,callBasicConfig);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}

// 主机向终端查询/设置触发参数
void  CCommandSend::CmdTriggerConfig(CSection&	device,					// 设备
                                    char trigger_switch,
                                    char trigger_mode,
                                    const char *trigger_songName,
                                    const char *trigger_songMd5,
                                    int playTimes,
                                    int volume)
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::TriggerConfig(szBuf,trigger_switch,trigger_mode,trigger_songName,trigger_songMd5,playTimes,volume);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}


// 寻呼台获取账户存储容量
void    CCommandSend::CmdAccountStorageCapacity(CSection&	device,	    // 设备
                                      const char*	szAccount,		    // 账号
                                      UINT64 storage_capacity,             // 总存储容量
                                      UINT64 storage_used,                 // 已用存储空间
                                      UINT64 storage_remaining,            //剩余存储空间（MB)
                                      int compress_bitrate              //压缩比特率(kbps)
                                    )
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::AccountStorageCapacity(szBuf,szAccount,storage_capacity,storage_used,storage_remaining,compress_bitrate);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
    
    #if 0   //UDP模式暂没用到，先取消
    // 添加到命令队列中
    AddCommand(szBuf, len, device);
    #endif
}

    // 寻呼台请求上传歌曲文件
void    CCommandSend::CmdRequestUploadSong(CSection&	device,					    // 设备
                                    int result,                       // 返回值
                                    const char*	szHostIP,	// 服务IP地址
                                    unsigned short	uPort,	// HTTP端口
                                    const char* uploadUrl             //上传地址（cgi）
                                )
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::RequestUploadSong(szBuf,result,szHostIP,uPort,uploadUrl);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
    
    #if 0   //UDP模式暂没用到，先取消
    // 添加到命令队列中
    AddCommand(szBuf, len, device);
    #endif
}

// 寻呼台通知服务器上传状态
void    CCommandSend::CmdUploadSongStatus(CSection&	device,					    // 设备
                                    int result                                  // 返回值
                                    )
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::UploadSongStatus(szBuf,result);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
    
    #if 0   //UDP模式暂没用到，先取消
    // 添加到命令队列中
    AddCommand(szBuf, len, device);
    #endif
}

// 寻呼台请求删除本地歌曲
void    CCommandSend::CmdRequestDeleteSong(CSection&	device,					    // 设备
                                    int result                                  // 返回值
                                    )
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::RequestDeleteSong(szBuf,result);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
    
    #if 0   //UDP模式暂没用到，先取消
    // 添加到命令队列中
    AddCommand(szBuf, len, device);
    #endif
}


// 控制寻呼台向指定分区设备发起广播寻呼
void    CCommandSend::CmdSetBroadcastPaging(CSection&	device,					// 设备
                                    const char*	szAccount,						// 用户名
                                    int control_event,                          //控制类别
                                    int isAllZone,                              //该用户的全部分区设备
                                    vector<string> &zoneMacs                    //分区设备集合
                                    )
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SetBroadcastPaging(szBuf,device.GetMac(),szAccount,control_event,isAllZone,zoneMacs);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
    
    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}


// 4.62 消防采集器通道触发状态改变时，主动通知主机
void CCommandSend::CmdResponseAlarmStateChanged(CSection&	device)	// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::ResponseAlarmStateChanged(szBuf);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}


// 4.66主机向终端查询/设置混音模式
void CCommandSend::CmdMixingMode(	CSection&	device,				// 设备
                                    char		channel,			// channel，为-1时，是查询，0双声道 ，1为单声道
                                    unsigned char		auxVol,				// 线路音量增益
                                    unsigned char		mixing,				// 0：混音关，1：DAC双声道混合输出，2：AUX混合DAC输出
                                    unsigned char		dacVol)				// 数字音量增益，AUX混合DAC输出时有效
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::MixingMode(szBuf, channel, auxVol, mixing, dacVol);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);

    // 设置，需要写入日志
    if (channel >= 0)
    {
        CMyString strTip;
        strTip.Format(("%s : channel = %d, mixing = %d, auxVol = %d, dacVol = %d"),
            device.GetIP(), channel, mixing, auxVol, dacVol);
        m_pNetwork->AddLog(strTip);
    }
}

//  4.68 主机向设备请求重启（组播）
void CCommandSend::CmdReootMulticast(vector<CSection*>& devices)		// 重启的设备
{
    // TCP无法组播
    if (NETWORK_IS_TCP)
    {
        return;
    }

    char	data[MAX_BUF_LEN]	= {0};
    char	szMac[10]			= {0};	// 数组需要大于7，否则SetMac中的sscanf_s会出错
    unsigned char	macLen				= 6;
    int		nDeviceCount = devices.size();
    int		dataLen = 1;	// 第一位是分区个数
    unsigned char	count	= 0;

    for (int i=0; i<nDeviceCount; ++i)
    {
        SetMac(szMac, devices[i]->GetMac());
        memcpy(data + dataLen, szMac, macLen);
        dataLen += macLen;
        count++;

        // 如果凑够缓冲区或者是最后一个分区，则发送出去
        if (dataLen >= MAX_BUF_LEN - 150 || i == nDeviceCount - 1)
        {
            data[0] = count;
            char szBuf[MAX_BUF_LEN] = {0};
            int len = CProtocol::RebootMulticast(szBuf, data, dataLen);
            m_pNetwork->SendUdpData(szBuf, len, MULTICAST_SEND_IP, MULTICAST_SEND_PORT, 1);
            dataLen = 1;
            count = 0;
        }
    }
}

// 4.70 回应终端被寻呼通知(TCP模式)
void CCommandSend::CmdResponseNotifyPaging(CSection&	device)		// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::ResponseNotifyPaging(szBuf);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}

// 4.72 回应寻呼台向终端发送掉线再次寻呼指令（TCP模式）
void CCommandSend::CmdResponsePagingAgain(CSection&	device)			// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::ResponsePagingAgain(szBuf);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}

// 4.75查询/设置网络解码分区器输出状态，为负数表示查询
void CCommandSend::CmdSplitterStatus(CSection&	device,				// 设备
                                        short	status)				// 输出状态（负数表示查询）
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::SplitterStatus(szBuf, status);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}

// 4.76 查询/设置解码功率分区器、数字功放EMC状态
void CCommandSend::CmdEmcStatus(CSection&	device,					// 设备
                                short		status)					// 状态（负数表示查询）
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::EmcStatus(szBuf, status);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}

// 4.97查询/设置 DSP6636无线MIC控制面板状态
void CCommandSend::CmdMicStatus(CSection&      device,				// 设备
                                char            volume,             // 音量：为负数表示查询，0-16
                                unsigned char	power,              // 发射功率 1-3
                                unsigned char	channel)            // 频道：1-99
{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::MicStatus(szBuf, volume, power, channel);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}

// 4.95 查询网速带宽
void CCommandSend::CmdQueryNetQuality(CSection&	device,		// 设备
                                      int   nNetType,       // 网络连接测试 0x00 : UDP单播, 0x01: UDP组播, 0x02 : TCP
                                      const char* szIP,     // 查询IP
                                      ushort  uPort)        // 查询端口

{
    char szBuf[MAX_BUF_LEN] = {0};
    int	len	= CProtocol::QueryDeviceNetQuality(szBuf, nNetType, szIP, uPort);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    //AddCommand(szBuf, len, device);
}


/*-----------------         集中模式命令             ------------------*/

// 设置工作模式
void CCommandSend::CmdSetWorkPattern(	CSection&	device,		// 设备
                                        char	pattern,		// 工作模式
                                        int		nRepeatCount)	// 重复发送次数
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::SetWorkPattern(szBuf, pattern);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, nRepeatCount);

    // 添加到命令队列中(有定时器检测，不需要加入到队列)
    //AddCommand(szBuf, len, device);

    CMyString strTip;
    strTip.Format(("%s Set Work Pattern : %d"), device.GetIP(), pattern);
    m_pNetwork->AddLog(strTip);
}


//4.42 回应寻呼站/移动设备请求主机播放节目源（集中模式）
void CCommandSend::CmdResponsePlaySource(	CSection&	device,		// 设备
                                            unsigned char	selSecID,		// 选中分区ID
                                            unsigned char	result)			// 播放结果
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::ResponsePlaySource(szBuf, selSecID, result);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    CMyString strTip;
    strTip.Format(("%s Response Play Source : %d"), device.GetIP(), result);
    m_pNetwork->AddLog(strTip);
}

// 4.43 请求播放节目源（指定分区）
void CCommandSend::CmdNotifyStreamSource(	CSection&	device,		// 设备
                                            char	source,			// 音源
                                            char	format,			// 歌曲格式
                                            unsigned int	sampleRate,		// 采样率
                                            unsigned char	bitSample,		// 采样精度
                                            unsigned char	channels,		// 声道数
                                            const char*	szName,             // 歌曲名称
                                            const char*	szMultiIP,          // 组播IP
                                            unsigned short	port,			// 组播端口
                                            unsigned char	volume,			// 定时音量
                                            unsigned int  fileLength,       // 歌曲文件大小
                                            unsigned int  totalFramesCnt, // 总帧数
                                            unsigned int  currentFrame,     //当前帧数
                                            string        fileMd5,          //md5
                                            bool isUseMulticastNewCmd,
                                            unsigned char	reserve)		// 保留位

{
    //printf("CmdNotifyStreamSource...\n");
    char szBuf[MAX_BUF_LEN] = {0};
    int len=0;

    #if SUPPORT_REMOTE_CONTROLER
    if(source == SOURCE_REMOTE_PLAY)
    {
        source = SOURCE_PLAY;
    }
    #endif

    //20250619 使用噪声自适应音量
    #if SUPPORT_NOISE_DETECTOR
    if(device.m_nNoiseDetectorSectionVolume>0 && device.m_nNoiseDetectorSectionVolume<=100)
    {
        volume = device.m_nNoiseDetectorSectionVolume;
    }
    #endif

    if(device.IsTcpMode())
    {
        //重置设备已存在本地歌曲标志，等待其应答
        device.SetIsExistLocalSong(false);
        len = CProtocol::NotifyStreamSource(szBuf,
                                            source,
                                            format,
                                            sampleRate,
                                            bitSample,
                                            channels,
                                            szName,
                                            CNetwork::GetHostIP(),
                                            g_Global.m_KCP_PORT,
                                            volume,
                                            fileLength,
                                            totalFramesCnt,
                                            currentFrame,
                                            fileMd5.data(),
                                            isUseMulticastNewCmd);
    }
    else
    {
        len = CProtocol::NotifyStreamSource(szBuf,
                                            source,
                                            format,
                                            sampleRate,
                                            bitSample,
                                            channels,
                                            szName,
                                            szMultiIP,
                                            port,
                                            volume,
                                            fileLength,
                                            totalFramesCnt,
                                            currentFrame,
                                            fileMd5.data(),
                                            isUseMulticastNewCmd);
    }
    
    

    szBuf[3] = reserve;

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    if(device.IsTcpMode())
    {
        //TCP模式不处理
    }else
    {
         AddCommand(szBuf, len, device);
    }
}

#if 0
// 4.44 播放节目源数据流传输（TCP下的集中模式的UDP单播）
void CCommandSend::CmdStreamSource(CSection&	device,					// 设备
                                    const char*		data,					// 数据流
                                    int			dataLen,				// 数据流长度
                                   CUDPSocket* pUdpSocket)
{
    if (device.m_uAudioPort > 0)
    {
        //CHAR szBuf[MAX_BUF_LEN] = {0};
        //int  len = CProtocol::StreamSource(szBuf, data, dataLen);

        if (pUdpSocket == NULL)
        {
            //m_pNetwork->m_pSocketStream->SendData(szBuf, len, device.GetIP(), device.m_uAudioPort, 1);
            m_pNetwork->m_pSocketStream->SendData((void *)data, dataLen, device.GetIP(), device.m_uAudioPort, 1);
        }
        else
        {
            //pUdpSocket->SendData(szBuf, len, device.GetIP(), device.m_uAudioPort, 1);
            pUdpSocket->SendData((void *)data, dataLen, device.GetIP(), device.m_uAudioPort, 1);
        }
    }
}
#endif

// 4.60寻呼台/移动设备/分控设备向主机发送选中的分区（集中模式）
void CCommandSend::CmdResponseSelectedSections(	CSection&	device,			// 设备
                                                unsigned char	selSecID,			// 选中分区ID
                                                unsigned char	packID)				// 包ID
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::ResponseSelectedSections(szBuf, selSecID, packID);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);
}

int  CCommandSend::SectionStatus(char* data, CSection& section)
{
    char szMac[10] = {0};	// 数组需要大于7，否则SetMac中的sscanf_s会出错

    #if 0
    unsigned char sigalStatus = 0x00;	// 默认是不具备回路检测功能

    if (section.m_pPowerInfo != NULL)
    {
        // 具备回路检测功能
        if (section.m_pPowerInfo->m_bLoop)
        {
            sigalStatus = (section.m_pPowerInfo->m_bSigal ? 0x02 : 0x01);
        }
    }
    #endif

    SetMac(szMac, section.GetMac());
    //printf("szMac : %s\n", section.GetMac());
    int dataLen = CProtocol::SectionStatus(	data,
                                            szMac,
                                            section.GetVolume(),
                                            section.GetProSource(),
                                            section.GetPlayStatus(),
                                            section.GetProName(),
                                            section.IsTimerInvalid() ? 0x00 : 0x01,
                                            section.GetDeviceFeature(),
                                            section.NeedUpdateSectionFile() ? 0x00 : 0x01);

    return dataLen;
}


// 4.67 主机向分控设备下发终端当前状态
void CCommandSend::CmdForwardStatus(CSection&	controlDevice,				// 分控设备
                                                                         CSection*	pSection)					// pSection为NULL时，表示所有的分区
{
    char data[MAX_BUF_LEN]	= {0};
    int  dataLen = 0;

    // 转发单个分区的状态
    if (pSection != NULL)
    {
        if(pSection->GetID() > MAX_SECTION_COUNT)
            return;
        dataLen = SectionStatus(data + 1, *pSection);
        data[0] = 1;	// 分区总数为1
        char szBuf[MAX_BUF_LEN] = {0};
        int bufLen = CProtocol::ForwardStatus(szBuf, data, dataLen + 1);
        m_pNetwork->SendData(szBuf, bufLen, controlDevice, controlDevice.GetIP(), UDP_PORT, 1);

        AddCommand(szBuf, bufLen, controlDevice);
    }
    // 转发全部分区的状态
    else
    {
        int		nSecCount	= g_Global.m_Sections.GetSecCount();
        unsigned char	count		= 0;

        dataLen = 1;	// data从1开始，0是分区个数

        for (int i=0; i<nSecCount; ++i)
        {
            CSection& section = g_Global.m_Sections.GetSection(i);
            if(section.GetID() <= MAX_SECTION_COUNT)
            {
                int pos = SectionStatus(data + dataLen, section);

                dataLen += pos;
                count++;
            }
            if(count == 0)
                continue;
            // 如果凑够缓冲区或者是最后一个分区，则发送出去
            if (dataLen >= MAX_BUF_LEN - 150 || i == nSecCount - 1)
            {
                data[0] = count;
                char szBuf[MAX_BUF_LEN] = {0};
                int bufLen = CProtocol::ForwardStatus(szBuf, data, dataLen);

                m_pNetwork->SendData(szBuf, bufLen, controlDevice, controlDevice.GetIP(), UDP_PORT, 1);

                AddCommand(szBuf, bufLen, controlDevice);

                dataLen = 1;
                count = 0;
            }
        }
    }

    // 添加到命令队列中(之前的设备不支持此命令，所以不能加到队列中去)
    //AddCommand(szBuf, len, controlDevice);
}



// 4.67 主机向分控设备下发终端当前状态
void CCommandSend::CmdForwardStatusVary(CSection&	controlDevice,				// 分控设备
                                                                         vector<string> &vecSections)					// pSection为NULL时，表示所有的分区
{
    char data[MAX_BUF_LEN]	= {0};
    int  dataLen = 0;

    int	nSecCount = vecSections.size();
    unsigned char	count		= 0;

    dataLen = 1;	// data从1开始，0是分区个数
    //printf("CmdForwardStatusVary:zone_cnt=%d\n",nSecCount);
    for (int i=0; i<nSecCount; ++i)
    {
        LPCSection lpSection = g_Global.m_Sections.GetSectionByMac(vecSections[i]);
        if(lpSection == NULL)
            continue;
        if(lpSection->GetID() <= MAX_SECTION_COUNT)
        {
            int pos = SectionStatus(data + dataLen, *lpSection);

            dataLen += pos;
            count++;
        }
        if(count == 0)
            continue;
        // 如果凑够缓冲区或者是最后一个分区，则发送出去
        if (dataLen >= MAX_BUF_LEN - 150 || i == nSecCount - 1)
        {
            data[0] = count;
            char szBuf[MAX_BUF_LEN] = {0};
            int bufLen = CProtocol::ForwardStatus(szBuf, data, dataLen);

            m_pNetwork->SendData(szBuf, bufLen, controlDevice, controlDevice.GetIP(), UDP_PORT, 1);

            AddCommand(szBuf, bufLen, controlDevice);

            dataLen = 1;
            count = 0;
        }
    }
}


 #if SUPPORT_PAGER_CALL
// 5.02主机向分控设备发送所有分控设备的信息
void CCommandSend::CmdForwardPagerStatus(CSection*	controlDevice,				// 分控设备
                                        CSection*	pDevice)					// pDevice为NULL时，表示所有的寻呼台设备
{
    // 支持寻呼台间对讲的版本才发送所有寻呼台的信息
    #if !APP_IS_SUPPORT_INTERCOM_BETWEEN_PAGER
    return;
    #endif
    char data[MAX_BUF_LEN]	= {0};
    int  dataLen = 0;

    if(controlDevice != NULL && controlDevice->IsSupportCallDevice())
    {
        // 转发单个分区的状态
        if (pDevice != NULL)
        {
            data[0] = 0x01;         //本次发送的寻呼台总数为1

            char szMac[10] = {0};	// 数组需要大于7，否则SetMac中的sscanf_s会出错
            SetMac(szMac, pDevice->GetMac());
            dataLen = CProtocol::PagerStatus(data+1,
                        szMac,
                        pDevice->GetProSource(),
                        pDevice->IsSupportCallDevice(),pDevice->IsSupportVideoDevice(),0);

            char szBuf[MAX_BUF_LEN] = {0};   
            int bufLen = CProtocol::ControlCommand(	szBuf,
                                            CMD_ALL_PAGER_STATUS,
                                            data,
                                            dataLen+1);
            m_pNetwork->SendData(szBuf, bufLen, *controlDevice, controlDevice->GetIP(), UDP_PORT, 1);

            AddCommand(szBuf, bufLen, *controlDevice);
        }
        // 转发全部分区的状态
        else
        {
            int		nSecCount	= g_Global.m_Pagers.GetSecCount();
            unsigned char	count		= 0;

            dataLen = 1;	      //data从1开始

            for (int i=0; i<nSecCount; ++i)
            {
                CSection& section = g_Global.m_Pagers.GetSection(i);

                char szMac[10] = {0};	// 数组需要大于7，否则SetMac中的sscanf_s会出错
                SetMac(szMac, section.GetMac());
                int pos = CProtocol::PagerStatus(data + dataLen,
                        szMac,
                        section.GetProSource(),
                        section.IsSupportCallDevice(),
                        section.IsSupportVideoDevice(),
                        0);

                dataLen += pos;
                count++;

                // 如果凑够缓冲区或者是最后一个分区，则发送出去
                if (dataLen >= MAX_BUF_LEN - 100 || i == nSecCount - 1)
                {
                    char szBuf[MAX_BUF_LEN] = {0};

                    data[0] = count;        //设备个数
                    
                    int bufLen = CProtocol::ControlCommand(	szBuf,
                                            CMD_ALL_PAGER_STATUS,
                                            data,
                                            dataLen);

                    m_pNetwork->SendData(szBuf, bufLen, *controlDevice, controlDevice->GetIP(), UDP_PORT, 1);

                    AddCommand(szBuf, bufLen, *controlDevice);

                    dataLen = 1;
                    count = 0;
                }
            }
        }
    }
    else
    {
         int nSecCount	= g_Global.m_Pagers.GetSecCount();
        for (int i=0; i<nSecCount; ++i)
        {
            controlDevice = &g_Global.m_Pagers.GetSection(i);
            if(!controlDevice->IsSupportCallDevice())
            {
                continue;
            }
            // 转发单个分区的状态
            if (pDevice != NULL)
            {
                data[0] = 0x01;         //本次发送的寻呼台总数为1

                char szMac[10] = {0};	// 数组需要大于7，否则SetMac中的sscanf_s会出错
                SetMac(szMac, pDevice->GetMac());
                dataLen = CProtocol::PagerStatus(data+1,
                            szMac,
                            pDevice->GetProSource(),
                            pDevice->IsSupportCallDevice(),
                            pDevice->IsSupportVideoDevice(),
                            0);

                char szBuf[MAX_BUF_LEN] = {0};   
                int bufLen = CProtocol::ControlCommand(	szBuf,
                                                CMD_ALL_PAGER_STATUS,
                                                data,
                                                dataLen+1);
                m_pNetwork->SendData(szBuf, bufLen, *controlDevice, controlDevice->GetIP(), UDP_PORT, 1);


                AddCommand(szBuf, bufLen, *controlDevice);
            }
            // 转发全部分区的状态
            else
            {
                int		nSecCount	= g_Global.m_Pagers.GetSecCount();
                unsigned char	count		= 0;

                dataLen = 1;	//

                for (int i=0; i<nSecCount; ++i)
                {
                    CSection& section = g_Global.m_Pagers.GetSection(i);

                    char szMac[10] = {0};	// 数组需要大于7，否则SetMac中的sscanf_s会出错
                    SetMac(szMac, section.GetMac());
                    int pos = CProtocol::PagerStatus(data + dataLen,
                            szMac,
                            section.GetProSource(),
                            section.IsSupportCallDevice(),
                            section.IsSupportVideoDevice(),
                            0);

                    dataLen += pos;
                    count++;

                    // 如果凑够缓冲区或者是最后一个分区，则发送出去
                    if (dataLen >= MAX_BUF_LEN - 100 || i == nSecCount - 1)
                    {
                        data[0] = count;        //设备个数

                        char szBuf[MAX_BUF_LEN] = {0};
                        

                        int bufLen = CProtocol::ControlCommand(	szBuf,
                                                CMD_ALL_PAGER_STATUS,
                                                data,
                                                dataLen);

                        m_pNetwork->SendData(szBuf, bufLen, *controlDevice, controlDevice->GetIP(), UDP_PORT, 1);

                        AddCommand(szBuf, bufLen, *controlDevice);

                        dataLen = 1;
                        count = 0;
                    }
                }
            }
        }
    }
    
}

#endif


// 4.69 主机向分控设备发送音频采集器列表在线/离线状态
void CCommandSend::CmdForwardAudioCollector(CSection&	controlDevice,		// 分控设备
                                            CSection*	pCollector)			// pCollector为NULL时，表示所有的音频采集器
{
    char	szMac[10]			= {0};	// 数组需要大于7，否则SetMac中的sscanf_s会出错
    char	data[MAX_BUF_LEN]	= {0};
    int		pos = 0;

    // 转发单个音频采集器的状态
    if (pCollector != NULL)
    {
        pos = 0;
        data[pos++] = 1;	// 音频采集器总数为1

        SetMac(szMac, pCollector->GetMac());
        memcpy(data + pos, szMac, 6);
        pos += 6;
        data[pos++] = (pCollector->IsOnline() ? 0x01 : 0x00);

        char szBuf[MAX_BUF_LEN] = {0};
        int bufLen = CProtocol::ForwardAudioCollectors(szBuf, data, pos);

        m_pNetwork->SendData(szBuf, bufLen, controlDevice, controlDevice.GetIP(), UDP_PORT, 1);
    }
    // 转发全部分区的状态
    else
    {
        int		nColCount			= g_Global.m_AudioCollectors.GetSecCount();
        unsigned char	count		= 0;

        pos	= 1;
        for (int i=0; i<nColCount; ++i)
        {
            CSection& collector = g_Global.m_AudioCollectors.GetSection(i);
            SetMac(szMac, collector.GetMac());
            memcpy(data + pos, szMac, 6);
            pos += 6;
            data[pos++] = (collector.IsOnline() ? 0x01 : 0x00);

            count++;

            // 如果凑够缓冲区或者是最后一个音频采集器，则发送出去
            if (pos >= MAX_BUF_LEN - 150 || i == nColCount - 1)
            {
                data[0] = count;
                char szBuf[MAX_BUF_LEN] = {0};
                int bufLen = CProtocol::ForwardAudioCollectors(szBuf, data, pos);
                m_pNetwork->SendData(szBuf, bufLen, controlDevice, controlDevice.GetIP(), UDP_PORT, 1);
                pos = 1;
                count = 0;
            }
        }
    }

    // 添加到命令队列中(之前的设备不支持此命令，所以不能加到队列中去)
    //AddCommand(szBuf, len, controlDevice);
}

// 4.73 查询/设置音频传输方式
void CCommandSend::CmdAudiocastMode(CSection&	device,			// 设备
                                    char		audiocast)		// 音频传输方式
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::AudiocastMode(szBuf, audiocast);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中(有定时器检测，不需要加入到队列)
    //AddCommand(szBuf, len, device);

    CMyString strTip;
    strTip.Format(("%s Set Audiocast : %d"), device.GetIP(), audiocast);
    m_pNetwork->AddLog(strTip);
}

/*
void CCommandSend::CmdNotifyPaging(CSection &device, string strExten, u_char uPort)
{
        int nExten = atoi(strExten.data());
        if(nExten <= 0 || nExten > 255*255)
                return;

        u_short uExten = nExten;
        char szBuf[MAX_BUF_LEN] = {0};
        char szIP[MAX_IP_LEN] = {0};
        int   nHNum = uExten>>8;                // 10 //+ nExten%10;
        int   nLNum  = uExten&0x00FF;

        sprintf(szIP, "235.6.%d.%d", nHNum, nLNum);


        m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

        AddCommand(szBuf, len, device);

        CMyString strTip;
        strTip.Format(("Notify paging : %s"), device.GetIP());
        m_pNetwork->AddLog(strTip);

}*/

/*-------------------          SIP命令                 -------------------*/
void CCommandSend::CmdSipGetStatus(	CSection&	device)	// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::SipGetStatus(szBuf);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}


// SIP账号登录
void CCommandSend::CmdSipLogIn(CSection& device,						// 设备
                               bool isEnableSip,                        //是否启用SIP
                               unsigned char outPutVolume,              // 输出音量
                               const char*	szAccount,					// 登录账号
                               const char*	szPassword,					// 登录密码
                               const char*	szServerAddr,				// 服务器地址
                               int		nServerPort,                    // 端口
                               int     nServerProtocol)                 // 协议
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::SipLogIn(szBuf, isEnableSip, outPutVolume, szAccount, szPassword, szServerAddr, nServerPort, nServerProtocol);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

#if 0
    // 添加到命令队列中
    AddCommand(szBuf, len, device);
#endif
    CMyString strTip;
    strTip.Format(("%s SIP Log in : %s, %s, %d"), device.GetIP(), szAccount, szServerAddr, nServerPort);
    m_pNetwork->AddLog(strTip);
}

// 获取SIP登录账号信息
void CCommandSend::CmdSipGetLogInfo(CSection&	device)				// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::SipGetLogInfo(szBuf);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}

// SIP会话挂断
void CCommandSend::CmdSipHangUp(CSection&	device)				// 设备
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::SetIdleStatus(szBuf);

    m_pNetwork->SendData(szBuf, len, device, device.GetIP(), UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);

    CMyString strTip;
    strTip.Format(("%s SIP Hang up."), device.GetIP());
    m_pNetwork->AddLog(strTip);
}


#if SUPPORT_WEB_PAGING
/**************WEB寻呼指令 START***********************************/
void CCommandSend::CmdWebPagingNotify(CWebPaging &webpPaging,int event,AudioAlgorithm audioCodecs)
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::ServerWebPagingNotify(szBuf,webpPaging, event,audioCodecs);

    //CmdControlCheckedSection(szBuf,len,"",webpPaging.GetSecIndexs(),webpPaging.GetSecCount(),false);
    unsigned int*   pCheckedIndexs = webpPaging.GetSecIndexs();
    for (unsigned int i=0; i<webpPaging.GetSecCount(); ++i)
    {
        CSection& section = g_Global.m_Sections.GetSection(pCheckedIndexs[i]);

        // 只有在线的分区才发送命令
        if (section.IsOnline())
        {
            m_pNetwork->SendData(szBuf, len, section, section.GetIP(), UDP_PORT, 1);
        }
    }
}

/**************WEB寻呼指令 END************************************/

#endif


//主机通知解码终端即将进入定时音源
void CCommandSend::CmdNotifyDecoderReadyTiming(CSection&	device)
{
    char szBuf[MAX_BUF_LEN] = {0};
    int len = CProtocol::NotifyDecoderTiming(szBuf);

    m_pNetwork->SendUdpData(szBuf, len, device.GetIP(), GPS_UDP_PORT, 1);

    // 添加到命令队列中
    AddCommand(szBuf, len, device);
}
