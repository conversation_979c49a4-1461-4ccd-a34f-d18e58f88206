#include "stdafx.h"
#include "Protocol.h"
#include <stdio.h>
#include "Network/Monitor/MonitorProtocol.h"
#include "math.h"

char CProtocol::cmdSequence = 0;

#define MAC_SIZE 6

CProtocol::CProtocol()
{

}

CProtocol::~CProtocol()
{

}


/**************************************************************/

// 获取设备描述
CMyString	CProtocol::GetDescriptionDevice(DeviceModel model)
{
    CMyString strDevice = ("Unknown Device");

    switch(model)
    {
    case MODEL_HOST:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Host", ("主机"));
        break;

    case MODEL_PAGER_A:
    case MODEL_PAGER_B:
    case MODEL_PAGER_C:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Smart Pager", ("智能寻呼站"));
        break;

    case MODEL_IP_SPEAKER_A:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "IP SpeakerA", ("解码终端A"));
        break;
    case MODEL_IP_SPEAKER_B:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "IP SpeakerB", ("解码终端B"));
        break;
    case MODEL_IP_SPEAKER_C:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "IP SpeakerC", ("解码终端C"));
        break;
    case MODEL_IP_SPEAKER_D:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "IP SpeakerD", ("解码终端D"));
        break;
    case MODEL_IP_SPEAKER_E:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "IP SpeakerE", ("解码终端E"));
    break;
    case MODEL_IP_SPEAKER_F:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "IP SpeakerF", ("解码终端F"));
        break;
    case MODEL_IP_SPEAKER_G:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "IP SpeakerG", ("解码终端G"));
        break;

    case MODEL_MOBILE:
        strDevice = ("Mobile Device");
        break;

    case MODEL_FIRE_COLLECTOR_A:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Fire CollectorA", ("消防采集器A"));
        break;

    case MODEL_AUDIO_COLLECTOR_A:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Audio CollectorA", ("音频采集器A"));
        break;

    case MODEL_SEQUENCE_POWER_A:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Sequence PowerA", ("电源时序器A"));
        break;
    
    case MODEL_FIRE_COLLECTOR_B:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Fire CollectorB", ("消防采集器B"));
        break;

    case MODEL_AUDIO_COLLECTOR_B:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Audio CollectorB", ("音频采集器B"));
        break;

    case MODEL_SEQUENCE_POWER_B:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Sequence PowerB", ("电源时序器B"));
        break;
    
    case MODEL_REMOTE_CONTROLER:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Remote Controler", ("远程遥控器"));
    break;

    case MODEL_FIRE_COLLECTOR_C:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Fire CollectorC", ("消防采集器C"));
        break;

    case MODEL_AUDIO_COLLECTOR_C:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Audio CollectorC", ("音频采集器C"));
        break;

    case MODEL_SEQUENCE_POWER_C:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Sequence PowerC", ("电源时序器C"));
        break;
    
    case MODEL_REMOTE_CONTROLER_C:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Remote ControlerC", ("远程遥控器C"));
    break;

    case MODEL_FIRE_COLLECTOR_F:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Fire CollectorF", ("消防采集器F"));
        break;

    case MODEL_AUDIO_COLLECTOR_F:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Audio CollectorF", ("音频采集器F"));
        break;

    case MODEL_SEQUENCE_POWER_F:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Sequence PowerF", ("电源时序器F"));
        break;
    
    case MODEL_REMOTE_CONTROLER_F:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Remote ControlerF", ("远程遥控器F"));
    break;

    case MODEL_INTERCOM_STATION:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Network Video Pager", ("网络可视对讲寻呼台"));
        break;

    case MODEL_GPS:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "GPS Equipment", ("GPS校时器"));
        break;

    case MODEL_CONTROL_SERVER:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Control Server", ("分控服务器"));
        break;

    case MODEL_AUDIO_MIXER_DECODER:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Audio Mixer Decoder", ("音频协处理器-解码器"));
    break;

    case MODEL_AUDIO_MIXER_ENCODER:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Audio Mixer Encoder", ("音频协处理器-编码器"));
    break;

    case MODEL_AUDIO_MIXER_DECODER_C:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Audio Mixer DecoderC", ("音频协处理器C-解码器"));
    break;

    case MODEL_AUDIO_MIXER_ENCODER_C:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Audio Mixer EncoderC", ("音频协处理器C-编码器"));
    break;

    case MODEL_PHONE_GATEWAY:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Phone Gateway", ("电话网关"));
    break;
    
    case MODEL_AMP_CONTROLER:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Amp Controler", ("功放主备控制器"));
    break;

    case MODEL_NOISE_DETECTOR:
        strDevice = LANG_STR(LANG_SECTION_ZONE_GROUP, "Noise Adaptive", ("噪声自适应终端"));
    break;

    default:
        break;
    }

    return strDevice;

}

// 获取节目源描述
CMyString CProtocol::GetDescriptionProSource(ProgramSource src)
{
    CMyString strProSrc = ("Unknown Source");

    switch(src)
    {
    case PRO_OFFLINE:
        strProSrc = LANG_STR(LANG_SECTION_ZONE_GROUP, "Offline", ("离线"));
        break;

    case PRO_IDLE:
        strProSrc = LANG_STR(LANG_SECTION_ZONE_GROUP, "Idle", ("空闲"));
        break;
    case PRO_ANALOG_INPUT:
        strProSrc = LANG_STR(LANG_SECTION_ZONE_GROUP, "Local Play", ("本地播放"));
        break;

    case PRO_ALARM:
        strProSrc = LANG_STR(LANG_SECTION_ZONE_GROUP, "Fire Alarm", ("消防告警"));
        break;

    case PRO_PAGING:
        strProSrc = LANG_STR(LANG_SECTION_ZONE_GROUP, "Paging", ("网络寻呼"));
        break;

    case PRO_LOCAL_PLAY:
        strProSrc = LANG_STR(LANG_SECTION_ZONE_GROUP, "Net Play", ("网络点播"));
        break;

    case PRO_CALL:
        strProSrc = LANG_STR(LANG_SECTION_ZONE_GROUP, "Call", ("对讲"));
        break;

    case PRO_TIMING:
        strProSrc = LANG_STR(LANG_SECTION_ZONE_GROUP, "Timing", ("定时播放"));
        break;

    case PRO_100V:
        strProSrc =("100V");
        break;

    case PRO_AUDIO_MIXED:
        strProSrc =("Mixed");
        break;
    
    case PRO_PHONE_GATEWAY:
        strProSrc =("Phone Gateway");
    break;

    case PRO_SIP_CALLING:
        strProSrc =("Sip");
    break;

    case PRO_API_TTS_MUSIC:
        strProSrc =("TTS MUSIC");
    break;

    case PRO_API_NET_RADIO:
        strProSrc =("Net Radio");
    break;

    case PRO_EVENT:
        strProSrc = LANG_STR(LANG_SECTION_ZONE_GROUP, "Event", ("事件触发"));
        break;

    default:
    {
        if (IsAudioCollectorSrc(src))
        {
            //strProSrc = LANG_STR(LANG_SECTION_ZONE_GROUP, "Audio Collector", ("音频采集"));
            strProSrc = "音频采集";
        }
    }

        break;
    }

    return strProSrc;
}


// 获取播放状态描述
CMyString	CProtocol::GetDescriptionPlayStatus(PlayStatus status)
{
    CMyString strStatus = ("Unknown Play Status");

    switch(status)
    {
    case PS_PLAY:
        strStatus = LANG_STR(LANG_SECTION_CONTROL_PANE, "Play", ("播放"));
        break;

    case PS_PAUSE:
        strStatus = LANG_STR(LANG_SECTION_CONTROL_PANE, "Pause", ("暂停"));
        break;

    case PS_STOP:
        strStatus = LANG_STR(LANG_SECTION_CONTROL_PANE, "Stop", ("停止"));
        break;

    default:
        break;
    }

    return strStatus;
}


// 获取播放模式描述
CMyString	CProtocol::GetDescriptionPlayMode(PlayMode mode)
{
    CMyString strMode = ("Unknown Play Mode");

    switch(mode)
    {
    case PM_SINGLE:
        strMode = LANG_STR(LANG_SECTION_PLAY_LIST, "Once", ("单曲播放"));
        break;

    case PM_SINGLE_CYCLE:
        strMode = LANG_STR(LANG_SECTION_PLAY_LIST, "Repeat Once", ("单曲循环"));
        break;

    case PM_ORDER:
        strMode = LANG_STR(LANG_SECTION_PLAY_LIST, "Order", ("顺序播放"));
        break;

    case PM_LIST_CYCLE:
        strMode = LANG_STR(LANG_SECTION_PLAY_LIST, "Repeat All", ("循环播放"));
        break;

    case PM_RANDOM:
        strMode = LANG_STR(LANG_SECTION_PLAY_LIST, "Shuffle", ("随机播放"));
        break;

    default:
        break;
    }

    return strMode;
}


// 获取文件类型描述
CMyString	CProtocol::GetDescriptionFileType(FileType ft)
{
    CMyString strFileType = ("Unknown File Type");

    switch(ft)
    {
    case FILE_GROUP:
        strFileType = LANG_STR(LANG_SECTION_DIALOG, "Group File", ("分组文件"));
        break;

    case FILE_PLAYLIST:
        strFileType = LANG_STR(LANG_SECTION_DIALOG, "Playlist File", ("播放列表"));
        break;

    case FILE_TIMER:
        strFileType = LANG_STR(LANG_SECTION_DIALOG, "Timing File", ("定时文件"));
        break;

    case FILE_SECTION:
        strFileType = LANG_STR(LANG_SECTION_DIALOG, "Zone File", ("分区文件"));
        break;

    case FILE_AUDIO_COLLECTOR:
        strFileType = LANG_STR(LANG_SECTION_DIALOG, "Audio Collector File", ("音频采集器文件"));
        break;

    case FILE_FIRE_COLLECTOR:
        strFileType = LANG_STR(LANG_SECTION_DIALOG, "Fire Collector File", ("消防采集器文件"));
        break;

    case FILE_MONITOR:
        strFileType = LANG_STR(LANG_SECTION_DIALOG, "Monitor File", ("监控文件"));
        break;
    
    case FILE_USER:
        strFileType = LANG_STR(LANG_SECTION_DIALOG, "User File", ("账户文件"));
        break;

    case FILE_SEQUENCE_POWER:
        strFileType = LANG_STR(LANG_SECTION_DIALOG, "Sequence Power File", ("电源时序器文件"));
    break;

    case FILE_PAGER:
        strFileType = LANG_STR(LANG_SECTION_DIALOG, "Pager File", ("寻呼台文件"));
    break;

    default:
        printf("ft=%d\n",ft);
        break;
    }

    return strFileType;
}


// 得到文件更新结果描述
CMyString	CProtocol::GetDescriptionFileUpdateResult(unsigned char result)
{
    CMyString strResult = ("Unknown File Result");

    if (result == UFR_LATEST)
    {
        strResult = LANG_STR(LANG_SECTION_ZONE_GROUP, "Latest File", ("文件最新"));
    }
    else if (result == UFR_NOT_IDLE)
    {
        strResult = LANG_STR(LANG_SECTION_ZONE_GROUP, "Not Idle", ("非空闲状态"));
    }
    else if (result == UFR_FAILURE)
    {
        strResult = LANG_STR(LANG_SECTION_ZONE_GROUP, "Sync Failure", ("同步失败"));
    }
    else if (result == UFR_SUCCESS)
    {
        strResult = LANG_STR(LANG_SECTION_ZONE_GROUP, "Sync Finished", ("同步完成"));
    }

    return strResult;
}


// 得到升级状态描述
CMyString	CProtocol::GetDescriptionUpgradeStatus(unsigned char status)
{
    CMyString strStatus = ("Unknown Upgrade Status");

    if (status == 0x01)
    {
        strStatus = LANG_STR(LANG_SECTION_ZONE_GROUP, "Start Downloading", ("开始下载"));
    }
    else if (status == 0x02)
    {
        strStatus = LANG_STR(LANG_SECTION_ZONE_GROUP, "Latest Version", ("最新版本"));
    }
    else if (status == 0x03)
    {
        strStatus = LANG_STR(LANG_SECTION_ZONE_GROUP, "Connection Timeout", ("连接超时"));
    }
    else if (status == 0x04)
    {
        strStatus = LANG_STR(LANG_SECTION_ZONE_GROUP, "Download Failed", ("下载失败"));
    }
    else if (status == 0x05)
    {
        strStatus = LANG_STR(LANG_SECTION_ZONE_GROUP, "Restarting", ("正在重启"));
    }

    return strStatus;
}


// 得到SIP状态描述
CMyString	CProtocol::GetDescriptionSipStatus(SipStatus status)
{
    CMyString strStatus = ("Unknown Sip Status");

    if (status == SIP_LOG_IN)
    {
        strStatus = LANG_STR(LANG_SECTION_SIP, "Log In", ("登录成功"));
    }
    else if (status == SIP_LOGGING)
    {
        strStatus = LANG_STR(LANG_SECTION_SIP, "Logging", ("正在登录"));
    }
    else if (status == SIP_LOG_FAILED)
    {
        strStatus = LANG_STR(LANG_SECTION_SIP, "Log Failed", ("登录失败"));
    }
    else if (status == SIP_RESPONSE_TIMEOUT)
    {
        strStatus = LANG_STR(LANG_SECTION_SIP, "Response Timeout", ("响应超时"));
    }
    else if (status == SIP_WRONG_PASSWORD)
    {
        strStatus = LANG_STR(LANG_SECTION_SIP, "Wrong Password", ("密码错误"));
    }
    else if (status == SIP_CALLING)
    {
        strStatus = LANG_STR(LANG_SECTION_SIP, "Calling", ("正在通话"));
    }

    return strStatus;
}

// 是否为音频采集器
bool	CProtocol::IsAudioCollectorSrc(ProgramSource src)
{
    return (PRO_AUDIO_COLLECTOR_MIN <= src && src<= PRO_AUDIO_COLLECTOR_MAX);
}

// 是否为点播、定时音源
bool CProtocol::IsAudioPlayStreamSrc(ProgramSource src)
{
    return (src == PRO_LOCAL_PLAY || src == PRO_TIMING);
}

// 是否为空闲音源
bool CProtocol::IsIdleSrc(ProgramSource src)
{
    return (src == PRO_IDLE);
}

// 是否为本地音源
bool CProtocol::IsLocalSrc(ProgramSource src)
{
    return (src == PRO_ANALOG_INPUT || src == PRO_100V);
}

// 获取音频采集音源的channelID
int  CProtocol::GetAudioCollectorChannelBySrc(ProgramSource src)
{
    int channelId=0;
    if(PRO_AUDIO_COLLECTOR_MIN <= src && src<= PRO_AUDIO_COLLECTOR_MAX)
    {
        channelId = (src-PRO_AUDIO_COLLECTOR_MIN)%4+1;
    }
    else
    {
        channelId=0;
    }
    return channelId;
}



/****************************************************************/

// 获取校验码
char CProtocol::GetChecksum(const char*	data,			// 数据内容
                            int		nLen)			// 数据长度
{
    // 数据长度为0
    if (nLen <= 0)
    {
        return 0;
    }

    char checksum = data[0];

    for (int i = 1; i < nLen; ++i)
    {
        // 异或和
        checksum = (char)(checksum ^ data[i]);
    }

    return checksum;
}


// 构成控制命令
unsigned short	CProtocol::ControlCommand(	char*	buf,		// 命令缓存区
                                    unsigned short	cmd,		// 命令
                                    const char*	data,		// 数据内容
                                    unsigned short	uDataLen)	// 数据长度
{
    buf[0]  = cmd/256;			// 请求命令
    buf[1]  = cmd%256;
    buf[2]  = cmdSequence++;	// 包序号
    buf[3]  = 0x00;				// 保留位
    buf[4]  = MODEL_HOST;		// 设备型号
    buf[5]  = 0x00;				// 包属性
    buf[6]  = uDataLen/256;		// 数据长度
    buf[7]	= uDataLen%256;

    // 数据内容为0
    if (uDataLen == 0)
    {
        buf[8] = 0x00;	// 检验码为0
    }
    else
    {
        memcpy(&buf[8], data, uDataLen);
        // 校验码
        buf[8+uDataLen] = CProtocol::GetChecksum(data, uDataLen);
    }

    // 返回整条命令长度
    return (8+uDataLen+1);
}

#if SUPPORT_SERVER_SYNC
// 广播服务器信息
unsigned short	CProtocol::BroadcastHostInfo(char* buf)
{
    char	data[MAX_BUF_LEN]	= {0};
    unsigned short	pos					= 0;

    //当前服务器的mac地址
    unsigned char	szMac[10]		= {0};	// 大小不能直接定义为MAC_LEN，否则调用sscanf_s会出错
    sscanf(CNetwork::GetHostMac(), "%x:%x:%x:%x:%x:%x", &szMac[0], &szMac[1], &szMac[2], &szMac[3], &szMac[4], &szMac[5]);
    memcpy(&data[pos], szMac, 6);
    pos += 6;

    //服务器类型，备用
    data[pos++]=0;

    //当前是否为备用服务器:0为主服务器,1为备用服务器
    data[pos++]=IS_BACKUP_SERVER;

    //服务器状态
    data[pos++]=g_Global.m_serverSync.m_nServerStatus;

    //服务器版本号长度
    int versionLen=strlen(VERSION);
    data[pos++]=versionLen;
    memcpy(data+pos,VERSION,versionLen);
    pos+=versionLen;


    return CProtocol::ControlCommand(	buf,
                                        CMD_BROADCAST_HOST_INFO,
                                        data,
                                        pos);
}
#endif


// 连接备用服务器
unsigned short	CProtocol::ConnectStandByServer(char* buf,unsigned char event,const char*	szMac,const char *sshPublicKey,const char* userName)
{
    char	data[MAX_BUF_LEN]	= {0};
    unsigned short	pos					= 0;

    //Event：1：主服务器请求连接备用服务器 2：备用服务器应答OK（已连接）  3：备用服务器应答失败（拒绝）
    data[pos++]=event;

    //当前服务器的mac地址
    //memcpy(&data[pos], CNetwork::GetHostMac(), 6);
    unsigned char	szTempMac[10]		= {0};	// 大小不能直接定义为MAC_LEN，否则调用sscanf_s会出错
    sscanf(CNetwork::GetHostMac(), "%x:%x:%x:%x:%x:%x", &szTempMac[0], &szTempMac[1], &szTempMac[2], &szTempMac[3], &szTempMac[4], &szTempMac[5]);
    memcpy(&data[pos], szTempMac, 6);
    pos += 6;

    //目的服务器的mac地址
    //memcpy(&data[pos], szMac, 6);
    sscanf(szMac, "%x:%x:%x:%x:%x:%x", &szTempMac[0], &szTempMac[1], &szTempMac[2], &szTempMac[3], &szTempMac[4], &szTempMac[5]);
    memcpy(&data[pos], szTempMac, 6);
    pos += 6;

//如果是主服务器，那么应该发送公钥
#if !IS_BACKUP_SERVER
    if(sshPublicKey!=NULL)
    {
        int key_len=strlen(sshPublicKey);
        data[pos++]=(key_len>>8)&0xFF;
        data[pos++]=key_len&0xFF;
        memcpy(&data[pos], sshPublicKey, key_len);
        pos += key_len;
    }
#else
    //备用服务器回传用户名
    if(userName!=NULL)
    {
        int user_len = strlen(userName);
        data[pos++]=user_len;
        memcpy(&data[pos], userName, user_len);
        pos += user_len;
    }
#endif

    return CProtocol::ControlCommand(	buf,
                                        CMD_CONNECT_STANDBY_SERVER,
                                        data,
                                        pos);
}


// 查询在线设备
unsigned short	CProtocol::SearchOnlineDevice(char* buf)
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_SEARCH_ONLINE_DEVICE,
                                        NULL,
                                        0);
}


// 时间同步
unsigned short	CProtocol::SyncronizeTime(	char*	buf,// 命令缓存区
                                    char*	szDate,		// 日期
                                    char*	szTime)		// 时间
{
    char	data[50]	= {0};
    unsigned short	pos			= 0;
    unsigned char	dateLen		= strlen(szDate);
    unsigned char	timeLen		= strlen(szTime);

    // 日期
    data[pos++]	= dateLen;
    memcpy(&data[pos], szDate, dateLen);
    pos += dateLen;

    // 时间
    data[pos++]	= timeLen;
    memcpy(&data[pos], szTime, timeLen);
    pos += timeLen;

    return CProtocol::ControlCommand(	buf,
                                        CMD_TIME_SYNC,
                                        data,
                                        pos);
}


// 设置别名
unsigned short	CProtocol::SetName( char*	buf,		// 命令缓存区
                            const char*	szName)				// 别名
{
    unsigned char	nameLen			= strlen(szName);
    char	data[50]		= {0};
    unsigned short  pos				= 0;

    data[pos++]	= nameLen;
    memcpy(&data[pos], szName, nameLen);
    pos += nameLen;

    return CProtocol::ControlCommand(	buf,
                                        CMD_DEVICE_NAME,
                                        data,
                                        pos);
}


// 设置音量
unsigned short	CProtocol::SetVolume(	char*	buf,		// 命令缓存区
                                char	volume)		// 音量
{
    char data[2] = {0};
    data[0]	= OPER_SET;	// 设置
    data[1] = volume;	// 音量

    return CProtocol::ControlCommand(	buf,
                                        CMD_DEVICE_VOLUME,
                                        data,
                                        2);
}

// 设置子音量
unsigned short	CProtocol::SetSubVolume(	char*	buf,		// 命令缓存区
                                char     subVolume,                    // 子音量
                                char     auxVolume,                    // 本地音量
                                bool	bSet)    // 是否为设置
{
    char data[3] = {0};
    unsigned short	pos				= 0;
    // 查询
    if (!bSet)
    {
        data[pos++] = (char)OPER_GET;
    }
    else
    {
        data[pos++] = (char)OPER_SET;
        data[pos++] = subVolume;	// 子音量
        data[pos++] = auxVolume;	// 本地音量
    }

    return CProtocol::ControlCommand(	buf,
                                        CMD_DEVICE_SUB_VOLUME,
                                        data,
                                        pos);
}

// 控制设备音量加/减
unsigned short	CProtocol::SetVolumeAddMin(	char*	buf,		// 命令缓存区
                                        bool isUp,              //是否为增加
                                        unsigned char step)    // 步进
{
    char data[2] = {0};
    unsigned short	pos	= 0;
    // 查询
    data[pos++] = isUp?1:0;

    data[pos++] = step;	    // 音量步进

    return CProtocol::ControlCommand(	buf,
                                        CMD_DEVICE_VOLUME_ADD_MIN,
                                        data,
                                        pos);
}


// 回应分控设备设置音量
unsigned short	CProtocol::ResponseSetVolume(	char*	buf)	// 命令缓存区
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_DEVICE_VOLUME,
                                        NULL,
                                        0);
}


// 查询状态
unsigned short	CProtocol::GetStatus(char*	buf)
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_GET_DEVICE_STATUS,
                                        NULL,
                                        0);
}


unsigned short	CProtocol::UpgradeFirmware(char*	buf,	// 命令缓存区
                                    char	model,          // 设备型号
                                    const char*	szVersion,	// 版本号
                                    const char*	szHostIP,	// 服务IP地址
                                    unsigned short	uPort,	// HTTP端口
                                    const char*	szPath,     // 文件路径
                                    const char* md5)		
{
    char	data[MAX_BUF_LEN] = {0};
    unsigned short	pos			= 0;
    unsigned char	versionLen	= strlen(szVersion);
    unsigned char	ipLen		= strlen(szHostIP);
    unsigned char	pathLen		= strlen(szPath);
    unsigned char	md5len		= strlen(md5);

    // 设备型号
    data[pos++] = model;

    // 版本号
    data[pos++] = versionLen;
    memcpy(&data[pos], szVersion, versionLen);
    pos += versionLen;

    // 服务器IP地址
    data[pos++] = ipLen;
    memcpy(&data[pos], szHostIP, ipLen);
    pos += ipLen;

    // 端口，两个字节
    data[pos++] = uPort/256;
    data[pos++] = uPort%256;

    // 文件路径，长度为两个字节
    data[pos++] = pathLen/256;
    data[pos++] = pathLen%256;
    memcpy(&data[pos], szPath, pathLen);
    pos += pathLen;

    data[pos++] = md5len;
    memcpy(&data[pos], md5, md5len);
    pos += md5len;

    return CProtocol::ControlCommand(	buf,
                                        CMD_FIRMWARE_UPGRADE,
                                        data,
                                        pos);
}


// 向设备点播主机上的歌曲
unsigned short	CProtocol::Webcasting(	char*	buf,			// 命令缓存区
                                const char*	url)			// 歌曲URL
{
    char	data[MAX_BUF_LEN]	= {0};
    //char	pos					= {0};
    unsigned short	nLen				= strlen(url);

    // 歌曲URL长度，两个字节
    data[0] = nLen/256;
    data[1] = nLen%256;

    memcpy(&data[2], url, nLen);

    return CProtocol::ControlCommand(	buf,
                                        CMD_WEBCASTING,
                                        data,
                                        nLen+2);
}


// 播放状态设置
unsigned short	CProtocol::SetPlayStatus(char*	buf,		// 命令缓存区
                                         char	status)		// 播放状态值
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_SET_PLAY_STATUS,
                                        &status,
                                        1);
}

// 回应 播放状态设置
unsigned short CProtocol::ResponseSetPlayStatus(char *buf)
{
    return CProtocol::ControlCommand(buf,
                                     CMD_SET_PLAY_STATUS,
                                     NULL,
                                     0);
}

// 静音状态设置
unsigned short	CProtocol::SetMuteStatus(char*	buf,		// 命令缓存区
                                         char	status)		// 静音值
{
    char data[2] = {0};
    data[0]	= OPER_SET;	// 设置
    data[1] = status;	// 静音值

    return CProtocol::ControlCommand(	buf,
                                        CMD_MUTE_STATUS,
                                        data,
                                        2);
}

// 回应分控设备静音状态设置
unsigned short	CProtocol::ResponseSetMuteStatus(char*	buf)	// 命令缓存区
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_MUTE_STATUS,
                                        NULL,
                                        0);
}

// 程控模式设置
unsigned short	CProtocol::SetControlMode(	char*	buf,		// 命令缓存区
                                    char	mode)		// 控制模式
{
    char data[2] = {0};
    data[0]	= OPER_SET;	// 设置
    data[1] = mode;		// 控制模式

    return CProtocol::ControlCommand(	buf,
                                        CMD_CONTROL_MODE,
                                        data,
                                        2);
}

unsigned short	CProtocol::ResponseSetControlMode(	char*	buf)// 命令缓存区
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_CONTROL_MODE,
                                        NULL,
                                        0);
}

// 获取文件信息
unsigned short	CProtocol::GetFileInfo(char*	buf,			// 命令缓存区
                                char	fileType)		// 文件类型
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_GET_FILE_INFO,
                                        &fileType,
                                        1);
}

// 更新文件
unsigned short	CProtocol::UpdateFile(	char*	buf,			// 命令缓存区
                                char	fileType,		// 文件类型
                                const char*	szDateTime,		// 文件更新日期时间
                                const char*	szHostIP,		// 服务IP地址
                                unsigned short	uPort,			// HTTP端口
                                const char*	szPath)			// 文件路径
{
    char	data[MAX_BUF_LEN] = {0};
    unsigned short	pos			= 0;
    unsigned char	dtLen		= strlen(szDateTime);
    unsigned char	ipLen		= strlen(szHostIP);
    unsigned char	pathLen		= strlen(szPath);

    // 文件型号
    data[pos++] = fileType;

    // 更新时间
    data[pos++] = dtLen;
    memcpy(&data[pos], szDateTime, dtLen);
    pos += dtLen;

    // 服务器IP地址
    data[pos++] = ipLen;
    memcpy(&data[pos], szHostIP, ipLen);
    pos += ipLen;

    // 端口，两个字节
    data[pos++] = uPort/256;
    data[pos++] = uPort%256;

    // 文件路径，长度为两个字节
    data[pos++] = pathLen/256;
    data[pos++] = pathLen%256;
    memcpy(&data[pos], szPath, pathLen);
    pos += pathLen;

    //******** 加入一个字段，表示是否支持寻呼台之间对讲
    data[pos++] = APP_IS_SUPPORT_INTERCOM_BETWEEN_PAGER;

    //******** 加多一个字段，用于判断话筒任意用户可以看到管理员创建的歌曲列表（适用于非龙之音云版本）
    data[pos++] = SUPPORT_ALL_ACCOUNT_VIEW_ADMIN_DIR_AND_SONG;

    return CProtocol::ControlCommand(	buf,
                                        CMD_UPDATE_FILE,
                                        data,
                                        pos);
}

unsigned short	CProtocol::UpdateFile(	char*	buf,			// 命令缓存区
                                char	fileType,		// 文件类型
                                const char*	szDateTime,		// 文件更新日期时间
                                const char*	szHostIP,		// 服务IP地址
                                unsigned short	uPort,			// HTTP端口
                                const char*	szPath,			// 文件路径
                                const char*	szServerIP,		// 歌曲服务器IP地址
                                unsigned short	uServerPort)	// 歌曲服务器端口
{
    char	data[MAX_BUF_LEN] = {0};
    unsigned short	pos			= 0;
    unsigned char	dtLen		= strlen(szDateTime);
    unsigned char	ipLen		= strlen(szHostIP);
    unsigned char	pathLen		= strlen(szPath);
    unsigned char	sIpLen		= strlen(szServerIP);

    // 文件型号
    data[pos++] = fileType;

    // 更新时间
    data[pos++] = dtLen;
    memcpy(&data[pos], szDateTime, dtLen);
    pos += dtLen;

    // 服务器IP地址
    data[pos++] = ipLen;
    memcpy(&data[pos], szHostIP, ipLen);
    pos += ipLen;

    // 端口，两个字节
    data[pos++] = uPort/256;
    data[pos++] = uPort%256;

    // 文件路径，长度为两个字节
    data[pos++] = pathLen/256;
    data[pos++] = pathLen%256;
    memcpy(&data[pos], szPath, pathLen);
    pos += pathLen;

    // 歌曲服务器IP地址
    data[pos++] = sIpLen;
    memcpy(&data[pos], szServerIP, sIpLen);
    pos += sIpLen;

    // 歌曲服务器端口，两个字节
    data[pos++] = uServerPort/256;
    data[pos++] = uServerPort%256;

    unsigned short uCmdLen = CProtocol::ControlCommand(buf, CMD_UPDATE_FILE, data, pos);
    buf[3] = 0x01;	// 保留位为0x01

    return uCmdLen;
}

#if 0
// 主动请请求文件信息
unsigned short	CProtocol::RequestGetFileInfo(	char*	buf,                // 命令缓存区
                                                char	fileType,           // 文件类型
                                                const char*	szDateTime,		// 文件更新日期时间
                                                const char*	szHostIP,		// 服务IP地址
                                                unsigned short	uPort,		// HTTP端口
                                                const char*	szPath)         // 文件路径
{
    char	data[MAX_BUF_LEN] = {0};
    unsigned short	pos			= 0;
    unsigned char	dtLen		= strlen(szDateTime);
    unsigned char	ipLen		= strlen(szHostIP);
    unsigned char	pathLen		= strlen(szPath);

    // 文件类型
    data[pos++] = fileType;

    // 更新时间
    data[pos++] = dtLen;
    memcpy(&data[pos], szDateTime, dtLen);
    pos += dtLen;

    // 服务器IP地址
    data[pos++] = ipLen;
    memcpy(&data[pos], szHostIP, ipLen);
    pos += ipLen;

    // 端口，两个字节
    data[pos++] = uPort/256;
    data[pos++] = uPort%256;

    // 文件路径，长度为两个字节
    data[pos++] = pathLen/256;
    data[pos++] = pathLen%256;
    memcpy(&data[pos], szPath, pathLen);
    pos += pathLen;

    return CProtocol::ControlCommand(	buf,
                                        CMD_REQUEST_FILE_INFO,
                                        data,
                                        pos);
}
#endif

// 响应终端同步歌曲文件的请求
unsigned short	CProtocol::ResponseSyncRequest(char*	buf,	// 命令缓存区
                                               char	res)	// 0x01 接受，0x10 拒绝
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_SYNC_SONG_FILE,
                                        &res,
                                        1);
}

// 中止同步歌曲文件
unsigned short	CProtocol::StopSyncSongFile(char*	buf)
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_STOP_SYNC_SONG,
                                        NULL,
                                        0);
}


// 设置钟声
unsigned short	CProtocol::SetRing(char*	buf,				// 命令缓存区
                            const char*	szListID,			// 播放列表ID
                            const char*	szSongName)			// 歌曲名称
{
    char	data[MAX_BUF_LEN]	= {0};
    unsigned short	pos					= 0;
    unsigned char	idLen				= strlen(szListID);
    unsigned char	nameLen				= strlen(szSongName);

    // 播放列表ID
    data[pos++] = idLen;
    memcpy(&data[pos], szListID, idLen);
    pos += idLen;

    // 歌曲名称
    data[pos++] = nameLen;
    memcpy(&data[pos], szSongName, nameLen);
    pos += nameLen;

    return CProtocol::ControlCommand(	buf,
                                        CMD_SET_RING,
                                        data,
                                        pos);
}

// 播放钟声
unsigned short	CProtocol::PlayRing(char*	buf)				// 命令缓存区
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_PLAY_RING,
                                        NULL,
                                        0);
}

// 回应分控设备播放钟声
unsigned short	CProtocol::ResponsePlayRing(char*	buf,		// 命令缓存区
                                    char	result)		// 播放结果
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_SET_RING,
                                        &result,
                                        1);
}

// 播放本地歌曲
unsigned short	CProtocol::PlayLocalSong(	char*	buf,		// 命令缓存区
                                    const char*	szListID,	// 播放列表ID
                                    const char*	szSongName)	// 歌曲名称
{
    char	data[MAX_BUF_LEN]	= {0};
    unsigned short	pos					= 0;
    unsigned char	idLen				= strlen(szListID);
    unsigned char	nameLen				= strlen(szSongName);

    // 播放列表ID
    data[pos++] = idLen;
    memcpy(&data[pos], szListID, idLen);
    pos += idLen;

    // 歌曲名称
    data[pos++] = nameLen;
    memcpy(&data[pos], szSongName, nameLen);
    pos += nameLen;

    return CProtocol::ControlCommand(	buf,
                                        CMD_PLAY_LOCAL_SONG,
                                        data,
                                        pos);
}

// 回应分控设备播放本地歌曲
unsigned short	CProtocol::ResponsePlayLocalSong(	char*	buf,	// 命令缓存区
                                            char	result)	// 播放结果
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_PLAY_LOCAL_SONG,
                                        &result,
                                        1);
}

// 播放模式设置
unsigned short	CProtocol::SetPlayMode(	char*	buf,		// 命令缓存区
                                char	mode)		// 播放模式
{
    char data[2] = {0};
    data[0]	= OPER_SET;	// 设置
    data[1] = mode;		// 播放模式

    return CProtocol::ControlCommand(	buf,
                                        CMD_PLAY_MODE,
                                        data,
                                        2);
}

// 回应分控设备播放模式设置
unsigned short	CProtocol::ResponsePlayMode(char*	buf,		// 命令缓存区
                                               char     mode)		// 播放模式（负数为设置，其它为查询）
{
    char	data[1] = {0};
    bool	isSet = (mode <= 0);

    if (!isSet)
    {
        data[0] = mode;		// 播放模式
    }

    return CProtocol::ControlCommand(	buf,
                                        CMD_PLAY_MODE,
                                        isSet ? NULL : data,
                                        isSet ? 0 : 1);
}

// 切换到空闲状态
unsigned short	CProtocol::SetIdleStatus(	char*	buf)		// 命令缓存区
{
    char    data[7] = {0};
    data[0] = 0x10; //管理员(寻呼台管理员及服务器)
    //memcpy(&data[1], CNetwork::GetHostMac(), 6);
    unsigned char	szMac[10]		= {0};	// 大小不能直接定义为MAC_LEN，否则调用sscanf_s会出错
    sscanf(CNetwork::GetHostMac(), "%x:%x:%x:%x:%x:%x", &szMac[0], &szMac[1], &szMac[2], &szMac[3], &szMac[4], &szMac[5]);
    memcpy(&data[1], szMac, 6);

    return CProtocol::ControlCommand(	buf,
                                        CMD_IDLE_STATUS,
                                        data,
                                        7);
}

// 主机请求终端重新分配MAC地址
unsigned short	CProtocol::ReassignMac(char*	buf)			// 命令缓存区
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_REASSIGN_MAC,
                                        NULL,
                                        0);
}

// 主机向设备请求重启
unsigned short	CProtocol::Reboot(char*	buf)					// 命令缓存区
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_REBOOT,
                                        NULL,
                                        0);
}

// 主机请求终端重置数据
unsigned short	CProtocol::ResetData(char *buf,				// 命令缓存区
                             char	type)				// 数据类型
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_RESET_DATA,
                                        &type,
                                        1);
}

// 查询FLASH信息
unsigned short	CProtocol::GetFlashInfo(char*	buf)
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_GET_FLASH_INFO,
                                        NULL,
                                        0);
}

// 获取设备日期时间
unsigned short	CProtocol::GetDateTime(char*	buf)			// 命令缓存区
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_GET_DATE_TIME,
                                        NULL,
                                        0);
}

// 主机向控制设备发送终端文件信息
unsigned short	CProtocol::SendFileInfo(char*	buf,		// 命令缓存区
                                const char*	szMac,		// 终端MAC地址
                                char	fileType,	// 文件类型
                                const char*	szDateTime) // 文件更新日期时间
{
    char	data[MAX_BUF_LEN] = {0};
    unsigned short	pos			= 0;
    unsigned char	macLen		= 6;
    unsigned char	dtLen		= strlen(szDateTime);

    // 终端MAC地址
    data[pos++] = macLen;
    memcpy(&data[pos], szMac, macLen);
    pos += macLen;

    // 文件型号
    data[pos++] = fileType;

    // 更新时间
    data[pos++] = dtLen;
    memcpy(&data[pos], szDateTime, dtLen);
    pos += dtLen;

    return CProtocol::ControlCommand(	buf,
                                        CMD_SEND_FILE_INFO,
                                        data,
                                        pos);
}


// 4.46 主机向终端设置警报声
unsigned short	CProtocol::SetAlarmSound(	char*	buf,		// 命令缓存区
                                    unsigned char	channelID,	// 通道ID
                                    const char*	szListID,	// 播放列表ID
                                    const char*	szName)		// 警报声名称
{
    char	data[MAX_BUF_LEN]	= {0};
    unsigned short	pos					= 0;
    unsigned char	idLen				= strlen(szListID);
    unsigned char	nameLen				= strlen(szName);

    // 通道ID
    data[pos++] = channelID;

    // 播放列表ID
    data[pos++] = idLen;
    memcpy(&data[pos], szListID, idLen);
    pos += idLen;

    // 警报声名称
    data[pos++] = nameLen;
    memcpy(&data[pos], szName, nameLen);
    pos += nameLen;

    return CProtocol::ControlCommand(	buf,
                                        CMD_SET_ALARM_SOUND,
                                        data,
                                        pos);
}

// 4.47 主机向终端设置开启/关闭警报
unsigned short	CProtocol::SetAlarmSwitch(	char*	buf,		// 命令缓存区
                                    unsigned char	channelID,	// 通道ID
                                    unsigned char	switcher,	// 警报开关
                                    const char*	szListID,	// 播放列表ID
                                    const char*	szName)		// 警报声名称
{
    char	data[MAX_BUF_LEN]	= {0};
    unsigned short	pos					= 0;
    unsigned char	idLen				= strlen(szListID);
    unsigned char	nameLen				= strlen(szName);

    // 通道ID
    data[pos++] = channelID;

    // 开关
    data[pos++] = switcher;

    // 播放列表ID
    data[pos++] = idLen;
    memcpy(&data[pos], szListID, idLen);
    pos += idLen;

    // 警报声名称
    data[pos++] = nameLen;
    memcpy(&data[pos], szName, nameLen);
    pos += nameLen;

    return CProtocol::ControlCommand(	buf,
                                        CMD_SET_ALARM_SWITCH,
                                        data,
                                        pos);
}


// 4.48主机向消防采集器查询通道触发状态
unsigned short	CProtocol::GetAlarmState(char*	buf)			// 命令缓存区
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_GET_ALARM_STATE,
                                        NULL,
                                        0);
}


// 4.49 主机向消防采集器查询/设置触发模式
unsigned short	CProtocol::AlarmMode(	char*	buf,		// 命令缓存区
                                unsigned char	channels,	// 通道数，0xFF为查询
                                int		mode)		// 触发模式
{
    char	data[MAX_BUF_LEN]	= {0};
    unsigned short	pos					= 0;

    if (channels == 0xFF)
    {
        // 查询
        data[pos++] = (char)OPER_GET;
    }
    else
    {
        // 设置
        data[pos++] = (char)OPER_SET;

        // 通道数
        data[pos++] = channels;

        // 触发模式
        data[pos++] = (mode>>24)&0xFF;
        data[pos++] = (mode>>16)&0xFF;
        data[pos++] = (mode>>8)&0xFF;
        data[pos++] = mode&0xFF;
    }

    return CProtocol::ControlCommand(	buf,
                                        CMD_ALARM_TRIGGER_MODE,
                                        data,
                                        pos);
}

// 4.50 主机向音频采集器/终端设置音频采集音源
unsigned short	CProtocol::SetAudioInfo(	char*	buf,		// 命令缓存区
                                    std::shared_ptr<CAudioCollector> pAudioCollector,  // 采集器
                                    unsigned char channel,
                                    bool IsTiming,        //是否定时
                                    unsigned char timingVolume,   //音量
                                    bool IsTrigger,        //是否触发
                                    unsigned char triggerVolume)   //触发音量
{
    char	data[MAX_BUF_LEN] = {0};
    unsigned short	pos	 = 0;
 
    // 端口
    int port=pAudioCollector->GetPort()+channel-1;
    data[pos++] = port/256;
    data[pos++] = port%256;

    // 音源ID
    data[pos++] = pAudioCollector->GetSourceID();

    //音频通道
    data[pos++] = channel;

    // 采样率
    data[pos++] = pAudioCollector->GetSampleRate()/(256*256);
    data[pos++] = pAudioCollector->GetSampleRate()/256%256;
    data[pos++] = pAudioCollector->GetSampleRate()%256;

    // 采样精度
    data[pos++] = pAudioCollector->GetSampleFMT();

    // 声道数
    data[pos++] = pAudioCollector->GetChannel();

    // 音频编解码算法
    data[pos++] = pAudioCollector->GetAlgorithm();

    // 20220720 增加是否定时标志、音量，用于定时采集
    data[pos++] = IsTiming;
    data[pos++] = timingVolume;

    // 20230914 增加是否触发采集标志、音量，用于触发采集
    data[pos++] = IsTrigger;
    data[pos++] = triggerVolume;

    data[pos++] = pAudioCollector->GetPriority();   //优先级

    return CProtocol::ControlCommand(	buf,
                                        CMD_SET_AUDIO_INFO,
                                        data,
                                        pos);
}


// 主机向混音器/终端设置混音音源
unsigned short	CProtocol::SetAudioMixerSourceInfo(	char*	buf,		// 命令缓存区
                                                    const char*   mixerDeviceMac, //混音器MAC
                                                    unsigned char eventType,       //事件类型（1为启动，0为停止）
                                                    unsigned char   mixerPriority,     //混音器优先级
                                                    unsigned char   mixerVolume,       //混音器音量
                                                    unsigned int    sampleRate,        //采样率
                                                    unsigned char   fmt,               //采样精度
                                                    unsigned char   channelNum,        //声道数
                                                    char    *broadcast_addr,           //组播地址
                                                    unsigned int     broadcast_port,   //组播端口
                                                    unsigned char    audioCodecs       //音频编码
                                                  )
{
    unsigned char	szMac[10]		= {0};	// 大小不能直接定义为MAC_LEN，否则调用sscanf_s会出错
    char        data[MAX_BUF_LEN]	= {0};
    unsigned short	pos				= 0;

    sscanf(mixerDeviceMac, "%x:%x:%x:%x:%x:%x", &szMac[0], &szMac[1], &szMac[2], &szMac[3], &szMac[4], &szMac[5]);

    //混音器MAC地址
    memcpy(&data[pos], szMac, MAC_SIZE);
    pos += MAC_SIZE;

    //事件类型
    data[pos++] = eventType;

    //混音器优先级
    data[pos++] = mixerPriority;
    
    //混音器音量
    data[pos++] = mixerVolume;

    // 采样率
    data[pos++] = sampleRate/(256*256);
    data[pos++] = sampleRate/256%256;
    data[pos++] = sampleRate%256;

    // 采样精度
    data[pos++] = fmt;

    // 声道数
    data[pos++] = channelNum;

    // 组播地址长度
    unsigned char broadcast_addrLen = strlen(broadcast_addr);
    data[pos++] = broadcast_addrLen;
    // 组播地址
    memcpy(&data[pos], broadcast_addr, broadcast_addrLen);
    pos += broadcast_addrLen;
    // 端口
    data[pos++] = broadcast_port/256;
    data[pos++] = broadcast_port%256;

    // 音频编解码算法
    data[pos++] = audioCodecs;

    return CProtocol::ControlCommand(	buf,
                                        CMD_SET_AUDIO_MIXER_SOURCE,
                                        data,
                                        pos);
}


    // 主机向电话网关/终端设置电话网关音源
unsigned short	CProtocol::SetPhoneGatewaySourceInfo(	char*	buf,		// 命令缓存区
                                                const char*   phoneGatewayDevice, //电话网关MAC
                                                unsigned char eventType,       //事件类型（1为启动，0为停止）
                                                unsigned char   mixerVolume,       //电话网关音量
                                                unsigned int    sampleRate,        //采样率
                                                unsigned char   fmt,               //采样精度
                                                unsigned char   channelNum,        //声道数
                                                char    *broadcast_addr,           //组播地址
                                                unsigned int     broadcast_port,   //组播端口
                                                unsigned char    audioCodecs      //音频编码
                                                )
{
    unsigned char	szMac[10]		= {0};	// 大小不能直接定义为MAC_LEN，否则调用sscanf_s会出错
    char        data[MAX_BUF_LEN]	= {0};
    unsigned short	pos				= 0;

    sscanf(phoneGatewayDevice, "%x:%x:%x:%x:%x:%x", &szMac[0], &szMac[1], &szMac[2], &szMac[3], &szMac[4], &szMac[5]);

    //电话网关MAC地址
    memcpy(&data[pos], szMac, MAC_SIZE);
    pos += MAC_SIZE;

    //事件类型
    data[pos++] = eventType;
    
    //电话网关音量
    data[pos++] = mixerVolume;

    // 采样率
    data[pos++] = sampleRate/(256*256);
    data[pos++] = sampleRate/256%256;
    data[pos++] = sampleRate%256;

    // 采样精度
    data[pos++] = fmt;

    // 声道数
    data[pos++] = channelNum;

    // 组播地址长度
    unsigned char broadcast_addrLen = strlen(broadcast_addr);
    data[pos++] = broadcast_addrLen;
    // 组播地址
    memcpy(&data[pos], broadcast_addr, broadcast_addrLen);
    pos += broadcast_addrLen;
    // 端口
    data[pos++] = broadcast_port/256;
    data[pos++] = broadcast_port%256;

    // 音频编解码算法
    data[pos++] = audioCodecs;

    return CProtocol::ControlCommand(	buf,
                                        CMD_SET_PHONE_GATEWAY_SOURCE,
                                        data,
                                        pos);
}


// 主机向终端设置网络电台音源
unsigned short	CProtocol::SetNetRadioSourceInfo(char*	buf,		// 命令缓存区
                                                const char *radioName,        //电台名称
                                                char *sessionId,        //电台的sessionId
                                                unsigned int    sampleRate,        //采样率
                                                unsigned char   fmt,               //采样精度
                                                unsigned char   channelNum,        //声道数
                                                char    *broadcast_addr,           //组播地址
                                                unsigned int     broadcast_port,   //组播端口
                                                bool IsTiming,                     //是否定时
                                                unsigned char volume)       //音量
{
    unsigned char	szMac[10]		= {0};	// 大小不能直接定义为MAC_LEN，否则调用sscanf_s会出错
    char        data[MAX_BUF_LEN]	= {0};
    unsigned short	pos				= 0;

    //电台名称大小
    unsigned char radioNameLen = strlen(radioName);
    data[pos++] = radioNameLen;
    //电台名称
    memcpy(&data[pos], radioName, radioNameLen);
    pos += radioNameLen;

    //电台sessionId大小
    unsigned char sessionIdLen = strlen(sessionId);
    data[pos++] = sessionIdLen;
    //电台sessionId
    memcpy(&data[pos], sessionId, sessionIdLen);
    pos += sessionIdLen;

    // 采样率
    data[pos++] = sampleRate/(256*256);
    data[pos++] = sampleRate/256%256;
    data[pos++] = sampleRate%256;

    // 采样精度
    data[pos++] = fmt;

    // 声道数
    data[pos++] = channelNum;

    // 组播地址长度
    unsigned char broadcast_addrLen = strlen(broadcast_addr);
    data[pos++] = broadcast_addrLen;
    // 组播地址
    memcpy(&data[pos], broadcast_addr, broadcast_addrLen);
    pos += broadcast_addrLen;
    // 端口
    data[pos++] = broadcast_port/256;
    data[pos++] = broadcast_port%256;

    //是否定时标志、音量
    data[pos++] = IsTiming;
    data[pos++] = volume;

    return CProtocol::ControlCommand(	buf,
                                        CMD_SET_NET_RADIO_SOURCE,
                                        data,
                                        pos);
}


unsigned short	CProtocol::ResponseSetAudioInfo(char*	buf,	// 命令缓存区
                                        char	result)	// 结果
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_SET_AUDIO_INFO,
                                        &result,
                                        1);
}


// 主机向电源时序器查询参数
unsigned short	CProtocol::SequencePowerInfo(char*	buf,
                                          std::shared_ptr<CSequencePower> pSequencePwrInfo, // 电源时序器信息
                                          bool	bSet    // 是否为设置
                                          )			    // 命令缓存区
{
    unsigned char	szMac[10]		= {0};	// 大小不能直接定义为MAC_LEN，否则调用sscanf_s会出错
    char        data[MAX_BUF_LEN]	= {0};
    unsigned short	pos				= 0;
    // 查询
    if (!bSet)
    {
        data[pos++] = (char)OPER_GET;
    }
    else
    {
        data[pos++] = (char)OPER_SET;
        data[pos++] = pSequencePwrInfo->GetControlMode();
        data[pos++] = SEQUENCE_POWER_DEFAULT_DELAY/256; //延时高位
        data[pos++] = SEQUENCE_POWER_DEFAULT_DELAY%256; //延时低位
        data[pos++] = pSequencePwrInfo->GetRealChannelCnt();
        data[pos++] = pSequencePwrInfo->GetAllSwitchState()/256;
        data[pos++] = pSequencePwrInfo->GetAllSwitchState()%256;
    }
    return CProtocol::ControlCommand(	buf,
                                        CMD_SEQUENCE_POWER_INFO,
                                        data,
                                        pos );
}



// 主机向电源时序器发送定时点信息
unsigned short	CProtocol::SequencePowerTimingInfo(char*	buf,
                                          std::shared_ptr<CSequencePower> pSequencePwrInfo, // 电源时序器信息
                                          const char *deviceMac, // mac地址
                                          bool IsQuickResponse
                                          )	
{
    unsigned char	szMac[10]		= {0};	// 大小不能直接定义为MAC_LEN，否则调用sscanf_s会出错
    char        data[MAX_BUF_LEN]	= {0};
    unsigned short	pos				= 0;

    sscanf(deviceMac, "%x:%x:%x:%x:%x:%x", &szMac[0], &szMac[1], &szMac[2], &szMac[3], &szMac[4], &szMac[5]);

    // MAC地址
    memcpy(&data[pos], szMac, MAC_SIZE);
    pos += MAC_SIZE;

    unsigned short channelIsExistTimer=0;
    unsigned short channelIsInTiming=0;

    int realChannelCnt=pSequencePwrInfo->GetRealChannelCnt();
    data[pos++] = realChannelCnt;
    for(int i=0;i<realChannelCnt;i++)
    {
        if(pSequencePwrInfo->GetChannel(i).GetIsExistTimer())
        {
            channelIsExistTimer += pow(2,i);
        }
        if(pSequencePwrInfo->GetChannel(i).GetIsInTiming())
        {
            channelIsInTiming += pow(2,i);
        }
    }
    data[pos++] = channelIsExistTimer/256; //延时高位
    data[pos++] = channelIsExistTimer%256; //延时低位
    data[pos++] = channelIsInTiming/256;
    data[pos++] = channelIsInTiming%256;

    data[pos++] = IsQuickResponse;
    
    return CProtocol::ControlCommand(	buf,
                                        CMD_SEQUENCE_POWER_TIMING,
                                        data,
                                        pos );
}



// 4.51 主机向终端设置网络模式
unsigned short	CProtocol::SetNetworkMode(	char*	buf,		// 命令缓存区
                                    bool	bSet,                   // 是否为设置
                                    unsigned char	mode,		// 网络模式
                                    const char*	szServerIP,	// 服务器IP
                                    unsigned short	nServerPort,// 服务器端口
                                    const char*	szServerIP2,      // 备用服务器IP
                                    unsigned short	nServerPort2) // 备用服务器端口
{
    char	data[MAX_BUF_LEN] = {0};
    unsigned short	pos	 = 0;

    // 查询
    if (!bSet)
    {
        data[pos++] = (char)OPER_GET;
    }
    else
    {
        data[pos++] = (char)OPER_SET;
        
         // 网络模式
        data[pos++] = mode;

        // 服务器IP
        unsigned char ipLen = strlen(szServerIP);
        data[pos++] = ipLen;
        memcpy(&data[pos], szServerIP, ipLen);
        pos += ipLen;

        // 端口
        data[pos++] = nServerPort/256;
        data[pos++] = nServerPort%256;

        // 备用服务器IP
        ipLen = strlen(szServerIP2);
        data[pos++] = ipLen;
        memcpy(&data[pos], szServerIP2, ipLen);
        pos += ipLen;

        // 备用服务器端口
        data[pos++] = nServerPort2/256;
        data[pos++] = nServerPort2%256;
    }

    return CProtocol::ControlCommand(	buf,
                                        CMD_NETWORK_MODE,
                                        data,
                                        pos);
}



// 4.52 主机向终端查询/设置IP属性
unsigned short	CProtocol::NetworkInfo( char*	buf,		// 命令缓存区
                                                                       CNetworkInfo *pNetInfo, // 网络信息
                                                                       bool	bSet)		// 是否为设置
{
    unsigned char	szMac[10]		= {0};	// 大小不能直接定义为MAC_LEN，否则调用sscanf_s会出错
    char        data[MAX_BUF_LEN]	= {0};
    unsigned short	pos				= 0;


    sscanf(pNetInfo->m_szMac, "%x:%x:%x:%x:%x:%x", &szMac[0], &szMac[1], &szMac[2], &szMac[3], &szMac[4], &szMac[5]);

    // MAC地址
    memcpy(&data[pos], szMac, MAC_SIZE);
    pos += MAC_SIZE;

    // 查询
    if (!bSet)
    {
        data[pos++] = (char)OPER_GET;

        return CProtocol::ControlCommand(	buf,
                                            CMD_IP_INFO,
                                            data,
                                            pos);
    }
    // 设置
    else
    {
        data[pos++] = OPER_SET;
        data[pos++] = pNetInfo->m_IpAccessMode;

        // 静态分配IP
        if (pNetInfo->m_IpAccessMode == IP_ACCESS_MODE_STATIC)
        {
            // IP地址
            LOG(pNetInfo->m_szIP, LV_INFO);
            unsigned char ipLen = strlen(pNetInfo->m_szIP);
            data[pos++] = ipLen;
            memcpy(&data[pos], pNetInfo->m_szIP, ipLen);
            pos += ipLen;

            // 子网掩码
            unsigned char subnetLen = strlen(pNetInfo->m_szSubnetMask);
            data[pos++] = subnetLen;
            memcpy(&data[pos], pNetInfo->m_szSubnetMask, subnetLen);
            pos += subnetLen;

            // 网关
            unsigned char gatewayLen = strlen(pNetInfo->m_szGateway);
            data[pos++] = gatewayLen;
            memcpy(&data[pos], pNetInfo->m_szGateway, gatewayLen);
            pos += gatewayLen;

            // DNS1
            unsigned char dns1Len = strlen(pNetInfo->m_szDNS1);
            data[pos++] = dns1Len;
            memcpy(&data[pos], pNetInfo->m_szDNS1, dns1Len);
            pos += dns1Len;

            // DNS2
            unsigned char dns2Len = strlen(pNetInfo->m_szDNS2);
            data[pos++] = dns2Len;
            memcpy(&data[pos], pNetInfo->m_szDNS2, dns2Len);
            pos += dns2Len;
        }

        return CProtocol::ControlCommand(	buf,
                                            CMD_IP_INFO,
                                            data,
                                            pos);
    }
}

// 4.53 主机向设备获取记录文件列表
unsigned short	CProtocol::GetLogFileList(char*	buf)		// 命令缓存区
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_GET_LOG_FILE_LIST,
                                        NULL,
                                        0);
}

// 4.54 主机向设备获取记录文件内容
unsigned short	CProtocol::GetLogFileData(	char*	buf,		// 命令缓存区
                                    const char*	szFileName,	// 文件名称
                                    unsigned short	packID)		// 包ID
{
    char	data[MAX_BUF_LEN] = {0};
    unsigned short	pos			= 0;
    unsigned char	nameLen		= strlen(szFileName);

    // 文件名称
    data[pos++] = nameLen;
    memcpy(&data[pos], szFileName, nameLen);
    pos += nameLen;

    // 包ID
    data[pos++] = packID/256;
    data[pos++] = packID%256;

    return CProtocol::ControlCommand(	buf,
                                        CMD_GET_LOG_FILE_DATA,
                                        data,
                                        pos);
}

// 4.55 主机向采集器主动发送终端音源选择状态
unsigned short	CProtocol::SendAudioState(	char*	buf,		// 命令缓存区
                                    bool	bUsed,unsigned char channelSelect)		// 是否被选择
{
    char data[2] = {0};
    data[0] = bUsed ? 1 : 0;
    data[1] = channelSelect;
    return CProtocol::ControlCommand(	buf,
                                        CMD_SEND_AUDIO_STATE,
                                        data,
                                        2);
}

// 4.56 主机向其他控制设备下发音频采集器设备列表
unsigned short	CProtocol::SendAudioList(	char*	buf,		// 命令缓存区
                                    char*	data,		// 音源列表数据
                                    unsigned short	len)		// 数据长度
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_SEND_AUDIO_LIST,
                                        data,
                                        len);
}

// 4.58 主机向网络解码播放器查询/设置电源输出模式
unsigned short	CProtocol::PowerOutputMode(	char*	buf,		// 命令缓存区
                                    unsigned char	mode,		// 电源输出模式，为0时，是查询
                                    unsigned short	timteout)	// 超时时间
{
    char data[4] = {0};
    data[0]	= (mode == 0 ? OPER_GET : OPER_SET);
    data[1] = mode;		// 电源输出模式
    data[2] = timteout/256;
    data[3] = timteout%256;

    return CProtocol::ControlCommand(	buf,
                                        CMD_POWER_OUTPUT_MODE,
                                        data,
                                        mode == 0 ? 1 : 4);
}

// 4.59 主机向网络解码播放器查询回路检测状态
unsigned short	CProtocol::SigalDetection(char*	buf)		// 命令缓存区
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_SIGAL_DETECTION,
                                        NULL,
                                        0);
}

// 4.61 主机向终端查询/设置EQ音效
unsigned short	CProtocol::DeviceEq(char*	buf,			// 命令缓存区
                            const char* szMac,               // MAC
                            char	mode,			        // EQ模式，为-1时，是查询
                            unsigned char gain[10])			// 增益值
{
    char data[128] = {0};
    int pos=0;
        // MAC地址
    unsigned char	Mac[10]		= {0};	// 大小不能直接定义为MAC_LEN，否则调用sscanf_s会出错
    sscanf(szMac, "%x:%x:%x:%x:%x:%x", &Mac[0], &Mac[1], &Mac[2], &Mac[3], &Mac[4], &Mac[5]);
    memcpy(&data[pos], Mac, 6);
    pos += 6;
    data[pos++]	= (mode == -1 ? OPER_GET : OPER_SET);
    if(mode!=-1)
    {
        data[pos++] = mode;			    // EQ模式
        for(int i=0;i<10;i++)
        {
            data[pos++] = gain[i];		// 增益
        }
    }
    return CProtocol::ControlCommand(	buf,
                                        CMD_EQ_MODE,
                                        data,
                                        mode == -1 ? 7 : (mode == EQ_CUTEOM ? pos : 8));
}


// 4.61 主机向终端查询/设置蓝牙参数
unsigned short	CProtocol::BlueToothInfo(char*	buf,			    // 命令缓存区
                            const char* szMac,                      // MAC
                            const char*	btName,					        // 蓝牙名称,为NULL时为查询
                            unsigned char btencryption,             // 蓝牙加密方式
                            const char*   btPin)			                // 蓝牙密码    
{
    char data[128] = {0};
    int pos=0;
    unsigned char	Mac[10]		= {0};	// 大小不能直接定义为MAC_LEN，否则调用sscanf_s会出错
    sscanf(szMac, "%x:%x:%x:%x:%x:%x", &Mac[0], &Mac[1], &Mac[2], &Mac[3], &Mac[4], &Mac[5]);
    memcpy(&data[pos], Mac, 6);
    pos += 6;
    data[pos++]	=  btName != NULL?OPER_SET:OPER_GET ;
    if(btName != NULL) //设置
    {
        data[pos++]	=  strlen(btName);
        memcpy(data+pos,btName,strlen(btName));
        pos+=strlen(btName);
        data[pos++]=btencryption;
        memcpy(data+pos,btPin,4);
        pos+=4;
    }
    return CProtocol::ControlCommand(	buf,
                                        CMD_BLUETOOTH_INFO,
                                        data,
                                        pos);
}



// 4.61 主机向终端查询/设置对讲基础配置
unsigned short	CProtocol::CallBasicConfig(char*	buf,			    // 命令缓存区
                                            CALLDEVICECONFIG *callBasicConfig)
{
    char data[128] = {0};
    int pos=0;
    data[pos++]	= callBasicConfig != NULL?OPER_SET:OPER_GET;
    if(callBasicConfig != NULL) //设置
    {
        int parmId=1;
        int parmLength=12;
        data[pos++]	= parmId;
        data[pos++]	= parmLength;
        
        unsigned char	szKey1Mac[10]		= {0};	// 大小不能直接定义为MAC_LEN，否则调用sscanf_s会出错
        unsigned char	szKey2Mac[10]		= {0};	// 大小不能直接定义为MAC_LEN，否则调用sscanf_s会出错
        sscanf(callBasicConfig->Key1_mac, "%x:%x:%x:%x:%x:%x", &szKey1Mac[0], &szKey1Mac[1], &szKey1Mac[2], &szKey1Mac[3], &szKey1Mac[4], &szKey1Mac[5]);
        sscanf(callBasicConfig->Key2_mac, "%x:%x:%x:%x:%x:%x", &szKey2Mac[0], &szKey2Mac[1], &szKey2Mac[2], &szKey2Mac[3], &szKey2Mac[4], &szKey2Mac[5]);

        memcpy(data+pos,szKey1Mac,6);
        pos+=6;
        memcpy(data+pos,szKey2Mac,6);
        pos+=6;


        parmId=2;
        parmLength=3;
        data[pos++]	= parmId;
        data[pos++]	= parmLength;

        data[pos++] = callBasicConfig->AutoAnswerTime;
        data[pos++] = callBasicConfig->micVol;
        data[pos++] = callBasicConfig->farOutVol;
        
    }
    return CProtocol::ControlCommand(	buf,
                                        CMD_INTERCOM_BASIC_CONFIG,
                                        data,
                                        pos);
}


// 4.61 主机向终端查询/设置触发参数
unsigned short	CProtocol::TriggerConfig(char*	buf,			    // 命令缓存区
                                        char trigger_switch,
                                        char trigger_mode,
                                        const char *trigger_song_name,
                                        const char *trigger_song_md5,
                                        int trigger_playTimes,
                                        int trigger_volume)
{
    char data[256] = {0};
    int data_len=0;
    data[data_len++]	= trigger_song_name != NULL?OPER_SET:OPER_GET;
    if(trigger_song_name != NULL) //设置
    {
        data[data_len++] = 1;	//参数id
        int parm1_pos=data_len++;
        int parm1_length=0;
        data[data_len++] = trigger_switch;	//触发开关
        parm1_length++;
        data[data_len++] = trigger_mode;		//触发模式
        parm1_length++;
        data[parm1_pos] = parm1_length;	//参数1长度
        
        data[data_len++] = 2;	//参数id
        int parm2_pos=data_len++;
        int parm2_length=0;
        int triggerSongName_len = trigger_song_name == NULL ? 0:strlen(trigger_song_name);
        data[data_len++] = triggerSongName_len;	//触发歌曲名称长度
        parm2_length++;
        memcpy(data+data_len,trigger_song_name,triggerSongName_len);
        data_len+=triggerSongName_len;
        parm2_length+=triggerSongName_len;
        int triggerSongMd5_len = trigger_song_md5 == NULL ? 0:strlen(trigger_song_md5);
        data[data_len++] = triggerSongMd5_len;	//触发歌曲MD5长度
        parm2_length++;
        memcpy(data+data_len,trigger_song_md5,triggerSongMd5_len);
        data_len+=triggerSongMd5_len;
        parm2_length+=triggerSongMd5_len;
        data[data_len++] = trigger_playTimes == 0?1:trigger_playTimes;		//触发播放次数(默认1)
        parm2_length++;
        data[data_len++] = trigger_volume;		//触发音量
        parm2_length++;
        data[parm2_pos] = parm2_length;		//参数2长度
    }
    return CProtocol::ControlCommand(	buf,
                                        CMD_SET_TRIGGER_CONFIG,
                                        data,
                                        data_len);
}


#if SUPPORT_AUDIO_MIXER
// 查询/设置音频混音器参数
unsigned short	CProtocol::AudioMixerConfig(char*	buf,			// 命令缓存区
                                        bool    bSet,               // 0:查询 1:设置
                                        int nMasterSwitch,          // 混音主开关（0关闭 1开启）
                                        int nPriority,              // 优先级
                                        int nTriggerType,           // 触发类型
                                        int nTriggerSensitivity,    // 触发灵敏度
                                        int nVolumeFadeLevel,       // 音量淡化级别
                                        int nZoneVolume)            // 分区音量
{
    char data[256] = {0};
    int data_len=0;
    data[data_len++] = bSet ? OPER_SET:OPER_GET;
    if(bSet)
    {
        data[data_len++] = nMasterSwitch;
        data[data_len++] = nPriority;
        data[data_len++] = nTriggerType;
        data[data_len++] = nTriggerSensitivity;
        data[data_len++] = nVolumeFadeLevel;
        data[data_len++] = nZoneVolume;
    }
    return CProtocol::ControlCommand(	buf,
                                        CMD_AUDIO_MIXER_CONFIG,
                                        data,
                                        data_len);
}

#endif


// 查询/设置信息发布参数
unsigned short	CProtocol::InformationPublish(char*	buf,		    // 命令缓存区
                                        bool	bSet,               // 是否为设置
                                        bool bEnableDisplay,           // 是否启用显示
                                        string strText,                // 输出文本
                                        int nEffects,                  // 特效
                                        int nMoveSpeed,                // 移动速度
                                        int nStayTime)                 // 停留时间
{
    char data[1024] = {0};
    int data_len=0;
    data[data_len++] = bSet ? OPER_SET:OPER_GET;
    if(bSet)
    {
        data[data_len++] = bEnableDisplay;
        int textLen=strText.size();
        data[data_len++] = textLen;
        memcpy(data+data_len,strText.data(),textLen);
        data_len+=textLen;
        data[data_len++] = nEffects;
        data[data_len++] = nMoveSpeed;
        data[data_len++] = nStayTime;

        //printf("strText=%s,len=%d\n",strText,textLen);
    }
    return CProtocol::ControlCommand(	buf,
                                        CMD_INFORMATION_PUBLISH_CONFIG,
                                        data,
                                        data_len);
}


#if SUPPORT_PHONE_GATEWAY
// 查询/设置电话网关参数
unsigned short	CProtocol::PhoneGatewayConfig(char*	buf,		    // 命令缓存区
                                        bool    bSet,               // 0:查询 1:设置
                                        int nMasterSwitch,          // 电话网关主开关（0关闭 1开启）
                                        int nZoneVolume,            // 分区音量
                                        string telWhitelist )       // 电话白名单
{
    char data[1024] = {0};
    int data_len=0;
    data[data_len++] = bSet ? OPER_SET:OPER_GET;
    if(bSet)
    {
        data[data_len++] = nMasterSwitch;
        data[data_len++] = nZoneVolume;
        data[data_len++] = telWhitelist.length();
        memcpy(data+data_len,telWhitelist.data(),telWhitelist.length());
        data_len+=telWhitelist.length();
    }
    return CProtocol::ControlCommand(	buf,
                                        CMD_PHONE_GATEWAY_CONFIG,
                                        data,
                                        data_len);
}
#endif

#if SUPPORT_AMP_CONTROLER
unsigned short	CProtocol::AmpControlerConfig(char*	buf,		    // 命令缓存区
                                        bool    bSet               // 0:查询 1:设置
                                         )
{
    char data[1024] = {0};
    int data_len=0;
    data[data_len++] = bSet ? OPER_SET:OPER_GET;
    return CProtocol::ControlCommand(	buf,
                                        CMD_AMP_CONTROLER_STATUS,
                                        data,
                                        data_len);
}
#endif


#if SUPPORT_NOISE_DETECTOR
unsigned short	CProtocol::NoiseDetectorConfig(char*	buf,		    // 命令缓存区
                                        bool    bSet,               // 0:查询 1:设置
                                        CNoiseDetector &noiseDetector
                                         )
{
    char data[1024] = {0};
    int data_len=0;
    data[data_len++] = bSet ? OPER_SET:OPER_GET;

    if(bSet)
    {
        data[data_len++] = noiseDetector.isEnable;
        for(int i=0;i<NOISE_NUM_SEGMENTS;i++)
        {
            data[data_len++] = noiseDetector.segmentVol[i];
        }
    }

    return CProtocol::ControlCommand(	buf,
                                        CMD_NOISE_DETECTOR_CONFIG,
                                        data,
                                        data_len);
}
#endif


// 查询/设置音频采集器参数
unsigned short	CProtocol::AudioCollectorConfig(char*	buf,			// 命令缓存区
                                        bool    bSet,               // 0:查询 1:设置
                                            int nTriggerSwitch,          // 触发开关（0关闭 1开启）
                                            int nTriggerChannelId,       // 触发通道ID
                                            int nTriggerZoneVolume)      // 触发分区音量
{
    char data[256] = {0};
    int data_len=0;
    data[data_len++] = bSet ? OPER_SET:OPER_GET;
    if(bSet)
    {
        data[data_len++] = nTriggerSwitch;
        data[data_len++] = nTriggerChannelId;
        data[data_len++] = nTriggerZoneVolume;
    }
    return CProtocol::ControlCommand(	buf,
                                        CMD_AUDIO_COLLECTOR_CONFIG,
                                        data,
                                        data_len);
}




// 寻呼台获取账户存储容量
unsigned short	CProtocol::AccountStorageCapacity(char*	buf,			    // 命令缓存区
                                        const char*	szAccount,		    // 账号,
                                        UINT64 storage_capacity,             // 总存储容量
                                        UINT64 storage_used,                 // 已用存储空间
                                        UINT64 storage_remaining,            //剩余存储空间
                                        int compress_bitrate             //压缩比特率(kbps)
                                        )
{
    char data[512] = {0};
    int data_len=0;

    int accountLen=strlen(szAccount);
    data[data_len++]	= accountLen;
    memcpy(data+data_len,szAccount,accountLen);	//账户名
    data_len+=accountLen;
    
    //总存储空间
    data[data_len++] = (storage_capacity>>56)&0xFF;
    data[data_len++] = (storage_capacity>>48)&0xFF;
    data[data_len++] = (storage_capacity>>40)&0xFF;
    data[data_len++] = (storage_capacity>>32)&0xFF;
    data[data_len++] = (storage_capacity>>24)&0xFF;
    data[data_len++] = (storage_capacity>>16)&0xFF;
    data[data_len++] = (storage_capacity>>8)&0xFF;
    data[data_len++] = storage_capacity&0xFF;

    //已用存储空间
    data[data_len++] = (storage_used>>56)&0xFF;
    data[data_len++] = (storage_used>>48)&0xFF;
    data[data_len++] = (storage_used>>40)&0xFF;
    data[data_len++] = (storage_used>>32)&0xFF;
    data[data_len++] = (storage_used>>24)&0xFF;
    data[data_len++] = (storage_used>>16)&0xFF;
    data[data_len++] = (storage_used>>8)&0xFF;
    data[data_len++] = storage_used&0xFF;

    //剩余存储空间
    data[data_len++] = (storage_remaining>>56)&0xFF;
    data[data_len++] = (storage_remaining>>48)&0xFF;
    data[data_len++] = (storage_remaining>>40)&0xFF;
    data[data_len++] = (storage_remaining>>32)&0xFF;
    data[data_len++] = (storage_remaining>>24)&0xFF;
    data[data_len++] = (storage_remaining>>16)&0xFF;
    data[data_len++] = (storage_remaining>>8)&0xFF;
    data[data_len++] = storage_remaining&0xFF;

    //压缩比特率
    data[data_len++] = (compress_bitrate>>8)&0xFF;
    data[data_len++] = compress_bitrate&0xFF;
    
    return CProtocol::ControlCommand(	buf,
                                        CMD_GET_ACCOUNT_STORAGE_CAPACITY,
                                        data,
                                        data_len);
}


unsigned short	CProtocol::RequestUploadSong(char*	buf,	        // 命令缓存区
                                        int result,                 //返回值
                                        const char*	szHostIP,	    // 服务IP地址
                                        unsigned short	uPort,	    // HTTP端口
                                        const char* uploadUrl       //上传地址（cgi）
                                        )
{
    char data[512] = {0};
    int data_len=0;

    data[data_len++]	= result;

    unsigned char	ipLen   = strlen(szHostIP);
    // 服务器IP地址
    data[data_len++] = ipLen;
    memcpy(&data[data_len], szHostIP, ipLen);
    data_len += ipLen;

    // 端口，两个字节
    data[data_len++] = uPort/256;
    data[data_len++] = uPort%256;

    int uploadUrlLen=strlen(uploadUrl);
    data[data_len++]	= uploadUrlLen;
    memcpy(data+data_len,uploadUrl,uploadUrlLen);	//账户名
    data_len+=uploadUrlLen;
    
    return CProtocol::ControlCommand(	buf,
                                        CMD_REQUEST_UPLOAD_SONG_FILE,
                                        data,
                                        data_len);
}

unsigned short	CProtocol::UploadSongStatus(char*	buf,	        // 命令缓存区
                                        int result                 //返回值
                                        )
{
    char data[512] = {0};
    int data_len=0;

    data[data_len++]	= result;

    return CProtocol::ControlCommand(	buf,
                                        CMD_NOTIFY_UPLOAD_STATUS,
                                        data,
                                        data_len);
}

unsigned short	CProtocol::RequestDeleteSong(char*	buf,	        // 命令缓存区
                                        int result                 //返回值
                                        )
{
    char data[512] = {0};
    int data_len=0;

    data[data_len++]	= result;

    return CProtocol::ControlCommand(	buf,
                                        CMD_REQUEST_DELETE_SONG,
                                        data,
                                        data_len);
}


// 控制寻呼台发起广播寻呼
unsigned short	CProtocol::SetBroadcastPaging(char*	buf,			    // 命令缓存区
                        const char* szMac,                      // MAC
                        const char*	szAccount,						// 用户名
                        int control_event,                          //控制类别
                        int isAllZone,                              //该用户的全部分区设备
                        vector<string> &zoneMacs)                    //分区设备集合  
{
    char data[128] = {0};
    int pos=0;
    unsigned char	Mac[10]		= {0};	// 大小不能直接定义为MAC_LEN，否则调用sscanf_s会出错
    sscanf(szMac, "%x:%x:%x:%x:%x:%x", &Mac[0], &Mac[1], &Mac[2], &Mac[3], &Mac[4], &Mac[5]);
    memcpy(&data[pos], Mac, 6);
    pos += 6;
    int accountLen=strlen(szAccount);
    data[pos++] = accountLen;    //用户名长度
    memcpy(data+pos,szAccount,accountLen);  //用户名
    pos+=accountLen;
    data[pos++] = control_event;    //控制类别
    data[pos++] = isAllZone;        //是否全部分区
    printf("SetBroadcastPaging:control_event=%d,isAllZone=%d,zoneSize=%d",control_event,isAllZone,zoneMacs.size());
    if(!isAllZone)
    {
        data[pos++] = zoneMacs.size();
        unsigned char zoneMac_array[200*6]={0};
        int zoneMacPos=0;
        for(int i=0;i<zoneMacs.size();i++)
        {
            sscanf(zoneMacs[i].data(), "%x:%x:%x:%x:%x:%x", zoneMac_array+zoneMacPos, zoneMac_array+zoneMacPos+1, zoneMac_array+zoneMacPos+2, zoneMac_array+zoneMacPos+3, zoneMac_array+zoneMacPos+4, zoneMac_array+zoneMacPos+5);
            zoneMacPos+=6;
        }
        memcpy(data+pos,zoneMac_array,zoneMacPos);
        pos+=zoneMacPos;
    }
    return CProtocol::ControlCommand(	buf,
                                        CMD_SET_BROADCAST_PAGING,
                                        data,
                                        pos);
}


// 4.62 消防采集器通道触发状态改变时，主动通知主机
unsigned short	CProtocol::ResponseAlarmStateChanged(char*	buf)
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_ALARM_STATE_CHANGED,
                                        NULL,
                                        0);
}

// 4.66主机向终端查询/设置混音模式
unsigned short	CProtocol::MixingMode(	char*	buf,			// 命令缓存区
                                char	channel,		// channel，为-1时，是查询，0双声道 ，1为单声道
                                unsigned char	auxVol,			// 线路音量增益
                                unsigned char	mixing,			// 0：混音关，1：DAC双声道混合输出，2：AUX混合DAC输出
                                unsigned char	dacVol)			// 数字音量增益，AUX混合DAC输出时有效
{
    char data[5] = {0};
    data[0]	= (channel < 0 ? OPER_GET : OPER_SET);
    SHORT nDataLen = 1;

    // 设置
    if (channel >= 0)
    {
        data[1] = (byte)channel;
        data[2] = auxVol;
        data[3] = mixing;
        nDataLen = 4;

        if (mixing == 2)
        {
            data[4] = dacVol;
            nDataLen = 5;
        }
    }


    return CProtocol::ControlCommand(	buf,
                                        CMD_MIXING_MODE,
                                        data,
                                        nDataLen);
}

// 4.68 主机向设备请求重启（组播）
unsigned short	CProtocol::RebootMulticast(	char*	buf,			// 命令缓存区
                                            const char*	data,		// 数据
                                            int		len)			// 数据长度
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_REBOOT_MULTICAST,
                                        data,
                                        len);
}

// 4.70 回应终端被寻呼通知(TCP模式)
unsigned short	CProtocol::ResponseNotifyPaging(char*	buf)		// 命令缓存区
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_NOTIFY_PAGING,
                                        NULL,
                                        0);
}

// 4.72 回应寻呼台向终端发送掉线再次寻呼指令（TCP模式）
unsigned short	CProtocol::ResponsePagingAgain(char*	buf)		// 命令缓存区
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_PAGING_AGAIN,
                                        NULL,
                                        0);
}

// 4.75查询/设置网络解码分区器输出状态，状态，为负数表示查询
unsigned short	CProtocol::SplitterStatus(char*		buf,			// 命令缓存区
                                    short	status)			// 状态，为负数表示查询
{
    char data[2] = {0};
    data[0]	= (status < 0 ? OPER_GET : OPER_SET);

    if (status >= 0)
    {
        data[1] = (byte)status;
    }

    return CProtocol::ControlCommand(	buf,
                                        CMD_SPLITTER_STATUS,
                                        data,
                                        (status < 0 ? 1 : 2));
}

// 4.76 查询/设置解码功率分区器、数字功放EMC状态
unsigned short	CProtocol::EmcStatus(	char*		buf,		// 命令缓存区
                                        short	status)			// 状态，为负数表示查询
{
    char data[2] = {0};
    data[0]	= (status < 0 ? OPER_GET : OPER_SET);

    if (status >= 0)
    {
        data[1] = (byte)status;
    }

    return CProtocol::ControlCommand(	buf,
                                        CMD_EMC_STATUS,
                                        data,
                                        (status < 0 ? 1 : 2));
}

// 4.97查询/设置 DSP6636无线MIC控制面板状态
unsigned short	CProtocol::MicStatus(char*          buf,		// 命令缓存区
                                    char            volume,		// 音量：为负数表示查询，0-16
                                    unsigned char	power,		// 发射功率 1-3
                                    unsigned char	channel)	// 频道：1-99
{
    char data[4] = {0};
    data[0]	= (volume < 0 ? OPER_GET : OPER_SET);

    if (volume >= 0)
    {
        data[1] = volume;		// 音量
        data[2] = power;		// 发射功率
        data[3] = channel;		// 频道
    }

    return CProtocol::ControlCommand(	buf,
                                        CMD_MIC_STATUS,
                                        data,
                                        (volume < 0 ? 1 : 4));
}


/*
// 4.77 主机下发监控设备的信息
unsigned short CProtocol::DistributeMonitorInfo(char*        buf,         // 命令缓存区
                                                u_char       uChannel,    // 通道号（代表摄像头的编号）
                                                const char*  szAccount,   // 登录账户
                                                const char*  szPassword,  // 登录密码
                                                const char*  szIP,        // IP地址
                                                ushort       uPort,       // 端口号
                                                const char*  szRTSP)      // RTSP地址
{
    char data[MAX_BUF_LEN] = {0};
    int pos = 0;

    // 通道号
    data[pos++] = uChannel;

    // 登录账户
    int nAccountLen = strlen(szAccount);
    data[pos++] = nAccountLen;
    memcpy(&data[pos], szAccount, nAccountLen);
    pos += nAccountLen;

    // 登录密码
    int nPasswordLen = strlen(szPassword);
    data[pos++] = nPasswordLen;
    memcpy(&data[pos], szPassword, nPasswordLen);
    pos += nPasswordLen;

    // IP地址
    int nIPLen = strlen(szIP);
    data[pos++] = nIPLen;
    memcpy(&data[pos], szIP, nIPLen);
    pos += nIPLen;

    // 端口号
    data[pos++] = uPort/256;
    data[pos++] = uPort%256;

    // RTSP地址
    int RTSPLen = strlen(szRTSP);
    data[pos++] = RTSPLen;
    memcpy(&data[pos], szRTSP, RTSPLen);
    pos += RTSPLen;

    return CProtocol::ControlCommand(buf,
                                     CMD_MON_INFO,
                                     data,
                                     pos);
}

// 4.79 主机下发监控设备的上报事件
unsigned short CProtocol::DistributeMonitorEvent(char*    buf,         // 命令缓存区
                                                 u_char   uChannel,    // 通道号（代表摄像头的编号）
                                                 u_char   uEventType,      // 事件类型
                                                 const char* szTime,   // 时间
                                                 char   direction)   // 入侵方向
{
    char data[MAX_BUF_LEN] = {0};
    int pos = 0;

    // 通道号（代表摄像头的编号）
    data[pos++] = uChannel;

    // 事件类型
    data[pos++] = uEventType;

    // 时间
    memcpy(&data[pos], szTime, MAX_TIME_LEN);
    pos += MAX_TIME_LEN;

    data[pos++] = direction;

    return CProtocol::ControlCommand(buf,
                                     CMD_ISSUE_EVENT,
                                     data,
                                     pos);

}
*/

//  4.77 主机通知终端接收寻呼（SIP）
unsigned short CProtocol::NotifyDevicePaging(char*   buf,                     // 命令缓存区
                                             const    char*  szMulIP,   // 组播IP
                                             ushort  uPort,                // 端口
                                             int         nRtpType,          // Rtp负载类型
                                             int         nVolume)         // 音量
{
        char data[MAX_BUF_LEN] = {0};
        int pos = 0;

        int nIPLen = strlen(szMulIP);
        LOG(FORMAT("ip:%s, ipLen:%d", szMulIP, nIPLen), LV_INFO);
        data[pos++] = nIPLen;           // IP长度
        memcpy(&data[pos], szMulIP, nIPLen);
        pos += nIPLen;                      // IP

        data[pos++] = (uPort>>8)&0xFF;
        data[pos++] = uPort&0xFF;
        data[pos++] = nRtpType;
        data[pos++] = nVolume;
        return CProtocol::ControlCommand(buf,
                                         CMD_NOTIFY_VOIPPAGING,
                                         data,
                                         pos);
}

// 4.84 主机通知终端发起寻呼（SIP）
unsigned short CProtocol::NotifyStartPaging(char* buf,                // 命令缓存区
                                                                             int      nPageType)  // 寻呼类型
{
        char data[MAX_BUF_LEN] = {0};
        int pos = 0;
        data[pos++] = nPageType;

        return CProtocol::ControlCommand(buf,
                                                                      CMD_NOTIFY_STARTPAGING,
                                                                      data,
                                         pos);
}

// 4.4 终端寻呼通知(UDP模式)
unsigned short CProtocol::ForwarePaging(char* buf,    // 命令缓存区
                                                                        int      nRef)  // 0x08 接受寻呼, 0x80 取消寻呼
{
    char data[MAX_BUF_LEN] = {0};
    int pos = 0;
    data[pos++] = nRef;

    return CProtocol::ControlCommand(buf,
                                                                  CMD_REQUEST_PAGING,
                                                                  data,
                                     pos);
}

// 4.95 主机向终端查询网速带宽
unsigned short CProtocol::QueryDeviceNetQuality(char *buf,             // 命令缓存区
                                                                                       int   nNetType,     // 网络连接测试 0x00 : UDP单播, 0x01: UDP组播, 0x02 : TCP
                                                                                       const char* szIP,  // 查询IP
                                                                                       ushort  uPort)       // 查询端口
{
    char data[MAX_BUF_LEN] = {0};
    int pos = 0;
    data[pos++] = nNetType;

    int nIPLen = strlen(szIP);
    data[pos++] = nIPLen;           // IP长度
    memcpy(&data[pos], szIP, nIPLen);
    pos += nIPLen;                      // IP

    data[pos++] = uPort/256;
    data[pos++] = uPort%256;
    return CProtocol::ControlCommand(buf,
                                     CMD_QUERY_DEVICEBW,
                                     data,
                                     pos);
}


// 4.93 分控服务器通知终端播放节目源(HTTP方式)
unsigned short CProtocol::NotifyHTTPPlaySong(char* buf,                // 命令缓存区
                                             char	source,             // 音源
                                             const char*	szListID,	// 播放列表ID
                                             const char*	szSongPath, // 歌曲名称
                                             unsigned char volume)  	// 定时音量
{
    char data[MAX_BUF_LEN] = {0};
    int pos = 0;

    data[pos++] = source;

    int nListIDLen = strlen(szListID);
    data[pos++] = nListIDLen;
    memcpy(&data[pos], szListID, nListIDLen);
    pos += nListIDLen;

    int nSongPathLen = strlen(szSongPath);
    data[pos++] = nSongPathLen;
    memcpy(&data[pos], szSongPath, nSongPathLen);
    pos += nSongPathLen;

    data[pos++] = volume;

    return CProtocol::ControlCommand(buf,
                                     CMD_NOTIFY_HTTP_PLAY,
                                     data,
                                     pos);
}



// 4.41 设置工作模式
unsigned short	CProtocol::SetWorkPattern(	char*	buf,		// 命令缓存区
                                    char	pattern)	// 工作模式
{
    char data[2] = {0};
    data[0]	= OPER_SET;	// 设置
    data[1] = pattern;	// 音量

    return CProtocol::ControlCommand(	buf,
                                        CMD_WORK_PATTERN,
                                        data,
                                        2);
}

// 4.42 回应寻呼站/移动设备请求主机播放节目源（集中模式）
unsigned short	CProtocol::ResponsePlaySource(	char*	buf,		// 命令缓存区
                                        unsigned char	selSecID,	// 选中分区ID
                                        unsigned char	result)		// 播放结果
{
    unsigned short	cmd		= CMD_REQUEST_PLAY_SOURCE;
    unsigned short uDataLen = 1;

    buf[0]  = cmd/256;			// 请求命令
    buf[1]  = cmd%256;
    buf[2]  = cmdSequence++;	// 包序号
    buf[3]  = selSecID;			// 保留位
    buf[4]  = MODEL_HOST;		// 设备型号
    buf[5]  = 0x00;				// 包属性
    buf[6]  = uDataLen/256;		// 数据长度
    buf[7]	= uDataLen%256;

    // 播放结果
    buf[8]  = result;

    // 校验码
    buf[9] = buf[8];

    // 返回整条命令长度
    return (8+uDataLen+1);
}

// 4.43 主机通知终端播放节目源（集中模式）
unsigned short	CProtocol::NotifyStreamSource(	char*	buf,            // 命令缓存区
                                              char	source,             // 音源
                                              char	format,             // 歌曲格式
                                              unsigned int	sampleRate,	// 采样率
                                              unsigned char	bitSample,	// 采样精度
                                              unsigned char	channels,	// 声道数
                                              const char*	szName,		// 歌曲名称
                                              const char*	szMultiIP,	// 组播IP
                                              unsigned short	port,	// 组播端口
                                              unsigned char	volume,     // 定时音量
                                              unsigned int  fileLength,        // 歌曲文件大小
                                              unsigned int totalFramesCnt,// 总帧数
                                              unsigned int  currentFrame,     //当前帧数
                                              const char* fileMd5,		// MD5
                                              bool isUseMulticastNewCmd)
{
    char	data[MAX_BUF_LEN] = {0};
    unsigned short	pos			= 0;
    unsigned char	nameLen		= (szName == NULL ? 0 : strlen(szName)>127?127:strlen(szName));
    unsigned char	ipLen		= (szMultiIP == NULL ? 0 : strlen(szMultiIP));

    // 音源与歌曲格式
    data[pos++] = source;
    data[pos++] = format;

    // 采样率
    data[pos++] = sampleRate/256/256;
    data[pos++] = sampleRate/256%256;
    data[pos++] = sampleRate%256;

    // 采样精度
    data[pos++] = bitSample;

    // 声道数
    data[pos++] = channels;

    // 歌曲名称
    data[pos++] = nameLen;

    if (szName != NULL)
    {
        memcpy(&data[pos], szName, nameLen);
    }

    pos += nameLen;

    // 组播IP
    data[pos++] = ipLen;

    if (szMultiIP != NULL)
    {
        memcpy(&data[pos], szMultiIP, ipLen);
    }

    pos += ipLen;

    // 端口
    data[pos++] = port/256;
    data[pos++] = port%256;

    // 音量
    data[pos++] = volume;

    // 歌曲文件大小
    data[pos++] = fileLength>>24;
    data[pos++] = fileLength>>16;
    data[pos++] = fileLength>>8;
    data[pos++] = fileLength;

    // 总帧数
    data[pos++] = totalFramesCnt>>24;
    data[pos++] = totalFramesCnt>>16;
    data[pos++] = totalFramesCnt>>8;
    data[pos++] = totalFramesCnt;

    // 当前帧数
    data[pos++] = currentFrame>>24;
    data[pos++] = currentFrame>>16;
    data[pos++] = currentFrame>>8;
    data[pos++] = currentFrame;

    // FileMd5
    int md5Len  = strlen(fileMd5);
    data[pos++] = md5Len;
    memcpy(data+pos,fileMd5,md5Len);
    pos += md5Len;
    // 音量（由于旧终端程序取最后一个playload的值作为定时音量，所以只能重复）
    data[pos++] = volume;

    data[pos++] = isUseMulticastNewCmd;
    // 音量（由于旧终端程序取最后一个playload的值作为定时音量，所以只能重复）
    data[pos++] = volume;

    return CProtocol::ControlCommand(	buf,
                                        CMD_NOTIFY_STREAM_SOURCE,
                                        data,
                                        pos);
}

// 4.44 播放节目源数据流传输（集中模式）
unsigned short	CProtocol::StreamSource(char*	buf,				// 命令缓存区
                                        const char*	data,			// 数据流
                                        int		len)				// 数据流长度
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_STREAM_SOURCE,
                                        data,
                                        len);
}

// 4.57音频采集器音频数据传输
unsigned short CProtocol::StreamAudioCollect(char*	buf,				// 命令缓存区
                                             const char*	data,		// 数据流
                                             int	len)				// 数据流长度
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_STREAM_AUDIO_DATA,
                                        data,
                                        len);
}

// 4.60 寻呼台/移动设备/分控设备向主机发送选中的分区（集中模式）
unsigned short	CProtocol::ResponseSelectedSections(char*	buf,		// 命令缓存区
                                            unsigned char	selSecID,	// 选中分区ID
                                            unsigned char	packID)		// 包ID
{
    unsigned short	cmd		= CMD_SELECTED_SECTIONS;
    unsigned short uDataLen = 1;

    buf[0]  = cmd/256;			// 请求命令
    buf[1]  = cmd%256;
    buf[2]  = cmdSequence++;	// 包序号
    buf[3]  = selSecID;			// 保留位
    buf[4]  = MODEL_HOST;		// 设备型号
    buf[5]  = 0x00;				// 包属性
    buf[6]  = uDataLen/256;		// 数据长度
    buf[7]	= uDataLen%256;

    // 包ID
    buf[8]  = packID;

    // 校验码
    buf[9] = buf[8];

    // 返回整条命令长度
    return (8+uDataLen+1);
}

// 构造分区状态
unsigned short	CProtocol::SectionStatus(char*	buf,                    // 命令缓存区
                                         char*	szMac,                  // MAC地址
                                         unsigned char	volume,			// 音量
                                         unsigned char	source,			// 节目源
                                         unsigned char	playStatus,		// 播放状态
                                         const char*	szProName,          // 节目名称
                                         unsigned char	timerStatus,	// 定时点状态
                                         unsigned char	deviceFeature,	// 设备特性
                                         unsigned char	syncStaus)		// 同步状态
{
    char	data[MAX_BUF_LEN]   = {0};
    unsigned short	pos			= 0;
    unsigned char	macLen		= 6;
    unsigned char	nameLen		= (szProName == NULL ? 0 : strlen(szProName));

    // MAC地址
    memcpy(&data[pos], szMac, macLen);
    pos += macLen;

    // 音量、节目源、播放状态
    data[pos++] = volume;
    data[pos++] = source;
    data[pos++] = playStatus;

    // 节目名称
    data[pos++] = nameLen;
    if (szProName != NULL)
    {
        memcpy(&data[pos], szProName, nameLen);
    }
    pos += nameLen;

    // 定时点、设备特性、同步状态
    data[pos++] = timerStatus;
    data[pos++] = deviceFeature;
    data[pos++] = syncStaus;

    memcpy(buf, data, pos);

    return pos;
}

 #if SUPPORT_PAGER_CALL
// 构造分控设备状态
unsigned short	CProtocol::PagerStatus(char*	buf,                    // 命令缓存区
                                         const char*	szMac,          // MAC地址
                                         unsigned char	source,		    // 节目源
                                         bool isSupportCall,            // 是否支持对讲
                                         unsigned char	isSupportVideo, // 是否支持可视
                                         unsigned char  reserve)        // 预留字段                                       
{
    char	data[MAX_BUF_LEN]   = {0};
    unsigned short	pos			= 0;
    unsigned char	macLen		= 6;

    // MAC地址
    memcpy(&data[pos], szMac, macLen);
    pos += macLen;

    //节目源、是否支持对讲、是否支持可视
    data[pos++] = source;
    data[pos++] = isSupportCall;
    data[pos++] = isSupportVideo;
    data[pos++] = reserve;

    memcpy(buf, data, pos);

    return pos;
}
#endif


// 4.67主机向分控设备下发终端当前状态
unsigned short	CProtocol::ForwardStatus(char*	buf,			// 命令缓存区
                                         const char*	data,	// 数据
                                         int		len)		// 数据长度

{
    return CProtocol::ControlCommand(	buf,
                                        CMD_FORWARD_STATUS,
                                        data,
                                        len);
}


// 4.69 主机向分控设备发送音频采集器列表在线/离线状态
unsigned short	CProtocol::ForwardAudioCollectors(char*	buf,            // 命令缓存区
                                                  const char*	data,	// 数据
                                                  int		len)        // 数据长度
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_FORWARD_AUDIO_COLLECTOR,
                                        data,
                                        len);
}

// 4.73 查询/设置音频传输方式（TCP模式）
unsigned short	CProtocol::AudiocastMode(char*	buf,		// 命令缓存区
                                        char	mode)		// 传输方式
{
    char data[2] = {0};
    data[0]	= (mode < 0 ? OPER_GET : OPER_SET);
    data[1] = mode;		// 音频传输方式

    return CProtocol::ControlCommand(	buf,
                                        CMD_AUDIOCAST_MODE,
                                        data,
                                        mode < 0 ? 1 : 2);
}


/*****************************************************************/

// 获取SIP会话状态
unsigned short	CProtocol::SipGetStatus(char*	buf)			// 命令缓存区
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_SIP_GET_STATUS,
                                        NULL,
                                        0);
}


// SIP账号登录
unsigned short	CProtocol::SipLogIn(char*	buf,                // 命令缓存区
                                    bool isEnableSip,                            //是否启用SIP
                                    unsigned char outPutVolume,              // 输出音量
                                    const char*	szAccount,		// 登录账号
                                    const char*	szPassword,		// 登录密码
                                    const char*	szServerAddr,	// 服务器地址
                                    int		nServerPort,        // 端口
                                    int     nServerProtocol)	// 协议
{
    char	data[MAX_BUF_LEN]	= {0};
    unsigned short	pos					= 0;
    unsigned char	accLen				= strlen(szAccount);
    unsigned char	pwdLen				= strlen(szPassword);
    unsigned char	ipLen				= strlen(szServerAddr);

    //是否启用SIP注册
    data[pos++] = isEnableSip;

    //输出音量
    data[pos++] = outPutVolume;

    //MIC输入级别 暂时固定5
    data[pos++] = 5;


    // 服务器IP
    data[pos++] = ipLen;
    memcpy(&data[pos], szServerAddr, ipLen);
    pos += ipLen;

    // 端口
    data[pos++] = nServerPort/256;
    data[pos++] = nServerPort%256;

    // 账号
    data[pos++] = accLen;
    memcpy(&data[pos], szAccount, accLen);
    pos += accLen;

    // 密码
    data[pos++] = pwdLen;
    memcpy(&data[pos], szPassword, pwdLen);
    pos += pwdLen;

    //协议
    data[pos++] = nServerProtocol;

    return CProtocol::ControlCommand(	buf,
                                        CMD_SIP_LOG_IN,
                                        data,
                                        pos);
}

// 获取SIP登录账号信息
unsigned short	CProtocol::SipGetLogInfo(char*	buf)			// 命令缓存区
{
    return CProtocol::ControlCommand(	buf,
                                        CMD_SIP_GET_LOG_INFO,
                                        NULL,
                                        0);
}


#if SUPPORT_WEB_PAGING
//5.16 服务器发起广播寻呼
unsigned short	CProtocol::ServerWebPagingNotify(char* buf,CWebPaging &webpPaging,int event,AudioAlgorithm audioCodecs)
{
    char	data[MAX_BUF_LEN]	= {0};
    unsigned short	pos		    = 0;

    data[pos++] = (event == 1)? 0x08:0x80;              //寻呼开始/结束
    if(event == 1)
    {
        //由uuid组成6字节MAC
        memcpy(data+pos,webpPaging.getMAC(),6);
        pos+=6;
        
        data[pos++] = (webpPaging.IsAdmin())? 0x10:0;       //管理员0x10,其他为0
        data[pos++] = webpPaging.GetVolume();               //寻呼音量

        int sampleRate = 16000;
        if(audioCodecs == ALGORITHM_OPUS)
        {
            sampleRate = 48000;
        }
        int sampleFMT  = 16;
        int channel = 1;
        int port = g_Global.m_KCP_PORT+1;

        //采样率
        data[pos++] = sampleRate/(256*256);
        data[pos++] = sampleRate/256%256;
        data[pos++] = sampleRate%256;
        // 采样精度
        data[pos++] = sampleFMT;
        // 声道数
        data[pos++] = channel;

        //语音编码
        data[pos++] = audioCodecs;//默认G722
        //端口号
        data[pos++] = port/256;
        data[pos++] = port%256;
    }
    else
    {
        //由uuid组成6字节MAC
        memcpy(data+pos,webpPaging.getMAC(),6);
        pos+=6;
    }

    return CProtocol::ControlCommand(	buf,
                                    CMD_WEB_PAGING_NOTIFY,
                                    data,
                                    pos);
}
#endif


//主机通知解码终端即将进入定时音源
unsigned short	CProtocol::NotifyDecoderTiming(char*	buf)				// 命令缓存区
{
    return CProtocol::ControlCommand(	buf,
                                    CMD_NOTIFY_DECODER_READY_TIMING,
                                    NULL,
                                    0);
}