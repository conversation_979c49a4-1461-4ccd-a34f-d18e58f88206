#ifndef PROTOCOL_H
#define PROTOCOL_H
#include <QtGlobal>
#include "Global/Const.h"
#include "Tools/CMyString.h"
#include "Model/Device/AudioCollector.h"
#include "Model/Device/NetworkInfo.h"
#include "Model/Device/SequencePower.h"
#if SUPPORT_WEB_PAGING
#include "Network/Web/WebPaging.h"
#endif
#include "Model/Device/AudioCall.h"

#if SUPPORT_NOISE_DETECTOR
#include "Model/Device/NoiseDetector.h"
#endif

#if defined(Q_OS_LINUX)
#include "GlobalDef.h"
#else
#include "windef.h"
#endif


#define MULTICAST_SEND_IP		("**************")		// 向所有终端发送通知组播组IP
#define MULTICAST_SEND_PORT		52094					// 端口

#define MULTICAST_RECV_IP		("**************")		// 终端在线宣告组播组IP
#define MULTICAST_RECV_PORT		52074					// 端口

#define MULTICAST_PAGING_IP		("**************")		// 组播转发寻呼台的音频流
#define MULTICAST_PAGING_PORT	52052					// 端口

#define UDP_PORT				48888					// 单播端口
#define RESTART_PORT			47777					// 重启音箱的通讯端口
#define OFFLINE_TIME			32						// 分区掉线时间(s)
#define OFFLINE_TIME_TCP        62                      // 分区掉线时间(tcp)
#define REBOOT_DURATION			5						// 重启至少所需要的时间(s)

#define TCP_PORT_DEFAULT				49888					// TCP通讯端口
#define AUDIO_SERVER_PORT		49999					// 音频服务器端口

#define UDP_STREAM_PORT_DEFAULT			49988					// TCP模式下UDP传输音频流端口

#define	GPS_BROADCAST_IP		("***************")		// 广播IP
#define GPS_UDP_PORT			40090					// 与校时器通讯端口
#define GPS_TIMEOUT				60						// GPS多久没有发送校时信息，防止GPS信号不好的状况

#define QUALITY_PORT            49000                   // 网络质量检测端口

#define SER_TCP_PORT            49900                   // 分控TCP通讯接口

#define SIP_DEFAULT_PORT		5060					// SIP默认登录端口

#define LOG_FILE_MAX_COUNT		30						// 设备最多保存30天的日志文件
#define LOG_FILE_NAME_MAX_LEN	32						// 日志文件名称最长

// 3.1设备型号
typedef enum
{
    MODEL_HOST					= 0x01,		// 主机
    MODEL_PAGER_A				= 0x03,		// 寻呼台A
    MODEL_PAGER_B				= 0x04,		// 寻呼台B
    MODEL_IP_SPEAKER_A			= 0x05,		// 解码终端A
    MODEL_IP_SPEAKER_B			= 0x06,		// 解码终端B
    MODEL_IP_SPEAKER_C			= 0x07,		// 解码终端C
    MODEL_IP_SPEAKER_D			= 0x0A,		// 解码终端D
    MODEL_IP_SPEAKER_G			= 0x31,		// 解码终端G

    MODEL_IP_SPEAKER_E			= 0x10,		// 解码终端E

    MODEL_FIRE_COLLECTOR_A		= 0x08,		// 网络消防采集器A(旧)
    MODEL_AUDIO_COLLECTOR_A		= 0x09,		// 网络音频采集器A(旧)
    MODEL_SEQUENCE_POWER_A      = 0x0B,     // 电源时序器A(旧)
    MODEL_FIRE_COLLECTOR_B      = 0x0C,     // 网络消防采集器B
    MODEL_AUDIO_COLLECTOR_B     = 0x0D,     // 网络音频采集器B
    MODEL_SEQUENCE_POWER_B      = 0x0E,     // 电源时序器B
    MODEL_REMOTE_CONTROLER      = 0x11,    //  远程遥控器
    MODEL_AUDIO_MIXER_DECODER   = 0x12,     // 音频混音器-解码器
    MODEL_AUDIO_MIXER_ENCODER   = 0x13,     // 音频混音器-编码器
    MODEL_PAGER_C				= 0x14,		// 寻呼台C
    MODEL_PHONE_GATEWAY         = 0x15,     // 电话网关
    
/*20250211 新增设备定义*/
    MODEL_FIRE_COLLECTOR_C      = 0x16,     // 消防采集器C
    MODEL_AUDIO_COLLECTOR_C     = 0x17,     // 音频采集器C
    MODEL_SEQUENCE_POWER_C      = 0x18,     // 电源时序器C
    MODEL_REMOTE_CONTROLER_C    = 0x19,     // 远程遥控器C
    MODEL_AUDIO_MIXER_DECODER_C = 0x1A,     // 音频混音器C-解码器
    MODEL_AUDIO_MIXER_ENCODER_C = 0x1B,     // 音频混音器C-编码器
/*********************/

/*20250222 新增设备定义*/
    MODEL_IP_SPEAKER_F			= 0x1C,		// 解码终端F-易会系列
    MODEL_FIRE_COLLECTOR_F      = 0x1D,     // 消防采集器F-易会系列
    MODEL_AUDIO_COLLECTOR_F     = 0x1E,     // 音频采集器F-易会系列
    MODEL_SEQUENCE_POWER_F      = 0x1F,     // 电源时序器F-易会系列
    MODEL_REMOTE_CONTROLER_F    = 0x30,     // 远程遥控器F-易会系列
/*********************/

    MODEL_AMP_CONTROLER         = 0x32,     // 功放控制器

    MODEL_NOISE_DETECTOR        = 0x33,     // 噪声检测器


    MODEL_MOBILE				= 0x20,		// 移动设备
    MODEL_INTERCOM_STATION      = 0x21,     // 网络可视对讲寻呼台 DSP9312(留着，以后用)

    MODEL_CONTROL_SERVER        = 0x25,     // 分控服务器(先留着，以后用)
    MODEL_GPS					= 0x34,		// GPS校时器

}DeviceModel;



// 3.2节目源
typedef enum
{
    PRO_OFFLINE			= -1,			// 离线状态
    PRO_IDLE			= 0x00,			// 空闲状态
    PRO_ANALOG_INPUT	= 0x01,			// 模拟输入(本地播放)
    PRO_LOCAL_PLAY		= 0x02,			// 网络点播
    PRO_TIMING			= 0x03,			// 定时
    PRO_AUDIO_MIXED		= 0x04,			// 音频混音
    PRO_CALL			= 0x05,			// 对讲
    PRO_EVENT           = 0x06,        //  事件报警     
    PRO_LISTEN          = 0x07,        //  监听
    PRO_ALARM			= 0x08,			// 消防告警
    PRO_PAGING			= 0x09,			// 寻呼
    PRO_100V			= 0x0A,			// 100V输入
    
    PRO_SIP_CALLING     = 0x0B,         // SIP通话
    PRO_API_TTS_MUSIC   = 0x0D,         // API播放TTS或者音乐
    PRO_API_NET_RADIO   = 0x0E,         // 网络电台
    
    PRO_PHONE_GATEWAY   = 0x20,         // 电话网关音源
    
    PRO_AUDIO_COLLECTOR_MIN = 0x51,		// 音频采集器0x51 - 0x78，支持10台音频采集器，共40个音源
    PRO_AUDIO_COLLECTOR_MAX = 0x78,     

}ProgramSource;

// 3.3播放模式
typedef enum
{
    PM_SINGLE           = 0x01,     // 单曲播放
    PM_SINGLE_CYCLE     = 0x02,     // 单曲循环
    PM_ORDER            = 0x03,     // 顺序播放
    PM_LIST_CYCLE		= 0x04,     // 循环播放
    PM_RANDOM           = 0x05,     // 随机播放

}PlayMode;

// 3.4播放状态
typedef enum
{
    PS_PLAY				= 0x01,			// 播放
    PS_PAUSE			= 0x02,			// 暂停
    PS_STOP				= 0x04,			// 停止

}PlayStatus;

// 3.5设置或查询
typedef enum
{
    OPER_SET			= 0x08,			// 设置
    OPER_GET			= 0x80,			// 查询

}Operating;


// 3.6静音状态
typedef enum
{
    MUTE_NO				= 0x01,			// 取消静音
    MUTE_YES			= 0x10,			// 设置静音

}MuteStatus;

// 3.7控制模式
typedef enum
{
    CTRL_PROGRAMMABLE	= 0x01,			// 程控
    CTRL_MANUALLY		= 0x10,			// 手控

}ControlMode;

// 3.8文件类型
typedef enum
{
    //FILE_UNKNOWN            = 0x00,         // 未知文件
    FILE_GROUP				= 0x01,			// 分组文件
    FILE_PLAYLIST			= 0x02,			// 播放列表文件
    FILE_TIMER				= 0x03,			// 定时文件
    FILE_SECTION			= 0x04,			// 分区文件
    FILE_AUDIO_COLLECTOR	= 0x05,			// 音频采集器文件
    FILE_FIRE_COLLECTOR		= 0x06,			// 消防采集器文件
    FILE_MONITOR            = 0x07,         // 监控文件
    FILE_INTERCOM_STATION   = 0x08,         // DSP9312文件
    FILE_USER               = 0x09,         // 用户文件
    FILE_SEQUENCE_POWER     = 0x0A,         // 电源时序器文件
    FILE_PAGER              = 0x0B,         // 寻呼台文件
    FILE_REMOTE_CONTROLER   = 0x0C,         // 远程遥控器文件
    FILE_AUDIO_MIXER        = 0x0D,         // 音频混音器文件
    FILE_PHONE_GATEWAY      = 0x0E,         // 电话网关文件
    FILE_AMP_CONTROLER      = 0x0F,         // 功放控制器文件
    FILE_NOISE_DETECTOR     = 0x10,         // 噪声检测器文件
    FILE_GPS                = 0x11,         // GPS校时器文件
    FILE_ALL                = 0x00          // 全部文件
}FileType;

#define FILE_SEC2MON  0x10         // 分区监控设备绑定

// 3.9 SIP会话状态
typedef enum
{
    SIP_NULL			= 0x00,			// 还没有获取到状态（协议没有）
    SIP_LOG_IN			= 0x01,			// 注册成功
    SIP_LOGGING			= 0x02,			// 正在注册
    SIP_LOG_FAILED		= 0x03,			// 注册失败
    SIP_RESPONSE_TIMEOUT= 0x04,			// 响应超时
    SIP_WRONG_PASSWORD	= 0x05,			// 密码错误
    SIP_CALLING			= 0x06			// 通话中

}SipStatus;

// 3.10 工作模式
typedef enum
{
    WP_UNKNOWN			= 0x00,			// 未知模式，当成分布模式处理
    WP_DISTRIBUTION		= 0x01,			// 分布模式
    WP_CENTRALIZED		= 0x02,			// 集中模式

}WorkPattern;

// 3.11 网络模式
typedef enum
{
    NETWORK_UNKNOWN		= 0x00,			// 未知模式，当成UDP模式处理
    NETWORK_UDP			= 0x01,			// UDP模式
    NETWORK_TCP			= 0x02,			// TCP模式

}NetworkMode;

// 3.12 音频算法
typedef enum
{
    ALGORITHM_PCM		= 0x00,			// 标准PCM
    ALGORITHM_711		= 0x01,			// G711
    ALGORITHM_722		= 0x02,			// G722
    ALGORITHM_722_1		= 0x03,			// G722.1
    ALGORITHM_OPUS      = 0x04,         // OPUS
}AudioAlgorithm;

// 3.13 寻呼优先级
typedef enum
{
    PAGE_PRI_NORMAL		= 0x00,			// 普通账户
    PAGE_PRI_ADMIN		= 0x10,			// 管理员

}PagingPriority;

// 3.14 EQ模式定义
typedef enum
{
    EQ_OFF			= 0x00,				// 关闭EQ
    EQ_CUTEOM		= 0x01,				// 自定义
    EQ_POP		    = 0x02,				// 流行
    EQ_DANCE		= 0x03,				// 舞曲
    EQ_ROCK		    = 0x04,				// 摇滚
    EQ_CLASSICAL	= 0x05,				// 古典
    EQ_VOICE		= 0x06,				// 人声
    EQ_SOFT		    = 0x07,			    // 柔和
}EqMode;

// 3.15 包属性
enum
{
    PACK_ATTR_NORMAL			= 0x00,	// 普通
    PACK_ATTR_CTRL_TO_HOST		= 0x01, // 分控设备需通过主机转发的数据包
    PACK_ATTR_HOST_FOWARD_PAGER	= 0x02,	// 主机转发寻呼设备的数据包
    PACK_ATTR_HOST_FOWARD_MOBILE= 0x03,	// 主机转发移动设备的数据包
};

// 3.16 音频传输方式(只针对TCP和集中模式下可选)
typedef enum
{
    AUDIOCAST_UNKNOWN		= 0x00,			// 未知，当组播处理
    AUDIOCAST_MULTICAST		= 0x01,			// 组播
    AUDIOCAST_UNICAST		= 0x02,			// 单播

}Audiocast;

// 控制命令
typedef enum
{
#if APP_IS_LZY_COMMERCE_VERSION
    CMD_BROADCAST_HOST_INFO		= 0xFFE0,		// 组播主机信息
    CMD_CONNECT_STANDBY_SERVER	= 0xFFE1,		// 连接备用服务器，备用服务器收到需要应答
    CMD_SEARCH_ONLINE_DEVICE	= 0xFFAE,		// 搜索在线设备
    CMD_NOTIFY_ONLINE_DEVICE	= 0xFFAA,		// 设备上线通知
    CMD_NOTIFY_OFFLINE_DEVICE	= 0xFFEE,		// 设备下线通知
#else
    CMD_BROADCAST_HOST_INFO		= 0xFFF0,		// 组播主机信息
    CMD_CONNECT_STANDBY_SERVER	= 0xFFF1,		// 连接备用服务器，备用服务器收到需要应答
    CMD_SEARCH_ONLINE_DEVICE	= 0xFFFF,		// 搜索在线设备
    CMD_NOTIFY_ONLINE_DEVICE	= 0xFFFA,		// 设备上线通知
    CMD_NOTIFY_OFFLINE_DEVICE	= 0xFFFE,		// 设备下线通知
#endif
    CMD_KCP_TEST	            = 0xFFEE,		// KCP测试包（用于验证是接收终端KCP接收否正常）
    CMD_REQUEST_PAGING			= 0x0001,		// 请求终端寻呼
    CMD_PAGING_PCM				= 0x0002,		// 寻呼数据发送
    CMD_TIME_SYNC				= 0x0003,		// 时间同步
    CMD_DEVICE_NAME				= 0x0004,		// 查询/设置设备名称
    CMD_DEVICE_VOLUME			= 0x0005,		// 查询/设置设备音量
    CMD_GET_DEVICE_STATUS		= 0x0006,		// 查询设备状态
    CMD_FIRMWARE_VERSION		= 0x0007,		// 查询终端固件版本号
    CMD_FIRMWARE_UPGRADE		= 0x0008,		// 设备固件升级
    CMD_NOTIFY_PROGRESS_RATE	= 0x0009,		// 升级进度上报
    CMD_WEBCASTING				= 0x000A,		// 主机/寻呼台点播歌曲
    CMD_SET_PLAY_STATUS			= 0x000B,		// 点播歌曲状态
    CMD_MUTE_STATUS				= 0x000C,		// 静音状态
    CMD_CONTROL_MODE			= 0x000D,		// 程控状态
    CMD_GET_FILE_INFO			= 0x000E,		// 主机向终端查询文件信息
    CMD_REQUEST_FILE_INFO		= 0x000F,		// 终端向主机查询文件信息
    CMD_UPDATE_FILE				= 0x0010,		// 主机向终端请求更新文件
    CMD_SYNC_SONG_FILE			= 0x0011,		// 终端向主机请求同步歌曲文件
    CMD_SONG_SYNC_PROGRESS		= 0x0012,		// 歌曲文件同步进度上传
    CMD_STOP_SYNC_SONG			= 0x0013,		// 主机中止同步歌曲文件
    CMD_SET_RING				= 0x0014,		// 主机向终端设置钟声
    CMD_PLAY_RING				= 0x0015,		// 主机向终端请求播放钟声
    CMD_PLAY_LOCAL_SONG			= 0x0016,		// 主机向终端请求播放本地歌曲
    CMD_PLAY_MODE				= 0x0017,		// 播放模式
    CMD_IDLE_STATUS				= 0x0018,		// 切换到空闲状态
    CMD_REQUEST_SYNC_TIME		= 0x0019,		// 终端主动请求时间同步
    CMD_REASSIGN_MAC			= 0x001A,		// 主机请求终端重新分配MAC地址
    CMD_REPAGING				= 0x001B,		// 寻呼台向终端发送掉线再次寻呼指令
    CMD_REBOOT					= 0x001C,		// 主机向设备请求重启
    CMD_RESET_DATA				= 0x001D,		// 主机向设备请求清除数据
    CMD_SPEAKER_OFFLINE_INFO	= 0x001E,		// 寻呼台向主机发送终端寻呼中途掉线信息
    CMD_SIP_GET_STATUS			= 0x001F,		// 查询SIP会话状态
    CMD_SIP_LOG_IN				= 0x0020,		// SIP账号登录
    CMD_SIP_GET_LOG_INFO		= 0x0021,		// 获取SIP账号登录信息
    CMD_GET_FLASH_INFO			= 0x0022,		// 获取FLASH信息
    CMD_GET_DATE_TIME			= 0x0024,		// 获 取设备日期时间
    CMD_WORK_PATTERN			= 0x0026,		// 4.41 查询/设置工作模式
    CMD_REQUEST_PLAY_SOURCE		= 0x0027,		// 4.42 寻呼站/移动设备请求主机播放节目源（集中模式）
    CMD_NOTIFY_STREAM_SOURCE	= 0x0028,		// 4.43 主机通知终端播放节目源（集中模式）
    CMD_STREAM_SOURCE			= 0x0029,		// 4.44 播放节目源数据流传输（集中模式）
    CMD_SEND_FILE_INFO			= 0x002A,		// 4.45 主机向控制设备发送终端文件信息
    CMD_SET_ALARM_SOUND			= 0x002B,		// 4.46 主机向终端设置警报声
    CMD_SET_ALARM_SWITCH		= 0x002C,		// 4.47 主机向终端设置开启/关闭警报
    CMD_GET_ALARM_STATE			= 0x002E,		// 4.48 主机向消防采集器查询通道触发状态
    CMD_ALARM_TRIGGER_MODE		= 0x002F,		// 4.49 主机向消防采集器设置触发模式
    CMD_SET_AUDIO_INFO			= 0x0030,		// 4.50 主机向音频采集器/终端设置音频采集音源
    CMD_NETWORK_MODE			= 0x0031,		// 4.51 主机向终端设置网络模式
    CMD_IP_INFO					= 0x0032,		// 4.52 主机向终端查询/设置IP属性
    CMD_GET_LOG_FILE_LIST		= 0x0033,		// 4.53 主机向设备获取记录文件列表
    CMD_GET_LOG_FILE_DATA		= 0x0034,		// 4.54 主机向设备获取记录文件内容
    CMD_SEND_AUDIO_STATE		= 0x0036,		// 4.55 主机向采集器主动发送终端音源选择状态
    CMD_SEND_AUDIO_LIST			= 0x0037,		// 4.56 主机向其他控制设备下发音频采集器设备列表
    CMD_STREAM_AUDIO_DATA		= 0x0038,		// 4.57 音频采集器音频数据传输（采集器发送，终端接收）
    CMD_POWER_OUTPUT_MODE		= 0x0039,		// 4.58 主机向网络解码播放器查询/设置电源输出模式
    CMD_SIGAL_DETECTION			= 0x003A,		// 4.59 主机向网络解码播放器查询回路检测状态
    CMD_SELECTED_SECTIONS		= 0x003B,		// 4.60 寻呼台/移动设备/分控设备向主机发送选中的分区（集中模式）
    CMD_EQ_MODE					= 0x003C,		// 4.61 主机向解码终端查询/设置EQ音效
    CMD_ALARM_STATE_CHANGED		= 0x003D,		// 4.62 消防采集器通道触发状态改变时，主动通知主机
    CMD_SEND_FILE_DATA			= 0x003E,		// 4.63 发送文件内容
    CMD_EMMC_STATUS				= 0x003F,		// 4.64 主机向终端查询/设置EMMC工作状态
    CMD_MIXING_MODE				= 0x0041,		// 4.66 混音模式
    CMD_FORWARD_STATUS			= 0x0042,		// 4.67 主机向分控设备下发终端当前状态
    CMD_REBOOT_MULTICAST		= 0x0043,		// 4.68 主机向设备请求重启（组播）
    CMD_FORWARD_AUDIO_COLLECTOR	= 0x0044,		// 4.69 主机向分控设备发送音频采集器列表在线/离线状态
    CMD_NOTIFY_PAGING			= 0x0045,		// 4.70 终端被寻呼通知(TCP模式)
    CMD_PAGING_STREAM			= 0x0046,		// 4.71 寻呼数据传输(TCP模式)
    CMD_PAGING_AGAIN			= 0x0047,		// 4.72寻呼台向终端发送掉线再次寻呼指令（TCP模式）
    CMD_AUDIOCAST_MODE			= 0x0048,		// 4.73 查询/设置音频传输方式
    CMD_CONFIRM_FORWARD_AUDIO	= 0x0049,		// 4.74 终端向主机发送单播音频传输确认包，UDP端口为49988
    CMD_SPLITTER_STATUS			= 0x004A,		// 4.75 查询/设置网络解码分区器输出状态
    CMD_EMC_STATUS				= 0x004B,		// 4.76 查询/设置解码功率分区器、数字功放EMC状态

 
    CMD_NOTIFY_VOIPPAGING       = 0x004C,       // 4.77 主机通知终端接收寻呼（SIP）
    CMD_NOTIFY_STARTPAGING      = 0x004D,       // 4.84 主机通知终端发起寻呼（SIP）
    CMD_QUERY_DEVICEBW          = 0x0056,       // 4.85 主机向终端查询网速带宽
    CMD_FIRST_BOOT              = 0x0057,       // 4.95 终端启动通知

   
    CMD_MIC_STATUS				= 0x0058,       // 4.97查询/设置 DSP6636无线MIC控制面板状态

    
    CMD_BLUETOOTH_INFO			= 0x0061,       // 5.0.0查询/设置 蓝牙信息


    CMD_TCP_CLIENT_CONNECTED    = 0x0062,		//TCP客户端连接后第一时间发送连接信息 
#if SUPPORT_PAGER_CALL
    CMD_ALL_PAGER_STATUS        = 0x0064,		//获取所有寻呼台设备的状态 
    CMD_CALLING_INVITATION		= 0x0065,		//对讲设备主叫方发起对讲邀请
    CMD_CALLED_RESPONSE			= 0x0066,		//对讲设备被叫方应答邀请
    CMD_CALLED_STATUS			= 0x0067,		//对讲设备状态反馈
    CMD_CALLING_AUDIOSTREAM		= 0x0068,		//对讲设备音频流传输

    CMD_CALL_REQUEST_VIDEO      = 0x0098,       //对讲设备请求发起视频流
    CMD_CALL_VIDEO_PARM         = 0x0099,       //对讲设备发送视频流参数
    CMD_CALL_VIDEO_STATUS       = 0x009A,       //对讲设备发送视频对讲状态
    CMD_CALL_VIDEO_STREAM       = 0x009B,       //对讲设备视频流传输
#endif

#if SUPPORT_LISTEN_FUNCTION
    CMD_LISTEN_EVENT            = 0x0071,		//监听设备发起监听
    CMD_LISTEN_RESPONSE         = 0x0072,       //被监听设备应答
    CMD_LISTEN_STREAM_UPLOAD    = 0x0073,		//监听设备上传音频流
    CMD_LISTEN_STATUS           = 0X0074,       //监听设备发送监听状态
#endif

#if SUPPORT_WEB_PAGING
    CMD_WEB_PAGING_NOTIFY              = 0x0077,       //服务器发起广播寻呼
    CMD_WEB_PAGING_STREAM              = 0x0078,       //服务器广播寻呼-音频流传输(APP喊话发送、声卡采集客户端编码PCM至G.722流后发送给设备端)
    CMD_SOUNDCARD_PAGING_STREAM        = 0x0178,       //声卡采集客户端采集（声卡采集器客户端发送、服务器转发OPUS流）
#endif

    CMD_CONTROLER_ACCOUNT_INFO         = 0x0079,       //控制设备向主机发送登录账户

    CMD_AUDIO_COLLECTOR_STREAM_TCP     = 0x007A, 		//音频采集器数据流发送（TCP)

    CMD_NOTIFY_DECODER_READY_TIMING    = 0x007C, 		//主机通知解码终端即将进入定时音源

    CMD_SEQUENCE_POWER_INFO            = 0x0080, 		//主机设置/查询时序器参数
    CMD_SEQUENCE_POWER_TIMING          = 0x0081, 		//主机向电源时序器发送定时点信息
    CMD_SET_AUDIO_MIXER_SOURCE         = 0x0082, 		//主机向混音器/终端设置混音音源
    CMD_AUDIO_MIXER_STREAM             = 0x0083, 		//混音器音频流

    CMD_DEVICE_SUB_VOLUME			   = 0x0084,		//查询/设置设备子音量
    CMD_DEVICE_VOLUME_ADD_MIN          = 0x0085,        //主机控制设备音量加/减
    CMD_REMOTE_CONTROLER_KEY           = 0x0086,        //远程遥控器按键

    CMD_INTERCOM_BASIC_CONFIG          = 0x0087,        //查询/设置对讲终端基础参数

    CMD_SET_TRIGGER_CONFIG             = 0x0088,        //查询/设置触发参数
    CMD_REQUEST_TRIGGER_PLAY           = 0x0089,        //请求服务器播放触发歌曲

    CMD_GET_ACCOUNT_STORAGE_CAPACITY   = 0x0090, 		//寻呼台获取账户存储容量
    CMD_REQUEST_UPLOAD_SONG_FILE       = 0x0091,        //寻呼台请求上传歌曲文件
    CMD_NOTIFY_UPLOAD_STATUS           = 0x0092,        //寻呼台通知服务器上传状态
    CMD_REQUEST_DELETE_SONG            = 0x0094,        //寻呼台对本地歌曲进行删除操作

    CMD_SET_BROADCAST_PAGING           = 0x0095,        //主机控制寻呼台发起广播寻呼

    CMD_AUDIO_MIXER_CONFIG             = 0x0096,        //查询/设置音频混音器参数

    CMD_AUDIO_COLLECTOR_CONFIG         = 0x0097,        //查询/设置音频采集器参数

    CMD_PHONE_GATEWAY_CONFIG             = 0x009C,       //查询/设置电话网关参数
    CMD_SET_PHONE_GATEWAY_SOURCE         = 0x009D, 		//主机向电话网关/终端设置电话网关音源
    CMD_PHONE_GATEWAY_STREAM             = 0x009E, 		//电话网关音频流

    CMD_RECEIVE_JSON_COMMAND             = 0x009F, 		//终端向服务器发送JSON命令

    CMD_STREAM_SOURCE_MULTICAST_NEW		 = 0x00B0,		// 播放节目源数据流传输组播(新指令,加入命令字，以便后续离线存储)

    CMD_INFORMATION_PUBLISH_CONFIG       = 0x00B1,      //查询/设置信息发布参数

    CMD_NETWORK_MODE_MULTICAST			 = 0x00B2,		// 主机向终端设置网络模式(组播)

    CMD_AMP_CONTROLER_STATUS             = 0x00B3,      //功放控制器状态

    CMD_NOISE_DETECTOR_CONFIG            = 0x00B4,      //噪声自适应器参数

    CMD_GPS_SYNC                         = 0x00B5,      //GPS同步时间

    CMD_SET_NET_RADIO_SOURCE             = 0x00B6,      //主机向终端设置网络电台音源
    CMD_NET_RADIO_STREAM                 = 0x00B7, 		//网络电台音频流

    // 分控服务器
    CMD_SERVER_NOTIFY_ONLINE      = 0xEEFD,         // 4.86 分控服务器在线通知
    CMD_SERVER_NOTIFY_PLAY_SOURCE = 0xEE01,         // 4.87 主机通知分控服务器播放节目源
    CMD_SERVER_GET_FILE_INFO      = 0xEE02,         // 4.88 主机向分控服务器查询文件信息
    CMD_SERVER_UPDATE_FILE        = 0xEE03,         // 4.89 主机向分控服务器请求更新文件
    CMD_SERVER_UPDATE_PROGRESS    = 0xEE04,         // 4.90 分控服务器歌曲文件同步进度上传
    CMD_SERVER_SET_IDLE           = 0xEE05,         // 4.91 切换分控服务器到空闲状态
    CMD_SERVER_SET_PLAYMODE       = 0xEE06,         // 4.92 主机向分控服务器查询/设置播放模式

    CMD_NOTIFY_HTTP_PLAY          = 0xEE07,         // 4.93 分控服务器通知终端播放节目源(HTTP方式)
    CMD_GET_NEXT_SONG             = 0xEE08,         // 4.94 终端向分控服务器请求播放下一曲(HTTP播放)

}ContrlCommand;

// GPS控制命令
typedef enum
{
    GPS_SEARCH_DEVICE			= 0xFF,			// 搜索GPS设备
    GPS_NOTIFY_DATETIME			= 0x01,			// GPS主动下发本地时间
    GPS_FIRMWARE_VERSION		= 0x06,			// 查询固件版本信息
    GPS_FIRMWARE_UPGRADE		= 0x07,			// 推送固件升级请求
    GPS_NOTIFY_PROGRESS_RATE	= 0x08,			// 固件升级进度
    GPS_GET_GPS_INFO			= 0x09,			// 获取GPS信息
    GPS_TIME_ZONE				= 0x0A,			// 设置时区信息

}GpsCommand;

typedef enum
{
    UFR_LATEST					= 0x01,			// 文件最新
    UFR_NOT_IDLE				= 0x02,			// 非空闲状态
    UFR_FAILURE					= 0x03,			// 同步失败
    UFR_SUCCESS					= 0x04,			// 同步成功

}UpdateFileResult;




/****************************************************************************/

// 网络化协议类
class CProtocol
{
public:
    CProtocol(void);
    ~CProtocol(void);

    // 常用方法
public:
    // 获取设备描述
    static CMyString	GetDescriptionDevice(DeviceModel model);

    // 获取节目源描述
    static CMyString	GetDescriptionProSource(ProgramSource src);

    // 获取播放状态描述
    static CMyString	GetDescriptionPlayStatus(PlayStatus status);

    // 获取播放模式描述
    static CMyString	GetDescriptionPlayMode(PlayMode mode);

    // 获取文件类型描述
    static CMyString	GetDescriptionFileType(FileType ft);

    // 得到文件更新结果描述
    static CMyString	GetDescriptionFileUpdateResult(unsigned char result);

    // 得到升级状态描述
    static CMyString	GetDescriptionUpgradeStatus(unsigned char status);

    // 得到SIP状态描述
    static CMyString	GetDescriptionSipStatus(SipStatus status);

    // 是否为音频采集音源
    static bool         IsAudioCollectorSrc(ProgramSource src);

    // 是否为点播、定时音源
    static bool         IsAudioPlayStreamSrc(ProgramSource src);

    // 是否为空闲音源
    static bool         IsIdleSrc(ProgramSource src);

    // 是否为本地音源
    static bool         IsLocalSrc(ProgramSource src);

    // 获取音频采集音源的channelId
    static int         GetAudioCollectorChannelBySrc(ProgramSource src);

    // 常用命令
public:
    // 获取校验码
    static char		GetChecksum(const char*	data,		// 数据内容
                                int		nLen);			// 数据长度

    // 构成控制命令
    static unsigned short	ControlCommand(	char*	buf,                // 命令缓存区
                                            unsigned short	cmd,        // 命令
                                            const char*	data,           // 数据内容
                                            unsigned short	uDataLen);	// 数据长度

    // 广播服务器信息
    static unsigned short	BroadcastHostInfo(char* buf);

    // 连接备用服务器
    static unsigned short	ConnectStandByServer(char* buf,             // 命令缓存区,
                                                unsigned char event,    // 事件event
                                                const char*	szMac,      // 需要连接的服务器MAC地址
                                                const char* sshPublicKey,   // 主服务器的ssh公钥
                                                const char* userName);   // 备用服务器的用户名

    // 查询在线设备
    static unsigned short	SearchOnlineDevice(char* buf);

    // 时间同步
    static unsigned short	SyncronizeTime(	char*	buf,		// 命令缓存区
                                            char*	szDate,		// 日期
                                            char*	szTime);	// 时间

    // 设置别名
    static unsigned short	SetName(char*	buf,				// 命令缓存区
                                    const char*	szName);			// 别名

    // 设置音量
    static unsigned short	SetVolume(	char*	buf,			// 命令缓存区
                                        char	volume);		// 音量

    // 设置子音量
    static unsigned short	SetSubVolume(	char*	buf,		// 命令缓存区
                                char     subVolume,                    // 子音量
                                char     auxVolume,                    // 本地音量
                                bool	bSet);    // 是否为设置
    
    // 控制设备音量加/减
    static unsigned short	SetVolumeAddMin(char*	buf,		// 命令缓存区
                                        bool isUp,              //是否为增加
                                        unsigned char step);    // 步进

    // 回应分控设备设置音量
    static unsigned short	ResponseSetVolume( char*	buf);	// 命令缓存区

    // 查询状态
    static unsigned short	GetStatus(char*	buf);

    // 固件升级
    static unsigned short	UpgradeFirmware(char*	buf,            // 命令缓存区
                                            char	model,          // 设备型号
                                            const char*	szVersion,	// 版本号
                                            const char*	szHostIP,	// 服务IP地址
                                            unsigned short	uPort,	// HTTP端口
                                            const char*	szPath,
                                            const char* md5);	// 文件路径

    // 向设备点播主机上的歌曲
    static unsigned short	Webcasting(	char*	buf,			// 命令缓存区
                                        const char*	url);		// 歌曲URL

    // 播放状态设置
    static unsigned short	SetPlayStatus(	char*	buf,		// 命令缓存区
                                            char	status);	// 播放状态值

    // 回应 播放状态设置
    static unsigned short   ResponseSetPlayStatus(char* buf);   // 命令缓存区

    // 静音状态设置
    static unsigned short	SetMuteStatus(	char*	buf,		// 命令缓存区
                                            char	status);	// 静音值

    // 回应分控设备静音状态设置
    static unsigned short	ResponseSetMuteStatus(	char*	buf);// 命令缓存区

    // 程控模式设置
    static unsigned short	SetControlMode(	char*	buf,		// 命令缓存区
                                            char	mode);		// 控制模式

    // 回应分控设备程控模式设置
    static unsigned short	ResponseSetControlMode(	char*	buf);// 命令缓存区

    // 获取文件信息
    static unsigned short	GetFileInfo(char*	buf,			// 命令缓存区
                                        char	fileType);		// 文件类型

    // 更新文件
    static unsigned short	UpdateFile(	char*	buf,                // 命令缓存区
                                        char	fileType,           // 文件类型
                                        const char*	szDateTime,		// 文件更新日期时间
                                        const char*	szHostIP,		// 服务IP地址
                                        unsigned short	uPort,		// HTTP端口
                                        const char*	szPath);		// 文件路径

    static unsigned short	UpdateFile(	char*	buf,                // 命令缓存区
                                        char	fileType,           // 文件类型
                                        const char*	szDateTime,		// 文件更新日期时间
                                        const char*	szHostIP,		// 服务IP地址
                                        unsigned short	uPort,		// HTTP端口
                                        const char*	szPath,			// 文件路径
                                        const char*	szServerIP,		// 歌曲服务器IP地址
                                        unsigned short	uServerPort);	// 歌曲服务器端口


    // 主动请请求文件信息
    static unsigned short	RequestGetFileInfo(	char*	buf,                // 命令缓存区
                                                char	fileType,           // 文件类型
                                                const char*	szDateTime,		// 文件更新日期时间
                                                const char*	szHostIP,		// 服务IP地址
                                                unsigned short	uPort,		// HTTP端口
                                                const char*	szPath);		// 文件路径

    // 响应终端同步歌曲文件的请求
    static unsigned short	ResponseSyncRequest(char*	buf,	// 命令缓存区
                                                char	res);	// 0x01 接受，0x10 拒绝

    // 中止同步歌曲文件
    static unsigned short	StopSyncSongFile(char*	buf);

    // 设置钟声
    static unsigned short	SetRing(char*	buf,				// 命令缓存区
                                    const char*	szListID,		// 播放列表ID
                                    const char*	szSongName);	// 歌曲名称

    // 播放钟声
    static unsigned short	PlayRing(char*	buf);				// 命令缓存区

    // 回应分控设备播放钟声
    static unsigned short	ResponsePlayRing(char*	buf,		// 命令缓存区
                                             char	result);	// 播放结果


    // 播放本地歌曲
    static unsigned short	PlayLocalSong(char*	buf,                // 命令缓存区
                                          const char*	szListID,	// 播放列表ID
                                          const char*	szSongName);// 歌曲名称

    // 回应分控设备播放本地歌曲
    static unsigned short	ResponsePlayLocalSong(char*	buf,        // 命令缓存区
                                                  char	result);    // 播放结果

    // 播放模式设置
    static unsigned short	SetPlayMode(char*	buf,		// 命令缓存区
                                        char	mode);		// 播放模式

    // 回应分控设备播放模式查询/设置
    static unsigned short	ResponsePlayMode(char*	buf,		// 命令缓存区
                                             char	mode);		// 播放模式（负数为设置，其它为查询）

    // 切换到空闲状态
    static unsigned short	SetIdleStatus(char*	buf);           // 命令缓存区

    // 主机请求终端重新分配MAC地址
    static unsigned short	ReassignMac(char*	buf);			// 命令缓存区

    // 主机向设备请求重启
    static unsigned short	Reboot(char*	buf);				// 命令缓存区

    // 主机请求终端重置数据
    static unsigned short	ResetData(char *buf,				// 命令缓存区
                                      char	type);				// 数据类型

    // 查询FLASH信息
    static unsigned short	GetFlashInfo(char*	buf);

    // 获取设备日期时间
    static unsigned short	GetDateTime(char*	buf);			// 命令缓存区

    // 主机向控制设备发送终端文件信息
    static unsigned short	SendFileInfo(	char*	buf,            // 命令缓存区
                                            const char*	szMac,		// 终端MAC地址
                                            char	fileType,       // 文件类型
                                            const char*	szDateTime);// 文件更新日期时间

    // 4.46 主机向终端设置警报声
    static unsigned short	SetAlarmSound	(char*	buf,                // 命令缓存区
                                             unsigned char	channelID,	// 通道ID
                                             const char*	szListID,	// 播放列表ID
                                             const char*	szName);	// 警报声名称

    // 4.47 主机向终端设置开启/关闭警报
    static unsigned short	SetAlarmSwitch(	char*	buf,                // 命令缓存区
                                            unsigned char	channelID,	// 通道ID
                                            unsigned char	switcher,	// 警报开关
                                            const char*	szListID,       // 播放列表ID
                                            const char*	szName);        // 警报声名称

    // 4.48主机向消防采集器查询通道触发状态
    static unsigned short	GetAlarmState(char*	buf);			// 命令缓存区

    // 4.49 主机向消防采集器设置查询/触发模式
    static unsigned short	AlarmMode(	char*	buf,                // 命令缓存区
                                        unsigned char	channels,	// 通道数，0xFF为查询
                                        int		mode);              // 触发模式

    // 4.50 主机向音频采集器/终端设置音频采集音源
    static unsigned short	SetAudioInfo(	char*	buf,		// 命令缓存区
                                            std::shared_ptr<CAudioCollector> pAudioCollector,  // 采集器
                                            unsigned char channel,
                                            bool IsTiming=false,        //是否定时
                                            unsigned char timingVolume=0xff,   //定时音量
                                            bool IsTrigger=false,        //是否触发
                                            unsigned char triggerVolume=0xff);   //触发音量

    // 主机向混音器/终端设置混音音源
    static unsigned short	SetAudioMixerSourceInfo(	char*	buf,		// 命令缓存区
                                                    const char*   mixerDeviceMac, //混音器MAC
                                                    unsigned char eventType,       //事件类型（1为启动，0为停止）
                                                    unsigned char   mixerPriority,     //混音器优先级
                                                    unsigned char   mixerVolume,       //混音器音量
                                                    unsigned int    sampleRate,        //采样率
                                                    unsigned char   fmt,               //采样精度
                                                    unsigned char   channelNum,        //声道数
                                                    char    *broadcast_addr,           //组播地址
                                                    unsigned int     broadcast_port,   //组播端口
                                                    unsigned char    audioCodecs      //音频编码
                                                  );

        // 主机向电话网关/终端设置混音音源
    static unsigned short	SetPhoneGatewaySourceInfo(	char*	buf,		// 命令缓存区
                                                    const char*   phoneGatewayDevice, //电话网关MAC
                                                    unsigned char eventType,       //事件类型（1为启动，0为停止）
                                                    unsigned char   mixerVolume,       //电话网关音量
                                                    unsigned int    sampleRate,        //采样率
                                                    unsigned char   fmt,               //采样精度
                                                    unsigned char   channelNum,        //声道数
                                                    char    *broadcast_addr,           //组播地址
                                                    unsigned int     broadcast_port,   //组播端口
                                                    unsigned char    audioCodecs      //音频编码
                                                  );
    
    static unsigned short	SetNetRadioSourceInfo(char*	buf,		// 命令缓存区
                                                const char *radioName,        //电台名称
                                                char *sessionId,        //电台的sessionId
                                                unsigned int    sampleRate,        //采样率
                                                unsigned char   fmt,               //采样精度
                                                unsigned char   channelNum,        //声道数
                                                char    *broadcast_addr,           //组播地址
                                                unsigned int     broadcast_port,   //组播端口
                                                bool IsTiming,                     //是否定时
                                                unsigned char volume);       //音量

    // 回应分控设备设置音频采集器音源
    static unsigned short	ResponseSetAudioInfo(char*	buf,	// 命令缓存区
                                                 char result);	// 结果

    static unsigned short	SequencePowerInfo(char*	buf,
                                          std::shared_ptr<CSequencePower> pSequencePwrInfo, // 电源时序器信息
                                          bool	bSet);          // 是否为设置
    // 主机向电源时序器发送定时点信息
    static unsigned short	SequencePowerTimingInfo(char*	buf,
                                         std::shared_ptr<CSequencePower> pSequencePwrInfo, // 电源时序器信息
                                          const char *deviceMac,
                                          bool IsQuickResponse); // mac地址

    // 4.51 主机向终端设置网络模式
    static unsigned short	SetNetworkMode(	char*	buf,                 // 命令缓存区
                                            bool	bSet,                   // 是否为设置
                                            unsigned char	mode,		 // 网络模式
                                            const char*	szServerIP,      // 服务器IP
                                            unsigned short	nServerPort,// 服务器端口
                                            const char*	szServerIP2,      // 备用服务器IP
                                            unsigned short	nServerPort2);// 备用服务器端口);

    // 4.52 主机向终端查询/设置IP属性
    static unsigned short	NetworkInfo(	char*	buf,		// 命令缓存区
                                                                CNetworkInfo *pNetInfo, // 网络信息
                                                                bool	bSet = false);		// 是否为设置


    // 4.53 主机向设备获取记录文件列表
    static unsigned short	GetLogFileList(	char*	buf);		// 命令缓存区

    // 4.54 主机向设备获取记录文件内容
    static unsigned short	GetLogFileData(	char*	buf,		// 命令缓存区
                                            const char*	szFileName,	// 文件名称
                                            unsigned short	packID);	// 包ID

    // 4.55 主机向采集器主动发送终端音源选择状态
    static unsigned short	SendAudioState(	char*	buf,		// 命令缓存区
                                            bool	bUsed,		// 是否被选择
                                            unsigned char channelSelect);

    // 4.56 主机向其他控制设备下发音频采集器设备列表
    static unsigned short	SendAudioList(	char*	buf,		// 命令缓存区
                                            char*	data,		// 音源列表数据
                                            unsigned short	len);		// 数据长度

    // 4.58 主机向网络解码播放器查询/设置电源输出模式
    static unsigned short	PowerOutputMode(char*	buf,			// 命令缓存区
                                            unsigned char	mode = 0,		// 电源输出模式，为0时，是查询
                                            unsigned short	timteout = 0);	// 超时时间

    // 4.59 主机向网络解码播放器查询回路检测状态
    static	unsigned short	SigalDetection(char*	buf);			// 命令缓存区

    // 4.61 主机向终端查询/设置EQ音效
    static	unsigned short	DeviceEq(	char*	buf,				// 命令缓存区
                                        const char* szMac,               // MAC
                                        char	mode = -1,			// EQ模式，为0时，是查询
                                        unsigned char gain[10]={0});	// 低音值

    // 5.0.0 主机向终端查询/设置蓝牙参数
    static unsigned short	BlueToothInfo(char*	buf,			// 命令缓存区
                            const char* szMac,               // MAC
                            const char*	btName =NULL,					        // 蓝牙名称，为NULL时为查询
                            unsigned char btencryption=0,             // 蓝牙加密方式
                            const char*   btPin=NULL);			                // 蓝牙密码

    // 主机向终端查询/设置对讲基础配置
    static unsigned short	CallBasicConfig(char*	buf,			// 命令缓存区
                                            CALLDEVICECONFIG *callBasicConfig);

    // 4.61 主机向终端查询/设置触发参数
    static unsigned short	TriggerConfig(char*	buf,			    // 命令缓存区
                                        char trigger_switch,
                                        char trigger_mode,
                                        const char *trigger_songName,
                                        const char *trigger_songMd5,
                                        int playTimes,
                                        int volume);

    #if SUPPORT_AUDIO_MIXER
    // 查询/设置音频混音器参数
    static unsigned short	AudioMixerConfig(char*	buf,		    // 命令缓存区
                                            bool    bSet,               // 0:查询 1:设置
                                            int nMasterSwitch,          // 混音主开关（0关闭 1开启）
                                            int nPriority,              // 优先级
                                            int nTriggerType,           // 触发类型
                                            int nTriggerSensitivity,    // 触发灵敏度
                                            int nVolumeFadeLevel,       // 音量淡化级别
                                            int nZoneVolume);            // 分区音量
    #endif


    // 查询/设置信息发布参数
    static unsigned short	InformationPublish(char*	buf,		    // 命令缓存区
                                        bool	bSet,               // 是否为设置
                                        bool bEnableDisplay,           // 是否启用显示
                                        string strText,                // 输出文本
                                        int nEffects,                  // 特效
                                        int nMoveSpeed,                // 移动速度
                                        int nStayTime);                 // 停留时间
    
    #if SUPPORT_PHONE_GATEWAY
    // 查询/设置电话网关参数
    static unsigned short	PhoneGatewayConfig(char*	buf,		    // 命令缓存区
                                            bool    bSet,               // 0:查询 1:设置
                                            int nMasterSwitch,          // 电话网关主开关（0关闭 1开启）
                                            int nZoneVolume,            // 分区音量
                                            string telWhitelist ); // 电话白名单
    #endif

    #if SUPPORT_AMP_CONTROLER
    static unsigned short	AmpControlerConfig(char*	buf,		    // 命令缓存区
                                        bool    bSet               // 0:查询 1:设置
                                         );
    #endif

    #if SUPPORT_NOISE_DETECTOR
    static unsigned short	NoiseDetectorConfig(char*	buf,		    // 命令缓存区
                                        bool    bSet,               // 0:查询 1:设置
                                        CNoiseDetector &noiseDetector
                                         );
    #endif

    // 查询/设置音频采集器参数
    static unsigned short	AudioCollectorConfig(char*	buf,		    // 命令缓存区
                                            bool    bSet,               // 0:查询 1:设置
                                            int nTriggerSwitch,          // 触发开关（0关闭 1开启）
                                            int nTriggerChannelId,       // 触发通道ID
                                            int nTriggerZoneVolume);      // 触发分区音量

    // 寻呼台获取账户存储容量
    static unsigned short	AccountStorageCapacity(char*	buf,	        // 命令缓存区
                                        const char*	szAccount,		        // 账号,
                                        UINT64 storage_capacity,               // 总存储容量
                                        UINT64 storage_used,                   // 已用存储空间
                                        UINT64 storage_remaining,              //剩余存储空间
                                        int compress_bitrate);             //压缩比特率(kbps)
    // 寻呼台请求上传歌曲
    static unsigned short	RequestUploadSong(char*	buf,	        // 命令缓存区
                                        int result,
                                        const char*	szHostIP,	// 服务IP地址
                                        unsigned short	uPort,	// HTTP端口
                                        const char* uploadUrl             //上传地址（cgi）
                                        );
    // 寻呼台通知服务器上传歌曲状态
    static unsigned short	UploadSongStatus(char*	buf,	        // 命令缓存区
                                        int result
                                        );
    // 寻呼台请求删除本地歌曲                                    
    static unsigned short	RequestDeleteSong(char*	buf,	        // 命令缓存区
                                        int result                 //返回值
                                        );

    // 控制寻呼台发起广播寻呼
    static unsigned short	SetBroadcastPaging(char*	buf,			    // 命令缓存区
                            const char* szMac,                      // MAC
                            const char*	szAccount,						// 用户名
                            int control_event,                          //控制类别
                            int isAllZone,                              //该用户的全部分区设备
                            vector<string> &zoneMacs);                    //分区设备集合

    // 4.62 消防采集器通道触发状态改变时，主动通知主机
    static	unsigned short	ResponseAlarmStateChanged(char*	buf);	// 命令缓存区

    // 4.66主机向终端查询/设置混音模式
    static	unsigned short	MixingMode(	char*	buf,				// 命令缓存区
                                        char	channel = -1,		// channel，为-1时，是查询，0双声道 ，1为单声道
                                        unsigned char	auxVol = 6,	// 线路音量增益
                                        unsigned char	mixing = 0,	// 0：混音关，1：DAC双声道混合输出，2：AUX混合DAC输出
                                        unsigned char	dacVol = 8);// 数字音量增益

    // 4.68 主机向设备请求重启（组播）
    static unsigned short	RebootMulticast(char*	buf,			// 命令缓存区
                                            const char*	data,		// 数据
                                            int		len);			// 数据长度

    // 4.70 回应终端被寻呼通知(TCP模式)
    static	unsigned short	ResponseNotifyPaging(char*	buf);		// 命令缓存区

    // 4.72 回应寻呼台向终端发送掉线再次寻呼指令（TCP模式）
    static	unsigned short	ResponsePagingAgain(char*	buf);		// 命令缓存区

    // 4.75查询/设置网络解码分区器输出状态
    static	unsigned short	SplitterStatus(char*	buf,			// 命令缓存区
                                           short	status = -1);	// 状态，为负数表示查询

    // 4.76 查询/设置解码功率分区器、数字功放EMC状态
    static	unsigned short	EmcStatus(char*	buf,					// 命令缓存区
                                      short	status = -1);			// 状态，为负数表示查询

    // 4.97查询/设置 DSP6636无线MIC控制面板状态
    static	unsigned short	MicStatus(char*	buf,					// 命令缓存区
                                      char          volume = -1,	// 音量：为负数表示查询，0-16
                                      unsigned char	power = 1,		// 发射功率 1-3
                                      unsigned char	channel = 1);	// 频道：1-99

/*
// 监控 zhuyg
    // 4.77 主机下发监控设备的信息
    static  unsigned short  DistributeMonitorInfo(char*        buf,         // 命令缓存区
                                                  u_char       uChannel,    // 通道号（代表摄像头的编号）
                                                  const char*  szAccount,   // 登录账户
                                                  const char*  szPassword,  // 登录密码
                                                  const char*  szIP,        // IP地址
                                                  ushort       uPort,       // 端口号
                                                  const char*  szRTSP);     // RTSP地址

    // 4.79 主机下发监控设备的上报事件
    static unsigned short  DistributeMonitorEvent(char*    buf,         // 命令缓存区
                                                  u_char   uChannel,    // 通道号（代表摄像头的编号）
                                                  u_char   uEventType,  // 事件类型
                                                  const char* szTime,   // 时间
                                                  char direction);      // 入侵方向
*/

    //  4.77 主机通知终端接收寻呼（SIP）
    static  unsigned short  NotifyDevicePaging(char*   buf,              // 命令缓存区
                                               const   char*  szMulIP,   // 组播IP
                                               unsigned short  uPort,            // 端口
                                               int     nRtpType,         // Rtp负载类型
                                               int     nVolume);         // 音量

    // 4.84 主机通知终端发起寻呼（SIP）
    static  unsigned short  NotifyStartPaging(char* buf,                    // 命令缓存区
                                              int      nPageType = 0x01);   // 寻呼类型 0x01 语音,0x02 视频

    // 4.4 终端寻呼通知(UDP模式)
    static   unsigned short  ForwarePaging(char* buf,               // 命令缓存区
                                                                       int      nRef = 0x08);  // 0x08 接受寻呼, 0x80 取消寻呼

    // 4.85 主机向终端查询网络质量
    static    unsigned short QueryDeviceNetQuality(char *buf,           // 命令缓存区
                                                   int   nNetType,      // 网络连接测试 0x00 : UDP单播, 0x01: UDP组播, 0x02 : TCP
                                                   const char* szIP,    // 查询IP
                                                   unsigned short  uPort);      // 查询端口

    // 4.93 分控服务器通知终端播放节目源(HTTP方式)
    static unsigned short   NotifyHTTPPlaySong(char* buf,               // 命令缓存区
                                               char	 source,            // 音源
                                               const char*	szListID,	// 播放列表ID
                                               const char*	szSongPath, // 歌曲名称
                                               unsigned char volume);	// 定时音量

 

    // 集中模式
public:
    // 4.41 设置工作模式
    static	unsigned short	SetWorkPattern(	char*	buf,		// 命令缓存区
                                            char	pattern);	// 工作模式

    // 4.42 回应寻呼站/移动设备请求主机播放节目源（集中模式）
    static unsigned short	ResponsePlaySource(	char*	buf,		// 命令缓存区
                                                unsigned char	selSecID,	// 选中分区ID
                                                unsigned char	result);	// 播放结果

    // 4.43 主机通知终端播放节目源（集中模式）
    static  unsigned short	NotifyStreamSource(	char*	buf,		// 命令缓存区
                                                char	source,		// 音源
                                                char	format,		// 歌曲格式
                                                unsigned int	sampleRate,	// 采样率
                                                unsigned char	bitSample,	// 采样精度
                                                unsigned char	channels,	// 声道数
                                                const char*	szName,         // 歌曲名称
                                                const char*	szMultiIP,      // 组播IP
                                                unsigned short	port,		// 组播端口
                                                unsigned char	volume,     // 定时音量    
                                                unsigned int  fileLength,   // 歌曲文件大小
                                                unsigned int totalFramesCnt,// 总帧数
                                                unsigned int  currentFrame,     //当前帧数
                                                const char* fileMd5,
                                                bool isUseMulticastNewCmd);	// MD5

    // 4.44 播放节目源数据流传输（TCP下的集中模式的单播）
    static  unsigned short	StreamSource(char*	buf,				// 命令缓存区
                                         const char*	data,		// 数据流
                                         int	len);				// 数据流长度

    // 4.57音频采集器音频数据传输
    static   unsigned short   StreamAudioCollect(char*	buf,				// 命令缓存区
                                                 const char*	data,		// 数据流
                                                 int	len);				// 数据流长度

    // 4.60 寻呼台/移动设备/分控设备向主机发送选中的分区（集中模式）
    static unsigned short	ResponseSelectedSections(char*	buf,                // 命令缓存区
                                                     unsigned char	selSecID,	// 选中分区ID
                                                     unsigned char	packID);	// 包ID

    // 4.67 主机向分控设备下发终端当前状态
    static unsigned short	SectionStatus(	char*	buf,                // 命令缓存区
                                            char*	szMac,              // MAC地址
                                            unsigned char	volume,		// 音量
                                            unsigned char	source,		// 节目源
                                            unsigned char	playStatus,	// 播放状态
                                            const char*	szProName,		// 节目名称
                                            unsigned char	timerStatus,// 定时点状态
                                            unsigned char	deviceFeature,// 设备特性
                                            unsigned char	syncStaus);	// 同步状态
 #if SUPPORT_PAGER_CALL
    //构造分控设备状态
    static unsigned short	PagerStatus(char*	buf,                    // 命令缓存区
                                         const char*	szMac,          // MAC地址
                                         unsigned char	source,		    // 节目源
                                         bool isSupportCall,            // 是否支持对讲
                                         unsigned char	isSupportVideo, // 是否支持可视
                                         unsigned char  reserve);       // 保留
#endif
    // 4.67主机向分控设备下发终端当前状态
    static unsigned short	ForwardStatus(	char*	buf,			// 命令缓存区
                                            const char*	data,		// 数据
                                            int		len);			// 数据长度

    // 4.69 主机向分控设备发送音频采集器列表在线/离线状态
    static unsigned short	ForwardAudioCollectors(	char*	buf,        // 命令缓存区
                                                    const char*	data,	// 数据
                                                    int		len);       // 数据长度

    // 4.73 查询/设置音频传输方式
    static unsigned short	AudiocastMode(char*	buf,				// 命令缓存区
                                          char	mode);				// 传输方式

    // SIP相关命令
public:
    // 获取SIP会话状态
    static unsigned short	SipGetStatus(char*	buf);			// 命令缓存区

    // SIP账号登录
    static unsigned short	SipLogIn(	char*	buf,                // 命令缓存区
                                        bool isEnableSip,                            //是否启用SIP
                                        unsigned char outPutVolume,              // 输出音量
                                        const char*	szAccount,		// 登录账号
                                        const char*	szPassword,		// 登录密码
                                        const char*	szServerAddr,	// 服务器地址
                                        int		nServerPort,        // 端口
                                        int     nServerProtocol);	// 协议

    // 获取SIP登录账号信息
    static unsigned short	 SipGetLogInfo(char*	buf);			// 命令缓存区

#if SUPPORT_WEB_PAGING
    //web寻呼指令
public:
    //5.16 服务器发起广播寻呼
    static unsigned short   ServerWebPagingNotify(char*buf,CWebPaging &webpPaging,int event, AudioAlgorithm audioCodecs = ALGORITHM_722);
#endif

    //主机通知解码终端即将进入定时音源
    static  unsigned short NotifyDecoderTiming(char*    buf);				// 命令缓存区

public:
    static char  cmdSequence;  // 命令序号
};



#endif // PROTOCOL_H
