#include "stdafx.h"
#include "CommandQueue.h"
#include "Protocol.h"
#include "Global/CType.h"

static CCommandQueue*   s_CommandQueue;

CProtocolCommand::CProtocolCommand()
{
    m_nLen = 0;
    memset(m_szIP, 0, sizeof(char)*MAX_IP_LEN);
    memset(m_szBuf, 0,sizeof(char)*MAX_BUF_LEN);
    m_nRepeatCount = 0;
    m_uCommand = 0;

    m_nPort = 0;
}

/*
CProtocolCommand::CProtocolCommand(const CProtocolCommand& proCmd)
{
    memcpy(m_szBuf, proCmd.m_szBuf, proCmd.m_nLen);
    m_nLen = proCmd.m_nLen;
    strcpy(m_szIP, proCmd.m_szIP);
    m_uCommand = proCmd.m_uCommand;
    m_nRepeatCount = 0;

    m_nPort = proCmd.m_nPort;
    m_pSocket = proCmd.m_pSocket;
}
*/
CProtocolCommand::CProtocolCommand(unsigned short uCmd, const char* szBuf, int nLen, const char* szIP)
{
    memcpy(m_szBuf, szBuf, nLen);
    m_nLen = nLen;
    strcpy(m_szIP, szIP);
    m_nRepeatCount = 0;
    m_uCommand = uCmd;

}


CProtocolCommand::~CProtocolCommand(void)
{

}


/**************************************************************/

CCommandQueue::CCommandQueue(void)
{
    m_pSocket = NULL;
    s_CommandQueue = this;

    m_csCommand = PTHREAD_MUTEX_INITIALIZER;
    m_csSendCmd = PTHREAD_MUTEX_INITIALIZER;
}


CCommandQueue::~CCommandQueue(void)
{
    //20220427 静态初始化的互斥锁不需要,也不能用pthread_mutex_destroy销毁锁，否则将出错
    //pthread_mutex_destroy(&m_csCommand);
    //pthread_mutex_destroy(&m_csSendCmd);
}


void	CCommandQueue::StartWorking(CUDPSocket* pSocket)
{
    m_pSocket = pSocket;

    pthread_t pthCommand;
    pthread_t pthSendCmd;

    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&pthCommand, &attr, CommandThread, (void*)this);
    //pthread_create(&pthSendCmd, &attr, SendCmdThread, (void*)this);
    pthread_attr_destroy(&attr);
}


// 添加到命令到队列
bool CCommandQueue::AddCommand(char* szBuf, int nLen, const char* szIP)
{
    bool	flag	= true;

    pthread_mutex_lock(&m_csCommand);

    unsigned short	uCmd	= CharsToShort(&szBuf[0]);

    list<CProtocolCommand>::iterator iter;
    for(iter=m_listCommands.begin();iter!=m_listCommands.end();++iter)
    {
        if (iter->m_uCommand == uCmd && strcmp(iter->m_szIP, szIP) == 0)
        {
            flag = false;
            break;
        }
    }

    if (flag)
    {
        CProtocolCommand command(uCmd, szBuf, nLen, szIP);
        m_listCommands.push_back(command);
    }

    pthread_mutex_unlock(&m_csCommand);

    return flag;
}


bool CCommandQueue::RemoveCommand(unsigned short uCmd, const char* szIP)
{
    bool flag = FALSE;

    pthread_mutex_lock(&m_csCommand);

    list<CProtocolCommand>::iterator iter;

    for(iter=m_listCommands.begin();iter!=m_listCommands.end();++iter)
    {
        if (iter->m_uCommand == uCmd && (strcmp(iter->m_szIP, szIP) == 0))
        {
            m_listCommands.erase(iter);
            flag = true;
            break;
        }
    }

    pthread_mutex_unlock(&m_csCommand);

    return flag;
}

void CCommandQueue::HandleCommand(void)
{
    pthread_mutex_lock(&m_csCommand);
    list<CProtocolCommand>::iterator iter;

    for(iter=m_listCommands.begin();iter!=m_listCommands.end();)
    {
        unsigned short	uCmd = CharsToShort(&iter->m_szBuf[0]);
        if (iter->m_nRepeatCount >= MAX_REPEAT_COUNT)
        {
            if(iter->m_nRepeatCount>=MAX_REPEAT_COUNT*2)    //异常数据
            {
                printf("iter->m_nRepeatCount>=MAX_REPEAT_COUNT*2...\n");
                iter++;
                continue;
            }
            //////////////////////////////////////////////////////////////////////////
#if 0
            CSection* pDevice = g_Global.m_Network.m_CmdHandle.GetOnlineDeviceByIP(iter->m_szIP);
            //  如果分区对控制连续MAX_REPEAT_COUNT不回应，则发送重启的命令
            if (pDevice != NULL && pDevice->IsSectionDevice())
            {
                if (   uCmd == CMD_DEVICE_VOLUME || uCmd == CMD_IDLE_STATUS
                    || uCmd == CMD_GET_FILE_INFO || uCmd == CMD_CONTROL_MODE
                    || uCmd == CMD_PLAY_RING	 || uCmd == CMD_PLAY_LOCAL_SONG
                    || uCmd == CMD_MUTE_STATUS	 || uCmd == CMD_NOTIFY_STREAM_SOURCE)
                {
                    g_Global.m_Network.m_CmdSend.CmdReboot(*pDevice, RESTART_PORT);
                }
            }
#endif
            //////////////////////////////////////////////////////////////////////////

            //这里“++”运算符与我们平常的理解刚好相反，erase( itList++) 是先获取下一个元素的位置在删除； erase( ++itList) 是删除以后再获取下一个元素的位置。
            iter=m_listCommands.erase(iter);  
        }
        else 
        {
            #if 0
            if (m_pSocket != NULL)
            {
                if(iter->m_nRepeatCount >=1)    //ADD 不要马上重发，隔几秒再重发
                {
                    m_pSocket->SendData(iter->m_szBuf, iter->m_nLen, iter->m_szIP, UDP_PORT, 1);
                    printf("cmd=0x%04x:repeat send!\n",uCmd);
                }
                else
                {
                    //printf("cmd=0x%04x:repeatCount=0!\n",uCmd);
                }
                iter->m_nRepeatCount++;
            }
            iter++;
            #endif


            if(iter->m_nRepeatCount >=1)    //ADD 不要马上重发，隔几秒再重发
            {
               //g_Global.m_Network.m_MySocketUnicast.SendData(iter->m_szBuf, iter->m_nLen, iter->m_szIP, UDP_PORT, 1);
                g_Global.m_Network.SendUdpData(iter->m_szBuf, iter->m_nLen, iter->m_szIP, UDP_PORT, 1);
                printf("cmd=0x%04x:repeat send!\n",uCmd);
            }
            else
            {
                //printf("cmd=0x%04x:repeatCount=0!\n",uCmd);
            }
            iter->m_nRepeatCount++;     
            iter++;
        }
    }
    pthread_mutex_unlock(&m_csCommand);
}


BOOL CCommandQueue::AddSendCmd( char*		data,			// 数据
                                int			len,			// 数据长度
                                CUDPSocket* pSocket,        // socket
                                const char*	szIP,			// 目标IP（UDP）
                                int			port,			// 端口（UDP）
                                int			repeat)         // 重复次数
{
    BOOL	flag	= TRUE;

    pthread_mutex_lock(&m_csSendCmd);

    CProtocolCommand cmdSend;
    memcpy(cmdSend.m_szBuf, data, len);
    cmdSend.m_nLen = len;
    cmdSend.m_pSocket = pSocket;
    strcpy(cmdSend.m_szIP, szIP);
    cmdSend.m_nPort = port;
    cmdSend.m_nRepeatCount = repeat;

    m_listSendCmd.push_back(cmdSend);

    pthread_mutex_unlock(&m_csSendCmd);

    return flag;
}

void	CCommandQueue::HandleSendCmd(void)
{
#if defined(Q_OS_LINUX)
    int nCount = 0;

    pthread_mutex_lock(&m_csSendCmd);

    list<CProtocolCommand>::iterator iter = m_listSendCmd.begin();
    for(; iter != m_listSendCmd.end(); )
    {
        /* 这样发送会失败
        g_Global.m_Network.SendData(iter->m_szBuf, iter->m_nLen, *(iter->m_pDevice),
                                    iter->m_szIP, iter->m_nPort, iter->m_nRepeatCount);
        */
        if (iter->m_pSocket != NULL)
        {
            iter->m_pSocket->SendData(iter->m_szBuf, iter->m_nLen, iter->m_szIP, iter->m_nPort, 1);
        }

        int cmd_word = iter->m_szBuf[0]*256 + iter->m_szBuf[1];
        printf("send_cmd:0x%04x\n",cmd_word);

        iter=m_listSendCmd.erase(iter);

        nCount++;
        if(nCount >= 10)   // 超过限制发送次数等到下次定时再发送
        {
            break;
        }
    }

    pthread_mutex_unlock(&m_csSendCmd);
#endif
}

void* CCommandQueue::CommandThread(void *lpParam)
{
    while(1)
    {
        usleep(SPAN_REPEAT_SEND * 1000);

        s_CommandQueue->HandleCommand();
    }

}

void* CCommandQueue::SendCmdThread(void* lpParam)
{
    while(1)
    {
        usleep(SPAN_COMMAND_SEND * 1000);

        s_CommandQueue->HandleSendCmd();
    }
}




