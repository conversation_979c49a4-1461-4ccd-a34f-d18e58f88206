#ifndef COMMANDHANDLE_H
#define COMMANDHANDLE_H

#include "Tools/tools.h"
#include "Protocol.h"

#include <QMutex>
#include <QMutexLocker>
#include <vector>

class CSection;
class CNetwork;


class CCommandHandle
{
public:
    CCommandHandle(void);
    ~CCommandHandle(void);
    void	SetNetwork(CNetwork* pNetwork);

    // 根据设备IP查找在线设备
    CSection*	GetOnlineDeviceByIP(const char* szIP);
    // 根据设备型号和设备IP查找在线设备
    CSection*   GetOnlineDeviceByIPAndModel(const char *szIP, DeviceModel nModel);
    // 根据tcp连接socket获取在线设备
    CSection*	GetOnlineDeviceBySockObj(LPS_SOCKET_OBJ SockObj);
    // 根据设备型号和tcp连接socket查找在线设备
    CSection*   GetOnlineDeviceBySockObjAndModel(LPS_SOCKET_OBJ SockObj, DeviceModel nModel);
    static CSection*	GetOnlineDeviceByMac(const char* szMac);

    // 分区节目源改变
    void	SectionChangedSource(CSection& section, ProgramSource newSrc);

public:
    // 处理命令
    void	HandleControlCommand(	const char*	pData,				// 数据区
                                    unsigned short	nLen,			// 数据长度
                                    const char*	szIP,				// IP地址
                                    unsigned short	uPort,			// 端口（UDP用得到，TCP可以直接设置为0）
                                    LPS_SOCKET_OBJ sockobj = NULL); // 为NULL时是UDP，否则为TCP

    // 处理分控设备需要转发的命令
    void	HandleForwardControlDevice(	unsigned short		command,	// 命令
                                        const char*		pData,			// 命令缓冲区
                                        int			nLen,			// 数据长度
                                        CSection&	ctrlDevice);	// 分控设备

    // 处理寻呼设备需要转发的命令
    void	HandleForwardPagingDevice(	unsigned short		command,	// 命令
                                        const char*		pData,			// 命令缓冲区
                                        int			nLen,			// 数据长度
                                        CSection&	pagingDevice);	// 寻呼设备

    // 处理查找在线设备命令
    void	HandleSearchOnlineDevice(	const char*		pData,		// 数据区
                                        unsigned short	nLen,       // 数据长度
                                        const char*		szIP,		// IP地址
                                        DeviceModel	nModel,         // 设备型号
                                        unsigned char		nReserved,	// 保留字
                                        LPS_SOCKET_OBJ sockobj = NULL); // 为NULL时是UDP，否则为TCP

    // 处理查找分区设备
    CSection*  HandleSearchSectionDevice(char* szName, char* szMac, unsigned char vol, ProgramSource src, char* szProName, PlayStatus playStatus,
        bool isTimerInvalid, char* szVersion, const char* szIP, DeviceModel	nModel, bool& isNewOnline, BYTE nReservedWord, BYTE szDeviceFeature,
        unsigned char module4G_signal_rssi,char* module4G_iccid,char *loginUser,bool audioMixerSingalValid,bool audioCollectorSignalValid,bool phoneGatewaySingalValid,
        int nExtraFeature);

    // 处理查找其它设备
    CSection*  HandleSearchOtherDevice(char* szName, char* szMac, unsigned char vol, ProgramSource src, char* szProName, PlayStatus playStatus,
        bool isTimerInvalid, char* szVersion, const char* szIP, DeviceModel	nModel, bool& isNewOnline,BYTE nReservedWord, BYTE szDeviceFeature,
        unsigned char module4G_signal_rssi,char* module4G_iccid,char *loginUser,bool audioMixerSingalValid,bool audioCollectorSignalValid,bool phoneGatewaySingalValid,
        int nExtraFeature);

    // 处理上线恢复播放
    void	HandleOnlineResumePlaying(CSection* pSection, time_t tNow, BYTE nReservedWord);

    // 处理获取终端状态命令
    void	HandleGetStatus(const char*	pData,					// 数据区
                            unsigned short	nLen,				// 数据长度
                            CSection& device);					// 设备

    // 处理固件升级命令
    void	HandleFirmwareUpgrade(	char	stateCode,			// 状态码
                                    CSection& device);			// 设备

    // 处理固件升级进度命令
    void	HandleProgressRate(	char	rate,					// 升级进度
                                CSection& device);				// 设备


    // 处理获取文件信息
    void	HandleGetFileInfo(	const char*	pData,					// 数据区
                                unsigned short	nLen,					// 数据长度
                                CSection& device);				// 设备

    // 处理文件更新返回结果
    void	HandleUpdateFileResult(	FileType	ft,				// 文件类型
                                    char		result,			// 更新结果
                                    CSection& device);			// 设备

    // 歌曲文件同步进度上传
    void	HandleSyncProgress( const char*	pData,				// 数据区
                                unsigned short	nLen,			// 数据长度
                                CSection& device);				// 设备

    // 终端主动请求同步时间
    void	HandleRequestSyncTime(CSection& device);			// 设备

    // 处理主机请求终端重新分配MAC地址
    void	HandleReassignMac(CSection& device);				// 设备

    // 处理主机向设备请求重启
    void	HandleReboot(CSection& device);						// 设备

    // 主机请求终端重置数据
    void	HandleResetData(char	type,						// 数据类型
                            CSection& device);					// 设备

    // 处理获取FLASH信息
    void	HandleGetFlashInfo(	const char*		pData,			// 数据区
                                unsigned short	nLen,			// 数据长度
                                CSection& device);				// 设备

    // 寻呼台向主机发送终端寻呼中途掉线信息
    void	HandleSpeakerOffline(	const char*	pData,			// 数据区
                                    unsigned short	nLen,		// 数据长度
                                    CSection& device);			// 设备

    // 处理设备返回日期时间
    void	HandleGetDateTime(	const char*	pData,					// 数据区
                                unsigned short	nLen,				// 数据长度
                                CSection& device);					// 设备

    // 处理返回日志文件列表
    void	HandleGetLogFileList(	const char*	pData,				// 数据区
                                    unsigned short	nLen,				// 数据长度
                                    CSection& device);			// 设备

    // 处理返回日志文件
    void	HandleGetLogFileData(	const char*	pData,				// 数据区
                                    unsigned short	nLen,				// 数据长度
                                    CSection& device);			// 设备

    // 处理消防采集器返回通道触发状态
    void	HandleGetAlarmState(	const char*	pData,				// 数据区
                                    unsigned short	nLen,				// 数据长度
                                    CSection& device,			// 设备
                                    bool	bResponse);			// 回应消防采集器

    // 处理消防采集器返回触发模式
    void	HandleGetAlarmMode(	const char*	pData,					// 数据区
                                unsigned short	nLen,					// 数据长度
                                CSection& device);				// 设备

    // 处理返回网络IP信息
    void	HandleNetworkIPInfo(	const char*	pData,					// 数据区
                                unsigned short	nLen);				// 数据长度

    // 处理返回网络模式信息
    void	HandleNetworkModeInfo(	const char*	pData,					// 数据区
                                unsigned short	nLen,
                                CSection& device);				// 数据长度
#if 0
    // 处理9131/9134网络解码器的电源输出模式返回
    void	HandlePowerOutputMode(	const char*	pData,			// 数据区
                                    unsigned short	nLen,		// 数据长度
                                    CSection& device);			// 设备

    // 处理9131/9134网络解码器的回路检测状态返回
    void	HandleSigalDetection(bool	bLoop,					// 是否有回路检测功能
                                bool	bSigal,					// 是否有信号
                                CSection& device);				// 设备
#endif
    // 处理返回查询EQ音效
    void	HandleGetDeviceEq(	const char*	pData,				// 数据区
                                unsigned short	nLen,			// 数据长度
                                CSection& device);				// 设备
    // 处理返回查询蓝牙信息
    void	HandleGetDeviceBtInfo(	const char*		pData,		// 数据区
                                    unsigned short	nLen,       // 数据长度
                                    CSection&	device);		// 设备

    // 处理返回查询电源时序器信息
    void	HandleGetSequencePowerInfo(	const char*		pData,		// 数据区
                                            unsigned short	nLen,   // 数据长度
                                            CSection&	device);	// 设备

    // 处理返回查询设备子音量信息
    void	HandleGetSubVolumeInfo(	const char*		pData,		        // 数据区
                                            unsigned short	nLen,       // 数据长度
                                            CSection&	device);		// 设备
    
    // 处理远程遥控器按键
    void	HandleRemoteControlerKey( const char*		pData,		// 数据区
                                            unsigned short	nLen,   // 数据长度
                                            CSection&	device);	// 设备

    // 处理返回分区器状态
    void	HandleGetSplitterStatus(unsigned char		status,	// 状态
                                    CSection&	device);		// 设备

    // 处理返回无线MIC的状态
    void	HandleGetDeviceMicStatus(const char*	pData,      // 数据区
                                    unsigned short	nLen,		// 数据长度
                                    CSection&        device);   // 设备

    // 处理返回EMC状态
    void	HandleGetEmcStatus(	unsigned char		status,		// 状态
                                CSection&	device);			// 设备

    // 处理返回查询混音模式
    void	HandleGetDeviceMixing(	const char*	pData,			// 数据区
                                    unsigned short	nLen,		// 数据长度
                                    CSection& device);			// 设备

    // 处理返回终端网速带宽
    void    HandleGetDeviceBW(const char* pData,       // 数据区
                              unsigned short nLen,     // 数据长度
                              CSection& device);       // 设备

    // 终端启动通知
    void    HandleFirstBoot(const char *pData, unsigned short nLen, CSection &device);     // 设备

// 集中模式

public:

    // 处理分控设备发过来的选中分区
    void	HandleSelectedSections(	unsigned char	selectedSecID,	// 选中分区的标识
                                    const char*	pData,				// 数据区
                                    unsigned short	nLen,			// 数据长度
                                    CSection& device);              // 设备

    // 处理分控设备发过来的点播请求
    void	HandleRequestPlaySource(const char*	pData,				// 数据区
                                    unsigned short	nLen,				// 数据长度
                                    CSection& device);			// 设备

    // 处理分控设备的设置播放模式
    void	HandleSetPlayMode(	const char*		pData,				// 命令内容
                                int			nLen,				// 命令长度
                                CSection&	device);			// 设备

    // 处理请求播放节目源返回结果
    void	HandleNotifyStreamSource(	unsigned char	result,			// 结果
                                        CSection& device);		// 设备

    // 处理请求播放音频采集音源
    void	HandleSetAudioCollectSource(	unsigned char	result,	// 结果
                                            CSection& device);		// 设备


// SIP命令处理
public:
    // 处理获取SIP状态命令
    void	HandleSipGetStatus(	unsigned char	status,			// SIP状态
                                CSection& device);				// 设备

    // 处理获取SIP账号登录信息命令
    void	HandleSipGetLogInfo(const char*	pData,					// 数据区
                                unsigned short	nLen,					// 数据长度
                                CSection& device);				// 设备

// GPS命令
public:

    // 处理GPS发过来的校时信息
    void	HandleGpsSyncTime(	const char*	pData,				// 数据区
                                unsigned short	nLen,			// 数据长度
                                CSection& gps);					// gps设备

public:
    #if SUPPORT_PAGER_CALL

    void  HandleForwardCallingCommand(unsigned short	 command,	 // 命令
                                                 const char*	 pData,		 // 命令缓冲区
                                                 int			 nLen,		 // 数据长度
                                                 CSection       *oriDevice);     // 设备

    #endif

    #if SUPPORT_LISTEN_FUNCTION

    void  HandleForwardListenCommand(unsigned short	 command,	 // 命令
                                                 const char*	 pData,		 // 命令缓冲区
                                                 int			 nLen,		 // 数据长度
                                                 CSection       *oriDevice);     // 设备

    #endif


    #if SUPPORT_WEB_PAGING

    void  HandleWebPagerCommand(const char*       pData,		// 数据区
                                          unsigned short	nLen,		// 数据长度
                                          const char*       szIP,		// IP地址
                                          unsigned short	uPort);		// UDP端口
    #endif

    #if SUPPORT_AUDIO_MIXER
    // 查询/设置音频协处理器参数
    void  HandleAudioMixerConfig(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device);			// 设备
    // 处理音频混音器数据流发送（TCP)
    void  HandleAudioMixerStream(const char*	pData,		// 数据区
                                     unsigned short	nLen,		// 数据长度
                                     CSection& device);			// 设备
    // 处理主机向混音器/终端设置混音音源时，终端的应答
    void  HandleSetAudioMixerSource(const char*	pData,		// 数据区
                                     unsigned short	nLen,		// 数据长度
                                     CSection& device);			// 设备
    #endif

    // 查询/设置信息发布参数
    void  HandleInformationPublishConfig(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device);			// 设备

    #if SUPPORT_PHONE_GATEWAY
    // 查询/设置电话网关参数
    void  HandlePhoneGatewayConfig(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device);			// 设备
    // 处理电话网关数据流发送（TCP)
    void  HandlePhoneGatewayStream(const char*	pData,		// 数据区
                                     unsigned short	nLen,		// 数据长度
                                     CSection& device);			// 设备
    // 处理主机向电话网关设置混音音源时，终端的应答
    void  HandleSetPhoneGatewaySource(const char*	pData,		// 数据区
                                     unsigned short	nLen,		// 数据长度
                                     CSection& device);			// 设备
    #endif
    
    #if SUPPORT_AMP_CONTROLER
    void HandleAmpControlerStatus(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device);			// 设备
    #endif

    #if SUPPORT_NOISE_DETECTOR
    void HandleNoiseDetectorConfig(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,	// 数据长度
                                     CSection& device);			// 设备
    #endif

    // 查询/设置音频采集器参数
    void  HandleAudioCollectorConfig(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device);			// 设备


    void  HandleControlerAccountInfo(const char*	pData,		// 数据区
                                     unsigned short	nLen,		// 数据长度
                                     CSection& device);			// 设备
    
    void  HandleAudioCollectorStream(const char*	pData,		// 数据区
                                    unsigned short	nLen,		// 数据长度
                                    CSection& device);			// 设备
    // 处理对讲设备基础配置
    void  HandleIntercomBasicConfig(const char*	pData,		    // 数据区
                                     unsigned short	nLen,		// 数据长度
                                     CSection& device);			// 设备

    // 处理触发配置
    void  HandleTriggerConfig(const char*	pData,  // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device);			    // 设备
    // 处理触发请求播放
    void  HandleTriggerRequestPlay(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device);			// 设备

    // 处理寻呼台获取账户存储容量
    void  HandleGetAccountStorageCapacity(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device);			// 设备
    // 处理寻呼台请求上传歌曲文件
    void  HandleRequestUploadSong(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device);			// 设备
    // 处理寻呼台通知上传状态
    void  HandleUploadSongStatus(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device);			// 设备
    // 处理寻呼台请求删除歌曲
    void  HandleRequestDeleteSong(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device);			// 设备

    // 处理控制寻呼台向指定分区设备发起广播寻呼的应答
    void  HandleSetBroadcastPaging(const char*	pData,		    // 数据区
                                     unsigned short	nDataLen,		// 数据长度
                                     CSection& device);			// 设备
    // 处理查找在线主机
    void HandleSearchOnlineHost(const char*		pData,		// 数据区
                                    unsigned short    nLen,		// 数据长度
                                    const char*       szIP);		// IP地址

    // 处理终端向服务器发送json命令
    void HandleReceiveJsonCommand(const char*		pData,		// 数据区
                                    unsigned short    nLen,		// 数据长度
                                    CSection& device);			// 设备
    
    // 处理连接备用服务器
    void HandleConnectStandByServer(const char*		pData,		// 数据区
                                            unsigned short    nLen,		// 数据长度
                                            const char*       szIP);		// IP地址	

public:
    CNetwork*		m_pNetwork;

private:
    QMutex   command_HandleSearch_Mutex;
};


#endif // COMMANDHANDLE_H
