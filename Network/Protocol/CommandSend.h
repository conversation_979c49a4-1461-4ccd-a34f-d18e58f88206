#ifndef COMMANDSEND_H
#define COMMANDSEND_H

#include "Model/Other/PlayList.h"
#include "Lib/SBETcpServerSocket.h"
#include "Lib/SBETcpClientSocket.h"
//#include "Lib/SBETcpSelectClient.h"
#include "CommandQueue.h"
#include "./Model/Device/StreamingGateway.h"
#include "Model/Device/SequencePower.h"

#if SUPPORT_WEB_PAGING
#include "Network/Web/WebPaging.h"
#endif

class CNetwork;
class CSection;
class CSelectedSections;
//class CCommandQueue;


class CCommandSend
{
public:
    CCommandSend(void);
    ~CCommandSend(void);
    void	SetNetwork(CNetwork* pNetwork);
    void	StartCommand(CUDPSocket* pSocket);
    bool	AddCommand(char* szBuf, int nLen, CSection& device);		// 添加到命令到队列
    bool	RemoveCommand(unsigned short uCmd, const char* szIP);			// 移除队列中的命令
    void	SetMac(char* pData, const char* szMac);

// 常用命令
public:

    // 控制所有分区
    void	CmdControlAllSection( char*	szBuf,				// 命令缓冲区
                                  int	len);               // 数据长度

    // 控制选中分区
    bool	CmdControlCheckedSection(char*      szBuf,                // 命令内容
                                     int		len,                  // 数据长度
                                     CMyString  strCmdText,           // 操作描述
                                     unsigned int*  pCheckedIndexs,   // 被选中的分区
                                     unsigned int	uCheckedCount,	  // 被选中的分区数量
                                     bool       bShowMsg = TRUE);     // 显示提示信息

    // 转发分控设备的命令
    bool	CmdForwardControlDevice( const char*	pData,          // 命令缓冲区
                                     int			nLen,           // 数据长度
                                     CSection&	ctrlDevice);        // 分控设备

    // 转发寻呼设备的命令
    bool	CmdForwardPagingDevice(	unsigned short	command,        // 命令
                                    const char*		pData,			// 命令缓冲区
                                    int             nLen,           // 数据长度
                                    CSection&       pagingDevice);  // 寻呼设备

    // 广播主机信息
    void	CmdBoradcasHostInfo(int nRepeatCount = 1);			// 重复发送次数

    // 主服务器连接备用服务器
    void	CmdConnectStandByServer(const char* szIP,           // 服务器IP
                                    const char* szMac,          // 服务器MAC
                                    const char* szSSHPublicKey,    // 公钥（主服务器主动连接才需要）
                                    int nRepeatCount = 1);		// 重复发送次数
    // 备用服务器应答主服务器连接
    void	CmdStandByServerResponseConnect(unsigned char event,                // 应答事件
                                            const char* szIP,           // 服务器IP
                                            const char* szMac,          // 服务器MAC
                                            const char* userName,
                                            int nRepeatCount = 1);		// 重复发送次数

#if 0
    // 回应其它主机搜索设备（把本身当成设备回应给其它主机）
    void	CmdResponseOnlineDevice(const char*	szIP);
#endif

    // 查找在线设备
    void	CmdSearchOnlineDevice(int nRepeatCount = 1);		// 重复发送次数

    // 时间同步
    void	CmdSyncronizeTime();								// 全部设备时间同步
    void	CmdSyncronizeTime(CSection& device);				// 某一设备时间同步

    // 设置设备名称
    void	CmdSetName(	CMyString strName,						// 别名
                        CSection& device);						// 设备

    // 设置音量
    void	CmdSetVolume(	char    volume,						// 音量值
                            unsigned int*   pCheckedIndexs,     // 被选中的分区
                            unsigned int	uCheckedCount);	    // 被选中的分区数量

    // 设置音量（单个分区设备）
    void    CmdSetVolume(char     volume,		    // 音量值
                            CSection& device);	        // 单个分区设备
    
    void    CmdSetSubVolume(bool	bSet,                       // 是否为设置
                            char     subVolume,                    // 子音量
                            char     auxVolume,                    // 本地音量
                            CSection& device);			        // 设备
    
    // 设置设备音量加/减
    void    CmdSetVolumeAddMin( bool isUp,                          //是否为增加
                                unsigned char step,                 // 步进
                                CSection& device);					// 设备

    // 回应分控设备设置音量
    void	CmdResponseSetVolume(CSection& device);				// 分控设备

    // 获取终端状态
    void	CmdGetStatus();

    // 固件升级
    void	CmdUpgradeFirmware(	char            model,              // 设备型号
                                CMyString		strVersion,			// 版本号
                                CMyString		strAbsolutePath,	// 绝对路径
                                CMyString		strRelativePath,	// 相对路径
                                const char*		lpServerIP,			// 服务器IP
                                unsigned short	uPort,              // 服务器端口
                                CSection&	device,
                                string md5);                // 设备

    // HTTP播放歌曲
    void	CmdWebcasting( CMyString	strSongPathName,			// 歌曲路径名
                           unsigned int*   pCheckedIndexs,          // 被选中的分区
                           unsigned int	uCheckedCount);	            // 被选中的分区数量

    // 设置单个设备的播放状态
    void	CmdSetSingleDevicePlayStatus(	PlayStatus status,   // 播放状态
                                            CSection& device);   // 分区

    // 设置播放状态
    void	CmdSetPlayStatus(	PlayStatus      status,          // 播放状态
                                unsigned int*   pCheckedIndexs,  // 被选中的分区
                                unsigned int	uCheckedCount);	 // 被选中的分区数量

    // 设置播放状态
    void    CmdResponseSetPlayStatus(CSection& device);             // 分控设备

    // 设置静音状态
    bool	CmdSetMuteStatus(	MuteStatus  status,                 // 静音状态
                                unsigned int*       pCheckedIndexs, // 被选中的分区
                                unsigned int	    uCheckedCount);	// 被选中的分区数量

    // 回应分控设备设置静音状态
    void	CmdResponseSetMuteStatus(CSection& device);             // 分控设备

    // 设置程控模式
    void	CmdSetControlMode(	ControlMode mode,                   // 程控模式
                                unsigned int*       pCheckedIndexs, // 被选中的分区
                                unsigned int	    uCheckedCount);	// 被选中的分区数量


    // 回应分控设备设置程控模式
    void	CmdResponseSetControlMode(CSection& device);		// 分控设备

    // 查询文件信息
    void	CmdGetFileInfo(	FileType	ft,						// 文件类型
                            CSection& device);					// 设备

    // 更新文件
    void	CmdUpdateFile(	FileType	ft,						// 文件类型
                            CSection&	device);				// 设备
#if 0
    // 请求获取文件信息
    void	CmdRequestGetFileInfo(	FileType	ft,				// 文件类型
                                    CSection&	device);		// 设备
#endif
    // 中止同步歌曲文件
    void	CmdStopSyncSongFile(CSection&	device);			// 设备

    // 设置钟声
    void	CmdSetRing(	int		    nList,				        // 播放列表索引
                        int		    nSong,					    // 歌曲名称索引
                        bool	    bAllZones,                  // 是否为全部分区
                        CPlayList*  pPlaylistShow,
                        unsigned int*       pCheckedIndexs,     // 被选中的分区
                        unsigned int	    uCheckedCount);	    // 被选中的分区数量

    // 播放钟声
    void	CmdPlayRing(unsigned int*       pCheckedIndexs,     // 被选中的分区
                        unsigned int	    uCheckedCount);	    // 被选中的分区数量);

    void	CmdResponsePlayRing(CSection&	device,				// 设备
                                char		result);			// 播放结果

    // 播放本地歌曲
    void	CmdPlayLocalSong(	int		    nList,					// 播放列表索引
                                int		    nSong, 					// 歌曲名称索引
                                CPlayList*  pPlaylistShow,
                                unsigned int*       pCheckedIndexs,         // 被选中的分区
                                unsigned int	    uCheckedCount);	        // 被选中的分区数量

    void	CmdResponsePlayLocalSong(CSection&	device,			// 设备
                                     char		result);		// 播放结果


    // 设置播放模式（选中分区）
    void	CmdSetPlayMode(	PlayMode    mode,			    	// 播放状态
                            unsigned int*       pCheckedIndexs, // 被选中的分区
                            unsigned int	    uCheckedCount);	// 被选中的分区数量

    // 设置播放模式（一般是设定分控设备的播放模式）
    void	CmdSetPlayMode(	PlayMode	mode,					// 播放状态
                            CSection&	device);				// 设备

    // 回应分控设备查询/设置播放模式
    void	CmdResponsePlayMode(CSection&	device,				// 设备
                                int			mode = -1);			// 播放状态

    // 切换到空闲状态
    void	CmdSetIdleStatus( unsigned int*       pCheckedIndexs,       // 被选中的分区
                              unsigned int	      uCheckedCount);	    // 被选中的分区数量

    void	CmdSetIdleStatus(CSection&	device,					// 设备
                             unsigned char	reserve = 0x00,		// 保留位
                             int		nRepeatCount = 1);		// 重复次数

    void    CmdSetIdleStatus();   // 全部分区置为空闲状态

    // 主机请求终端重新分配MAC地址
    void	CmdReassignMac(CSection&	device);				// 设备

    // 主机向设备请求重启
    void	CmdReboot(	CSection&	device,						// 设备
                        unsigned short	uPort = UDP_PORT);		// UDP端口

    // 主机请求终端重置数据
    void	CmdResetData(char type,					// 数据类型
                         CSection&	device);		// 设备

    // 查询FLASH信息
    void	CmdGetFlashInfo(CSection&	device);				// 设备

    // 获取设备日期时间
    void	CmdGetDateTime(CSection&	device);				// 设备

    // 主机向控制设备发送终端文件信息
    void	CmdSendFileInfo(CSection&	device,					// 设备
                            const char*	szSecMac,				// 终端MAC地址
                            char	fileType,					// 文件类型
                            CMyString	strDateTime);			// 文件更新日期时间

    // 4.46 主机向终端设置警报声
    void	CmdSetAlarmSound(	unsigned char	channelID,		// 通道ID
                                int		nList,					// 播放列表索引
                                int		nSong,					// 歌曲名称索引
                                CSection&	device);			// 设备

    // 4.47 主机向终端设置开启/关闭警报
    void	CmdSetAlarmSwitch(	CSection&	device,				// 设备
                                unsigned char	channelID,		// 通道ID
                                unsigned char	switcher,		// 警报开关
                                CMyString strPathName);			// 报警音效路径名

    // 4.48主机向消防采集器查询通道触发状态
    void	CmdGetAlarmState(CSection&	device);				// 设备

    // 4.49 主机向消防采集器查询/设置触发模式
    void	CmdAlarmMode(	CSection&	device,					// 设备
                            unsigned char	channels,			// 通道数，0xFF为查询
                            int		mode);						// 触发模式

    // 4.50 主机向音频采集器/终端设置音频采集音源(选中分区)
    void	CmdSetAudioInfo(std::shared_ptr<CAudioCollector> pAudioCollector,  // 采集器
                            unsigned char channel,
                            unsigned int*   pCheckedIndexs,     // 被选中的分区
                            unsigned int	uCheckedCount);	    // 被选中的分区数量

    // 4.50 主机向音频采集器/终端设置音频采集音源(采集器)
    void	CmdSetAudioInfo(CSection&		device,				// 设备
                            std::shared_ptr<CAudioCollector> pAudioCollector,  // 采集器
                            unsigned char channel,
                            unsigned char	reserve = 0x00,     //保留位
                            bool IsTiming=false,        //是否定时
                            unsigned char timingVolume=0xff,   //音量
                            bool IsTrigger=false,        //是否触发
                            unsigned char triggerVolume=0xff);   //触发音量
    
    // 主机向终端设置网络电台音源
    void CmdSetNetRadioSource(CSection&	notifydevice,		  // 通知的设备
                            const char *radioName,        //电台名称
                            char *sessionId,        //电台的sessionId
                            int multicastPort,      //组播端口
                            bool IsTiming,                     //是否定时
                            unsigned char volume);       //音量

    #if SUPPORT_AUDIO_MIXER
    //主机向混音器/终端设置混音音源
    void    CmdSetAudioMixerSourceInfo(CSection&	notifydevice,		  // 通知的设备
                                       CSection&	mixerDevice,		  // 混音器设备
                                       int event                         // 事件类型：1为启动，0为停止，2为重发
                                      );
    // 主机查询/设置音频混音器参数
    void    CmdAudioMixerConfig(CSection&	device,			    // 设备
                                bool	bSet=false              // 是否为设置
                                );

    #endif

    #if SUPPORT_PHONE_GATEWAY
    //主机向电话网关/终端设置电话网关音源
    void    CmdSetPhoneGatewaySourceInfo(CSection&	notifydevice,		  // 通知的设备
                                       CSection&	phoneGatewayDevice,		  // 电话网关设备
                                       int event                         // 事件类型：1为启动，0为停止，2为重发
                                      );
    // 主机查询/设置电话网关参数
    void    CmdPhoneGatewayConfig(CSection&	device,			    // 设备
                                string telWhitelist,            //电话白名单
                                bool	bSet=false              // 是否为设置
                                );

    #endif

    #if SUPPORT_AMP_CONTROLER
//功放控制器配置
    void  CmdAmpControlerConfig(CSection&	device,			   // 设备
                                        bool	bSet                   // 是否为设置
                                );
    #endif

    #if SUPPORT_NOISE_DETECTOR
//噪声自适应器配置
    void  CmdNoiseDetectorConfig(CSection&	device,			   // 设备
                                        bool	bSet                   // 是否为设置
                                );
    #endif


    // 主机查询/设置信息发布参数
    void  CmdInformationPublish(CSection&	device,			   // 设备
                                bool	bSet,                  // 是否为设置
                                bool bEnableDisplay,           // 是否启用显示
                                string strText,                // 输出文本
                                int nEffects,                  // 特效
                                int nMoveSpeed,                // 移动速度
                                int nStayTime                 // 停留时间 
                                );

    // 主机查询/设置音频采集器参数
    void    CmdAudioCollectorConfig(CSection&	device,			    // 设备
                                bool	bSet=false              // 是否为设置
                                );

    // 回应分控向音频采集器/终端设置音频采集音源
    void	CmdResponseSetAudioInfo(CSection&	device);		// 分控设备

    // 主机向电源时序器查询参数
    void    CmdGetSequencePowerInfo(CSection&	device);	    // 设备

    // 主机向电源时序器设置参数
    void    CmdSetSequencePowerInfo(CSection&	device,std::shared_ptr<CSequencePower> pSequencePwrInfo);	    // 设备

    // 主机向电源时序器发送定时信息
    void    CmdSetSequencePowerTiming(CSection&	device,std::shared_ptr<CSequencePower> pSequencePwrInfo,bool IsQuickResponse);	// 设备

    // 4.51 主机向终端设置网络模式
    void	CmdSetNetworkMode(	CSection&	device,				// 设备
                                bool	bSet,                   // 是否为设置
                                unsigned char	mode,			// 网络模式
                                const char*	szServerIP,			// 服务器IP
                                unsigned short	nServerPort,    // 服务器端口
                                const char*	szServerIP2,	    // 备用服务器IP
                                unsigned short	nServerPort2);	// 备用服务器端口

    // 4.52 主机向终端查询/设置IP属性(组播)
    void	CmdNetworkInfo(	CSection&		device,				// 设备
                            CNetworkInfo&	netInfo,			// IP属性
                            bool			bSet = FALSE);		// 是否为设置

    // 4.53 主机向设备获取记录文件列表
    void	CmdGetLogFileList(CSection&		device);			// 设备

    // 4.54 主机向设备获取记录文件内容
    void	CmdGetLogFileData(	CSection&	device,				// 设备
                                const char*	szFileName,			// 文件名称
                                unsigned short	packID);		// 包ID

    // 4.55 主机向采集器主动发送终端音源选择状态
    void	CmdSendAudioState(	CSection&	device,				// 设备
                                bool		bUsed,
                                unsigned char channelSelect);				// 是否被选择

    // 4.56 主机向其他控制设备下发音频采集器设备列表
    void	CmdSendAudioList(CSection&		device);			// 设备

    // 4.58 主机向网络解码播放器查询/设置电源输出模式
    void	CmdPowerOutputMode( CSection&	device,				// 设备
                                unsigned char	mode = 0,		// 电源输出模式，为0时，是查询
                                unsigned short	timteout = 0);	// 超时时间

    // 4.59 主机向网络解码播放器查询回路检测状态
    void	CmdSigalDetection(CSection&	device);				// 设备

    // 4.61 主机向终端查询/设置EQ音效
    void	CmdDeviceEq(CSection&	device,						// 设备
                        char		mode	= -1,				// EQ模式，为-1时，是查询
                        unsigned char	gain[10]={0});		    // 低音值

    // 4.61 主机向终端查询/设置蓝牙信息
    void    CmdBlueToothInfo(CSection&	device,					// 设备
                                const char*	btName =NULL,					        // 蓝牙名称,为NULL时为查询
                                unsigned char btencryption=0,             // 蓝牙加密方式
                                const char*   btPin=NULL);			                // 蓝牙密码

    // 4.61 主机向对讲终端查询/设置基础配置
    void    CmdIntercomBasicConfig(CSection&	device,					// 设备
                                  CALLDEVICECONFIG *callBasicConfig = NULL);	//  对讲基础配置,为空代表查询

    // 主机向终端查询/设置触发参数
    void    CmdTriggerConfig(CSection&	device,					// 设备
                            char trigger_switch,
                            char trigger_mode,
                            const char *trigger_songName,
                            const char *trigger_songMd5,
                            int playTimes,
                            int volume); 

    // 寻呼台获取账户存储容量
    void    CmdAccountStorageCapacity(CSection&	device,					// 设备
                                      const char*	szAccount,		    // 账号
                                      UINT64 storage_capacity,             // 总存储容量
                                      UINT64 storage_used,                 // 已用存储空间
                                      UINT64 storage_remaining,            //剩余存储空间（MB)
                                      int compress_bitrate              //压缩比特率(kbps)   
                                    );
    // 寻呼台请求上传歌曲文件
    void    CmdRequestUploadSong(CSection&	device,					    // 设备
                                      int result,                       // 返回值
                                      const char*	szHostIP,	// 服务IP地址
                                      unsigned short	uPort,	// HTTP端口
                                      const char* uploadUrl             //上传地址（cgi）
                                    );
    // 寻呼台通知服务器上传状态
    void    CmdUploadSongStatus(CSection&	device,					    // 设备
                                    int result);                       // 返回值
    // 寻呼台请求删除本地歌曲
    void    CmdRequestDeleteSong(CSection&	device,					    // 设备
                                    int result);                                  // 返回值

    // 控制寻呼台向指定分区设备发起广播寻呼
    void    CmdSetBroadcastPaging(CSection&	device,					// 设备
                                    const char*	szAccount,						// 用户名
                                    int control_event,                          //控制类别
                                    int isAllZone,                              //该用户的全部分区设备
                                    vector<string> &zoneMacs);                    //分区设备集合

    // 4.62 消防采集器通道触发状态改变时，主动通知主机
    void	CmdResponseAlarmStateChanged(CSection&	device);	// 设备

    // 4.66主机向终端查询/设置混音模式
    void	CmdMixingMode(	CSection&	device,					// 设备
                            char		channel = -1,			// channel，为-1时，是查询，0双声道 ，1为单声道
                            unsigned char		auxVol = 6,		// 线路音量增益
                            unsigned char		mixing = 0,		// 0：混音关，1：DAC双声道混合输出，2：AUX混合DAC输出
                            unsigned char		dacVol = 8);	// 数字音量增益，AUX混合DAC输出时有效

    //  4.68 主机向设备请求重启（组播）
    void	CmdReootMulticast(vector<CSection*>& devices);		// 重启的设备

    // 4.70 回应终端被寻呼通知(TCP模式)
    void	CmdResponseNotifyPaging(CSection&	device);		// 设备

    // 4.72 回应寻呼台向终端发送掉线再次寻呼指令（TCP模式）
    void	CmdResponsePagingAgain(CSection&	device);		// 设备

    // 4.75查询/设置网络解码分区器输出状态
    void	CmdSplitterStatus(CSection&	device,					// 设备
                              short	status = -1);               // 输出状态（负数表示查询）

    // 4.76 查询/设置解码功率分区器、数字功放EMC状态
    void	CmdEmcStatus(CSection&	device,					// 设备
                         short	status = -1);				// 状态（负数表示查询）

    // 4.97查询/设置 DSP6636无线MIC控制面板状态
    void	CmdMicStatus(CSection&      device,				// 设备
                        char            volume = -1,		// 音量：为负数表示查询，0-16
                        unsigned char	power = 1,			// 发射功率 1-3
                        unsigned char	channel = 1);		// 频道：1-99

    // 4.85 查询网速带宽
    void    CmdQueryNetQuality(CSection&	device,	// 设备
                               int   nNetType,     // 网络连接测试 0x00 : UDP单播, 0x01: UDP组播, 0x02 : TCP
                               const char* szIP,   // 查询IP
                               ushort  uPort);     // 查询端口

public:
    // 设置工作模式
    void	CmdSetWorkPattern(	CSection&	device,				// 设备
                                char	pattern,				// 工作模式
                                int		nRepeatCount = 1);		// 重复发送次数

    // 4.42 回应寻呼站/移动设备请求主机播放节目源（集中模式）
    void	CmdResponsePlaySource(	CSection&	device,			// 设备
                                    unsigned char	selSecID,	// 选中分区ID
                                    unsigned char	result);	// 播放结果

    // 4.43 请求播放节目源（指定分区）
    void CmdNotifyStreamSource( CSection&	device,				// 设备
                                char		source,				// 音源
                                char		format,				// 歌曲格式
                                unsigned int	sampleRate,			// 采样率
                                unsigned char	bitSample,			// 采样精度
                                unsigned char	channels,			// 声道数
                                const char*		szName,				// 歌曲名称
                                const char*		szMultiIP,			// 组播IP
                                unsigned short	port,				// 组播端口
                                unsigned char	volume,				// 定时音量
                                unsigned int  fileLength,           // 歌曲文件大小
                                unsigned int  totalFramesCnt,       // 总帧数
                                unsigned int  currentFrame,         //当前帧数
                                string        fileMd5,              // md5
                                bool isUseMulticastNewCmd,
                                unsigned char	reserve = 0x00);	// 保留位

    // 4.44 播放节目源数据流传输（TCP下的集中模式的单播）
    void	CmdStreamSource(CSection&	device,					// 设备
                            const char*	data,                   // 数据流
                            int			dataLen,				// 数据流长度
                            CUDPSocket* pUdpSocket);

    // 4.60 寻呼台/移动设备/分控设备向主机发送选中的分区（集中模式）
    void	CmdResponseSelectedSections(	CSection&	device,         // 设备
                                            unsigned char	selSecID,	// 选中分区ID
                                            unsigned char	packID);	// 包ID

    int		SectionStatus(char* data, CSection& section);

    // 4.67 主机向分控设备下发终端当前状态
    void	CmdForwardStatus(	CSection&	controlDevice,		// 分控设备
                                CSection*	pSection = NULL);	// pSection为NULL时，表示所有的分区

        // 主机向分控设备下发终端当前状态（变化的分区）
    void	CmdForwardStatusVary(	CSection&	controlDevice,		// 分控设备
                                vector<string> &vecSections);	// pSection为NULL时，表示所有的分区

    #if SUPPORT_PAGER_CALL
    void    CmdForwardPagerStatus(CSection*	controlDevice,				// 分控设备
                                             CSection*	pDevice);					// pSection为NULL时，表示所有分控设备
    #endif
    // 4.69 主机向分控设备发送音频采集器列表在线/离线状态
    void	CmdForwardAudioCollector(CSection&	controlDevice,		// 分控设备
                                     CSection*	pCollector = NULL);	// pCollector为NULL时，表示所有的音频采集器

    // 4.73 查询/设置音频传输方式
    void	CmdAudiocastMode(CSection&	device,					// 设备
                             char		audiocast = -1);		// 音频传输方式

    // 4.77 主机通知终端接收寻呼（SIP） zhuyg
    void  CmdNotifyPaging(CSection&    device,        // 设备
                          string       strExten,      // 发起寻呼的分机号码
                          u_char       uPort);        // 组播端口

    // SIP命令
public:
    // 获取SIP会话状态
    void	CmdSipGetStatus(CSection&	device);				// 设备

    // SIP账号登录
    void	CmdSipLogIn(CSection&	device,						// 设备
                        bool isEnableSip,                       // 是否启用SIP
                        unsigned char outPutVolume,             // 输出音量
                        const char*	szAccount,					// 登录账号
                        const char*	szPassword,					// 登录密码
                        const char*	szServerAddr,				// 服务器地址
                        int		nServerPort,                    // 端口
                        int     nServerProtocol);               // 协议

    // 获取SIP登录账号信息
    void	CmdSipGetLogInfo(CSection&	device);				// 设备

    // SIP会话挂断
    void	CmdSipHangUp(CSection&	device);					// 设备

#if SUPPORT_WEB_PAGING
    //WEB寻呼
public:
    void CmdWebPagingNotify(CWebPaging &webpPaging,int event,AudioAlgorithm audioCodecs = ALGORITHM_722);
#endif

    //主机通知解码终端即将进入定时音源
    void    CmdNotifyDecoderReadyTiming(CSection&	device);

private:
    CCommandQueue	m_commandQueue;	// 已发送的命令队列
    CNetwork*		m_pNetwork;

};




#endif // COMMANDSEND_H






