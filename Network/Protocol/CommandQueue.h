#ifndef COMMANDQUEUE_H
#define COMMANDQUEUE_H

#include "Global/Const.h"
#include <pthread.h>
#include <list>
#include <Lib/UDPSocket.h>

using namespace std;

/************************************************************************/
/* 命令队列                                                             */
/************************************************************************/

#define SPAN_REPEAT_SEND	2000
#define MAX_REPEAT_COUNT	10

#define SPAN_COMMAND_SEND	40

class CSection;

class CProtocolCommand
{
public:
    CProtocolCommand();
    //CProtocolCommand(const CProtocolCommand& proCmd);
    CProtocolCommand(unsigned short uCmd, const char* szBuf, int nLen, const char* szIP);
    ~CProtocolCommand(void);

public:
    unsigned short	m_uCommand;
    char	    m_szBuf[MAX_BUF_LEN];	// 动态分配内存，在析构函数里delete掉会出错
    int         m_nLen;
    char	    m_szIP[MAX_IP_LEN];		// 目标IP
    int         m_nRepeatCount;			// 重发次数

    int			m_nPort;
    CUDPSocket* m_pSocket;
};

class CCommandQueue
{
public:
    CCommandQueue(void);
    ~CCommandQueue(void);

public:
    void	StartWorking(CUDPSocket* pSocket);
    bool	AddCommand(char* szBuf, int nLen, const char* szIP);
    bool	RemoveCommand(unsigned short uCmd, const char* szIP);
    void	HandleCommand(void);

    BOOL	AddSendCmd( char*		data,			// 数据
                        int			len,			// 数据长度
                        CUDPSocket* pSocket,        // socket
                        const char*	szIP,			// 目标IP（UDP）
                        int			port,			// 端口（UDP）
                        int			repeat);		// 重复次数
    void	HandleSendCmd(void);

    static void *CommandThread(void* lpParam);
    static void *SendCmdThread(void* lpParam);

private:
    list<CProtocolCommand>	m_listCommands;
    pthread_mutex_t         m_csCommand;
    CUDPSocket*             m_pSocket;

    list<CProtocolCommand>	m_listSendCmd;
    pthread_mutex_t         m_csSendCmd;
};

//static CCommandQueue*   s_CommandQueue;

#endif // COMMANDQUEUE_H
