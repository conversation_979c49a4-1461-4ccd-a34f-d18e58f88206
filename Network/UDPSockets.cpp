#include "UDPSocket.h"

/************************************************************************/
/*                                                                      */
/*  CUDPSocket : Sokcet通讯类                                           */
/*                                                                      */
/*  实现点对点、组播数据的发送与接收                                    */
/*                                                                      */
/************************************************************************/

CUDPSocket::CUDPSocket(){}
CUDPSocket::~CUDPSocket(){}

// 网络
bool	CUDPSocket::CreateSocket(unsigned short	uBindPort,					// 绑定端口
					 UDP_CALLBACK*	pFunRecvData,		// 收到数据后的回调函数
					 void*			pUserDefined)		// 自定义数据
                     {return true;}

void    CleanSocket(void){}										// 清理socket

// 组播
bool	JoinMulticast(const char* multicastIP){return true;}					// 加入组播
bool	LeaveMulticast(){return true;}										// 离开组播

// 广播
bool	EnableBroadcast(){return true;}										// 开启广播模式

// 数据处理
int		HandleIO(void){return 1;}										// 处理事件的到来
int		RecvData(void){return 1;}										// 接收数据
bool	SendData(void*			lpData,						// 数据
				 int			nLen,						// 数据长度
				 const char*	szHost,						// IP地址
				 unsigned short	uPort,						// 端口
				 int			nRepeatCount){return 1;}				// 重复次数


/************************************************************************/
/*                                                                      */
/*  CUDPSockets : Sokcet通讯集合                                        */
/*                                                                      */
/*  负责对所有socket的管理                                              */
/*                                                                      */
/************************************************************************/

CUDPSockets::CUDPSockets(void){}
CUDPSockets::~CUDPSockets(void){}


CUDPSocket*	CUDPSockets::CreateSocket(unsigned short	uBindPort,		// 创建socket
						 UDP_CALLBACK*	pFunRecvData,	// 收到数据后的回调函数
						 void*			pUserDefined){}	// 自定义数据

void	CleanSockets(void){}										// 清除socket

// 工作线程
bool	StartWorkThread(){return true;}										// 启动工作线程
static  void*   WorkThread(void* lpParam){return NULL;}      // 工作线程处理过程
void        ExitWorkThread(void){}

