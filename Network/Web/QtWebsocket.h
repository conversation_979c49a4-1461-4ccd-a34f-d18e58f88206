#ifndef QTWEBSOCKET_H
#define QTWEBSOCKET_H

#include <QList>
#include <QMap>
#include <QDebug>
#include <QtWebSockets/QWebSocket>
#include <QtWebSockets/QWebSocketServer>
#include <QTimer>
#include <QMutex>
#include <QMutexLocker>
#include "Model/Device/WebSection.h"

typedef struct CommandData_t
{
    string strUID;
    string strBuf;
    int cmdCnt;
}CommandData;

typedef struct
{
    QWebSocket* qwebsocket;
    int timeoutCnt;
}stWebsocket;

#define MAX_FIX_TIME_SEND_COUNT   500      // 在固定时间内最大发送数据次数
#define MAX_SEND_TIMER            200      // 最大发送时间间隔(ms)


#define MAX_TIMEOUT_MINUTE        30       //单位min

class QtWebsocket:public QObject
{
    Q_OBJECT

public:
    QtWebsocket();
    ~QtWebsocket();

    void    StartWebsocket();
    void    StopWebsocket();
    QWebSocket* GetWebSocketByUID(string strUID);

    void    SendAll(QString strBuf);
    #if 0
    void    AddCommandBuf(string strUID, string strBuf);
    #endif
    //void    DisconnectSocket(string strUID);

Q_SIGNALS:
    void    closed();
    void    signal_CloseSocket(string,bool);
    void    send(string,string);

private Q_SLOTS:
    void    onNewConnection();
    void    processTextMessage(QString message);
    void    socketDisconnected();
    void    socketError(QAbstractSocket::SocketError error);
    void    SendMessageQ(string strBuf, string strUID);
    void    SendData();
    void   CloseSocket(string,bool);

    void   OnTimeOut();

private:
    //QTimer  m_Timer;       // 定时器
    QMutex  m_mutex_cmd;
    QMutex  m_mutex_send;

    QWebSocketServer * m_WebSocketServer;
    map<string, stWebsocket> m_WebSockets;
    bool m_debug;
    QWebSocket *pSocket;
    QDateTime  *current_date_time;

    int     m_nCurSendID;       // 当前发送队列
    bool    m_bIsSending;       // 是否正在发送

    list<CommandData_t>      m_sendData1;        // 待发送数据 队列
    list<CommandData_t>      m_sendData2;

    list<CommandData>       m_Commandlist;
};

#endif // QTWEBSOCKET_H
