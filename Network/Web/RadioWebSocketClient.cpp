#include "RadioWebSocketClient.h"
#include "NodeJSManager.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QUuid>
#include <QDebug>
#include <QDateTime>
#include "stdafx.h"

RadioWebSocketClient* RadioWebSocketClient::m_instance = nullptr;
QMutex RadioWebSocketClient::m_instanceMutex;

RadioWebSocketClient* RadioWebSocketClient::getInstance()
{
    QMutexLocker locker(&m_instanceMutex);
    if (m_instance == nullptr)
    {
        m_instance = new RadioWebSocketClient();
    }
    return m_instance;
}

RadioWebSocketClient::RadioWebSocketClient(QObject *parent)
    : QObject(parent)
    , m_nodeJSManager(nullptr)
{
    // 初始化心跳定时器
    m_heartbeatTimer = new QTimer(this);
    connect(m_heartbeatTimer, &QTimer::timeout, this, &RadioWebSocketClient::onHeartbeatTimeout);
    m_heartbeatTimer->start(10000); // 每10秒发送心跳
    
    // 获取Node.js管理器实例
    m_nodeJSManager = NodeJSManager::getInstance();
}

RadioWebSocketClient::~RadioWebSocketClient()
{
    shutdownNodeJSService();
    stopAllRadioPlay();
}

bool RadioWebSocketClient::initializeNodeJSService()
{
    if (!m_nodeJSManager)
    {
        qDebug() << "NodeJSManager is not initialized";
        return false;
    }
    
    if (m_nodeJSManager->isRunning())
    {
        qDebug() << "Node.js service is already running";
        return true;
    }
    
    qDebug() << "Starting Node.js service for radio streaming...";
    bool result = m_nodeJSManager->startRadioJS();
    
    if (result)
    {
        qDebug() << "Node.js service started successfully";
        // 等待一下让服务完全启动
        QThread::msleep(2000);
    }
    else
    {
        qDebug() << "Failed to start Node.js service";
    }
    
    return result;
}

void RadioWebSocketClient::shutdownNodeJSService()
{
    if (m_nodeJSManager && m_nodeJSManager->isRunning())
    {
        qDebug() << "Shutting down Node.js service...";
        m_nodeJSManager->stopRadioJS();
        qDebug() << "Node.js service shutdown completed";
    }
}

bool RadioWebSocketClient::startRadioPlay(const string& radioUrl, const vector<string>& zoneMacs, int volume, string& sessionId)
{
    #if 1 //Network.cpp的StartWorking已经启动Node.js服务
    // 确保Node.js服务已启动
    if (!m_nodeJSManager->isRunning())
    {
        qDebug() << "Node.js service is not running!";
        return false;
    }
    #endif
    
    QMutexLocker locker(&m_sessionsMutex);
    
    // 生成会话ID
    sessionId = generateSessionId();
    
    // 分配组播端口
    int allocatedPort = allocateMulticastPort();
    if (allocatedPort == 0)
    {
        qDebug() << "Failed to allocate multicast port for session:" << sessionId.c_str();
        return false;
    }
    
    // 创建会话信息
    auto session = make_unique<RadioPlaySession>();
    session->sessionId = sessionId;
    session->radioUrl = radioUrl;
    session->zoneMacs = zoneMacs;
    session->volume = volume;
    session->multicastPort = allocatedPort;
    session->isActive = true;
    
    // 创建WebSocket连接
    session->webSocket = createWebSocketConnection(sessionId);
    if (session->webSocket == nullptr)
    {
        qDebug() << "Failed to create WebSocket connection for session:" << sessionId.c_str();
        return false;
    }
    
    // 创建播放定时器
    session->playbackTimer = new QTimer(this);
    session->playbackTimer->setTimerType(Qt::PreciseTimer);
    session->playbackTimer->setInterval(PLAYBACK_INTERVAL_MS); // 使用定义的播放间隔
    connect(session->playbackTimer, &QTimer::timeout, this, &RadioWebSocketClient::onPlaybackTimerTimeout);
    
    // 创建会话检查定时器
    session->sessionCheckTimer = new QTimer(this);
    session->sessionCheckTimer->setInterval(1000); // 每秒检查一次
    connect(session->sessionCheckTimer, &QTimer::timeout, this, &RadioWebSocketClient::onSessionCheckTimeout);
    session->sessionCheckTimer->start();
    
    // 创建定时检查定时器
    session->timingCheckTimer = new QTimer(this);
    session->timingCheckTimer->setInterval(TIMING_CHECK_INTERVAL_MS);
    connect(session->timingCheckTimer, &QTimer::timeout, this, &RadioWebSocketClient::onTimingCheckTimeout);
    session->timingCheckTimer->start();
    
    // 初始化播放时间同步相关字段
    session->playStartTime = QDateTime::currentMSecsSinceEpoch();
    session->totalSentBytes = 0;
    
    // 保存会话
    m_sessions[sessionId] = std::move(session);
    
    qDebug() << "Started radio play session:" << sessionId.c_str() << "URL:" << radioUrl.c_str() << "Port:" << allocatedPort;
    return true;
}

bool RadioWebSocketClient::stopRadioPlay(const string& sessionId)
{
    QMutexLocker locker(&m_sessionsMutex);
    
    auto it = m_sessions.find(sessionId);
    if (it == m_sessions.end())
    {
        qDebug() << "Session not found:" << sessionId.c_str();
        return false;
    }
    
    // 停止播放定时器
    if (it->second->playbackTimer)
    {
        it->second->playbackTimer->stop();
        it->second->playbackTimer->deleteLater();
        it->second->playbackTimer = nullptr;
    }
    
    // 停止会话检查定时器
    if (it->second->sessionCheckTimer)
    {
        it->second->sessionCheckTimer->stop();
        it->second->sessionCheckTimer->deleteLater();
        it->second->sessionCheckTimer = nullptr;
    }
    
    // 停止定时检查定时器
    if (it->second->timingCheckTimer)
    {
        it->second->timingCheckTimer->stop();
        it->second->timingCheckTimer->deleteLater();
        it->second->timingCheckTimer = nullptr;
    }
    
    // 释放分配的端口
    if (it->second->multicastPort > 0)
    {
        releaseMulticastPort(it->second->multicastPort);
    }
    
    // 发送停止命令并关闭连接
    if (it->second->webSocket)
    {
        // 先断开信号连接，避免onDisconnected回调导致重复清理
        it->second->webSocket->disconnect();
        
        if (it->second->webSocket->state() == QAbstractSocket::ConnectedState)
        {
            QJsonObject stopCommand;
            stopCommand["event"] = 0; // 停止播放
            QJsonDocument doc(stopCommand);
            it->second->webSocket->sendTextMessage(doc.toJson(QJsonDocument::Compact));
        }
        
        // 关闭连接
        it->second->webSocket->close();
        it->second->webSocket->deleteLater();
    }
    
    // 移除会话
    m_sessions.erase(it);
    
    qDebug() << "Stopped radio play session:" << sessionId.c_str();
    return true;
}

void RadioWebSocketClient::stopAllRadioPlay()
{
    QMutexLocker locker(&m_sessionsMutex);
    
    for (auto& pair : m_sessions)
    {
        // 停止播放定时器
        if (pair.second->playbackTimer)
        {
            pair.second->playbackTimer->stop();
            pair.second->playbackTimer->deleteLater();
        }
        
        // 停止会话检查定时器
        if (pair.second->sessionCheckTimer)
        {
            pair.second->sessionCheckTimer->stop();
            pair.second->sessionCheckTimer->deleteLater();
        }
        
        // 停止定时检查定时器
        if (pair.second->timingCheckTimer)
        {
            pair.second->timingCheckTimer->stop();
            pair.second->timingCheckTimer->deleteLater();
        }
        
        if (pair.second->webSocket)
        {
            // 发送停止命令
            if (pair.second->webSocket->state() == QAbstractSocket::ConnectedState)
            {
                QJsonObject stopCommand;
                stopCommand["event"] = 0;
                QJsonDocument doc(stopCommand);
                pair.second->webSocket->sendTextMessage(doc.toJson(QJsonDocument::Compact));
            }
            
            pair.second->webSocket->close();
            pair.second->webSocket->deleteLater();
        }
    }
    
    m_sessions.clear();
    qDebug() << "Stopped all radio play sessions";
}

QWebSocket* RadioWebSocketClient::createWebSocketConnection(const string& sessionId)
{
    QWebSocket* webSocket = new QWebSocket();
    
    // 连接信号槽
    connect(webSocket, &QWebSocket::connected, this, &RadioWebSocketClient::onConnected);
    connect(webSocket, &QWebSocket::disconnected, this, &RadioWebSocketClient::onDisconnected);
    connect(webSocket, &QWebSocket::binaryMessageReceived, this, &RadioWebSocketClient::onBinaryMessageReceived);
    connect(webSocket, &QWebSocket::textMessageReceived, this, &RadioWebSocketClient::onTextMessageReceived);
    connect(webSocket, QOverload<QAbstractSocket::SocketError>::of(&QWebSocket::error), this, &RadioWebSocketClient::onError);
    
    // 设置会话ID作为属性
    webSocket->setProperty("sessionId", QString::fromStdString(sessionId));
    
    // 连接到Node.js服务
    QUrl url(QString::fromStdString(m_nodeServerUrl));
    webSocket->open(url);
    
    return webSocket;
}

void RadioWebSocketClient::onConnected()
{
    QWebSocket* webSocket = qobject_cast<QWebSocket*>(sender());
    if (!webSocket) return;
    
    string sessionId = webSocket->property("sessionId").toString().toStdString();
    qDebug() << "WebSocket connected for session:" << sessionId.c_str();
    
    QMutexLocker locker(&m_sessionsMutex);
    auto it = m_sessions.find(sessionId);
    if (it != m_sessions.end())
    {
        // 发送播放命令
        sendPlayCommand(webSocket, it->second->radioUrl);
        
        // 启动会话检查定时器
        if (it->second->sessionCheckTimer)
        {
            it->second->sessionCheckTimer->start();
        }
    }
}

void RadioWebSocketClient::onTimingCheckTimeout()
{
    QTimer* timer = qobject_cast<QTimer*>(sender());
    if (!timer) return;

    qint64 currentCheckTime = QDateTime::currentMSecsSinceEpoch();
    
    QMutexLocker locker(&m_sessionsMutex);
    
    // 查找对应的会话
    string targetSessionId;
    for (auto& pair : m_sessions)
    {
        if (pair.second->timingCheckTimer == timer)
        {
            targetSessionId = pair.first;
            break;
        }
    }

    if (targetSessionId.empty()) return;
    
    auto it = m_sessions.find(targetSessionId);
    if (it == m_sessions.end()) return;

    if (!it->second->isActive || it->second->isBuffering || it->second->unusedCount>0)
    {
        return;
    }
    
    RadioPlaySession& session = *(it->second);
    qint64 elapsedTime = currentCheckTime - session.playStartTime;
    
    // 计算理论上应该发送的字节数 (64Kbps = 8KB/s)
    qint64 expectedBytes = (elapsedTime * NET_RADIO_BYTES_PER_SECOND) / 1000;
    
    // 计算发送字节数的偏差
    qint64 bytesDiff = session.totalSentBytes - expectedBytes;
    
    #if 1
    qDebug() << "Session:" << session.sessionId.c_str()
                << "Elapsed:" << elapsedTime << "ms"
                << "Expected:" << expectedBytes << "bytes"
                << "Actual:" << session.totalSentBytes << "bytes"
                << "Diff:" << bytesDiff << "bytes";
    #endif
    
    // 如果发送的数据超过预期太多，暂停播放定时器一段时间
    if (bytesDiff > (NET_RADIO_BYTES_PER_SECOND / 4)) // 超过0.25秒的数据量
    {
        if (session.playbackTimer && session.playbackTimer->isActive())
        {
            // 计算需要暂停的时间 (毫秒)
            int pauseTime = (bytesDiff * 1000) / NET_RADIO_BYTES_PER_SECOND;
            pauseTime = qMin(pauseTime, 200); // 最多暂停200ms
            
            qDebug() << "Session" << session.sessionId.c_str() 
                        << "pausing for" << pauseTime << "ms to sync timing";
            
            // 先停止播放定时器
            session.playbackTimer->stop();
            // 使用QTimer::singleShot来延迟重启播放定时器
                // 注意：避免在lambda中获取锁，使用智能指针确保安全访问
                QTimer* resumeTimer = session.playbackTimer;
                qint64 startTime = session.playStartTime;
                qint64* totalSentBytesPtr = &session.totalSentBytes;
                
                QTimer::singleShot(pauseTime, [resumeTimer, sessionId = session.sessionId, startTime, totalSentBytesPtr]() {
                    if (resumeTimer)
                    {
                        // 重新检查数据量，确保恢复时机合适
                        qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
                        qint64 elapsedTime = currentTime - startTime;
                        qint64 expectedBytes = (elapsedTime * NET_RADIO_BYTES_PER_SECOND) / 1000;
                        qint64 bytesDiff = *totalSentBytesPtr - expectedBytes;
                        
                        // 如果数据仍然超发太多，继续延迟
                        if (bytesDiff > (NET_RADIO_BYTES_PER_SECOND / 4))
                        {
                            int additionalPause = qMin((bytesDiff * 1000) / NET_RADIO_BYTES_PER_SECOND, 300LL);
                            qDebug() << "Session" << sessionId.c_str() 
                                     << "still over-sending, additional pause:" << additionalPause << "ms";
                            
                            // 递归延迟
                            QTimer::singleShot(additionalPause, [resumeTimer, sessionId]() {
                                if (resumeTimer)
                                {
                                    resumeTimer->start();
                                    qDebug() << "Session" << sessionId.c_str() << "resumed playback after additional delay";
                                }
                            });
                        }
                        else
                        {
                            resumeTimer->start();
                            qDebug() << "Session" << sessionId.c_str() << "resumed playback";
                        }
                    }
                });
        }
    }
    // 如果发送的数据少于预期，可以稍微加快播放速度（减少间隔）
    else if (bytesDiff < -(NET_RADIO_BYTES_PER_SECOND / 8)) // 少于0.125秒的数据量
    {
        if (session.playbackTimer && session.playbackTimer->interval() > 20)
        {
            int newInterval = session.playbackTimer->interval() - 1;
            session.playbackTimer->setInterval(newInterval);
            qDebug() << "Session" << session.sessionId.c_str() 
                        << "speeding up, new interval:" << newInterval << "ms";
        }
    }
    // 如果时间同步较好，恢复正常间隔
    else if (abs(bytesDiff) < (NET_RADIO_BYTES_PER_SECOND / 16)) // 偏差小于0.0625秒的数据量
    {
        if (session.playbackTimer && session.playbackTimer->interval() != PLAYBACK_INTERVAL_MS)
        {
            session.playbackTimer->setInterval(PLAYBACK_INTERVAL_MS);
            qDebug() << "Session" << session.sessionId.c_str() 
                        << "timing synced, restored normal interval:" << PLAYBACK_INTERVAL_MS << "ms";
        }
    }
}

void RadioWebSocketClient::onDisconnected()
{
    QWebSocket* webSocket = qobject_cast<QWebSocket*>(sender());
    if (!webSocket) return;
    
    string sessionId = webSocket->property("sessionId").toString().toStdString();
    qDebug() << "WebSocket disconnected for session:" << sessionId.c_str();
    
    // 检查会话是否还存在，避免重复清理
    QMutexLocker locker(&m_sessionsMutex);
    auto it = m_sessions.find(sessionId);
    if (it != m_sessions.end())
    {
        locker.unlock();
        cleanupSession(sessionId);
    }
}

void RadioWebSocketClient::onBinaryMessageReceived(QByteArray message)
{
    QWebSocket* webSocket = qobject_cast<QWebSocket*>(sender());
    if (!webSocket) return;
    
    string sessionId = webSocket->property("sessionId").toString().toStdString();
    
    // 广播音频数据到分区设备
    broadcastAudioToZones(sessionId, message);
}

void RadioWebSocketClient::onTextMessageReceived(QString message)
{
    QWebSocket* webSocket = qobject_cast<QWebSocket*>(sender());
    if (!webSocket) return;
    
    string sessionId = webSocket->property("sessionId").toString().toStdString();
    qDebug() << "Received text message for session:" << sessionId.c_str() << "Message:" << message;
}

void RadioWebSocketClient::onError(QAbstractSocket::SocketError error)
{
    QWebSocket* webSocket = qobject_cast<QWebSocket*>(sender());
    if (!webSocket) return;
    
    string sessionId = webSocket->property("sessionId").toString().toStdString();
    qDebug() << "WebSocket error for session:" << sessionId.c_str() << "Error:" << error;
    
    // 检查会话是否还存在，避免重复清理
    QMutexLocker locker(&m_sessionsMutex);
    auto it = m_sessions.find(sessionId);
    if (it != m_sessions.end())
    {
        locker.unlock();
        cleanupSession(sessionId);
    }
}

void RadioWebSocketClient::onHeartbeatTimeout()
{
    QMutexLocker locker(&m_sessionsMutex);
    
    for (auto& pair : m_sessions)
    {
        if (pair.second->webSocket && pair.second->webSocket->state() == QAbstractSocket::ConnectedState)
        {
            sendHeartbeat(pair.second->webSocket);
        }
    }
}

void RadioWebSocketClient::sendPlayCommand(QWebSocket* webSocket, const string& radioUrl)
{
    QJsonObject playCommand;
    playCommand["type"] = "play_radio_event";
    playCommand["event"] = 1; // 开始播放
    playCommand["radio_url"] = QString::fromStdString(radioUrl);
    
    QJsonDocument doc(playCommand);
    webSocket->sendTextMessage(doc.toJson(QJsonDocument::Compact));
    
    qDebug() << "Sent play command for URL:" << radioUrl.c_str();
}

void RadioWebSocketClient::sendHeartbeat(QWebSocket* webSocket)
{
    QJsonObject heartbeat;
    heartbeat["type"] = "play_radio_heartbeat";
    heartbeat["timestamp"] = QDateTime::currentMSecsSinceEpoch();
    QJsonDocument doc(heartbeat);
    webSocket->sendTextMessage(doc.toJson(QJsonDocument::Compact));
}

void RadioWebSocketClient::broadcastAudioToZones(const string& sessionId, const QByteArray& audioData)
{
    QMutexLocker locker(&m_sessionsMutex);
    
    auto it = m_sessions.find(sessionId);
    if (it == m_sessions.end() || !it->second->isActive)
    {
        return;
    }
    
    RadioPlaySession& session = *(it->second);
    
    // 将音频数据添加到缓冲区
    {
        QMutexLocker bufferLocker(&session.bufferMutex);
        session.audioBuffer.append(audioData);
        #if 0
        qDebug() << "Added audio data to buffer. Session:" << sessionId.c_str() 
                 << "Data size:" << audioData.size() << "bytes"
                 << "Buffer size:" << session.audioBuffer.size() << "bytes";
        #endif
        // 如果缓冲区达到最小要求且正在缓冲状态，开始播放
        if (session.isBuffering && session.audioBuffer.size() >= MIN_BUFFER_SIZE)
        {
            session.isBuffering = false;
            session.lastPlayTime = QDateTime::currentMSecsSinceEpoch();
            session.playbackTimer->start();
            qDebug() << "Buffer filled, starting playback for session:" << sessionId.c_str();
        }
    }
}

string RadioWebSocketClient::generateSessionId()
{
    QUuid uuid = QUuid::createUuid();
    return uuid.toString(QUuid::WithoutBraces).toStdString();
}

void RadioWebSocketClient::onPlaybackTimerTimeout()
{
    QTimer* timer = qobject_cast<QTimer*>(sender());
    if (!timer) return;
    
    QMutexLocker locker(&m_sessionsMutex);
    
    // 查找对应的会话
    string targetSessionId;
    for (auto& pair : m_sessions)
    {
        if (pair.second->playbackTimer == timer)
        {
            targetSessionId = pair.first;
            break;
        }
    }
    if (targetSessionId.empty()) return;
    auto it = m_sessions.find(targetSessionId);
    if (it == m_sessions.end()) return;
    if (!it->second->isActive || it->second->isBuffering || it->second->unusedCount>0)
    {
        return;
    }
    
    RadioPlaySession& session = *(it->second);
    
    QMutexLocker bufferLocker(&session.bufferMutex);
    
    // 检查缓冲区是否有足够的数据
    if (session.audioBuffer.size() < MP3_FRAME_SIZE)
    {
        // 缓冲区数据不足，重新进入缓冲状态
        if (session.audioBuffer.size() == 0)
        {
            session.isBuffering = true;
            session.playbackTimer->stop();
            qDebug() << "Buffer empty, entering buffering state for session:" << session.sessionId.c_str();
            return;
        }
        // 如果还有少量数据，发送剩余的
    }
    
    // 从缓冲区取出数据
    int chunkSize = qMin(session.audioBuffer.size(), MP3_FRAME_SIZE);
    QByteArray audioChunk = session.audioBuffer.left(chunkSize);
    session.audioBuffer.remove(0, chunkSize);

    #if 0
    qDebug() << "Broadcasting radio audio,"
                << "Chunk size:" << audioChunk.size() << "bytes"
                << "Buffer remaining:" << session.audioBuffer.size() << "bytes";
    #endif

            
    char sendBuf[MAX_BUF_LEN] = {0};
    unsigned char dataBuf[512]={0};
    //加入sessionId
    dataBuf[0] = session.sessionId.size();
    memcpy(dataBuf+1,session.sessionId.c_str(),session.sessionId.size());

    int dataLen = 1+session.sessionId.size()+audioChunk.size();
    memcpy(dataBuf+1+session.sessionId.size(),audioChunk.data(),audioChunk.size());
    //发送音频流到相应的终端
    int len = CProtocol::ControlCommand(sendBuf,CMD_NET_RADIO_STREAM,(const char*)dataBuf,dataLen);
    

    //发送组播音频流到分配的端口
    if(g_Global.m_Sections.HasUdpSectionOnline())
    {
        g_Global.m_Network.SendUdpData(sendBuf, len, NET_RADIO_BROADCAST_ADDR, session.multicastPort, 1);
    }
    
    // 音频数据到各个分区设备(TCP)
    for (const string& zoneMac : session.zoneMacs)
    {
        CSection* pSection = g_Global.m_Sections.GetSectionByMac(zoneMac.c_str());
        if (pSection && pSection->IsOnline())
        {
            if(pSection->IsTcpMode())
            {
                if(pSection->m_kcpsocket && pSection->m_kcpsocket->GetDesPort() && pSection->m_kcpsocket->GetSendValid() && !STREAM_ALLWAYS_USED_TCP)
                {
                    pSection->m_kcpsocket->SendData(1,sendBuf,len);
                }
                else
                {
                    char* cbuf = new char[len];//足够长
                    memcpy(cbuf,sendBuf,len);
                    g_Global.m_Network.SendTcpData(cbuf, len, pSection->m_pSocketObj);
                    delete[] cbuf;
                }
            }

        }
    }
    
    // 统计发送的字节数
    session.totalSentBytes += audioChunk.size();
    session.lastPlayTime = QDateTime::currentMSecsSinceEpoch();
}

void RadioWebSocketClient::cleanupSession(const string& sessionId)
{
    QMutexLocker locker(&m_sessionsMutex);
    
    auto it = m_sessions.find(sessionId);
    if (it != m_sessions.end())
    {
        // 停止播放定时器
        if (it->second->playbackTimer)
        {
            it->second->playbackTimer->stop();
            it->second->playbackTimer->deleteLater();
        }
        
        // 停止会话检查定时器
        if (it->second->sessionCheckTimer)
        {
            it->second->sessionCheckTimer->stop();
            it->second->sessionCheckTimer->deleteLater();
        }
        
        // 停止定时检查定时器
        if (it->second->timingCheckTimer)
        {
            it->second->timingCheckTimer->stop();
            it->second->timingCheckTimer->deleteLater();
        }
        
        // 释放分配的端口
        if (it->second->multicastPort > 0)
        {
            releaseMulticastPort(it->second->multicastPort);
        }
        
        it->second->isActive = false;
        if (it->second->webSocket)
        {
            it->second->webSocket->close();
            it->second->webSocket->deleteLater();
        }
        m_sessions.erase(it);
    }
}

// 分配一个未使用的组播端口
int RadioWebSocketClient::allocateMulticastPort()
{
    QMutexLocker locker(&m_portsMutex);
    
    for (int port = MULTICAST_PORT_MIN; port <= MULTICAST_PORT_MAX; port++)
    {
        if (m_usedPorts.find(port) == m_usedPorts.end())
        {
            m_usedPorts.insert(port);
            qDebug() << "Allocated multicast port:" << port;
            return port;
        }
    }
    
    qDebug() << "No available multicast port in range" << MULTICAST_PORT_MIN << "-" << MULTICAST_PORT_MAX;
    return 0; // 没有可用端口
}

// 释放组播端口
void RadioWebSocketClient::releaseMulticastPort(int port)
{
    QMutexLocker locker(&m_portsMutex);
    
    auto it = m_usedPorts.find(port);
    if (it != m_usedPorts.end())
    {
        m_usedPorts.erase(it);
        qDebug() << "Released multicast port:" << port;
    }
}

// 检查端口是否正在使用
bool RadioWebSocketClient::isPortInUse(int port)
{
    QMutexLocker locker(&m_portsMutex);
    return m_usedPorts.find(port) != m_usedPorts.end();
}

void RadioWebSocketClient::onSessionCheckTimeout()
{
    QTimer* timer = qobject_cast<QTimer*>(sender());
    if (!timer) return;
    
    QMutexLocker locker(&m_sessionsMutex);
    
    // 查找对应的会话
    string targetSessionId;
    for (auto& pair : m_sessions)
    {
        if (pair.second->sessionCheckTimer == timer)
        {
            targetSessionId = pair.first;
            break;
        }
    }
    
    if (targetSessionId.empty()) return;
    
    auto it = m_sessions.find(targetSessionId);
    if (it == m_sessions.end()) return;
    
    // 检查是否有分区设备在使用该sessionId
    bool isSessionInUse = false;
    int sectionCount = g_Global.m_Sections.GetSecCount();
    
    for (int i = 0; i < sectionCount; ++i)
    {
        CSection& section = g_Global.m_Sections.GetSection(i);
        if (section.IsOnline() && section.IsNetRadio() && section.GetNetRadioSessionId() == targetSessionId)
        {
            isSessionInUse = true;
            break;
        }
    }
    
    if (isSessionInUse)
    {
        // 有设备在使用，重置计数器
        it->second->unusedCount = 0;
        //qDebug() << "Session" << targetSessionId.c_str() << "is in use, reset counter";
    }
    else
    {
        // 没有设备在使用，增加计数器
        it->second->unusedCount++;
        qDebug() << "Session" << targetSessionId.c_str() << "unused count:" << it->second->unusedCount;
        
        // 如果连续5秒没有设备使用，停止该client
        if (it->second->unusedCount >= 2)
        {
            qDebug() << "Session" << targetSessionId.c_str() << "unused for 2 seconds, stopping client";
            
            // 使用QTimer::singleShot延迟执行stopRadioPlay，避免在定时器槽函数中直接删除定时器
            QTimer::singleShot(0, [this, targetSessionId]() {
                stopRadioPlay(targetSessionId);
            });
        }
    }
}