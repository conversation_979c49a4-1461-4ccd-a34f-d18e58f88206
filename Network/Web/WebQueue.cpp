 #include "stdafx.h"
#include "WebQueue.h"


CVOIPTalkback::CVOIPTalkback()
{
    m_strExten1 = "";
    m_strExten2 = "";
}

void CVOIPTalkback::AddTalkback(string strExten1, string strExten2)
{
    m_strExten1 = strExten1;
    m_strExten2 = strExten2;
}


////////////////////////////////////////////////////////////////////////////

CVOIPPaging::CVOIPPaging()
{
    m_strMulSip = "";
    m_nPageType = 1;

    memset(m_szMulIP, 0, MAX_IP_LEN);
    m_vecExtens.clear();
}

void CVOIPPaging::AddPaging(string strMulSip, int nPageTpye, int nVolume, int nExtenCount, string *strExtens, int dt)
{
    m_strMulSip = strMulSip;
    m_nPageType = nPageTpye;
    m_nVolume = nVolume;
    m_nDeviceType =dt;

    // 计算组播地址，发起账号,转成16进制, 高位地址在前，低位地址在后组成
    // 如分机号码为802，则组播地址为**********
    u_short uExten = atoi(strMulSip.data());
    int   nHNum = uExten>>8;
    int   nLNum  = uExten&0x00FF;
    sprintf(m_szMulIP, "235.6.%d.%d", nHNum, nLNum);

    for(int i=0; i<nExtenCount; i++)
    {
        m_vecExtens.push_back(strExtens[i]);
    }
}

//////////////////////////////////////////////////////////////////////////

CVOIPMonitor::CVOIPMonitor()
{
    m_strExten1 = "";
    m_strExten2 = "";
}

void CVOIPMonitor::AddMonitorQ(string strExten1, string strExten2)
{
    m_strExten1 = strExten1;
    m_strExten2 = strExten2;
}




///////////////////////////////////////////////////////////////////////////

CWebQueue::CWebQueue(string strCommand, string strSocketID)
{
    m_strCommand = strCommand;
    m_strSocketID = strSocketID;
}


CWebQueue::CWebQueue(unsigned short uCmd, string strUID, string strMac)
{
    m_uCommand = uCmd;
    m_strSocketID  = strUID;
    strcpy(m_szSecMac, strMac.data());
}

LPCWebSection CWebQueue::GetWebSection()
{
    return g_Global.m_WebSections.GetWebSectionBySocketID(m_strSocketID);
}


/***********************************************/


CWebQueues::CWebQueues()
{
    m_ErrorToField[EC_SUCCESS] = "Success";
    m_ErrorToField[EC_SIP_INUSE] = "Sip InUse";
    m_ErrorToField[EC_SIP_OFFLINE] = "Sip Offine";
    m_ErrorToField[EC_SIP_NOTEXIST] = "Sip NotExist";
    m_ErrorToField[EC_SIP_RULE_NOTEXIST] = "Sip Rule NotExist";
    m_ErrorToField[EC_SIP_RULE_NOTUNIFY] = "Sip Rule Notunified";
    m_ErrorToField[EC_SIP_ERROR] = "Sip Error";

    m_ErrorToDesc[EC_SUCCESS] = "执行成功";
    m_ErrorToDesc[EC_SIP_INUSE] = "sip 繁忙";
    m_ErrorToDesc[EC_SIP_OFFLINE] = "sip 离线";
    m_ErrorToDesc[EC_SIP_NOTEXIST] = "Sip 账号不存在";
    m_ErrorToDesc[EC_SIP_RULE_NOTEXIST] = "Sip 拨号规则不存在";
    m_ErrorToDesc[EC_SIP_RULE_NOTUNIFY] = "Sip 拨号规则不统一";
    m_ErrorToDesc[EC_SIP_ERROR] = "Sip 未知错误";

    m_csCommand = PTHREAD_MUTEX_INITIALIZER;
}

CWebQueues::~CWebQueues()
{
    //20220427 静态初始化的互斥锁不需要,也不能用pthread_mutex_destroy销毁锁，否则将出错
    //pthread_mutex_destroy(&m_csCommand);
}


void CWebQueues::AddWebQueue(string strComID, LPCWebQueue pWebQueue)
{
    ::pthread_mutex_lock(&m_csCommand);
    if(pWebQueue != NULL)
    {
        m_WebQueues[strComID] = pWebQueue;
    }
    ::pthread_mutex_unlock(&m_csCommand);
}

void CWebQueues::AddCommand(string strCmd, string strSocketID, string strComID)
{
    ::pthread_mutex_lock(&m_csCommand);
    LPCWebQueue webQueue = new CWebQueue(strCmd, strSocketID);
    m_WebQueues[strComID] = webQueue;
    ::pthread_mutex_unlock(&m_csCommand);
}


void CWebQueues::AddCommand1(unsigned short uCmd, string strSocketID, string strMac)
{
    bool flag = true;
    list<LPCWebQueue>::iterator iter;

    pthread_mutex_lock(&m_csCommand);
    for(iter=m_SecQueues.begin();iter!=m_SecQueues.end();++iter)
    {
        // 避免重复添加
        if ((*iter)->m_uCommand == uCmd &&                            // 命令是否相同
             strcmp((*iter)->m_szSecMac, strMac.data()) == 0 &&       // 发送到的设备MAC是否相同
             (*iter)->m_strSocketID == strSocketID)                       // 发送源是否相同
        {
            flag = false;
            break;
        }
    }

    if(flag)
    {
        LPCWebQueue webQueue = new CWebQueue(uCmd, strSocketID, strMac);
        m_SecQueues.push_back(webQueue);
    }
    pthread_mutex_unlock(&m_csCommand);
}


void CWebQueues::RemoveQueue(string strComID)
{
    ::pthread_mutex_lock(&m_csCommand);
    if(IsExistQueue(strComID))
    {
        delete m_WebQueues[strComID];
        m_WebQueues.erase(strComID);
    }
    ::pthread_mutex_unlock(&m_csCommand);
}

// 获取服务器语音编码类型
int     CWebQueues::GetnRtpType(string strRtpType)
{
    if(strRtpType == "ulaw")            // pcmu
    {
        return 0;
    }
    else if(strRtpType == "gsm")
    {
        return 3;
    }
    else if(strRtpType == "g723")
    {
        return 4;
    }
    else if(strRtpType == "alaw")       // pcma
    {
        return 8;
    }
    else if(strRtpType == "g722")
    {
        return 9;
    }
    else if(strRtpType == "g728")
    {
        return 15;
    }
    else if(strRtpType == "g729")
    {
        return 18;
    }
    else
    {
        return -1;
    }
}

// 收到sip回复，执行组播操作
void CWebQueues::ExecutePage(string strComID, string strRtpType, int nResult)
{
    if(!IsExistQueue(strComID))
        return;

    LPCWebQueue  webQueue = m_WebQueues[strComID];
    if(webQueue->m_strCommand == "start_paging")
    {
        // 组包
        char szBuf[MAX_BUF_LEN] = {0};
        int nRtpType = GetnRtpType(strRtpType);     // 获取服务器语音编码类型
        if(nRtpType == -1)      return;

        int len = CProtocol::NotifyDevicePaging(szBuf, webQueue->m_Paging.m_szMulIP,
                                                VOIP_MULTICAST_PORT, nRtpType, webQueue->m_Paging.m_nVolume);

        // 通知设备加入组播
        for(int j=0; j<(int)webQueue->m_Paging.m_vecExtens.size(); j++)
        {
            LPCSection  pSection = g_Global.m_Sections.GetSectionBySip(webQueue->m_Paging.m_vecExtens[j]);
            if(pSection != NULL && pSection->IsOnline())
            {
                g_Global.m_Network.SendData(szBuf, len, *pSection, pSection->GetIP(), UDP_PORT, 1);
                g_Global.m_Network.m_CmdSend.AddCommand(szBuf, len, *pSection);
            }
        }

        // 通知设备发起广播
        LPCSection pSection = g_Global.m_Sections.GetSectionBySip(webQueue->m_Paging.m_strMulSip);
    }
}

// 判断是否有正在发起组播或监听的sip账号，如果有，则把所有该组播或监听的sip全部挂断
void CWebQueues::CheckSipPage(int nSipCount, SipInfo *pSipInfo, bool bRes)
{
    //LOG("CheckSipPage", LV_INFO);
    vector<string>   vecUID;

    static int nTriggerCount = 0;
    static int nMonTriggerCount = 0;
    bool isSend = false;
    for(int i = 0; i<nSipCount; i++)
    {
        string strExten = pSipInfo[i].m_strExten;
        map<string, LPCWebQueue>::iterator iter = m_WebQueues.begin();

        for(; iter != m_WebQueues.end(); iter++)        // 寻找是否有匹配的正在发起组播或监听的sip账号
        {
            string strComID = iter->first;
            LPCWebQueue  webQueue = iter->second;
            if(webQueue == NULL)
            {
                continue;
            }

            LOG(webQueue->m_strCommand.data(), LV_INFO);
            if(webQueue->m_strCommand == "start_paging")
            {
                // 组播SIP是否相同
                if(webQueue->m_Paging.m_strMulSip == strExten)
                {
                    LOG(FORMAT("%s:%d",webQueue->m_Paging.m_strMulSip.data(), nTriggerCount), LV_INFO);

                    // 过滤掉寻呼操作时SIP服务器发送的第一条sip账号空闲
                    /*nTriggerCount++;
                            if(nTriggerCount < 2 && !bRes)
                            {
                                continue;
                            }
                            nTriggerCount = 0;*/

                    LOG(FORMAT("%d", webQueue->m_Paging.m_nPageType), LV_WARNING);
                    if(webQueue->m_Paging.m_nPageType == 1 && webQueue->m_Paging.m_nDeviceType != TYPE_9312 ||   // 组播
                            bRes == TRUE)
                    {
                        int nSecCount = 0;
                        unsigned int pSecIndexs[MAX_SECTION_COUNT_FORMAL] = {0};
                        for(int k=0; k<webQueue->m_Paging.m_vecExtens.size(); k++)
                        {
                            LPCSection pSection = g_Global.m_Sections.GetSectionBySip(webQueue->m_Paging.m_vecExtens[k]);
                            if(pSection != NULL && pSection->IsOnline() && pSection->GetProSource() == PRO_PAGING)
                            {
                                pSecIndexs[nSecCount++] = pSection->GetID()-1;
                                pSection->m_SipInfo.m_nPageType = PT_TALKBACK;
                            }
                        }
                        LOG(FORMAT("nSecCount = %d\n", nSecCount), LV_INFO);
                        if(nSecCount > 0)               // 设置该组播所有分区为空闲
                        {
                            g_Global.m_Network.m_CmdSend.CmdSetIdleStatus(pSecIndexs, nSecCount);

                            //return;           // 一条组播可能有多个包的分区,循环检测
                        }

                        vecUID.push_back(strComID);
                    }
                }
            }
            else if(webQueue->m_strCommand == "start_monitor")
            {
                if(webQueue->m_Monitor.m_strExten1 == strExten)
                {
                    LOG(FORMAT("%s:%d",webQueue->m_Monitor.m_strExten1.data(), nMonTriggerCount), LV_INFO);
                    nMonTriggerCount++;
                    if(nMonTriggerCount < 2 && !bRes)
                    {
                        continue;
                    }
                    nMonTriggerCount = 0;

                    vecUID.push_back(strComID);

                }
            }
            else
            {
                vecUID.push_back(strComID);
            }
        }
    }

    // 移除命令数据
    for(int i=0; i<vecUID.size(); i++)
    {
        RemoveQueue(vecUID[i]);
    }
}

// 写日志
void CWebQueues::InsertLog(string strComID, int nResult, string hupExten)
{
    CMyString strContents;
    CMyString strDesc;
    if(nResult == 0)
    {
        strDesc = LANG_STR(LANG_SECTION_PAGE, "Success", "Success");
    }
    else
    {
        strDesc = LANG_STR(LANG_SECTION_PAGE, "Error", "Error");
    }


    string strExten = "";
    LogType type = LT_CALL_LOG;
    LPCWebQueue pWebQueue =  GetWebQueueByComID(strComID);
    if(pWebQueue != NULL)
    {
        if(pWebQueue->m_strCommand == "start_talkback")
        {
            type = LT_CALL_LOG;
            strExten = pWebQueue->m_Talkback.m_strExten1;
            strContents.Format(("%s %s %s :%d"),
                               LANG_STR(LANG_SECTION_PAGE, "Call", "Call").C_Str(),
                               pWebQueue->m_Talkback.m_strExten2.data(),
                               strDesc.Data(), nResult);
        }
        else  if(pWebQueue->m_strCommand == "start_paging")
        {
            type = LT_PAGING_LOG;
            strExten = pWebQueue->m_Paging.m_strMulSip;
            string sipArray = "{";
            for(int i=0; i<(int)pWebQueue->m_Paging.m_vecExtens.size(); i++)
            {
                sipArray += pWebQueue->m_Paging.m_vecExtens[i];
                sipArray += ",";
            }
            sipArray += "}";
            strContents.Format(("%s %s %s :%d"),
                               LANG_STR(LANG_SECTION_PAGE, "Paging", "Paging").C_Str(),
                               sipArray.data(),
                               strDesc.Data(), nResult);
        }
        else if(pWebQueue->m_strCommand == "start_monitor")
        {
            type = LT_LISTEN_LOG;
            strExten = pWebQueue->m_Monitor.m_strExten1;
            strContents.Format(("%s %s %s :%d"),
                               LANG_STR(LANG_SECTION_PAGE, "Listen", "Listen").C_Str(),
                               pWebQueue->m_Monitor.m_strExten2.data(),
                               strDesc.Data(), nResult);
        }
        else if(pWebQueue->m_strCommand == "hangup")
        {
            type = LT_CALL_LOG;
            strExten = hupExten;
            strContents.Format(("%s %s %s :%d"),
                               LANG_STR(LANG_SECTION_PAGE, "Hangup", "Hangup").C_Str(),
                               "",
                               strDesc.Data(), nResult);
        }

        g_Global.m_logTable.InsertLog(CMyString(strExten),
                                      CMyString(strExten),
                                      type,
                                      strContents);
    }
}



// sip服务器回复Web终端
void CWebQueues::ReponseCommand(string strComID, int nResult, string strExten)
{
    LPCWebQueue pWebQueue = g_Global.m_WebNetwork.m_WebQueues.GetWebQueueByComID(strComID);
    LPCWebSection pWebSection = g_Global.m_WebNetwork.m_WebQueues.GetWebSectionByComID(strComID);

    if(pWebSection != NULL)
    {
        string strCommand = pWebQueue->m_strCommand;

        if(strCommand == "start_talkback")      // 对讲
        {
            g_Global.m_WebNetwork.m_WebSend.WebResponseTalkback(pWebSection, nResult);
            InsertLog(strComID, nResult);
        }

        else if(strCommand == "start_monitor")  // 监听
        {
            g_Global.m_WebNetwork.m_WebSend.WebResponseListen(pWebSection, nResult);
            InsertLog(strComID, nResult);
        }

        else if(strCommand == "hangup")     // 挂断
        {
            g_Global.m_WebNetwork.m_WebSend.WebResponseHangup(strExten, pWebSection, nResult);
            InsertLog(strComID, nResult, strExten);
        }

        RemoveQueue(strComID);
    }
    else
    {
        LOG(FORMAT("%s %s","pWebSection is NULL", __FUNCTION__), LV_ERROR);
    }

}


bool CWebQueues::IsExistQueue(string strComID)
{
    if(m_WebQueues.count(strComID) > 0)
    {
        return true;
    }

    return false;
}

LPCWebQueue CWebQueues::GetWebQueueByComID(string strComID)
{
    if(IsExistQueue(strComID))
    {
         LPCWebQueue pWebQueue = m_WebQueues[strComID];
         return pWebQueue;
    }
    return NULL;
}

LPCWebSection CWebQueues::GetWebSectionByComID(string strComID)
{
    if(IsExistQueue(strComID))
    {
        LPCWebQueue pWebQueue =   m_WebQueues[strComID];
        LPCWebSection pWebSec = g_Global.m_WebSections.GetWebSectionBySocketID(pWebQueue->m_strSocketID);

        return pWebSec;
    }
    return NULL;
}








