#include "stdafx.h"
#include "WebCommandSend.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <algorithm>
#if defined(Q_OS_LINUX)
#include <arpa/inet.h>
#endif
#include <unistd.h>
#include "Network/Protocol/Protocol.h"
#include "math.h"


CWebCommandSend::CWebCommandSend()
{
    m_mutex = PTHREAD_MUTEX_INITIALIZER;
}


CWebCommandSend::~CWebCommandSend()
{
    //20220427 静态初始化的互斥锁不需要,也不能用pthread_mutex_destroy销毁锁，否则将出错
    //pthread_mutex_destroy(&m_mutex);
}


// 获取客户端状态
void CWebCommandSend::WebGetClientStatus(CWebSection* pWebSec)    // 控制设备
{
    string strBuf = CWebProtocol::CmdGetClientStatus();

    //ForwardDataToWeb(pWebSec, strBuf, false,false);
    ForwardDataToWeb(pWebSec, strBuf);
}

// 发送更新文件信息
void CWebCommandSend::SendFileInfo(CWebSection &pWebSec,    // 设备
                                   int          nFileType)  // 文件类型
{
    string updateTime;

    string pathName = HTTP_FOLDER_SEPARATOR;
    pathName += HTTP_FOLDER_XML;
    pathName += HTTP_FOLDER_SEPARATOR;
    switch(nFileType)
    {
    case FILE_GROUP:
    {
        updateTime = g_Global.m_Groups.GetDateTime().C_Str();
        pathName += HTTP_FILE_GROUP;
        break;
    }
    case FILE_PLAYLIST:
    {
        updateTime = g_Global.m_PlayList.GetDateTime().C_Str();
        pathName += HTTP_FILE_PLAYLIST;
        break;
    }
    case FILE_TIMER:
    {
        updateTime = g_Global.m_TimerScheme.GetDateTime().C_Str();
        pathName += HTTP_FILE_TIMER;
        break;
    }
    case FILE_SECTION:
    {
        updateTime = g_Global.m_Sections.GetDateTime().C_Str();
        pathName += HTTP_FILE_SECTION;
        break;
    }
    case FILE_AUDIO_COLLECTOR:
    {
        updateTime = g_Global.m_AudioCollectors.GetDateTime().C_Str();
        pathName += HTTP_FILE_AUDIO_COLLECTOR;
        break;
    }
    case FILE_FIRE_COLLECTOR:
    {
        updateTime = g_Global.m_FireCollectors.GetDateTime().C_Str();
        pathName += HTTP_FILE_FIRE_COLLECTOR;
        break;
    }
    case FILE_MONITOR:
    {
        updateTime = g_Global.m_Monitors.GetDateTime().C_Str();
        pathName += HTTP_FILE_MONITOR;
        break;
    }
    case FILE_USER:
    {
        updateTime = g_Global.m_Users.GetDateTime().C_Str();
        pathName += HTTP_FILE_USER;
        break;
    }
    case FILE_SEQUENCE_POWER:
    {
        updateTime = g_Global.m_SequencePower.GetDateTime().C_Str();
        pathName += HTTP_FILE_SEQUENCE_POWER;
        break;
    }
    case FILE_PAGER:
    {
        updateTime = g_Global.m_Pagers.GetDateTime().C_Str();
        pathName += HTTP_FILE_PAGER;
        break;
    }
    #if SUPPORT_REMOTE_CONTROLER
    case FILE_REMOTE_CONTROLER:
    {
        updateTime = g_Global.m_RemoteControlers.GetDateTime().C_Str();
        pathName += HTTP_FILE_REMOTE_CONTROLER;
        break;
    }
    #endif
    #if SUPPORT_AUDIO_MIXER
    case FILE_AUDIO_MIXER:
    {
        updateTime = g_Global.m_AudioMixers.GetDateTime().C_Str();
        pathName += HTTP_FILE_AUDIO_MIXER;
        break;
    }
    #endif
    default:
        return;
    }

    string strBuf = CWebProtocol::CmdNotifyUpdateFile(nFileType,
                                                      updateTime,
                                                      CNetwork::GetHostIP(),
                                                      std::to_string(g_Global.m_HTTP_PORT),
                                                      pathName);
    CommandSend(strBuf.data(), pWebSec);
}

// 发送单个分区状态到Web设备
void CWebCommandSend::WebSendOneZoneInfo(int nDeviceType,                   // 设备类型
                                         CSection *pSection,              // 终端设备
                                         CWebSection* pWebSec,     // 控制设备
                                         BOOL isPrint)
{
    if(nDeviceType < DEVICE_SECTION || nDeviceType >= DEVICE_TYPE_COUNT )
    {
        return;
    }
    if(pSection == NULL)
        return;
    //如果是分区设备,又是未注册版本，那么只发送受限的分区个数
    if(nDeviceType == DEVICE_SECTION)
    {
        if(pSection->GetID() > MAX_SECTION_COUNT)
        {
            return;
        }
    }

    //判断WEB设备是否拥有此分区的权限，如果没有，那么不发送。
    if(pWebSec == NULL)
        return;
    LPCUserInfo pDestUser =  g_Global.m_Users.GetUserByID(pWebSec->GetSocketID());
    if(pDestUser == NULL)
        return;
    if(!pDestUser->HasFoundSectionMac(pSection->GetMac()))
    {
        return;
    }

    // 发送指定设备信息到WebSec
    ZoneInfo ZoneList[1];

    ZoneList[0].nID             = pSection->GetID();
    ZoneList[0].strExten        = pSection->m_SipInfo.m_szAccount;
    ZoneList[0].nExtenStatus    = pSection->m_SipInfo.GetWebStatus();
    ZoneList[0].strIP           = pSection->GetIP();
    ZoneList[0].strMac          = pSection->GetMac();
    ZoneList[0].strMonitorMac   = pSection->GetMonitorMac();
    LPCMonitorInfo pMonitor     = g_Global.m_Monitors.GetMonitorByMac(pSection->GetMonitorMac());
    ZoneList[0].monitorStatus   = (pMonitor == NULL) ? MS_OFFLINE : pMonitor->GetMonStatus();
    ZoneList[0].strName         = pSection->GetUTFName();
    if(!pSection->IsExtraFeatureValid())
    {
        ZoneList[0].strName.append("(未授权)");
    }

    ZoneList[0].nVolume         = pSection->GetVolume();
    ZoneList[0].nSource         = pSection->GetProSource();
    ZoneList[0].strStreamingMac = "";
    ZoneList[0].strStreamingIP = "";
    ZoneList[0].nStreamingMode = 0;
    ZoneList[0].nStreamingStatus = 0;

    // 节目源名称
    if(CProtocol::IsAudioCollectorSrc(pSection->GetProSource()))
    {
        CSection* pSec = g_Global.m_AudioCollectors.GetSectionBySrcID(pSection->GetProSource());
        if(pSec != NULL)
        {
            //判断属于哪个音频通道
            unsigned char channel_num = (pSection->GetProSource()-PRO_AUDIO_COLLECTOR_MIN)%4 +1;
            //ZoneList[0].strSourceName = pSec->GetUTFName() + " (CH" + to_string(channel_num) + ")";
            ZoneList[0].strSourceName  = pSec->GetUTFName() + " (" + pSec->m_pAudioCollector->GetChannelNameById(channel_num) + ")";
        }
    }
    else
    {
        //空闲、本地播放、寻呼及100V音源节目名称置空
        if(pSection->GetProSource() == PRO_IDLE || pSection->GetProSource() == PRO_ANALOG_INPUT || pSection->GetProSource() == PRO_PAGING || pSection->GetProSource() == PRO_100V)
        {
            ZoneList[0].strSourceName = "";
        }
        else
            ZoneList[0].strSourceName      = pSection->GetProName();
    }

    ZoneList[0].nPlayID            = pSection->GetPlayID();
    ZoneList[0].nPlayStatus        = pSection->GetPlayStatus();
    ZoneList[0].nDeviceModel       = pSection->GetDeviceModel();
    ZoneList[0].bTimerValid        = !pSection->IsTimerInvalid();
    ZoneList[0].strFirmwareVersion = pSection->GetVersion();
    ZoneList[0].strMemory          = pSection->m_strMemory.C_Str();

    if(pSection->IsOnline())
        ZoneList[0].deviceFeature         = pSection->GetDeviceFeature();
    else
        ZoneList[0].deviceFeature          = 0;

    ZoneList[0].networkMode               = pSection->GetNetworkMode();

    //20230601，新增4G信号质量及4G ICCID卡号
    ZoneList[0].nModule4GSignalLevel      = CSection::GetModule4GCSQ_LevelByRssi(pSection->GetModule4GCSQ_Rssi());
    ZoneList[0].strModule4GIccid          = pSection->GetModule4G_ICCID();

    string strBuf = CWebProtocol::CmdForwardZoneInfo(nDeviceType, 1, 1, 1,ZoneList);

    ForwardDataToWeb(pWebSec, strBuf, false, isPrint);
}





// 发送变化的分区状态到Web设备
void CWebCommandSend::WebSendVaryZoneInfo(int nDeviceType,                   // 设备类型
                                         vector<string> &vecSectionMac,              // 终端设备
                                         CWebSection* pWebSec,     // 控制设备
                                         BOOL isPrint)
{
    if(nDeviceType < DEVICE_SECTION || nDeviceType >= DEVICE_TYPE_COUNT )
    {
        return;
    }
    //判断WEB设备是否拥有此分区的权限，如果没有，那么不发送。
    if(pWebSec == NULL || vecSectionMac.size() == 0)
        return;
    LPCUserInfo pDestUser =  g_Global.m_Users.GetUserByID(pWebSec->GetSocketID());
    if(pDestUser == NULL)
        return;
    vector<string> vecSectionMacc=vecSectionMac;
    pDestUser->GetValidSectionMacArray(vecSectionMacc);
    if(vecSectionMacc.size() == 0)
        return;

    // 发送指定设备信息到WebSec
    int zone_cnt=vecSectionMacc.size();
    if(zone_cnt == 0)
        return;
    int real_zone_index=0;
    ZoneInfo ZoneList[zone_cnt];

    //取出相应的sections
    for(int i = 0; i < zone_cnt; i++)
    {
        CSection *pSection = g_Global.m_Sections.GetSectionByMac(vecSectionMacc[i]);

        //如果没有从有效mac数组中找到，那么跳过
        if(!pSection)
        {
            continue;
        }

        //如果是分区设备,又是未注册版本，那么只发送受限的分区个数
        if(nDeviceType == DEVICE_SECTION)
        {
            if(pSection->GetID() > MAX_SECTION_COUNT)
            {
                continue;
            }
        }
        
        ZoneList[real_zone_index].nID             = pSection->GetID();
        ZoneList[real_zone_index].strExten        = pSection->m_SipInfo.m_szAccount;
        ZoneList[real_zone_index].nExtenStatus    = pSection->m_SipInfo.GetWebStatus();
        ZoneList[real_zone_index].strIP           = pSection->GetIP();
        ZoneList[real_zone_index].strMac          = pSection->GetMac();
        ZoneList[real_zone_index].strMonitorMac   = pSection->GetMonitorMac();
        LPCMonitorInfo pMonitor     = g_Global.m_Monitors.GetMonitorByMac(pSection->GetMonitorMac());
        ZoneList[real_zone_index].monitorStatus   = (pMonitor == NULL) ? MS_OFFLINE : pMonitor->GetMonStatus();
        ZoneList[real_zone_index].strName         = pSection->GetUTFName();
        if(!pSection->IsExtraFeatureValid())
        {
            ZoneList[real_zone_index].strName.append("(未授权)");
        }
        ZoneList[real_zone_index].nVolume         = pSection->GetVolume();
        ZoneList[real_zone_index].nSource         = pSection->GetProSource();
        ZoneList[real_zone_index].strStreamingMac = "";
        ZoneList[real_zone_index].strStreamingIP = "";
        ZoneList[real_zone_index].nStreamingMode = 0;
        ZoneList[real_zone_index].nStreamingStatus = 0;

        // 节目源名称
        if(CProtocol::IsAudioCollectorSrc(pSection->GetProSource()))
        {
            CSection* pSec = g_Global.m_AudioCollectors.GetSectionBySrcID(pSection->GetProSource());
            if(pSec != NULL)
            {
                //判断属于哪个音频通道
                unsigned char channel_num = (pSection->GetProSource()-PRO_AUDIO_COLLECTOR_MIN)%4 +1;
                //ZoneList[real_zone_index].strSourceName = pSec->GetUTFName() + " (CH" + to_string(channel_num) + ")";
                ZoneList[real_zone_index].strSourceName  = pSec->GetUTFName() + " (" + pSec->m_pAudioCollector->GetChannelNameById(channel_num) + ")";
            }
        }
        else
        {
            //空闲、本地播放、寻呼及100V音源节目名称置空
            if(pSection->GetProSource() == PRO_IDLE || pSection->GetProSource() == PRO_ANALOG_INPUT || pSection->GetProSource() == PRO_PAGING || pSection->GetProSource() == PRO_100V)
            {
                ZoneList[real_zone_index].strSourceName = "";
            }
            else
                ZoneList[real_zone_index].strSourceName      = pSection->GetProName();
        }

        ZoneList[real_zone_index].nPlayID            = pSection->GetPlayID();
        ZoneList[real_zone_index].nPlayStatus        = pSection->GetPlayStatus();
        ZoneList[real_zone_index].nDeviceModel       = pSection->GetDeviceModel();
        ZoneList[real_zone_index].bTimerValid        = !pSection->IsTimerInvalid();
        ZoneList[real_zone_index].strFirmwareVersion = pSection->GetVersion();
        ZoneList[real_zone_index].strMemory          = pSection->m_strMemory.C_Str();

        if(pSection->IsOnline())
            ZoneList[real_zone_index].deviceFeature         = pSection->GetDeviceFeature();
        else
            ZoneList[real_zone_index].deviceFeature          = 0;

        ZoneList[real_zone_index].networkMode               = pSection->GetNetworkMode();

        //20230601，新增4G信号质量及4G ICCID卡号
        ZoneList[real_zone_index].nModule4GSignalLevel      = CSection::GetModule4GCSQ_LevelByRssi(pSection->GetModule4GCSQ_Rssi());
        ZoneList[real_zone_index].strModule4GIccid          = pSection->GetModule4G_ICCID();

        real_zone_index++;
    }

    //printf("WebSendVaryZoneInfo:zone_cnt=%d,real_zone_cnt=%d\n",zone_cnt,real_zone_index);
    if(real_zone_index == 0)
        return;
    string strBuf = CWebProtocol::CmdForwardZoneInfo(nDeviceType, 1, 1, real_zone_index,ZoneList);

    ForwardDataToWeb(pWebSec, strBuf, false, isPrint);
}



#if 0
// 根据页数 发送分区信息
void CWebCommandSend::WebSendPageZoneInfo(int nDeviceType,          // 设备类型
                                          int nPage,                // 页数
                                          CWebSection *pWebSec,     // 终端设备
                                          bool bAllZone)            // 是否请求全部分区
{
    if(nDeviceType < DEVICE_SECTION || nDeviceType >= DEVICE_TYPE_COUNT )
    {
        return;
    }

    CSections* pDevices = g_Global.m_pAllDevices[nDeviceType];
    int nSecCount = pDevices->GetSecCount();
    int nPageCount = nSecCount/20;
    if(nSecCount%20 >= 0)             nPageCount++;

    if(nPage > nPageCount)      return;                 // 请求页数大于总页数，返回

    int start = (nPage - 1)*MAX_PAGE_COUNT;
    int end = (nPageCount == nPage ? nSecCount : nPage*MAX_PAGE_COUNT);
    int nCount = end-start;

    ZoneInfo ZoneList[nCount];
    for(int i=0; i<nCount; i++)
    {
        // 分区id = 页数*20 + 每页分区索引 + 1 （分区ID从1开始）
        CSection &pSec = pDevices->GetSection(i + start);

        ZoneList[i].nID           = pSec.GetID();
        ZoneList[i].strExten      = pSec.m_SipInfo.m_szAccount;
        ZoneList[i].nExtenStatus  = pSec.m_SipInfo.GetWebStatus();
        ZoneList[i].strIP         = pSec.GetIP();
        ZoneList[i].strMac        = pSec.GetMac();
        ZoneList[i].strMonitorMac = pSec.GetMonitorMac();
        LPCMonitorInfo pMonitor   = g_Global.m_Monitors.GetMonitorByMac(ZoneList[i].strMonitorMac.data());
        ZoneList[i].monitorStatus = (pMonitor == NULL) ? MS_OFFLINE : pMonitor->GetMonStatus();
        ZoneList[i].strName       = pSec.GetUTFName();
        ZoneList[i].nVolume       = pSec.GetVolume();
        ZoneList[i].nSource       = pSec.GetProSource();

        ZoneList[i].strStreamingMac = "";
        ZoneList[i].strStreamingIP = "";
        ZoneList[i].nStreamingMode = 0;
        ZoneList[i].nStreamingStatus = 0;

        // 节目源为音频采集器
        if(CProtocol::IsAudioCollectorSrc(pSec.GetProSource()))
        {
            CSection* pAudioCol = g_Global.m_AudioCollectors.GetSectionBySrcID(pSec.GetProSource());

            if(pAudioCol != NULL)
            {
                //判断属于哪个音频通道
                unsigned char channel_num = (pSec.GetProSource()-PRO_AUDIO_COLLECTOR_MIN)%4 +1;
                //ZoneList[i].strSourceName  = pAudioCol->GetUTFName() + " (CH" + to_string(channel_num) + ")";
                ZoneList[i].strSourceName  = pAudioCol->GetUTFName() + " (" + pAudioCol->m_pAudioCollector->GetChannelNameById(channel_num) + ")";
            }
        }
        else
        {
            //寻呼及100V音源节目名称置空
            if(pSec.GetProSource() == PRO_IDLE || pSec.GetProSource() == PRO_ANALOG_INPUT || pSec.GetProSource() == PRO_PAGING || pSec.GetProSource() == PRO_100V)
            {
                ZoneList[i].strSourceName = "";
            }
            else
                ZoneList[i].strSourceName = pSec.GetProName();
        }

        ZoneList[i].nPlayID            = pSec.GetPlayID();
        ZoneList[i].nPlayStatus        = pSec.GetPlayStatus();
        ZoneList[i].nDeviceModel       = pSec.GetDeviceModel();
        ZoneList[i].bTimerValid        = !pSec.IsTimerInvalid();
        ZoneList[i].strFirmwareVersion = pSec.GetVersion();
        ZoneList[i].strMemory          = pSec.m_strMemory.C_Str();

        if(pSec.IsOnline())
            ZoneList[i].deviceFeature   = pSec.GetDeviceFeature();
        else
            ZoneList[i].deviceFeature   = 0;
    }

    string strBuf = CWebProtocol::CmdForwardZoneInfo(nDeviceType, nPageCount, nPage, nCount, ZoneList, bAllZone);
    ForwardDataToWeb(pWebSec, strBuf, false, false, bAllZone);
}

void CWebCommandSend::WebSendAllZoneInfo(int nDeviceType, CWebSection *pWebSec)
{
    CSections* pDevices = g_Global.m_pAllDevices[nDeviceType];
    int nSecCount = pDevices->GetSecCount();
    int nPageCount = nSecCount/20;
    if(nSecCount%20 >= 0)             nPageCount++;

    for(int i=1; i<=nPageCount; i++)
    {
        WebSendPageZoneInfo(nDeviceType, i, pWebSec, true);
    }
}
#endif

// 一次性发送所有分区信息
void CWebCommandSend::WebSendAllZoneInfo_Once(int nDeviceType, CWebSection *pWebSec)
{
    if(nDeviceType < DEVICE_SECTION || nDeviceType >= DEVICE_TYPE_COUNT )
    {
        return;
    }

    CSections* pDevices = g_Global.m_pAllDevices[nDeviceType];
    int nSecCount = pDevices->GetSecCount();
    
    //如果是分区设备,又是未注册版本，那么只发送受限的分区个数
    if(nDeviceType == DEVICE_SECTION)
    {
        if(nSecCount > MAX_SECTION_COUNT)
        {
            nSecCount = MAX_SECTION_COUNT;
        }
    }

    //判断WEB设备是否拥有此分区的权限，如果没有，那么不发送。
    if(pWebSec == NULL)
        return;
    LPCUserInfo pDestUser =  g_Global.m_Users.GetUserByID(pWebSec->GetSocketID());
    if(pDestUser == NULL)
        return;
    vector<string> vecSectionMac;
    for(int i=0;i<nSecCount;i++)
    {
        vecSectionMac.push_back(pDevices->GetSectionMac(i));
    }
    pDestUser->GetValidSectionMacArray(vecSectionMac);

    int real_zone_index=0;
    ZoneInfo ZoneList[nSecCount];
    for(int i=0; i<nSecCount; i++)
    {
        CSection &pSec = pDevices->GetSection(i);
        //如果没有从有效mac数组中找到，那么跳过
        if(std::find(vecSectionMac.begin(),vecSectionMac.end(),pSec.GetMac()) == vecSectionMac.end())
        {
            continue;
        }
        ZoneList[real_zone_index].nID           = pSec.GetID();
        ZoneList[real_zone_index].strExten      = pSec.m_SipInfo.m_szAccount;
        ZoneList[real_zone_index].nExtenStatus  = pSec.m_SipInfo.GetWebStatus();
        ZoneList[real_zone_index].strIP         = pSec.GetIP();
        ZoneList[real_zone_index].strMac        = pSec.GetMac();
        ZoneList[real_zone_index].strMonitorMac = pSec.GetMonitorMac();
        LPCMonitorInfo pMonitor   = g_Global.m_Monitors.GetMonitorByMac(pSec.GetMonitorMac());
        ZoneList[real_zone_index].monitorStatus = (pMonitor == NULL) ? MS_OFFLINE : pMonitor->GetMonStatus();
        ZoneList[real_zone_index].strName       = pSec.GetUTFName();
        if(!pSec.IsExtraFeatureValid())
        {
            ZoneList[real_zone_index].strName.append("(未授权)");
        }
        ZoneList[real_zone_index].nVolume       = pSec.GetVolume();
        ZoneList[real_zone_index].nSource       = pSec.GetProSource();

        ZoneList[real_zone_index].strStreamingMac = "";
        ZoneList[real_zone_index].strStreamingIP = "";
        ZoneList[real_zone_index].nStreamingMode = 0;
        ZoneList[real_zone_index].nStreamingStatus = 0;

        // 节目源为音频采集器
        if(CProtocol::IsAudioCollectorSrc(pSec.GetProSource()))
        {
            CSection* pAudioCol = g_Global.m_AudioCollectors.GetSectionBySrcID(pSec.GetProSource());

            if(pAudioCol != NULL)
            {
                //判断属于哪个音频通道
                unsigned char channel_num = (pSec.GetProSource()-PRO_AUDIO_COLLECTOR_MIN)%4 +1;
                //ZoneList[real_zone_index].strSourceName  = pAudioCol->GetUTFName() + " (CH" + to_string(channel_num) + ")";
                ZoneList[real_zone_index].strSourceName  = pAudioCol->GetUTFName() + " (" + pAudioCol->m_pAudioCollector->GetChannelNameById(channel_num) + ")";
            }
        }
        else
        {
            //寻呼及100V音源节目名称置空
            if(pSec.GetProSource() == PRO_IDLE || pSec.GetProSource() == PRO_ANALOG_INPUT || pSec.GetProSource() == PRO_PAGING || pSec.GetProSource() == PRO_100V)
            {
                ZoneList[real_zone_index].strSourceName = "";
            }
            else
                ZoneList[real_zone_index].strSourceName = pSec.GetProName();
        }

        ZoneList[real_zone_index].nPlayID            = pSec.GetPlayID();
        ZoneList[real_zone_index].nPlayStatus        = pSec.GetPlayStatus();
        ZoneList[real_zone_index].nDeviceModel       = pSec.GetDeviceModel();
        ZoneList[real_zone_index].bTimerValid        = !pSec.IsTimerInvalid();
        ZoneList[real_zone_index].strFirmwareVersion = pSec.GetVersion();
        ZoneList[real_zone_index].strMemory          = pSec.m_strMemory.C_Str();
        if(pSec.IsOnline())
            ZoneList[real_zone_index].deviceFeature   = pSec.GetDeviceFeature();
        else
            ZoneList[real_zone_index].deviceFeature   = 0;
        ZoneList[real_zone_index].networkMode         = pSec.GetNetworkMode();

        //20230601，新增4G信号质量及4G ICCID卡号
        ZoneList[real_zone_index].nModule4GSignalLevel      = CSection::GetModule4GCSQ_LevelByRssi(pSec.GetModule4GCSQ_Rssi());
        ZoneList[real_zone_index].strModule4GIccid          = pSec.GetModule4G_ICCID();

        real_zone_index++;
    }

    string strBuf = CWebProtocol::CmdForwardZoneInfo(nDeviceType, 1, 1, real_zone_index, ZoneList, 1);
    ForwardDataToWeb(pWebSec, strBuf, false, false, 1);
}


void CWebCommandSend::WebSendGroupInfo(char* groupId, CWebSection *pWebSec)
{
    int nGroupCount = g_Global.m_Groups.GetGroupCount();
    GroupInfo GroupList[nGroupCount];

    //判断WEB用户是否正常
    if(pWebSec == NULL)
        return;
    LPCUserInfo pDestUser =  g_Global.m_Users.GetUserByID(pWebSec->GetSocketID());
    if(pDestUser == NULL)
        return;

    int real_group_index=0;
    for(int i=0; i<nGroupCount; i++)
    {
        GroupList[real_group_index].m_strID = g_Global.m_Groups.GetGroupID(i);
        GroupList[real_group_index].m_szGroupName = g_Global.m_Groups.GetGroupName(i);
        GroupList[real_group_index].m_zone_cunt = g_Global.m_Groups.GetGroup(i).GetSecCount();
        GroupList[real_group_index].m_strAccount = g_Global.m_Groups.GetGroup(i).GetUserAccount();
        
        string group_account=g_Global.m_Groups.GetGroup(i).GetUserAccount().Data();
        //判断当前web端用户是不是当前group的创建者或者它的上级
        vector<string> vecParAccount;
        g_Global.m_Users.GetAllParentUserAccountByAccount(group_account,vecParAccount);
        if( !(group_account == pDestUser->GetAccount() || pDestUser->IsSuperUser() ||
            std::find(vecParAccount.begin(),vecParAccount.end(),pDestUser->GetAccount()) != vecParAccount.end()) )
        {
            continue;
        }

        for(int j=0;j< GroupList[real_group_index].m_zone_cunt;j++)
        {
            GroupList[real_group_index].m_vecSecMac.push_back(g_Global.m_Groups.GetGroup(i).GetSecMac(j));
        }
        real_group_index++;
    }
    string strBuf = CWebProtocol::CmdForwardGroupInfo(g_Global.m_Groups.GetDateTime().C_Str(),real_group_index,GroupList, 0);
   ForwardDataToWeb(pWebSec, strBuf, false, false, 1);
}



void CWebCommandSend::WebSendPlaylistInfo(char* playlistId, CWebSection *pWebSec)
{
    int nPlaylistCount = g_Global.m_PlayList.GetListCount();
    PlaylistInfo PlayList[nPlaylistCount];
   
    //判断WEB用户是否正常
    if(pWebSec == NULL)
        return;
    LPCUserInfo pDestUser =  g_Global.m_Users.GetUserByID(pWebSec->GetSocketID());
    if(pDestUser == NULL)
        return;

    int real_playlist_index=0;

    for(int i=0; i<nPlaylistCount; i++)
    {
        PlayList[real_playlist_index].m_strID = g_Global.m_PlayList.GetListID(i);
        PlayList[real_playlist_index].m_szPlaylistName = g_Global.m_PlayList.GetListName(i);
        PlayList[real_playlist_index].m_userAccount = g_Global.m_PlayList.GetListUserAccount(i);

        string playlist_account=g_Global.m_PlayList.GetListUserAccount(i).Data();
        //任何用户均可以查看管理员创建的歌曲列表（以前的做法，现在舍弃）
        //********子用户默认不允许查看管理员创建的歌曲列表
        //当前用户可以查看自身+下级所有用户创建的歌曲列表
        //******** 还是恢复所有子账户能够查看管理员创建的歌曲列表，学校里面还是有很多共同的歌曲文件需要管理员处理，比如铃声
        vector<userParm>  vecSubUserInfo;
        g_Global.m_Users.GetAllSubUserByAccount(pDestUser->GetAccount(), vecSubUserInfo);
        vector<string> vecSubAccount;
        for(int k=0;k<vecSubUserInfo.size();k++)
        {
            vecSubAccount.push_back(vecSubUserInfo[k].strAccount);
        }

        bool permission_ok=false;
       
        #if APP_IS_LZY_LIMIT_STORAGE
            #if SUPPORT_ADMIN_VIEW_ALL_USER_PLAYLIST
            if( playlist_account == pDestUser->GetAccount() || pDestUser->IsSuperUser() )
            {
                permission_ok=true;
            }
            #else
            if( playlist_account == pDestUser->GetAccount() )
            {
                permission_ok=true;
            }
            #endif
        #else
            #if SUPPORT_ALL_ACCOUNT_VIEW_ADMIN_DIR_AND_SONG
            if( playlist_account == SUPER_USER_NAME ||
                playlist_account == pDestUser->GetAccount() || pDestUser->IsSuperUser() || pDestUser->HasLimits(USER_LIMITS_PLAYLIST) ||
                std::find(vecSubAccount.begin(),vecSubAccount.end(),playlist_account) != vecSubAccount.end())
            {
                permission_ok=true;
            }
            #else
            if(playlist_account == pDestUser->GetAccount() || pDestUser->IsSuperUser() || pDestUser->HasLimits(USER_LIMITS_PLAYLIST) ||
            std::find(vecSubAccount.begin(),vecSubAccount.end(),playlist_account) != vecSubAccount.end())
            {
                permission_ok=true;
            }
            #endif
        #endif

        //TTS LIST(index=0)都能看到，但是里面的歌曲需要筛选
        if( !(i == 0 || permission_ok ) )
        {
            continue;
        }

        PlayList[real_playlist_index].m_songs_cunt = g_Global.m_PlayList.GetSongList(i).GetSongCount();
        WebSongInfo song[PlayList[real_playlist_index].m_songs_cunt];
        int real_song_index=0;
        for(int j=0;j< PlayList[real_playlist_index].m_songs_cunt;j++)
        {  
            song[real_song_index].duration=g_Global.m_PlayList.GetSongList(i).GetSongDuration(j);
            song[real_song_index].songName=g_Global.m_PlayList.GetSongList(i).GetSongName(j);
            song[real_song_index].songPathName=g_Global.m_PlayList.GetSongList(i).GetSongPathName(j);
            song[real_song_index].songBitRate=g_Global.m_PlayList.GetSongList(i).GetSongBitRate(j);
            song[real_song_index].songMd5=g_Global.m_PlayList.GetSongList(i).GetSongMd5(j).data();
            song[real_song_index].songAccount=g_Global.m_PlayList.GetSongList(i).GetSongAccount(j);
            //******** 如果是龙之音V1版本，那么不允许查看管理员的歌曲，即使管理员手动添加歌曲到子用户的列表上
            #if APP_IS_LZY_LIMIT_STORAGE
                #if SUPPORT_ADMIN_VIEW_ALL_USER_PLAYLIST
                if(!(pDestUser->IsSuperUser() || song[real_song_index].songAccount == pDestUser->GetAccount() ))
                {
                    continue;
                }
                #else
                if(!(song[real_song_index].songAccount == pDestUser->GetAccount() ))
                {
                    continue;
                }
                #endif
            #else
            {
                #if SUPPORT_ALL_ACCOUNT_VIEW_ADMIN_DIR_AND_SONG
                if(!(song[real_song_index].songAccount == SUPER_USER_NAME || 
                    pDestUser->IsSuperUser() || song[real_song_index].songAccount == pDestUser->GetAccount() || pDestUser->HasLimits(USER_LIMITS_PLAYLIST) || 
                    std::find(vecSubAccount.begin(),vecSubAccount.end(),song[real_song_index].songAccount.Data()) != vecSubAccount.end()))
                {
                    continue;
                }
                #elif SUPPORT_SUB_ACCOUNT_VIEW_SELF_LIST_ADMIN_SONG
                if(!(pDestUser->IsSuperUser() || (song[real_song_index].songAccount == SUPER_USER_NAME && i!=0)  || song[real_song_index].songAccount == pDestUser->GetAccount() || pDestUser->HasLimits(USER_LIMITS_PLAYLIST) ||
                    std::find(vecSubAccount.begin(),vecSubAccount.end(),song[real_song_index].songAccount.Data()) != vecSubAccount.end()))
                {
                    continue;
                }
                #else
                if(!(pDestUser->IsSuperUser() || song[real_song_index].songAccount == pDestUser->GetAccount() || pDestUser->HasLimits(USER_LIMITS_PLAYLIST) ||
                    std::find(vecSubAccount.begin(),vecSubAccount.end(),song[real_song_index].songAccount.Data()) != vecSubAccount.end()))
                {
                    continue;
                }
                #endif
            }
            #endif
            song[real_song_index].alive=g_Global.m_PlayList.GetSongList(i).GetSongAlive(j);
            PlayList[real_playlist_index].m_vecSongInfo.push_back(song[real_song_index]);

            real_song_index++;
        }
        PlayList[real_playlist_index].m_songs_cunt = real_song_index;

        real_playlist_index++;
    }

    string strBuf = CWebProtocol::CmdForwardPlaylistInfo(g_Global.m_PlayList.GetDateTime().C_Str(),real_playlist_index,PlayList, 0);
   ForwardDataToWeb(pWebSec, strBuf, false, false, 1);
}



void CWebCommandSend::WebSendTimerInfo(int schemeId, CWebSection *pWebSec)
{
    #if 0
    int nSchemeCount = g_Global.m_TimerScheme.GetSchemeCount();

    TimerSchemeInfo TimerScheme[nSchemeCount];
   
    for(int i=0; i<nSchemeCount; i++)
    {
        TimerScheme[i].schemeId = i+1;
        TimerScheme[i].schemeName = g_Global.m_TimerScheme.GetSchemeName(i);
        TimerScheme[i].m_TimerPoint_Cunt = g_Global.m_TimerScheme.GetScheme(i).GetTimerCount();
        WebTimerPointInfo TimerPoint[TimerScheme[i].m_TimerPoint_Cunt];
        for(int j=0;j< TimerScheme[i].m_TimerPoint_Cunt;j++)
        {  
            TimerPoint[i].m_nID=g_Global.m_TimerScheme.GetScheme(i).GetTimer(j).GetID();
            TimerPoint[i].m_strName=g_Global.m_TimerScheme.GetScheme(i).GetTimer(j).GetName();
            TimerPoint[i].m_bValid=g_Global.m_TimerScheme.GetScheme(i).GetTimer(j).IsValid();
            TimerPoint[i].m_bPlayToEnd=g_Global.m_TimerScheme.GetScheme(i).GetTimer(j).IsPlayToEnd();
            TimerPoint[i].m_bResumePlaying=g_Global.m_TimerScheme.GetScheme(i).GetTimer(j).GetResumePlaying();
            TimerPoint[i].m_bIntercut=g_Global.m_TimerScheme.GetScheme(i).GetTimer(j).GetIntercut();
            TimerPoint[i].m_bFollowDevice=g_Global.m_TimerScheme.GetScheme(i).GetTimer(j).GetFollowDevice();
            TimerPoint[i].m_bSinglePlay=g_Global.m_TimerScheme.GetScheme(i).GetTimer(j).GetSinglePlay();
            TimerPoint[i].m_nVolume=g_Global.m_TimerScheme.GetScheme(i).GetTimer(j).GetVolume();

            TimerPoint[i].m_DateStart=g_Global.m_TimerScheme.GetScheme(i).GetTimer(j).GetDateStart();
            TimerPoint[i].m_DateEnd=g_Global.m_TimerScheme.GetScheme(i).GetTimer(j).GetDateEnd();
            TimerPoint[i].m_TimeStart=g_Global.m_TimerScheme.GetScheme(i).GetTimer(j).GetTimeStart();
            TimerPoint[i].m_TimeEnd=g_Global.m_TimerScheme.GetScheme(i).GetTimer(j).GetTimeEnd();

            TimerPoint[i].m_Type=g_Global.m_TimerScheme.GetScheme(i).GetTimer(j).GetTimeType();
            TimerPoint[i].m_nPlayMode=g_Global.m_TimerScheme.GetScheme(i).GetTimer(j).GetPlayMode();
            memcpy(TimerPoint[i].m_SelectedDays,g_Global.m_TimerScheme.GetScheme(i).GetTimer(j).GetSelectedDays(),sizeof(TimerPoint[i].m_SelectedDays));
        }
    }
    #endif

    //判断WEB用户是否正常
    if(pWebSec == NULL)
        return;
    LPCUserInfo pDestUser =  g_Global.m_Users.GetUserByID(pWebSec->GetSocketID());
    if(pDestUser == NULL)
        return;

    CTimerScheme &m_TimerScheme=g_Global.m_TimerScheme;//此处一定要用引用，否则极易引起系统堆栈奔溃，待分析
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command",    cJSON_CreateString("get_timer_info"));
    cJSON_AddItemToObject(root, "update_at", cJSON_CreateString(m_TimerScheme.GetDateTime().C_Str()));
    cJSON_AddItemToObject(root, "current_scheme_id", cJSON_CreateNumber(m_TimerScheme.GetCurScheme()+1));
   
    cJSON *ctime_schemes = cJSON_CreateArray();

    for(int i = 0; i < m_TimerScheme.GetSchemeCount() ; i++)
    {  
        cJSON *time_scheme = cJSON_CreateObject();
        cJSON_AddItemToObject(time_scheme, "time_scheme_id",   cJSON_CreateNumber( i+1) );  //定时方案id从1开始
        cJSON_AddItemToObject(time_scheme, "time_scheme_unique_id",   cJSON_CreateString(m_TimerScheme.GetScheme(i).GetUniqueID().C_Str()) );
        cJSON_AddItemToObject(time_scheme, "time_scheme_name",   cJSON_CreateString(m_TimerScheme.GetScheme(i).GetName().C_Str()) );

        cJSON *ctimerlists = cJSON_CreateArray();

        for(int j=0;j<m_TimerScheme.GetScheme(i).GetTimerCount();j++)
        {
            cJSON *timerlist = cJSON_CreateObject();
            cJSON_AddItemToObject(timerlist, "id", cJSON_CreateNumber(m_TimerScheme.GetScheme(i).GetTimer(j).GetID()-1) );  //定时点id从0开始
            cJSON_AddItemToObject(timerlist, "unique_id", cJSON_CreateString(m_TimerScheme.GetScheme(i).GetTimer(j).GetUniqueID().C_Str()));
            cJSON_AddItemToObject(timerlist, "name", cJSON_CreateString( m_TimerScheme.GetScheme(i).GetTimer(j).GetName().C_Str()));

            const char *timerAccount = m_TimerScheme.GetScheme(i).GetTimer(j).GetAccount();
            cJSON_AddItemToObject(timerlist, "account", cJSON_CreateString( g_Global.m_Users.IsExistUser(timerAccount)?timerAccount:SUPER_USER_NAME ));

            /**********************************************************************/
            //管理员、当前用户可以查看自身+下级所有用户创建的定时点
            vector<userParm>  vecSubUserInfo;
            g_Global.m_Users.GetAllSubUserByAccount(timerAccount, vecSubUserInfo);
            vector<string> vecSubAccount;
            for(int k=0;k<vecSubUserInfo.size();k++)
            {
                vecSubAccount.push_back(vecSubUserInfo[k].strAccount);
            }

            if( !(strcmp(timerAccount,pDestUser->GetAccount()) == 0 || pDestUser->IsSuperUser() || pDestUser->HasLimits(USER_LIMITS_TIMER)  ||
                std::find(vecSubAccount.begin(),vecSubAccount.end(),timerAccount) != vecSubAccount.end()) )
            {
                continue;
            }
            /**********************************************************************/

            if(m_TimerScheme.GetScheme(i).GetTimer(j).IsValid() == 1)
                 cJSON_AddItemToObject(timerlist, "vaild",cJSON_CreateString("true") );
            else
                 cJSON_AddItemToObject(timerlist, "vaild", cJSON_CreateString ("false") );

            if(m_TimerScheme.GetScheme(i).GetTimer(j).GetFollowDevice() == 1)
                 cJSON_AddItemToObject(timerlist, "vol_follow_device",cJSON_CreateString("true") );
            else
                 cJSON_AddItemToObject(timerlist, "vol_follow_device", cJSON_CreateString ("false") );

            cJSON_AddItemToObject(timerlist, "volume", cJSON_CreateNumber( m_TimerScheme.GetScheme(i).GetTimer(j).GetVolume()) );
            cJSON_AddItemToObject(timerlist, "time_mode", cJSON_CreateNumber( m_TimerScheme.GetScheme(i).GetTimer(j).GetTimeType()) );
           
            string weekday;
            char weekday_char[7]={'1','2','3','4','5','6','7'};
            bool *day_selected=m_TimerScheme.GetScheme(i).GetTimer(j).GetSelectedDays();
            for(int k=0;k<7;k++)
            {
                if(day_selected[k])
                {
                    weekday.append(1,weekday_char[k]);
                }
            }
            cJSON_AddItemToObject(timerlist, "weekday", cJSON_CreateString( weekday.c_str() ) );

            cJSON_AddItemToObject(timerlist, "play_mode", cJSON_CreateNumber (m_TimerScheme.GetScheme(i).GetTimer(j).GetPlayMode()) );
            if(m_TimerScheme.GetScheme(i).GetTimer(j).IsPlayToEnd()  == 1)
                cJSON_AddItemToObject(timerlist, "play_to_end", cJSON_CreateString("true") );
            else
                cJSON_AddItemToObject(timerlist, "play_to_end",cJSON_CreateString ("false") );

            if(m_TimerScheme.GetScheme(i).GetTimer(j).GetResumePlaying()  == 1)
                cJSON_AddItemToObject(timerlist, "resume_play", cJSON_CreateString("true") );
            else
                cJSON_AddItemToObject(timerlist, "resume_play",cJSON_CreateString ("false") );

            if(m_TimerScheme.GetScheme(i).GetTimer(j).GetIntercut()  == 1)
                cJSON_AddItemToObject(timerlist, "inter_cut", cJSON_CreateString("true") );
            else
                cJSON_AddItemToObject(timerlist, "inter_cut",cJSON_CreateString ("false") );

            if(m_TimerScheme.GetScheme(i).GetTimer(j).GetSinglePlay()  == 1)
                cJSON_AddItemToObject(timerlist, "random_single", cJSON_CreateString("true") );
            else
                cJSON_AddItemToObject(timerlist, "random_single",cJSON_CreateString ("false") );


            TIMER_DATE start_date,end_date;
            TIMER_TIME start_time,end_time;
            start_date = m_TimerScheme.GetScheme(i).GetTimer(j).GetDateStart();
            end_date = m_TimerScheme.GetScheme(i).GetTimer(j).GetDateEnd();
            start_time = m_TimerScheme.GetScheme(i).GetTimer(j).GetTimeStart();
            end_time = m_TimerScheme.GetScheme(i).GetTimer(j).GetTimeEnd();
            
            char c_startDate[25] = {0};
            char c_endDate[25] = {0};
            sprintf(c_startDate,"%04d-%02d-%02d",start_date.nYear, start_date.nMon, start_date.nDay);
            sprintf(c_endDate,"%04d-%02d-%02d",end_date.nYear, end_date.nMon, end_date.nDay);

            char c_startTime[20] = {0};
            char c_endTime[20] = {0};
            sprintf(c_startTime,"%02d:%02d:%02d",start_time.nHour, start_time.nMin, start_time.nSec);
            sprintf(c_endTime,"%02d:%02d:%02d",end_time.nHour, end_time.nMin, end_time.nSec);

            cJSON_AddItemToObject(timerlist, "start_date", cJSON_CreateString( c_startDate ) );
            cJSON_AddItemToObject(timerlist, "end_date", cJSON_CreateString( c_endDate ) );
            cJSON_AddItemToObject(timerlist, "start_time", cJSON_CreateString( c_startTime ) );
            cJSON_AddItemToObject(timerlist, "end_time", cJSON_CreateString( c_endTime ) );

            //20220719 增加音源类型
            int source_type=m_TimerScheme.GetScheme(i).GetTimer(j).GetSourceType();
            cJSON_AddItemToObject(timerlist, "source_type", cJSON_CreateNumber(source_type) );

            int device_type=m_TimerScheme.GetScheme(i).GetTimer(j).GetDeviceType();
            cJSON_AddItemToObject(timerlist, "device_type", cJSON_CreateNumber(device_type) );

            cJSON *sections = cJSON_CreateArray();
            for(int k=0;k<m_TimerScheme.GetScheme(i).GetTimer(j).GetZoneCount();k++)
            {
                cJSON_AddItemToArray(sections, cJSON_CreateString(m_TimerScheme.GetScheme(i).GetTimer(j).GetZoneMac(k).C_Str()));
            }
             cJSON_AddItemToObject(timerlist, "sections", sections);

            cJSON *groups = cJSON_CreateArray();
            for(int k=0;k<m_TimerScheme.GetScheme(i).GetTimer(j).GetGroupCount();k++)
            {
                cJSON_AddItemToArray(groups, cJSON_CreateString(m_TimerScheme.GetScheme(i).GetTimer(j).GetGroupID(k).C_Str()));
            }
           cJSON_AddItemToObject(timerlist, "groups", groups);

            //20220719 区分音频类型是本地歌曲播放还是音频采集器
            if(source_type == TIMER_SOURCE_TYPE_LOCAL_SONG)
            {
                cJSON *songs = cJSON_CreateArray();
                for(int k=0;k<m_TimerScheme.GetScheme(i).GetTimer(j).GetSongCount();k++)
                {
                    cJSON_AddItemToArray(songs, cJSON_CreateString(m_TimerScheme.GetScheme(i).GetTimer(j).GetSong(k).GetPathName().C_Str()));
                }
                cJSON_AddItemToObject(timerlist, "songs", songs);

                //加入空的audio_collector
                cJSON *audio_collector = cJSON_CreateObject();
                cJSON_AddItemToObject(timerlist, "audio_collector", audio_collector);
            }
            else if(source_type == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
            {
                //加入空的songs
                cJSON *songs = cJSON_CreateArray();
                cJSON_AddItemToObject(timerlist, "songs", songs);

                cJSON *audio_collector = cJSON_CreateObject();
                if(strlen(m_TimerScheme.GetScheme(i).GetTimer(j).GetAudioCollector().mac)>0)
                {
                    cJSON_AddItemToObject(audio_collector, "device_mac",cJSON_CreateString(m_TimerScheme.GetScheme(i).GetTimer(j).GetAudioCollector().mac));
                    cJSON_AddItemToObject(audio_collector, "channel",cJSON_CreateNumber(m_TimerScheme.GetScheme(i).GetTimer(j).GetAudioCollector().channelId));
                }
                cJSON_AddItemToObject(timerlist, "audio_collector", audio_collector);
            }

            cJSON *sequence_powers = cJSON_CreateArray();
            for(int k=0;k<m_TimerScheme.GetScheme(i).GetTimer(j).GetSeqPwrCount();k++)
            {
                cJSON *sequence_power = cJSON_CreateObject();
                cJSON_AddItemToObject(sequence_power, "device_mac",cJSON_CreateString(m_TimerScheme.GetScheme(i).GetTimer(j).GetSeqPwrMac(k).C_Str()));
                cJSON *sequence_channels = cJSON_CreateArray();
                int channelVal = m_TimerScheme.GetScheme(i).GetTimer(j).GetSeqPwrChannels(k);
                vector<int> vecChannel;
                for(int t=0;t<16;t++)
                {
                    int bitVal=pow(2,t);
                    if(channelVal & bitVal)
                    {
                        vecChannel.push_back(t+1);
                    }
                }
                for(int t=0;t<vecChannel.size();t++)
                {
                    cJSON_AddItemToArray(sequence_channels, cJSON_CreateNumber(vecChannel[t]));
                }
                cJSON_AddItemToObject(sequence_power, "channels",sequence_channels);
                
                cJSON_AddItemToArray(sequence_powers, sequence_power);
            }
            cJSON_AddItemToObject(timerlist, "sequence_powers", sequence_powers);


            cJSON_AddItemToArray(ctimerlists, timerlist);
        }
 
        cJSON_AddItemToObject(time_scheme, "timerlists",ctimerlists);
        cJSON_AddItemToObject(ctime_schemes, "timerlists",time_scheme);
    }

    cJSON_AddItemToObject(root, "time_schemes", ctime_schemes);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(0));

    char*  szBuf = cJSON_Print(root);

    string strData(szBuf);

    free(szBuf);
    cJSON_Delete(root);

    ForwardDataToWeb(pWebSec, strData, false, false, 1);


}




void CWebCommandSend::WebSendFireCollectorInfo(CWebSection *pWebSec)
{
    CSections &m_FireCollector=g_Global.m_FireCollectors;//此处一定要用引用，否则极易引起系统堆栈奔溃，待分析
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command",    cJSON_CreateString("get_fire_collector_info"));
    cJSON_AddItemToObject(root, "update_at", cJSON_CreateString(m_FireCollector.GetDateTime().C_Str()));
   
    cJSON *fire_collectors = cJSON_CreateArray();

    for(int i = 0; i < m_FireCollector.GetSecCount() ; i++)
    {  
        cJSON *fire_collector = cJSON_CreateObject();
        cJSON_AddItemToObject(fire_collector, "device_mac",cJSON_CreateString(m_FireCollector.GetSection(i).GetMac()));  //mac

        cJSON *channels = cJSON_CreateArray();
        for(int j=0;j<m_FireCollector.GetSection(i).m_pFireCollector->GetChannelCount();j++)
        {
            cJSON *channel = cJSON_CreateObject();
            cJSON_AddItemToObject(channel, "channel_id", cJSON_CreateNumber( m_FireCollector.GetSection(i).m_pFireCollector->GetChannel(j).GetID() ));  
            cJSON_AddItemToObject(channel, "name", cJSON_CreateString( m_FireCollector.GetSection(i).m_pFireCollector->GetChannel(j).GetName() ));  
            cJSON_AddItemToObject(channel, "trigger", cJSON_CreateNumber( m_FireCollector.GetSection(i).m_pFireCollector->GetChannel(j).GetTriggerMode() ));  
            cJSON_AddItemToObject(channel, "status", cJSON_CreateNumber( m_FireCollector.GetSection(i).m_pFireCollector->GetChannel(j).GetTriggerState() ));  
            cJSON_AddItemToObject(channel, "sound", cJSON_CreateString( m_FireCollector.GetSection(i).m_pFireCollector->GetChannel(j).GetSoundPathName().C_Str()));

            cJSON *sections = cJSON_CreateArray();
            for(int k=0;k<m_FireCollector.GetSection(i).m_pFireCollector->GetChannel(j).GetSectionCount();k++)
            {
                cJSON_AddItemToArray(sections, cJSON_CreateString(m_FireCollector.GetSection(i).m_pFireCollector->GetChannel(j).GetSectionMac(k).C_Str()));
            }
            cJSON_AddItemToObject(channel, "zone_macs",sections);
            cJSON_AddItemToArray(channels, channel);
        }
        cJSON_AddItemToObject(fire_collector, "channels", channels);
        cJSON_AddItemToArray(fire_collectors, fire_collector);
    }

    cJSON_AddItemToObject(root, "fire_collectors", fire_collectors);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(0));

    char*  szBuf = cJSON_Print(root);

    string strData(szBuf);

    free(szBuf);
    cJSON_Delete(root);

    ForwardDataToWeb(pWebSec, strData, false, true, 1);


}


void CWebCommandSend::WebSendAudioCollectorInfo(CWebSection *pWebSec)
{
    CSections &m_AudioCollector=g_Global.m_AudioCollectors;//此处一定要用引用，否则极易引起系统堆栈奔溃，待分析
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command",    cJSON_CreateString("get_audio_collector_info"));
    cJSON_AddItemToObject(root, "update_at", cJSON_CreateString(m_AudioCollector.GetDateTime().C_Str()));
   
    cJSON *audio_collectors = cJSON_CreateArray();

    for(int i = 0; i < m_AudioCollector.GetSecCount() ; i++)
    {  
        cJSON *audio_collector = cJSON_CreateObject();
        cJSON_AddItemToObject(audio_collector, "device_mac",cJSON_CreateString(m_AudioCollector.GetSection(i).GetMac()));  //mac

        cJSON *channels = cJSON_CreateArray();
        for(int j=0;j<AUDIO_COLLECTOR_CHANNELS;j++)
        {
            int channel_id=j+1;
            cJSON *channel = cJSON_CreateObject();
            cJSON_AddItemToObject(channel, "channel_id", cJSON_CreateNumber(channel_id));
            cJSON_AddItemToObject(channel, "channel_name", cJSON_CreateString( m_AudioCollector.GetSection(i).m_pAudioCollector->GetChannelNameById(channel_id).data() ));
            
            cJSON_AddItemToArray(channels, channel);
        }
        cJSON_AddItemToObject(audio_collector, "channels", channels);


        cJSON_AddItemToObject(audio_collector, "trigger_switch", cJSON_CreateNumber(m_AudioCollector.GetSection(i).m_pAudioCollector->GetTriggerSwitch()));
        cJSON_AddItemToObject(audio_collector, "trigger_channel_id", cJSON_CreateNumber(m_AudioCollector.GetSection(i).m_pAudioCollector->GetTriggerChannelId()));
        cJSON_AddItemToObject(audio_collector, "trigger_zone_volume", cJSON_CreateNumber(m_AudioCollector.GetSection(i).m_pAudioCollector->GetTriggerVolume()));
        cJSON_AddItemToObject(audio_collector, "priority", cJSON_CreateNumber(m_AudioCollector.GetSection(i).m_pAudioCollector->GetPriority()));

        cJSON* jsZoneMacArray =  cJSON_CreateArray();
        for(int j=0; j<m_AudioCollector.GetSection(i).m_pAudioCollector->GetSectionCount(); j++)
        {
            cJSON_AddItemToArray(jsZoneMacArray, cJSON_CreateString(m_AudioCollector.GetSection(i).m_pAudioCollector->GetSectionMac(j).Data()));
        }
        cJSON_AddItemToObject(audio_collector, "trigger_zone_macs", jsZoneMacArray);

        cJSON_AddItemToArray(audio_collectors, audio_collector);
    }

    cJSON_AddItemToObject(root, "audio_collectors", audio_collectors);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(0));

    char*  szBuf = cJSON_Print(root);

    string strData(szBuf);

    free(szBuf);
    cJSON_Delete(root);

    ForwardDataToWeb(pWebSec, strData, false, true, 1);
}


void CWebCommandSend::WebSendTodayTimerInfo(CWebSection *pWebSec,vector<StTodayTimerP> TimerVec)
{
    //判断WEB用户是否正常
    if(pWebSec == NULL)
        return;
    LPCUserInfo pDestUser =  g_Global.m_Users.GetUserByID(pWebSec->GetSocketID());
    if(pDestUser == NULL)
        return;
        
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command",    cJSON_CreateString("get_today_timer_info"));
    cJSON_AddItemToObject(root, "current_scheme_id", cJSON_CreateNumber(g_Global.m_TimerScheme.GetCurScheme()+1));
   
    cJSON *timerInfos = cJSON_CreateArray();

    int real_timer_index=0;
    int nTimerCount = TimerVec.size();
    for(int i = 0; i < nTimerCount ; i++)
    {  
        cJSON *timerInfo = cJSON_CreateObject();
        cJSON_AddItemToObject(timerInfo, "id",cJSON_CreateNumber(TimerVec[i].TimerPoint.GetID()-1));  //mac
        cJSON_AddItemToObject(timerInfo, "name",cJSON_CreateString(TimerVec[i].TimerPoint.GetName().C_Str()));  //mac
        cJSON_AddItemToObject(timerInfo, "status",cJSON_CreateNumber(TimerVec[i].status));  //mac

        string timerAccount=TimerVec[i].TimerPoint.GetAccount();
        //管理员、当前用户可以查看自身+下级所有用户创建的定时点
        vector<userParm>  vecSubUserInfo;
        g_Global.m_Users.GetAllSubUserByAccount(timerAccount, vecSubUserInfo);
        vector<string> vecSubAccount;
        for(int k=0;k<vecSubUserInfo.size();k++)
        {
            vecSubAccount.push_back(vecSubUserInfo[k].strAccount);
        }

        if( !(timerAccount == pDestUser->GetAccount() || pDestUser->IsSuperUser() || pDestUser->HasLimits(USER_LIMITS_TIMER) ||
            std::find(vecSubAccount.begin(),vecSubAccount.end(),timerAccount) != vecSubAccount.end()) )
        {
            continue;
        }
        real_timer_index++;

        cJSON_AddItemToArray(timerInfos, timerInfo);
    }

    cJSON_AddItemToObject(root, "timerlists", timerInfos);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(0));

    char*  szBuf = cJSON_Print(root);

    string strData(szBuf);

    free(szBuf);
    cJSON_Delete(root);

    ForwardDataToWeb(pWebSec, strData, false, false, 1);
}


void CWebCommandSend::WebSendManualTaskInfo(CWebSection *pWebSec,vector<StManualTask> manualTaskVec)
{
    //判断WEB用户是否正常
    if(pWebSec == NULL)
        return;
    LPCUserInfo pDestUser =  g_Global.m_Users.GetUserByID(pWebSec->GetSocketID());
    if(pDestUser == NULL)
        return;

    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command",    cJSON_CreateString("get_manual_task_info"));
   
    cJSON *TaskInfos = cJSON_CreateArray();

    int real_task_index=0;
    int nTaskCount = manualTaskVec.size();
    for(int i = 0; i < nTaskCount ; i++)
    {  
        cJSON *taskInfo = cJSON_CreateObject();
        cJSON_AddItemToObject(taskInfo, "task_name",cJSON_CreateString(manualTaskVec[i].strTaskName.C_Str()));  //task_name
        cJSON_AddItemToObject(taskInfo, "account",cJSON_CreateString(manualTaskVec[i].strUserAccount.c_str()));  //account
        
        string taskAccount=manualTaskVec[i].strUserAccount;
        //管理员、当前用户可以查看自身+下级所有用户创建的定时点
        vector<userParm>  vecSubUserInfo;
        g_Global.m_Users.GetAllSubUserByAccount(taskAccount, vecSubUserInfo);
        vector<string> vecSubAccount;
        for(int k=0;k<vecSubUserInfo.size();k++)
        {
            vecSubAccount.push_back(vecSubUserInfo[k].strAccount);
        }

        if( !(taskAccount == pDestUser->GetAccount() || pDestUser->IsSuperUser() ||
            std::find(vecSubAccount.begin(),vecSubAccount.end(),taskAccount) != vecSubAccount.end()) )
        {
            continue;
        }
        
        cJSON_AddItemToObject(taskInfo, "play_id",cJSON_CreateNumber(manualTaskVec[i].nPlayID));  //play_id

        cJSON *sections = cJSON_CreateArray();
        for(int k=0;k<manualTaskVec[i].vSections.size();k++)
        {
            cJSON_AddItemToArray(sections, cJSON_CreateString(manualTaskVec[i].vSections[k].data()));
        }
        cJSON_AddItemToObject(taskInfo, "sections",sections);

        CMyString	strName	= GetNameByPathName(manualTaskVec[i].strCurPathName, TRUE);
        cJSON_AddItemToObject(taskInfo, "song_name",cJSON_CreateString(strName.C_Str()));  //song_name
        cJSON_AddItemToObject(taskInfo, "song_path_name",cJSON_CreateString(manualTaskVec[i].strCurPathName.C_Str()));  //song_path_name
        cJSON_AddItemToObject(taskInfo, "list_id",cJSON_CreateNumber(manualTaskVec[i].nPlayList));  //list_id
        cJSON_AddItemToObject(taskInfo, "play_mode",cJSON_CreateNumber(manualTaskVec[i].nPlayMode));  //play_mode
        cJSON_AddItemToObject(taskInfo, "play_status",cJSON_CreateNumber(manualTaskVec[i].nPlayStatus));  //play_status

        #if SUPPORT_MANUALTASK_PROGRESS
        cJSON_AddItemToObject(taskInfo, "cur_play_time",cJSON_CreateNumber(manualTaskVec[i].nCurPlayTime));  //play_mode
        cJSON_AddItemToObject(taskInfo, "song_duration",cJSON_CreateNumber(manualTaskVec[i].nSongDuration));  //play_status
        #endif

        real_task_index++;
        cJSON_AddItemToArray(TaskInfos, taskInfo);
    }

    cJSON_AddItemToObject(root, "tasks", TaskInfos);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(0));

    char*  szBuf = cJSON_Print(root);

    string strData(szBuf);

    free(szBuf);
    cJSON_Delete(root);

    ForwardDataToWeb(pWebSec, strData, false, false, 1);
}


#if SUPPORT_SONG_MANAGER
void CWebCommandSend::WebSendLocalSongInfo(CWebSection *pWebSec)
{
    //判断WEB用户是否正常
    if(pWebSec == NULL)
        return;
    LPCUserInfo pDestUser =  g_Global.m_Users.GetUserByID(pWebSec->GetSocketID());
    if(pDestUser == NULL)
        return;

    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command",    cJSON_CreateString("get_local_songlist_info"));
   
    cJSON *Songlists = cJSON_CreateArray();

    int nSongCount = g_Global.m_SongManager.GetSongsCount();
    cJSON_AddItemToObject(root, "update_at", cJSON_CreateString(g_Global.m_SongManager.GetDateTime().C_Str()));
    for(int i = 0; i < nSongCount ; i++)
    {
        cJSON *songInfo = cJSON_CreateObject();

        CSong &song = g_Global.m_SongManager.m_Songs[i];

        cJSON_AddItemToObject(songInfo, "song_path_name",cJSON_CreateString(song.GetPathName().C_Str()));  //song_name
        cJSON_AddItemToObject(songInfo, "duration",cJSON_CreateNumber(song.GetDuration()));  //song_name
        cJSON_AddItemToObject(songInfo, "bitrate",cJSON_CreateNumber(song.GetBitRate()));  //bitrate
        //******** 如果是龙之音云版本，那么此处size应该传压缩后的文件大小，而不是原大小
        #if APP_IS_LZY_LIMIT_STORAGE
            cJSON_AddItemToObject(songInfo, "size",cJSON_CreateNumber(song.GetLowRateSize()?song.GetLowRateSize():song.GetSize()));  //size
        #else
            cJSON_AddItemToObject(songInfo, "size",cJSON_CreateNumber(song.GetSize()));  //size
        #endif
        cJSON_AddItemToObject(songInfo, "account",cJSON_CreateString(song.GetUserAccount().C_Str()));  //account

        cJSON_AddItemToObject(songInfo, "audit",cJSON_CreateBool(song.GetAudit()));  //audit

        string songAccount=song.GetUserAccount().C_Str();
        //管理员、当前用户可以查看自身+下级所有用户上传的歌曲文件
        vector<userParm>  vecSubUserInfo;
        g_Global.m_Users.GetAllSubUserByAccount(songAccount, vecSubUserInfo);
        vector<string> vecSubAccount;
        for(int k=0;k<vecSubUserInfo.size();k++)
        {
            vecSubAccount.push_back(vecSubUserInfo[k].strAccount);
        }

        //******** 还是恢复所有子账户能够查看管理员上传的歌曲，学校里面还是有很多共同的歌曲文件需要管理员处理，比如铃声
        #if APP_IS_LZY_LIMIT_STORAGE
        if( !(songAccount == pDestUser->GetAccount() || pDestUser->IsSuperUser() ) )
        {
            continue;
        }
        #else
            #if SUPPORT_ALL_ACCOUNT_VIEW_ADMIN_DIR_AND_SONG
            if( !( songAccount == SUPER_USER_NAME ||
                songAccount == pDestUser->GetAccount() || pDestUser->IsSuperUser() || pDestUser->HasLimits(USER_LIMITS_PLAYLIST) ||
                std::find(vecSubAccount.begin(),vecSubAccount.end(),songAccount) != vecSubAccount.end()) )
            {
                continue;
            }
            #else
            if( !(songAccount == pDestUser->GetAccount() || pDestUser->IsSuperUser() || pDestUser->HasLimits(USER_LIMITS_PLAYLIST) ||
                std::find(vecSubAccount.begin(),vecSubAccount.end(),songAccount) != vecSubAccount.end()) )
            {
                continue;
            }
            #endif
        #endif


        cJSON_AddItemToArray(Songlists, songInfo);
    }

    cJSON_AddItemToObject(root, "songlists", Songlists);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(0));

    char*  szBuf = cJSON_Print(root);

    string strData(szBuf);

    free(szBuf);
    cJSON_Delete(root);

    ForwardDataToWeb(pWebSec, strData, false, false, 1);
}
#endif


void CWebCommandSend::WebSendSequencePowerInfo(CWebSection *pWebSec)
{
        CSections &m_SequencePower=g_Global.m_SequencePower;//此处一定要用引用，否则极易引起系统堆栈奔溃，待分析
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command",    cJSON_CreateString("get_sequence_power_info"));
    cJSON_AddItemToObject(root, "update_at", cJSON_CreateString(m_SequencePower.GetDateTime().C_Str()));
   
    cJSON *SequencePowers = cJSON_CreateArray();

    for(int i = 0; i < m_SequencePower.GetSecCount() ; i++)
    {  
        cJSON *SequencePower = cJSON_CreateObject();
        cJSON_AddItemToObject(SequencePower, "device_mac",cJSON_CreateString(m_SequencePower.GetSection(i).GetMac()));  //mac
        cJSON_AddItemToObject(SequencePower, "mode",cJSON_CreateNumber(m_SequencePower.GetSection(i).m_pSequencePower->GetControlMode()));  //mac

        cJSON *channels = cJSON_CreateArray();
        for(int j=0;j<m_SequencePower.GetSection(i).m_pSequencePower->GetRealChannelCnt();j++)
        {
            cJSON *channel = cJSON_CreateObject();
            cJSON_AddItemToObject(channel, "channel_id", cJSON_CreateNumber( m_SequencePower.GetSection(i).m_pSequencePower->GetChannel(j).GetID() ));  
            cJSON_AddItemToObject(channel, "name", cJSON_CreateString( m_SequencePower.GetSection(i).m_pSequencePower->GetChannel(j).GetName() ));  
            cJSON_AddItemToObject(channel, "status", cJSON_CreateNumber( m_SequencePower.GetSection(i).m_pSequencePower->GetChannel(j).GetSwitchState() ));  

            cJSON_AddItemToArray(channels, channel);
        }
        cJSON_AddItemToObject(SequencePower, "channels", channels);
        cJSON_AddItemToArray(SequencePowers, SequencePower);
    }

    cJSON_AddItemToObject(root, "sequence_powers", SequencePowers);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(0));

    char*  szBuf = cJSON_Print(root);

    string strData(szBuf);

    free(szBuf);
    cJSON_Delete(root);

    ForwardDataToWeb(pWebSec, strData, false, true, 1);
}

#if SUPPORT_REMOTE_CONTROLER
void CWebCommandSend::WebSendRemoteControlerInfo(CWebSection *pWebSec)
{
    CSections &m_RemoteControlers=g_Global.m_RemoteControlers;//此处一定要用引用，否则极易引起系统堆栈奔溃，待分析
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command",    cJSON_CreateString("get_remote_controler_info"));
    cJSON_AddItemToObject(root, "update_at", cJSON_CreateString(m_RemoteControlers.GetDateTime().C_Str()));
   
    cJSON *cjRemoteControlers = cJSON_CreateArray();

    for(int i = 0; i < m_RemoteControlers.GetSecCount() ; i++)
    {  
        CSection &m_SecRemoteControler = m_RemoteControlers.GetSection(i);

        std::shared_ptr<CRemoteControl> remoteControl = m_SecRemoteControler.m_pRemoteControler;
        
        cJSON *cjRemoteControler = cJSON_CreateObject();
        cJSON_AddItemToObject(cjRemoteControler,"device_mac",cJSON_CreateString(m_SecRemoteControler.GetMac()));

        cJSON *cjTasks = cJSON_CreateArray();

        int taskCnt = remoteControl->GetTaskCnt();
        for(int j=0;j<taskCnt;j++)
        {
            CRemoteControlTask &task = remoteControl->GetTaskDetail(j+1);
            cJSON *cjTask = cJSON_CreateObject();
            cJSON_AddItemToObject(cjTask,"task_id",cJSON_CreateNumber(task.GetTaskID()));
            cJSON_AddItemToObject(cjTask,"name",cJSON_CreateString(task.GetTaskName().Data()));
            cJSON_AddItemToObject(cjTask,"source_type",cJSON_CreateNumber(task.GetSourceType()));
            cJSON_AddItemToObject(cjTask,"play_mode",cJSON_CreateNumber(task.GetPlayMode()));
            cJSON_AddItemToObject(cjTask,"volume",cJSON_CreateNumber(task.GetVolume()));
            
            cJSON *cjTaskZoneMacs = cJSON_CreateArray();
            for(int k=0;k<task.GetZoneCount();k++)
            {
                cJSON_AddItemToArray(cjTaskZoneMacs, cJSON_CreateString(task.GetZoneMac(k).Data()));
            }
            cJSON_AddItemToObject(cjTask,"zone_macs",cjTaskZoneMacs);

            cJSON *cjTaskGroupIds = cJSON_CreateArray();
            for(int k=0;k<task.GetGroupCount();k++)
            {
                cJSON_AddItemToArray(cjTaskGroupIds, cJSON_CreateString(task.GetGroupID(k).Data()));
            }
            cJSON_AddItemToObject(cjTask,"group_ids",cjTaskGroupIds);

            if(task.GetSourceType() == TIMER_SOURCE_TYPE_LOCAL_SONG)
            {
                cJSON *cjTaskSongs = cJSON_CreateArray();
                for(int k=0;k<task.GetSongCount();k++)
                {
                    cJSON_AddItemToArray(cjTaskSongs, cJSON_CreateString(task.GetSong(k).GetPathName().Data()));
                }
                cJSON_AddItemToObject(cjTask,"song_pathnames",cjTaskSongs);

                //加入空的audio_collector
                cJSON *audio_collector = cJSON_CreateObject();
                cJSON_AddItemToObject(cjTask, "audio_collector", audio_collector);
            }
            else if(task.GetSourceType() == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
            {
                //加入空的song_pathnames
                cJSON *cjTaskSongs = cJSON_CreateArray();
                cJSON_AddItemToObject(cjTask, "song_pathnames", cjTaskSongs);

                cJSON *audio_collector = cJSON_CreateObject();
                if(strlen(task.GetAudioCollector().mac)>0)
                {
                    cJSON_AddItemToObject(audio_collector, "device_mac",cJSON_CreateString(task.GetAudioCollector().mac));
                    cJSON_AddItemToObject(audio_collector, "channel",cJSON_CreateNumber(task.GetAudioCollector().channelId));
                }
                cJSON_AddItemToObject(cjTask, "audio_collector", audio_collector);
            }

            cJSON_AddItemToArray(cjTasks,cjTask);
        }
        cJSON_AddItemToObject(cjRemoteControler,"tasks",cjTasks);

        cJSON *cjKeys = cJSON_CreateArray();
        int keyCnt = REMOTE_CONTROL_MAX_KEY_NUM;
        for(int j=0;j<keyCnt;j++)
        {
            cJSON *cjKey = cJSON_CreateObject();
            cJSON_AddItemToObject(cjKey,"key_id",cJSON_CreateNumber(j+1));
            cJSON_AddItemToObject(cjKey,"event",cJSON_CreateNumber(remoteControl->GetKeyEvent(j+1)));

            cJSON_AddItemToArray(cjKeys,cjKey);
        }
        cJSON_AddItemToObject(cjRemoteControler,"keys",cjKeys);


        cJSON_AddItemToArray(cjRemoteControlers,cjRemoteControler);
    }

    cJSON_AddItemToObject(root, "remote_controlers", cjRemoteControlers);

    char*  szBuf = cJSON_Print(root);

    string strData(szBuf);

    free(szBuf);
    cJSON_Delete(root);

    ForwardDataToWeb(pWebSec, strData, false, true, 1);
}
#endif

// 发送用户分区信息
void CWebCommandSend::WebSendUserZoneInfo(CUserInfo&     pSubUser,          // 用户
                                          CWebSection   *pWebSection,       // 终端
                                          int  nPage)                       // 页数，为-1时发送全部数据
{
    // 组合包数量与分区Mac信息
    int nSecCount =  pSubUser.GetSectionCount();
    // 组包
    int nPageCount = nSecCount/20;
    if(nSecCount%20 >= 0)             nPageCount++;

#if 0
    for(int k=1; k<=nPageCount; k++)
    {
        int nStart = (k-1) * MAX_PAGE_COUNT;
        int nEnd   = (nPageCount == k ? nSecCount : k*MAX_PAGE_COUNT);
        int nCount = nEnd - nStart;

        if(k == nPage || nPage == -1)     // 如果等于指定请求页面数据或请求全部数据
        {
            string strMacArray[nCount];
            for(int i=0; i<nCount; i++)
            {
                strMacArray[i] = pSubUser.GetSectionMac(i + nStart);
            }

            string strBuf = CWebProtocol::CmdResponseGetUserZone(pSubUser.GetAccount(), nPageCount, k, nCount, strMacArray);
            ForwardDataToWeb(pWebSection, strBuf, false, false, true);
        }
    }
#else

    int nCount =  pSubUser.GetSectionCount();
    string strMacArray[nCount];
    for(int i=0; i<nCount; i++)
    {
        strMacArray[i] = pSubUser.GetSectionMac(i);
    }
    string strBuf = CWebProtocol::CmdResponseGetUserZone(pSubUser.GetAccount(), 1, 1, nCount, strMacArray);
    ForwardDataToWeb(pWebSection, strBuf, false, false, true);
#endif
}


#if 0
// 发送用户分组信息
void CWebCommandSend::WebSendUserGroupInfo(CUserInfo& pSubUser,               // 用户
                                                                                          CWebSection*  pWebSection,    // 终端
                                                                                          int  nPage)                                    // 页数，为-1时发送全部数据
{
    bool    IsSuper = pSubUser.IsSuperUser();        // 是否超级用户, 超级用户可以设置所有分区分组
    // 组合包数量与分组ID信息
    int nGroupCount = 0;
    if(IsSuper)
    {
        nGroupCount = g_Global.m_Groups.GetGroupCount();
    }
    else
    {
        nGroupCount = pSubUser.GetGroupCount();
    }

    // 组包
    int nPageCount = nGroupCount/MAX_PAGE_COUNT;
    if(nGroupCount%MAX_PAGE_COUNT >= 0)             nPageCount++;

//    int nStart = (nPage-1) * MAX_PAGE_COUNT;
//    int nEnd   = (nPageCount == nPage ? nGroupCount : nPage*MAX_PAGE_COUNT);
//    int nCount = nEnd - nStart;

    for(int k=1; k<=nPageCount; k++)
    {

        int nStart = (k-1) * MAX_PAGE_COUNT;
        int nEnd   = (nPageCount == k ? nGroupCount : k*MAX_PAGE_COUNT);
        int nCount = nEnd - nStart;

        if(k == nPage || nPage == -1)     // 如果等于指定请求页面数据或请求全部数据
        {
            string strIDArray[nCount];
            for(int i=0; i<nCount; i++)
            {
                if(IsSuper)
                {
                    strIDArray[i] = g_Global.m_Groups.GetGroup(i + nStart).GetID().C_Str();
                }
                else
                {
                    strIDArray[i] = pSubUser.GetGroupID(i + nStart);
                }
            }

            string strBuf = CWebProtocol::CmdResponseGetUserGroup(pSubUser.GetAccount(), nPageCount, k, nCount, strIDArray);
            ForwardDataToWeb(pWebSection, strBuf);
        }
    }
}
#endif


void CWebCommandSend::WebSendAccountStorageCapacity(CWebSection *pWebSec,string strAccount)     //发送对应账户的存储信息给WEB
{
    int nResult = EC_SUCCESS;

    if(pWebSec == NULL)
    {
        return;
    }

    LPCUserInfo pOperaUser =  g_Global.m_Users.GetUserByID(pWebSec->GetSocketID());
    LPCUserInfo pDestUser =  g_Global.m_Users.GetUserByAccount(strAccount);
    if(pOperaUser == NULL || pDestUser == NULL)
    {
        return;
    }
    if( !(pOperaUser->IsSuperUser() || pOperaUser == pDestUser) )
    {
        nResult=EC_NO_LIMIT;
    }

    UINT64 storage_capacity=0;
    UINT64 storage_used=0;
    UINT64 storage_remaining=0;

    double nstorageCapacity=0;
    double nUsedSpace=0;          
    double nRemainingSpace=0;

    if(nResult == EC_SUCCESS)
    {
        //龙之音V1版本才限制存储空间,其他版本传0
        #if APP_IS_LZY_LIMIT_STORAGE
        storage_capacity = ((UINT64)pDestUser->GetStorageCapacity())*1024*1024;
        //计算该账户的已用存储空间（字节）
        storage_used = pDestUser->GetStorageUsedSpace();
        //计算该账户的剩余存储空间（字节）
        storage_remaining = pDestUser->GetStorageRemainingSpace();
        
        nstorageCapacity=storage_capacity/1024/1024;    //保持整数
        nUsedSpace=storage_used/1024.0/1024.0;
        nRemainingSpace=storage_remaining/1024.0/1024.0;
        #endif
    }

    // 回复WEB
    string strBuf = CWebProtocol::CmdResponseGetUserStorageCapacity(strAccount, nstorageCapacity, nUsedSpace, nRemainingSpace, nResult);
    g_Global.m_WebNetwork.m_WebSend.CommandSend(strBuf.data(), *pWebSec, false);
}

// 主机下发监控设备的信息
void CWebCommandSend::WebForwardMonitorInfo(CWebSection *pWebSec,                // 设备
                                            int          nMonitorCount,          // 设备数量
                                            LPCMonitorInfo* pMonitorInfos)         // 监控设备信息数组
{
    string strBuf = CWebProtocol::CmdForwardMonitorInfo(nMonitorCount, pMonitorInfos);
    ForwardDataToWeb(pWebSec, strBuf);
}


// 主机下发监控设备的上报事件
void CWebCommandSend::WebForwardMonitorEvent(CWebSection *pWebSec,     // 设备
                                             const char  *szMac,       // 通道号（代表摄像头的编号）
                                             u_char       uEvent,      // 事件类型
                                             const char*  szTime,      // 时间
                                             u_char       direction)   // 入侵方向
{
    string strBuf = CWebProtocol::CmdForwardMonitorEvent(szMac,
                                                         uEvent,
                                                         szTime,
                                                         direction);

}

/*
void CWebCommandSend::WebForwardSipInfo(int nPageCount, int nPage, CWebSection *pWebSec, int nSipCount, SipInfo* pSipInfos)
{
    string  strBuf ;
    strBuf = CWebProtocol::CmdResponseSipInfo(nPageCount, nPage, nSipCount, pSipInfos);
    CommandSend(strBuf.data(), *pWebSec);
}
*/

// 添加用户
void CWebCommandSend::WebResponseAddUser(string strComID,     // 命令ID
                                         string strAccount,   // 用户名
                                         CWebSection *pWebSec,
                                         int nResult)   //  返回结果
{
    if(pWebSec == NULL)
        return;

    string strBuf = CWebProtocol::CmdResponseAddUser(strComID, strAccount, nResult);
    CommandSend(strBuf.data(), *pWebSec);
}

void CWebCommandSend::WebResponseModifyUser(string strComID,     // 命令ID
                                            string strAccount,   // 用户名
                                            CWebSection *pWebSec,
                                            int nResult)     //  返回结果
{
        if(pWebSec == NULL)
            return;

        string strBuf = CWebProtocol::CmdResponseModifyUser(strComID, strAccount, nResult);
        CommandSend(strBuf.data(), *pWebSec);
}


void CWebCommandSend::WebResponseAddGroup(string strComID,       // 命令标识
                                          string strGroupName,   // 分组名称
                                          int      nZoneCount,   // 分区数量
                                          CWebSection *pWebSec,
                                          int nResult)           // 返回结果
{
    if(pWebSec == NULL)
        return;

    string strBuf = CWebProtocol::CmdResponseAddGroup(strComID, strGroupName, nZoneCount, nResult);
    CommandSend(strBuf.data(), *pWebSec);
}

// 编辑分组 回复
void CWebCommandSend::WebResponseModifyGroup(string strComID,       // 命令标识
                                             string strGroupID,     // 组ID
                                             string strGroupName,   // 分组名称
                                             int    nZoneCount,     // 分区数量
                                             CWebSection *pWebSec,
                                             int nResult)    // 返回结果
{
    if(pWebSec == NULL)
        return;

    string strBuf = CWebProtocol::CmdResponseModifyGroup(strComID, strGroupID,strGroupName, nZoneCount, nResult);
    CommandSend(strBuf.data(), *pWebSec);
}

// 添加定时点信息
void CWebCommandSend::WebResponseAddTimePoint(int nTimeSchemeID,      // 方案序号
                                              string strComID,        // 命令ID
                                              CExTimePoint &tp,     // 定时点信息
                                              CWebSection *pWebSec,
                                              int  nResult)                // 返回结果
{
    if(pWebSec == NULL)
        return;

    string strBuf = CWebProtocol::CmdResponseAddTimePonit(nTimeSchemeID, strComID, tp, nResult);
    CommandSend(strBuf.data(), *pWebSec);
}

// 编辑定时点信息
void CWebCommandSend::WebResponseModifyTimePoint(int nTimeSchemeID,      // 方案序号
                                                 string strComID,        // 命令ID
                                                 CExTimePoint &tp,       // 定时点信息
                                                 CWebSection *pWebSec,
                                                 int  nResult)           // 返回结果
{
    if(pWebSec == NULL)
        return;

    string strBuf = CWebProtocol::CmdResponseModifyTimePoint(nTimeSchemeID, strComID, tp, nResult);
    CommandSend(strBuf.data(), *pWebSec);
}

// 客户端向服务器发起对讲请求 回复
void CWebCommandSend::WebResponseTalkback(CWebSection *pWebSec, int nResult)
{
    string strBuf = CWebProtocol::CmdResponseTalkback(nResult);
    CommandSend(strBuf.data(), *pWebSec);
}

// 客户端向服务器发起广播(寻呼)请求
void CWebCommandSend::WebResponsePaging(int nType,        // 寻呼类型 1 : 组播  2: 单播
                                        int nRtpType,   // Rtp类型
                                        int nPage,        // 指定页数
                                        CWebSection *pWebSec,   // Web终端
                                        int nResult)     // 返回结果
{
    string  strBuf = CWebProtocol::CmdResponsePaging(nType, nRtpType, nPage, nResult);
    CommandSend(strBuf.data(), *pWebSec, true, true, true);
}

// 客户端向服务器发起监听请求
void CWebCommandSend::WebResponseListen(CWebSection *pWebSec,   // Web终端
                                        int nResult)    // 返回结果
{
    string strBuf = CWebProtocol::CmdResponseListen(nResult);
    CommandSend(strBuf.data(), *pWebSec);
}

// 客户端向服务器发送挂断请求
void CWebCommandSend::WebResponseHangup(string strExten,
                                        CWebSection *pWebSec,   // Web终端
                                        int nResult)           // 返回结果
{
    string strBuf = CWebProtocol::CmdResponseHangup(strExten, nResult);
    CommandSend(strBuf.data(), *pWebSec);
}

// 重置设备数据 回复
void CWebCommandSend::WebResponseResetDeviceData(string strMac, CWebSection *pWebSec)
{
    string strBuf = CWebProtocol::CmdResponseResetDeviceData(strMac);
    ForwardDataToWeb(pWebSec, strBuf);
}

// 处理返回EMC状态
void CWebCommandSend::WebResponseEmcStatus(string strMac, bool bStatus, CWebSection *pWebSec)
{
    string strBuf = CWebProtocol::CmdResponseEMCStatus(strMac, REST_GET, bStatus);
    ForwardDataToWeb(pWebSec, strBuf, TRUE);
}

// 处理返回EQ音效
void CWebCommandSend::WebResponseDeviceEQ(CSection &device, CWebSection *pWebSec)
{
    string strBuf = CWebProtocol::CmdResponseEQMode(device.GetMac(),
                                                    REST_GET,
                                                    device.m_DeviceEQ.m_eqMode,
                                                    device.m_DeviceEQ.m_eqGain);

    ForwardDataToWeb(pWebSec, strBuf, TRUE);
}

// 处理返回蓝牙信息
void CWebCommandSend::WebResponseDeviceBTinfo(CSection &device, CWebSection *pWebSec)
{
    string strBuf = CWebProtocol::CmdResponseBlueToothInfo(device.GetMac(),
                                                    REST_GET,
                                                    string(device.m_Bluetooth.m_BlueTooth_Name),
                                                    device.m_Bluetooth.m_BlueTooth_encryption,
                                                    string(device.m_Bluetooth.m_BlueTooth_Pin));

    ForwardDataToWeb(pWebSec, strBuf, TRUE);
}

// 处理返回查询混音模式
void CWebCommandSend::WebResponseDeviceMixing(CSection &device, CWebSection *pWebSec)
{
    string strBuf = CWebProtocol::CmdResponseMixMode(device.GetMac(),
                                                     REST_GET,
                                                     false,
                                                     device.m_DeviceMixing.m_nLineChn,
                                                     device.m_DeviceMixing.m_nMixing,
                                                     device.m_DeviceMixing.m_nAUX,
                                                     device.m_DeviceMixing.m_nDAC);

    ForwardDataToWeb(pWebSec, strBuf, TRUE);
}

#if 0
// 处理返回电源输出模式
void CWebCommandSend::WebResponsePowerOutputMode(CSection& device,		// 设备
                                                 CWebSection *pWebSec)  // Web终端
{
    string strBuf = CWebProtocol::CmdPowOutputMode(device.GetMac(),
                                                   REST_GET,
                                                   device.m_pPowerInfo->m_powerMode,
                                                   device.m_pPowerInfo->m_timeout);

    ForwardDataToWeb(pWebSec, strBuf, TRUE);
}

// 处理返回电源输出状态
void CWebCommandSend::WebResponseSplitterStatus(string strMac,           // 设备Mac
                                                int nChannelStatus,      // 通道状态
                                                CWebSection *pWebSec)    // Web终端
{
    string strBuf = CWebProtocol::CmdSplitterStatus(strMac, REST_GET, nChannelStatus);
    ForwardDataToWeb(pWebSec, strBuf, TRUE);
}

// 处理返回无线MIC的状态
void CWebCommandSend::WebResponseMicStatus(string strMac,                 // 设备Mac
                                            int volume,                 // 音量
                                            int power,                  // 功率
                                            int channel,                // 通道
                                            CWebSection *pWebSec)       // Web终端
{
    string strBuf = CWebProtocol::CmdMicStatus(strMac, REST_GET, volume, power, channel);

    //ForwardDataToWeb(pWebSec, strBuf, TRUE);

    // 调用ForwardDataToWeb不能发出去（暂时找不到原因），所以直接发送
    ForwardDataToWeb(pWebSec, strBuf);
}

#endif

void CWebCommandSend::WebResponseUpgradeDevice(CWebSection *pWebSec,  // Web终端
                                               string strMac,         // 设备mac地址
                                               int nUpgradeStatus,    // 更新状态
                                               int nPer,              // 升级进度百分比
                                               int nResult)           // 返回结果
{
    string strBuf = CWebProtocol::CmdResponseUpgradeDevice(strMac, nUpgradeStatus, nPer, nResult);

    ForwardDataToWeb(pWebSec, strBuf, TRUE);
}

// 处理查询GPS信息
void CWebCommandSend::WebResponseGPSTimeZone(string strMac, const char *pData, CWebSection *pWebSec)
{
    byte timeZone = pData[0];
    int nTimeZone = (timeZone > 24 ? -1 : timeZone);
    bool   isDst = (pData[1] == 0x00) ? false : true;

    string strBuf = CWebProtocol::CmdResponseGPSTimeZone(strMac, REST_GET, nTimeZone, isDst);
    ForwardDataToWeb(pWebSec, strBuf, TRUE);
}

// 设置消防采集器
void CWebCommandSend::WebResponseSetFireCollector(CWebSection *pWebSec,  // Web终端
                                                  string strComID,       // 命令ID
                                                  string strMac,         // 设备mac
                                                  string strName,        // 设备名称
                                                  int    nChannelCount,  // 消防通道数量
                                                  int  nResult)          // 返回结果
{
    if(pWebSec == NULL)
        return;

    string strBuf = CWebProtocol::CmdSetFireCollector(strComID, strMac, strName, nChannelCount, nResult);
    CommandSend(strBuf.data(), *pWebSec);
}

// 处理返回IP属性信息
void CWebCommandSend::WebResponseDeviceIP(string strMac, const char *pData, CWebSection *pWebSec)
{
    CSection *pSection = g_Global.m_Sections.GetSectionByMac(strMac.data());

    if(pSection == NULL)
    {
        return;
    }

    string strBuf = CWebProtocol::CmdDeviceIP(strMac,
                                              pSection->GetDeviceModel(),
                                              REST_GET,
                                              pSection->m_netInfo.m_IpAccessMode,
                                              pSection->m_netInfo.m_szIP,
                                              pSection->m_netInfo.m_szSubnetMask,
                                              pSection->m_netInfo.m_szGateway,
                                              pSection->m_netInfo.m_szDNS1,
                                              pSection->m_netInfo.m_szDNS2);

    CommandSend(strBuf.data(), *pWebSec);
}

// 回复错误码
void CWebCommandSend::WebResponseErrorCode(CWebSection *pWebSec, int nResult, string strCommand)
{
    string strBuf = CWebProtocol::CmdUserAuthFailure(nResult, strCommand);
    CommandSend(strBuf.data(), *pWebSec, false, true, false);
}



/*************************************************************/

void CWebCommandSend::CommandSend(const char*  buf,       // 数据
                                  CWebSection& pWebSec,   // 设备
                                  bool isPrint ,          // 是否打印
                                  bool isSend,            // 是否立即发送
                                  bool isCheckValid)      // 是否检查有效性，区分用户登录错误时与服务器回调信息
{
    if(isCheckValid)
    {
        if(!pWebSec.IsValid())
        {
                return;
        }
    }

    if(isPrint)
    {
        if (pWebSec.GetWebType() == WT_QT)
        {
            LOG_INFO("\n----------send to web:\n");
        }
        else if(pWebSec.GetWebType() == WT_TCP)
        {
            LOG_INFO("\n----------send to dsp9312:\n");
        }
        else if(pWebSec.GetWebType() == WT_TRANSFER)
        {
            LOG_INFO("\n----------send to transfer:\n");
        }

        LOG(buf, LV_INFO);
    }

    // 大屏
    if(pWebSec.GetWebType() == WT_TABLET )
    {
        CommandSendToTablet(buf, pWebSec);
    }
    // websocket
    else if(pWebSec.GetWebType() == WT_QT )
    {
        #if 1
        if(isSend)
        {
            emit    g_Global.m_WebNetwork.m_QWebSock.send(pWebSec.m_strSockID,string(buf));        // QWebsocket不能在别的线程调用函数，所有用信号代替
        }
        else
        {
            //g_Global.m_WebNetwork.m_QWebSock.AddCommandBuf(pWebSec.m_strSockID, buf);
        }
        #else
             g_Global.m_WebNetwork.m_QWebSock.AddCommandBuf(pWebSec.m_strSockID, buf);
        #endif
    }
    // websocket
    else if(pWebSec.GetWebType() == WT_TRANSFER )
    {
        emit    g_Global.m_WebNetwork.m_WsQTransferClient.send(pWebSec.m_strSockID,string(buf));        // QWebsocket不能在别的线程调用函数，所有用信号代替
    }
}



void CWebCommandSend::CommandSendToTablet(const char *buf,
                                          CWebSection &pWebSec)
{
    g_Global.m_Network.SendWebData((void*)buf, strlen(buf), pWebSec, pWebSec.m_Tablet.m_strIP.data(), UDP_PORT);
}

void CWebCommandSend::CommandSendToWeb(const char *buf, CWebSection &pWebSec, bool isSend)                        // 是否立即发送
{
    #if 1
    if(isSend)
    {
        emit g_Global.m_WebNetwork.m_QWebSock.send(pWebSec.m_strSockID,string(buf));        // QWebsocket不能在别的线程调用函数，所有用信号代替
    }
    else
    {
        //g_Global.m_WebNetwork.m_QWebSock.AddCommandBuf(pWebSec.m_strSockID, buf);
    }
    #else
        g_Global.m_WebNetwork.m_QWebSock.AddCommandBuf(pWebSec.m_strSockID, buf);
    #endif
}

// 转发数据到Web端设备
void CWebCommandSend::ForwardDataToWeb(CWebSection* pWebSection,      // Web终端设备
                                       string       strData,          // 转发数据
                                       bool         bSuper,           // 仅发送到超级用户
                                       bool         isPrint,          // 是否打印
                                       bool         isForcedSend)     // 是否强制发送
{
    // 如果pWebSection为空，则分发到全部Web设备(大屏，浏览器)
    if(pWebSection == NULL)     // 发送到全部web分区
    {
        int nWebCount    = g_Global.m_WebSections.GetWebCount();      

        for(int i=0; i<nWebCount; i++)
        {
            CWebSection* pWebSec = g_Global.m_WebSections.GetWebSection(i);
            if(pWebSec)
            {
                if(!bSuper || (bSuper && pWebSec->IsSuperUser()))
                {
                    //LOG(strData.data(), LV_INFO);
                    CommandSend(strData.data(), *pWebSec, isPrint, true);
                }
            }
        }
    }
    // 发送到单个web设备
    else
    {  
         //if(pWebSection->IsForware() || isForcedSend)
         {
            CommandSend(strData.data(), *pWebSection, isPrint, true);
         }
    }

    //emit    g_Global.m_WebNetwork.m_QWebSock.send();
}


// 推送设备对讲状态到WEB
void  CWebCommandSend::PushIntercomStatusToWeb(CWebSection *pWebSec,CSection &callingDevice,CSection &calledDevice,int callStatus)
{
     //判断WEB用户是否正常
    if(pWebSec == NULL)
        return;
    LPCUserInfo pDestUser =  g_Global.m_Users.GetUserByID(pWebSec->GetSocketID());
    if(pDestUser == NULL)
        return;

    //20230721 如果不是管理员用户或者当前用户没有主叫和被叫设备的管理权限，那么返回
    if(!(pDestUser->IsSuperUser() || (pDestUser->HasFoundSectionMac(callingDevice.GetMac()) && pDestUser->HasFoundSectionMac(calledDevice.GetMac()))))
    {
        return;
    }

    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("push_intercom_status"));

    cJSON_AddItemToObject(root, "calling_mac", cJSON_CreateString(callingDevice.GetMac()));
    cJSON_AddItemToObject(root, "called_mac", cJSON_CreateString(calledDevice.GetMac()));
    cJSON_AddItemToObject(root, "calling_device_model", cJSON_CreateNumber(callingDevice.GetDeviceModel()));
    cJSON_AddItemToObject(root, "called_device_model", cJSON_CreateNumber(calledDevice.GetDeviceModel()));
    cJSON_AddItemToObject(root, "call_status", cJSON_CreateNumber(callStatus));

    char*  szBuf = cJSON_Print(root);

    string strData(szBuf);

    free(szBuf);
    cJSON_Delete(root);

    ForwardDataToWeb(pWebSec, strData, false, false, 1);
}   

#if SUPPORT_SERVER_SYNC
// 推送备用服务器信息到WEB
void  CWebCommandSend::PushBackupServerInfoToWeb(CWebSection *pWebSec)
{
     //判断WEB用户是否正常
    if(pWebSec == NULL)
        return;
    LPCUserInfo pDestUser =  g_Global.m_Users.GetUserByID(pWebSec->GetSocketID());
    if(pDestUser == NULL)
        return;

    //20230721 如果不是管理员用户或者当前用户没有主叫和被叫设备的管理权限，那么返回
    if(!pDestUser->IsSuperUser())
    {
        return;
    }

    string strBuf = CWebProtocol::CmdResponseSetBackupServer(0, true,"");

    ForwardDataToWeb(pWebSec, strBuf, false, false, 1);
}
#endif