#ifndef WEBPAGING_H
#define WEBPAGING_H

#include "Global/Const.h"
#include "Tools/G722/g722_encoder.h"
#if SUPPORT_SPEEX_DENOISE
#include "speex/speex_preprocess.h"
#endif

//WEB寻呼
class CWebPaging
{
public:
    CWebPaging(unsigned int *pSecIndexs,unsigned int uSecCount,int volume,bool IsAdmin,string uuid);
    ~CWebPaging();
    unsigned int *GetSecIndexs(void)    { return m_pSecIndexs;};
    unsigned int GetSecCount(void)      { return m_SecCount;};
    unsigned int GetVolume(void)        { return m_volume; };
    bool IsAdmin(void)  {return m_IsAdmin;};
    void webPagingLock(){
        pthread_mutex_lock(&m_webPagingMutex);
    }
    void webPagingUnLock(){
        pthread_mutex_unlock(&m_webPagingMutex);
    }

    G722_ENC_CTX *Get_G722_enc_ctx(){
        if(g722_enc_ctx == NULL)
        {
            int srate=G722_SAMPLE_RATE_8000;
	        srate &= ~ G722_SAMPLE_RATE_8000;
            g722_enc_ctx = g722_encoder_new(64000, srate);
        }
        return g722_enc_ctx;
    }
    #if SUPPORT_SPEEX_DENOISE
    SpeexPreprocessState *Get_speex_st_appPaging(){
        if(speexSt_appPagingMic == NULL)
        {
            speexSt_appPagingMic = speex_preprocess_state_init(512, 16000);
            int IsEnableDeNoise=1;//1表示开启，0表示关闭
            //SPEEX_PREPROCESS_SET_DENOISE表示降噪
            speex_preprocess_ctl(speexSt_appPagingMic, SPEEX_PREPROCESS_SET_DENOISE, &IsEnableDeNoise);//降噪
            int noiseSuppress = -45;//噪音分贝数，是一个负值
            //Speex的降噪是通过简单的设置音频数据的阀值，过滤掉低分贝的声音来实现的
            //优点是简单，使用Speex编解码库时可以直接使用
            //缺点是会把声音细节抹掉
            speex_preprocess_ctl(speexSt_appPagingMic, SPEEX_PREPROCESS_SET_NOISE_SUPPRESS, &noiseSuppress);
        }
        return speexSt_appPagingMic;
    }
    #endif

    string getUUID()                     { return m_uuid; };
    unsigned char *getMAC()              { return m_mac; };

    

private:
    int                 m_volume;                                   // 初始寻呼音量
    bool                m_IsAdmin;                                  // 是否管理员
    string              m_uuid;                                     // UUID 
    unsigned char       m_mac[6];                                   // MAC     
    unsigned int        m_SecCount;                                 // 选中分区数量
    unsigned int	    m_pSecIndexs[MAX_SECTION_COUNT_FORMAL];	    // 选中分区索引
    G722_ENC_CTX        *g722_enc_ctx;
    #if SUPPORT_SPEEX_DENOISE
    SpeexPreprocessState    *speexSt_appPagingMic;
    #endif
    pthread_mutex_t     m_webPagingMutex;
};



#endif
