#ifndef RADIOWEBSOCKETCLIENT_H
#define RADIOWEBSOCKETCLIENT_H

#include <QWebSocket>
#include <QTimer>
#include <QMutex>
#include <QThread>
#include <QByteArray>
#include <QQueue>
#include <QDateTime>
#include <string>
#include <vector>
#include <map>
#include <set>
#include <memory>
using namespace std;

// 前向声明
class NodeJSManager;

// 网络电台广播地址和端口
#define NET_RADIO_BROADCAST_ADDR  "230.230.231.16"

// 动态组播端口范围
#define MULTICAST_PORT_MIN 50500
#define MULTICAST_PORT_MAX 51500

// 网络电台音频参数 (128Kbps MP3)
#define NET_RADIO_SAMPLE_RATE 44100      // 44.1KHz采样率
#define NET_RADIO_FMT 16                 // 16位采样
#define NET_RADIO_CHANNELS 2             // 立体声
#define NET_RADIO_BITRATE 64000         // 128Kbps-128000 , 64Kbps-64000
#define NET_RADIO_BYTES_PER_SECOND (NET_RADIO_BITRATE / 8)  // 每秒字节数

// 音频缓冲参数
#define AUDIO_BUFFER_DURATION_SEC 1      // 1秒缓冲
#define MIN_BUFFER_SIZE (NET_RADIO_BYTES_PER_SECOND * AUDIO_BUFFER_DURATION_SEC)  // 最小缓冲大小

// MP3帧大小计算: (144 * bitrate / sample_rate) + padding
// 对于128Kbps, 44.1KHz: (144 * 128000 / 44100) ≈ 417字节
// 对于64Kbps, 44.1KHz: (144 * 64000 / 44100) ≈ 209字节
#define MP3_FRAME_SIZE 209               // MP3一帧的数据量(64kbps,如果是128kbps,使用418)
#define PLAYBACK_INTERVAL_MS 26          // 播放间隔(毫秒)
#define MP3_FRAME_DURATION_MS 26.12244897959  // MP3帧实际时长(毫秒): 1152 samples / 44100 Hz * 1000
#define TIMING_CHECK_INTERVAL_MS 1000    // 定时检查间隔(毫秒)

// 电台播放会话信息
struct RadioPlaySession
{
    string sessionId;           // 会话ID
    string radioUrl;            // 电台URL
    vector<string> zoneMacs;    // 分区设备MAC列表
    int volume;                 // 播放音量
    int multicastPort;          // 分配的组播端口
    bool isActive;              // 是否活跃
    QWebSocket* webSocket;      // WebSocket连接
    
    // 音频缓冲相关
    QByteArray audioBuffer;     // 音频数据缓冲区
    QMutex bufferMutex;         // 缓冲区互斥锁
    QTimer* playbackTimer;      // 播放定时器
    bool isBuffering;           // 是否正在缓冲
    qint64 lastPlayTime;        // 上次播放时间
    
    // 会话检查相关
    QTimer* sessionCheckTimer;  // 会话检查定时器
    int unusedCount;            // 连续未使用计数
    
    // 播放时间同步相关
    qint64 playStartTime;       // 播放开始时间(毫秒)
    qint64 totalSentBytes;      // 已发送的总字节数
    QTimer* timingCheckTimer;   // 定时检查定时器
    
    RadioPlaySession()
    {
        volume = 50;
        multicastPort = 0;
        isActive = false;
        webSocket = nullptr;
        playbackTimer = nullptr;
        isBuffering = true;
        lastPlayTime = 0;
        sessionCheckTimer = nullptr;
        unusedCount = 0;
        playStartTime = 0;
        totalSentBytes = 0;
        timingCheckTimer = nullptr;
    }
    
    // 禁用复制构造函数和复制赋值操作符
    RadioPlaySession(const RadioPlaySession&) = delete;
    RadioPlaySession& operator=(const RadioPlaySession&) = delete;
    
    // 移动构造函数
    RadioPlaySession(RadioPlaySession&& other) noexcept
        : sessionId(std::move(other.sessionId))
        , radioUrl(std::move(other.radioUrl))
        , zoneMacs(std::move(other.zoneMacs))
        , volume(other.volume)
        , multicastPort(other.multicastPort)
        , isActive(other.isActive)
        , webSocket(other.webSocket)
        , audioBuffer(std::move(other.audioBuffer))
        , playbackTimer(other.playbackTimer)
        , isBuffering(other.isBuffering)
        , lastPlayTime(other.lastPlayTime)
        , sessionCheckTimer(other.sessionCheckTimer)
        , unusedCount(other.unusedCount)
        , playStartTime(other.playStartTime)
        , totalSentBytes(other.totalSentBytes)
        , timingCheckTimer(other.timingCheckTimer)
    {
        other.webSocket = nullptr;
        other.playbackTimer = nullptr;
        other.sessionCheckTimer = nullptr;
        other.timingCheckTimer = nullptr;
    }
    
    // 移动赋值操作符
    RadioPlaySession& operator=(RadioPlaySession&& other) noexcept
    {
        if (this != &other)
        {
            sessionId = std::move(other.sessionId);
            radioUrl = std::move(other.radioUrl);
            zoneMacs = std::move(other.zoneMacs);
            volume = other.volume;
            multicastPort = other.multicastPort;
            isActive = other.isActive;
            webSocket = other.webSocket;
            audioBuffer = std::move(other.audioBuffer);
            playbackTimer = other.playbackTimer;
            isBuffering = other.isBuffering;
            lastPlayTime = other.lastPlayTime;
            
            other.webSocket = nullptr;
            other.playbackTimer = nullptr;
        }
        return *this;
    }
};

// WebSocket客户端管理类
class RadioWebSocketClient : public QObject
{
    Q_OBJECT
    
public:
    static RadioWebSocketClient* getInstance();
    
    // 初始化Node.js服务
    bool initializeNodeJSService();
    
    // 关闭Node.js服务
    void shutdownNodeJSService();
    
    // 启动电台播放
    bool startRadioPlay(const string& radioUrl, const vector<string>& zoneMacs, int volume, string& sessionId);
    
    // 停止电台播放
    bool stopRadioPlay(const string& sessionId);
    
    // 停止所有电台播放
    void stopAllRadioPlay();
    
    // 获取会话信息
    const map<string, unique_ptr<RadioPlaySession>>& getSessions() const { return m_sessions; }
    
private slots:
    void onConnected();
    void onDisconnected();
    void onBinaryMessageReceived(QByteArray message);
    void onTextMessageReceived(QString message);
    void onError(QAbstractSocket::SocketError error);
    void onHeartbeatTimeout();
    void onPlaybackTimerTimeout();
    void onSessionCheckTimeout();
    void onTimingCheckTimeout();
    
private:
    RadioWebSocketClient(QObject *parent = nullptr);
    ~RadioWebSocketClient();
    
    // 创建新的WebSocket连接
    QWebSocket* createWebSocketConnection(const string& sessionId);
    
    // 发送播放命令
    void sendPlayCommand(QWebSocket* webSocket, const string& radioUrl);
    
    // 发送心跳
    void sendHeartbeat(QWebSocket* webSocket);
    
    // 广播音频数据到分区设备
    void broadcastAudioToZones(const string& sessionId, const QByteArray& audioData);
    
    // 生成会话ID
    string generateSessionId();
    
    // 清理会话
    void cleanupSession(const string& sessionId);
    
    // 端口分配管理
    int allocateMulticastPort();
    void releaseMulticastPort(int port);
    bool isPortInUse(int port);
    
private:
    static RadioWebSocketClient* m_instance;
    static QMutex m_instanceMutex;
    
    map<string, unique_ptr<RadioPlaySession>> m_sessions;  // 会话管理
    QMutex m_sessionsMutex;                    // 会话互斥锁
    
    set<int> m_usedPorts;                      // 已使用的端口集合
    QMutex m_portsMutex;                       // 端口分配互斥锁
    
    QTimer* m_heartbeatTimer;                  // 心跳定时器
    NodeJSManager* m_nodeJSManager;            // Node.js管理器
    
    const string m_nodeServerUrl = "ws://localhost:8085";  // Node.js服务地址
};

#endif // RADIOWEBSOCKETCLIENT_H