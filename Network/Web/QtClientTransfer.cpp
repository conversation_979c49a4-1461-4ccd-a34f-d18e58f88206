#include "stdafx.h"
#include "QtClientTransfer.h"
#include <QUrl>
#include <QNetworkProxy>
#include <algorithm>
#include "Tools/base64.h"


QtClientTransfer::QtClientTransfer(QObject *parent)
    : QObject(parent)
{
    m_webSocket = new QWebSocket();

    m_webSocket->setProxy(QNetworkProxy::NoProxy);  //一定要先取消代理，否则如果开了系统代理软件（如v2ray)，可能连接不上局域网

    QObject::connect(m_webSocket, SIGNAL(connected()), this, SLOT(onConnected()));
    QObject::connect(m_webSocket, SIGNAL(disconnected()), this, SLOT(onDisconnected()));
    QObject::connect(m_webSocket, SIGNAL(textMessageReceived(QString)), this, SLOT(onTextMessageReceived(QString)));
    QObject::connect(m_webSocket, SIGNAL(error(QAbstractSocket::SocketError)), this, SLOT(slotError(QAbstractSocket::SocketError)));

    QObject::connect(&m_Timer, SIGNAL(timeout()), this, SLOT(OnTimeOut()));

    QObject::connect(this, SIGNAL(send(string,string)), this, SLOT(SendMessageQ(string,string)));

    login_status = LOGIN_STATUS_DISCONNECT;       //默认未登录
    wsStatus = WS_STATUS_DISCONNECT;    //默认未连接

}

QtClientTransfer::~QtClientTransfer()
{
    if (m_webSocket)
    {
        m_webSocket->close();
        delete(m_webSocket);
    }
}

void QtClientTransfer::onConnected()
{
    qDebug("Connected to WebSocket server");
    setWsStatus(WS_STATUS_CONNECTED);
}

void QtClientTransfer::onDisconnected()
{
    qDebug("Disconnected from WebSocket server");
    //连接失败或者断开连接
    setWsStatus(WS_STATUS_DISCONNECT);

    if(!(login_status == LOGIN_STATUS_DISCONNECT || login_status == LOGIN_STATUS_CONNECT_ERROR || login_status == LOGIN_STATUS_FAILED))
    {
       setLoginStatus(LOGIN_STATUS_DISCONNECT);
    }

    memset(&st_extension_userInfo,0,sizeof(st_extension_userInfo));
    map_extension.clear();

#if 1
    // 获取关闭码和原因
        auto closeCode = m_webSocket->closeCode();
        auto closeReason = m_webSocket->closeReason();

        qDebug() << "Disconnected with code:" << closeCode;
        qDebug() << "Reason:" << closeReason;

        // 判断关闭码
        switch (closeCode) {
            case QWebSocketProtocol::CloseCodeNormal:
                qDebug() << "Connection closed normally.";
                break;
            case QWebSocketProtocol::CloseCodeGoingAway:
                qDebug() << "Connection is going away.";
                break;
            case QWebSocketProtocol::CloseCodeProtocolError:
                qDebug() << "Protocol error.";
                break;
            // 添加更多的关闭码判断...
            default:
                qDebug() << "Other close code:" << closeCode;
                break;
        }
#endif
}

void QtClientTransfer::onTextMessageReceived(QString message)
{
    //qDebug()<<"Received: " <<message;
    HandleWebCommand(message.toLocal8Bit().data());
}


// 解析Websocket数据(WEB端)
void QtClientTransfer::HandleWebCommand(const char *data)
{
    cJSON* root = cJSON_Parse(data);
    if(root == NULL) return;
    cJSON* jsCommand = cJSON_GetObjectItem(root, "command");
    if(jsCommand == NULL)  return;
    string strCommand = jsCommand->valuestring;
    cJSON* jsUUID = cJSON_GetObjectItem(root, "uuid");
    if(jsUUID == NULL)  return;
    string strUUID = jsUUID->valuestring;

    if(strCommand == "heartbeat") {
       //printf("Transfer:heatbeat...\n");
       m_heartBeatRecvTimeout = 0;
       cJSON_Delete(root);
       return;
    }

    #if 0
    if(strCommand == "user_login")
    {
        cJSON* jsResult = cJSON_GetObjectItem(root, "result");
        if(jsResult->valueint != 0)
        {
            setLoginStatus(LOGIN_STATUS_FAILED);
            cJSON_Delete(root);
            return;
        }

        setLoginStatus(LOGIN_STATUS_SUCCEED);

        cJSON* jsData = cJSON_GetObjectItem(root, "data");
        cJSON* jsAccount = cJSON_GetObjectItem(jsData, "account");
        cJSON* jsCompany = cJSON_GetObjectItem(jsData, "company");
        cJSON* jsID = cJSON_GetObjectItem(jsData, "id");
        cJSON* jsName = cJSON_GetObjectItem(jsData, "name");
        cJSON* jsUUID = cJSON_GetObjectItem(jsData, "uuid");

        st_extension_userInfo.id = jsID->valueint;
        st_extension_userInfo.name = jsName->valuestring;
        st_extension_userInfo.uuid = jsUUID->valuestring;
        st_extension_userInfo.account = jsAccount->valuestring;
        st_extension_userInfo.company = jsCompany->valuestring;

        qDebug()<<"uuid="<<st_extension_userInfo.uuid.data();

        //登录成功后，查询次数
        queryCount();
    }
    else if(strCommand == "get_count")
    {
        cJSON* jsResult = cJSON_GetObjectItem(root, "result");
        if(jsResult->valueint != 0)
        {
            setLoginStatus(LOGIN_STATUS_FAILED);
            cJSON_Delete(root);
            return;
        }
        cJSON* jsData = cJSON_GetObjectItem(root, "data");
        // 遍历data对象中的所有键值对
        cJSON *item = jsData->child;
        while (item != NULL) {
            // 打印键名和对应的值
            qDebug() << "Key:" << item->string << ", Value:" << item->valueint;

            map_extension[item->string] = item->valueint;

            // 移动到下一个键值对
            item = item->next;
        }
    }
    #endif

    //先判断UUID是否存在，不存在，就加入
    CWebSection* pWebSec=NULL;
    pWebSec=g_Global.m_WebSections.GetWebSectionBySocketID(strUUID);
    LPCUserInfo lpUser=g_Global.m_Users.GetUserByUserUUID(strUUID);

    if(!lpUser)
    {
        printf("transfer:user error!\n");
        cJSON_Delete(root);
        return;
    }
    if(strCommand == "forwarding_server_user_state")
    {
        cJSON* jsState = cJSON_GetObjectItem(root, "state");
        int state=jsState->valueint;
        if(state == 1)
        {
            if(pWebSec==NULL)
            {
                printf("transfer:add account=%s\n",lpUser->GetAccount());
                g_Global.m_Users.AddUuid(strUUID,lpUser->GetAccount());        // 添加用户UUID与账户名的映射
                pWebSec = new CWebSection(strUUID,WT_TRANSFER);
                pWebSec->SetSocketID(strUUID);
                pWebSec->ResetSecFileDateTime();
                g_Global.m_WebSections.AddWebSection(*pWebSec);

                CMyString strLogContents;
                strLogContents.Format("%s:%s,IP=%s", lpUser->GetAccount(),"Logged in","127.0.0.1");
                g_Global.m_logTable.InsertLog(	lpUser->GetAccount(),
                                                lpUser->GetAccount(),
                                                LT_ADVANCED_LOG,
                                                strLogContents);
            }
        }
        else
        {
            //删除
            if(pWebSec)
            {
                g_Global.m_WebSections.RemoveWebSectionBySockID(strUUID);
                g_Global.m_Users.RemoveUuid(strUUID,"127.0.0.1");
            }
        }
    }
    else
    {
        //printf("QtClientTransfer:user=%s,command=%s\n",lpUser->GetAccount(),strCommand.data());
        g_Global.m_WebNetwork.m_WebHandle.HandleUserCommand(*pWebSec, data);
    }


    cJSON_Delete(root);
}

// 响应报错
void QtClientTransfer::slotError(QAbstractSocket::SocketError error)
{
    qDebug() << __FILE__ << __LINE__ << (int)error << m_webSocket->errorString();
    if(m_webSocket->errorString().contains("Connection refused") || m_webSocket->errorString().contains("Invalid socket descriptor"))
    {
        setLoginStatus(LOGIN_STATUS_CONNECT_ERROR);
    }
}


void QtClientTransfer::connect()
{
    if (getWSStatus() == WS_STATUS_DISCONNECT) {
        qDebug("connecting to WebSocket Transfer server...");
        setWsStatus(WS_STATUS_CONNECTING);
        setLoginStatus(LOGIN_STATUS_LOGGING);

        //主机mac组成
        const char *serverMac=CNetwork::GetHostMac();
        char serverAddr[256]={0};
        //将服务器mac转换成base64编码
        string base64_mac = base64_encode((unsigned char*)serverMac,strlen(serverMac));
        // 去掉=号
        base64_mac.erase(std::remove(base64_mac.begin(), base64_mac.end(), '='), base64_mac.end());
        sprintf(serverAddr,"ws://***********:8091/websocket/server-%s",base64_mac.data());
        m_webSocket->open(QUrl(serverAddr));
    } else {
        //todo
    }
}

void QtClientTransfer::disconnect()
{
    if (wsStatus != WS_STATUS_DISCONNECT) {
        m_webSocket->close();
        //m_webSocket->abort();
        qDebug("Disconnecting from WebSocket server...");
        m_heartBeatRecvTimeout=0;
        m_heartBeatSendTimeout=0;
    }
}

void QtClientTransfer::sendMessage(string strBuf)
{
    if (getWSStatus() == WS_STATUS_CONNECTED) {
        m_webSocket->sendTextMessage(QString::fromStdString(strBuf));
    } else {

    }
}


int QtClientTransfer::setWsStatus(int status)
{
    if(status!=wsStatus)
    {
        qDebug()<<"setWsStatus:"<<status;
        wsStatus=status;
        emit wsConnectStatusChanged(status);
    }
    m_heartBeatRecvTimeout=0;
    m_heartBeatSendTimeout=0;
    return 0;
}

int QtClientTransfer::getWSStatus()
{
    return wsStatus;
}

bool QtClientTransfer::isWsConnected()
{
    return wsStatus==WS_STATUS_CONNECTED;
}


int QtClientTransfer::setLoginStatus(int status)
{
    if(status!=login_status)
    {
        qDebug()<<"setLoginStatus:"<<status;
        login_status=status;
        emit wsLoginStatusChanged(login_status);
    }
    return 0;
}

int QtClientTransfer::getLoginStatus()
{
    return login_status;
}



void QtClientTransfer::login(string account,string password)
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("user_login"));

    cJSON* data = cJSON_CreateObject();
    cJSON_AddItemToObject(data, "account", cJSON_CreateString(account.data()));
    cJSON_AddItemToObject(data, "password", cJSON_CreateString(password.data()));

    cJSON_AddItemToObject(root, "data", data);

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);

    qDebug()<<szBuf;

    sendMessage(strData);

    free(szBuf);
    cJSON_Delete(root);
}



void QtClientTransfer::queryCount()
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_count"));

    cJSON* data = cJSON_CreateObject();
    cJSON_AddItemToObject(data, "uuid", cJSON_CreateString(st_extension_userInfo.uuid.data()));

    cJSON_AddItemToObject(root, "data", data);

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);

    qDebug()<<szBuf;

    sendMessage(strData);

    free(szBuf);
    cJSON_Delete(root);
}



void QtClientTransfer::StartWorking()
{
    if(g_Global.m_bCloudControl)
    {
        connect();
    }
    m_Timer.start(5000);   //5秒连接一次
}


void QtClientTransfer::OnTimeOut()
{
    if(getWSStatus() == WS_STATUS_DISCONNECT)
    {
        if(g_Global.m_bCloudControl)
        {
            connect();
        }
    }
    else if(getWSStatus() == WS_STATUS_CONNECTED)
    {
        if(g_Global.m_bCloudControl)
        {
            //每隔20秒发送心跳到中间服务器
            m_heartBeatSendTimeout += 5;
            if(m_heartBeatSendTimeout >= 20)
            {
                SendMessageQ("","{\"command\":\"heartbeat\"}");
                m_heartBeatSendTimeout=0;
            }

            m_heartBeatRecvTimeout+=5;
            if(m_heartBeatRecvTimeout >= 60) {
                printf("Transfer Server:heartBeatTimeout,disconnect!\n");
                disconnect();
            }
        }
        else
        {
            disconnect();
        }
    }
}


void QtClientTransfer::SendMessageQ(string strUID, string strBuf)
{
    QMutexLocker locker(&m_mutex_send);
    if(m_webSocket != NULL && m_webSocket->isValid())
    {
        //重组json，加入uuid
        cJSON *root = cJSON_Parse(strBuf.data());
        if(root!=NULL)
        {
            // 将 uuid 项添加到 root 对象中
            cJSON_AddItemToObject(root, "uuid", cJSON_CreateString(strUID.data()));
            char*  szBuf = cJSON_Print(root);
            m_webSocket->sendTextMessage(QString(szBuf));
            free(szBuf);
            cJSON_Delete(root);
        }
    }
}