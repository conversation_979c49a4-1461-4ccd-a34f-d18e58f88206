//#include "stdafx.h"
//#include "WebSocket.h"
//#include <arpa/inet.h>
//#include <sys/types.h>
//#include <sys/epoll.h>
//#include <sys/select.h>
//#include <sys/time.h>
//#include <sys/socket.h>
//#include <unistd.h>
//#include <fcntl.h>
//#include <string.h>
//#include <stdio.h>
//#include <map>
//#include <pthread.h>
//#include "WebNetwork.h"

///*
//#define  MAX_LISTEN_COUNT   256
//#define  MAX_EVENTS_COUNT   256
//#define  TIMEWAIT           100


//CWebSocket::CWebSocket():m_listenFd(0),m_epollFd(0)
//{

//}


//CWebSocket::~CWebSocket()
//{

//}


//void CWebSocket::WebRun()
//{
//    pthread_t pth;
//    pthread_create(&pth, NULL, WebPthread, (void*)this);
//}


//bool CWebSocket::CreateSock()
//{
//    m_listenFd = socket(AF_INET, SOCK_STREAM, 0);
//    if(m_listenFd == -1)
//    {
//        perror("web create socket failed");
//        return false;
//    }

//    struct sockaddr_in server_addr;
//    memset(&server_addr, 0, sizeof(server_addr));
//    server_addr.sin_family = AF_INET;
//    server_addr.sin_addr.s_addr =  htonl(INADDR_ANY);
//    server_addr.sin_port = htons(g_Global.m_WEB_PORT);

//    /*设置地址重用,以解决第二次运行时端口占用的问题(SO_REUSEADDR允许完全相同的地址和端口的重复绑定，但这只用于UDP)*/
//    /*int opt = 1;
//    if((setsockopt(m_listenFd,SOL_SOCKET,SO_REUSEADDR,&opt,sizeof(opt)))<0)//设置socket状态,允许在 bind（）过程中本地地址可重复使用
//    {
//        perror("setsockopt SO_REUSEADDR failed");
//        //LOG("ERROR: setsockopt SO_REUSEADDR failed");
//        close(m_listenFd);
//    }
//    socklen_t flag;

//    /*int nRet;
//    int nRecvLen1 = MAX_SOCKET_BUF_LEN*4;
//    int nRecvLen2 = 0;

//    nRet = setsockopt(m_listenFd, SOL_SOCKET, SO_RCVBUF, &nRecvLen1, 4);
//    printf("nRecvLen1 : %d\n", nRecvLen1);
//    printf("nRet : %d\n", nRet);

//    nRet = getsockopt(m_listenFd, SOL_SOCKET, SO_RCVBUF, &nRecvLen2, &flag);
//    printf("nRecvLen2 : %d\n", nRecvLen2);*/

//    /*if(bind(m_listenFd, (sockaddr*)&server_addr, sizeof(server_addr)) == -1)
//    {
//        perror("web bind socket failed!");
//        close(m_listenFd);
//        return false;
//    }

//    if(listen(m_listenFd, MAX_LISTEN_COUNT) == -1)
//    {
//        perror("web listen socket failed!");
//        close(m_listenFd);
//        return false;
//    }

//    m_epollFd = epoll_create(MAX_EVENTS_COUNT);
//    CtlEpollEvent(m_listenFd, true);

//    LOG("web server is run", LV_INFO);
//    return true;
//}


//// 设置socket 连接，关闭
//void CWebSocket::CtlEpollEvent(int fd, bool flag)
//{
//    struct epoll_event ev;
//    ev.data.fd = fd;
//    ev.events  = flag ? (EPOLLIN|EPOLLHUP|EPOLLERR) : 0;    // 使用边缘触发模式，丢掉最后一个包，水平触发模式掉包几率大

//    int nRet = epoll_ctl(m_epollFd, flag ? EPOLL_CTL_ADD : EPOLL_CTL_DEL, fd, &ev);
//    if(nRet == -1)
//    {
//            printf("fd : %d\n", fd);
//            perror("epoll_ctl  failed : ");
//            return;
//    }

//    if(flag)
//    {
//        SetNoblock(fd);
//        if(fd != m_listenFd)
//        {
//            printf("fd: %d join epoll loop\n", fd);
//            g_Global.m_WebSections.AddWeb(fd);
//        }
//    }
//    else
//    {
//        close(fd);
//        printf("fd: %d quit epoll loop\n", fd);
//    }
//}


//// epoll 循环
//void CWebSocket::EpollLoop()
//{
//    struct sockaddr_in client_addr;
//    bzero(&client_addr, sizeof(client_addr));
//    socklen_t cliLen = 0;
//    int nfds = 0;
//    int fd = 0;
//    int bufLen = 0;
//    struct epoll_event events[MAX_EVENTS_COUNT];

//    char szBuf[MAX_BUF_LEN] = {0};
//    while(true)
//    {
//        nfds = epoll_wait(m_epollFd, events, MAX_EVENTS_COUNT, TIMEWAIT);

//        //printf("nfds = %d\n", nfds);
//        for(int i=0; i<nfds; i++)
//        {
//            if(events[i].data.fd == m_listenFd)
//            {
//                fd = accept(m_listenFd, (sockaddr*)&client_addr, &cliLen);
//                if(fd > 2)
//                {
//                    CtlEpollEvent(fd, true);    // 新的连接，加入epoll
//                    //pthread_t pth;
//                    //pthread_create(&pth, NULL, TestPthread, &fd);
//                }
//                else
//                {
//                    printf("accept by : %d failed\n", fd);
//                }
//                continue;
//            }

//            if(events[i].events & EPOLLHUP)
//            {
//                if((fd = events[i].data.fd) < 0)
//                    continue;
//                CtlEpollEvent(fd, false);
//            }

//            else if(events[i].events & EPOLLERR)
//            {
//                if((fd = events[i].data.fd) < 0)
//                    continue;
//                CtlEpollEvent(fd, false);
//            }

//            if(events[i].events & EPOLLIN)
//            {
//                if((fd = events[i].data.fd) < 0)
//                    continue;

//                bzero(szBuf, sizeof(szBuf));
//                if((bufLen=recv(fd, szBuf, MAX_BUF_LEN, MSG_DONTWAIT)) <= 0)
//                {
//                    printf("websocekt fd=%d read error\n", fd);
//                    //CtlEpollEvent(fd, false);
//                    break;
//                }
//                printf("buf len: %d , szBuf len : %d\n", bufLen, strlen(szBuf));
//                //g_Global.m_WebNetwork.m_WebHandle.HandleCommand(fd, szBuf, bufLen);
//             }
//        }
//    }

//}



//void CWebSocket::SelectLoop()
//{
//    struct sockaddr_in client_addr;
//    bzero(&client_addr, sizeof(client_addr));
//    socklen_t cliLen = 0;
//    char szBuf[MAX_BUF_LEN] = {0};
//    int bufLen = 0;

//    fd_set sockets;
//    fd_set readFd;
//    int maxfd = m_listenFd+1;
//    struct timeval tv;
//    vector<int> acceptList;
//    FD_ZERO(&sockets);
//    FD_SET(m_listenFd, &sockets);        //把listenFd加入描述符集

//    while(1)
//    {
//        FD_ZERO(&readFd);     //清除描述符集
//        readFd = sockets;
//        //FD_SET(sockets, &readFd);
//        tv.tv_sec = 5;     //超时的设定
//        tv.tv_usec = 0;

//        //如果文件描述符中有连接请求 会做相应的处理，实现I/O的复用 多用户的连接通讯
//        int ret = select(maxfd,&readFd,NULL,NULL,&tv);
//        if(ret < 0) //没有找到有效的连接 失败
//        {
//            perror("select error!\n");
//            break;
//        }

//        if(FD_ISSET(m_listenFd, &readFd))
//        {
//            int fd = accept(m_listenFd, (sockaddr*)&client_addr, &cliLen);
//            if(fd > 2)
//            {
//                FD_SET(fd, &sockets);
//                maxfd = fd+1;
//                acceptList.push_back(fd);
//                printf("accept by : %d success\n", fd);
//                g_Global.m_WebSections.AddWeb(fd);
//            }
//            else
//            {
//                printf("accept by : %d failed\n", fd);
//                continue;
//            }
//        }
//        else
//        {
//            // 检查读操作
//            vector<int>::iterator iter = acceptList.begin();
//            for(; iter != acceptList.end(); iter++)
//            {
//                int fd = *iter;

//                if(FD_ISSET(fd, &readFd))
//                {
//                    bzero(szBuf, sizeof(szBuf));
//                    bufLen = recv(fd, szBuf, MAX_BUF_LEN, 0);
//                    if(bufLen < 0)
//                    {
//                        printf("websocekt fd=%d read error\n", fd);
//                        //CtlEpollEvent(fd, false);
//                        continue;
//                    }
//                    else if(bufLen == 0)
//                    {
//                        close(fd);
//                        iter = acceptList.erase(iter);
//                        iter--;
//                        FD_CLR(fd, &sockets);
//                    }
//                    else
//                    {
//                        //szBuf[bufLen] = '\0';
//                        printf("buf len: %d , szBuf len : %d\n", bufLen, strlen(szBuf));
//                        //g_Global.m_WebNetwork.m_WebHandle.HandleCommand(fd, szBuf, bufLen);
//                    }
//                }
//            }

//        }

//    }

//}


//// 设置套接字非阻塞
//int CWebSocket::SetNoblock(int fd)
//{
//    int flags;
//    if ((flags = fcntl(fd, F_GETFL, 0)) == -1)
//        flags = 0;
//    return fcntl(fd, F_SETFL, flags | O_NONBLOCK);
//}


//void *CWebSocket::WebPthread(void *lparam)
//{
//    CWebSocket* pWeb = (CWebSocket*)lparam;

//    if(pWeb->CreateSock())
//    {
//        pWeb->EpollLoop();      // epoll模型
//        //pWeb->SelectLoop();   // select模型
//    }

//    exit(0);
//}*/


///*void *CWebSocket::TestPthread(void *lparam)
//{
//        int* fd = (int*)lparam;
//        char szBuf[MAX_SOCKET_BUF_LEN];
//        while(1)
//        {
//            bzero(szBuf, sizeof(szBuf));
//            int bufLen = recv(*fd, szBuf, MAX_BUF_LEN, 0);
//            if(bufLen < 0)
//            {
//                printf("websocekt fd=%d read error\n", fd);
//                //CtlEpollEvent(fd, false);
//                break;
//            }
//            else if(bufLen == 0)
//            {
//                close(*fd);
//                break;
//            }
//            else
//            {
//                //szBuf[bufLen] = '\0';
//                printf("buf len: %d , szBuf len : %d\n", bufLen, strlen(szBuf));
//                g_Global.m_WebNetwork.m_WebHandle.HandleCommand(*fd, szBuf, bufLen);
//            }
//        }
//}*/
