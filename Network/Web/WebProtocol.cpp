#include "stdafx.h"
#include "WebProtocol.h"
#include "Tools/cJSON.h"
#include "Network/Network.h"
#include "model/Device/AudioCall.h"
#include "Tools/base64.h"

CWebProtocol::CWebProtocol()
{

}

string CWebProtocol::CmdResponseGetHeartbeatInfo(bool bLinkNet,
                                                int nResult)    // 返回结果
{
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("heartbeat"));
    cJSON_AddItemToObject(root, "internet_accessible", cJSON_CreateBool(bLinkNet));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 服务器通知客户端更新文件
string CWebProtocol::CmdNotifyUpdateFile(int     nFileType,      // 文件类型
                                         string  strUpdateTime,      // 文件更新时间
                                         string  strWebIP,                // Web服务器ip
                                         string  strWebPort,            // Web服务器端口
                                         string  strFilePath,             // 文件路径
                                         int       nResult)                  // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("update_file"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));
    cJSON_AddItemToObject(root, "file_type", cJSON_CreateNumber(nFileType));
    cJSON_AddItemToObject(root, "update_at", cJSON_CreateString(strUpdateTime.data()));
    cJSON_AddItemToObject(root, "server_ip", cJSON_CreateString(strWebIP.data()));
    cJSON_AddItemToObject(root, "server_port", cJSON_CreateString(strWebPort.data()));
    #if 0   //不需要返回具体地址，WEB会自行处理，获取相应信息
    cJSON_AddItemToObject(root, "file_path", cJSON_CreateString(strFilePath.data()));
    #endif
    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 3.5 下发分区状态
string  CWebProtocol::CmdForwardZoneInfo(int nDeviceType,   // 设备类型
                                         int nPageCount,    // 总页数
                                         int nPage,         // 第几页
                                         int nZoneCount,    // 此页包含的分区数
                                         ZoneInfo* zones,   // 分区状态列表
                                         bool bAllZone,     // 是否请求全部分区
                                         int       nResult) // 返回结果
{
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command",    cJSON_CreateString("get_device_info"));
    cJSON_AddItemToObject(root, "device_type",cJSON_CreateNumber(nDeviceType));
    cJSON_AddItemToObject(root, "result",     cJSON_CreateNumber(nResult));
    cJSON_AddItemToObject(root, "all_zone",   cJSON_CreateBool(bAllZone));
    cJSON_AddItemToObject(root, "page_count", cJSON_CreateNumber(nPageCount));
    cJSON_AddItemToObject(root, "page",       cJSON_CreateNumber(nPage));
    cJSON_AddItemToObject(root, "zone_count", cJSON_CreateNumber(nZoneCount));
    cJSON *cjZones = cJSON_CreateArray();

    for(int i = 0; i < nZoneCount; i++)
    {
        cJSON *zone = cJSON_CreateObject();
        cJSON_AddItemToObject(zone, "id",            cJSON_CreateNumber(zones[i].nID));
        cJSON_AddItemToObject(zone, "ip",            cJSON_CreateString(zones[i].strIP.data()));
        cJSON_AddItemToObject(zone, "mac" ,          cJSON_CreateString(zones[i].strMac.data()));
        cJSON_AddItemToObject(zone, "monitor_mac",   cJSON_CreateString(zones[i].strMonitorMac.data()));
        cJSON_AddItemToObject(zone, "monitor_status",cJSON_CreateNumber(zones[i].monitorStatus));
        cJSON_AddItemToObject(zone, "name",          cJSON_CreateString(zones[i].strName.data()));
        cJSON_AddItemToObject(zone, "volume",        cJSON_CreateNumber(zones[i].nVolume));
        cJSON_AddItemToObject(zone, "source",        cJSON_CreateNumber(zones[i].nSource));
        cJSON_AddItemToObject(zone, "source_name",   cJSON_CreateString(zones[i].strSourceName.data()));
        cJSON_AddItemToObject(zone, "play_id",       cJSON_CreateNumber(zones[i].nPlayID));
        cJSON_AddItemToObject(zone, "play_status",   cJSON_CreateNumber(zones[i].nPlayStatus));
        cJSON_AddItemToObject(zone, "device_model",  cJSON_CreateNumber(zones[i].nDeviceModel));
        cJSON_AddItemToObject(zone, "timer_valid",   cJSON_CreateBool(zones[i].bTimerValid));
        cJSON_AddItemToObject(zone, "firmware_version" , cJSON_CreateString(zones[i].strFirmwareVersion.data()));
        cJSON_AddItemToObject(zone, "memory",        cJSON_CreateString(zones[i].strMemory.data()));
        cJSON_AddItemToObject(zone, "feature",        cJSON_CreateNumber(zones[i].deviceFeature));
        cJSON_AddItemToObject(zone, "network_mode",  cJSON_CreateNumber(zones[i].networkMode));

        cJSON_AddItemToObject(zone, "module4g_csq",  cJSON_CreateNumber(zones[i].nModule4GSignalLevel));
        cJSON_AddItemToObject(zone, "module4g_iccid",  cJSON_CreateString(zones[i].strModule4GIccid.data()));

#if 0   //去除流媒体网关及SIP相关参数应答
        cJSON_AddItemToObject(zone, "exten",         cJSON_CreateString(zones[i].strExten.data()));
        cJSON_AddItemToObject(zone, "exten_status",  cJSON_CreateNumber(zones[i].nExtenStatus));
        cJSON_AddItemToObject(zone, "net_audio_mac",    cJSON_CreateString(zones[i].strStreamingMac.data()));
        cJSON_AddItemToObject(zone, "net_audio_ip",     cJSON_CreateString(zones[i].strStreamingIP.data()));
        cJSON_AddItemToObject(zone, "net_audio_status", cJSON_CreateNumber(zones[i].nStreamingStatus));
        cJSON_AddItemToObject(zone, "net_audio_mode",   cJSON_CreateNumber(zones[i].nStreamingMode));
#endif
        cJSON_AddItemToArray(cjZones, zone);
    }
    cJSON_AddItemToObject(root, "zones", cjZones);

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}



// 3.5 下发分组状态
string  CWebProtocol::CmdForwardGroupInfo(
                                         string  strUpdateTime,      // 文件更新时间
                                         int nGroupCount,
                                         GroupInfo* groups,   // 分组信息
                                         int       nResult) // 返回结果
{
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command",    cJSON_CreateString("get_group_info"));
    cJSON_AddItemToObject(root, "update_at", cJSON_CreateString(strUpdateTime.data()));
    cJSON *cjgroups = cJSON_CreateArray();

    for(int i = 0; i < nGroupCount ; i++)
    {
        cJSON *group = cJSON_CreateObject();
        cJSON_AddItemToObject(group, "group_id",           cJSON_CreateString( groups[i].m_strID.Data()));
        cJSON_AddItemToObject(group, "group_name",         cJSON_CreateString(groups[i].m_szGroupName));
        cJSON_AddItemToObject(group, "group_account",      cJSON_CreateString(groups[i].m_strAccount.Data()));

        cJSON *cjZone_mac = cJSON_CreateArray();
        for(int j=0;j<groups[i].m_zone_cunt;j++)
        {
            cJSON_AddItemToArray(cjZone_mac, cJSON_CreateString(groups[i].m_vecSecMac.at(j).Data()));
        }
        cJSON_AddItemToObject(group, "zones", cjZone_mac);

        cJSON_AddItemToArray(cjgroups, group);
    }
    cJSON_AddItemToObject(root, "groups", cjgroups);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);
    
    return strData;
}



// 3.5 下发歌曲列表信息
string  CWebProtocol::CmdForwardPlaylistInfo(
                                         string  strUpdateTime,      // 文件更新时间
                                         int nPlaylistCount,
                                         PlaylistInfo* playlists,   // 分组信息
                                         int       nResult) // 返回结果
{
    
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command",    cJSON_CreateString("get_playlist_info"));
    cJSON_AddItemToObject(root, "update_at", cJSON_CreateString(strUpdateTime.data()));
    cJSON *cjplaylists = cJSON_CreateArray();

    for(int i = 0; i < nPlaylistCount ; i++)
    {
        cJSON *playlist = cJSON_CreateObject();
        cJSON_AddItemToObject(playlist, "list_id",       cJSON_CreateString( playlists[i].m_strID.Data()));
        cJSON_AddItemToObject(playlist, "list_name",     cJSON_CreateString(playlists[i].m_szPlaylistName.C_Str()));
        cJSON_AddItemToObject(playlist, "list_account", cJSON_CreateString( playlists[i].m_userAccount.C_Str()));
        cJSON *cjsongs = cJSON_CreateArray();
        for(int j=0;j<playlists[i].m_songs_cunt;j++)
        {
            cJSON *song = cJSON_CreateObject();
            cJSON_AddItemToObject(song, "duration", cJSON_CreateNumber( playlists[i].m_vecSongInfo.at(j).duration));
            cJSON_AddItemToObject(song, "song_name", cJSON_CreateString( playlists[i].m_vecSongInfo.at(j).songName.C_Str()));
            cJSON_AddItemToObject(song, "song_path_name", cJSON_CreateString( playlists[i].m_vecSongInfo.at(j).songPathName.C_Str()));
            cJSON_AddItemToObject(song, "song_bitrate", cJSON_CreateNumber( playlists[i].m_vecSongInfo.at(j).songBitRate));
            cJSON_AddItemToObject(song, "song_md5", cJSON_CreateString( playlists[i].m_vecSongInfo.at(j).songMd5.C_Str()));
            cJSON_AddItemToObject(song, "song_account", cJSON_CreateString( playlists[i].m_vecSongInfo.at(j).songAccount.C_Str()));
            cJSON_AddItemToObject(song, "alive", cJSON_CreateBool( playlists[i].m_vecSongInfo.at(j).alive));
            cJSON_AddItemToArray(cjsongs, song);
        }
        cJSON_AddItemToObject(playlist, "songs", cjsongs);

        cJSON_AddItemToArray(cjplaylists, playlist);
    }
    
    cJSON_AddItemToObject(root, "playlists", cjplaylists);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));
 
    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);
    return strData;
}


// 获取客户端状态(DSP9312)
string CWebProtocol::CmdGetClientStatus(void)
{
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_client_status"));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

string CWebProtocol::CmdSetClientInfo(string strName, string strMonitoMac)
{
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_client_info"));
    cJSON_AddItemToObject(root, "client_name", cJSON_CreateString(strName.c_str()));
    cJSON_AddItemToObject(root, "monitor_mac", cJSON_CreateString(strMonitoMac.c_str()));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 分发网络信息
string CWebProtocol::CmdForwardNetInfo(bool  bLink,          // 是否连接到网络
                                       double ispeed,     // 上传速度
                                       double ospeed)   // 下载速度
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_network_info"));
    cJSON_AddItemToObject(root, "islink", cJSON_CreateBool((int)bLink));
    cJSON_AddItemToObject(root, "upload_speed", cJSON_CreateNumber(ispeed));
    cJSON_AddItemToObject(root, "download_speed", cJSON_CreateNumber(ospeed));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 分发网络质量
string CWebProtocol::CmdForwardNetQuality(string strMac,           // 设备Mac地址
                                          string strName,          // 设备名称
                                          int nNetType,            // 网络类型
                                          double dBandWidth,       // 带宽
                                          double dDelayed,         // 延时
                                          double dLossRate,        // 丢包率
                                          int    nResult)          // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_network_quality"));
    cJSON_AddItemToObject(root, "type", cJSON_CreateNumber(nNetType));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "device_name", cJSON_CreateString(strName.data()));
    cJSON_AddItemToObject(root, "bandwidth", cJSON_CreateNumber(dBandWidth));
    cJSON_AddItemToObject(root, "delay", cJSON_CreateNumber(dDelayed));
    cJSON_AddItemToObject(root, "loss_rate", cJSON_CreateNumber(dLossRate));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}




// 4.1 回复设置选中分区
string  CWebProtocol::CmdResponseSetSelectedZone(int nID,           // 回传选中分区的id
                                                 int nPage,      // 收到第几个包
                                                 int nResult)    // 返回结果
{
    cJSON *root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_select_zones"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));
    cJSON_AddItemToObject(root, "selected_id", cJSON_CreateNumber(nID));
    cJSON_AddItemToObject(root, "page", cJSON_CreateNumber(nPage));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 4.2 播放节目源 回复
string  CWebProtocol::CmdResponsePlaySource(int nResult)      //  返回结果
{
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("play_source"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 12.1 服务器请求广播寻呼 回复
string  CWebProtocol::CmdResponseWebPaging(int nResult,int nEvnet)      //  返回结果
{
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("request_paging"));
    cJSON_AddItemToObject(root, "event", cJSON_CreateNumber(nEvnet));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 12.2 服务器请求广播寻呼音频流 回复
string  CWebProtocol::CmdResponseWebPagingStream(int nResult)      //  返回结果
{
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("send_paging_stream"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 应答 控制指定寻呼台向指定分区设备发起广播寻呼
string  CWebProtocol::CmdResponseSetBroadcastPaging(int nResult)      //  返回结果
{
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_broadcast_paging"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 停止多媒体网关 回复
string  CWebProtocol::CmdResponseStopStreaming(int nResult)         //  返回结果
{
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("stop_net_audio"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 查询/设置监听音箱 回复
string CWebProtocol::CmdResponseSetMonitorSpeaker(int nSetMode,              // 1：设置   0：查询
                                                string device_mac ,          // 监听音箱mac
                                                int nResult)                //  返回结果
{
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_audio_monitor_speaker"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSetMode));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(device_mac.c_str()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 监听节目源 回复
string CWebProtocol::CmdResponseMonitorSource(int nOper,       // 开启或关闭监听 0x00:关闭 0x01:开启
                                              int nResult)    //  返回结果
{
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("audio_monitor_source"));
    cJSON_AddItemToObject(root, "oper", cJSON_CreateNumber(nOper));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 播放音频采集器音源 回复
string CWebProtocol::CmdResponsePlayAudioCollector(string strMac,  // 分区设备mac地址
                                                   int nSelID,      // 选择分区ID
                                                   int nResult)     //  返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("play_audiocollect_source"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "selected_id", cJSON_CreateNumber(nSelID));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 播放AUX音源
string CWebProtocol::CmdResponsePlayAUX(int nSelID,      // 选择分区ID
                                        int nResult)     //  返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("play_aux_source"));
    cJSON_AddItemToObject(root, "selected_id", cJSON_CreateNumber(nSelID));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 4.3 调节音量 回复
string  CWebProtocol::CmdResponseSetVolume(int nResult)   //  返回结果
{
    cJSON *root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_volume"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 4.3 设置设备子音量 回复
string  CWebProtocol::CmdResponseSetSubVolume(int nSetMode,              // 1：设置   0：查询,
                                              int nSubVolume,            // 子音量值
                                              int nAuxVolume,            // 本地音量值
                                              int nResult)   //  返回结果
{
    cJSON *root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_sub_volume"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSetMode));
    cJSON_AddItemToObject(root, "sub_volume", cJSON_CreateNumber(nSubVolume));
    cJSON_AddItemToObject(root, "aux_volume", cJSON_CreateNumber(nAuxVolume));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 设置对讲终端基础参数 回复
string  CWebProtocol::CmdResponseSetIntercomBasic(int nSetMode,              // 1：设置   0：查询,
                                                 string strMac,              //设备MAC
                                              CALLDEVICECONFIG *callBasicConfig,	//对讲基础配置
                                              int nResult)   //  返回结果
{
    cJSON *root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_intercom_basic"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSetMode));
    cJSON_AddItemToObject(root, "device_mac",   cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "key1_mac", cJSON_CreateString(callBasicConfig->Key1_mac));
    cJSON_AddItemToObject(root, "key2_mac", cJSON_CreateString(callBasicConfig->Key2_mac));
    cJSON_AddItemToObject(root, "mic_vol", cJSON_CreateNumber(callBasicConfig->micVol));
    cJSON_AddItemToObject(root, "far_out_vol", cJSON_CreateNumber(callBasicConfig->farOutVol));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 设置终端触发参数 回复
string  CWebProtocol::CmdResponseTriggerBasic(int nSetMode,              // 1：设置   0：查询,
                                              string strMac,              //设备MAC
                                              char trigger_switch,
                                            char trigger_mode,
                                            char *trigger_songName,
                                            int volume,
                                              int nResult)   //  返回结果
{
    cJSON *root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_trigger"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSetMode));
    cJSON_AddItemToObject(root, "device_mac",   cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "trigger_switch", cJSON_CreateNumber(trigger_switch));
    cJSON_AddItemToObject(root, "trigger_mode", cJSON_CreateNumber(trigger_mode));
    cJSON_AddItemToObject(root, "trigger_song_path_name", cJSON_CreateString(trigger_songName));
    cJSON_AddItemToObject(root, "trigger_volume", cJSON_CreateNumber(volume));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 启动/停止手动告警 回复
string  CWebProtocol::CmdResponseStartManualAlarm(int nSetMode,string songName,
                                          int nResult)   //  返回结果
{
    cJSON *root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("start_manual_alarm"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSetMode));
    cJSON_AddItemToObject(root, "song_name", cJSON_CreateString(songName.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 4.4 设置分区为空闲状态 回复
string  CWebProtocol::CmdResponseSetIdle(int nResult)       //  返回结果
{
    cJSON *root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_idle_status"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 4.5 设置钟声 回复
string  CWebProtocol::CmdResponseSetBell(int nResult)       //  返回结果
{
    cJSON *root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_chime"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 4.6 播放钟声 回复
string  CWebProtocol::CmdResponsePlayBell(int nResult)        //  返回结果
{
    cJSON *root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("play_chime"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


string CWebProtocol::CmdResponseSetMutes(int nSetStatus, int nResult)
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_mute"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSetStatus));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 查询/设置播放模式 回复
string CWebProtocol::CmdResponseChangePlayMode(int nSetMode,              // 1：设置   0：查询
                                               int nPlayMode,            // 播放模式,此字段查询时有效
                                               int nResult)                  // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("play_mode"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSetMode));
    cJSON_AddItemToObject(root, "play_mode", cJSON_CreateNumber(nPlayMode));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}



string CWebProtocol::CmdForwardMonitorInfo(
                                           int nMonitroCount,
                                           LPCMonitorInfo *MonitorInfos,
                                           int       nResult)         // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_monitor_info"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));
    //cJSON_AddItemToObject(root, "monitor_count", cJSON_CreateNumber(nMonitroCount));
    cJSON* jsMonitors = cJSON_CreateArray();

    for(int  i=0; i<nMonitroCount; i++)
    {
        cJSON* jsMonInfo = cJSON_CreateObject();
        cJSON_AddItemToObject(jsMonInfo, "monitor_mac", cJSON_CreateString(MonitorInfos[i]->GetMac()));
        cJSON_AddItemToObject(jsMonInfo, "account", cJSON_CreateString(MonitorInfos[i]->GetAccount()));
        cJSON_AddItemToObject(jsMonInfo, "password", cJSON_CreateString(MonitorInfos[i]->GetPassword()));
        cJSON_AddItemToObject(jsMonInfo, "ip_addr", cJSON_CreateString(MonitorInfos[i]->GetIP()));
        cJSON_AddItemToObject(jsMonInfo, "port", cJSON_CreateNumber(MonitorInfos[i]->GetPort()));
        cJSON_AddItemToObject(jsMonInfo, "url", cJSON_CreateString(MonitorInfos[i]->GetRTSP()));
        cJSON_AddItemToObject(jsMonInfo, "name", cJSON_CreateString(MonitorInfos[i]->GetName()));
        cJSON_AddItemToObject(jsMonInfo, "is_auto_add", cJSON_CreateBool(MonitorInfos[i]->GetIsAutoAdd()));
        cJSON_AddItemToObject(jsMonInfo, "monitor_status", cJSON_CreateNumber((int)MonitorInfos[i]->GetMonStatus()));
        /*cJSON* jsEvents = cJSON_CreateArray();

                int nEventCount = MonitorInfos[i]->GetEventConfigCount();

                for(int j=0; j<nEventCount; j++)
                {
                        LPCEventConfig pEventConfig = MonitorInfos[i]->GetEventConfig(j);
                        int nSecCount = pEventConfig->GetZonesCount();
                        cJSON* jsEventInfo = cJSON_CreateObject();
                        cJSON_AddItemToObject(jsEventInfo, "event_id", cJSON_CreateNumber(MonitorInfos[i]->GetEventType(j)));
                        cJSON_AddItemToObject(jsEventInfo, "sound", cJSON_CreateString(pEventConfig->GetSoundPath().data()));
                        cJSON_AddItemToObject(jsEventInfo, "zone_count", cJSON_CreateNumber(nSecCount));
                        cJSON* jsSecArray = cJSON_CreateArray();

                        for(int k=0; k<nSecCount; k++)
                        {
                                cJSON_AddItemToArray(jsSecArray, cJSON_CreateString(pEventConfig->GetZone(k).data()));
                        }
                        cJSON_AddItemToObject(jsEventInfo, "zones_mac", jsSecArray);
                        cJSON_AddItemToArray(jsEvents, jsEventInfo);
                }

                cJSON_AddItemToObject(jsMonInfo, "events", jsEvents);*/
        cJSON_AddItemToArray(jsMonitors, jsMonInfo);
    }

    cJSON_AddItemToObject(root, "monitors", jsMonitors);

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 服务器向客户端发送监控的触发事件
string CWebProtocol::CmdForwardMonitorEvent(string strMac,        // 通道号
                                            int    nEventType,   // 事件类型
                                            string strDateTime,     // 日期时间
                                            int    nDirection)      // 入侵方向
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command",          cJSON_CreateString("monitor_trigger"));
    cJSON_AddItemToObject(root, "device_mac",   cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "trigger",          cJSON_CreateNumber(nEventType));
    cJSON_AddItemToObject(root, "date_time",        cJSON_CreateString(strDateTime.data()));
    cJSON_AddItemToObject(root, "invade_direction", cJSON_CreateNumber(nDirection));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 设置监控设备信息
string CWebProtocol::CmdResponseSetMonitorInfo(string strMac,                   // Mac地址
                                               string strAccount,               // 账号
                                               string strPassword,              // 密码
                                               int nResult)
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_monitor_info"));
    cJSON_AddItemToObject(root, "monitor_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "account", cJSON_CreateString(strAccount.data()));
    cJSON_AddItemToObject(root, "password", cJSON_CreateString(strPassword.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 添加自定义监控设备
string  CWebProtocol::CmdResponseAddCustomMonitor(string strMonitorName,       // 账号
                                            string strMonitorUrl,        // 密码
                                            int nResult)   // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("add_custom_monitor"));
    cJSON_AddItemToObject(root, "name", cJSON_CreateString(strMonitorName.data()));
    cJSON_AddItemToObject(root, "url", cJSON_CreateString(strMonitorUrl.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

//删除监控设备信息
string CWebProtocol::CmdResponseDeleteMonitorInfo(string strMac,                   // Mac地址
                                               int nResult)
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("delete_monitor"));
    cJSON_AddItemToObject(root, "monitor_mac", cJSON_CreateString(strMac.data()));;
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 设置监控设备事件
string CWebProtocol::CmdResponseSetMonitorEvent(string strComID,                   // 命令标识
                                                string strMac,    // Mac地址
                                                int nResult)        // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_monitor_event"));
    cJSON_AddItemToObject(root, "monitor_mac", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置监控设备事件触发分区
string CWebProtocol::CmdResponseSetEventZone(string strComID,             // 命令标识
                                             string strMac,                  // Mac地址
                                             int  nEventID,                  // 事件类型
                                             int  nPage,                       // 指定页数
                                             int  nResult)  // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_monitor_event_zone"));
    cJSON_AddItemToObject(root, "command_uuid", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "monitor_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "event_id", cJSON_CreateNumber(nEventID));
    cJSON_AddItemToObject(root, "page", cJSON_CreateNumber(nPage));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 指定监控RTSP流(web)
string CWebProtocol::CmdResponseMonitorRtsp(
                                            int  nResult)     // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("monitor_rtsp"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 开启/关闭视频监控
string CWebProtocol::CmdResponseEnableMonitor(int  nSet,        // 0:查询 1:设置
                                            bool isEnableMonitor, //是否启用视频监控
                                            int  nResult)     // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("monitor_switch"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    cJSON_AddItemToObject(root, "enable_monitor", cJSON_CreateBool(isEnableMonitor));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 开启/关闭考试模式
string CWebProtocol::CmdResponseEnableExaminationMode(int  nSet,        // 0:查询 1:设置
                                            bool examination_mode, //是否启用考试模式
                                            int  nResult)     // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("examination_mode_switch"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    cJSON_AddItemToObject(root, "examination_mode", cJSON_CreateBool(examination_mode));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 开启/关闭云控制
string CWebProtocol::CmdResponseEnableCloudControl(int  nSet,        // 0:查询 1:设置
                                        bool cloudControl, //是否启用云控制
                                        bool isConnected,  //连接状态(1已连接，0未连接)
                                        int  nResult)     // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("cloud_control_switch"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    cJSON_AddItemToObject(root, "cloud_control", cJSON_CreateBool(cloudControl));
    if(!cloudControl)
    {
        isConnected = false;
    }
    cJSON_AddItemToObject(root, "connected", cJSON_CreateBool(isConnected));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 开启/关闭通话录音
string CWebProtocol::CmdResponseEnableCallRecord(int  nSet,        // 0:查询 1:设置
                                            bool isEnableCallRecord, //是否启用通话录音
                                            int  nResult)     // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("call_record_switch"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    cJSON_AddItemToObject(root, "enable_call_record", cJSON_CreateBool(isEnableCallRecord));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置系统主题
string CWebProtocol::CmdSetSystemTheme(string key,string value,
                                    int  nResult)     // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_system_theme"));
    cJSON_AddItemToObject(root, "key", cJSON_CreateString(key.data()));
    cJSON_AddItemToObject(root, "value", cJSON_CreateString(value.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置AISP配置文件
string CWebProtocol::CmdSetAispConfig(int  nSet,        // 0:查询 1:设置
                                      string value,
                                      int  nResult)     // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_aisp_config"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    cJSON_AddItemToObject(root, "value", cJSON_CreateString(value.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 客户端向服务器请求SIP设备信息 回复
string CWebProtocol::CmdResponseSipInfo(int             nPageCount,         // 总页数
                                        int             nPage,                 // 指定页数
                                        int             nSipCount,          // 此页包含的设备数，最多20个分区
                                        SipInfo*   pSipInfo,              // sip设备列表
                                        int             nResult)               //  返回结果
{
    cJSON*  root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("sip_device_info"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));
    cJSON_AddItemToObject(root, "page_count", cJSON_CreateNumber(nPageCount));
    cJSON_AddItemToObject(root, "page", cJSON_CreateNumber(nPage));
    cJSON_AddItemToObject(root, "device_count", cJSON_CreateNumber(nSipCount));
    cJSON*   cjSipInfo = cJSON_CreateArray();

    for(int i = 0; i < nSipCount; i++)
    {
        cJSON* sipInfo = cJSON_CreateObject();
        cJSON_AddItemToObject(sipInfo, "exten", cJSON_CreateString(pSipInfo[i].m_strExten.data()));
        cJSON_AddItemToObject(sipInfo, "status", cJSON_CreateNumber(pSipInfo[i].m_nStatus));
        cJSON_AddItemToArray(cjSipInfo, sipInfo);
    }
    cJSON_AddItemToObject(root,  "sip_devices", cjSipInfo);

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 客户端向服务器发起对讲请求 回复
string CWebProtocol::CmdResponseTalkback(int nResult)        //  返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("start_talkback"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 客户端向服务器发起广播/寻呼请求  回复
string CWebProtocol::CmdResponsePaging(int    nType,           // 寻呼类型
                                       int     nRtpType,      // RTP类型
                                       int     nPage,           // 指定页数
                                       int     nResult)      //  返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("start_paging"));
    cJSON_AddItemToObject(root, "rtp", cJSON_CreateNumber(nRtpType));
    cJSON_AddItemToObject(root, "type", cJSON_CreateNumber(nType));
    cJSON_AddItemToObject(root, "page", cJSON_CreateNumber(nPage));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 客户端向服务器发起广播(寻呼)请求(PCM)
string CWebProtocol::CmdResponsePcmPaging(string strExten,      // 发起寻呼的号码
                                          int     nRtpType,   // RTP类型
                                          int     nResult)     //  返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("start_paging_pcm"));
    cJSON_AddItemToObject(root, "exten", cJSON_CreateString(strExten.data()));
    cJSON_AddItemToObject(root, "rtp", cJSON_CreateNumber(nRtpType));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 客户端向服务器发起监听请求
string CWebProtocol::CmdResponseListen(int nResult)
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root , "command", cJSON_CreateString("start_monitor"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 客户端向服务器发送挂断请求
string CWebProtocol::CmdResponseHangup(string strExten, int nResult)
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("hangup"));
    cJSON_AddItemToObject(root, "exten", cJSON_CreateString(strExten.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 客户端向服务器发送挂断请求(PCM) 回复
string CWebProtocol::CmdResponsePCMHangup(string strExten, int nSelID, int nResult)
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("hangup_pcm"));
    cJSON_AddItemToObject(root, "exten", cJSON_CreateString(strExten.data()));
    cJSON_AddItemToObject(root, "selected_id", cJSON_CreateNumber(nSelID));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}
// 获取媒体端口 回复
string CWebProtocol::CmdResponseGetMediaPort(string strID,
                                           string udpPagingPort,
                                           int nResult)           //  返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_server_media_port"));
    cJSON_AddItemToObject(root, "uuid", cJSON_CreateString(strID.data()));
    cJSON_AddItemToObject(root, "udp_paging_port", cJSON_CreateString(udpPagingPort.data()));
    
    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 用户登录 回复
string CWebProtocol::CmdResponseUserLogin(string strID,
                                       UserParm_t& uParm,
                                       int appType,             // 软件类型
                                       string strRegisterMachineCode,   // 机器码
                                       string strCloudControlMachineCode,   // 云控制机器码
                                       string strIP,            // 本地IP地址
                                       string strVersion,      // 服务器版本
                                       string strMac,          // 服务器MAC地址
                                       bool isEnbaleMonitor,   // 是否启用视频监控
                                       bool examination_mode,  // 是否启用考试模式
                                       string expiration_date,  // 软件授权到期时间
                                       int nResult)            //  返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("user_login"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));
    cJSON_AddItemToObject(root, "uuid", cJSON_CreateString(strID.data()));

    cJSON_AddItemToObject(root, "type", cJSON_CreateNumber(appType));
    if(expiration_date.size()>0)
    {
        cJSON_AddItemToObject(root, "expiration_date", cJSON_CreateString(expiration_date.data()));
    }
    cJSON_AddItemToObject(root, "machine_code", cJSON_CreateString(strRegisterMachineCode.data()));
    cJSON_AddItemToObject(root, "ip", cJSON_CreateString(strIP.data()));
    cJSON_AddItemToObject(root, "mac", cJSON_CreateString(strMac.data()));
    if (IS_SYSTEM_IN_CLOUD)
        strVersion += "S";
    #if IS_BACKUP_SERVER
        strVersion += "_BackupServer";
    #endif

    #if STREAM_ALLWAYS_USED_TCP
    strVersion+="_TCP";
    #endif

    cJSON_AddItemToObject(root, "version", cJSON_CreateString(strVersion.data()));
	#if defined(Q_OS_LINUX)
    cJSON_AddItemToObject(root, "system_type", cJSON_CreateNumber(1));   //系统类型：1-Linux 2-Windows
    #else
	cJSON_AddItemToObject(root, "system_type", cJSON_CreateNumber(2));   //系统类型：1-Linux 2-Windows
	#endif
    cJSON_AddItemToObject(root, "cloud_server", cJSON_CreateBool(IS_SYSTEM_IN_CLOUD));   //是否运行于云服务器
    cJSON_AddItemToObject(root, "enable_monitor", cJSON_CreateBool(isEnbaleMonitor));   //是否启用监控
    cJSON_AddItemToObject(root, "examination_mode", cJSON_CreateBool(examination_mode));   //是否启用考试模式(启用后禁止非管理员登录)

    #if APP_IS_LZY_COMMERCE_VERSION
    cJSON_AddItemToObject(root, "tts_basic_valid", cJSON_CreateNumber(g_Global.b_tts_basic_Authorized));   //TTS功能授权状态：0=未授权且未试用，1=已授权，2=未授权但正在试用
    #else
    cJSON_AddItemToObject(root, "cloud_control_valid", cJSON_CreateBool(g_Global.b_cloudControl_Authorized));   //云控制功能是否授权
    cJSON_AddItemToObject(root, "cloud_control_machine_code", cJSON_CreateString(strCloudControlMachineCode.data()));
    #endif

#if 0   //20230711 去除此部分，改用单独指令
#if defined(Q_OS_LINUX) //微信小程序暂只支持linux系统上传歌曲（因为windows的cgi暂未处理临时文件夹存储，待后续再处理）
    #if APP_IS_LZY_LIMIT_STORAGE
    cJSON_AddItemToObject(root, "storage_capacity", cJSON_CreateNumber(uParm.nStorageCapacity));    //存储空间（如果是0,代表此版本没有限制存储空间，不需要显示相关信息；如果没有此字段，代表不支持微信小程序上传）
    #else
    cJSON_AddItemToObject(root, "storage_capacity", cJSON_CreateNumber(0));    //存储空间（如果是0,代表此版本没有限制存储空间，不需要显示相关信息；如果没有此字段，代表不支持微信小程序上传）
    #endif
#endif
#endif

    if(nResult == EC_SUCCESS)
    {
        cJSON* jsUserParm = cJSON_CreateObject();

        cJSON_AddItemToObject(jsUserParm, "account", cJSON_CreateString(uParm.strAccount.data()));
        cJSON_AddItemToObject(jsUserParm, "zone_count", cJSON_CreateNumber(uParm.nZoneCount));
        /*--------******** 权限字段加入分区mac数组---------*/
        cJSON* jsZoneMacArray =  cJSON_CreateArray();
        for(int i=0; i<uParm.nZoneCount; i++)
        {
            cJSON_AddItemToArray(jsZoneMacArray, cJSON_CreateString(uParm.vec_SecMac[i].data()));
        }
        cJSON_AddItemToObject(jsUserParm, "zones_mac", jsZoneMacArray);
        /*------------------------------------------------------*/
        cJSON_AddItemToObject(jsUserParm, "authority", cJSON_CreateNumber(uParm.nAuthority));
        cJSON_AddItemToObject(root, "user_authority", jsUserParm);

        cJSON_AddItemToObject(root, "user_type", cJSON_CreateNumber(uParm.nUserType));
        cJSON_AddItemToObject(root, "user_account", cJSON_CreateString(uParm.strAccount.data()));
        cJSON_AddItemToObject(root, "user_name", cJSON_CreateString(uParm.strUserName.data()));

        LPCUserInfo   lpUser = g_Global.m_Users.GetUserByAccount(uParm.strAccount);
        if(lpUser)
        {
            cJSON_AddItemToObject(root, "user_uuid", cJSON_CreateString(lpUser->GetUserId()));
            //将服务器mac转换成base64编码
            string base64_mac = base64_encode((unsigned char*)strMac.data(),strMac.length());
            // 去掉=号
            base64_mac.erase(std::remove(base64_mac.begin(), base64_mac.end(), '='), base64_mac.end());
            cJSON_AddItemToObject(root, "server_uuid", cJSON_CreateString(base64_mac.data()));
        }
    }
    else
    {
        cJSON_AddItemToObject(root, "user_authority", cJSON_CreateString(""));
    }

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 添加用户 回复
string CWebProtocol::CmdResponseAddUser(string strComID,     // 命令ID
                                        string strAccount,   // 用户名
                                        int nResult)             //  返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("add_user"));
    cJSON_AddItemToObject(root, "command_uuid", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "account", cJSON_CreateString(strAccount.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 移除用户 回复
string CWebProtocol::CmdResponseRemoveUser(string strAccount, int nResult)     //  返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("remove_user"));
    //cJSON_AddItemToObject(root, "remove_sub_user", cJSON_CreateBool(bDelSubuser));
    cJSON_AddItemToObject(root, "account", cJSON_CreateString(strAccount.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 编辑用户（修改用户权限）
string CWebProtocol::CmdResponseModifyUser(string strComID,     // 命令ID
                                           string strAccount,  // 用户名
                                           int nResult)             //  返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("edit_user"));
    cJSON_AddItemToObject(root, "command_uuid", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "dest_account", cJSON_CreateString(strAccount.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 查询用户信息 回复
string CWebProtocol::CmdResponseGetUserInfo(userParm &destUserUParm,       // 待查询用户信息
                                             vector<userParm> &vecSubUsers, // 待查询用户的子用户信息
                                             int        nResult)            // 返回结果

{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_user_info"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));
    cJSON_AddItemToObject(root, "dest_account", cJSON_CreateString(destUserUParm.strAccount.data()));
    cJSON_AddItemToObject(root, "dest_password", cJSON_CreateString(destUserUParm.strPassword.data()));
    cJSON_AddItemToObject(root, "user_name", cJSON_CreateString(destUserUParm.strUserName.data()));
    if(destUserUParm.strUserName == "")
    {
        cJSON_AddItemToObject(root, "user_name", cJSON_CreateString(destUserUParm.strAccount.data()));
    }
    else
    {
        cJSON_AddItemToObject(root, "user_name", cJSON_CreateString(destUserUParm.strUserName.data()));
    }
    cJSON_AddItemToObject(root, "user_type", cJSON_CreateNumber(destUserUParm.nUserType));
    #if APP_IS_LZY_LIMIT_STORAGE   //龙之音V1版本才显示存储容量字段
    cJSON_AddItemToObject(root, "storage_capacity", cJSON_CreateNumber(destUserUParm.nStorageCapacity));
    #endif
    cJSON_AddItemToObject(root, "sub_user_count", cJSON_CreateNumber(destUserUParm.nSubUserCount));
    cJSON* jsAuthority = cJSON_CreateObject();

    cJSON_AddItemToObject(jsAuthority, "account", cJSON_CreateString(destUserUParm.strAccount.data()));
    cJSON_AddItemToObject(jsAuthority, "zone_count", cJSON_CreateNumber(destUserUParm.nZoneCount));
    //cJSON_AddItemToObject(jsAuthority, "group_count", cJSON_CreateNumber(nGroupCount));
    cJSON_AddItemToObject(jsAuthority, "authority", cJSON_CreateNumber(destUserUParm.nAuthority));
    cJSON_AddItemToObject(root, "user_authority", jsAuthority);

    /*--------******** 账户信息权限字段加入分区mac数组---------*/
    cJSON* jsZoneMacArray =  cJSON_CreateArray();
    for(int i=0; i<destUserUParm.nZoneCount; i++)
    {
        cJSON_AddItemToArray(jsZoneMacArray, cJSON_CreateString(destUserUParm.vec_SecMac[i].data()));
    }
    cJSON_AddItemToObject(jsAuthority, "zones_mac", jsZoneMacArray);
    /*------------------------------------------------------*/
    cJSON* jsSubIDArrays =  cJSON_CreateArray();

    for(int i=0; i<destUserUParm.nSubUserCount; i++)
    {
        //cJSON_AddItemToArray(jsSubIDArrays, cJSON_CreateString(strSubUsers[i].data()));
        cJSON* jsSubUser = cJSON_CreateObject();
        cJSON_AddItemToObject(jsSubUser, "account", cJSON_CreateString(vecSubUsers[i].strAccount.data()));
        cJSON_AddItemToObject(jsSubUser, "password", cJSON_CreateString(vecSubUsers[i].strPassword.data()));
        if(vecSubUsers[i].strUserName == "")
        {
            cJSON_AddItemToObject(jsSubUser, "user_name", cJSON_CreateString(vecSubUsers[i].strAccount.data()));
        }
        else
        {
            cJSON_AddItemToObject(jsSubUser, "user_name", cJSON_CreateString(vecSubUsers[i].strUserName.data()));
        }
        cJSON_AddItemToObject(jsSubUser, "user_type", cJSON_CreateNumber(vecSubUsers[i].nUserType));
        #if APP_IS_LZY_LIMIT_STORAGE   //龙之音V1版本才显示存储容量字段
        cJSON_AddItemToObject(jsSubUser, "storage_capacity", cJSON_CreateNumber(vecSubUsers[i].nStorageCapacity));
        #endif
        cJSON_AddItemToObject(jsSubUser, "sub_user_count", cJSON_CreateNumber(vecSubUsers[i].nSubUserCount));
#if 1
        cJSON* jsAuthority = cJSON_CreateObject();

        cJSON_AddItemToObject(jsAuthority, "account", cJSON_CreateString(vecSubUsers[i].strAccount.data()));
        cJSON_AddItemToObject(jsAuthority, "zone_count", cJSON_CreateNumber(vecSubUsers[i].nZoneCount));
        //cJSON_AddItemToObject(jsAuthority, "group_count", cJSON_CreateNumber(nGroupCount));
        cJSON_AddItemToObject(jsAuthority, "authority", cJSON_CreateNumber(vecSubUsers[i].nAuthority));

        /*--------******** 账户信息权限字段加入分区mac数组---------*/
        cJSON* jsZoneMacArray =  cJSON_CreateArray();
        for(int j=0; j<vecSubUsers[i].nZoneCount; j++)
        {
            cJSON_AddItemToArray(jsZoneMacArray, cJSON_CreateString(vecSubUsers[i].vec_SecMac[j].data()));
        }
        cJSON_AddItemToObject(jsAuthority, "zones_mac", jsZoneMacArray);
        /*------------------------------------------------------*/

        cJSON_AddItemToObject(jsSubUser, "user_authority", jsAuthority);
#endif
        //cJSON_AddItemToObject(jsSubUser, "authority", cJSON_CreateNumber(vecSubUsers[i].nAuthority));

        cJSON_AddItemToArray(jsSubIDArrays, jsSubUser);
    }
    cJSON_AddItemToObject(root, "sub_users", jsSubIDArrays);

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 修改用户密码 回复
string CWebProtocol::CmdResponseModifyPsd(int nResult)      // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("modify_user_password"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


#if APP_IS_AISP_TZY_ENCRYPTION
string  CWebProtocol::CmdResponseSetUserCertificate(int nSet,            // 0:查询 1:设置
                                                string strAccount,  // 用户名
                                                string strCertificate,  //用户证书
                                                int nResult)
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_user_certificate"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    cJSON_AddItemToObject(root, "user_certificate", cJSON_CreateString(strCertificate.data()));
    cJSON_AddItemToObject(root, "dest_account", cJSON_CreateString(strAccount.data()));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);
    return strData;
}
#endif

// 获取用户的分区
string CWebProtocol::CmdResponseGetUserZone(string    strAccount,      // 查询用户账户
                                            int         nPageCount,    // 总页数
                                            int         nPage,              // 指定页数
                                            int         nZoneCount,   // 此页包含的分区数
                                            string*  strMacs,          // 分区Mac数组
                                            int          nResult)          // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_user_zone"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));
    cJSON_AddItemToObject(root, "dest_account", cJSON_CreateString(strAccount.data()));
    //cJSON_AddItemToObject(root, "page_count", cJSON_CreateNumber(nPageCount));
    //cJSON_AddItemToObject(root, "page", cJSON_CreateNumber(nPage));
    
    cJSON_AddItemToObject(root, "zone_count", cJSON_CreateNumber(nZoneCount));
    cJSON* jsZoneMacArray =  cJSON_CreateArray();

    for(int i=0; i<nZoneCount; i++)
    {
        cJSON_AddItemToArray(jsZoneMacArray, cJSON_CreateString(strMacs[i].data()));
    }
    cJSON_AddItemToObject(root, "zones_mac", jsZoneMacArray);

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 获取用户的分组
string CWebProtocol::CmdResponseGetUserGroup(string    strAccount,      // 查询用户账户
                                             int         nPageCount,     // 总页数
                                             int         nPage,                // 指定页数
                                             int         nGroupCount,   // 此页包含的分组数
                                             string*  strGroupIDs,     // 分组ID数组
                                             int          nResult)            // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_user_group"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));
    cJSON_AddItemToObject(root, "dest_account", cJSON_CreateString(strAccount.data()));
    cJSON_AddItemToObject(root, "page_count", cJSON_CreateNumber(nPageCount));
    cJSON_AddItemToObject(root, "page", cJSON_CreateNumber(nPage));
    cJSON_AddItemToObject(root, "group_count", cJSON_CreateNumber(nGroupCount));
    cJSON* jsGroupIDArray = cJSON_CreateArray();

    for(int i=0; i<nGroupCount; i++)
    {
        cJSON_AddItemToArray(jsGroupIDArray, cJSON_CreateString(strGroupIDs[i].data()));
    }
    cJSON_AddItemToObject(root, "group_id", jsGroupIDArray);

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置(子)用户的分区
string CWebProtocol::CmdResponseSetUserZone(string    strComID,        //  返回结果
                                            string    strAccount,      // 查询用户账户
                                            int          nPage,          // 指定页数
                                            int          nResult)       // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_user_zone"));
    cJSON_AddItemToObject(root, "command_uuid", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "dest_account", cJSON_CreateString(strAccount.data()));
    cJSON_AddItemToObject(root, "page", cJSON_CreateNumber(nPage));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


string CWebProtocol::CmdResponseSetUserGroup(string    strComID,        //  返回结果
                                             string    strAccount,      // 查询用户账户
                                             int          nPage,              // 指定页数
                                             int          nResult)           // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_user_group"));
    cJSON_AddItemToObject(root, "command_id", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "dest_account", cJSON_CreateString(strAccount.data()));
    cJSON_AddItemToObject(root, "page", cJSON_CreateNumber(nPage));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


string CWebProtocol::CmdResponseSetUserAuthority(
                                             string    strAccount,      // 账户
                                             int          authority,        // 权限
                                             int          nResult)      // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_user_authority"));
    cJSON_AddItemToObject(root, "dest_account", cJSON_CreateString(strAccount.data()));
    cJSON_AddItemToObject(root, "authority", cJSON_CreateNumber(authority));

    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


string CWebProtocol::CmdResponseGetUserStorageCapacity(
                                string    strAccount,              // 账户
                                double    nstorageCapacity,        // 总存储空间
                                double    nUsedSpace,              // 已用存储空间
                                double    nRemainingSpace,         // 剩余存储空间
                                int       nResult)      // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_user_storage_capacity"));
    cJSON_AddItemToObject(root, "dest_account", cJSON_CreateString(strAccount.data()));
    cJSON_AddItemToObject(root, "storage_capacity", cJSON_CreateNumber(nstorageCapacity));
    cJSON_AddItemToObject(root, "used_space", cJSON_CreateNumber(nUsedSpace));
    cJSON_AddItemToObject(root, "remaining_space", cJSON_CreateNumber(nRemainingSpace));

    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

string CWebProtocol::CmdResponseSetUserStorageCapacity(
                                             string    strAccount,      // 账户
                                             int          nstorageCapacity,        // 存储空间
                                             int          nResult)      // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_user_storage_capacity"));
    cJSON_AddItemToObject(root, "dest_account", cJSON_CreateString(strAccount.data()));
    cJSON_AddItemToObject(root, "storage_capacity", cJSON_CreateNumber(nstorageCapacity));

    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 主机请求用户重新登录
string CWebProtocol::CmdResquestUserRelogin(int nStatus)     // 状态码
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("request_user_relogin"));
    cJSON_AddItemToObject(root, "status_code", cJSON_CreateNumber(nStatus));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 添加分组 回复
string CWebProtocol::CmdResponseAddGroup(string    strGroupID,           // 分组ID
                                         string    strGroupName,     // 分组名称
                                         int   nZoneCount,                // 分区数量
                                         int   nResult )                       // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("add_group"));
    cJSON_AddItemToObject(root, "group_id", cJSON_CreateString(strGroupID.data()));
    cJSON_AddItemToObject(root, "group_name", cJSON_CreateString(strGroupName.data()));
    cJSON_AddItemToObject(root, "zone_count", cJSON_CreateNumber(nZoneCount));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 编辑分组 回复
string CWebProtocol::CmdResponseModifyGroup(string    strComID,                   // 命令ID
                                            string   strGroupID,                // 分组ID
                                            string    strGroupName,         // 分组名称
                                            int   nZoneCount,                    // 分区数量
                                            int   nResult)    // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("edit_group"));
    cJSON_AddItemToObject(root, "command_id", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "group_id", cJSON_CreateString(strGroupID.data()));
    cJSON_AddItemToObject(root, "group_name", cJSON_CreateString(strGroupName.data()));
    cJSON_AddItemToObject(root, "zone_count", cJSON_CreateNumber(nZoneCount));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 移除分组 回复
string CWebProtocol::CmdResponseRemoveGroup(string   strGroupID,   // 分组ID
                                            int   nResult )              // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("remove_group"));
    cJSON_AddItemToObject(root, "group_id", cJSON_CreateString(strGroupID.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置分组的分区
string CWebProtocol::CmdResponseSetGroupSec(string   strComID,                     // 命令ID
                                            string   strGroupID,     // 分组ID
                                            int        nPage,              // 指定页数
                                            int   nResult)                 // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_group_zone"));
    cJSON_AddItemToObject(root, "command_id", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "group_id", cJSON_CreateString(strGroupID.data()));
    cJSON_AddItemToObject(root, "page", cJSON_CreateNumber(nPage));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 添加歌曲列表
string CWebProtocol::CmdResponseAddSongList(string strListID,          // 列表ID
                                            string strListName,   // 列表名称
                                            int nResult)                // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("add_song_list"));
    cJSON_AddItemToObject(root, "list_id", cJSON_CreateString(strListID.data()));
    cJSON_AddItemToObject(root, "list_name", cJSON_CreateString(strListName.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 重命名歌曲列表
string CWebProtocol::CmdResponseRenameSongList(string strListID,          // 列表ID
                                               string strListName,   // 列表名称
                                               int nResult)                // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("rename_song_list"));
    cJSON_AddItemToObject(root, "list_id", cJSON_CreateString(strListID.data()));
    cJSON_AddItemToObject(root, "list_name", cJSON_CreateString(strListName.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 移除歌曲列表 回复
string CWebProtocol::CmdResponseRemoveSongList(string  strListID,         // 列表ID
                                               int  nResult)               // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("remove_song_list"));
    cJSON_AddItemToObject(root, "list_id", cJSON_CreateString(strListID.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 获取服务器上传路径下的所有歌曲 回复
string CWebProtocol::CmdResponseGetServerAllSong(int nPageCount,             // 总页数
                                                 int  nPage,                      // 指定页数
                                                 int  nSongCount,           //  此页包含的歌曲名称数量，最多100个歌曲名称
                                                 string *strSongs,           //  歌曲名称数组
                                                 int  nResult)                   //  返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_server_upload_songs"));
    cJSON_AddItemToObject(root, "page_count", cJSON_CreateNumber(nPageCount));
    cJSON_AddItemToObject(root, "page", cJSON_CreateNumber(nPage));
    cJSON_AddItemToObject(root, "song_count", cJSON_CreateNumber(nSongCount));
    cJSON* jsSongArrays = cJSON_CreateArray();

    for(int i=0; i<nSongCount; i++)
    {
        cJSON_AddItemToArray(jsSongArrays, cJSON_CreateString(strSongs[i].data()));
    }
    cJSON_AddItemToObject(root, "song_names", jsSongArrays);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 请求上传歌曲到服务器 回复
string CWebProtocol::CmdResponseUploadSongToServer(
                                                   string strServerIP,        // Web服务器ip
                                                   string strServerPort,    // Web服务器端口
                                                   string strUploadPath,  // 上传路径
                                                   int      nResult)              // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("request_upload_song_to_server"));
    cJSON_AddItemToObject(root, "server_ip", cJSON_CreateString(strServerIP.data()));
    cJSON_AddItemToObject(root, "server_port", cJSON_CreateString(strServerPort.data()));
    cJSON_AddItemToObject(root, "upload_path", cJSON_CreateString(strUploadPath.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 添加歌曲到列表 回复
string CWebProtocol::CmdResponseAddSongToList(string strListID,           // 列表ID
                                              int song_count,
                                              vector<string> song_names,
                                              int      nResult)            // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("add_song_to_list"));
    cJSON_AddItemToObject(root, "list_id", cJSON_CreateString(strListID.data()));
    cJSON_AddItemToObject(root, "song_count", cJSON_CreateNumber(song_count));

    cJSON* jsSongNames = cJSON_CreateArray();
    for(int i=0; i<song_count; i++)
    {
        cJSON_AddItemToArray(jsSongNames, cJSON_CreateString(song_names[i].data()));
    }
    cJSON_AddItemToObject(root, "song_names", jsSongNames);

    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 停止上传歌曲
string CWebProtocol::CmdResponseStopUploadSong(int  nResult)            // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("stop_upload_song"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 从列表移除指定歌曲
string CWebProtocol::CmdResponseRemoveSongFromList(string strListID,           // 列表ID
                                                    int song_count,
                                                    vector<string> song_names,
                                                   int      nResult)            // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("remove_song_from_list"));
    cJSON_AddItemToObject(root, "list_id", cJSON_CreateString(strListID.data()));
    cJSON_AddItemToObject(root, "song_count", cJSON_CreateNumber(song_count));

    cJSON* jsSongNames = cJSON_CreateArray();
    for(int i=0; i<song_count; i++)
    {
        cJSON_AddItemToArray(jsSongNames, cJSON_CreateString(song_names[i].data()));
    }
    cJSON_AddItemToObject(root, "song_names", jsSongNames);
    
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 管理员审核指定歌曲
string  CWebProtocol::CmdResponseAuditLocalSong(int song_count,
                                                    vector<string> song_names,
                                                      int      nResult)           // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("audit_local_song"));;
    cJSON_AddItemToObject(root, "song_count", cJSON_CreateNumber(song_count));

    cJSON* jsSongNames = cJSON_CreateArray();
    for(int i=0; i<song_count; i++)
    {
        cJSON_AddItemToArray(jsSongNames, cJSON_CreateString(song_names[i].data()));
    }
    cJSON_AddItemToObject(root, "song_names", jsSongNames);
    
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 移除指定列表中所有的歌曲
string CWebProtocol::CmdResponseRemoveAllSong(string strListID,           // 列表ID
                                              int      nResult)            // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("remove_all_songs_from_list"));
    cJSON_AddItemToObject(root, "list_id", cJSON_CreateString(strListID.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 文字转语音
string CWebProtocol::CmdText2Speech(string strFileName,   // 合成文件名称
                                    int nResult)    // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("text_to_speech"));
    cJSON_AddItemToObject(root, "file_name", cJSON_CreateString(strFileName.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 控制指定分区播放指定歌曲/TTS
string CWebProtocol::CmdResponsePlaySpecifiedSource(int nResult)    // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("play_specified_source"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 歌曲列表排序
string CWebProtocol::CmdResponseSortPlayList(string strListID,           // 列表ID
                                              int      nResult)            // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("sort_song_list"));
    cJSON_AddItemToObject(root, "list_id", cJSON_CreateString(strListID.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}



/////////////////////////////////////////////////////////////////////////////////////
/*-----------------------          定时管理               ------------------------*/

// 添加定时点信息
string CWebProtocol::CmdResponseAddTimePonit(int nTimeSchemeID,      // 方案序号
                                             string strComID,        // 命令ID
                                             CExTimePoint &tp,    // 定时点信息
                                             int  nResult)                // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("add_time_point"));
    cJSON_AddItemToObject(root, "command_uuid", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON* jsTP = cJSON_CreateObject();

    cJSON_AddItemToObject(jsTP, "id", cJSON_CreateNumber(tp.m_nID));
    cJSON_AddItemToObject(jsTP, "name", cJSON_CreateString(tp.m_strName.data()));
    cJSON_AddItemToObject(root, "time_point", jsTP);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 编辑定时点信息
string CWebProtocol::CmdResponseModifyTimePoint(int nTimeSchemeID,     // 方案序号
                                                string strComID,        // 命令ID
                                                CExTimePoint &tp,   // 定时点信息
                                                int nResult)                // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("edit_time_point"));
    cJSON_AddItemToObject(root, "command_uuid", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON* jsTP = cJSON_CreateObject();
    cJSON_AddItemToObject(jsTP, "id", cJSON_CreateNumber(tp.m_nID));
    cJSON_AddItemToObject(jsTP, "name", cJSON_CreateString(tp.m_strName.data()));

    cJSON_AddItemToObject(root, "time_point", jsTP);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 移除定时点信息
string CWebProtocol::CmdResponseRemoveTimePoint(int nTimeSchemeID,      // 方案序号
                                                CExTimePoint &tp,    // 定时点信息
                                                int nResult)                 // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("remove_time_point"));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON* jsTP = cJSON_CreateObject();
    cJSON_AddItemToObject(jsTP, "id", cJSON_CreateNumber(tp.m_nID));
    cJSON_AddItemToObject(jsTP, "name", cJSON_CreateString(tp.m_strName.data()));

    cJSON_AddItemToObject(root, "time_point", jsTP);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置定时点分区
string CWebProtocol::CmdResponseSetTimeZone(int nTimeSchemeID,              // 方案序号
                                            string strComID,        // 命令ID
                                            CExTimePoint &tp,          // 定时点信息
                                            int nPage,     // 指定页数
                                            int nResult)  // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_time_zone"));
    cJSON_AddItemToObject(root, "command_uuid", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON* jsTP = cJSON_CreateObject();
    cJSON_AddItemToObject(jsTP, "id", cJSON_CreateNumber(tp.m_nID));
    cJSON_AddItemToObject(jsTP, "name", cJSON_CreateString(tp.m_strName.data()));

    cJSON_AddItemToObject(root, "time_point", jsTP);
    cJSON_AddItemToObject(root, "page", cJSON_CreateNumber(nPage));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置定时点分组
string CWebProtocol::CmdResponseSetTimeGroup(int nTimeSchemeID,      // 方案序号
                                             string strComID,        // 命令ID
                                             CExTimePoint &tp,   // 定时点信息
                                             int nPage,                   // 指定页数
                                             int nResult)                // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_time_group"));
    cJSON_AddItemToObject(root, "command_uuid", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON* jsTP = cJSON_CreateObject();
    cJSON_AddItemToObject(jsTP, "id", cJSON_CreateNumber(tp.m_nID));
    cJSON_AddItemToObject(jsTP, "name", cJSON_CreateString(tp.m_strName.data()));

    cJSON_AddItemToObject(root, "time_point", jsTP);
    cJSON_AddItemToObject(root, "page", cJSON_CreateNumber(nPage));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置定时点歌曲
string CWebProtocol::CmdResponseSetTimeSong(int nTimeSchemeID,  // 方案序号
                                            string strComID,        // 命令ID
                                            CExTimePoint &tp,   // 定时点信息
                                            int nPage,                   // 指定页数
                                            int nResult)                // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_time_song"));
    cJSON_AddItemToObject(root, "command_uuid", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON* jsTP = cJSON_CreateObject();
    cJSON_AddItemToObject(jsTP, "id", cJSON_CreateNumber(tp.m_nID));
    cJSON_AddItemToObject(jsTP, "name", cJSON_CreateString(tp.m_strName.data()));
    cJSON_AddItemToObject(root, "time_point", jsTP);

    cJSON_AddItemToObject(root, "page", cJSON_CreateNumber(nPage));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}



// 设置定时点时序器
string CWebProtocol::CmdResponseSetTimeSequencePower(int nTimeSchemeID,  // 方案序号
                                            string strComID,        // 命令ID
                                            CExTimePoint &tp,   // 定时点信息
                                            int nPage,                   // 指定页数
                                            int nResult)                // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_time_sequence_power"));
    cJSON_AddItemToObject(root, "command_uuid", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON* jsTP = cJSON_CreateObject();
    cJSON_AddItemToObject(jsTP, "id", cJSON_CreateNumber(tp.m_nID));
    cJSON_AddItemToObject(jsTP, "name", cJSON_CreateString(tp.m_strName.data()));
    cJSON_AddItemToObject(root, "time_point", jsTP);

    cJSON_AddItemToObject(root, "page", cJSON_CreateNumber(nPage));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 设置定时点音频采集器
string CWebProtocol::CmdResponseSetTimeAudioCollector(int nTimeSchemeID,  // 方案序号
                                            string strComID,        // 命令ID
                                            CExTimePoint &tp,   // 定时点信息
                                            int nPage,                   // 指定页数
                                            int nResult)                // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_time_audio_collector"));
    cJSON_AddItemToObject(root, "command_uuid", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON* jsTP = cJSON_CreateObject();
    cJSON_AddItemToObject(jsTP, "id", cJSON_CreateNumber(tp.m_nID));
    cJSON_AddItemToObject(jsTP, "name", cJSON_CreateString(tp.m_strName.data()));
    cJSON_AddItemToObject(root, "time_point", jsTP);

    cJSON_AddItemToObject(root, "page", cJSON_CreateNumber(nPage));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 禁用/启用指定定时点
string CWebProtocol::CmdResponseTimePointValid(int nTimeSchemeID,       // 方案序号
                                               CExTimePoint &tp,          // 定时点信息
                                               bool isValid,                     // 是否有效
                                               string conflictTimingName,       //冲突的定时点名称（当nResult=EC_TC_TP_CONFLICT时有效）
                                               int nResult )                     // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("time_point_valid"));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON* jsTP = cJSON_CreateObject();
    cJSON_AddItemToObject(jsTP, "id", cJSON_CreateNumber(tp.m_nID));
    cJSON_AddItemToObject(jsTP, "name", cJSON_CreateString(tp.m_strName.data()));
    cJSON_AddItemToObject(root, "time_point", jsTP);

    cJSON_AddItemToObject(root, "isvalid", cJSON_CreateBool(isValid));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));
    //如果是定时点冲突，那么加入与之冲突的定时点名称
    if(nResult == EC_TC_TP_CONFLICT)
    {
        cJSON_AddItemToObject(root, "conflict_timing", cJSON_CreateString(conflictTimingName.data()));
    }

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 单次取消/恢复指定定时点
string CWebProtocol::CmdResponseTimePointSingleCancel(int nTimeSchemeID,       // 方案序号
                                               CExTimePoint &tp,          // 定时点信息
                                               bool isCancel,                     // 是否有效
                                               int nResult )                     // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("single_cancel_time_point"));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON* jsTP = cJSON_CreateObject();
    cJSON_AddItemToObject(jsTP, "id", cJSON_CreateNumber(tp.m_nID));
    cJSON_AddItemToObject(jsTP, "name", cJSON_CreateString(tp.m_strName.data()));
    cJSON_AddItemToObject(root, "time_point", jsTP);

    cJSON_AddItemToObject(root, "iscancel", cJSON_CreateBool(isCancel));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 复制指定定时点
string CWebProtocol::CmdCopyTimePoint(int nTimeSchemeID,       // 方案序号
                                      CExTimePoint &tp,          // 定时点信息
                                      int nResult)              // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("copy_time_point"));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON* jsTP = cJSON_CreateObject();
    cJSON_AddItemToObject(jsTP, "id", cJSON_CreateNumber(tp.m_nID));
    cJSON_AddItemToObject(jsTP, "name", cJSON_CreateString(tp.m_strName.data()));
    cJSON_AddItemToObject(root, "time_point", jsTP);

    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 对定时点进行排序
string CWebProtocol::CmdSortTimePoint(   int  nTimeSchemeID,    // 方案序号
                                        int  nSortItem,         // 1:定时点名称 2：状态  3：播放周期  4：开始时间
                                        bool bAscendOrder,      // true: 升序  false：降序
                                        int  nResult)           // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("sort_time_point"));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON_AddItemToObject(root, "sort_item", cJSON_CreateNumber(nSortItem));
    cJSON_AddItemToObject(root, "ascending_order", cJSON_CreateBool(bAscendOrder));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 添加定时方案
string CWebProtocol::CmdResponseAddTimeScheme(int nTimeSchemeID,           // 方案序号
                                              string  strSchemeName,     // 定时方案名称
                                              int   nResult)                        // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("add_time_scheme"));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON_AddItemToObject(root, "time_scheme_name", cJSON_CreateString(strSchemeName.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 编辑定时方案
string CWebProtocol::CmdResponseModifyTimeScheme(int nTimeSchemeID,           // 方案序号
                                                 string  strSchemeName,     // 定时方案名称
                                                 int  nResult)           // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("edit_time_scheme"));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON_AddItemToObject(root, "time_scheme_name", cJSON_CreateString(strSchemeName.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 移除定时方案
string CWebProtocol::CmdResponseRemoveTimeScheme(int nTimeSchemeID,           // 方案序号
                                                 string  strSchemeName,     // 定时方案名称
                                                 int  nResult)           // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("remove_time_scheme"));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON_AddItemToObject(root, "time_scheme_name", cJSON_CreateString(strSchemeName.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置当前定时方案
string CWebProtocol::CmdResponseSetCurTimeScheme(int nTimeSchemeID,    // 方案序号
                                                 int  nResult)        // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_current_time_scheme"));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 禁用/恢复指定定时方案
string CWebProtocol::CmdResponseTimeSchemeValid(int nTimeSchemeID,    // 方案序号
                                                bool  bValid,                  // 是否有效
                                                int  nResult)       // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("time_scheme_valid"));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON_AddItemToObject(root, "isvalid", cJSON_CreateBool(bValid));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 复制指定定时方案
string CWebProtocol::CmdCopyTimeScheme(int nTimeSchemeID,    // 方案序号
                                       int  nResult)      // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("copy_time_scheme"));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nTimeSchemeID));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 请求上传定时方案
string CWebProtocol::CmdUploadTimeScheme(string strSchName,                   // 定时方案名称
                                         string strServerIP,                     // Web服务器ip
                                         string strServerPort,                 // Web服务器端口
                                         string strUploadPath,               // 上传路径
                                         int      nResult)             // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("request_upload_time_scheme"));
    cJSON_AddItemToObject(root, "path_name", cJSON_CreateString(strSchName.data()));
    cJSON_AddItemToObject(root, "server_ip", cJSON_CreateString(strServerIP.data()));
    cJSON_AddItemToObject(root, "server_port", cJSON_CreateString(strServerPort.data()));
    cJSON_AddItemToObject(root, "upload_path", cJSON_CreateString(strUploadPath.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 导入定时方案
string CWebProtocol::CmdImportTimeScheme(string strPathName,               // 定时方案相对路径名称
                                         int   nResult)  // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("import_time_scheme"));
    cJSON_AddItemToObject(root, "path_name", cJSON_CreateString(strPathName.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 导出定时方案
string CWebProtocol::CmdExportTimeScheme(int nSchID,                                // 定时方案ID
                                         string strServerIP,                     // Web服务器ip
                                         string strServerPort,                 // Web服务器端口
                                         string strPathName,                 // 文件路径名称
                                         int      nResult)     // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("export_time_scheme"));
    cJSON_AddItemToObject(root, "time_scheme_id", cJSON_CreateNumber(nSchID));
    cJSON_AddItemToObject(root, "server_ip", cJSON_CreateString(strServerIP.data()));
    cJSON_AddItemToObject(root, "server_port", cJSON_CreateString(strServerPort.data()));
    cJSON_AddItemToObject(root, "path_name", cJSON_CreateString(strPathName.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置设备信息
string CWebProtocol::CmdResponseSetDeviceInfo(
                                              int set_type,
                                              int      nDevModel,    // 设备型号
                                              string strMac,       // 设备Mac
                                              string strName,    // 设备名称
                                              string strMonMac, // 监控设备mac
                                              int       nResult)     // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_device_info"));
    cJSON_AddItemToObject(root, "set_type", cJSON_CreateNumber(set_type));
    cJSON_AddItemToObject(root, "device_model", cJSON_CreateNumber(nDevModel));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "device_name", cJSON_CreateString(strName.data()));
    cJSON_AddItemToObject(root, "monitor_mac", cJSON_CreateString(strMonMac.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 查询/设置网络解码播放器EMC状态
string CWebProtocol::CmdResponseEMCStatus(string strMac,            // 设备Mac
                                          RestType rt,               //  请求类型
                                          bool    bEmcValid,    // Emc是否有效，查询时有效
                                          int       nResult)         // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("device_emc"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber((int)rt));
    cJSON_AddItemToObject(root, "emc", cJSON_CreateBool(bEmcValid));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 查询/设置EQ音效
string CWebProtocol::CmdResponseEQMode(string strMac,           // 设备Mac
                                       RestType rt,             // 请求类型
                                       int      nEQMode,       //  EQ模式
                                       unsigned char nEQGain[10],   //EQ GAIN
                                       int      nResult)           // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("device_eq_mode"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(rt));
    cJSON_AddItemToObject(root, "eq_mode", cJSON_CreateNumber(nEQMode));
    char gain[10]={0};
    for(int i=0;i<10;i++)
    {
        sprintf(gain,"gain%d",i+1);
        cJSON_AddItemToObject(root, gain, cJSON_CreateNumber(nEQGain[i]));
    }
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 查询/设置蓝牙参数
string CWebProtocol::CmdResponseBlueToothInfo(string strMac,           // 设备Mac
                                       RestType rt,             // 请求类型
                                       string	btName,					        // 蓝牙名称,为NULL时为查询
                                       unsigned char btencryption,             // 蓝牙加密方式
                                       string   btPin,			                // 蓝牙密码
                                       int      nResult)           // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_bluetooth"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(rt));
    cJSON_AddItemToObject(root, "bluetooth_name", cJSON_CreateString(btName.data()));
    cJSON_AddItemToObject(root, "bluetooth_encryption", cJSON_CreateNumber(btencryption));
    cJSON_AddItemToObject(root, "bluetooth_pin", cJSON_CreateString(btPin.data()));

    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}



// 查询/设置混音
string CWebProtocol::CmdResponseMixMode(string strMac,            // 设备Mac
                                        RestType rt,                // 请求类型
                                        bool   bSetAllZone ,   // 是否设置到全部分区
                                        int      nChannel,        // 声道：1 单声道   0 双声道(默认)
                                        int      nMixMode ,     //  混音模式
                                        int      nAUX,               //  AUX值
                                        int      nDAC,               //  DAC值
                                        int      nResult)           //  返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("device_mix_mode"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(rt));
    cJSON_AddItemToObject(root, "set_all_zone_mix", cJSON_CreateBool(bSetAllZone));
    cJSON_AddItemToObject(root, "sound_channel", cJSON_CreateNumber(nChannel));
    cJSON_AddItemToObject(root, "mix_mode", cJSON_CreateNumber(nMixMode));
    cJSON_AddItemToObject(root, "aux", cJSON_CreateNumber(nAUX));
    cJSON_AddItemToObject(root, "dac", cJSON_CreateNumber(nDAC));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置控制模式(程控/手控)
string CWebProtocol::CmdResponseSetControlMode(int nSelectID,            //     选择分区ID
                                               ControlMode cm,     // 控制模式
                                               int nResult)               // 返回结果
{
    cJSON*  root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_control_mode"));
    cJSON_AddItemToObject(root, "control_mode", cJSON_CreateNumber(cm));
    cJSON_AddItemToObject(root, "selected_id", cJSON_CreateNumber(nSelectID));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 暂停播放节目源
string CWebProtocol::CmdResponPauseSource(int nSelID,       // 选择分区ID
                                          int nResult)      // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("pause_source"));
    cJSON_AddItemToObject(root, "selected_id", cJSON_CreateNumber(nSelID));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


    // 查询设置信息发布参数
string CWebProtocol::CmdResponseInformationPublishInfo(string strMac,      // 设备mac地址
                                                    int  nSet,                    // 0:查询 1:设置
                                                    bool bEnableDisplay,          // 是否启用显示
                                                    string strText,                 //输出文本
                                                    int nEffects,                 // 特效
                                                    int nMoveSpeed,               // 移动速度
                                                    int nStayTime,                // 停留时间 
                                                    int nResult)    // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_information_publish"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));

    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "enable_display", cJSON_CreateBool(bEnableDisplay));
    //将其base64编码后再上传
    string base64_encode_text = base64_encode(strText);
    cJSON_AddItemToObject(root, "text", cJSON_CreateString(base64_encode_text.data()));
    //printf("strText=%s,base64_encode_text=%s\n",strText.data(),base64_encode_text.data());
    cJSON_AddItemToObject(root, "effects", cJSON_CreateNumber(nEffects));
    cJSON_AddItemToObject(root, "speed", cJSON_CreateNumber(nMoveSpeed));
    cJSON_AddItemToObject(root, "stay_time", cJSON_CreateNumber(nStayTime));;
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 查询sip登录信息
string CWebProtocol::CmdResponseSipLoginInfo(string strMac,                    // 设备mac地址
                                             int  nSet,        // 0:查询 1:设置
                                             bool bEnableSip,                  // 是否启用SIP
                                             int outputVolume,               //输出音量
                                             string strSipAccount,        // sip登录账号
                                             string strSipPassword,     // sip登录密码
                                             string strServerIP,            // sip服务器地址
                                             int strServerPort,        // sip服务器端口
                                             int nServerProtocol,         //传输协议
                                             int nSipStatus,                  // sip会话状态
                                             int nResult)      // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_sip_info"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "sip_enable", cJSON_CreateBool(bEnableSip));
    cJSON_AddItemToObject(root, "sip_output_vol", cJSON_CreateNumber(outputVolume));
    cJSON_AddItemToObject(root, "sip_account", cJSON_CreateString(strSipAccount.data()));
    cJSON_AddItemToObject(root, "sip_password", cJSON_CreateString(strSipPassword.data()));
    cJSON_AddItemToObject(root, "sip_server_ip", cJSON_CreateString(strServerIP.data()));
    cJSON_AddItemToObject(root, "sip_server_port", cJSON_CreateNumber(strServerPort));
    cJSON_AddItemToObject(root, "sip_server_protocol", cJSON_CreateNumber(nServerProtocol));
    cJSON_AddItemToObject(root, "sip_status", cJSON_CreateNumber(nSipStatus));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// sip账号登录
string CWebProtocol::CmdResponseSipLogin(string strMac,      // 设备mac地址
                                         int nResult)     // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_sip_login"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 服务器通知客户端设备文件需要同步
string CWebProtocol::CmdNotifyNeedSyncFile(string strMac,    // 设备Mac地址
                                           FileType ft,         //   文件类型
                                           string strFileDateTime,    // 文件时间
                                           DeviceModel model)       // 设备类型
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("device_need_sync"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "device_model", cJSON_CreateNumber((int)model));
    cJSON_AddItemToObject(root, "file_type", cJSON_CreateNumber(ft));
    cJSON_AddItemToObject(root, "file_time", cJSON_CreateString(strFileDateTime.data()));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 客户端请求服务器需要更新设备文件 回复
string CWebProtocol::CmdResponseUpdateDeviceFile(string strMac,             //  设备Mac地址
                                                 DeviceModel model,  //  设备类型
                                                 FileType ft,                  //   文件类型
                                                 int       nSyncStatus,   //   同步状态
                                                 int       nResult,          //    返回结果
                                                 bool    IsSongStatus,  //   是否是歌曲状态，如果不是则去掉下面的同步歌曲参数
                                                 int       nSongCount,   //   一共需要同步的歌曲数目
                                                 int       nSyncCount,   //   已完成同步的歌曲数目
                                                 int       nPercent)        //   正在同步歌曲的百分比
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("sync_device_file"));
    cJSON_AddItemToObject(root, "device_model", cJSON_CreateNumber(model));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "file_type", cJSON_CreateNumber(ft));
    cJSON_AddItemToObject(root, "update_status", cJSON_CreateNumber(nSyncStatus));
    if(IsSongStatus)
    {
        cJSON_AddItemToObject(root, "song_count", cJSON_CreateNumber(nSongCount));
        cJSON_AddItemToObject(root, "finished_count", cJSON_CreateNumber(nSyncCount));
        cJSON_AddItemToObject(root, "percentage", cJSON_CreateNumber(nPercent));
    }
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 客户端请求中止同步文件
string CWebProtocol::CmdResponseStopSyncFile(string strMac,      // 设备Mac地址
                                             bool   bAllzone,    // 是否中止全部分区
                                             int    nResult)     // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("stop_sync_device_file"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "set_all_zone", cJSON_CreateBool(bAllzone));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 获取服务器上传路径下的所有固件 回复
string CWebProtocol::CmdResponseGetServerAllFirmware(int nPageCount,                    // 总页数
                                                     int nPage,                              // 指定页数
                                                     int nFirmwareCount,            // 此页包含的固件名称数量，最多100个固件名称
                                                     string* strFirmwareNames, // 固件名称数组
                                                     int nResult)                            // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_server_upload_firmwares"));
    cJSON_AddItemToObject(root, "page_count", cJSON_CreateNumber(nPageCount));
    cJSON_AddItemToObject(root, "page", cJSON_CreateNumber(nPage));
    cJSON_AddItemToObject(root, "firmware_count", cJSON_CreateNumber(nFirmwareCount));
    cJSON* jsFwName = cJSON_CreateArray();

    for(int i=0; i<nFirmwareCount; i++)
    {
        cJSON_AddItemToArray(jsFwName, cJSON_CreateString(strFirmwareNames[i].data()));
    }
    cJSON_AddItemToObject(root, "firmware_names", jsFwName);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 请求上传固件到服务器 回复
string CWebProtocol::CmdResponseUploadFirmwareToServer(string strFirmwareName,   // 固件名称
                                                       string strServerIP,               // 服务器ip
                                                       string strServerPort,           // 服务器端口
                                                       string strUploadPath,         // 上传路径
                                                       int nResult)                          // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("request_upload_firmware_to_server"));
    cJSON_AddItemToObject(root, "firmware_name", cJSON_CreateString(strFirmwareName.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));
    cJSON_AddItemToObject(root, "server_ip", cJSON_CreateString(strServerIP.data()));
    cJSON_AddItemToObject(root, "server_port", cJSON_CreateString(strServerPort.data()));
    cJSON_AddItemToObject(root, "upload_path", cJSON_CreateString(strUploadPath.data()));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设备固件升级 回复
string CWebProtocol::CmdResponseUpgradeDevice(string  strMac,        // 设备Mac地址
                                              int        nStatus,       // 更新状态
                                              int        nPercent,   // 升级进度百分比
                                              int nResult)   // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("upgrade_device"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    //cJSON_AddItemToObject(root, "firmware_name", cJSON_CreateString(strFirmwareName.data()));
    cJSON_AddItemToObject(root, "upgrade_status", cJSON_CreateNumber(nStatus));
    cJSON_AddItemToObject(root, "percentage", cJSON_CreateNumber(nPercent));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 终端日志列表获取 回复
string CWebProtocol::CmdResponseDevLogList(CSection &device,
                                           int nResult)   // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_sec_log_list"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(device.GetMac()));
    cJSON_AddItemToObject(root, "log_count", cJSON_CreateNumber(device.m_LogFiles.GetLogFileCount()));
    cJSON* LogList = cJSON_CreateArray();

    for(int i=0; i<device.m_LogFiles.GetLogFileCount(); i++)
    {
        cJSON* LogInfo = cJSON_CreateObject();
        CLogFile& logFile = device.m_LogFiles.GetLogFile(i);
        cJSON_AddItemToObject(LogInfo, "file_name", cJSON_CreateString(logFile.m_szName));
        cJSON_AddItemToObject(LogInfo, "file_size", cJSON_CreateNumber(logFile.m_nSize));
        cJSON_AddItemToArray(LogList, LogInfo);
    }
    cJSON_AddItemToObject(root, "log_list", LogList);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 终端日志下载进度
string CWebProtocol::CmdDevLogDown(string strMac,            // 设备Mac地址
                                   string  strFileName,   // 下载文件大小
                                   int    nCompletedSize,  // 已下载文件大小
                                   int    nTotalSize,          // 总文件大小
                                   int    nResult)  // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("download_sec_log"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "file_name", cJSON_CreateString(strFileName.data()));
    cJSON_AddItemToObject(root, "completed_size", cJSON_CreateNumber(nCompletedSize));
    cJSON_AddItemToObject(root, "total_size", cJSON_CreateNumber(nTotalSize));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 查询/设置网络解码播放器电源输出模式(9131/9138)
string CWebProtocol::CmdPowOutputMode(string strMac,        // 设备Mac地址
                                      int nSet,             // 0:查询 1:设置
                                      int nPowerMode,       // 电源模式
                                      int nTimeOut,         // 超时时间
                                      int nResult)          // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("power_output_mode"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    cJSON_AddItemToObject(root, "power_mode", cJSON_CreateNumber(nPowerMode));
    cJSON_AddItemToObject(root, "time_out", cJSON_CreateNumber(nTimeOut));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 查询/设置网络解码分区器输出状态（9138）
string CWebProtocol::CmdSplitterStatus(string strMac,       // 设备Mac地址
                                       int nSet,            // 0:查询 1:设置
                                       int nChannelStatus,  // 通道状态
                                       int nResult)         // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("splitter_status"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    cJSON_AddItemToObject(root, "channel_status", cJSON_CreateNumber(nChannelStatus));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

string CWebProtocol::CmdMicStatus( string strMac,                 // 设备Mac地址
                                        int nSet,                   // 0:查询 1:设置
                                        int volume,                 // 音量
                                        int power,                  // 功率
                                        int channel,                // 通道
                                        int nResult)                // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("network_terminal_mic_status"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    cJSON_AddItemToObject(root, "volume", cJSON_CreateNumber(volume));
    cJSON_AddItemToObject(root, "power", cJSON_CreateNumber(power));
    cJSON_AddItemToObject(root, "channel", cJSON_CreateNumber(channel));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 查询/设置GPS时区
string CWebProtocol::CmdResponseGPSTimeZone(string strMac,           // 设备Mac
                                            RestType rt,             // 请求类型
                                            int      nTimeZone,      // 时区设置
                                            bool     bDst,           // 是否夏时令
                                            int      nResult)        // 返回结果
{
    cJSON*  root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("gps_time_zone"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(rt));
    cJSON_AddItemToObject(root, "time_zone", cJSON_CreateNumber(nTimeZone));
    cJSON_AddItemToObject(root, "isdst", cJSON_CreateBool(bDst));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置消防采集器
string CWebProtocol::CmdSetFireCollector(string strComID,         // 命令ID
                                         string strMac,           // 设备mac
                                         string strName,          // 设备名称
                                         int    nChannelCount,    // 消防通道数量
                                         int  nResult)            // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_fire_device"));
    cJSON_AddItemToObject(root, "command_uuid", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "device_name", cJSON_CreateString(strName.data()));
    cJSON_AddItemToObject(root, "channel_count", cJSON_CreateNumber(nChannelCount));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置消防通道
string CWebProtocol::CmdSetFireChannel(string strComID,           // 命令ID
                                       string strMac,             // 设备mac
                                       int isSetAllchannel,       // 设置所有通道
                                       CExtraChannel channel,     // 通道数据
                                       int nResult)               // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_fire_channel"));
    cJSON_AddItemToObject(root, "command_uuid", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "all_channel", cJSON_CreateNumber(isSetAllchannel));

    cJSON* jsFireChannel = cJSON_CreateObject();
    cJSON_AddItemToObject(jsFireChannel, "channel_id", cJSON_CreateNumber(channel.m_nID));
    cJSON_AddItemToObject(root, "fire_chhannel", jsFireChannel);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置电源时序器
string CWebProtocol::CmdSetSequencePower(string strMac,             // 设备mac
                                       int nResult)               // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_sequence_power_device"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}
#if SUPPORT_REMOTE_CONTROLER
// 设置远程遥控器任务
string CWebProtocol::CmdSetRemoteControlerTask(string strMac,             // 设备mac
                                        int nEvent,               //事件
                                       int nResult)               // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_remote_controler_task"));
    cJSON_AddItemToObject(root, "event", cJSON_CreateNumber(nEvent));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置远程遥控器按键
string CWebProtocol::CmdSetRemoteControlerKey(string strMac,             // 设备mac
                                       int nResult)               // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_remote_controler_key"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}
#endif

#if SUPPORT_AUDIO_MIXER
// 设置音频混音器参数
string CWebProtocol::CmdSetAudioMixerParm(string strMac,             // 设备mac
                                        int nSet,                   // 0:查询 1:设置
                                        int nMasterSwitch,          // 混音主开关（0关闭 1开启）
                                        int nPriority,              // 优先级
                                        int nTriggerType,           // 触发类型
                                        int nTriggerSensitivity,    // 触发灵敏度
                                        int nVolumeFadeLevel,       // 音量淡化级别
                                        int nZoneVolume,            // 分区音量
                                        vector<CMyString> &vecSecMacs,  //分区数组
                                        int nResult)               // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_audio_mixer_parm"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    cJSON_AddItemToObject(root, "master_switch", cJSON_CreateNumber(nMasterSwitch));
    cJSON_AddItemToObject(root, "priority", cJSON_CreateNumber(nPriority));
    cJSON_AddItemToObject(root, "trigger_type", cJSON_CreateNumber(nTriggerType));
    cJSON_AddItemToObject(root, "trigger_sensitivity", cJSON_CreateNumber(nTriggerSensitivity));
    cJSON_AddItemToObject(root, "volume_fade_level", cJSON_CreateNumber(nVolumeFadeLevel));
    cJSON_AddItemToObject(root, "zone_volume", cJSON_CreateNumber(nZoneVolume));

    cJSON* jsZoneMacArray =  cJSON_CreateArray();

    for(int i=0; i<vecSecMacs.size(); i++)
    {
        cJSON_AddItemToArray(jsZoneMacArray, cJSON_CreateString(vecSecMacs[i].Data()));
    }
    cJSON_AddItemToObject(root, "zone_macs", jsZoneMacArray);

    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}
#endif

#if SUPPORT_PHONE_GATEWAY
// 设置电话网关参数
string CWebProtocol::CmdSetPhoneGatewayParm(string strMac,             // 设备mac
                                        int nSet,                   // 0:查询 1:设置
                                        int nMasterSwitch,          // 混音主开关（0关闭 1开启）
                                        int nZoneVolume,            // 分区音量
                                        vector<CMyString> &vecSecMacs,  //分区数组
                                        string strTelWhitelist,     //电话白名单
                                        int nResult)      // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_phone_gateway_parm"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    cJSON_AddItemToObject(root, "master_switch", cJSON_CreateNumber(nMasterSwitch));
    cJSON_AddItemToObject(root, "zone_volume", cJSON_CreateNumber(nZoneVolume));
    cJSON_AddItemToObject(root, "tel_whitelist", cJSON_CreateString(strTelWhitelist.data()));

    cJSON* jsZoneMacArray =  cJSON_CreateArray();

    for(int i=0; i<vecSecMacs.size(); i++)
    {
        cJSON_AddItemToArray(jsZoneMacArray, cJSON_CreateString(vecSecMacs[i].Data()));
    }
    cJSON_AddItemToObject(root, "zone_macs", jsZoneMacArray);


    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}
#endif


#if SUPPORT_AMP_CONTROLER

string CWebProtocol::CmdSetAmpControler(string strMac,             // 设备mac
                                        int nSet,                   // 0:查询 1:设置
                                        unsigned char *MasterChannelStatus,     // 主通道状态
                                        unsigned char backupChannelStatus,      // 备份通道状态
                                        unsigned char backupChannelId,          // 备份通道ID
                                        int nResult)      // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("amp_controler_config"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    //将5个主通道的数组转换为JSON数组
    cJSON* jsMasterChannelStatus = cJSON_CreateArray();
    for(int i=0;i<5;i++)
    {
        cJSON_AddItemToArray(jsMasterChannelStatus, cJSON_CreateNumber(MasterChannelStatus[i]));
    }
    cJSON_AddItemToObject(root, "master_channel_status", jsMasterChannelStatus);
    cJSON_AddItemToObject(root, "backup_channel_status", cJSON_CreateNumber(backupChannelStatus));
    cJSON_AddItemToObject(root, "backup_channel_id", cJSON_CreateNumber(backupChannelId));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}
#endif


#if SUPPORT_NOISE_DETECTOR

string CWebProtocol::CmdSetNoiseDetector(string strMac,             // 设备mac
                                        int nSet,                   // 0:查询 1:设置
                                        CNoiseDetector &noiseDetector,
                                        int nResult)      // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("noise_adaptive_config"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));

    cJSON_AddItemToObject(root, "enable_control", cJSON_CreateBool(noiseDetector.isEnable));
    //将8个通道的噪声数值转换为JSON数组
    cJSON* jsChannelValueArray = cJSON_CreateArray();
    for(int i=0;i<NOISE_NUM_CHANNELS;i++)
    {
        cJSON_AddItemToArray(jsChannelValueArray, cJSON_CreateNumber(noiseDetector.channelVal[i]));
    }
    cJSON_AddItemToObject(root, "channel_value", jsChannelValueArray);

    //将8个段的噪声-音量值转换为JSON数组
    cJSON* jsSegVolumeArray = cJSON_CreateArray();
    for(int i=0;i<NOISE_NUM_SEGMENTS;i++)
    {
        cJSON_AddItemToArray(jsSegVolumeArray, cJSON_CreateNumber(noiseDetector.segmentVol[i]));
    }
    cJSON_AddItemToObject(root, "segment_volume", jsSegVolumeArray);


    cJSON* jsZoneMacArray =  cJSON_CreateArray();

    vector<CMyString> vecSecMacsCur;
    noiseDetector.GetSelectedSections(vecSecMacsCur);
    for(int i=0; i<vecSecMacsCur.size(); i++)
    {
        cJSON_AddItemToArray(jsZoneMacArray, cJSON_CreateString(vecSecMacsCur[i].Data()));
    }
    cJSON_AddItemToObject(root, "zone_macs", jsZoneMacArray);

    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}
#endif



// 设置音频采集器参数
string CWebProtocol::CmdSetAudioCollectorParm(string strMac,             // 设备mac
                                                int nSet,                   // 0:查询 1:设置
                                                int nTriggerSwitch,          // 触发开关（0关闭 1开启）
                                                int nTriggerChannel,              // 触发通道id（1~4）
                                                int nTriggerZoneVolume,           // 触发类型（触发分区音量）
                                                int priority,                     // 优先级(1默认,小于定时; 2高优先级，大于定时)
                                                vector<CMyString> &vecSecMacs,  //分区数组
                                                vector<string> channelNameArray,      //通道名称数组
                                                int nResult)               // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_audio_collector_parm"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    
    cJSON *channels = cJSON_CreateArray();
    for(int j=0;j<AUDIO_COLLECTOR_CHANNELS;j++)
    {
        int channel_id=j+1;
        cJSON *channel = cJSON_CreateObject();
        cJSON_AddItemToObject(channel, "channel_id", cJSON_CreateNumber(channel_id));
        cJSON_AddItemToObject(channel, "channel_name", cJSON_CreateString( channelNameArray[j].data() ));
        cJSON_AddItemToArray(channels, channel);
    }
    cJSON_AddItemToObject(root, "channels", channels);

    cJSON_AddItemToObject(root, "trigger_switch", cJSON_CreateNumber(nTriggerSwitch));
    cJSON_AddItemToObject(root, "trigger_channel_id", cJSON_CreateNumber(nTriggerChannel));
    cJSON_AddItemToObject(root, "trigger_zone_volume", cJSON_CreateNumber(nTriggerZoneVolume));
    cJSON_AddItemToObject(root, "priority", cJSON_CreateNumber(priority));

    cJSON* jsZoneMacArray =  cJSON_CreateArray();

    for(int i=0; i<vecSecMacs.size(); i++)
    {
        cJSON_AddItemToArray(jsZoneMacArray, cJSON_CreateString(vecSecMacs[i].Data()));
    }
    cJSON_AddItemToObject(root, "trigger_zone_macs", jsZoneMacArray);

    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 查询/设置IP属性
string CWebProtocol::CmdDeviceIP(string   strMac,          // 设备Mac
                                 int      nDeviceModel,    // 设备类型
                                 RestType rt,              // 请求类型
                                 int      nIPMode,         // IP模式
                                 string   strIP,           // ip地址
                                 string   strSubnet,       // 子网掩码
                                 string   strGatway,       // 默认网关
                                 string   strDNS1,         // 首选DNS
                                 string   strDNS2,         // 备用DNS
                                 int      nResult)         // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("device_ip"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "device_model", cJSON_CreateNumber(nDeviceModel));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(rt));
    cJSON_AddItemToObject(root, "ip_mode", cJSON_CreateNumber(nIPMode));
    cJSON_AddItemToObject(root, "device_ip", cJSON_CreateString(strIP.data()));
    cJSON_AddItemToObject(root, "device_subnet", cJSON_CreateString(strSubnet.data()));
    cJSON_AddItemToObject(root, "device_gatway", cJSON_CreateString(strGatway.data()));
    cJSON_AddItemToObject(root, "device_dns1", cJSON_CreateString(strDNS1.data()));
    cJSON_AddItemToObject(root, "device_dns2", cJSON_CreateString(strDNS2.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 查询/设置网络模式
string CWebProtocol::CmdDeviceNetworkMode(string   strMac,          // 设备Mac
                                 int      nDeviceModel,    // 设备类型
                                 RestType rt,              // 请求类型
                                 int      all_model_device, //该类型的所有设备
                                 int      nNetworkMode,    // IP模式
                                 string   strServerIP,     // 服务器地址地址
                                 int   serverPort,   // 服务器端口
                                 string   strServerIP2,     // 备用服务器地址地址
                                 int   serverPort2,         // 备用服务器端口
                                 int      nResult)         // 返回结果
{
    cJSON* root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("device_network_mode"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "device_model", cJSON_CreateNumber(nDeviceModel));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(rt));
    cJSON_AddItemToObject(root, "all_model_device", cJSON_CreateNumber(all_model_device));
    cJSON_AddItemToObject(root, "network_mode", cJSON_CreateNumber(nNetworkMode));
    cJSON_AddItemToObject(root, "server_ip", cJSON_CreateString(strServerIP.data()));
    cJSON_AddItemToObject(root, "server_port", cJSON_CreateNumber(serverPort));
    cJSON_AddItemToObject(root, "backup_server_ip", cJSON_CreateString(strServerIP2.data()));
    cJSON_AddItemToObject(root, "backup_server_port", cJSON_CreateNumber(serverPort2));

    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 获取运行日志
string CWebProtocol::CmdGetRunlog(string strServerIP,     // Web服务器ip
                                  string strServerPort,   // Web服务器端口
                                  string strFilePath,     // 文件路径
                                  int nResult)            // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_run_log"));
    cJSON_AddItemToObject(root, "server_ip", cJSON_CreateString(strServerIP.data()));
    cJSON_AddItemToObject(root, "server_port", cJSON_CreateString(strServerPort.data()));
    cJSON_AddItemToObject(root, "file_path", cJSON_CreateString(strFilePath.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

string CWebProtocol::CmdExportRunlog(string strServerIP,     // Web服务器ip
                                     string strServerPort,   // Web服务器端口
                                     string strFilePath,     // 文件路径
                                     int nResult)            // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("export_run_log"));
    cJSON_AddItemToObject(root, "server_ip", cJSON_CreateString(strServerIP.data()));
    cJSON_AddItemToObject(root, "server_port", cJSON_CreateString(strServerPort.data()));
    cJSON_AddItemToObject(root, "file_path", cJSON_CreateString(strFilePath.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

#if SUPPORT_CALL_RECORD
// 查询通话日志
string CWebProtocol::CmdQueryCallLog(const vector<RecordTableData>& vecRecordData,  // 通话记录数据
                                     int nResult)                 // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("query_call_log"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));
    
    cJSON* cjCallLogs = cJSON_CreateArray();
    
    for (size_t i = 0; i < vecRecordData.size(); i++)
    {
        const RecordTableData& record = vecRecordData[i];
        cJSON* callLog = cJSON_CreateObject();
        
        cJSON_AddItemToObject(callLog, "record_id", cJSON_CreateNumber(record.nRecordId));
        cJSON_AddItemToObject(callLog, "call_id", cJSON_CreateString(record.strCallId.C_Str()));
        cJSON_AddItemToObject(callLog, "caller_mac", cJSON_CreateString(record.strCallerMac.C_Str()));
        cJSON_AddItemToObject(callLog, "caller_name", cJSON_CreateString(record.strCallerName.C_Str()));
        cJSON_AddItemToObject(callLog, "callee_list", cJSON_CreateString(record.strCalleeList.C_Str()));
        cJSON_AddItemToObject(callLog, "record_type", cJSON_CreateNumber(record.recordType));
        cJSON_AddItemToObject(callLog, "record_status", cJSON_CreateNumber(record.recordStatus));
        cJSON_AddItemToObject(callLog, "start_time", cJSON_CreateString(record.strStartTime.C_Str()));
        cJSON_AddItemToObject(callLog, "end_time", cJSON_CreateString(record.strEndTime.C_Str()));
        cJSON_AddItemToObject(callLog, "duration", cJSON_CreateNumber(record.nDuration));
        cJSON_AddItemToObject(callLog, "file_path", cJSON_CreateString(record.strFilePath.C_Str()));
        cJSON_AddItemToObject(callLog, "file_size", cJSON_CreateNumber(record.nFileSize));
        //cJSON_AddItemToObject(callLog, "audio_format", cJSON_CreateNumber(record.audioFormat));
        //cJSON_AddItemToObject(callLog, "sample_rate", cJSON_CreateNumber(record.nSampleRate));
        //cJSON_AddItemToObject(callLog, "channels", cJSON_CreateNumber(record.nChannels));
        //cJSON_AddItemToObject(callLog, "bit_rate", cJSON_CreateNumber(record.nBitRate));
        //cJSON_AddItemToObject(callLog, "create_date", cJSON_CreateString(record.strCreateDate.C_Str()));
        //cJSON_AddItemToObject(callLog, "create_time", cJSON_CreateString(record.strCreateTime.C_Str()));
        //cJSON_AddItemToObject(callLog, "remark", cJSON_CreateString(record.strRemark.C_Str()));
        
        cJSON_AddItemToArray(cjCallLogs, callLog);
    }
    
    cJSON_AddItemToObject(root, "call_logs", cjCallLogs);
    
    char* szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);
    
    return strData;
}

// 删除通话日志
string CWebProtocol::CmdDeleteCallLog(const string& strCallId,  // 通话ID
                                      int nResult)              // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("delete_call_log"));
    cJSON_AddItemToObject(root, "call_id", cJSON_CreateString(strCallId.c_str()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));
    
    char* szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);
    
    return strData;
}

// 获取系统日志
string CWebProtocol::CmdGetSystemLog(string strServerIP,     // Web服务器ip
                                     string strServerPort,   // Web服务器端口
                                     string strFilePath,     // 文件路径
                                     int nResult)            // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_system_log"));
    cJSON_AddItemToObject(root, "server_ip", cJSON_CreateString(strServerIP.data()));
    cJSON_AddItemToObject(root, "server_port", cJSON_CreateString(strServerPort.data()));
    cJSON_AddItemToObject(root, "file_path", cJSON_CreateString(strFilePath.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}
#endif

// 重置设备数据
string CWebProtocol::CmdResponseResetDeviceData(string strMac,    // 设备Mac地址
                                                int   nResult)    // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("reset_device_data"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 重启设备 回复
string CWebProtocol::CmdResponseRestartDevice(string   strMac,  // 设备Mac地址
                                              int   nResult)    // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("reboot_device"));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 分区自定义排序 回复
string CWebProtocol::CmdResponseSortZoneCustom(int   nResult)    // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("sort_device_custom"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 删除分区设备 回复
string CWebProtocol::CmdResponseDeleteDevice(int  nDevModel,    // 设备型号
                                          string strMac,    //  设备MAC
                                          int nResult)    // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("delete_device"));
    cJSON_AddItemToObject(root, "device_model", cJSON_CreateNumber(nDevModel));
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 查询/设置当前高级设置
string CWebProtocol::CmdAdvancedSetting(RestType rt,              // 请求类型
                                        int nWorkPattern,         // 工作模式
                                        int nNetMode,             // 网络模式
                                        string strServerIP,       // 服务器网络地址
                                        int      nAudiocast,      // 音频传输：1 组播 2 单播
                                        int      nSyncCount,      // 同时同步设备数
                                        int      nPlayChannel,    // 播放通道: 0 从设备播放 1 从HTTP播放
                                        int      nSongFrom,       // 歌曲来自：0 服务器本地   1 http服务器
                                        string strSongServerIP,   // 歌曲服务器IP
                                        string strSongServerPort, // 歌曲服务器端口
                                        int  nResult )            // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("advanced_setting"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(rt));
    cJSON_AddItemToObject(root, "work_pattern", cJSON_CreateNumber(nWorkPattern));
    cJSON_AddItemToObject(root, "network_mode", cJSON_CreateNumber(nNetMode));
    cJSON_AddItemToObject(root, "server_ip", cJSON_CreateString(strServerIP.data()));
    cJSON_AddItemToObject(root, "audiocast", cJSON_CreateNumber(nAudiocast));
    cJSON_AddItemToObject(root, "sync_count", cJSON_CreateNumber(nSyncCount));
    cJSON_AddItemToObject(root, "play_channel", cJSON_CreateNumber(nPlayChannel));
    cJSON_AddItemToObject(root, "song_from", cJSON_CreateNumber(nSongFrom));
    cJSON_AddItemToObject(root, "song_server_ip", cJSON_CreateString(strSongServerIP.data()));
    cJSON_AddItemToObject(root, "song_server_port", cJSON_CreateString(strSongServerPort.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置系统时间
string CWebProtocol::CmdResponseSetSystemDateTime(int    nSet,           // 1：设置   0：查询
                                                  string strDateTime,    // 当前时间
                                                  string strDataBootTime,//启动时间
                                                  int nResult)           // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_system_date_time"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    cJSON_AddItemToObject(root, "system_date_time", cJSON_CreateString(strDateTime.data()));
      cJSON_AddItemToObject(root, "system_boot_time", cJSON_CreateString(strDataBootTime.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


// 获取系统存储信息
string CWebProtocol::CmdResponseGetSystemStorage(
                                                 int hardDisk_total,       // 磁盘总大小
                                                 int hardDisk_remain,   //剩余磁盘大小
                                                 int memory_total,         //总内存大小
                                                 int memory_remain,        //剩余内存大小
                                                 int nResult)              //内存总大小
                                                
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_system_storage"));
    cJSON_AddItemToObject(root, "hard_disk_total", cJSON_CreateNumber(hardDisk_total));
    cJSON_AddItemToObject(root, "hard_disk_remain", cJSON_CreateNumber(hardDisk_remain));
    cJSON_AddItemToObject(root, "memory_total", cJSON_CreateNumber(memory_total));
    cJSON_AddItemToObject(root,"momeory_remain", cJSON_CreateNumber(memory_remain));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}



// 查询/设置系统网络配置
string CWebProtocol::CmdResponseSetSystemNetwork(
                                                 int    nSet,            // 1：设置   0：查询
                                                 string ip_address,      //IP地址
                                                 string subnet_mask,     //子网掩码
                                                 string gateway,         //网关
                                                 string dns_server,      //dns
                                                 int nResult)           //result
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_system_network"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    cJSON_AddItemToObject(root, "ip_address", cJSON_CreateString(ip_address.data()));
    cJSON_AddItemToObject(root, "subnet_mask", cJSON_CreateString(subnet_mask.data()));
    cJSON_AddItemToObject(root, "gateway", cJSON_CreateString(gateway.data()));
    cJSON_AddItemToObject(root,"dns_server", cJSON_CreateString(dns_server.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

#if SUPPORT_SERVER_SYNC
// 查询/设置系统主备服务器
string CWebProtocol::CmdResponseSetBackupServer(int    nSet,                // 1：设置   0：查询
                                                 bool isEnable,             //是否启用主备服务器
                                                 string dest_server_ip,   //备用服务器IP
                                                 int nResult)               //result
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_backup_server"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    if(nSet == 1)   //设置
    {
        cJSON_AddItemToObject(root, "dest_server_ip", cJSON_CreateString(dest_server_ip.data()));
         cJSON_AddItemToObject(root, "enable_server_sync", cJSON_CreateBool(isEnable));
    }
    else
    {
        #if IS_BACKUP_SERVER
        cJSON_AddItemToObject(root, "dest_server_ip", cJSON_CreateString(g_Global.m_serverSync.m_strMasterServerHostIp.data()));
        cJSON_AddItemToObject(root, "enable_server_sync", cJSON_CreateBool(true));   //备用服务器固定启用主备功能
        #else
        cJSON_AddItemToObject(root, "dest_server_ip", cJSON_CreateString(g_Global.m_serverSync.m_strBackupServerHostIp.data()));
        cJSON_AddItemToObject(root, "enable_server_sync", cJSON_CreateBool(g_Global.m_serverSync.m_bMasterSwitch));
        #endif
    }
    
    cJSON_AddItemToObject(root, "is_backup_server", cJSON_CreateBool(IS_BACKUP_SERVER));
    cJSON_AddItemToObject(root, "current_server_status", cJSON_CreateNumber(g_Global.m_serverSync.m_nServerStatus));

    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}
#endif


// 重置服务器数据
string CWebProtocol::CmdResponseResetServerData(int nFileFlags,  // 文件类型标识
                                                int   nResult )  // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("reset_server_data"));
    cJSON_AddItemToObject(root, "file_types", cJSON_CreateNumber(nFileFlags));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 备份服务器数据 回复
string CWebProtocol::CmdResponseBackupServerData(int nType,     // 备份类型
                                                 int nResult )  // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("backup_server_data"));
    cJSON_AddItemToObject(root, "type", cJSON_CreateNumber(nType));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 获取服务器备份文件名称
string CWebProtocol::CmdGetBackupFileName(vector<string>& vecNames,    // 备份文件名称数组
                                          int nResult)                 // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("get_backup_file_names"));
    cJSON* jsFileArrays = cJSON_CreateArray();
    for(int i=0; i<vecNames.size(); i++)
    {
        CMyString strPathName;
        strPathName.Format("/%s/%s", HTTP_FOLDER_BACKUP, vecNames.at(i).data());
        cJSON_AddItemToArray(jsFileArrays, cJSON_CreateString(strPathName.Data()));
    }
    cJSON_AddItemToObject(root, "backup_names", jsFileArrays);
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 还原服务器数据 回复
string CWebProtocol::CmdResponseRestoreServerData(string strFileName, // 文件名称
                                                  int   nResult )     // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("restore_server_data"));
    cJSON_AddItemToObject(root, "backup_name", cJSON_CreateString(strFileName.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 移除备份文件 回复
string CWebProtocol::CmdResponseRemoveBackupFile(string strFileName,    // 文件名称
                                                 int  nResult)  // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("remove_backup_file"));
    cJSON_AddItemToObject(root, "backup_name", cJSON_CreateString(strFileName.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 上传数据 回复
string CWebProtocol::CmdResponseUploadBackupFile(string strFileName,      // 上传路径
                                                 int nResult)             // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("backup_server_data"));
    cJSON_AddItemToObject(root, "backup_name", cJSON_CreateString(strFileName.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 恢复出厂设置
string CWebProtocol::CmdFactoryReset(int nResult)        // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("factory_reset"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 请求上传升级包到服务器
string CWebProtocol::CmdResponseUploadUpgradeBagToServer(string strUpgrageBagName,   // 升级包名称
                                                         string strServerIP,         // 服务器ip
                                                         string strServerPort,       // 服务器端口
                                                         string strUploadPath,       // 上传路径
                                                         int nResult)   // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("request_upload_upgradebag_to_server"));
    cJSON_AddItemToObject(root, "upgradebag_name", cJSON_CreateString(strUpgrageBagName.data()));
    cJSON_AddItemToObject(root, "server_ip", cJSON_CreateString(strServerIP.data()));
    cJSON_AddItemToObject(root, "server_port", cJSON_CreateString(strServerPort.data()));
    cJSON_AddItemToObject(root, "upload_path", cJSON_CreateString(strUploadPath.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 请求升级服务器
string CWebProtocol::CmdResponseUpgradeServer(string strUpgrageBagName,   // 升级包名称
                                              int nResult)           // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("upgrade_server"));
    cJSON_AddItemToObject(root, "upgradebag_name", cJSON_CreateString(strUpgrageBagName.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 请求重启服务器
string CWebProtocol::CmdResponseRebootServer(int nResult)       // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("reboot_server"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 关于服务器
string CWebProtocol::CmdServerAbout(string strVersion,      // 服务器版本
                                    string strMac,          // 服务器MAC地址
                                    int nAPPType,           // 程序类型
                                    string strMachineCode,  // 机器码
                                    int nResult)            // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("server_about"));
    cJSON_AddItemToObject(root, "version", cJSON_CreateString(strVersion.data()));
    cJSON_AddItemToObject(root, "mac", cJSON_CreateString(strMac.data()));
    cJSON_AddItemToObject(root, "type", cJSON_CreateNumber(nAPPType));
    cJSON_AddItemToObject(root, "machine_code", cJSON_CreateString(strMachineCode.data()));
    cJSON_AddItemToObject(root, "ip", cJSON_CreateString(CNetwork::GetHostIP()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 服务器注册
string CWebProtocol::CmdResponseRegisterServer(int nResult)    // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("server_register"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}
#if APP_IS_LZY_COMMERCE_VERSION
string  CWebProtocol::CmdResponseTTSRegister(int nResult)  // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("tts_basic_authorization"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

string  CWebProtocol::CmdResponseTTSTrial(int nResult, bool bApplyTrial, int nRemainingDays)  // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("tts_trial_application"));
    cJSON_AddItemToObject(root, "apply_trial", cJSON_CreateBool(bApplyTrial));
    cJSON_AddItemToObject(root, "tts_basic_valid", cJSON_CreateNumber(g_Global.b_tts_basic_Authorized));   //TTS功能授权状态：0=未授权且未试用，1=已授权，2=未授权但正在试用
    cJSON_AddItemToObject(root, "remaining_seconds", cJSON_CreateNumber(nRemainingDays));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}
#endif

string  CWebProtocol::CmdResponseCloudControlRegister(int nResult)  // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("cloud_control_authorization"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

/********************************************************/

// 分控设备上线（主要用于UDP）
string CWebProtocol::CmdSearchControlDevice()
{
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("search_device"));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}


string CWebProtocol::CmdUserAuthFailure(int nResult, string strCommand)
{
    cJSON*  root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString(strCommand.data()));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}




/************播放任务管理****************************/

// 设置任务播放模式
string CWebProtocol::CmdResponse_SetPlayTask_PlayMode(int nResult)    // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_task_play_mode"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置任务播放状态
string CWebProtocol::CmdResponseSetPlayTask_PlayStatus(int nResult)    // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_task_play_status"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

// 设置任务播放上下曲
string CWebProtocol::CmdResponseSetPlayTask_PreNext(int nResult)    // 返回结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_task_play_pre_next"));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}

#if SUPPORT_MANUALTASK_PROGRESS
// 设置任务播放进度
string CWebProtocol::CmdResponseSetPlayTask_Progress(int  nSet,       // 0:查询 1:设置
                                                    int nPlayId,      //播放id
                                                    int nPlayTime,    //当前播放时间
                                                    int nResult)      //结果
{
    cJSON* root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString("set_task_playback_progress"));
    cJSON_AddItemToObject(root, "set", cJSON_CreateNumber(nSet));
    cJSON_AddItemToObject(root, "play_id", cJSON_CreateNumber(nPlayId));
    cJSON_AddItemToObject(root, "cur_play_time", cJSON_CreateNumber(nPlayTime));
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(nResult));

    char*  szBuf = cJSON_Print(root);
    string strData(szBuf);
    free(szBuf);
    cJSON_Delete(root);

    return strData;
}
#endif