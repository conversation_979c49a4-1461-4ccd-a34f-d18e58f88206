#ifndef QTCLIENTTRANSFER_H
#define QTCLIENTTRANSFER_H

#include <QWebSocket>
#include <string>
#include <QTimer>
#include <QMutex>
using namespace std;

extern char *extentionName[];

enum
{
   WS_STATUS_DISCONNECT,        //未连接
   WS_STATUS_CONNECTING,        //正在连接
   WS_STATUS_CONNECTED,         //已连接
};


enum
{
   LOGIN_STATUS_DISCONNECT,         //未登录
   //当WS_STATUS处于WS_STATUS_CONNECTED时才有下面的状态
   LOGIN_STATUS_LOGGING,            //正在登录
   LOGIN_STATUS_CONNECT_ERROR,      //连接失败
   LOGIN_STATUS_SUCCEED,            //登录成功
   LOGIN_STATUS_FAILED,             //登录失败(账号/密码错误)
};

typedef struct
{
   string account;
   string company;
   int id;
   string name;
   string uuid;
}struct_extension_userInfo;

enum
{
    AUTH_DEVICE_TTS,
    AUTH_DEVICE_SIP,
    AUTH_DEVICE_INTERCOM,
    AUTH_DEVICE_FIRE,
    AUTH_DEVICE_POWER,
    AUTH_DEVICE_REMOTE,
    MAX_AUTH_DEVICE_EXTENSION
};


class QtClientTransfer : public QObject
{
    Q_OBJECT

public:
    explicit QtClientTransfer(QObject *parent = 0);
    ~QtClientTransfer();

    void connect();
    void disconnect();
    void sendMessage(string strBuf);

    int setWsStatus(int status);
    int getWSStatus();
    int setLoginStatus(int status);
    int getLoginStatus();

    bool isWsConnected();

    void login(string account,string password);
    void queryCount();

    void HandleWebCommand(const char *data);

    void StartWorking();
    static void *HandleConnectThread(void *lpParam);

signals:
    void wsConnectStatusChanged(int status);
    void wsLoginStatusChanged(int status);
    void    send(string,string);

private slots:
    void onConnected();
    void onDisconnected();
    void onTextMessageReceived(QString message);
    void slotError(QAbstractSocket::SocketError error);
    void    SendMessageQ(string strBuf, string strUID);
    void    OnTimeOut();

private:
    QWebSocket *m_webSocket;
    int wsStatus;
    int login_status;
    int m_heartBeatRecvTimeout;     //接收心跳超时时间（没超过60秒没有收到数据，认为错误，需要断开连接)
    int m_heartBeatSendTimeout;     //发送心跳超时时间（连接上中间服务器后，每隔20秒发送)
    QTimer  m_Timer;                // 定时器
    QMutex  m_mutex_send;
public:
    struct_extension_userInfo st_extension_userInfo;
    map<string,int> map_extension;
};

#endif // WEBSOCKETCLIENT_H
