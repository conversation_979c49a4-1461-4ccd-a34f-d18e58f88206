#include "stdafx.h"
#include "QtTcpSocket.h"
#include <QByteArray>
#include <QTextCodec>


QtTcpSocket::QtTcpSocket()
{
    m_nCurSendID = 1;
    m_bSending = false;

    m_tcpServer = new QTcpServer(this);
    QObject::connect(this, SIGNAL(send()), this, SLOT(SendData()));
    QObject::connect(&m_Timer, SIGNAL(timeout()), this, SLOT(OnTimeOut()));
}

void QtTcpSocket::StartTCPSocket()
{
    if(m_tcpServer->listen(QHostAddress::Any, 8090))
    {
        QObject::connect(m_tcpServer, SIGNAL(newConnection()), this, SLOT(connected()));
        LOG("tcp server is run", LV_INFO);
    }
    else
    {
        qDebug() << m_tcpServer->errorString();
        m_tcpServer->close();
    }

    m_Timer.start(MAX_SEND_TIMER);
}

void QtTcpSocket::AddCommandBuf(string strUID, string strBuf)
{
    QMutexLocker locker(&m_mutex);
    CommandData sd;
    sd.strUID = strUID;
    sd.strBuf = strBuf;
    m_Commandlist.push_back(sd);
}

void QtTcpSocket::connected()
{
    pTcpSocket = new QTcpSocket(this);
    pTcpSocket = m_tcpServer->nextPendingConnection();
    string strUID = GetGUID().Data();
    pTcpSocket->setObjectName(QString(strUID.data()));

    QObject::connect(pTcpSocket, SIGNAL(readyRead()), this, SLOT(recvData()));
    QObject::connect(pTcpSocket,SIGNAL(disconnected()),this,SLOT(socketDisconnected()));

    //LOG(FORMAT("connect:%s", strUID.data()), LV_INFO);
    m_TcpSockets[strUID] = pTcpSocket;

}

// 处理粘包
void SubData(const char *szData, int nLen, string strUID)
{
    static char buf[MAX_BUF_LEN*4+1] = {0};
    static int start = 0;
    static int count = -1;
    int pos = 0;  // szData pos

    while (pos < nLen)
    {
        if (szData[pos] == '{')
        {
            count = ((count < 0) ? 1 : count+1);
        }

        if (count < 0)
        {
            pos++;
            continue;
        }

        if (szData[pos] == '}')
        {
            count--;
        }

        // 防止不为json数据时超出数组范围，导致内存异常
        if(start >= MAX_BUF_LEN*4+1)
        {
            start = 0;
        }

        buf[start++] = szData[pos];

        if (count == 0)
        {

            buf[start++] = 0;
            //LOG(FORMAT("handle : %s", buf), LV_WARNING);
            g_Global.m_WebNetwork.m_WebHandle.HandleTCPCommand(buf, strUID);
            count = -1;
            start = 0;
        }

        pos++;
    }
}

void QtTcpSocket::recvData()
{
    QTcpSocket * socket= ( QTcpSocket *)sender();
    string strUID = socket->objectName().toLatin1().data();
    QString strInfo = QString("客户端[%1:%2] : ").arg(socket->peerAddress().toString()).arg(socket->peerPort());

    QString msg;
    while(!socket->atEnd())
    {
        msg.append(QString(socket->readAll()));
    }

    SubData(msg.toLocal8Bit().data(), msg.toLocal8Bit().length(), strUID);
}

void QtTcpSocket::socketDisconnected()
{
    QTcpSocket * socket= ( QTcpSocket *)sender();
    string strUID = socket->objectName().toLatin1().data();
    ERROR_LOG("disconnect : %s, error : %s", strUID.data(), socket->errorString().toLocal8Bit().data());
    m_TcpSockets.erase(strUID);
    g_Global.m_WebSections.RemoveWebSectionBySockID(strUID);
    g_Global.m_Users.RemoveUuid(strUID,"127.0.0.1");
    g_Global.m_WebNetwork.WebDeviceOffline(strUID);
}

void QtTcpSocket::SendData()
{
    /*
    if(m_bSending)
    {
        LOG("sending ...", LV_WARNING);
        return;
    }

    if(m_TcpSockets.size() == 0)
    {
        m_sendData1.clear();
        m_sendData2.clear();
    }

    m_bSending = true;

    if(m_nCurSendID == 1)
    {
        m_nCurSendID = 2;
        list<CommandData>::iterator iter = m_sendData1.begin();
        for(; iter != m_sendData1.end(); iter++)
        {
            string strUID = iter->strUID;
            string strBuf = iter->strBuf;
            QTcpSocket* pTcpSocket = m_TcpSockets[strUID];
            if(pTcpSocket != NULL && pTcpSocket->isValid())
            {
                pTcpSocket->write(strBuf.data());
            }
        }
        m_sendData1.clear();
    }
    else
    {
        m_nCurSendID = 1;
        list<CommandData>::iterator iter = m_sendData2.begin();
        for(; iter != m_sendData2.end(); iter++)
        {
            string strUID = iter->strUID;
            string strBuf = iter->strBuf;
            QTcpSocket* pTcpSocket = m_TcpSockets[strUID];
            if(pTcpSocket != NULL && pTcpSocket->isValid())
            {
                pTcpSocket->write(strBuf.data());
            }
        }
        m_sendData2.clear();
    }

    m_bSending = false;
    */
}

void QtTcpSocket::OnTimeOut()
{
    if(m_TcpSockets.size() == 0)
    {
        m_Commandlist.clear();
        return;
    }

    QMutexLocker locker(&m_mutex);

    int nCount = 0;
    list<CommandData>::iterator iter = m_Commandlist.begin();
    for(; iter != m_Commandlist.end(); )
    {
        string strUID = iter->strUID;
        string strBuf = iter->strBuf;
        QTcpSocket* pTcpSocket = m_TcpSockets[strUID];
        if(pTcpSocket != NULL && pTcpSocket->isValid())
        {
            pTcpSocket->write(strBuf.data());
        }

        iter=m_Commandlist.erase(iter);
        if(m_Commandlist.size() == 0)
            break;
        else
        {
            iter++;
        }

        nCount++;
        if(nCount >= MAX_FIX_TIME_SEND_COUNT)   // 超过限制发送次数等到下次定时再发送
        {
            break;
        }
    }
}

