#ifndef WEBPROTOCOL_H
#define WEBPROTOCOL_H

#include <string>
#include "Model/Other/VoipSipInfo.h"
#include "Network/Monitor/MonProc.h"
#include "Model/User/UserManager.h"
//#include "Model/Other/WebTimePoint.h"
#include "Model/Recv/ExtraTimer.h"
#include "Model/Recv/ExtraFireCollector.h"
#include "Global/Const.h"

#if SUPPORT_NOISE_DETECTOR
#include "Model/Device/NoiseDetector.h"
#endif

#if SUPPORT_CALL_RECORD
#include "Database/RecordTable.h"
#endif

using std::string;

// 分区信息
typedef  struct tagZoneInfo
{
    int           nID;                    // 分区ID
    string        strExten;               // sip分机号码
    int           nExtenStatus;           // Sip会话状态
    string        strIP;                  // ip地址
    string        strMac;                 // mac地址
    string        strMonitorMac;          // 绑定监控设备mac地址
    MonitorStatus monitorStatus;          // 监控设备状态
    string        strName;                // 分区别名
    int           nVolume;                // 音量
    int           nSource;                // 节目源
    string        strSourceName;          // 节目源名称
    int           nPlayID;                // 播放ID
    int           nPlayStatus;            // 播放状态
    int           nDeviceModel;           // 设备型号
    bool          bTimerValid;            // 定时点是否有效
    string        strFirmwareVersion;     // 固件版本
    string        strMemory;              // 内存容量
    BYTE          deviceFeature;          // 设备特征
    NetworkMode   networkMode;            // 网络模式

    int           nModule4GSignalLevel;   // 4G模块信号强度等级
    string        strModule4GIccid;       // 4G模块ICCID卡号

    string        strStreamingMac;           // 当前播放的流媒体mac
    string        strStreamingIP;            // 当前播放的流媒体ip
    int           nStreamingStatus;        // 流媒体连接状态
    int           nStreamingMode;          // 流媒体工作模式


}ZoneInfo, *LPZoneInfo;




//  分组信息
typedef  struct tagGroupInfo
{
    CMyString	m_strID;		// 分组ID
    CMyString	m_strAccount;	// 创建该分组的用户名
    const char*	m_szGroupName;	// 分组名称
    int  m_zone_cunt;    //分区数量
    vector<CMyString>	m_vecSecMac;
}GroupInfo,*LPGroupInfo;


typedef struct tagWebSongInfo
{
    int duration;
    CMyString songName;
    CMyString songPathName;
    CMyString songMd5;
    CMyString songAccount;
    int songBitRate;
    bool alive;
}WebSongInfo;

//   歌曲列表信息
typedef  struct tagPlaylistInfo
{
    CMyString	m_strID;		    //歌曲列表ID
    CMyString	m_szPlaylistName;	//歌曲列表名称
    CMyString   m_userAccount;      //创建列表的账户名称
    int  m_songs_cunt;              //歌曲数量
    vector<WebSongInfo> m_vecSongInfo;
}PlaylistInfo,*LPPlaylistInfo;


#if 0
typedef struct tagWebTimerPointInfo
{
    int					m_nID;			
    bool				m_bValid;
    CMyString			m_strName;

    vector<CMyString>	m_vecSecMac;
    vector<CMyString>	m_vecGroupID;
    vector<CSong>		m_vecSong;

    bool				m_bPlayToEnd;
    bool				m_bResumePlaying;	// 定时结束后恢复播放歌曲

    bool				m_bIntercut;		// 插播

    bool				m_bFollowDevice;	// 音量跟随设备

    bool				m_bSinglePlay;		// 随机播放单曲

    int					m_nVolume;
    TIMER_DATE			m_DateStart;
    TIMER_DATE			m_DateEnd;
    TIMER_TIME			m_TimeStart;
    TIMER_TIME			m_TimeEnd;
    TIMER_TIME_TYPE			m_Type;             //定时循环类型 0-周 1-日期
    int					m_nPlayMode;        
    bool				m_SelectedDays[7];	// 按周一，周二..周日来存储，但GetDayOfWeek是周日(1)，周一(2)...周六(7)
}WebTimerPointInfo;

//   定时信息
typedef  struct tagTimerSchemeInfo
{
    int	schemeId;		          //从1开始 
    CMyString schemeName;	
    int  m_TimerPoint_Cunt;              
    vector<WebTimerPointInfo> m_vecTimerPoint;
}TimerSchemeInfo,*LPTimerSchemeInfo;

#endif





typedef enum
{
    REST_GET = 0,                   // 请求查询
    REST_SET = 1                    // 请求设置

}RestType;

enum
{
    RG_AUTH_CHANGER     = 1,        // 用户权限更改
    RG_USER_DELETE      = 2,        // 用户移除
    RG_USER_LOGIN_OHTER = 3,        // 用户在其他地方登陆
    RG_SYSTEM_REBOOT    = 4,        // 系统重启
    RG_ACCOUNT_ERROR    = 5         // 账号异常
};


class CWebProtocol
{
public:
    CWebProtocol();

    /*---------------------          获取信息接口              ----------------------*/
    // 回复请求心跳信息
    static string  CmdResponseGetHeartbeatInfo(bool bLinkNet,
                                               int nResult = EC_SUCCESS);    // 返回结果

    // 服务器通知客户端更新文件
    static string  CmdNotifyUpdateFile(int     nFileType,               // 文件类型
                                       string  strUpdateTime,           // 文件更新时间
                                       string  strWebIP,                // Web服务器ip
                                       string  strWebPort,              // Web服务器端口
                                       string  strFilePath,             // 文件路径
                                       int     nResult = EC_SUCCESS);   // 返回结果

    // 下发分区状态
    static string  CmdForwardZoneInfo(int nDeviceType,                  // 设备类型
                                      int nPageCount,                   // 总页数
                                      int nPage,                        // 第几页
                                      int nZoneCount,                   // 此页包含的分区数，最多20个分区
                                      ZoneInfo* zones,                  // 分区状态列表
                                      bool bAllZone = false,            // 是否请求全部分区
                                      int  nResult = EC_SUCCESS);       // 返回结果

    // 下发分组信息
    static string  CmdForwardGroupInfo( string  strUpdateTime,
                                         int nGroupCount,
                                         GroupInfo* groups,   // 分组信息
                                         int       nResult); // 返回结果


    // 下发歌曲列表信息
    static string  CmdForwardPlaylistInfo(
                                         string  strUpdateTime,      // 文件更新时间
                                         int nPlaylistCount,
                                         PlaylistInfo* playlists,   // 分组信息
                                         int       nResult); // 返回结果

    // 获取客户端状态(DSP9312)
    static string  CmdGetClientStatus(void);

    // 设置客户端信息
    static string  CmdSetClientInfo(string strName,
                                    string strMonitoMac);

    // 分发网络信息
    static  string  CmdForwardNetInfo(bool  bLink,                      // 是否连接到网络
                                      double ispeed,                    // 上传速度
                                      double ospeed);                   // 下载速度

    // 分发网络质量
    static  string  CmdForwardNetQuality(string strMac,                 // 设备Mac地址
                                         string strName,                // 设备名称
                                         int nNetType,                  // 网络类型
                                         double dBandWidth = 0.0,       // 带宽
                                         double dDelayed = 0.0,         // 延时
                                         double dLossRate = 0.0,        // 丢包率
                                         int    nResult = EC_SUCCESS);   // 返回结果

    // 回复设置选中分区
    static string  CmdResponseSetSelectedZone(int nID,                      // 回传选中分区的id
                                              int nPage,                    // 收到第几个包
                                              int nResult = EC_SUCCESS);    // 返回结果

    // 播放节目源 回复
    static string  CmdResponsePlaySource(int nResult = EC_SUCCESS);         //  返回结果

    // 服务器请求广播寻呼 回复
    static string  CmdResponseWebPaging(int nResult, int nEvent);                       //  返回结果

    // 发送广播寻呼音频流 回复
    static string  CmdResponseWebPagingStream(int nResult);                 //  返回结果

    static string  CmdResponseSetBroadcastPaging(int nResult);        //  返回结果

    // 停止多媒体网关 回复
    static string  CmdResponseStopStreaming(int nResult = EC_SUCCESS);         //  返回结果

    //查询/设置监听音箱 回复
    static string CmdResponseSetMonitorSpeaker(int nSetMode,                 // 1：设置   0：查询
                                                string device_mac ,          // 监听音箱mac
                                                int nResult);                //  返回结果
    // 监听节目源 回复
    static string  CmdResponseMonitorSource(int nOper,                      // 开启或关闭监听 0x00:关闭 0x01:开启
                                            int nResult = EC_SUCCESS);      //  返回结果

    // 播放音频采集器音源 回复
    static string  CmdResponsePlayAudioCollector(string strMac,             // 分区设备mac地址
                                                int nSelID,                 // 选择分区ID
                                                int nResult = EC_SUCCESS);     //  返回结果

    // 播放AUX音源
    static string  CmdResponsePlayAUX(int nSelID,      // 选择分区ID
                                      int nResult = EC_SUCCESS);     //  返回结果

    // 调节音量 回复
    static string  CmdResponseSetVolume(int nResult = EC_SUCCESS);      //  返回结果

    // 4.3 设置设备子音量 回复
    static string  CmdResponseSetSubVolume(int nSetMode,              // 1：设置   0：查询,
                                              int nSubVolume,            // 子音量值
                                              int nAuxVolume,            // 本地音量值
                                              int nResult = EC_SUCCESS);   //  返回结果
    // 设置对讲终端基础参数 回复
    static string  CmdResponseSetIntercomBasic(int nSetMode,              // 1：设置   0：查询,
                                              string strMac,              //设备MAC
                                              CALLDEVICECONFIG *callBasicConfig,	//对讲基础配置
                                              int nResult = EC_SUCCESS);   //  返回结果

    // 设置终端触发参数 回复
    static string  CmdResponseTriggerBasic(int nSetMode,              // 1：设置   0：查询,
                                              string strMac,              //设备MAC
                                              char trigger_switch,
                                              char trigger_mode,
                                              char *trigger_songName,
                                              int volume,
                                              int nResult = EC_SUCCESS);   //  返回结果

    // 启动/停止手动告警 回复
    static string  CmdResponseStartManualAlarm(int nSetMode,string songName,
                                          int nResult);   //  返回结果

    // 设置分区为空闲状态 回复
    static string  CmdResponseSetIdle(int nResult = EC_SUCCESS);        //  返回结果

    // 设置钟声 回复
    static string  CmdResponseSetBell(int nResult = EC_SUCCESS);        //  返回结果

    // 播放钟声 回复
    static string  CmdResponsePlayBell(int nResult = EC_SUCCESS);       //  返回结果

    // 设置/取消静音 回复
    static string  CmdResponseSetMutes(int  nSetStatus,         // 设置状态
                                                                   int  nResult = EC_SUCCESS);              //  返回结果

    // 查询/设置播放模式 回复
    static string  CmdResponseChangePlayMode(int nSetMode,                // 1：设置   0：查询
                                             int nPlayMode = 0,           // 播放模式,此字段查询时有效
                                             int nResult = EC_SUCCESS);   // 返回结果

  

/*************            监控       ************/
    // 服务器向客户端发送监控设备信息
    /*static string  CmdForwardMonitorInfo(int    nChannel,          // 通道号
                                                                      string strAccount,      // 登录账号
                                                                      string strPassword,   // 登录密码
                                                                      string strIP,                // ip地址
                                                                      int    nPort,                 // 端口号
                                                                      string strRtsp,            // rtsp地址
                                                                      int       nResult = EC_SUCCESS);         // 返回结果*/


    // 服务器向客户端发送监控设备信息
    static  string  CmdForwardMonitorInfo(
                                          int  nMonitroCount,
                                          LPCMonitorInfo* MonitorInfos,
                                          int       nResult = EC_SUCCESS);    // 返回结果


    // 服务器向客户端发送监控的触发事件
    static string CmdForwardMonitorEvent(string  strMac,        // Mac地址
                                         int    nEventType,     // 事件类型
                                         string strDateTime,    // 日期时间
                                         int    nDirection);    // 入侵方向

    // 设置监控设备信息
    static string  CmdResponseSetMonitorInfo(string strMac,               // Mac地址
                                             string strAccount,           // 账号
                                             string strPassword,          // 密码
                                             int nResult = EC_SUCCESS);   // 返回结果

    // 添加自定义监控设备
    static string  CmdResponseAddCustomMonitor(string strMonitorName,       // 账号
                                             string strMonitorUrl,        // 密码
                                             int nResult = EC_SUCCESS);   // 返回结果

    // 删除监控设备信息
    static string  CmdResponseDeleteMonitorInfo(string strMac,               // Mac地址
                                             int nResult = EC_SUCCESS);   // 返回结果

    // 设置监控设备事件
    static string  CmdResponseSetMonitorEvent(string strComID,            // 命令标识
                                              string strMac,              // Mac地址
                                              int nResult = EC_SUCCESS);  // 返回结果

    // 设置监控设备事件触发分区
    static string  CmdResponseSetEventZone(string strComID,             // 命令标识
                                           string strMac,               // Mac地址
                                           int  nEventID,               // 事件类型
                                           int  nPage,                  // 指定页数
                                           int  nResult = EC_SUCCESS);  // 返回结果

    // 指定监控RTMP流(web)
    static string  CmdResponseMonitorRtsp(
                                          int  nResult = EC_SUCCESS);  // 返回结果

    // 开启/关闭视频监控
    static string CmdResponseEnableMonitor(int  nSet,        // 0:查询 1:设置
                                            bool isEnableMonitor, //是否启用视频监控
                                            int  nResult);     // 返回结果
    // 开启/关闭考试模式
    static string CmdResponseEnableExaminationMode(int  nSet,        // 0:查询 1:设置
                                            bool examination_mode, //是否启用考试模式
                                            int  nResult);     // 返回结果
    // 开启/关闭云控制
    static string CmdResponseEnableCloudControl(int  nSet,        // 0:查询 1:设置
                                            bool cloudControl, //是否启用云控制,
                                            bool isConnected,  //连接状态(1已连接，0未连接)
                                            int  nResult);     // 返回结果

    // 开启/关闭通话录音
    static string CmdResponseEnableCallRecord(int  nSet,        // 0:查询 1:设置
                                            bool isEnableCallRecord, //是否启用通话录音
                                            int  nResult);     // 返回结果

    // 设置系统主题
    static string CmdSetSystemTheme(string key,string value,
                                    int  nResult);     // 返回结果

    // 设置AISP配置
    static string CmdSetAispConfig(int nSet,string value,
                                    int  nResult);     // 返回结果

// 对讲/广播/监听
    // 客户端向服务器请求SIP设备信息 回复
    static string CmdResponseSipInfo(int       nPageCount,             // 总页数
                                     int       nPage,                  // 指定页数
                                     int       nSipCount,              // 此页包含的设备数，最多20个分区
                                     SipInfo*  pSipInfo,               // sip设备列表
                                     int       nResult = EC_SUCCESS);  // 返回结果

    // 客户端向服务器发起对讲请求 回复
    static  string  CmdResponseTalkback(int       nResult = EC_SUCCESS);   //  返回结果

    // 客户端向服务器发起广播/寻呼请求  回复
    static  string  CmdResponsePaging(int    nType,                     // 寻呼类型
                                      int    nRtpType,                  // RTP类型
                                      int     nPage,                    // 指定页数
                                      int     nResult = EC_SUCCESS);    //  返回结果

    // 客户端向服务器发起广播(寻呼)请求(PCM)
    static   string   CmdResponsePcmPaging(string      strExten,         // 发起寻呼的号码
                                           int     nRtpType,             // RTP类型
                                           int     nResult = EC_SUCCESS);//  返回结果

    // 客户端向服务器发起监听请求 回复
    static   string   CmdResponseListen(int  nResult = EC_SUCCESS);      //  返回结果

    // 客户端向服务器发送挂断请求 回复
    static   string   CmdResponseHangup(string strExten, int  nResult = EC_SUCCESS);     //  返回结果

    // 客户端向服务器发送挂断请求(PCM) 回复
    static   string   CmdResponsePCMHangup(string strExten,            // 发起寻呼的号码
                                           int  nSelID,                // 选中分区的标识
                                           int  nResult = EC_SUCCESS); //  返回结果

    // 获取媒体端口 回复
    static   string   CmdResponseGetMediaPort(string strID,
                                           string udpPagingPort,
                                           int nResult);           //  返回结果
/*-----------------          用户管理                      ---------------------*/
    // 用户登录 回复
    static   string   CmdResponseUserLogin(string strID,
                                           UserParm_t& uParm,
                                           int appType,             // 软件类型
                                           string strRegisterMachineCode,   // 机器码
                                           string strCloudControlMachineCode,   // 云控制机器码
                                           string strIP,            // 本地IP地址
                                           string strVersion,      // 服务器版本
                                           string strMac,          // 服务器MAC地址
                                           bool isEnbaleMonitor,   // 是否启用视频监控,
                                           bool examination_mode,  // 是否启用考试模式
                                           string expiration_date,  // 软件授权到期时间
                                           int nResult);           //  返回结果

    // 添加用户 回复
    static   string   CmdResponseAddUser(string strComID,     // 命令ID
                                        string strAccount,   // 用户名
                                        int nResult);         //  返回结果

    // 移除用户 回复
    static   string   CmdResponseRemoveUser(string strAccount, // 用户名
                                            int nResult);      //  返回结果

    // 编辑用户（修改用户权限）
    static   string   CmdResponseModifyUser(string strComID,     // 命令ID
                                          string strAccount,   // 用户名
                                          int nResult);             //  返回结果

    // 查询用户信息 回复
    static   string   CmdResponseGetUserInfo(userParm &destUserUParm,      // 待查询用户信息
                                             vector<userParm> &vecSubUsers, // 待查询用户的子用户信息
                                             int        nResult = EC_SUCCESS);   // 返回结果

    // 修改用户密码 回复
    static  string  CmdResponseModifyPsd(int nResult);          // 返回结果

    #if APP_IS_AISP_TZY_ENCRYPTION
    static  string  CmdResponseSetUserCertificate(int nSet,            // 0:查询 1:设置
                                                    string strAccount,  // 用户名
                                                    string strCertificate,  //用户证书
                                                    int nResult = EC_SUCCESS);
    #endif

    // 获取(子)用户的分区
    static  string  CmdResponseGetUserZone(string    strAccount,      // 查询用户账户
                                           int       nPageCount,    // 总页数
                                           int       nPage,              // 指定页数
                                           int       nZoneCount,   // 此页包含的分区数
                                           string*   strMacs,          // 分区Mac数组
                                           int       nResult = EC_SUCCESS);      // 返回结果

    // 获取(子)用户的分组
    static  string   CmdResponseGetUserGroup(string    strAccount,        // 查询用户账户
                                             int       nPageCount,    // 总页数
                                             int       nPage,              // 指定页数
                                             int       nGroupCount,   // 此页包含的分组数
                                             string*   strGroupIDs,     // 分组ID数组
                                             int       nResult = EC_SUCCESS);     // 返回结果

    // 设置(子)用户的分区
    static  string   CmdResponseSetUserZone(string    strComID,        //  返回结果
                                            string    strAccount,      // 查询用户账户
                                            int          nPage,              // 指定页数
                                            int          nResult = EC_SUCCESS);      // 返回结果

    // 设置(子)用户的分组
    static  string   CmdResponseSetUserGroup(string    strComID,        //  返回结果
                                             string    strAccount,      // 查询用户账户
                                             int          nPage,              // 指定页数
                                             int          nResult = EC_SUCCESS);      // 返回结果

    // 设置用户权限
    static string    CmdResponseSetUserAuthority(
                                             string    strAccount,      // 账户
                                             int          authority,        // 权限
                                             int          nResult = EC_SUCCESS);      // 返回结果

    static string    CmdResponseGetUserStorageCapacity(
                                            string    strAccount,      // 账户
                                            double    nstorageCapacity,        // 总存储空间
                                            double    nUsedSpace,              // 已用存储空间
                                            double    nRemainingSpace,         // 剩余存储空间
                                            int          nResult = EC_SUCCESS);      // 返回结果

    static string    CmdResponseSetUserStorageCapacity(
                                             string    strAccount,      // 账户
                                             int          nstorageCapacity,        // 存储空间
                                             int          nResult = EC_SUCCESS);      // 返回结果

    // 主机请求用户重新登录
    static  string    CmdResquestUserRelogin(int nStatus);    // 状态码

/*----------------------         编辑分组                            ------------------------*/
    // 添加分组 回复
    static  string    CmdResponseAddGroup(string    strGroupID,                // 分组ID
                                            string    strGroupName,          // 分组名称
                                            int   nZoneCount,                     // 分区数量
                                            int   nResult = EC_SUCCESS);   // 返回结果

    // 编辑分组 回复
    static  string      CmdResponseModifyGroup(string    strComID,                   // 命令ID
                                                string   strGroupID,                // 分组ID
                                                string    strGroupName,         // 分组名称
                                                int   nZoneCount,                    // 分区数量
                                                int   nResult = EC_SUCCESS);  // 返回结果

    // 移除分组 回复
    static  string      CmdResponseRemoveGroup(string   strGroupID,                 // 分组ID
                                                 int   nResult = EC_SUCCESS);   // 返回结果

    // 设置分组的分区
    static  string      CmdResponseSetGroupSec(string   strComID,                     // 命令ID
                                               string   strGroupID,                 // 分组ID
                                               int        nPage,                          // 指定页数
                                               int      nResult = EC_SUCCESS);  // 返回结果

/*----------------------         编辑播放列表                       ------------------------*/
    // 添加歌曲列表 回复
    static   string    CmdResponseAddSongList(string strListID,                         // 列表ID
                                                string  strName,                        // 列表名称
                                                int nResult = EC_SUCCESS);      // 返回结果

    // 重命名歌曲列表 回复
    static   string    CmdResponseRenameSongList(string strListID,          // 列表ID
                                                string strListName,   // 列表名称
                                                int nResult = EC_SUCCESS);        // 返回结果

    // 移除歌曲列表 回复
    static   string    CmdResponseRemoveSongList(string  strListID,         // 列表ID
                                                int  nResult = EC_SUCCESS);              // 返回结果

    // 获取服务器上传路径下的所有歌曲 回复
    static   string     CmdResponseGetServerAllSong(int nPageCount,             // 总页数
                                                      int  nPage,                      // 指定页数
                                                      int  nSongCount,           //  此页包含的歌曲名称数量，最多100个歌曲名称
                                                      string *strSongs,           //  歌曲名称数组
                                                      int  nResult = EC_SUCCESS);    //  返回结果

    // 请求上传歌曲到服务器 回复
    static   string     CmdResponseUploadSongToServer(
                                                      string strServerIP,        // Web服务器ip
                                                      string strServerPort,      // Web服务器端口
                                                      string strUploadPath,      // 上传路径
                                                      int    nResult = EC_SUCCESS);     // 返回结果

    // 添加歌曲到列表 回复
    static   string     CmdResponseAddSongToList(string strListID,           // 列表ID
                                                int song_count,
                                                vector<string> song_names,
                                                  int      nResult = EC_SUCCESS);           // 返回结果

    // 从列表移除指定歌曲
    static   string     CmdResponseRemoveSongFromList(string strListID,           // 列表ID
                                                    int song_count,
                                                    vector<string> song_names,
                                                      int      nResult = EC_SUCCESS);           // 返回结果

    // 管理员审核指定歌曲
    static   string     CmdResponseAuditLocalSong(int song_count,
                                                    vector<string> song_names,
                                                      int      nResult = EC_SUCCESS);           // 返回结果

    // 停止上传歌曲
    static   string     CmdResponseStopUploadSong(int  nResult = EC_SUCCESS);            // 返回结果

    // 移除指定列表中所有的歌曲
    static    string    CmdResponseRemoveAllSong(string strListID,           // 列表ID
                                                 int      nResult = EC_SUCCESS);  // 返回结果

    // 文字转语音
    static  string  CmdText2Speech(string strFileName,      // 合成文件名称
                                   int nResult = EC_SUCCESS);  // 返回结果
    
    // 控制指定分区播放指定歌曲/TTS
    static string   CmdResponsePlaySpecifiedSource(int nResult);    // 返回结果

    // 歌曲列表排序
    static string CmdResponseSortPlayList(string strListID,           // 列表ID
                                         int nResult = EC_SUCCESS);  // 返回结果

    /////////////////////////////////////////////////////////////////////////////////////////////////
    /*-----------------------          定时管理               ------------------------*/

    // 添加定时点信息
    static      string      CmdResponseAddTimePonit(int nTimeSchemeID,               // 方案序号
                                                     string strComID,                      // 命令ID
                                                     CExTimePoint &tp,                  // 定时点信息
                                                     int  nResult = EC_SUCCESS);    // 返回结果

    // 编辑定时点信息
    static      string      CmdResponseModifyTimePoint(int nTimeSchemeID,              // 方案序号
                                                          string strComID,        // 命令ID
                                                          CExTimePoint &tp,               // 定时点信息
                                                          int nResult = EC_SUCCESS);  // 返回结果

    // 移除定时点信息
    static      string      CmdResponseRemoveTimePoint(int nTimeSchemeID,              // 方案序号
                                                        CExTimePoint &tp,              // 定时点信息
                                                        int nResult = EC_SUCCESS);  // 返回结果

    // 设置定时点分区
    static      string      CmdResponseSetTimeZone(int nTimeSchemeID,            // 方案序号
                                                   string strComID,                 // 命令ID
                                                   CExTimePoint &tp,             // 定时点信息
                                                   int nPage,                             // 指定页数
                                                   int nResult = EC_SUCCESS);  // 返回结果

    // 设置定时点分组
    static      string      CmdResponseSetTimeGroup(int nTimeSchemeID,            // 方案序号
                                                     string strComID,                   // 命令ID
                                                     CExTimePoint &tp,          // 定时点信息
                                                     int nPage,                            // 指定页数
                                                     int nResult = EC_SUCCESS);  // 返回结果

    // 设置定时点歌曲
    static      string      CmdResponseSetTimeSong(int nTimeSchemeID,            // 方案序号
                                                   string strComID,        // 命令ID
                                                   CExTimePoint &tp,          // 定时点信息
                                                   int nPage,                          // 指定页数
                                                   int nResult = EC_SUCCESS);  // 返回结果

// 设置定时点时序器
    static      string      CmdResponseSetTimeSequencePower(int nTimeSchemeID,  // 方案序号
                                                    string strComID,        // 命令ID
                                                    CExTimePoint &tp,   // 定时点信息
                                                    int nPage,                   // 指定页数
                                                    int nResult = EC_SUCCESS);                // 返回结果

// 设置定时点音频采集器
    static      string      CmdResponseSetTimeAudioCollector(int nTimeSchemeID,  // 方案序号
                                                    string strComID,        // 命令ID
                                                    CExTimePoint &tp,   // 定时点信息
                                                    int nPage,                   // 指定页数
                                                    int nResult = EC_SUCCESS);                // 返回结果

    // 禁用/启用指定定时点
    static      string      CmdResponseTimePointValid(int nTimeSchemeID,            // 方案序号
                                                      CExTimePoint &tp,          // 定时点信息
                                                      bool  isValid,                    // 是否有效
                                                      string conflictTimingName,        //冲突的定时点名称（当nResult=EC_TC_TP_CONFLICT时有效）
                                                      int nResult = EC_SUCCESS);  // 返回结果

    // 单次取消/恢复指定定时点
    static      string      CmdResponseTimePointSingleCancel(int nTimeSchemeID,            // 方案序号
                                                      CExTimePoint &tp,          // 定时点信息
                                                      bool  isCancel,                    // 是否单次取消
                                                      int nResult = EC_SUCCESS);  // 返回结果

    // 复制指定定时点
    static      string      CmdCopyTimePoint(int nTimeSchemeID,            // 方案序号
                                              CExTimePoint &tp,          // 定时点信息
                                              int nResult = EC_SUCCESS);  // 返回结果

    // 对定时点进行排序
    static      string      CmdSortTimePoint(   int  nTimeSchemeID,         // 方案序号
                                                int  nSortItem,             // 1:定时点名称 2：状态  3：播放周期  4：开始时间
                                                bool bAscendOrder,          // true: 升序  false：降序
                                                int  nResult = EC_SUCCESS); // 返回结果

    // 添加定时方案
    static      string      CmdResponseAddTimeScheme(int nTimeSchemeID,               // 方案序号
                                                     string  strSchemeName,     // 定时方案名称
                                                     int  nResult = EC_SUCCESS);   // 返回结果

    // 编辑定时方案
    static      string      CmdResponseModifyTimeScheme(int nTimeSchemeID,               // 方案序号
                                                        string  strSchemeName,     // 定时方案名称
                                                        int  nResult = EC_SUCCESS);   // 返回结果

    // 移除定时方案
    static      string      CmdResponseRemoveTimeScheme(int nTimeSchemeID,               // 方案序号
                                                        string  strSchemeName,     // 定时方案名称
                                                        int  nResult = EC_SUCCESS);   // 返回结果

    // 设置当前定时方案
    static      string      CmdResponseSetCurTimeScheme(int nTimeSchemeID,              // 方案序号
                                                        int  nResult = EC_SUCCESS);   // 返回结果

    // 禁用/恢复指定定时方案
    static      string      CmdResponseTimeSchemeValid(int nTimeSchemeID,               // 方案序号
                                                       bool  bValid,                            // 是否有效
                                                       int  nResult = EC_SUCCESS);   // 返回结果

    // 复制指定定时方案
    static      string      CmdCopyTimeScheme(int nTimeSchemeID,               // 方案序号
                                              int  nResult = EC_SUCCESS);   // 返回结果

    // 请求上传定时方案
    static      string      CmdUploadTimeScheme(string strSchName,                   // 定时方案名称
                                                string strServerIP,                     // Web服务器ip
                                                string strServerPort,                 // Web服务器端口
                                                string strUploadPath,               // 上传路径
                                                int      nResult = EC_SUCCESS);             // 返回结果

    // 导入定时方案
    static      string      CmdImportTimeScheme(string strPathName,               // 定时方案相对路径名称
                                                int   nResult = EC_SUCCESS);  // 返回结果

    // 导出定时方案
    static      string      CmdExportTimeScheme(int nSchID,                    // 定时方案ID
                                                string strServerIP,            // Web服务器ip
                                                string strServerPort,          // Web服务器端口
                                                string strPathName,            // 文件路径名称
                                                int    nResult = EC_SUCCESS);  // 返回结果



    ////////////////////////////////////////////////////////////////////////////////////////////////////
    // 设置设备信息
    static      string      CmdResponseSetDeviceInfo(
                                                     int set_type,
                                                     int      nDevModel,    // 设备型号
                                                     string strMac,         // 设备Mac
                                                     string strName,      // 设备名称
                                                     string strMonMac, // 监控设备mac
                                                     int      nResult = EC_SUCCESS);    // 返回结果

    // 查询/设置网络解码播放器EMC状态
    static      string      CmdResponseEMCStatus(string strMac,            // 设备Mac
                                                 RestType rt,               //  请求类型
                                                 bool    bEmcValid = false,             // Emc是否有效，查询时有效
                                                 int       nResult = EC_SUCCESS);    // 返回结果

    // 查询/设置EQ音效
    static      string      CmdResponseEQMode(string strMac,                       // 设备Mac
                                              RestType rt,                           // 请求类型
                                              int      nEQMode = 0,            //  EQ模式
                                              unsigned char nEQGain[10]={0},   //EQ GAIN
                                              int      nResult = EC_SUCCESS); // 返回结果

    // 查询/设置蓝牙参数
    static     string       CmdResponseBlueToothInfo(string strMac,           // 设备Mac
                                       RestType rt,             // 请求类型
                                       string	btName,					        // 蓝牙名称,为NULL时为查询
                                       unsigned char btencryption,             // 蓝牙加密方式
                                       string   btPin,			                // 蓝牙密码
                                       int      nResult = EC_SUCCESS);           // 返回结果

    // 查询/设置混音
    static      string      CmdResponseMixMode(string strMac,                       // 设备Mac
                                               RestType rt,                           // 请求类型
                                               bool   bSetAllZone = false,   // 是否设置到全部分区
                                               int      nChannel = 0,             // 声道：1 单声道   0 双声道(默认)
                                               int      nMixMode = 0,            //  混音模式
                                               int      nAUX = 0,                     //  AUX值
                                               int      nDAC = 0,                   //  DAC值
                                               int      nResult = EC_SUCCESS);  // 返回结果

    // 设置控制模式(程控/手控)
    static      string      CmdResponseSetControlMode(int nSelectID,            //     选择分区ID
                                                      ControlMode cm = CTRL_PROGRAMMABLE,  // 控制模式
                                                      int nResult = EC_SUCCESS);  // 返回结果

    // 暂停播放节目源
    static      string      CmdResponPauseSource(int nSelID,                    // 选择分区ID
                                                 int nResult = EC_SUCCESS);     // 返回结果

    // 查询设置信息发布参数
    static      string      CmdResponseInformationPublishInfo(string strMac,      // 设备mac地址
                                                    int  nSet,                    // 0:查询 1:设置
                                                    bool bEnableDisplay,          // 是否启用显示
                                                    string strText,                 //输出文本
                                                    int nEffects,                 // 特效
                                                    int nMoveSpeed,               // 移动速度
                                                    int nStayTime,                // 停留时间 
                                                    int nResult = EC_SUCCESS);    // 返回结果

    // 查询sip登录信息
    static      string      CmdResponseSipLoginInfo(string strMac,                    // 设备mac地址
                                                    int  nSet,                      // 0:查询 1:设置
                                                    bool bEnableSip,                 // 是否启用SIP
                                                    int outputVolume,               //输出音量
                                                    string strSipAccount,        // sip登录账号
                                                    string strSipPassword,     // sip登录密码
                                                    string strServerIP,            // sip服务器地址
                                                    int strServerPort,        // sip服务器端口
                                                    int nServerProtocol,         //传输协议
                                                    int nSipStatus,                  // sip会话状态
                                                    int nResult = EC_SUCCESS);    // 返回结果

    // sip账号登录
    static      string      CmdResponseSipLogin(string strMac,      // 设备mac地址
                                               int nResult = EC_SUCCESS);    // 返回结果

    // 服务器通知客户端设备文件需要同步
    static    string    CmdNotifyNeedSyncFile(string strMac,    // 设备Mac地址
                                             FileType ft,         //   文件类型
                                             string strFileDateTime,    // 文件时间
                                             DeviceModel model);      // 设备类型

    // 客户端请求服务器需要更新设备文件 回复
    static    string    CmdResponseUpdateDeviceFile(string strMac,             //  设备Mac地址
                                                    DeviceModel model,         //  设备类型
                                                    FileType ft,               //   文件类型
                                                    int       nSyncStatus,     //   同步状态
                                                    int       nResult = EC_SUCCESS,     //   返回结果
                                                    bool    IsSongStatus = true,        //   是否是歌曲状态，如果不是则去掉同步歌曲参数
                                                    int       nSongCount = 0,  //   一共需要同步的歌曲数目
                                                    int       nSyncCount = 0,  //   已完成同步的歌曲数目
                                                    int       nPercent = 0);   //   正在同步歌曲的百分比



    // 客户端请求中止同步文件 回复
    static     string    CmdResponseStopSyncFile(string strMac,                     // 设备Mac地址
                                                 bool   bAllzone,                   // 是否中止全部分区
                                                 int    nResult = EC_SUCCESS);      // 返回结果

    // 获取服务器上传路径下的所有固件 回复
    static     string    CmdResponseGetServerAllFirmware(   int nPageCount,             // 总页数
                                                           int nPage,                   // 指定页数
                                                           int nFirmwareCount,          // 此页包含的固件名称数量，最多100个固件名称
                                                           string* strFirmwareNames,    // 固件名称数组
                                                           int nResult = EC_SUCCESS);   // 返回结果

    // 请求上传固件到服务器 回复
    static     string     CmdResponseUploadFirmwareToServer(string strFirmwareName,     // 固件名称
                                                            string strServerIP,         // 服务器ip
                                                            string strServerPort,       // 服务器端口
                                                            string strUploadPath,       // 上传路径
                                                            int nResult = EC_SUCCESS);  // 返回结果

    // 设备固件升级 回复
    static     string     CmdResponseUpgradeDevice(string  strMac,          // 设备Mac地址
                                                   int     nStatus,         // 更新状态
                                                   int     nPercent,        // 升级进度百分比
                                                   int nResult = EC_SUCCESS);   // 返回结果

    // 终端日志列表获取 回复
    static      string      CmdResponseDevLogList(CSection& device,
                                                  int    nResult = EC_SUCCESS);  // 返回结果

    // 终端日志下载进度
    static      string      CmdDevLogDown(string strMac,            // 设备Mac地址
                                          string strFileName,       // 下载文件大小
                                          int    nCompletedSize,    // 已下载文件大小
                                          int    nTotalSize,          // 总文件大小
                                          int    nResult = EC_SUCCESS);  // 返回结果

    // 查询/设置网络解码播放器电源输出模式(9131/9138)
    static      string      CmdPowOutputMode(string strMac,        // 设备Mac地址
                                             int nSet,             // 0:查询 1:设置
                                             int nPowerMode,       // 电源模式
                                             int nTimeOut,         // 超时时间
                                             int nResult = EC_SUCCESS);  // 返回结果

    // 查询/设置网络解码分区器输出状态（9138）
    static      string      CmdSplitterStatus(string strMac,       // 设备Mac地址
                                              int nSet,            // 0:查询 1:设置
                                              int nChannelStatus,  // 通道状态
                                              int nResult = EC_SUCCESS);  // 返回结果

    // 4.97查询/设置 DSP6636无线MIC控制面板状态）
    static      string      CmdMicStatus(string strMac,                 // 设备Mac地址
                                            int nSet,                   // 0:查询 1:设置
                                            int volume,                 // 音量
                                            int power,                  // 功率
                                            int channel,                // 通道
                                            int nResult = EC_SUCCESS);  // 返回结果

// GPS校时器
    // 查询/设置GPS时区
    static      string      CmdResponseGPSTimeZone(string strMac,               // 设备Mac
                                                   RestType rt,                 // 请求类型
                                                   int      nTimeZone = 0,      // 时区设置
                                                   bool   bDst = false,         // 是否夏时令
                                                   int      nResult = EC_SUCCESS);  // 返回结果

// 消防采集器
    // 设置消防采集器
    static      string      CmdSetFireCollector(string strComID,            // 命令ID
                                                string strMac,              // 设备mac
                                                string strName,             // 设备名称
                                                int      nChannelCount,     // 消防通道数量
                                                int  nResult = EC_SUCCESS); // 返回结果

    // 设置消防通道
    static      string      CmdSetFireChannel(string strComID,             // 命令ID
                                              string strMac,               // 设备mac
                                              int isSetAllchannel,         // 设置所有通道
                                              CExtraChannel channel,       // 通道数据
                                              int nResult = EC_SUCCESS);   // 返回结果

    // 设置电源时序器
    static      string      CmdSetSequencePower(
                                       string strMac,             // 设备mac
                                       int nResult = EC_SUCCESS);               // 返回结果
#if SUPPORT_REMOTE_CONTROLER        
    // 设置远程遥控器任务
    static      string      CmdSetRemoteControlerTask(string strMac,            // 设备mac
                                       int nEvent,                              //事件
                                       int nResult = EC_SUCCESS);               // 返回结果

    // 设置远程遥控器按键
    static      string      CmdSetRemoteControlerKey(string strMac,             // 设备mac
                                       int nResult = EC_SUCCESS);               // 返回结果
#endif

#if SUPPORT_AUDIO_MIXER
// 设置音频混音器参数
    static      string      CmdSetAudioMixerParm(string strMac,     // 设备mac
                                        int nSet,                   // 0:查询 1:设置
                                        int nMasterSwitch,          // 混音主开关（0关闭 1开启）
                                        int nPriority,              // 优先级
                                        int nTriggerType,           // 触发类型
                                        int nTriggerSensitivity,    // 触发灵敏度
                                        int nVolumeFadeLevel,       // 音量淡化级别
                                        int nZoneVolume,            // 分区音量
                                        vector<CMyString> &vecSecMacs,  //分区数组
                                        int nResult = EC_SUCCESS);      // 返回结果
#endif

#if SUPPORT_PHONE_GATEWAY
// 设置电话网关参数
    static      string      CmdSetPhoneGatewayParm(string strMac,     // 设备mac
                                        int nSet,                   // 0:查询 1:设置
                                        int nMasterSwitch,          // 混音主开关（0关闭 1开启）
                                        int nZoneVolume,            // 分区音量
                                        vector<CMyString> &vecSecMacs,  //分区数组
                                        string strTelWhitelist,    //电话白名单
                                        int nResult = EC_SUCCESS);      // 返回结果
#endif

#if SUPPORT_AMP_CONTROLER
    static string CmdSetAmpControler(string strMac,             // 设备mac
                                        int nSet,                   // 0:查询 1:设置
                                        unsigned char *MasterChannelStatus,     // 主通道状态
                                        unsigned char backupChannelStatus,      // 备份通道状态
                                        unsigned char backupChannelId,          // 备份通道ID
                                        int nResult = EC_SUCCESS);      // 返回结果
#endif

#if SUPPORT_NOISE_DETECTOR
    static string CmdSetNoiseDetector(string strMac,             // 设备mac
                                        int nSet,                   // 0:查询 1:设置
                                        CNoiseDetector &noiseDetector,
                                        int nResult = EC_SUCCESS);      // 返回结果
#endif

// 设置音采集器参数
    static      string      CmdSetAudioCollectorParm(string strMac,     // 设备mac
                                        int nSet,                   // 0:查询 1:设置
                                        int nTriggerSwitch,          // 触发开关（0关闭 1开启）
                                        int nTriggerChannel,              // 触发通道id（1~4）
                                        int nTriggerZoneVolume,           // 触发类型（触发分区音量）
                                        int priority,                     // 优先级(1默认,小于定时; 2高优先级，大于定时)
                                        vector<CMyString> &vecSecMacs,  //分区数组
                                        vector<string> channelNameArray,      //通道名称数组
                                        int nResult = EC_SUCCESS);      // 返回结果

    // 查询/设置IP属性
    static      string      CmdDeviceIP(string strMac,              // 设备Mac
                                        int nDeviceModel,           // 设备类型
                                        RestType rt,                // 请求类型
                                        int nIPMode,                // IP模式
                                        string strIP,               // ip地址
                                        string strSubnet,           // 子网掩码
                                        string strGatway,           // 默认网关
                                        string strDNS1,             // 首选DNS
                                        string strDNS2,             // 备用DNS
                                        int  nResult = EC_SUCCESS); // 返回结果

    // 查询/设置网络模式
    static      string      CmdDeviceNetworkMode(string   strMac,   // 设备Mac
                                 int      nDeviceModel,    // 设备类型
                                 RestType rt,              // 请求类型
                                 int      all_model_device, //该类型的所有设备
                                 int      nNetworkMode,    // IP模式
                                 string   strServerIP,     // 服务器地址地址
                                 int   serverPort,         // 服务器端口
                                 string   strServerIP2,     // 备用服务器地址地址
                                 int   serverPort2,         // 备用服务器端口
                                 int      nResult = EC_SUCCESS);        // 返回结果

    // 获取运行日志
    static      string      CmdGetRunlog(string strServerIP,          // Web服务器ip
                                         string strServerPort,        // Web服务器端口
                                         string strFilePath,          // 文件路径
                                         int nResult = EC_SUCCESS);   // 返回结果

    // 导出运行日志
    static      string      CmdExportRunlog(string strServerIP,          // Web服务器ip
                                            string strServerPort,        // Web服务器端口
                                            string strFilePath,          // 文件路径
                                            int nResult = EC_SUCCESS);   // 返回结果
#if SUPPORT_CALL_RECORD
    // 查询通话日志
    static      string      CmdQueryCallLog(const vector<RecordTableData>& vecRecordData,  // 通话记录数据
                                            int nResult = EC_SUCCESS);   // 返回结果
    // 删除通话日志
    static      string      CmdDeleteCallLog(const string& strCallId,     // 通话ID
                                             int nResult = EC_SUCCESS);   // 返回结果

    // 获取系统日志
    static      string      CmdGetSystemLog(string strServerIP,          // Web服务器ip
                                            string strServerPort,        // Web服务器端口
                                            string strFilePath,          // 文件路径
                                            int nResult = EC_SUCCESS);   // 返回结果
#endif
    // 重置设备数据 回复
    static      string      CmdResponseResetDeviceData(string strMac,    // 设备Mac地址
                                                       int   nResult = EC_SUCCESS); // 返回结果

    // 重启设备 回复
    static      string      CmdResponseRestartDevice(string   strMac,                         // 设备Mac地址
                                                     int   nResult = EC_SUCCESS);   // 返回结果
    // 分区自定义排序
    static      string      CmdResponseSortZoneCustom(int   nResult);           // 返回结果

    //删除设备
    static      string      CmdResponseDeleteDevice(int nDevModel, string strMac,int nResult);    // 返回结果

// 高级设置
    // 查询/设置当前高级设置
    static      string      CmdAdvancedSetting(RestType rt,                  // 请求类型
                                               int nWorkPattern,             // 工作模式
                                               int nNetMode,                 // 网络模式
                                               string strServerIP,           // 服务器网络地址
                                               int      nAudiocast,          // 音频传输：1 组播 2 单播
                                               int      nSyncCount,          // 同时同步设备数
                                               int      nPlayChannel,        // 播放通道: 0 从设备播放 1 从HTTP播放
                                               int      nSongFrom,           // 歌曲来自：0 服务器本地   1 http服务器
                                               string strSongServerIP,       // 歌曲服务器IP
                                               string strSongServerPort,     // 歌曲服务器端口
                                               int  nResult = EC_SUCCESS);   // 返回结果

    // 设置系统时间
    static      string     CmdResponseSetSystemDateTime(int      nSet,                // 1：设置   0：查询
                                                        string strDateTime,           // 当前时间
                                                        string strDataBootTime,       //启动时间  
                                                        int nResult = EC_SUCCESS);    // 返回结果
    // 查询系统磁盘信息
    static      string      CmdResponseGetSystemStorage(
                                                 int hardDisk_total,       // 磁盘总大小
                                                 int hardDisk_remain,   //剩余磁盘大小
                                                 int memory_total,         //总内存大小
                                                 int memory_remain,        //剩余内存大小
                                                 int nResult);              //内存总大小

    static      string CmdResponseSetSystemNetwork(
                                                 int    nSet,            // 1：设置   0：查询
                                                 string ip_address,      //IP地址
                                                 string subnet_mask,     //子网掩码
                                                 string gateway,         //网关
                                                 string dns_server,      //dns
                                                 int nResult);           //result

    // 查询/设置系统主备服务器
    static      string CmdResponseSetBackupServer(
                                                 int    nSet,               // 1：设置   0：查询
                                                 bool isEnable,             //是否启用主备服务器
                                                 string dest_server_ip,   //备用服务器IP
                                                 int nResult = EC_SUCCESS);              //result

    // 重置服务器数据 回复
    static      string     CmdResponseResetServerData(int nFileFlags,                         // 文件类型标识
                                                      int   nResult = EC_SUCCESS); // 返回结果

    // 备份服务器数据 回复
    static      string     CmdResponseBackupServerData(int   nType,        // 备份类型
                                                       int   nResult = EC_SUCCESS); // 返回结果

    // 获取服务器备份文件名称
    static      string     CmdGetBackupFileName(vector<string>& vecNames,       // 备份文件名称数组
                                                int nResult = EC_SUCCESS);        // 返回结果

    // 还原服务器数据 回复
    static      string     CmdResponseRestoreServerData(string strFileName,          // 文件名称
                                                        int   nResult = EC_SUCCESS); // 返回结果

    // 移除备份文件 回复
    static      string     CmdResponseRemoveBackupFile(string strFileName,          // 文件名称
                                                       int  nResult = EC_SUCCESS);  // 返回结果

    // 上传数据 回复
    static      string     CmdResponseUploadBackupFile(string strFileName,        // 文件名称
                                                       int nResult = EC_SUCCESS); // 返回结果

    // 恢复出厂设置
    static      string     CmdFactoryReset(int   nResult = EC_SUCCESS); // 返回结果

    // 请求上传升级包到服务器
    static     string     CmdResponseUploadUpgradeBagToServer(string strUpgrageBagName,   // 升级包名称
                                                                                                           string strServerIP,               // 服务器ip
                                                                                                           string strServerPort,           // 服务器端口
                                                                                                           string strUploadPath,         // 上传路径
                                                                                                           int nResult = EC_SUCCESS);   // 返回结果

    // 请求升级服务器
    static      string      CmdResponseUpgradeServer(string strUpgrageBagName,   // 升级包名称
                                                                                      int nResult = EC_SUCCESS);   // 返回结果

    // 请求重启服务器
    static      string      CmdResponseRebootServer(int nResult = EC_SUCCESS);  // 返回结果

    // 关于服务器
    static      string      CmdServerAbout(string strVersion,          // 服务器版本
                                           string strMac,              // 服务器MAC地址
                                           int    nAPPType,            // 程序类型
                                           string strMachineCode,      // 机器码
                                           int nResult = EC_SUCCESS);  // 返回结果

    // 服务器注册
    static      string      CmdResponseRegisterServer(int nResult = EC_SUCCESS);  // 返回结果
#if APP_IS_LZY_COMMERCE_VERSION
    // TTS授权注册
    static      string      CmdResponseTTSRegister(int nResult = EC_SUCCESS);  // 返回结果
    // TTS试用申请
    static      string      CmdResponseTTSTrial(int nResult = EC_SUCCESS, bool bApplyTrial=false, int nRemainingDays = 0);  // 返回结果
#endif

    static      string      CmdResponseCloudControlRegister(int nResult = EC_SUCCESS);  // 返回结果
/****************************************************/
// 其他

    // 分控设备上线（主要用于UDP）
    static  string   CmdSearchControlDevice();

    // 用户验证失败，返回
    static  string   CmdUserAuthFailure(int nResult, string strCommand);

/************播放任务管理****************************/
    // 设置任务播放模式
    static string CmdResponse_SetPlayTask_PlayMode(int nResult);
    // 设置任务播放状态
    static string CmdResponseSetPlayTask_PlayStatus(int nResult);
    // 设置任务播放上下曲
    static string CmdResponseSetPlayTask_PreNext(int nResult);

    #if SUPPORT_MANUALTASK_PROGRESS
    // 设置任务播放进度
    static string CmdResponseSetPlayTask_Progress(int  nSet,        // 0:查询 1:设置
                                                  int nPlayId,      //播放id
                                                  int nPlayTime, //当前播放时间
                                                  int nResult); //结果
    #endif
};



#endif // WEBPROTOCOL_H



