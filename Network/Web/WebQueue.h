#ifndef WEBQUEUE_H
#define WEBQUEUE_H

#include <iostream>
#include <vector>
#include <map>
#include <list>
#include "Model/Device/Section.h"
#include "Model/Device/WebSection.h"
#include "Model/Other/VoipSipInfo.h"

using namespace std;

class CVOIPTalkback
{
public:
    CVOIPTalkback();
    void     AddTalkback(string strExten1, string strExten2);

    string   m_strExten1;       // 开始对讲号码1
    string   m_strExten2;       // 开始对讲号码2
};

enum
{
    TYPE_9312 = 0,
    TYPE_OTHER
};

class CVOIPPaging
{
public:
    CVOIPPaging();
    void AddPaging(string strMulSip, int nPageTpye, int nVolume, int nExtenCount, string*  strExtens, int dt=TYPE_OTHER);

    string      m_strMulSip;            // 开始广播sip账号
    int         m_nDeviceType;          // 设备类型
    char        m_szMulIP[MAX_IP_LEN];  // 广播组播IP地址
    int         m_nPageType;            // 寻呼类型  1 : 组播  2: 单播
    int         m_nVolume;              // 寻呼音量
    vector<string>       m_vecExtens;   // 广播sip账号
};

class CVOIPMonitor
{
public:
    CVOIPMonitor();
    void     AddMonitorQ(string strExten1, string strExten2);

    string   m_strExten1;       // 开始监听号码1
    string   m_strExten2;       // 开始监听号码2
};


/*
 * 处理Web控制设备发送命令到终端时终端的回复
 */

/*--------------            保存命令队列信息                    ----------------*/

class   CWebQueue
{
public:
// Web端命令
    CWebQueue(string strCommand, string  strSocketID);

// 标准协议命令
    CWebQueue(unsigned short uCmd, string strUID, string strMac);

    /////////////////
    LPCWebSection GetWebSection();

public:
    string    m_strCommand;             // Web协议命令
    ushort    m_uCommand;               // 标准协议命令
    string    m_strSocketID;              //  socket的UUID

    CVOIPTalkback   m_Talkback;         // 对讲
    CVOIPPaging     m_Paging;           // 广播
    CVOIPMonitor    m_Monitor;          // 监听

    char      m_szSecMac[MAX_MAC_LEN];  // 发送到指定设备的MAC地址
};

typedef  CWebQueue*   LPCWebQueue;

class   CWebQueues
 {
public:
    CWebQueues();
    ~CWebQueues();

    int     GetQueueCount()           { return m_WebQueues.size(); }
    void    AddWebQueue(string strComID, LPCWebQueue pWebQueue);

    void    AddCommand(string strCmd, string strSocketID, string strComID);
    void    AddCommand1(unsigned short uCmd, string strSocketID, string strMac);

    void    RemoveQueue(string  strComID);

    // 收到sip回复，执行组播操作
    void    ExecutePage(string strComID, string strRtpType, int nResult);

    // 判断是否有正在发起组播或监听的sip账号，如果有，则把所有该组播或监听的sip全部挂断（设置成空闲）
    void    CheckSipPage(int nSipCount, SipInfo* pSipInfo, bool bRes = false);

    // 写日志 LogType lt,
    void    InsertLog(string strComID, int nResult, string hupExten = "");

public:
    // sip服务器回复Web终端
    void    ReponseCommand(string strComID, int nResult, string strExten = "");

    static   int     GetnRtpType(string strRtpType);

private:

public:
    bool          IsExistQueue(string strComID);
    LPCWebQueue   GetWebQueueByComID(string strComID);
    LPCWebSection GetWebSectionByComID(string strComID);

private:
    pthread_mutex_t   m_csCommand;

    map<string, LPCWebQueue>    m_WebQueues; // 命令ID与发送队列映射(SIP服务器)   // 处理命令复杂操作

    list<LPCWebQueue>    m_SecQueues;        // 终端命令发送队列(to设备)

    map<int, string>  m_ErrorToField;
    map<int, string>  m_ErrorToDesc;
};



#endif // WEBRESPONSE_H
