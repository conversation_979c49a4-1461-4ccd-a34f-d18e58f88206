#include "stdafx.h"
#include "WebNetwork.h"
#include "Model/Device/Section.h"
#include "Network/Protocol/CommandSend.h"
#include "Network/Protocol/CommandHandle.h"
#include "Network/Web/WebProtocol.h"


CWebNetwork::CWebNetwork()
{

}

CWebNetwork::~CWebNetwork()
{

}

void CWebNetwork::StartWebWorking()
{
    //m_WebSock.WebRun();
    m_QWebSock.StartWebsocket();
    #if SUPPORT_ADVANTAGE_MINIAPP
    if(g_Global.b_cloudControl_Authorized)
    {
        printf("Cloud Control Client StartWorking\n");
        m_WsQTransferClient.StartWorking();
    }
    else
    {
        printf("Cloud Control Client not authorized\n");
    }
    #endif
}

// 分发今日定时点信息到WEB
void CWebNetwork::ForwardTodayTimerInfoToWeb(CWebSection *pWebSection, vector<StTodayTimerP> TimerVec)
{
    if(pWebSection == NULL)
    {
        //发送到所有web，需要后再处理
        int nWebCount    = g_Global.m_WebSections.GetWebCount();      
        for(int i=0; i<nWebCount; i++)
        {
            CWebSection* pWebSec = g_Global.m_WebSections.GetWebSection(i);
            if(pWebSec)
            {
                m_WebSend.WebSendTodayTimerInfo(pWebSec, TimerVec);
            }
        }
    }
    else
    {
        m_WebSend.WebSendTodayTimerInfo(pWebSection, TimerVec);
    }
}


// 分发手动任务信息到WEB
void CWebNetwork::ForwardManualTaskInfoToWeb(CWebSection *pWebSection, vector<StManualTask> manualTaskVec)
{
    if(pWebSection == NULL)
    {
        //发送到所有web，需要后再处理
        int nWebCount    = g_Global.m_WebSections.GetWebCount();      
        for(int i=0; i<nWebCount; i++)
        {
            CWebSection* pWebSec = g_Global.m_WebSections.GetWebSection(i);
            if(pWebSec)
            {
                m_WebSend.WebSendManualTaskInfo(pWebSec, manualTaskVec);
            }
        }
    }
    else
    {
        m_WebSend.WebSendManualTaskInfo(pWebSection, manualTaskVec);
    }
}

// 分发本地歌曲信息到WEB
void CWebNetwork::ForwardLocalSongInfoToWeb(CWebSection *pWebSection)
{
    if(pWebSection == NULL)
    {
        //发送到所有web，需要后再处理
        int nWebCount    = g_Global.m_WebSections.GetWebCount();      
        for(int i=0; i<nWebCount; i++)
        {
            CWebSection* pWebSec = g_Global.m_WebSections.GetWebSection(i);
            if(pWebSec)
            {
                m_WebSend.WebSendLocalSongInfo(pWebSec);
            }
        }
    }
    else
    {
        m_WebSend.WebSendLocalSongInfo(pWebSection);
    }
}

// 分发分区状态到所有webSection，如果pSection为空，则分发全部分区数据
void CWebNetwork::ForwardSectionInfoToWeb(int nDeviceType, CSection *pSection, CWebSection *pWebSection, BOOL isPrint)
{
    if(pSection == NULL)
    {
        //发送到所有web，需要后再处理
        if(pWebSection == NULL)
        {
            int nWebCount    = g_Global.m_WebSections.GetWebCount();      
            for(int i=0; i<nWebCount; i++)
            {
                CWebSection* pWebSec = g_Global.m_WebSections.GetWebSection(i);
                if(pWebSec)
                {
                    m_WebSend.WebSendAllZoneInfo_Once(nDeviceType, pWebSec);
                }
            }
        }
        else
        {
            m_WebSend.WebSendAllZoneInfo_Once(nDeviceType, pWebSection);
        }
    }
    else
    {
        //发送到所有web，需要后再处理
        if(pWebSection == NULL)
        {
            int nWebCount    = g_Global.m_WebSections.GetWebCount();      
            for(int i=0; i<nWebCount; i++)
            {
                CWebSection* pWebSec = g_Global.m_WebSections.GetWebSection(i);
                if(pWebSec)
                {
                    m_WebSend.WebSendOneZoneInfo(nDeviceType, pSection, pWebSec, isPrint);
                }
            }
        }
        else
        {
            m_WebSend.WebSendOneZoneInfo(nDeviceType, pSection, pWebSection, isPrint);
        }
    }

}


// 分发变化的分区状态到webSection
void CWebNetwork::ForwardVarySectionInfoToWeb(int nDeviceType, vector<string> &vecSectionMac, CWebSection *pWebSection, BOOL isPrint)
{
    //发送到所有web，需要后再处理
    if(pWebSection == NULL)
    {
        int nWebCount    = g_Global.m_WebSections.GetWebCount();      
        for(int i=0; i<nWebCount; i++)
        {
            CWebSection* pWebSec = g_Global.m_WebSections.GetWebSection(i);
            if(pWebSec)
            {
                m_WebSend.WebSendVaryZoneInfo(nDeviceType, vecSectionMac, pWebSec, isPrint);
            }
        }
    }
    else
    {
        m_WebSend.WebSendVaryZoneInfo(nDeviceType, vecSectionMac, pWebSection, isPrint);
    }
}

// 服务器分发更新文件信息到所有webSection
void CWebNetwork::ForwardUpdateFileInfo(FileType ft)       // 文件类型
{
    // 如果ft为0，则分发全部文件
    if(ft == FILE_ALL)
    {
        int nWebCount    = g_Global.m_WebSections.GetWebCount();

        for(int i=0; i<nWebCount; i++)
        {
            LPCWebSection pWebSec = g_Global.m_WebSections.GetWebSection(i);
            if(pWebSec)
            {
                for(int j=0; j<DT_COUNT; j++)   // 1-6
                {
                    m_WebSend.SendFileInfo(*pWebSec, j+1);          // 不必判断websocket是否在线(TCP连接)，掉线会清除
                }
            }
        }
    }
    // 如果ft为>0，则分发指定文件
    else if(ft != FILE_SECTION)
    {
        int nWebCount    = g_Global.m_WebSections.GetWebCount();
        for(int i=0; i<nWebCount; i++)
        {
            LPCWebSection pWebSec = g_Global.m_WebSections.GetWebSection(i);
            if(pWebSec)
            {
                m_WebSend.SendFileInfo(*pWebSec, ft);
            }
        }
    }
}

void CWebNetwork::ForwardPlayMode(CWebSection *pWebSection,
                                  int nPlayMode)
{
    string strBuf = CWebProtocol::CmdResponseChangePlayMode(WEB_SET, nPlayMode);
    m_WebSend.ForwardDataToWeb(pWebSection, strBuf);
}

void CWebNetwork::ForwardPlayMode(string strUserAccount,
                                  int nPlayMode)
{   //WEB端只有SET的应答才会变化
    string strBuf = CWebProtocol::CmdResponseChangePlayMode(WEB_SET, nPlayMode);

    vector<LPCWebSection> vecWebSection;
    g_Global.m_Users.GetWebSectionArrayByAccount(strUserAccount,vecWebSection);
    for(int i=0;i<vecWebSection.size();i++)
    {
        m_WebSend.ForwardDataToWeb(vecWebSection[i], strBuf);
    }
}

void CWebNetwork::ForwardSystemDateTime(CWebSection *pWebSection,
                                        bool   bAutoMode,      // 自动设置时间
                                        string strDateTime)    // 日期时间
{
    //string strBuf = CWebProtocol::CmdResponseSetSystemDateTime(REST_GET, bAutoMode, strDateTime);
    //m_WebSend.ForwardDataToWeb(pWebSection, strBuf);
}

// 请求用户重新登录
void CWebNetwork::RequestUserReLogin(string strUserAccount, int nStatus,int userCnt)
{
    string strBuf = CWebProtocol::CmdResquestUserRelogin(nStatus);

    // 用户名为空，则发送到全部用户
    if(strUserAccount.empty())
    {
        m_WebSend.ForwardDataToWeb(NULL, strBuf.data());
    }
    // 发送到指定用户的所有socket
    else
    {
        vector<LPCWebSection> vecWebSection;
        int closeCnt=0;
        g_Global.m_Users.GetWebSectionArrayByAccount(strUserAccount,vecWebSection);
        vector<LPCWebSection>::iterator iter = vecWebSection.begin();
        for(; iter != vecWebSection.end() && closeCnt<userCnt; iter++)
        {
            closeCnt++;
            LPCWebSection pWebSection = *iter;
            printf("RequestUserReLogin:close websocket ID:%s\n",pWebSection->GetSocketID().data());
            //一定要先通知WEB让其主动关闭，否则直接关闭socket的话会造成被关闭的websocket又再次尝试重连。
            g_Global.m_WebNetwork.m_WebSend.CommandSend(strBuf.data(), *pWebSection, true, true);
            //g_Global.m_WebNetwork.m_QWebSock.AddCommandBuf(pWebSection->GetSocketID(), "");
        }

        #if 0
        vector<string> vecUID;
        g_Global.m_Users.GetUIDArrayByAccount(strUserAccount,vecUID);
        //printf("vecUID.size()=%lu\n",vecUID.size());
        int closeCnt=0;
        for(int i=0; i<vecUID.size() && closeCnt<userCnt; i++)
        {
            LPCWebSection pWebSection = g_Global.m_WebSections.GetWebSectionBySocketID(vecUID.at(i));
            if(pWebSection != NULL)
            {
                //m_WebSend.ForwardDataToWeb(pWebSection, strBuf.data());
                g_Global.m_WebNetwork.m_WebSend.CommandSend(strBuf.data(), *pWebSection, true, true);
                // 网页需断开Websocket连接，应该延迟一下再发送，否则可能收不到
                if(pWebSection->GetWebType() == WT_QT)
                {
                    closeCnt++;
                    printf("close websocket ID:%s\n",pWebSection->GetSocketID().data());
                    //emit g_Global.m_WebNetwork.m_QWebSock.signal_CloseSocket(pWebSection->GetSocketID(),true);
                    g_Global.m_WebNetwork.m_QWebSock.AddCommandBuf(pWebSection->GetSocketID(), "");
                }
            }
        }
        #endif
    }
}

// 分发所有监控设备信息
void CWebNetwork::ForwardMonitorInfo(CWebSection  *pWebSection,
                                     CMonitorInfo *pMonitorInfo)
{
    if(pMonitorInfo == NULL)     // 如果pMonitorInfo为空，则发送所有监控数据
    {
        int nMonitorCount = g_Global.m_Monitors.GetMonitorCount();

        LPCMonitorInfo  pMonitors[nMonitorCount];
        for(int i=0; i<nMonitorCount; i++)
        {
            pMonitors[i] = &g_Global.m_Monitors.GetMonitor(i);
        }
        m_WebSend.WebForwardMonitorInfo(pWebSection, nMonitorCount, pMonitors);
    }
    else
    {
        LPCMonitorInfo  pMonitors[1];
        pMonitors[0] = pMonitorInfo;
        m_WebSend.WebForwardMonitorInfo(pWebSection, 1, pMonitors);
    }
}

// 设备网络质量分发
void CWebNetwork::ForwardNetQuality(string strMac,       // 设备Mac地址
                                    string strName,      // 设备名称
                                    int nNetType,        // 网络类型
                                    double dBandWidth,   // 带宽
                                    double dDelayed,     // 延时
                                    double dLossRate)    // 丢包率
{
    string strBuf = CWebProtocol::CmdForwardNetQuality(strMac, StringToUTF8(strName.data()), nNetType, dBandWidth, dDelayed, dLossRate);

    m_WebSend.ForwardDataToWeb(NULL, strBuf, false, false);
}


// 向客户端分发监控设备的 监控事件
void CWebNetwork::ForwardMonitorEvent(CWebSection *pWebSection,
                                      const char     *szMac,
                                      TriggerEvent &triggerEvent)
{
    string strBuf = CWebProtocol::CmdForwardMonitorEvent(szMac,
                                                         triggerEvent.eventType,
                                                         triggerEvent.strDateTime,
                                                         triggerEvent.direction);

    m_WebSend.ForwardDataToWeb(pWebSection, strBuf, false, false);
}

// 分发sip信息到web设备
void CWebNetwork::ForwardSipInfo(int nPageCount,
                                 int nPage,
                                 int nSipCount,
                                 SipInfo *pSipInfos,
                                 CWebSection *pWebSection)
{
    if(pSipInfos == NULL)       // sip数据为空，则返回
    {
        LOG("pSipInfos is NULL", LV_INFO);
        return;
    }

    string strBuf = CWebProtocol::CmdResponseSipInfo(nPageCount, nPage, nSipCount, pSipInfos);
    m_WebSend.ForwardDataToWeb(pWebSection, strBuf, false, false);
}

// 下发SIP状态到设备
void CWebNetwork::ForwardSipStatus(CSection& device,      // 设备
                                   CWebSection  *pWebSection)    // WebSection，为空时，发送到全部Web终端
{
    string strBuf = CWebProtocol::CmdResponseSipLoginInfo(device.GetMac(),
                                                          0,
                                                          device.m_SipInfo.m_bEnableSIP,
                                                          device.m_SipInfo.m_nSipOutputVol,
                                                          device.m_SipInfo.m_szAccount,
                                                          device.m_SipInfo.m_szPassword,
                                                          device.m_SipInfo.m_szServerAddr,
                                                          device.m_SipInfo.m_nServerPort,
                                                          device.m_SipInfo.m_nServerProtocol,
                                                          device.m_SipInfo.m_nSipStaus);
    
    int nWebCount    = g_Global.m_WebSections.GetWebCount();      

    for(int i=0; i<nWebCount; i++)
    {
        CWebSection* pWebSec = g_Global.m_WebSections.GetWebSection(i);
        if(pWebSec)
        {
            LPCUserInfo pDestUser =  g_Global.m_Users.GetUserByID(pWebSec->GetSocketID());
            if(pDestUser == NULL)
                continue;
            //******** 如果不是管理员用户或者当前用户没有主叫和被叫设备的管理权限，那么返回
            if(pDestUser->IsSuperUser() || pDestUser->HasFoundSectionMac(device.GetMac()))
            {
                m_WebSend.ForwardDataToWeb(pWebSec, strBuf, false, false);
            }
        }
    }
}

// 下发设备文件需要更新信息
void CWebNetwork::ForwardDeviceNeedUpdateFile(CWebSection *pWebSection, FileType ft, string strFileDateTime, string strMac, DeviceModel model)
{
    int nWebCount    = g_Global.m_WebSections.GetWebCount();

    string strBuf = CWebProtocol::CmdNotifyNeedSyncFile(strMac, ft ,strFileDateTime, model);

    if(pWebSection == NULL)
    {
        for(int i=0; i<nWebCount; i++)
        {
            CWebSection* pWebSec = g_Global.m_WebSections.GetWebSection(i);
            if(pWebSec)
            {
                // 只有网页可以同步文件信息，所以只发送到浏览器端
                if(pWebSec->NeedUpdateFile(strMac, ft, strFileDateTime) && pWebSec->GetWebType()==WT_QT
                && pWebSec->IsSuperUser() )
                {
                    m_WebSend.ForwardDataToWeb(pWebSec, strBuf, false, false);
                }
            }
        }
    }
    else
    {
        if(pWebSection->NeedUpdateFile(strMac, ft, strFileDateTime) && pWebSection->GetWebType()==WT_QT && pWebSection->IsSuperUser())
        {
            m_WebSend.ForwardDataToWeb(pWebSection, strBuf, false, false);
        }
    }

}

// 下发设备文件更新实时状态
void CWebNetwork::ForwardDeviceFileStatus(CWebSection *pWebSection,       // Web端
                                          DeviceModel model,                 // 设备类型
                                          FileType ft,                             //  文件类型
                                          string     strMac,                    //  Mac地址
                                          int          nSyncStatus)           //  同步状态
{
//    LOG("**********************************", LV_INFO);
//    LOG("******             设备文件更新状态                 *****", LV_INFO);
//    LOG("**********************************", LV_INFO);

    string strBuf = CWebProtocol::CmdResponseUpdateDeviceFile(strMac, model, ft, nSyncStatus);
    m_WebSend.ForwardDataToWeb(pWebSection, strBuf, false, false);
}

// 下发设备歌曲同步状态
void CWebNetwork::ForwardUpdateSongStatus(CWebSection *pWebSection,   // Web终端
                                          DeviceModel model,          // 设备类型
                                          FileType ft,                // 文件类型
                                          string     strMac,          // Mac地址
                                          int nUpdateStatus,          // 同步状态
                                          int nSongCount ,            // 一共需要同步的歌曲数目
                                          int nFinishCount ,          // 已完成同步的歌曲数目
                                          int nPercentage)            // 正在同步歌曲的百分比
{
    string strBuf = CWebProtocol::CmdResponseUpdateDeviceFile(strMac, model, ft, nUpdateStatus+4, EC_SUCCESS, true, nSongCount, nFinishCount, nPercentage);
    m_WebSend.ForwardDataToWeb(pWebSection, strBuf);
}

// 下发设备网络IP信息
void CWebNetwork::ForwardDeviceNetInfo(CWebSection *pWebSection,   // Web终端
                                       CSection& device)           // 设备
{
    string strBuf = CWebProtocol::CmdDeviceIP(device.GetMac(), device.GetDeviceModel(), REST_GET, device.m_netInfo.m_IpAccessMode,
                                              device.m_netInfo.m_szIP, device.m_netInfo.m_szSubnetMask, device.m_netInfo.m_szGateway,
                                              device.m_netInfo.m_szDNS1, device.m_netInfo.m_szDNS2);
    m_WebSend.ForwardDataToWeb(pWebSection, strBuf, false, true);

#if 0
    // 设备SIP状态改变，发送设备信息到Web终端
    ForwardSectionInfoToWeb(0, &device, pWebSection);
#endif
}


// 下发设备网络模式信息
void CWebNetwork::ForwardDeviceNetModeInfo(CWebSection *pWebSection,   // Web终端
                                       CSection& device)               // 设备
{
    string strBuf = CWebProtocol::CmdDeviceNetworkMode(device.GetMac(), device.GetDeviceModel(), REST_GET,1, device.GetNetworkMode(),
                                        device.GetTcpServerIP(),device.GetTcpServerPort(),device.GetTcpBackupServerIP(),device.GetTcpBackupServerPort());
    m_WebSend.ForwardDataToWeb(pWebSection, strBuf, false, true);
}

// 下发固件升级进度状态
void CWebNetwork::ForwardUpgradeDeviceStatus(CWebSection *pWebSection,   // Web终端
                                             string strMac,              //  Mac地址
                                             int nUpgrade,               //  升级状态
                                             int nPercentage)            //  升级百分比
{
    //LOG("升级进度", LV_INFO);
    //LOG(FORMAT("upgrade = %d   nPer = %d\n", nUpgrade, nPercentage), LV_INFO);
    string strBuf = CWebProtocol::CmdResponseUpgradeDevice(strMac, nUpgrade, nPercentage);
    m_WebSend.ForwardDataToWeb(pWebSection, strBuf, false, false);
}

// 下发用户信息       ...暂时处理，待修改
void CWebNetwork::ForwardUserInfo(string strSendToAccount,                       //发往账户名称
                                  string strDestAccount,                         //目标账户名
                                  int nFlags)                                    //数据类型标识
{
    LPCUserInfo pSendToUser = g_Global.m_Users.GetUserByAccount(strSendToAccount.data());
    LPCUserInfo pDestUser = g_Global.m_Users.GetUserByAccount(strDestAccount.data());
    if(pSendToUser!=NULL && pDestUser != NULL)
    {
        if(nFlags & UD_USER)
        {
            userParm destUserParm;
            g_Global.m_Users.GetDestUserParmByAccount(strDestAccount, destUserParm);

            vector<userParm>  vecUserInfo;
            g_Global.m_Users.GetDirectSubUserByAccount(strDestAccount, vecUserInfo);

            string strBuf;
            strBuf = CWebProtocol::CmdResponseGetUserInfo(destUserParm,
                                                          vecUserInfo);

            vector<LPCWebSection> vecWebSection;
            g_Global.m_Users.GetWebSectionArrayByAccount(strSendToAccount,vecWebSection);
            vector<LPCWebSection>::iterator iter = vecWebSection.begin();
            for(; iter != vecWebSection.end(); iter++)
            {
                LPCWebSection pWebSection = *iter;
                m_WebSend.ForwardDataToWeb(pWebSection, strBuf);
            }
        }

        #if 0
        if(nFlags & UD_SECTION) //新的基本用不到此处，为了兼容以前的对接客户，保留此处
        {
            m_WebSend.WebSendUserZoneInfo(*pDestUser, NULL, -1);
        }
        #endif
    }

}

// 分发设备日志列表(暂只发送到超级用户)
void CWebNetwork::ForwardDevceLogList(CSection &device)
{
    string strBuf = CWebProtocol::CmdResponseDevLogList(device);

    m_WebSend.ForwardDataToWeb(NULL, strBuf);
}

// 分发设备日志下载进度
void CWebNetwork::ForwardDevceLogDownSche(CSection &device, CLogFile *pLogFile)
{
    if(pLogFile->HasFinished())
    {
        CMyString strFilePathName = device.m_LogFiles.GetLogFileDirectory(device.GetMac());
        strFilePathName  += "/";
        strFilePathName += pLogFile->m_szName;

        // 把数据写到文件
        MyCFile fileLog;
        if(fileLog.Open(strFilePathName.C_Str(), "wb+"))
        {
            fileLog.Write(pLogFile->m_pData, pLogFile->m_nRecvSize);
            fileLog.Close();
        }

        device.m_LogFiles.Clear();
    }
    else if(pLogFile->NextPackID() > 0)
    {
        // 获取下一个包内容
        g_Global.m_Network.m_CmdSend.CmdGetLogFileData(device, pLogFile->m_szName, pLogFile->NextPackID());
    }

    string strBuf = CWebProtocol::CmdDevLogDown(device.GetMac(), pLogFile->m_szName, pLogFile->m_nRecvSize, pLogFile->m_nSize);
    m_WebSend.ForwardDataToWeb(NULL, strBuf);
}

void CWebNetwork::ForwardBackupData(CWebSection *pWebSection)
{
    // 获取备份文件夹下所有文件名
    CMyString strBackupPath;
    strBackupPath.Format("%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_BACKUP);
    vector<string> vecBackupFile;
    GetDirAllFileName(strBackupPath, vecBackupFile);
    std::sort(vecBackupFile.begin(),vecBackupFile.end());
    string strBuf = CWebProtocol::CmdGetBackupFileName(vecBackupFile);

    if(pWebSection != NULL)
    {
        m_WebSend.ForwardDataToWeb(pWebSection, strBuf);
    }
    else
    {
        m_WebSend.ForwardDataToWeb(pWebSection, strBuf, TRUE, TRUE);
    }

}

#if 0
// 下发用户分组信息
void CWebNetwork::ForwardUserGroup(CWebSection *pWebSection,    // Web终端
                                   CUserInfo      *pUser)                // 用户信息
{
    /*bool    IsSuper = pUser->IsSuperUser();        // 是否超级用户, 超级用户可以设置所有分区分组
    // 组合包数量与分组ID信息
    int nGroupCount = 0;
    if(IsSuper)
    {
        nGroupCount = g_Global.m_Groups.GetGroupCount();
    }
    else
    {
        nGroupCount = pUser->GetGroupCount();
    }

    // 组包
    int nPageCount = nGroupCount/MAX_PAGE_COUNT;
    if(nGroupCount%MAX_PAGE_COUNT >= 0)             nPageCount++;

    for(int i=0; i<nPageCount; i++)
    {
        int nStart = i * MAX_PAGE_COUNT;
        int nEnd   = (nPageCount == i+1 ? nGroupCount : (i+1)*MAX_PAGE_COUNT);
        int nCount = nEnd - nStart;

        string strIDArray[nCount];
        for(int j=0; j<nCount; j++)
        {
            if(IsSuper)
            {
                strIDArray[j] = g_Global.m_Groups.GetGroup(j + nStart).GetID().C_Str();
            }
            else
            {
                strIDArray[j] = pUser->GetGroupID(j + nStart);
            }
        }

        string strBuf = CWebProtocol::CmdResponseGetUserGroup(pUser->GetAccount(), nPageCount, i+1, nCount, strIDArray);
        m_WebSend.ForwardDataToWeb(pWebSection, strBuf);
    }
    */

    if(pUser == NULL)       return;

    if(pWebSection == NULL)     // 如果Web终端为空，则发送到所有使用该用户登录的Section
    {
        vector<string> vecUID = g_Global.m_Users.GetUIDByAccount(pUser->GetAccount());
        for(int i=0; i<(int)vecUID.size(); i++)
        {
            LPCWebSection pWeb = g_Global.m_WebSections.GetWebSectionBySocketID(vecUID[i]);
            if(pWeb != NULL)
                m_WebSend.WebSendUserGroupInfo(*pUser, pWeb, -1);
        }
    }
    else
    {
        m_WebSend.WebSendUserGroupInfo(*pUser, pWebSection, -1);
    }

    // 发送到超级用户
    if(!pUser->IsSuperUser())
    {
        LPCUserInfo SuperUser = g_Global.m_Users.GetUserByAccount(SUPER_USER_NAME);
        if(SuperUser != NULL)
        {
            m_WebSend.WebSendUserGroupInfo(*SuperUser, NULL);
        }
    }
}
#endif

// 检测大屏设备掉线情况
void CWebNetwork::TabletsCheckOffline(long long tNow)
{
    int nSecCount = g_Global.m_WebSections.GetWebCount();

    for(int i=0; i<nSecCount; i++)
    {
        LPCWebSection pWebSection = g_Global.m_WebSections.GetWebSection(i);

        if(pWebSection && pWebSection->GetWebType() == WT_TABLET)
        {
            ctime_t span = tNow - pWebSection->GetLatestTime();

            if(span > OFFLINE_TIME)
            {
                pWebSection->SetIsOnline(false);
            }
            else if (span >= (OFFLINE_TIME - 5))	//后面5秒要发搜索设备命令
            {
                //m_WebSend.SearchOnlineTablet();
            }
        }
    }
}

// 检测web浏览器设备掉线情况
void CWebNetwork::WebCheckOffline(long long tNow)
{
    /*int nSecCount = g_Global.m_WebSections.GetWebCount();

    for(int i=0; i<nSecCount; i++)
    {
        CWebSection& pWebSection = g_Global.m_WebSections.GetWebBrowseByIndex(i);

        if(pWebSection == NULL)     continue;
        ctime_t span = tNow - pWebSection.GetLatestTime();

        if(span > OFFLINE_WEB_TIME)
        {
            pWebSection.SetIsOnline(false);
            m_WebSock.CtlEpollEvent(pWebSection->GetFd(), false);
            g_Global.m_WebSections.RemoveWeb(pWebSection->GetFd());
        }
    }*/
}

// web掉线
void CWebNetwork::WebDeviceOffline(string strSocketID)
{
    CSection* pDevice = g_Global.m_IntercomStations.GetSectionBySocketID(strSocketID);
    if (pDevice != NULL)
    {
        pDevice->SetProSource(PRO_OFFLINE);

        // 加入web回调通知分区更新
        g_Global.m_WebNetwork.ForwardSectionInfoToWeb(pDevice->GetDevType(), pDevice, NULL);
    }
}


// 重置服务器数据
void CWebNetwork::ResetServerData(int nFileFlag, bool bRestoreFactory)
{
    bool toSearchDevice = FALSE;

    if(nFileFlag & WEB_RESET_ZONE)      // 分区
    {
        // 清除分组中、定时点中、消防采集的所有离线分区(还差分区绑定的监控设备的处理)
        int nSecCount = g_Global.m_Sections.GetSecCount();
        BOOL bUpdateGroupFile	= FALSE;
        BOOL bUpdateTimerFile	= FALSE;
        BOOL bUpdateFireColFile = FALSE;
        BOOL bUpdateRemoteControlFile = FALSE;
        BOOL bUpdateAudioCollectorFile = FALSE;
        BOOL bUpdateAudioMixer = FALSE;

        #if SUPPORT_PHONE_GATEWAY
        BOOL bUpdatePhoneGateway = FALSE;
        #endif
        #if SUPPORT_AMP_CONTROLER
        BOOL bUpdateAmpControler = FALSE;
        #endif
        #if SUPPORT_NOISE_DETECTOR
        BOOL bUpdateNoiseDetector = FALSE;
        #endif

        for (int i=0; i<nSecCount; ++i)
        {
            CSection& section = g_Global.m_Sections.GetSection(i);

            if (!section.IsOnline())
            {
                #if SUPPORT_AUDIO_MIXER
                //如果是音频混音器-解码器，则不删除
                if(section.GetDeviceModel() == MODEL_AUDIO_MIXER_DECODER || section.GetDeviceModel() == MODEL_AUDIO_MIXER_DECODER_C)
                {
                    continue;
                }
                #endif
                if (g_Global.m_Groups.RemoveSectionFromAllGroups(section.GetMac()))
                {
                    bUpdateGroupFile = TRUE;
                }

                if (g_Global.m_TimerScheme.RemoveSectionFromAllSchemes(section.GetMac()))
                {
                    bUpdateTimerFile = TRUE;
                }

                if (g_Global.m_FireCollectors.RemoveSectionFromAllFireCollectors(section.GetMac()))
                {
                    bUpdateFireColFile = TRUE;
                }

                //清除所有音频采集器中的指定分区
                if(g_Global.m_AudioCollectors.RemoveSectionFromAllAudioCollectors(section.GetMac()))
                {
                    bUpdateAudioCollectorFile = TRUE;
                }

#if SUPPORT_REMOTE_CONTROLER
                //删除所有遥控任务中的分区信息
                if (g_Global.m_RemoteControlers.RemoveSectionFromAllRemoteControler(section.GetMac()))
                {
                    bUpdateRemoteControlFile = TRUE;
                }
#endif
#if SUPPORT_AUDIO_MIXER
                //删除所有音频混音器中的分区
                if (g_Global.m_AudioMixers.RemoveSectionFromAllAudioMixers(section.GetMac()))
                {
                    bUpdateAudioMixer = TRUE;
                }
#endif        
#if SUPPORT_PHONE_GATEWAY
                //删除所有电话网关中的分区
                if (g_Global.m_PhoneGateways.RemoveSectionFromAllPhoneGateways(section.GetMac()))
                {
                    bUpdatePhoneGateway = TRUE;
                }
#endif
#if 0//SUPPORT_AMP_CONTROLER
                //删除所有功放控制器中的分区
                if (g_Global.m_AmpControlers.RemoveSectionFromAllAmpControlers(section.GetMac()))
                {
                    bUpdateAmpControler = TRUE;
                }
#endif
#if SUPPORT_NOISE_DETECTOR
                //删除所有噪声检测器中的分区
                if (g_Global.m_NoiseDetectors.RemoveSectionFromAllNoiseDetectors(section.GetMac()))
                {
                    bUpdateNoiseDetector = TRUE;
                }
#endif

            }
        }

        // 内存有更新，则更新文件
        if (bUpdateGroupFile)
        {
            g_Global.WriteXmlFile(FILE_GROUP);
        }
        if (bUpdateTimerFile)
        {
            g_Global.WriteXmlFile(FILE_TIMER);
        }
        if (bUpdateFireColFile)
        {
            g_Global.WriteXmlFile(FILE_FIRE_COLLECTOR);
        }

        if (bUpdateAudioCollectorFile)
        {
            g_Global.WriteXmlFile(FILE_AUDIO_COLLECTOR);
        }

#if SUPPORT_REMOTE_CONTROLER
        if (bUpdateRemoteControlFile)
        {
            g_Global.WriteXmlFile(FILE_REMOTE_CONTROLER);
        }
#endif
#if SUPPORT_AUDIO_MIXER
        if (bUpdateAudioMixer)
        {
            g_Global.WriteXmlFile(FILE_AUDIO_MIXER);
        }
#endif
#if SUPPORT_PHONE_GATEWAY
        if (bUpdatePhoneGateway)
        {
            g_Global.WriteXmlFile(FILE_PHONE_GATEWAY);
        }
#endif
#if SUPPORT_AMP_CONTROLER
        if (bUpdateAmpControler)
        {
            g_Global.WriteXmlFile(FILE_AMP_CONTROLER);
        }
#endif
#if SUPPORT_NOISE_DETECTOR
        if (bUpdateNoiseDetector)
        {
            g_Global.WriteXmlFile(FILE_NOISE_DETECTOR);
        }
#endif
        // 清除分区数据
        //g_Global.m_Sections.ClearSections();
        g_Global.m_Sections.RemoveOfflineSections();
        // 只留下在线分区的监控设备绑定信息
        //g_Global.m_Sections.WriteSecMonitorFile(HTTP_FILE_SECMONITOR);
        //g_Global.WriteXmlFile(FILE_SECTION);

        //********如果是清除分区数据，那么将所有用户的分区信息更新
        int userCount=g_Global.m_Users.GetUserCount();
        for(int i=0;i<userCount;i++)
        {
            LPCUserInfo lpUser=g_Global.m_Users.GetUser(i);
            CMyString strUserAccount = lpUser->GetAccount();
            g_Global.m_Sections.PushWriteXmlAccountTask(strUserAccount);
        }

        //如果监听音箱不存在，清除
        if (g_Global.m_Sections.GetSectionByMac(g_Global.m_ListenDevice) == NULL)
        {
            memset(g_Global.m_ListenDevice,0,sizeof(g_Global.m_ListenDevice));
            g_Global.m_IniConfig.SetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_LISTEN_DEVICE, g_Global.m_ListenDevice);
            g_Global.m_IniConfig.Write();
        }
        g_Global.m_Users.RereshListenDevice();


        // 直接让用户重新登录
        g_Global.m_WebNetwork.RequestUserReLogin("", RG_AUTH_CHANGER);

        toSearchDevice = TRUE;

        // 加到日志
        CMyString strLogContents;
        strLogContents.Format("%s:%s", LANG_STR(LANG_SECTION_DIALOG, "Reset", ("重置")).C_Str(),
                              CProtocol::GetDescriptionFileType(FILE_SECTION).C_Str());

        g_Global.m_Network.AddLog(strLogContents);
        g_Global.m_logTable.InsertLog(	SUPER_USER_NAME, SUPER_USER_NAME, LT_ADVANCED_LOG, strLogContents);
    }

    if(nFileFlag & WEB_RESET_GROUP)        // 分组
    {
        // 删除所有分组本地的播放列表
        g_Global.m_Groups.ClearAllPlaylistGroup();

        // 删除所有定点中的分组信息
        if (g_Global.m_TimerScheme.RemoveAllGroupsFromAllSchemes())
        {
            g_Global.WriteXmlFile(FILE_TIMER);
        }
#if SUPPORT_REMOTE_CONTROLER
        //删除所有遥控任务中的分组信息
        if (g_Global.m_RemoteControlers.RemoveGroupFromAllRemoteControler(true,""))
        {
            g_Global.WriteXmlFile(FILE_REMOTE_CONTROLER);
        }
#endif
        // 删除分组
        g_Global.m_Groups.ClearGroups();
        g_Global.WriteXmlFile(FILE_GROUP);

        // 加到日志
        CMyString strLogContents;
        strLogContents.Format("%s:%s", LANG_STR(LANG_SECTION_DIALOG, "Reset", ("重置")).C_Str(),
                              CProtocol::GetDescriptionFileType(FILE_GROUP).C_Str());

        g_Global.m_Network.AddLog(strLogContents);
        g_Global.m_logTable.InsertLog(	SUPER_USER_NAME, SUPER_USER_NAME, LT_ADVANCED_LOG, strLogContents);
    }

    if(nFileFlag & WEB_RESET_PLAY_LIST)         // 播放列表
    {
        g_Global.m_Network.m_CmdSend.CmdSetIdleStatus();
        g_Global.InitPlaylist(TRUE);
        //如果启用了本地歌曲管理功能，那么只有在恢复出厂设置的时候才删除所有本地歌曲，否则不处理。
        #if SUPPORT_SONG_MANAGER
        if(bRestoreFactory)
        {
            g_Global.m_PlayList.RemoveAllSongFile();

            g_Global.m_SongManager.Reset();
        }
        #else
        g_Global.m_PlayList.RemoveAllSongFile();
        #endif    
        g_Global.m_WebNetwork.ForwardUpdateFileInfo(FILE_PLAYLIST);

        // 加到日志
        CMyString strLogContents;
        strLogContents.Format("%s:%s", LANG_STR(LANG_SECTION_DIALOG, "Reset", ("重置")).C_Str(),
                              CProtocol::GetDescriptionFileType(FILE_PLAYLIST).C_Str());

        g_Global.m_Network.AddLog(strLogContents);
        g_Global.m_logTable.InsertLog(	SUPER_USER_NAME, SUPER_USER_NAME, LT_ADVANCED_LOG, strLogContents);

        //如果不是恢复出厂，更新所有用户的播放列表XML文件(将其重置)
        //如果是出厂，那么应该在最后删除对应用户的XML文件
        if(!bRestoreFactory)
        {
            //更新所有用户的播放列表XML文件(将其重置)
            for(int i=0; i<g_Global.m_Users.GetUserCount(); i++)
            {
                LPCUserInfo pUserInfo = g_Global.m_Users.GetUser(i);
                if(strcmp(pUserInfo->GetAccount(), SUPER_USER_NAME) != 0)
                {
                    g_Global.m_PlayList.PushWriteXmlTask(pUserInfo->GetAccount());
                }
            }
        }
    }

    if(nFileFlag & WEB_RESET_TIMER)            // 定时
    {
        g_Global.m_TimerScheme.ClearSchemes();
        g_Global.m_TimerScheme.AddScheme(LANG_STR(LANG_SECTION_TIMING, "Timing Scheme", ("定时方案")));
        g_Global.m_TimerScheme.SetCurScheme(0);
        g_Global.WriteXmlFile(FILE_TIMER);

        // 加到日志
        CMyString strLogContents;
        strLogContents.Format("%s:%s", LANG_STR(LANG_SECTION_DIALOG, "Reset", ("重置")).C_Str(),
                              CProtocol::GetDescriptionFileType(FILE_TIMER).C_Str());

        g_Global.m_Network.AddLog(strLogContents);
        g_Global.m_logTable.InsertLog(	SUPER_USER_NAME, SUPER_USER_NAME, LT_ADVANCED_LOG, strLogContents);
    }

    if(nFileFlag & WEB_RESET_AUDIO)      // 音频采集器
    {
        g_Global.m_AudioCollectors.ClearSections();
        g_Global.WriteXmlFile(FILE_AUDIO_COLLECTOR);

        toSearchDevice = TRUE;

        // 加到日志
        CMyString strLogContents;
        strLogContents.Format("%s:%s", LANG_STR(LANG_SECTION_DIALOG, "Reset", ("重置")).C_Str(),
                              CProtocol::GetDescriptionFileType(FILE_AUDIO_COLLECTOR).C_Str());

        g_Global.m_Network.AddLog(strLogContents);
        g_Global.m_logTable.InsertLog(	SUPER_USER_NAME, SUPER_USER_NAME, LT_ADVANCED_LOG, strLogContents);
    }

    if(nFileFlag & WEB_RESET_PAGER)      // 寻呼台文件
    {
        //删除离线的寻呼台文件
        g_Global.m_Pagers.RemoveOfflineSections();
        g_Global.WriteXmlFile(FILE_PAGER);

        toSearchDevice = TRUE;

        // 加到日志
        CMyString strLogContents;
        strLogContents.Format("%s:%s", LANG_STR(LANG_SECTION_DIALOG, "Reset", ("重置")).C_Str(),
                              CProtocol::GetDescriptionFileType(FILE_PAGER).C_Str());

        g_Global.m_Network.AddLog(strLogContents);
        g_Global.m_logTable.InsertLog(	SUPER_USER_NAME, SUPER_USER_NAME, LT_ADVANCED_LOG, strLogContents);
    }

    if(nFileFlag & WEB_RESET_FIRE)       // 消防采集器
    {
        g_Global.m_FireCollectors.ClearSections();
        g_Global.WriteXmlFile(FILE_FIRE_COLLECTOR);

        toSearchDevice = TRUE;

        // 加到日志
        CMyString strLogContents;
        strLogContents.Format("%s:%s", LANG_STR(LANG_SECTION_DIALOG, "Reset", ("重置")).C_Str(),
                              CProtocol::GetDescriptionFileType(FILE_FIRE_COLLECTOR).C_Str());

        g_Global.m_Network.AddLog(strLogContents);
        g_Global.m_logTable.InsertLog(	SUPER_USER_NAME, SUPER_USER_NAME, LT_ADVANCED_LOG, strLogContents);
    }

    if (nFileFlag & WEB_RESET_MONITOR)  // 清除离线的监控设备
    {
        vector<const char *> vecMac;  // 保存离线的监控设备mac
        bool bClearMonitor = g_Global.m_Monitors.ClearOffineMonitors(vecMac);

        // 有离线的监控设备
        if (bClearMonitor)
        {
            bool bChangedSection = false;

            // 把分区绑定离线监控设备的mac清除
            for(int i=0; i<vecMac.size(); ++i)
            {
                if (g_Global.m_Sections.ResetOfflineMonitorByMac(vecMac[i]))
                {
                    bChangedSection = true;
                }
            }

            // 如果有清除，才写入文件
            if(bChangedSection)
            {
                //g_Global.m_Sections.WriteSecMonitorFile(HTTP_FILE_SECMONITOR);
                g_Global.WriteXmlFile(FILE_SECTION);
            }

            g_Global.WriteXmlFile(FILE_MONITOR);

            // 直接让用户重新登录
            g_Global.m_WebNetwork.RequestUserReLogin("", RG_AUTH_CHANGER);
        }

        // 加到日志
        CMyString strLogContents;
        strLogContents.Format("%s:%s", LANG_STR(LANG_SECTION_DIALOG, "Reset", ("重置")).C_Str(),
                              CProtocol::GetDescriptionFileType(FILE_MONITOR).C_Str());

        g_Global.m_Network.AddLog(strLogContents);
        g_Global.m_logTable.InsertLog(	SUPER_USER_NAME, SUPER_USER_NAME, LT_ADVANCED_LOG, strLogContents);
    }
    if(nFileFlag & WEB_RESET_FIRMWARE)  //清除升级固件
    {
        g_Global.CleanFirmware();
    }
    if(nFileFlag & WEB_RESET_SEQUENCE_POWER)    //清除电源时序器文件
    {
        // 清除定时点中的所有不存在的电源时序器,函数里面改变会更新timer文件
        int nSeqPwrCount = g_Global.m_SequencePower.GetSecCount();
        for (int i=0; i<nSeqPwrCount; ++i)
        {
            CSection& seqPwr = g_Global.m_SequencePower.GetSection(i);
            g_Global.m_TimerScheme.RemoveSequencePowerFromAllSchemes(seqPwr.GetMac());
        }

        g_Global.m_SequencePower.ClearSections();
        g_Global.WriteXmlFile(FILE_SEQUENCE_POWER);
        

        toSearchDevice = TRUE;
        // 加到日志
        CMyString strLogContents;
        strLogContents.Format("%s:%s", LANG_STR(LANG_SECTION_DIALOG, "Reset", ("重置")).C_Str(),
                              CProtocol::GetDescriptionFileType(FILE_SEQUENCE_POWER).C_Str());

        g_Global.m_Network.AddLog(strLogContents);
        g_Global.m_logTable.InsertLog(	SUPER_USER_NAME, SUPER_USER_NAME, LT_ADVANCED_LOG, strLogContents);
    }
    if(nFileFlag & WEB_RESET_LOG)               //清除日志文件
    {
        //清除Log日志文件及data.db数据库
        g_Global.CleanLog(0);
    }

    // 需要搜索设备
    if (toSearchDevice)
    {
        bool canSearch=true;
        #if IS_BACKUP_SERVER
        if(!g_Global.m_serverSync.IfBakcupServerChangeToMaster())
        {
            canSearch=false;
        }
        #endif
        if(canSearch)
        {
            //搜索在线网络化设备
            g_Global.m_Network.m_CmdSend.CmdSearchOnlineDevice();
        }

        // 写入所需默认的配置信息
        #if 0
        g_Global.InitDefaultConfig();
        #endif
    }

    // 恢复出厂设置
    if (bRestoreFactory)
    {
        // 重置用户数据
        g_Global.m_Users.ResetUserData();

        //Todo重置日志文件，包括数据库和log
    }
}

// 重置用户保存的设备文件时间
void CWebNetwork::ResetUserFileDateTime(CWebSection *pWebSection,  // Web终端
                                        string strMac,             // 设备MAC
                                        FileType ft)               // 文件类型
{
    if(pWebSection == NULL)
    {
        int nWebCount    = g_Global.m_WebSections.GetWebCount();

        for(int i=0; i<nWebCount; i++)
        {
            CWebSection* pWebSec = g_Global.m_WebSections.GetWebSection(i);
            if(pWebSec)
            {
                pWebSec->SetSecFileDateTime(strMac, ft, DEFAULT_DATETIME);
            }
        }
    }
    else
    {
        pWebSection->SetSecFileDateTime(strMac, ft, DEFAULT_DATETIME);
    }
}


void CWebNetwork::ForwardAccountStorageCapacity(string strNotifyAccount,string strDestAccount)
{  
    //龙之音V1版本,账户存储容量变化后才通知，其他版本没必要
    #if !APP_IS_LZY_LIMIT_STORAGE
    return;
    #endif
    vector<LPCWebSection> vecWebSection;
    g_Global.m_Users.GetWebSectionArrayByAccount(strNotifyAccount,vecWebSection);
    for(int i=0;i<vecWebSection.size();i++)
    {
        m_WebSend.WebSendAccountStorageCapacity(vecWebSection[i], strDestAccount);
    }
}

void CWebNetwork::ForwardNetworkInfo()
{
    //puts("ForwardNetworkInfo();");
    //    static io_speed io;
    //    io_speed temp;
    //    temp.ispeed = 0.0;
    //    temp.ospeed = 0.0;
    //    GetNetSpeed(&temp);

    //    //printf("ispeed: %5.2f, ospeed: %5.2f\n", temp.ispeed, temp.ospeed);
    //    if(io.ispeed != temp.ispeed || io.ospeed != temp.ospeed)
    //    {
    //        io.ispeed = temp.ispeed;
    //        io.ospeed = temp.ospeed;

    //        string strBuf = CWebProtocol::CmdForwardNetInfo(IsLinkNetwork(), io.ispeed, io.ospeed);
    //        m_WebSend.ForwardDataToWeb(NULL, strBuf, false, false);
    //    }
}

#if 0
// 备份服务器数据
void CWebNetwork::BackupServerData(int nFileFlag)
{
    CMyString strSrcPath;
    CMyString strDesPath;
    vector<string> srcPathArray;
    vector<string> dstPathArray;
#if 0
    // 创建文件夹
    CMyString strPath;
    strPath.Format("%s/%s", g_Global.m_strFolderPath.Data(), "Backup");
    CreateDirectoryQ(strPath.C_Str());
#endif
    if(nFileFlag & WEB_RESET_ZONE)      // 分区
    {
        strSrcPath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, HTTP_FILE_SECTION);
        strDesPath.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_BACK, HTTP_FILE_SECTION);
        srcPathArray.push_back(strSrcPath.C_Str());
        dstPathArray.push_back(strDesPath.C_Str());
    }

    if(nFileFlag & WEB_RESET_GROUP)        // 分组
    {
        strSrcPath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, HTTP_FILE_GROUP);
        strDesPath.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_BACK, HTTP_FILE_GROUP);
        srcPathArray.push_back(strSrcPath.C_Str());
        dstPathArray.push_back(strDesPath.C_Str());
    }

    if(nFileFlag & WEB_RESET_PLAY_LIST)         // 播放列表
    {
        strSrcPath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, HTTP_FILE_PLAYLIST);
        strDesPath.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_BACK, HTTP_FILE_PLAYLIST);
        srcPathArray.push_back(strSrcPath.C_Str());
        dstPathArray.push_back(strDesPath.C_Str());
    }

    if(nFileFlag & WEB_RESET_TIMER)            // 定时
    {
        strSrcPath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, HTTP_FILE_TIMER);
        strDesPath.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_BACK, HTTP_FILE_TIMER);
        srcPathArray.push_back(strSrcPath.C_Str());
        dstPathArray.push_back(strDesPath.C_Str());
    }

    if(nFileFlag & WEB_RESET_AUDIO)      // 音频采集器
    {
        strSrcPath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, HTTP_FILE_AUDIO_COLLECTOR);
        strDesPath.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_BACK, HTTP_FILE_AUDIO_COLLECTOR);
        srcPathArray.push_back(strSrcPath.C_Str());
        dstPathArray.push_back(strDesPath.C_Str());
    }

    if(nFileFlag & WEB_RESET_FIRE)       // 消防采集器
    {
        strSrcPath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, HTTP_FILE_FIRE_COLLECTOR);
        strDesPath.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_BACK, HTTP_FILE_FIRE_COLLECTOR);
        srcPathArray.push_back(strSrcPath.C_Str());
        dstPathArray.push_back(strDesPath.C_Str());
    }

    g_Global.m_fileManager.CopyFileArray(srcPathArray, dstPathArray);
}

// 还原服务器数据
void CWebNetwork::RestoreServerData(int nFileFlag)
{
    CMyString strPathName;
    CMyString strSrcPath;
    CMyString strDesPath;

    if(nFileFlag & WEB_RESET_ZONE)      // 分区
    {
        strSrcPath.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_BACK, HTTP_FILE_SECTION);
        strDesPath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, HTTP_FILE_SECTION);

        if(IsExistFile(strSrcPath.C_Str()))        // 备份文件存在再执行还原操作
        {
            // 清除分组中的分区
            for (int i=0; i<g_Global.m_Groups.GetGroupCount(); ++i)
            {
                CGroup& group = g_Global.m_Groups.GetGroup(i);
                group.ClearSections();
            }
            // 写入分组文件
            g_Global.m_Groups.WriteGroupFile(HTTP_FILE_GROUP);
            // 清除分区数据
            g_Global.m_Sections.ClearSections();

            // 复制文件
            g_Global.m_fileManager.CopyOneFile(strSrcPath, strDesPath);

            g_Global.m_Sections.ReadFile();
            ForwardUpdateFileInfo(FILE_SECTION);
        }
    }

    if(nFileFlag & WEB_RESET_GROUP)        // 分组
    {
        strSrcPath.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_BACK, HTTP_FILE_GROUP);
        strDesPath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, HTTP_FILE_GROUP);

        if(IsExistFile(strSrcPath.C_Str()))        // 备份文件存再执行还原操作
        {
            // 删除分区中的分组信息
            g_Global.m_Sections.ClearAllGroupID();

            // 删除所有分组本地的播放列表
            g_Global.m_Groups.ClearAllPlaylistGroup();

            // 删除所有定点中的分组信息
            g_Global.m_TimerScheme.ClearAllTimePointGroup();

            // 删除分组
            g_Global.m_Groups.ClearGroups();

            g_Global.m_fileManager.CopyOneFile(strSrcPath, strDesPath);

            g_Global.m_Groups.ReadGroupFile(HTTP_FILE_GROUP);
            ForwardUpdateFileInfo(FILE_GROUP);
        }
    }

    if(nFileFlag & WEB_RESET_PLAY_LIST)         // 播放列表
    {
        strSrcPath.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_BACK, HTTP_FILE_GROUP);
        strDesPath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, HTTP_FILE_GROUP);

        if(IsExistFile(strSrcPath.C_Str()))        // 备份文件存再执行还原操作
        {
            g_Global.InitPlaylist(TRUE);

            // 本地
            //if (g_Global.m_nSongsFrom == SONGS_FROM_LOCAL)
            {
                g_Global.m_PlayList.SetFileName(HTTP_FILE_PLAYLIST);
                g_Global.m_fileManager.CopyOneFile(strSrcPath, strDesPath);

                // 播放列表文件

                if(!g_Global.m_PlayList.ReadFile())
                {
                    g_Global.m_PlayList.ResetList();
                }
            }

            ForwardUpdateFileInfo(FILE_PLAYLIST);
        }
    }

    if(nFileFlag & WEB_RESET_TIMER)            // 定时
    {
        strSrcPath.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_BACK, HTTP_FILE_TIMER);
        strDesPath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, HTTP_FILE_TIMER);

        if(IsExistFile(strSrcPath.C_Str()))        // 备份文件存再执行还原操作
        {
            g_Global.m_TimerScheme.ClearSchemes();
            g_Global.m_TimerScheme.AddScheme(LANG_STR(LANG_SECTION_TIMING, "Timing Scheme", ("定时方案")));
            g_Global.m_TimerScheme.SetCurScheme(0);

            g_Global.m_fileManager.CopyOneFile(strSrcPath, strDesPath);
            g_Global.m_TimerScheme.ReadTimerFile(HTTP_FILE_TIMER);
            ForwardUpdateFileInfo(FILE_TIMER);
        }
    }

    if(nFileFlag & WEB_RESET_AUDIO)      // 音频采集器
    {
        strSrcPath.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_BACK, HTTP_FILE_AUDIO_COLLECTOR);
        strDesPath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, HTTP_FILE_AUDIO_COLLECTOR);

        if(IsExistFile(strSrcPath.C_Str()))        // 备份文件存再执行还原操作
        {
            g_Global.m_AudioCollectors.ClearSections();

            g_Global.m_fileManager.CopyOneFile(strSrcPath, strDesPath);
            g_Global.m_AudioCollectors.ReadFile();
            ForwardUpdateFileInfo(FILE_AUDIO_COLLECTOR);
        }
    }

    if(nFileFlag & WEB_RESET_FIRE)       // 消防采集器
    {
        strSrcPath.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_BACK, HTTP_FILE_FIRE_COLLECTOR);
        strDesPath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, HTTP_FILE_FIRE_COLLECTOR);

        if(IsExistFile(strSrcPath.C_Str()))        // 备份文件存再执行还原操作
        {
            g_Global.m_FireCollectors.ClearSections();

            g_Global.m_fileManager.CopyOneFile(strSrcPath, strDesPath);
            g_Global.m_FireCollectors.ReadFile();
            ForwardUpdateFileInfo(FILE_FIRE_COLLECTOR);
        }
    }
}
#endif



// 分发对讲状态到WEB客户端
void CWebNetwork::ForwardIntercomStatusToWeb(CWebSection *pWebSection,CSection &callingDevice,CSection &calledDevice,int callStatus)
{
    if(pWebSection == NULL)
    {
        //发送到所有web，需要后再处理
        int nWebCount    = g_Global.m_WebSections.GetWebCount();      
        for(int i=0; i<nWebCount; i++)
        {
            CWebSection* pWebSec = g_Global.m_WebSections.GetWebSection(i);
            if(pWebSec)
            {
                //m_WebSend.WebSendLocalSongInfo(pWebSec);
                m_WebSend.PushIntercomStatusToWeb(pWebSec,callingDevice,calledDevice,callStatus);
            }
        }
    }
    else
    {
        m_WebSend.PushIntercomStatusToWeb(pWebSection,callingDevice,calledDevice,callStatus);
    }
}

#if SUPPORT_SERVER_SYNC
// 分发备用服务器信息到WEB客户端（只发给管理员用户）
void CWebNetwork::ForwardBackupServerInfoToWeb(CWebSection *pWebSection)
{
    if(pWebSection == NULL)
    {
        //发送到所有web，需要后再处理
        int nWebCount    = g_Global.m_WebSections.GetWebCount();      
        for(int i=0; i<nWebCount; i++)
        {
            CWebSection* pWebSec = g_Global.m_WebSections.GetWebSection(i);
            if(pWebSec)
            {
                m_WebSend.PushBackupServerInfoToWeb(pWebSec);
            }
        }
    }
    else
    {
        m_WebSend.PushBackupServerInfoToWeb(pWebSection);
    }
}
#endif



