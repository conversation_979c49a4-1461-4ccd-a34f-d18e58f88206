#ifndef WEBHANDLE_H
#define WEBHANDLE_H

#include <QtCore/qglobal.h>

#include <iostream>
#include <vector>
#include <map>
#include <set>
#include <mutex>
#if defined(Q_OS_LINUX)
#include <sys/wait.h>
#endif
#include <sys/types.h>
#include "Model/User/UserManager.h"
#include "Model/Device/WebSection.h"
//#include "Model/Other/WebTimePoint.h"
//#include "Model/Other/RecvData.h"
#include "Model/Recv/WebData.h"
#include "Tools/cJSON.h"
#include "Global/Const.h"

using namespace std;

#define MAGIC_KEY "258EAFA5-E914-47DA-95CA-C5AB0DC85B11"

#define MAX_IP_LEN  16
//#define MAX_BUF_LEN  1450*5
#define MAX_ZONECOUNT 20

class CSection;

enum WebOperating
{
        WEB_SET = 1,            // 设置
        WEB_GET = 0            // 查询
};


//enum
//{
//        DATA_SECTION,       // 分区
//        DATA_GROUP,         //  分组
//        DATA_SONG            //  歌曲
//};

/**********************************************************/
/*           Web命令处理类                                                                      */
/**********************************************************/

class CWebHandle
{
public:
    CWebHandle();
    ~CWebHandle();


    // 处理Websocket命令(WEB端)
    void    HandleWebCommand(const char* data,      // 数据
                             string strSocketUID,   // qwbesocket
                             string peerAddress);   // peer IP
    // 处理用户命令
    void    HandleUserCommand(CWebSection& pWebSection,
                            const char* data);

public:
    /*-------------------             用户                  -----------------------*/

        // 处理用户登录
    void    HandleGetMediaPort(CWebSection& pWebSection,
                            string peerAddress,
                            const char *data);

    // 处理用户登录
    void    HandleUserLogin(CWebSection& pWebSection,
                            string peerAddress,
                            const char *data);

//  用户管理
    // 处理 用户管理命令
    void    HandleUserManagerCommand(CUserInfo& pUser, CWebSection& pWebSection, const char *data);

    // 处理添加用户
    void    HandleAddUser(CUserInfo& pUser,
                          CWebSection& pWebSection,
                          const char *data);

    // 移除用户
    void    HandleRemoveUser(CWebSection& pWebSection,
                             const char* data);

    // 编辑用户（修改用户权限）
    void    HandleModifyUser(CWebSection& pWebSection,
                             const char* data);

    #if APP_IS_AISP_TZY_ENCRYPTION
    void    HandleSetUserCertificate(CWebSection &pWebSection, const char *data);
    #endif
    
    // 查询用户信息
    void    HandleGetUserInfo(CUserInfo& pUser, CUserInfo&  pDestUser, CWebSection& pWebSection);

    // 修改用户密码
    //void    HandleModifyPsd(CUserInfo& pUser, CWebSection& pWebSection, const char *data);

    // 获取(子)用户的分区
    void    HandleGetUserZone(CUserInfo&    pUser,
                              CUserInfo&    pDestUser,
                              CWebSection&  pWebSection,
                              int  nPage);

    // 用户离线(主要用于UDP)
    //void    HandleUserOffline();

#if 0
    // 获取(子)用户的分组
    void    HandleGetUserGroup(CUserInfo&      pUser,
                                                     CUserInfo&       pDestUser,
                                                     CWebSection&  pWebSection,
                                                     int  nPage);
#endif

    // 设置用户数据(分区/分组)
    void    HandleSetUserData(CUserInfo&        pUser,
                              CUserInfo&        pDestUser,
                              CWebSection&      pWebSection,
                              const char*       data);

    // 设置用户分区
    void    HandleSetUserSection(CWebSection& pWebSection,
                                 const char* data);
    // 设置用户权限
    void    HandleSetUserAuthority(CWebSection &pWebSection, const char *data);
    
    // 查询用户存储空间
    void    HandleGetUserStorageCapacity(CWebSection &pWebSection, const char *data);

    // 设置用户存储空间(仅管理员才支持修改，龙之音V1版本）
    void    HandleSetUserStorageCapacity(CWebSection &pWebSection, const char *data);

    ///////////////////////////////////////////////////////////////////////////////
    /*-------------------           获取信息接口           -----------------------*/

    // 获取请求心跳信息
    void    HandleGetHeartbeatInfo(CWebSection &pWebSection);

    // 处理获取分区状态
    void    HandleGetDeviceInfo(CWebSection &pWebSection, const char *data);

    // 处理获取分组信息
    void    HandleGetGroupInfo(CWebSection &pWebSection, const char *data);

    // 处理获取歌曲列表信息
    void    HandleGetPlaylistInfo(CWebSection &pWebSection, const char *data);

    // 处理获取定时信息
    void    HandleGetTimerInfo(CWebSection &pWebSection, const char *data);
    
    // 处理获取消防采集器信息
    void HandleGetFireCollectorInfo(CWebSection &pWebSection, const char *data);

    // 处理获取今日定时点信息
    void HandleGetTodayTimerInfo(CWebSection &pWebSection, const char *data);

    // 处理获取手动任务信息
    void HandleGetManualTaskInfo(CWebSection &pWebSection, const char *data);

    // 处理获取本地歌曲信息
    void HandleGetLocalSongInfo(CWebSection &pWebSection, const char *data);

    // 处理获取电源时序器信息
    void HandleGetSequencePowerInfo(CWebSection &pWebSection, const char *data);
#if SUPPORT_REMOTE_CONTROLER
    // 处理获取远程遥控器信息
    void HandleGetRemoteControlerInfo(CWebSection &pWebSection, const char *data);
#endif
    void HandleGetAudioCollectorInfo(CWebSection &pWebSection, const char *data);
    // 处理获取客户端状态(DSP9312)
    void  HandleGetClientStatus(CWebSection &pWebSection, const char *data);

    // 处理获取更新文件信息
    void    HandleGetFileInfo(CWebSection&  pWebSection,
                              const char*       data);

    // 处理网络质量检测
    void    HandleGetQuality(CWebSection&  pWebSection,
                             const char*       data);

///////////////////////////////////////////////////////////////////////////////////////////////
/*------------- 控制设备接口 -----------------*/

    // 处理设置选中分区
    void    HandleSetSelectedZone(CWebSection& pWebSection, const char* data);

    // 处理播放音源
    void    HandlePlaySource(CWebSection& pWebSection, const char* data, LPCUserInfo pUser);

    // 处理控制指定分区播放指定歌曲/TTS
    void    HandlePlaySpecifiedSource(CWebSection &pWebSection, const char *data, LPCUserInfo pUser);
    
    // 播放网络电台
    void    HandlePlayRadioSource(CWebSection &pWebSection, const char *data, LPCUserInfo pUser);
    
    // 停止网络电台播放
    void    HandleStopRadioSource(CWebSection &pWebSection, const char *data, LPCUserInfo pUser);

    /*-------------------             电台管理                  -----------------------*/
    // 添加电台信息
    void    HandleAddRadioInfo(CWebSection& pWebSection, const char* data);
    
    // 编辑电台信息
    void    HandleEditRadioInfo(CWebSection& pWebSection, const char* data);
    
    // 删除电台信息
    void    HandleDeleteRadioInfo(CWebSection& pWebSection, const char* data);
    
    // 获取电台分组列表
    void    HandleGetRadioGroupList(CWebSection& pWebSection, const char* data);
    
    // 通过电台分组ID获取详细电台列表
    void    HandleGetRadioListByGroupId(CWebSection& pWebSection, const char* data);

#if SUPPORT_WEB_PAGING
    // 服务器请求广播寻呼
    void    HandleRequestPaging(CWebSection& pWebSection, const char* data, LPCUserInfo pUser);
    void    HandlePagingStream(CWebSection& pWebSection, const char* data, LPCUserInfo pUser);
#endif
    // 12.1 控制指定寻呼台向指定分区设备发起广播寻呼
    void    HandleSetBroadcastPaging(CWebSection &pWebSection, const char *data, LPCUserInfo pUser);

    // 查询/设置监听音箱
    void HandleSetMonitorSpeaker(CWebSection &pWebSection, const char *data);

    // 监听节目源
    void    HandleMonitorSource(CWebSection& pWebSection, const char* data);

    // 播放音频采集器音源
    void    HandlePlayAudio(CWebSection& pWebSection, const char* data);

    // 播放AUX音源
    void    HandlePlayAux(CWebSection& pWebSection, const char* data);

    // 处理设置音量
    void    HandleSetVolume(CWebSection& pWebSection, const char* data);

    // 处理查询/设置设备子音量
    void    HandleSetSubVolume(CWebSection &pWebSection, const char* data);

    // 设置分区为空闲状态
    void    HandleSetIdle(CWebSection& pWebSection, const char* data);

    // 设置钟声
    void    HandleSetChime(CWebSection& pWebSection, const char* data);

    // 播放钟声
    void    HandlePlayChime(CWebSection& pWebSection, int nSelectID);

    // 设置/取消静音
    void    HandleSetMute(CWebSection& pWebSection, const char* data);

    // 查询/设置播放模式
    void    HandleSetPlayMode(CWebSection& pWebSection, const char* data);

    // 设置控制模式(程控/手控)
    void    HandleSetControlMode(CWebSection& pWebSection, const char* data);

////////////////////////////////////////////////////////////////////////////////////
    /*------------       SIP服务器         ---------------*/

    // 客户端向服务器请求SIP设备信息
    void    HandleGetSipInfo(CWebSection& pWebSection, const char* data);

    // 客户端向服务器发起对讲请求
    void    HandleRequestTalkback(CWebSection& pWebSection, const char* data);

    // 客户端向服务器发起广播/寻呼请求
    void    HandleRequestPaging(CWebSection& pWebSection, const char* data);

    // 客户端向服务器发起广播/寻呼请求(PCM)
    void    HandleRequestPcmPaging(CWebSection& pWebSection, const char* data);

    // 客户端向服务器发起监听请求
    void    HandleRequestListen(CWebSection& pWebSection, const char* data);

    // 客户端向服务器发送挂断请求
    void    HandleRequestHangup(CWebSection& pWebSection, const char* data);

    // 客户端向服务器发送挂断请求PCM
    void    HandleRequestPCMHangup(CWebSection& pWebSection, const char* data);

/////////////////////////////////////////////////////////////////////////////////////
    /*--------------        监控            -------------*/

    // 客户端向服务器请求监控设备信息
    void    HandleGetMonitorInfo(CWebSection& pWebSection, const char* data);

    // 设置监控设备信息
    void    HandleSetMonitorInfo(CWebSection& pWebSection, const char* data);

    // 添加自定义监控设备
    void    HandleCustomMonitorInfo(CWebSection& pWebSection, const char* data);

    // 删除监控设备信息
    void    HandleDeleteMonitorInfo(CWebSection &pWebSection, const char *data);

    // 设置监控设备事件
    void    HandleSetMonitorEvent(CWebSection& pWebSection, const char* data);

    // 设置监控设备事件触发分区
    void    HandleSetEventZone(CWebSection& pWebSection, const char* data);

    // 指定监控RTSP流(web)
    void    HandleMonitorRtsp(CWebSection& pWebSection, const char* data);

    // 开启/关闭视频监控
    void    HandleEnableMonitor(CWebSection &pWebSection, const char *data);

    // 开启/关闭通话录音
    void    HandleEnableCallRecord(CWebSection &pWebSection, const char *data);

    // 开启/关闭考试模式
    void    HandleEnableExaminationMode(CWebSection &pWebSection, const char *data);

    // 开启/关闭云控制
    void    HandleEnableCloudControl(CWebSection &pWebSection, const char *data);

    // 设置系统主题
    void    HandleSetSystemTheme(CWebSection &pWebSection, const char *data);

    // 获取/设置AISP配置文件
    void    HandleSetAispConfig(CWebSection &pWebSection, const char *data);

    // 查询/设置信息发布参数
    void    HandleSetInformationPublish(CWebSection &pWebSection, const char *data);

/////////////////////////////////////////////////////////////////////////////////////
    /*--------------------         编辑分组                        ----------------------*/
    // 处理分组管理命令
    void    HandleGroupCommand(CWebSection& pWebSection, const char* data);

    // 添加分组
    void    HandleAddGroup(CWebSection& pWebSection, const char* data);

    // 编辑分组
    void    HandleModifyGroup(CWebSection& pWebSection, const char* data);

    // 移除分组
    void    HandleRemoveGroup(CWebSection& pWebSection, const char* data);

    // 设置分组的分区
    void    HandleSetGroupSec(CWebSection& pWebSection, const char* data);

///////////////////////////////////////////////////////////////////////////////
    /*---------------          编辑播放列表                    -----------------*/
    // 处理编辑播放列表命令
    void    HandlePlayListCommand(CWebSection& pWebSection, const char* data);

    // 添加歌曲列表
    void    HandleRequestAddSongList(CWebSection& pWebSection, const char* data);

    // 重命名歌曲列表
    void    HandleRequestRenameSongList(CWebSection& pWebSection, const char* data);

    // 移除歌曲列表
    void    HandleRequestRemoveSongList(CWebSection& pWebSection, const char* data);

    // 获取服务器上传路径下的所有歌曲
    void    HandleGetServerAllSong(CWebSection& pWebSection, const char* data);

    // 请求上传歌曲到服务器
    void    HandleRequestUploadSongToServer(CWebSection& pWebSection, const char* data);

    // 请求添加歌曲到列表
    void    HandleRequestAddSongToList(CWebSection& pWebSection, const char* data);

    // 从列表移除指定歌曲
    void    HandleRequestRemoveSong(CWebSection& pWebSection, const char* data);

    // 审核本地曲库歌曲
    void    HandleAuditLocalSong(CWebSection& pWebSection, const char* data);

    // 停止上传歌曲
    void    HandleStopUploadSong(CWebSection& pWebSection, const char* data);

    // 移除指定列表中所有的歌曲
    void    HandleRequestRemoveAllSong(CWebSection& pWebSection, const char* data);

    // 文字转语音
    void    HandleText2Speech(CWebSection& pWebSection, const char* data);

    // 歌曲列表排序
    void    HandleSortPlayList(CWebSection &pWebSection, const char *data);

//////////////////////////////////////////////////////////////////////////////
    /*---------------------          定时管理               --------------------*/

    // 处理定时管理命令
    void    HandleTimerCommand(CWebSection& pWebSection, const char* data);

    // 添加定时点信息
    void    HandleAddTimePoint(CWebSection& pWebSection, const char* data, CExTimePoint &tp);

    // 编辑定时点信息
    void    HandleEditTimePoint(CWebSection& pWebSection, const char* data, CExTimePoint &tp);

    // 移除定时点信息
    void    HandleRemoveTimePoint(CWebSection& pWebSection, const char* data);

    // 设置定时点数据(分区/分组/歌曲)
    void    HandleSetTimeData(CWebSection& pWebSection, const char* data);

    // 禁用/恢复指定定时点
    void    HandleTimePointValid(CWebSection& pWebSection, const char* data);

    // 单次取消/恢复指定定时点
    void    HandleTimePointSingleCancel(CWebSection &pWebSection, const char *data);

    // 复制指定定时点
    void    HandleCopyTimePoint(CWebSection& pWebSection, const char* data);

    // 排序定时点
    void    HandleSortTimePoint(CWebSection& pWebSection, const char* data);

    // 添加定时方案
    void    HandleAddTimeScheme(CWebSection& pWebSection, const char* data);

    // 编辑定时方案
    void    HandleModifyTimeScheme(CWebSection& pWebSection, const char* data);

    // 移除定时方案
    void    HandleRemoveTimeScheme(CWebSection& pWebSection, const char* data);

    // 设置当前定时方案
    void    HandleSetCurTimeScheme(CWebSection& pWebSection, const char* data);

    // 禁用/恢复指定定时方案
    void    HandleTimeSchemeValid(CWebSection& pWebSection, const char* data);

    // 复制指定定时方案
    void    HandleCopyTimeScheme(CWebSection& pWebSection, const char* data);

    // 请求上传定时方案
    void    HandleUploadTimeScheme(CWebSection& pWebSection, const char* data);

    // 导入定时方案
    void    HandleImportTimeScheme(CWebSection& pWebSection, const char* data);

    // 导出定时方案
    void    HandleExportTimeScheme(CWebSection& pWebSection, const char* data);

////////////////////////////////////////////////////////////////////////////////////////////////
    /*---------------------          设备管理               --------------------*/

    // 处理设备管理命令
    void    HandleDeviceCommand(CWebSection& pWebSection, const char* data);

// 分区设备
    //  设置设备信息
    void    HandleSetDeviceInfo(CWebSection& pWebSection, const char* data);

    // 查询/设置网络解码播放器EMC状态
    void    HandleEMCStatus(CWebSection& pWebSection, const char* data);

    // 查询/设置EQ音效
    void    HandleDeviceEQ(CWebSection& pWebSection, const char* data);

    // 查询/设置设备蓝牙参数
    void    HandleDeviceBluetooth(CWebSection &pWebSection, const char *data);

#if SUPPORT_INTERCOM_DEVICE_CONFIG
    // 查询/设置对讲终端基础配置
    void    HandleCallDeviceBasic(CWebSection &pWebSection, const char *data);
#endif

    // 查询/设置终端触发设置
    void    HandleTriggerConfig(CWebSection &pWebSection, const char *data);

    // 启动/停止手动告警
    void    HandleStartManualAlarm(CWebSection &pWebSection, const char *data);

    // 查询/设置混音
    void    HandleMixMode(CWebSection& pWebSection, const char* data);

    // 查询/设置sip登录信息
    void    HandleSipLoginInfo(CWebSection& pWebSection, const char* data);
#if 0
    // sip账号登录
    void    HandleSipLogin(CWebSection& pWebSection, const char* data);
#endif
    // 通知客户端设备文件需要同步 回复
    void    HandleUpdateWebSectionTime(CWebSection& pWebSection, const char* data);

    // 客户端请求服务器更新设备文件
    void    HandleUpdateDeviceFile(CWebSection& pWebSection, const char* data);

    // 客户端请求中止同步文件
    void    HandleStopUpdateDeviceFile(CWebSection& pWebSection, const char* data);

    // 获取服务器上传路径下的所有固件名称
    void    HandleGetServerAllFirmware(CWebSection& pWebSection, const char* data);

    // 请求上传固件到服务器
    void    HandleUploadFirmwareToServer(CWebSection& pWebSection, const char* data);

    // 设备固件升级
    void    HandleUpgradeDevice(CWebSection& pWebSection, const char* data);

    // 终端日志列表获取
    void    HandleGetSecLogList(CWebSection& pWebSection, const char* data);

    // 终端日志下载
    void    HandleGetSecLogData(CWebSection& pWebSection, const char* data);
#if 0
    // 查询/设置网络解码播放器电源输出模式(9131/9138)
    void    HandlePowerOutputMode(CWebSection& pWebSection, const char* data);

    //  查询/设置网络解码分区器输出状态（9138）
    void    HandleSplitterStatus(CWebSection& pWebSection, const char* data);

    //  查询/设置无线MIC的状态
    void    HandleMicStatus(CWebSection& pWebSection, const char* data);
#endif
// 消防采集器
    // 设置消防采集器
    void    HandleSetFireDevice(CWebSection& pWebSection, const char* data);

    // 设置消防通道
    void    HandleSetFireChannel(CWebSection& pWebSection, const char* data);

//电源时序器
    // 设置电源时序器参数
    void    HandleSetSequencePowerDevice(CWebSection &pWebSection, const char *data);
#if SUPPORT_REMOTE_CONTROLER
//远程遥控器
    // 设置远程遥控器任务
    void HandleSetRemoteControlerTask(CWebSection &pWebSection, const char *data);
    // 设置远程遥控器按键
    void HandleSetRemoteControlerKey(CWebSection &pWebSection, const char *data);
#endif
#if SUPPORT_AUDIO_MIXER
    // 设置音频混音器参数
    void HandleSetAudioMixerParm(CWebSection &pWebSection, const char *data);
#endif
#if SUPPORT_PHONE_GATEWAY
    // 设置电话网关参数
    void HandleSetPhoneGatewayParm(CWebSection &pWebSection, const char *data);
#endif
#if SUPPORT_AMP_CONTROLER
    void HandleAmpControlerConfig(CWebSection &pWebSection, const char *data);
#endif

#if SUPPORT_NOISE_DETECTOR
    void HandleSetNoiseDetectorParm(CWebSection &pWebSection, const char *data);
#endif

    // 设置音频采集器参数
    void HandleSetAudioCollectorParm(CWebSection &pWebSection, const char *data);

// 其他设置
    // 查询/设置IP属性
    void    HandleDeviceIP(CWebSection& pWebSection, const char* data);

    // 查询/设置网络模式
    void    HandleNetWorkMode(CWebSection& pWebSection, const char* data);

    // 获取运行日志
    void    HandleGetRunlog(CWebSection& pWebSection, const char* data);

    // 导出运行日志
    void    HandleExportRunLog(CWebSection& pWebSection, const char* data);

    // 导出日志任务结构体
    typedef struct 
    {
        string strLogType;         // 日志类型
        string strStartDate;       // 开始日期
        string strEndDate;         // 结束日期
        string strDeviceName;      // 设备名称
        string strMac;             // 设备MAC
        string strFileName;        // 文件名
        string strSocketID;        // WebSocket ID
        bool   isSuperUser;        // 是否超级用户
        vector<string> vecSubUserDeviceMac; // 子用户设备MAC列表
    } ExportLogTask;

    // 查询通话日志任务结构体
    typedef struct 
    {
        string strStartDate;       // 开始日期
        string strEndDate;         // 结束日期
        string strRecordType;      // 录音类型
        string strCallerMac;       // 主叫方MAC
        string strSocketID;        // WebSocket ID
        bool   isSuperUser;        // 是否超级用户
        vector<string> vecSubUserDeviceMac; // 子用户设备MAC列表
    } QueryCallLogTask;

    // 查询通话日志
    void    HandleQueryCallLog(CWebSection& pWebSection, const char* data);

    // 删除通话日志
    void    HandleDeleteCallLog(CWebSection& pWebSection, const char* data);

    static void* ExportLogThreadProc(void* lpParam);
    void ExecuteExportLogTask(ExportLogTask* task);

    static void* QueryCallLogThreadProc(void* lpParam);
    void ExecuteQueryCallLogTask(QueryCallLogTask* task);

    // 用户导出任务状态管理
    static std::set<string> s_activeExportUsers;  // 正在进行导出操作的用户Socket ID集合
    static std::mutex s_exportUsersMutex;         // 保护活跃用户集合的互斥锁
    
    static bool IsUserExporting(const string& socketID);
    static void SetUserExporting(const string& socketID, bool exporting);

    // 重置设备数据
    void    HandleResetDeviceData(CWebSection& pWebSection, const char* data);

    // 重启设备
    void    HandleRebootDevice(CWebSection& pWebSection, const char* data);

    static void* DisConnect_DeviceTcp(void* parm);
    void DisConnect_DeviceTcp_Thread(char* mac);


    // 分区自定义排序
    void    HandleZoneSortCustom(CWebSection& pWebSection, const char* data);

    // 分区自定义排序
    void    HandleDeleteDevice(CWebSection& pWebSection, const char* data);
//////////////////////////////////////////////////////////////////////////////////////////////////
    /*********           高级设置                   **********/
    // 处理高级设置命令
    void    HandleAdvancedCommand(CWebSection& pWebSection, const char* data);

    // 查询/设置当前高级设置
    void    HandleAdvancedSetting(CWebSection& pWebSection, const char* data);

    // 设置系统日期时间
    void    HandleSetSystemDateTime(CWebSection& pWebSection, const char* data);

    // 重置服务器数据
    void    HandleResetServerData(CWebSection& pWebSection, const char* data);

    // 服务器备份数据
    void    HandleBackupServerData(CWebSection& pWebSection, const char* data);

    // 获取服务器备份文件名称
    void    HandleGetBackupData(CWebSection& pWebSection, const char* data);

    // 服务器还原数据
    void    HandleRestoreServerData(CWebSection& pWebSection, const char* data);

    // 服务器上传数据(超级用户权限)
    void    HandleUploadServerData(CWebSection& pWebSection, const char* data);

    // 移除备份文件
    void    HandleRemoveBackupData(CWebSection& pWebSection, const char* data);

    // 恢复出厂设置(超级用户权限)
    void    HandleFactoryReset(CWebSection& pWebSection, const char* data);

// 服务器升级
    // 请求上传升级包到服务器
    void    HandleUploadGradeBagToServer(CWebSection& pWebSection, const char* data);

    // 请求升级服务器
    void    HandleUpGradeServer(CWebSection& pWebSection, const char* data);

    // 服务器重启
    void    HandleRebootServer(CWebSection& pWebSection);

    // 关于服务器
    void    HandleServerAbout(CWebSection& pWebSection);

    // 服务器注册
    void    HandleRegisterServer(CWebSection& pWebSection, const char* data);
#if APP_IS_LZY_COMMERCE_VERSION
    // TTS授权注册
    void    HandleTTSRegister(CWebSection& pWebSection, const char* data);
    // TTS试用申请
    void    HandleTTSTrial(CWebSection& pWebSection, const char* data, bool bApplyTrial = true);
#endif
    void    HandleCloudControlRegister(CWebSection& pWebSection, const char* data);
    // 获取系统存储信息
    void HandleGetSystemStorage(CWebSection &pWebSection);

    // 设置/查询系统网络配置
    void HandleSetSystemNetwork(CWebSection &pWebSection,const char *data);

    // 设置/查询主备服务器
    void HandleSetBackupServer(CWebSection &pWebSection,const char *data);

/////////////////////////////////////////////////////////////////////////////////////////////////
    /*******           播放任务管理                *********/
    //  处理播放任务管理命令
    void    HandlePlayTaskCommand(CWebSection &pWebSection, const char *data);
    // 设置任务播放模式
    void    HandleSetPlayTask_PlayMode(CWebSection &pWebSection, const char *data);
    // 设置任务播放状态
    void    HandleSetPlayTask_PlayStatus(CWebSection &pWebSection, const char *data);
    // 设置任务播放上下曲
    void    HandleSetPlayTask_PreNext(CWebSection &pWebSection, const char *data);
    #if SUPPORT_MANUALTASK_PROGRESS
    // 设置播放任务进度
    void    HandleSetPlayTask_Progress(CWebSection &pWebSection, const char *data);
    #endif
private:
    // 发送参数错误响应并清理资源
    void    SendParameterErrorResponse(CWebSection* pWebSection, cJSON* root, const string& functionName = "");

    // 是否拥有权限
    bool    IsHaveAuthority(LPCUserInfo pUser, string strCommand);


    bool    IsGroupManagerCommand(string strCommand,bool bIsSuperUser);     // 是否分组管理命令
    bool    IsEditPlayListCommand(string strCommand,bool bIsSuperUser);     // 是否编辑播放列表命令
    bool    IsUserManagerCommand(string strCommand,bool bIsSuperUser);      // 是否用户管理命令
    bool    IsTimerManagerCommand(string strCommand,bool bIsSuperUser);     // 是否定时管理命令
    bool    IsDeviceManagerCommand(string strCommand,bool bIsSuperUser);    // 是否设备管理命令
    bool    IsAdvancedSettingCommand(string strCommand,bool bIsSuperUser);  // 是否高级设置命令
    bool    IsPlayTaskManagerCommand(string strCommand,bool bIsSuperUser);  // 是否播放任务管理任务
    int     GetSectionStatus(CSection* pSection);         // 获取Section状态(离线，不存在等)

private:
//
    uint8_t fin_;                   // FIN 位    描述消息是否结束
    uint8_t opcode_;                // OPCODE位  表示消息接收类型
    uint8_t mask_;                  // MASK位    标识PayloadData是否经过掩码处理
    uint8_t masking_key_[4];        // MASKKEY   掩码值，如果MASK为0则为0，否则payloadData按位异或
    uint64_t payload_length_;       // PayloadData的长度
    char payload_[MAX_BUF_LEN];     // 实体数据
};




#endif // WEBHANDLE_H
