#include "NodeJSManager.h"
#include "../../stdafx.h"
#include "../../Global/GlobalMethod.h"
#include <QCoreApplication>
#include <QDir>
#include <QFile>
#include <QTextStream>
#include <QStandardPaths>
#include <QDebug>
#include <QMutexLocker>
#include <QProcessEnvironment>

NodeJSManager* NodeJSManager::m_instance = nullptr;
QMutex NodeJSManager::m_instanceMutex;

NodeJSManager* NodeJSManager::getInstance()
{
    QMutexLocker locker(&m_instanceMutex);
    if (m_instance == nullptr)
    {
        m_instance = new NodeJSManager();
    }
    return m_instance;
}

NodeJSManager::NodeJSManager(QObject* parent)
    : QObject(parent)
    , m_nodeProcess(nullptr)
    , m_heartbeatTimer(new QTimer(this))
    , m_isStarting(false)
{
    // 设置心跳定时器
    m_heartbeatTimer->setInterval(HEARTBEAT_INTERVAL_MS);
    connect(m_heartbeatTimer, &QTimer::timeout, this, &NodeJSManager::onHeartbeatTimeout);
}

NodeJSManager::~NodeJSManager()
{
    stopRadioJS();
}

bool NodeJSManager::startRadioJS()
{
    QMutexLocker locker(&m_processMutex);
    
    if (m_isStarting || (m_nodeProcess && m_nodeProcess->state() != QProcess::NotRunning))
    {
        qDebug() << "Node.js process is already running or starting";
        return true;
    }
    
    m_isStarting = true;
    
    // 获取Node.js可执行文件路径
    QString nodeExePath = getNodeExecutablePath();
    if (nodeExePath.isEmpty())
    {
        qDebug() << "Failed to find Node.js executable";
        m_isStarting = false;
        return false;
    }
    
    // 创建临时radio.js文件
    m_tempRadioJSPath = createTempRadioJSFile();
    if (m_tempRadioJSPath.isEmpty())
    {
        //qDebug() << "Failed to create temporary radio.js file";
        m_isStarting = false;
        return false;
    }
    
    // 创建QProcess实例
    m_nodeProcess = std::make_unique<QProcess>(this);
    
    // 连接信号
    connect(m_nodeProcess.get(), QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            this, &NodeJSManager::onProcessFinished);
    connect(m_nodeProcess.get(), &QProcess::errorOccurred,
            this, &NodeJSManager::onProcessError);
    connect(m_nodeProcess.get(), &QProcess::started,
            this, &NodeJSManager::onProcessStarted);
    
    // 设置进程环境变量
    setupProcessEnvironment();
    
    // 设置工作目录为node_modules所在目录
    QString nodeModulesPath = getNodeModulesPath();
    m_nodeProcess->setWorkingDirectory(nodeModulesPath);
    
    // 启动Node.js进程
    QStringList arguments;
    arguments << m_tempRadioJSPath;
    
    //qDebug() << "Starting Node.js process:" << nodeExePath << "with args:" << arguments;
    qDebug() << "Working directory:" << nodeModulesPath;
    
    m_nodeProcess->start(nodeExePath, arguments);
    
    // 等待进程启动
    if (!m_nodeProcess->waitForStarted(5000))
    {
        qDebug() << "Failed to start Node.js process:" << m_nodeProcess->errorString();
        m_nodeProcess.reset();
        removeTempRadioJSFile();
        m_isStarting = false;
        return false;
    }
    
    //qDebug() << "Node.js process started successfully";
    return true;
}

void NodeJSManager::stopRadioJS()
{
    QMutexLocker locker(&m_processMutex);
    
    m_heartbeatTimer->stop();
    
    if (m_nodeProcess && m_nodeProcess->state() != QProcess::NotRunning)
    {
        qDebug() << "Stopping Node.js process...";
        
        // 先尝试优雅关闭
        m_nodeProcess->terminate();
        
        // 等待2秒，如果还没关闭则强制杀死
        if (!m_nodeProcess->waitForFinished(2000))
        {
            qDebug() << "Force killing Node.js process...";
            m_nodeProcess->kill();
            m_nodeProcess->waitForFinished(1000);
        }
        
        m_nodeProcess.reset();
    }
    
    // 删除临时文件
    removeTempRadioJSFile();
    
    m_isStarting = false;
    
    qDebug() << "Node.js process stopped";
}

bool NodeJSManager::isRunning() const
{
    QMutexLocker locker(const_cast<QMutex*>(&m_processMutex));
    return m_nodeProcess && m_nodeProcess->state() == QProcess::Running;
}

QString NodeJSManager::getProcessStatus() const
{
    QMutexLocker locker(const_cast<QMutex*>(&m_processMutex));
    
    if (!m_nodeProcess)
    {
        return "Not initialized";
    }
    
    switch (m_nodeProcess->state())
    {
    case QProcess::NotRunning:
        return "Not running";
    case QProcess::Starting:
        return "Starting";
    case QProcess::Running:
        return "Running";
    default:
        return "Unknown";
    }
}

QString NodeJSManager::getNodeExecutablePath() const
{
#if defined(Q_OS_WIN32)
    // Windows平台使用tools目录下的node.exe
    QString toolsPath = QString::fromStdString(g_Global.m_strRunDirPath.Data()) + "/tools";
    QString nodeExePath = toolsPath + "/node.exe";
    
    if (QFile::exists(nodeExePath))
    {
        return nodeExePath;
    }
    
    qDebug() << "node.exe not found in tools directory:" << nodeExePath;
    return QString();
#else
    // Linux平台直接调用node
    return "node";
#endif
}

QString NodeJSManager::getNodeModulesPath() const
{
    return QString::fromStdString(g_Global.m_strFolderPath.Data());
}

QString NodeJSManager::createTempRadioJSFile()
{
    QString tempDir = QString::fromStdString(g_Global.m_strTempFolderPath.Data());
    QString tempFilePath = tempDir + "/radio_temp.js";
    
    // 确保临时目录存在
    QDir dir;
    if (!dir.exists(tempDir))
    {
        if (!dir.mkpath(tempDir))
        {
            qDebug() << "Failed to create temp directory:" << tempDir;
            return QString();
        }
    }
    
    // radio.js的完整内容
    QString radioJSContent = R"(const WebSocket = require('ws');
const ffmpeg = require('child_process').spawn;
const { PassThrough } = require('stream');
const path = require('path');
const os = require('os');

const ENCRYPT_PREFIX = 'ENCRYPTED_RADIO_';
const MYSEC_KEY = 'MhitechRadioKey0';

// 小端 32 位读写
function bytesToWords(bin) {
  const bytes = Buffer.from(bin, 'binary');
  // 确保长度是4的倍数，用0填充
  const paddedLength = Math.ceil(bytes.length / 4) * 4;
  const paddedBytes = Buffer.alloc(paddedLength);
  bytes.copy(paddedBytes);
  
  const words = [];
  for (let i = 0; i < paddedBytes.length; i += 4) {
    words.push(paddedBytes.readUInt32LE(i));
  }
  return words;
}

function wordsToBytes(words) {
  const buf = Buffer.alloc(words.length * 4);
  words.forEach((w, i) => {
    // 确保w是32位无符号整数
    const value = (w >>> 0); // 转换为32位无符号整数
    buf.writeUInt32LE(value, i * 4);
  });
  return buf.toString('binary');
}

function padTo4(str) {
  const pad = (4 - (str.length & 3)) & 3;
  return str + '\0'.repeat(pad);
}

function unpad(str) {
  return str.replace(/\0+$/, '');
}

function mySecDecrypt(data, key) {
  if (data.length % 4) return null;
  const v = bytesToWords(data);
  const k = bytesToWords(key.padEnd(16, '\0'));
  const n = v.length;
  if (n <= 1) return wordsToBytes(v);
  
  let rounds = 6 + Math.floor(52 / n);
  let sum = (rounds * 0x9E3779B9) >>> 0;
  let y = v[0];
  let z, p, e;
  
  do {
    e = (sum >>> 2) & 3;
    for (p = n - 1; p > 0; p--) {
      z = v[p - 1];
      v[p] = (v[p] - (((z >>> 5 ^ y << 2) + (y >>> 3 ^ z << 4)) ^ ((sum ^ y) + (k[(p & 3) ^ e] ^ z)))) >>> 0;
      y = v[p];
    }
    z = v[n - 1];
    v[0] = (v[0] - (((z >>> 5 ^ y << 2) + (y >>> 3 ^ z << 4)) ^ ((sum ^ y) + (k[(0 & 3) ^ e] ^ z)))) >>> 0;
    y = v[0];
    sum = (sum - 0x9E3779B9) >>> 0;
  } while (--rounds > 0);
  
  return wordsToBytes(v);
}

function tryDecryptRadioUrl(url) {
  if (!url.startsWith(ENCRYPT_PREFIX)) return url;

  const encoded = url.slice(ENCRYPT_PREFIX.length);
  const cipherBin = Buffer.from(encoded, 'base64').toString('binary');

  const plainBin = mySecDecrypt(cipherBin, MYSEC_KEY);
  if (!plainBin) return url;

  return unpad(plainBin);
}

// 获取FFmpeg可执行文件路径
function getFFmpegPath() {
    if (os.platform() === 'win32') {
        // Windows平台使用与node.exe同路径下的ffmpeg.exe
        // 使用process.execPath获取node.exe的完整路径，然后获取其目录
        const nodeDir = path.dirname(process.execPath);
        return path.join(nodeDir, 'ffmpeg.exe');
    } else {
        // Linux平台直接使用ffmpeg命令
        return 'ffmpeg';
    }
}

// 创建WebSocket服务器
const wss = new WebSocket.Server({ 
    port: 8085,
    perMessageDeflate: false  // 禁用压缩，减少CPU开销
});

// 客户端连接管理
const clients = new Map(); // 使用Map来存储每个客户端的状态
const HEARTBEAT_TIMEOUT = 15000; // 15秒心跳超时

// 客户端状态类
class ClientSession {
    constructor(ws) {
        this.ws = ws;
        this.ffmpegProcess = null;
        this.audioStream = null;
        this.currentRadioUrl = null;
        this.lastHeartbeatTime = Date.now();
        this.dataHandler = null;
    }

    // 启动FFmpeg进程
    startFFmpeg(radioUrl) {
        // 如果已有进程，先完全清理
        if (this.ffmpegProcess || this.audioStream) {
            console.log(`客户端 ${this.getClientId()} 清理现有资源`);
            this.stopFFmpeg();
            // 给一点时间让清理完成
            setTimeout(() => this._doStartFFmpeg(radioUrl), 100);
            return;
        }
        
        this._doStartFFmpeg(radioUrl);
    }

    // 实际启动FFmpeg进程的方法
    _doStartFFmpeg(radioUrl) {
        // 保存当前电台URL
        this.currentRadioUrl = radioUrl;
        console.log(`客户端 ${this.getClientId()} 启动FFmpeg进程，源URL: ${radioUrl}`);

        // 优化后的FFmpeg参数
        const args = [
            // 日志级别设置为error，减少日志输出
            '-loglevel', 'error',
            // 输入选项
            '-fflags', '+genpts+discardcorrupt',  // 生成时间戳，丢弃损坏的数据
            '-thread_queue_size', '512',         // 增加线程队列大小
            '-analyzeduration', '1000000',       // 分析持续时间1秒
            '-probesize', '1000000',             // 探测大小1MB
            // 输入源
            '-i', radioUrl,
            // 重连选项
            '-reconnect', '1',
            '-reconnect_at_eof', '1',
            '-reconnect_streamed', '1',
            '-reconnect_delay_max', '5',
            // 超时设置
            '-timeout', '10000000',              // 10秒超时
            '-stimeout', '10000000',             // 10秒流超时
            // 音频编码选项
            '-vn',                               // 无视频
            '-c:a', 'libmp3lame',                // 使用MP3编码
            '-b:a', '64k',                      // 比特率
            '-ar', '44100',                      // 采样率
            '-ac', '2',                          // 声道数
            // 缓冲区设置
            '-bufsize', '256k',                  // 增加缓冲区大小
            '-max_muxing_queue_size', '1024',    // 最大复用队列大小
            // 输出选项
            '-flush_packets', '1',               // 立即刷新包
            '-f', 'mp3',                         // 输出格式
            '-movflags', '+faststart',           // 快速启动
            '-'                                  // 输出到标准输出
        ];

        try {
            const ffmpegPath = getFFmpegPath();
            console.log(`使用FFmpeg路径: ${ffmpegPath}`);
            this.ffmpegProcess = ffmpeg(ffmpegPath, args);
            
            // 创建音频流
            this.audioStream = new PassThrough({
                highWaterMark: 1024 * 1024  // 1MB缓冲区
            });

            // 设置数据处理器
            this.dataHandler = (chunk) => {
                if (this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send(chunk, { binary: true });
                }
            };

            // 处理FFmpeg输出
            this.ffmpegProcess.stdout.on('data', (chunk) => {
                // 检查音频流是否仍然存在，防止竞态条件
                if (!this.audioStream) {
                    console.log(`客户端 ${this.getClientId()} 音频流已清理，忽略数据块`);
                    return;
                }
                
                // 将数据传递给音频流
                try {
                    if (!this.audioStream.write(chunk)) {
                        // 如果缓冲区满了，暂停读取
                        if (this.ffmpegProcess && this.ffmpegProcess.stdout) {
                            this.ffmpegProcess.stdout.pause();
                        }
                        
                        // 再次检查音频流是否存在
                        if (this.audioStream) {
                            this.audioStream.once('drain', () => {
                                if (this.ffmpegProcess && this.ffmpegProcess.stdout) {
                                    this.ffmpegProcess.stdout.resume();
                                }
                            });
                        }
                    }
                } catch (err) {
                    console.error(`客户端 ${this.getClientId()} 写入音频流失败:`, err);
                }
            });

            // 添加数据监听器
            this.audioStream.on('data', this.dataHandler);

            // 处理错误
            this.ffmpegProcess.stderr.on('data', (data) => {
                // 只记录真正的错误，忽略HLS相关的警告
                const errorStr = data.toString();
                if (errorStr.includes('error') || errorStr.includes('Error') || 
                    errorStr.includes('failed') || errorStr.includes('Failed')) {
                    console.error(`客户端 ${this.getClientId()} FFmpeg错误: ${errorStr}`);
                }
            });

            // 处理进程退出
            this.ffmpegProcess.on('close', (code) => {
                console.log(`客户端 ${this.getClientId()} FFmpeg进程退出，代码: ${code}`);
                
                // 避免重复处理
                if (!this.ffmpegProcess) {
                    return;
                }
                
                this.ffmpegProcess = null;
                
                // 通知客户端
                this.sendMessage({ 
                    type: 'status', 
                    message: 'FFmpeg进程已退出',
                    code: code
                });
                
                // 1秒后断开客户端连接并清理资源
                setTimeout(() => {
                    console.log(`客户端 ${this.getClientId()} FFmpeg进程退出，1秒后断开连接`);
                    this.disconnectClient();
                }, 1000);
            });

            // 处理进程错误
            this.ffmpegProcess.on('error', (err) => {
                console.error(`客户端 ${this.getClientId()} FFmpeg进程错误: ${err}`);
                
                // 避免重复处理
                if (!this.ffmpegProcess) {
                    return;
                }
                
                this.ffmpegProcess = null;
                
                // 通知客户端
                this.sendMessage({ 
                    type: 'error', 
                    message: 'FFmpeg进程错误',
                    error: err.message
                });
                
                // 1秒后断开客户端连接并清理资源
                setTimeout(() => {
                    console.log(`客户端 ${this.getClientId()} FFmpeg进程错误，1秒后断开连接`);
                    this.disconnectClient();
                }, 1000);
            });

            // 通知客户端转码已开始
            this.sendMessage({ 
                type: 'status', 
                message: '转码已开始',
                url: radioUrl
            });

        } catch (err) {
            console.error(`客户端 ${this.getClientId()} 启动FFmpeg失败:`, err);
            this.ffmpegProcess = null;
            // 通知客户端启动失败
            this.sendMessage({ 
                type: 'error', 
                message: '启动FFmpeg失败',
                error: err.message
            });
        }
    }

    // 停止FFmpeg进程
    stopFFmpeg() {
        console.log(`客户端 ${this.getClientId()} 停止FFmpeg进程`);
        
        // 先清理音频流，防止FFmpeg进程继续写入
        if (this.audioStream && this.dataHandler) {
            this.audioStream.removeListener('data', this.dataHandler);
            this.dataHandler = null;
        }
        
        // 销毁音频流
        if (this.audioStream) {
            try {
                this.audioStream.destroy();
            } catch (err) {
                console.error(`客户端 ${this.getClientId()} 销毁音频流失败:`, err);
            }
            this.audioStream = null;
        }
        
        // 然后停止FFmpeg进程
        if (this.ffmpegProcess) {
            try {
                this.ffmpegProcess.kill('SIGTERM');
            } catch (err) {
                console.error(`客户端 ${this.getClientId()} 终止FFmpeg进程失败:`, err);
            }
            this.ffmpegProcess = null;
        }
        
        this.currentRadioUrl = null;
        
        // 通知客户端转码已停止
        this.sendMessage({ 
            type: 'status', 
            message: '转码已停止'
        });
    }

    // 发送消息给客户端
    sendMessage(data) {
        if (this.ws.readyState === WebSocket.OPEN) {
            try {
                this.ws.send(JSON.stringify(data));
            } catch (err) {
                console.error(`发送消息到客户端 ${this.getClientId()} 失败:`, err);
            }
        }
    }

    // 获取客户端ID（用于日志）
    getClientId() {
        return this.ws._socket ? `${this.ws._socket.remoteAddress}:${this.ws._socket.remotePort}` : 'unknown';
    }

    // 断开客户端连接并清理资源
    disconnectClient() {
        console.log(`主动断开客户端 ${this.getClientId()} 连接`);
        
        try {
            // 清理所有资源
            this.cleanup();
            
            // 从客户端列表中移除
            if (clients.has(this.ws)) {
                clients.delete(this.ws);
            }
            
            // 关闭WebSocket连接
            if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
                this.ws.close(1000, 'FFmpeg进程异常，服务器主动断开连接');
            }
        } catch (err) {
            console.error(`断开客户端 ${this.getClientId()} 连接时发生错误:`, err);
        }
    }

    // 清理资源
    cleanup() {
        console.log(`客户端 ${this.getClientId()} 清理资源`);
        
        // 停止FFmpeg进程（这会清理音频流）
        this.stopFFmpeg();
        
        // 确保所有资源都被清理
        if (this.audioStream) {
            try {
                this.audioStream.destroy();
            } catch (err) {
                console.error(`客户端 ${this.getClientId()} 清理时销毁音频流失败:`, err);
            }
            this.audioStream = null;
        }
        
        if (this.dataHandler) {
            this.dataHandler = null;
        }
        
        this.currentRadioUrl = null;
    }

    // 检查心跳超时
    checkHeartbeatTimeout() {
        const now = Date.now();
        if (now - this.lastHeartbeatTime > HEARTBEAT_TIMEOUT && this.ffmpegProcess) {
            console.log(`客户端 ${this.getClientId()} 心跳超时，停止FFmpeg进程`);
            this.stopFFmpeg();
        }
    }
}
// 处理新的客户端连接
wss.on('connection', (ws) => {
    console.log('新的客户端连接');
    
    // 创建客户端会话
    const clientSession = new ClientSession(ws);
    clients.set(ws, clientSession);
    
    // 发送连接确认
    clientSession.sendMessage({ 
        type: 'connection', 
        message: '连接成功',
        status: '空闲'
    });

    // 处理客户端消息
    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);
            console.log(`收到客户端 ${clientSession.getClientId()} 消息:`, data);
            
            // 处理播放命令
            if (data.type === 'play_radio_event') {
                if (data.event === 1 && data.radio_url) {
                    const realUrl = tryDecryptRadioUrl(data.radio_url);
                    clientSession.startFFmpeg(realUrl);
                } else if (data.event === 0) {
                    // 停止转码
                    clientSession.stopFFmpeg();
                }
            }
            // 处理心跳包
            else if (data.type === 'play_radio_heartbeat') {
                clientSession.lastHeartbeatTime = Date.now();
                // 回复心跳确认
                clientSession.sendMessage({ 
                    type: 'heartbeat', 
                    timestamp: clientSession.lastHeartbeatTime
                });
            }
        } catch (err) {
            console.error(`解析客户端 ${clientSession.getClientId()} 消息失败:`, err);
        }
    });

    // 处理连接关闭
    ws.on('close', () => {
        console.log(`客户端 ${clientSession.getClientId()} 断开连接`);
        clientSession.cleanup();
        clients.delete(ws);
    });

    // 处理错误
    ws.on('error', (err) => {
        console.error(`客户端 ${clientSession.getClientId()} WebSocket错误:`, err);
        clientSession.cleanup();
        clients.delete(ws);
    });
});

// 处理服务器错误
wss.on('error', (err) => {
    console.error('WebSocket服务器错误:', err);
});

// 启动心跳检查定时器
const heartbeatInterval = setInterval(() => {
    clients.forEach((clientSession) => {
        clientSession.checkHeartbeatTimeout();
    });
}, 5000);

// 优雅关闭
process.on('SIGINT', () => {
    console.log('收到SIGINT，正在关闭服务器...');
    
    // 清除心跳检查定时器
    if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
    }
    
    // 关闭所有客户端连接和FFmpeg进程
    clients.forEach((clientSession, ws) => {
        clientSession.cleanup();
        if (ws.readyState === WebSocket.OPEN) {
            ws.close();
        }
    });
    
    // 关闭WebSocket服务器
    wss.close(() => {
        console.log('WebSocket服务器已关闭');
        process.exit(0);
    });
});

// 处理未捕获的异常
process.on('uncaughtException', (err) => {
    console.error('未捕获的异常:', err);
    // 尝试恢复 - 清理所有客户端的FFmpeg进程
    clients.forEach((clientSession) => {
        clientSession.cleanup();
    });
});

console.log('WebSocket服务器已启动，监听端口: 8085');
console.log('等待客户端发送播放命令...');
console.log('每个客户端连接现在可以独立请求转码');
)";
    
    // 写入临时文件
    QFile tempFile(tempFilePath);
    if (!tempFile.open(QIODevice::WriteOnly | QIODevice::Text))
    {
        qDebug() << "Failed to create temp radio.js file:" << tempFilePath;
        return QString();
    }
    
    QTextStream out(&tempFile);
    out.setCodec("UTF-8");
    out << radioJSContent;
    tempFile.close();
    
    //qDebug() << "Created temporary radio.js file:" << tempFilePath;
    return tempFilePath;
}

void NodeJSManager::removeTempRadioJSFile()
{
    if (!m_tempRadioJSPath.isEmpty() && QFile::exists(m_tempRadioJSPath))
    {
        if (QFile::remove(m_tempRadioJSPath))
        {
            //qDebug() << "Removed temporary radio.js file:" << m_tempRadioJSPath;
        }
        else
        {
            //qDebug() << "Failed to remove temporary radio.js file:" << m_tempRadioJSPath;
        }
        m_tempRadioJSPath.clear();
    }
}



void NodeJSManager::setupProcessEnvironment()
{
    if (!m_nodeProcess)
        return;
    
    QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
    
    // 设置NODE_PATH指向node_modules目录
    QString nodeModulesPath = getNodeModulesPath() + "/node_modules";
    env.insert("NODE_PATH", nodeModulesPath);
    
    // 设置NODE_ENV
    env.insert("NODE_ENV", "production");
    
    m_nodeProcess->setProcessEnvironment(env);
}

void NodeJSManager::onProcessStarted()
{
    qDebug() << "radio server process started successfully";
    //延迟2秒，删除临时文件
    QTimer::singleShot(2000, this, [this]() {
        removeTempRadioJSFile();
    });
    
    // 启动心跳检查
    m_heartbeatTimer->start();
}

void NodeJSManager::onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    qDebug() << "Node.js process finished with exit code:" << exitCode 
             << "status:" << (exitStatus == QProcess::NormalExit ? "Normal" : "Crashed");
    
    m_heartbeatTimer->stop();
    
    // 清理资源
    removeTempRadioJSFile();
    m_isStarting = false;
    
    // 如果是异常退出，可以考虑重启
    if (exitStatus == QProcess::CrashExit)
    {
        qDebug() << "Node.js process crashed, consider restarting...";
    }
}

void NodeJSManager::onProcessError(QProcess::ProcessError error)
{
    QString errorString;
    switch (error)
    {
    case QProcess::FailedToStart:
        errorString = "Failed to start";
        break;
    case QProcess::Crashed:
        errorString = "Crashed";
        break;
    case QProcess::Timedout:
        errorString = "Timed out";
        break;
    case QProcess::WriteError:
        errorString = "Write error";
        break;
    case QProcess::ReadError:
        errorString = "Read error";
        break;
    case QProcess::UnknownError:
    default:
        errorString = "Unknown error";
        break;
    }
    
    qDebug() << "Node.js process error:" << errorString;
    
    m_heartbeatTimer->stop();
    removeTempRadioJSFile();
    m_isStarting = false;
}

void NodeJSManager::onHeartbeatTimeout()
{
    // 检查进程是否还在运行
    if (!isRunning())
    {
        qDebug() << "Node.js process is not running, stopping heartbeat";
        m_heartbeatTimer->stop();
        removeTempRadioJSFile();
    }
    else
    {
        //qDebug() << "Node.js process heartbeat check - process is running";
    }
}