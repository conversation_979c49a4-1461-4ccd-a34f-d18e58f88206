#include "stdafx.h"
#include "WebPaging.h"


CWebPaging::CWebPaging(unsigned int *pSecIndexs,unsigned int uSecCount,int volume,bool IsAdmin,string uuid)
{
    m_SecCount = uSecCount;
    m_IsAdmin = IsAdmin;
    memcpy(  m_pSecIndexs,pSecIndexs, sizeof(unsigned int)*uSecCount);
    m_volume = volume;
    g722_enc_ctx=NULL;
    #if SUPPORT_SPEEX_DENOISE
    speexSt_appPagingMic=NULL;
    #endif
    m_webPagingMutex = PTHREAD_MUTEX_INITIALIZER;

    m_uuid = uuid;
    
    m_mac[0] = uuid[0];
    m_mac[1] = uuid[1];
    m_mac[2] = uuid[2];
    m_mac[3] = uuid[3];
    m_mac[4] = uuid[4];
    m_mac[5] = uuid[5];
}

CWebPaging::~CWebPaging()
{
    webPagingLock();
    if(g722_enc_ctx!=NULL)
    {
        g722_encoder_destroy(g722_enc_ctx);
        g722_enc_ctx=NULL;
    }
    #if SUPPORT_SPEEX_DENOISE
    if(speexSt_appPagingMic != NULL)
    {
        speex_preprocess_state_destroy(speexSt_appPagingMic);
        speexSt_appPagingMic=NULL;
    }
    #endif
    webPagingUnLock();
}