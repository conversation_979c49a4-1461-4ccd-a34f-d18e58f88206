#ifndef WEBNETWORK_H
#define WEBNETWORK_H
#include "WebCommandSend.h"
#include "WebHandle.h"
#include "Network/Web/WebSocket.h"
#include "Network/Monitor/MonProc.h"
#include "Model/Other/VoipSipInfo.h"
#include "Model/Device/WebSection.h"
#include "Network/Protocol/CommandSend.h"
#include "Network/Protocol/CommandHandle.h"
#include "Network/Web/WebQueue.h"
#include "QtWebsocket.h"
#include "Network/Web/QtClientTransfer.h"

// 重置数据标识
enum
{
    WEB_RESET_NULL          = 0x0000,    // 初始化
    WEB_RESET_GROUP         = 0x0001,	 // 重置分组
    WEB_RESET_PLAY_LIST		= 0x0002,	 // 重置播放列表
    WEB_RESET_TIMER         = 0x0004,	 // 重置定时信息
    WEB_RESET_ZONE			= 0x0008,	 // 重置分区
    WEB_RESET_AUDIO			= 0x0010,	 // 重置音频采集器信息
    WEB_RESET_FIRE			= 0x0020,	 // 重置告警采集器信息
    WEB_RESET_MONITOR		= 0x0040,	 // 清除离线的监控设备
    WEB_RESET_FIRMWARE		= 0x0080,	 // 升级固件
    WEB_RESET_SEQUENCE_POWER= 0x0100,	 // 电源时序器文件
    WEB_RESET_LOG		    = 0x0200,	 // 日志文件
    WEB_RESET_PAGER		    = 0x0400,	 // 重置寻呼台文件
    WEB_RESET_ALL			= 0xFFFF,    // 重置所有文件
};

// 用户数据类型
enum
{
    UD_USER        = 0x01,         // 用户信息
    UD_LIMIT       = 0x02,         // 权限
    UD_SECTION     = 0x04,         // 分区
    UD_GROUP       = 0x08          // 分组
};


class CWebNetwork
{
public:
    CWebNetwork();
    ~CWebNetwork();

    void    StartWebWorking();

    //  分发今日定时点信息到WEB
    void ForwardTodayTimerInfoToWeb(CWebSection *pWebSection, vector<StTodayTimerP> TimerVec);

    // 分发手动任务信息到WEB
    void ForwardManualTaskInfoToWeb(CWebSection *pWebSection, vector<StManualTask> manualTaskVec);

    // 分发本地歌曲信息到WEB
    void ForwardLocalSongInfoToWeb(CWebSection *pWebSection);

    // 分发分区状态到所有webSocket，如果pSection为空，则分发全部分区
    void    ForwardSectionInfoToWeb(int nDeviceType, CSection* pSection, CWebSection* pWebSection, BOOL isPrint = FALSE);

    //分发变化的分区状态到webSection
    void ForwardVarySectionInfoToWeb(int nDeviceType, vector<string> &vecSectionMac, CWebSection *pWebSection, BOOL isPrint);

    // 服务器分发更新文件信息到所有webSection, 如果ft为0，则分发全部文件
    void    ForwardUpdateFileInfo(FileType ft); // 文件类型

    // 分发系统播放模式
    void    ForwardPlayMode(CWebSection *pWebSection,
                            int nPlayMode);
    void    ForwardPlayMode(string strUserAccount,
                            int nPlayMode);

    // 分发系统日期时间
    void    ForwardSystemDateTime(CWebSection *pWebSection,
                                  bool   bAutoMode,      // 自动设置时间
                                  string strDateTime);    // 日期时间

    // 请求用户重新登录
    void    RequestUserReLogin(string strUserAccount,
                               int nStatus,
                               int userCnt=0xff);

    // 分发监控设备信息
    void    ForwardMonitorInfo(CWebSection* pWebSection,
                               CMonitorInfo* pMonitorInfo);

    // 设备网络质量分发
    void    ForwardNetQuality(string strMac,              // 设备Mac地址
                              string strName,             // 设备名称
                              int nNetType,               // 网络类型
                              double dBandWidth = 0.0,    // 带宽
                              double dDelayed = 0.0,      // 延时
                              double dLossRate = 0.0);    // 丢包率


    // 向客户端分发监控设备的 监控事件
    void    ForwardMonitorEvent(CWebSection   *pWebSection,
                                const char*    szMac,
                                TriggerEvent  &triggerEvent);

    // 分发sip信息到web设备
    void    ForwardSipInfo( int   nPageCount,                     // 总页数
                            int   nPage,                          // 指定页数
                            int   nSipCount,                      // SIP信息数量
                            SipInfo* pSipInfos,                   // SIP数组
                            CWebSection  *pWebSection = NULL);    // WebSection，为空时，发送到全部Web终端

    // 下发SIP状态到设备
    void    ForwardSipStatus(CSection& device,      // 设备
                             CWebSection  *pWebSection = NULL);   // WebSection，为空时，发送到全部Web终端

    // 下发设备文件需要更新信息
    void    ForwardDeviceNeedUpdateFile(CWebSection   *pWebSection,  // Web终端
                                        FileType ft,                 // 文件类型
                                        string strFileDateTime,      // 文件时间
                                        string strMac,               // Mac地址
                                        DeviceModel model);          // 设备类型

    // 下发设备文件更新实时状态
    void    ForwardDeviceFileStatus(CWebSection *pWebSection,       // Web终端
                                    DeviceModel model,              // 设备类型
                                    FileType    ft,                 //  文件类型
                                    string      strMac,             //  Mac地址
                                    int         nSyncStatus);       //  同步状态

    // 下发设备歌曲同步状态
    void    ForwardUpdateSongStatus(CWebSection *pWebSection,   // Web终端
                                    DeviceModel model,          // 设备类型
                                    FileType ft,                // 文件类型
                                    string     strMac,          // Mac地址
                                    int nUpdateStatus,          // 同步状态
                                    int nSongCount = 0,         // 一共需要同步的歌曲数目
                                    int nFinishCount = 0,       // 已完成同步的歌曲数目
                                    int nPercentage = 0);       // 正在同步歌曲的百分比

    // 下发设备网络信息
    void    ForwardDeviceNetInfo(CWebSection *pWebSection,   // Web终端
                                 CSection& device);                   // 设备

    void    ForwardDeviceNetModeInfo(CWebSection *pWebSection, // Web终端
                                    CSection& device);         // 设备

    // 下发固件升级进度状态
    void    ForwardUpgradeDeviceStatus(CWebSection *pWebSection,   // Web终端
                                       string strMac,              // Mac地址
                                       int nUpgrade = 0,           // 升级状态
                                       int nPercentage = 0);       // 升级百分比

    // 下发用户信息
    void    ForwardUserInfo(string strSendToAccount,                       //发往账户名称
                                  string strDestAccount,                         // 目标账户名
                                  int nFlags);                                    // 数据类型标识

    // 分发设备日志列表(暂只发送到超级用户)
    void    ForwardDevceLogList(CSection& device);

    // 分发设备日志下载进度
    void    ForwardDevceLogDownSche(CSection& device, CLogFile *pLogFile);



#if 0
    // 下发用户分组信息
    void    ForwardUserGroup(CWebSection *pWebSection,     // Web终端
                             CUserInfo     *pUser);                // 用户信息
#endif

    // 分发系统备份信息
    void    ForwardBackupData(CWebSection *pWebSection);

    /**********************************************************/

    // 检测大屏设备掉线情况
    void    TabletsCheckOffline(ctime_t tNow);

    // 检测web浏览器设备掉线情况
    void    WebCheckOffline(ctime_t tNow);

    // web掉线
    void   WebDeviceOffline(string strSocketID);

    // 重置服务器数据
    void    ResetServerData(int nFileFlag, bool bRestoreFactory = false);

    // 重置用户保存的设备文件时间
    void    ResetUserFileDateTime(CWebSection *pWebSection,     // Web终端
                                  string strMac,      // 设备MAC
                                  FileType ft);       // 文件类型

    void    ForwardAccountStorageCapacity(string strNotifyAccount,string strDestAccount);
#if 0
    // 备份服务器数据
    void    BackupServerData(int nFileFlag);

    // 还原服务器数据
    void    RestoreServerData(int nFileFlag);
#endif

    // networkInfo
    void    ForwardNetworkInfo();

    void    ForwardIntercomStatusToWeb(CWebSection *pWebSection,CSection &callingDevice,CSection &calledDevice,int callStatus);

    // 分发备用服务器信息到WEB客户端（只发给管理员用户）
    void    ForwardBackupServerInfoToWeb(CWebSection *pWebSection);

public:
    CWebHandle           m_WebHandle;

    //CWebSocket            m_WebSock;
    QtWebsocket          m_QWebSock;

    CWebCommandSend      m_WebSend;

    CWebQueues           m_WebQueues;

    QtClientTransfer     m_WsQTransferClient;

};


#endif // WEBNETWORK_H
