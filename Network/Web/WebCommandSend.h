#ifndef WEBCOMMANDSEND_H
#define WEBCOMMANDSEND_H

#include "Model/Other/VoipSipInfo.h"
#include "Model/Device/WebSection.h"
#include "Network/Monitor/MonProc.h"
#include "WebProtocol.h"





/**********************************************************/
/*           Web协议发送类                                                                      */
/**********************************************************/

class CWebCommandSend
{
public:
    CWebCommandSend();
    ~CWebCommandSend();


    // 获取客户端状态
    void    WebGetClientStatus(CWebSection* pWebSec);    // 控制设备

    // 发送更新文件信息
    void    SendFileInfo(CWebSection& pWebSec,  // 设备
                         int  nFileType);       // 文件类型

    // 发送单个分区状态到Web设备
    void    WebSendOneZoneInfo(int nDeviceType,          // 设备类型
                               CSection *pSection,       // 终端设备
                               CWebSection* pWebSec,     // 控制设备
                               BOOL isPrint = FALSE);

    // 发送变化的分区状态到Web设备
    void    WebSendVaryZoneInfo(int nDeviceType,          // 设备类型
                               vector<string> &vecSectionMac,   // 传递vector
                               CWebSection* pWebSec,     // 控制设备
                               BOOL isPrint = FALSE);
#if 0
    // 根据页数 发送分区信息
    void    WebSendPageZoneInfo(int nDeviceType,          // 设备类型
                                int nPage,                // 页数
                                CWebSection *pWebSec,     // 终端设备
                                bool bAllZone = false);   // 是否请求全部分区

    // 发送全部分区信息
    void    WebSendAllZoneInfo(int nDeviceType,          // 设备类型
                               CWebSection *pWebSec);    // 终端设备
#endif

    // 发送全部分区信息(一次性)
    void    WebSendAllZoneInfo_Once(int nDeviceType,          // 设备类型
                               CWebSection *pWebSec);    // 终端设备                           

    // 发送用户分区信息
    void    WebSendUserZoneInfo(CUserInfo&    pSubUser,     // 用户
                                CWebSection*  pWebSection,  // 终端
                                int  nPage = -1);           // 页数，为-1时发送全部数据

    void WebSendGroupInfo(char* groupId, CWebSection *pWebSec); //发送分组信息

    void WebSendPlaylistInfo(char* playlistId, CWebSection *pWebSec);   //发送歌曲列表信息

    void WebSendTimerInfo(int schemeId, CWebSection *pWebSec); //发送定时信息

    void WebSendFireCollectorInfo(CWebSection *pWebSec);       //发送消防采集器信息

    void WebSendAudioCollectorInfo(CWebSection *pWebSec);       //发送音频采集器信息

    void WebSendTodayTimerInfo(CWebSection *pWebSec,vector<StTodayTimerP> TimerVec);          //发送今日定时点信息

    void WebSendManualTaskInfo(CWebSection *pWebSec,vector<StManualTask> manualTaskVec);   //发送手动任务信息

    void WebSendLocalSongInfo(CWebSection *pWebSec);      //发送本地歌曲信息

    void WebSendSequencePowerInfo(CWebSection *pWebSec);    //发送电源时序器信息
#if SUPPORT_REMOTE_CONTROLER
    void WebSendRemoteControlerInfo(CWebSection *pWebSec); //发送远程遥控器信息
#endif
#if 0
    // 发送用户分组信息
    void    WebSendUserGroupInfo(CUserInfo& pSubUser,               // 用户
                                                          CWebSection*  pWebSection,    // 终端
                                                          int  nPage = -1);                           // 页数，为-1时发送全部数据
#endif

    void WebSendAccountStorageCapacity(CWebSection *pWebSec,string strAccount);     //发送对应账户的存储信息给WEB

// 监控
    // 主机下发监控设备的信息
    void    WebForwardMonitorInfo(CWebSection  *pWebSec,            // 设备
                                  int          nMonitorCount,       // 设备数量
                                  LPCMonitorInfo *pMonitorInfos);     // 监控设备信息数组


    // 主机下发监控设备的上报事件
    void    WebForwardMonitorEvent(CWebSection *pWebSec,    // 设备
                                   const char*  szMac,      // Mac
                                   u_char   uEvent,         // 事件类型
                                   const char* szTime,      // 时间
                                   u_char   direction);     // 入侵方向


    /*
    // 主机下发sip信息
    void    WebForwardSipInfo(int nPageCount,
                              int nPage,
                              CWebSection *pWebSec,
                              int nSipCount,
                              SipInfo *pSipInfos);
    */

    // 添加用户 回复
    void    WebResponseAddUser(string strComID,         // 命令ID
                               string strAccount,       // 用户名
                               CWebSection *pWebSec,
                               int nResult = EC_SUCCESS);   //  返回结果

    // 编辑用户 回复
    void   WebResponseModifyUser(string strComID,     // 命令ID
                                 string strAccount,   // 用户名
                                 CWebSection *pWebSec,
                                 int nResult = EC_SUCCESS);   //  返回结果

    // 添加分组 回复
    void    WebResponseAddGroup(string strComID,            // 命令标识
                                string strGroupName,        // 分组名称
                                int      nZoneCount,        // 分区数量
                                CWebSection *pWebSec,
                                int nResult = EC_SUCCESS);  // 返回结果

    // 编辑分组 回复
    void    WebResponseModifyGroup(string strComID,            // 命令标识
                                   string strGroupID,          // 组ID
                                   string strGroupName,        // 分组名称
                                   int    nZoneCount,          // 分区数量
                                   CWebSection *pWebSec,
                                   int nResult = EC_SUCCESS);  // 返回结果

    // 添加定时点信息
    void    WebResponseAddTimePoint(int nTimeSchemeID,      // 方案序号
                                    string strComID,        // 命令ID
                                    CExTimePoint &tp,       // 定时点信息
                                    CWebSection *pWebSec,
                                    int  nResult = EC_SUCCESS);    // 返回结果

    // 编辑定时点信息
    void    WebResponseModifyTimePoint(int nTimeSchemeID,      // 方案序号
                                       string strComID,        // 命令ID
                                       CExTimePoint &tp,       // 定时点信息
                                       CWebSection *pWebSec,
                                       int  nResult = EC_SUCCESS);  // 返回结果

    // 客户端向服务器发起对讲请求 回复
    void    WebResponseTalkback(CWebSection *pWebSec, int nResult = EC_SUCCESS);

    // 客户端向服务器发起广播(寻呼)请求
    void    WebResponsePaging(int nType,        // 寻呼类型 1 : 组播  2: 单播
                              int nRtpType,     // Rtp类型
                              int nPage,        // 指定页数
                              CWebSection *pWebSec,   // Web终端
                              int nResult = EC_SUCCESS);    // 返回结果

    // 客户端向服务器发起监听请求
    void    WebResponseListen(CWebSection *pWebSec,   // Web终端
                              int nResult = EC_SUCCESS);    // 返回结果

    // 客户端向服务器发送挂断请求
    void    WebResponseHangup(string strExten,
                              CWebSection *pWebSec,         // Web终端
                              int nResult = EC_SUCCESS);    // 返回结果

    // 重置设备数据 回复
    void    WebResponseResetDeviceData(string strMac,
                                       CWebSection *pWebSec);

    // 处理返回EMC状态
    void    WebResponseEmcStatus(string strMac, bool bStatus, CWebSection *pWebSec);

    // 处理返回EQ音效
    void    WebResponseDeviceEQ(CSection &device, CWebSection *pWebSec);

    // 处理返回蓝牙信息
    void    WebResponseDeviceBTinfo(CSection &device, CWebSection *pWebSec);

    // 处理返回查询混音模式
    void    WebResponseDeviceMixing(CSection& device,		// 设备
                                    CWebSection *pWebSec);

    // 处理返回电源输出模式
    void    WebResponsePowerOutputMode(CSection& device,		// 设备
                                       CWebSection *pWebSec);   // Web终端

    // 处理返回电源输出状态
    void    WebResponseSplitterStatus(string strMac,            // 设备Mac
                                      int nChannelStatus,       // 通道状态
                                      CWebSection *pWebSec);    // Web终端

    // 处理返回无线MIC的状态
    void    WebResponseMicStatus(string strMac,                 // 设备Mac
                                    int volume,                 // 音量
                                    int power,                  // 功率
                                    int channel,                // 通道
                                    CWebSection *pWebSec);      // Web终端

    // 处理返回设备升级
    void    WebResponseUpgradeDevice(CWebSection *pWebSec,  // Web终端
                                     string strMac,         // 设备mac地址
                                     int nUpgradeStatus,    // 更新状态
                                     int nPer,                    //  升级进度百分比
                                     int nResult = EC_SUCCESS);   // 返回结果

    // 处理查询GPS信息
    void    WebResponseGPSTimeZone(string strMac, const char *pData, CWebSection *pWebSec);


    // 设置消防采集器
    void    WebResponseSetFireCollector(CWebSection *pWebSec,    // Web终端
                                        string strComID,         // 命令ID
                                        string strMac,           // 设备mac
                                        string strName,          // 设备名称
                                        int    nChannelCount,    // 消防通道数量
                                        int  nResult = EC_SUCCESS);  // 返回结果

    // 处理返回IP属性信息
    void    WebResponseDeviceIP(string strMac, const char *pData, CWebSection *pWebSec);

    // 回复错误码
    void    WebResponseErrorCode(CWebSection* pWebSec,      // 设备
                                 int nResult,               // 结果
                                 string strCommand);        // 命令


/*************************************************/

public:
    void    CommandSend(const char*  buf,        // 数据
                        CWebSection& pWebSec,    // 设备
                        bool isPrint = true,     // 是否打印
                        bool isSend = true,      // 是否立即发送
                        bool isCheckValid = true);  // 是否检查有效性，区分用户登录错误与服务器回调信息


    // 发送数据到大屏
    void    CommandSendToTablet(const char*  buf,
                                CWebSection& pWebSec);

    // 发送数据到web界面
    void    CommandSendToWeb(const char*  buf,
                             CWebSection& pWebSec,
                             bool isSend = true);           // 是否立即发送

    // 转发数据到Web端设备
    void    ForwardDataToWeb(CWebSection* pWebSection,      // Web终端设备
                             string       strData,          // 转发数据
                             bool         bSuper = false,   // 仅发送到超级用户
                             bool         isPrint = true,   // 是否打印
                             bool         isForcedSend = false);  // 是否强制发送

    // 推送设备对讲状态到WEB
    void    PushIntercomStatusToWeb(CWebSection *pWebSec,CSection &callingDevice,CSection &calledDevice,int callStatus);

    // 推送备用服务器信息到WEB
    void  PushBackupServerInfoToWeb(CWebSection *pWebSec);

private:
    // 把实体数据组合成webSocket实际内容
    //char* WebDataCombination(const char* buf, int* nLen);

    pthread_mutex_t  m_mutex;
};


#endif // WEBCOMMANDSEND_H
