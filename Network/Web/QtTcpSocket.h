#ifndef QTTCPSOCKET_H
#define QTTCPSOCKET_H

#include <iostream>
#include <QTcpServer>
#include <QTcpSocket>
#include <QHostAddress>
#include <QNetworkInterface>
#include "QtWebsocket.h"

using namespace std;

class QtTcpSocket: public QObject
{
    Q_OBJECT

public:
    QtTcpSocket();

    void    StartTCPSocket();
    void    AddCommandBuf(string strUID, string strBuf);

Q_SIGNALS:
        void send();

private Q_SLOTS:
    void    connected();             // 新连接
    void    recvData();              // 接收数据
    void    socketDisconnected();    // 断开连接
    void    SendData();

    void    OnTimeOut();

private:
    QTimer  m_Timer;       // 定时器
    QMutex  m_mutex;

    QTcpServer *m_tcpServer;
    QTcpSocket *pTcpSocket;

    map<string, QTcpSocket*> m_TcpSockets;

    int     m_nCurSendID;
    bool    m_bSending;

    list<CommandData>      m_sendData1;        // 待发送数据 队列
    list<CommandData>      m_sendData2;

    list<CommandData>       m_Commandlist;
};

#endif // QTTCPSOCKET_H
