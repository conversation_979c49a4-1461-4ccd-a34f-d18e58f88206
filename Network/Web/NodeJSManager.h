#ifndef NODEJSMANAGER_H
#define NODEJSMANAGER_H

#include <QObject>
#include <QProcess>
#include <QString>
#include <QMutex>
#include <QTimer>
#include <memory>

class NodeJSManager : public QObject
{
    Q_OBJECT

public:
    static NodeJSManager* getInstance();
    
    // 启动Node.js进程运行radio.js
    bool startRadioJS();
    
    // 停止Node.js进程
    void stopRadioJS();
    
    // 检查Node.js进程是否正在运行
    bool isRunning() const;
    
    // 获取Node.js进程状态
    QString getProcessStatus() const;

private slots:
    void onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus);
    void onProcessError(QProcess::ProcessError error);
    void onProcessStarted();
    void onHeartbeatTimeout();

private:
    NodeJSManager(QObject* parent = nullptr);
    ~NodeJSManager();
    
    // 获取Node.js可执行文件路径
    QString getNodeExecutablePath() const;
    
    // 获取node_modules路径
    QString getNodeModulesPath() const;
    
    // 创建临时radio.js文件
    QString createTempRadioJSFile();
    
    // 删除临时radio.js文件
    void removeTempRadioJSFile();
    

    
    // 设置进程环境变量
    void setupProcessEnvironment();
    
    static NodeJSManager* m_instance;
    static QMutex m_instanceMutex;
    
    std::unique_ptr<QProcess> m_nodeProcess;
    QMutex m_processMutex;
    QString m_tempRadioJSPath;
    QTimer* m_heartbeatTimer;
    bool m_isStarting;
    
    static const int HEARTBEAT_INTERVAL_MS = 5000; // 5秒心跳检查
};

#endif // NODEJSMANAGER_H