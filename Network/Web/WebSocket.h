/*

#ifndef CWEBSOCKET_H
#define CWEBSOCKET_H


class CWebSocket
{
public:
    CWebSocket();
    ~CWebSocket();

    void    WebRun();

    bool    CreateSock();

    // 设置socket 连接，关闭
    void    CtlEpollEvent(int fd, bool flag);

    // epoll 循环
    void    EpollLoop();

    // Select 模型
    void    SelectLoop();

private:
    // 设置套接字非阻塞
    int     SetNoblock(int fd);

    static  void*  WebPthread(void* lparam);

    //static  void*  TestPthread(void *lparam);

private:
    int     m_listenFd;     // 监听socket

    int     m_epollFd;      // epoll socket

};


#endif // CWEBSOCKET_H

*/
