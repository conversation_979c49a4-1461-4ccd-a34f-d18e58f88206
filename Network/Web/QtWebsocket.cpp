#include "stdafx.h"
#include "QtWebsocket.h"
#include <QByteArray>
#include <QDebug>


QtWebsocket::QtWebsocket()
{
    m_nCurSendID = 1;
    m_bIsSending = false;

    m_WebSocketServer=new QWebSocketServer("server",QWebSocketServer::NonSecureMode);
    QObject::connect(m_WebSocketServer,SIGNAL(newConnection()),this,SLOT(onNewConnection()));
    QObject::connect(this,SIGNAL(signal_CloseSocket(string,bool)),this,SLOT(CloseSocket(string,bool)));
    //QObject::connect(this, SIGNAL(closeSocekt(string strID)), this, SLOT(CloseSocket(string strID)));
    //QObject::connect(&m_Timer, SIGNAL(timeout()), this, SLOT(OnTimeOut()));
    QObject::connect(this, SIGNAL(send(string,string)), this, SLOT(SendMessageQ(string,string)));
}

QtWebsocket::~QtWebsocket()
{
    LOG("QtWebSocket free!", LV_INFO);
    #if defined(Q_OS_LINUX)   //测试主备服务器函数
    //需要杀掉lsyncd
    g_Global.m_serverSync.StopLsyncdWorking();
    #endif
    exit(0);
}

void QtWebsocket::StartWebsocket()
{
    if(m_WebSocketServer->listen(QHostAddress::Any, g_Global.m_WEB_PORT))
    {
        LOG("web server is run", LV_INFO);
    }
    else
    {
        if(m_WebSocketServer->listen(QHostAddress(CNetwork::GetHostIP()), g_Global.m_WEB_PORT))
        {
            LOG("web server is run2", LV_INFO);
        }
        else
        {
            qDebug() << m_WebSocketServer->errorString();
            LOG("web server is error", LV_INFO);
            exit(0);
        }
    }

    //m_Timer.start(MAX_SEND_TIMER);
}

void QtWebsocket::StopWebsocket()
{
    m_WebSocketServer->close();

}

QWebSocket *QtWebsocket::GetWebSocketByUID(string strUID)
{
    if(m_WebSockets.count(strUID) > 0)
    {
        return m_WebSockets[strUID].qwebsocket;
    }

    return NULL;
}

void QtWebsocket::SendAll(QString strBuf)
{
    QMutexLocker locker(&m_mutex_send);
    map<string, stWebsocket>::iterator iter = m_WebSockets.begin();
    for(; iter!=m_WebSockets.end(); iter++)
    {
        QWebSocket* pSoc = (iter->second).qwebsocket;
        pSoc->sendTextMessage(strBuf);
    }

}

void QtWebsocket::SendMessageQ(string strUID, string strBuf)
{
    QMutexLocker locker(&m_mutex_send);
    if(m_WebSockets.count(strUID) > 0)
    {
        //LOG(strUID.data(), LV_INFO);
        QWebSocket* pWebSocket = m_WebSockets[strUID].qwebsocket;
        if(pWebSocket != NULL && pWebSocket->isValid())
        {
            pWebSocket->sendTextMessage(QString(strBuf.data()));
        }
    }
}

#if 0
void QtWebsocket::AddCommandBuf(string strUID, string strBuf)
{
    QMutexLocker locker(&m_mutex_cmd);
    CommandData sd;
    sd.strUID = strUID;
    sd.strBuf = strBuf;
    sd.cmdCnt = 0;
    m_Commandlist.push_back(sd);

#if 0
      if(m_nCurSendID == 1)
      {
            sendData sd;
            sd.strUID = strUID;
            sd.strBuf = strBuf;
            m_sendData1.push_back(sd);
      }
      else
      {
            sendData sd;
            sd.strUID = strUID;
            sd.strBuf = strBuf;
            m_sendData2.push_back(sd);
      }
#endif


}
#endif


//连接上之后
void QtWebsocket::onNewConnection(){
    QMutexLocker locker(&m_mutex_send);
    pSocket = m_WebSocketServer->nextPendingConnection();

    //QString item = pSocket->peerAddress().toString();
    string strUID = GetGUID().Data();
    pSocket->setObjectName(QString(strUID.data()));

    //LOG(FORMAT("connect success : %s", strUID.data()), LV_INFO);
    m_WebSockets[strUID].qwebsocket = pSocket;
    m_WebSockets[strUID].timeoutCnt = 0;

    QObject::connect(pSocket,SIGNAL(textMessageReceived(QString)),this,SLOT(processTextMessage(QString)));
    QObject::connect(pSocket,SIGNAL(disconnected()),this,SLOT(socketDisconnected()));
    QObject::connect(pSocket,SIGNAL(error(QAbstractSocket::SocketError)),this,SLOT(socketError(QAbstractSocket::SocketError)));

}


//收到消息并显示
void QtWebsocket::processTextMessage(QString message){
    //QString time = current_date_time->currentDateTime().toString("yyyy.MM.dd hh:mm:ss.zzz ddd");
    //QString item = pSocket->peerAddress().toString();
    //m_receiveTextEdit->append(time +""+ item + "\n" + message);
    //QMutexLocker locker(&m_mutex);
    QWebSocket * wbSkt= ( QWebSocket *)sender();
    string strUID = wbSkt->objectName().toLatin1().data();

    if(m_WebSockets.count(strUID) >0)
        m_WebSockets[strUID].timeoutCnt = 0;

    QHostAddress peerAddress = wbSkt->peerAddress(); // 获取远程地址
    string ipAddr= QHostAddressToString(peerAddress);

    g_Global.m_WebNetwork.m_WebHandle.HandleWebCommand(message.toLocal8Bit().data(), strUID ,ipAddr);

}


//连接断开的操作
void QtWebsocket::socketDisconnected(){
    QWebSocket * wbSkt= ( QWebSocket *)sender();
    string strKey = wbSkt->objectName().toLatin1().data();
    //LOG(FORMAT("websocket close:strKey=%s\n",strKey.data()), LV_INFO);
    printf("socketDisconnected:strKey=%s\n",strKey.data());
    CloseSocket(strKey,false);
}

//连接断开的操作
void QtWebsocket::socketError(QAbstractSocket::SocketError error){
    QWebSocket * wbSkt= ( QWebSocket *)sender();
    string strKey = wbSkt->objectName().toLatin1().data();
    if(wbSkt->isValid())
        wbSkt->abort();
    printf("socketError:strKey=%s\n",strKey.data());
   //LOG(FORMAT("websocket error:%d,strKey=%s\n",error,strKey.data()), LV_INFO);
    CloseSocket(strKey,false);
}





void QtWebsocket::SendData()
{

}



void QtWebsocket::CloseSocket(string strID,bool need_closeSocket)
{
    printf("QtWebsocket,CloseSocket:%s ready\n",strID.data());
    if(m_WebSockets.count(strID) > 0)
    {
        QMutexLocker locker(&m_mutex_send);
        if(m_WebSockets.count(strID) > 0)
        {
            QWebSocket* qwebsocket = m_WebSockets[strID].qwebsocket;

            QHostAddress peerAddress = qwebsocket->peerAddress(); // 获取远程地址
            string ipAddr= QHostAddressToString(peerAddress);

            m_WebSockets.erase(strID);  //先清除，不然因为立即触发socketDisconnected而造成死锁
            if(need_closeSocket)
            {
                qwebsocket->close();   //会立即触发socketDisconnected,执行完毕再执行下述代码
            }
            g_Global.m_WebSections.RemoveWebSectionBySockID(strID);
            g_Global.m_Users.RemoveUuid(strID,ipAddr);
            printf("QtWebsocket,CloseSocket OK...\n");
        }
    }
    else
    {
        printf("QtWebsocket,CloseSocket failed:not found\n",strID.data());
    }
}

void QtWebsocket::OnTimeOut()
{
    QMutexLocker locker(&m_mutex_cmd);
    if(m_WebSockets.size() == 0)
    {
        m_Commandlist.clear();
        return;
    }


    int nCount = 0;
    list<CommandData>::iterator iter = m_Commandlist.begin();
    for(; iter != m_Commandlist.end(); )
    {
        string strUID = iter->strUID;
        string strBuf = iter->strBuf;
        bool delCMD=true;

        if(strBuf.length() == 0)
        {
            delCMD=false;
            //关闭
            if(++(iter->cmdCnt)>=3)
            {
                printf("cmd>=3,cloe UID:%s\n",strUID.data());
                delCMD=true;
                CloseSocket(strUID,true);
                printf("close UID OK...\n");
            }
        }
        else
        {
            //printf("strBuf : %s\n", strBuf.data());
            SendMessageQ(strUID,strBuf);
        }
        if(delCMD)
        {
            iter=m_Commandlist.erase(iter);
        }
        nCount++;
        if(nCount >= MAX_FIX_TIME_SEND_COUNT)   // 超过限制发送次数等到下次定时再发送
        {
            break;
        }
    }
#if SUPPORT_WEBSOCKET_TIMEOUT_MANAGEMENT
//如果启用此功能，需要重新优化互斥锁
    //心跳管理，超时时间15min，客户端1分钟发一次心跳包
    int need_repeat_check=0;
    static int timeout_200ms=0;
    static int cnt_1min=(1000/MAX_SEND_TIMER)*60;
    timeout_200ms++;
    if(timeout_200ms%cnt_1min == 0)
    {
        printf("Check websocket alive...\n");
        map<string, stWebsocket>::iterator iter_websockets = m_WebSockets.begin();
        for(; iter_websockets != m_WebSockets.end(); )
        {
            (iter_websockets->second).timeoutCnt++;
            if((iter_websockets->second).timeoutCnt >= MAX_TIMEOUT_MINUTE)
            {
                string strUID = iter_websockets->first;
                printf("QtWebsocket,timeout:%s\n",strUID.data());

                QWebSocket* qtwebsocket = (iter_websockets->second).qwebsocket;
                QHostAddress peerAddress = qwebsocket->peerAddress(); // 获取远程地址
                string ipAddr= QHostAddressToString(peerAddress);

                m_WebSockets.erase(iter_websockets++);
                g_Global.m_WebSections.RemoveWebSectionBySockID(strUID);
                g_Global.m_Users.RemoveUuid(strUID,ipAddr);

                qtwebsocket->close();   //会立即触发socketDisconnected,执行完毕再执行下述代码
            }
            else
            {
                string strUID = iter_websockets->first;
                printf("Check websocket:%s,timeout:%d\n",strUID.data(),(iter_websockets->second).timeoutCnt);
                iter_websockets++;
            }
        }
    }
#endif
}


