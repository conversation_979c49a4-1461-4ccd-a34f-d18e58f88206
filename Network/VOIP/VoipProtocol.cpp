#include "Global/GlobalMethod.h"
#include "VoipProtocol.h"

CVoipProtocol::CVoipProtocol()
{

}

// 获取SIP设备状态
string CVoipProtocol::CmdGetSipStatus()
{
        cJSON* root = cJSON_CreateObject();

        cJSON_AddItemToObject(root, "command", cJSON_CreateString("device_status"));
        cJSON_AddItemToObject(root, "command_id", cJSON_CreateString(GetGUID().C_Str()));

        string  strBuf = cJSON_Print(root);
        cJSON_Delete(root);
        return strBuf;
}


// 客户端请求对讲
string CVoipProtocol::CmdRequestTalkback(string strSipNum1, string strSipNum2, std::string strComID)
{

    cJSON *root = cJSON_CreateObject();

    cJSON_AddItemToObject(root, "command", cJSON_CreateString("talkback"));
    cJSON_AddItemToObject(root, "command_id", cJSON_CreateString(strComID.data()));
    cJSON_AddItemToObject(root, "exten1", cJSON_CreateString(strSipNum1.data()));
    cJSON_AddItemToObject(root, "exten2", cJSON_CreateString(strSipNum2.data()));

    string data = cJSON_Print(root);

    cJSON_Delete(root);
    return data;
}

 // 客户端请求广播/寻呼
string CVoipProtocol::CmdRequestPaging(string   strType,              // 寻呼类型
                                                                       string   strComID,             // 命令ID
                                                                       int         nPageCount,     // 包总数
                                                                       int         nPage,               // 当前包序号
                                                                       string    strExten,           // 主动寻呼的号码
                                                                       int         nExtenCount,   // 此页包含的设备数，最多20个分区
                                                                       string*  strExtens)         // 接收广播/寻呼设备的号码
{
        cJSON* root = cJSON_CreateObject();

        cJSON_AddItemToObject(root, "command", cJSON_CreateString("page"));
        cJSON_AddItemToObject(root, "Type", cJSON_CreateString(strType.data()));
        cJSON_AddItemToObject(root, "command_id", cJSON_CreateString(strComID.data()));
        cJSON_AddItemToObject(root, "pack_num", cJSON_CreateNumber(nPageCount));
        cJSON_AddItemToObject(root, "pack_id", cJSON_CreateNumber(nPage));
        cJSON_AddItemToObject(root, "exten", cJSON_CreateString(strExten.data()));
        cJSON_AddItemToObject(root, "number", cJSON_CreateNumber(nExtenCount));

        if(nExtenCount > 0)
        {
            cJSON* cjExtens = cJSON_CreateArray();

            for(int i=0; i<nExtenCount; i++)
            {
                    cJSON_AddItemToArray(cjExtens, cJSON_CreateString(strExtens[i].data()));
            }

            cJSON_AddItemToObject(root, "value", cjExtens);
        }

        string    strBuf = cJSON_Print(root);
        cJSON_Delete(root);
        return strBuf;
}

// 客户端请求监听
string CVoipProtocol::CmdRequestListen(string  strExten1,         // 监听方
                                                                      string  strExten2,         // 被监听方
                                                                      std::string strComID,
                                                                      string  strValue)          // 是否进行录音
{
        cJSON* root = cJSON_CreateObject();

        cJSON_AddItemToObject(root, "command", cJSON_CreateString("spy"));
        cJSON_AddItemToObject(root, "command_id", cJSON_CreateString(strComID.data()));
        cJSON_AddItemToObject(root, "exten1",  cJSON_CreateString(strExten1.data()));
        cJSON_AddItemToObject(root, "exten2", cJSON_CreateString(strExten2.data()));
        cJSON_AddItemToObject(root, "value", cJSON_CreateString(strValue.data()));

        string  strBuf = cJSON_Print(root);
        cJSON_Delete(root);
        return strBuf;
}

// 客户端请求挂断
string CVoipProtocol::CmdRequestHangup(string strExten,       // 挂断号码
                                                                         string strComID)    // 命令ID
{
        cJSON* root = cJSON_CreateObject();

        cJSON_AddItemToObject(root, "command", cJSON_CreateString("hangup"));
        cJSON_AddItemToObject(root, "command_id", cJSON_CreateString(strComID.data()));
        cJSON_AddItemToObject(root, "exten", cJSON_CreateString(strExten.data()));

        string strBuf = cJSON_Print(root);
        cJSON_Delete(root);
        return strBuf;
}


