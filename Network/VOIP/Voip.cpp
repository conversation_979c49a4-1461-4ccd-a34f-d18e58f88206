#include "stdafx.h"
#include "Voip.h"
#include <pthread.h>

#include <sys/types.h>
#include <unistd.h>
#include <string.h>
#if defined(Q_OS_LINUX)
#include <sys/socket.h>
#include <arpa/inet.h>
#include <sys/epoll.h>
#endif


#define MAX_EPOLL_EVENTS   128


#define  POST  "POST %s:%d HTTP/1.1\r\n" \
               "Content-Type:application/json;\r\n" \
               "charset=utf-8;\r\n"\
               "Content-Length: %d\r\n" \
               "Connection: Keep-Alive\r\n\r\n" \
               "%s"


CVoip::CVoip()
{
    m_Voipfd = -1;
    m_bValid = false;
    memset(m_szVoipIP, 0,IP_LEN);
}


CVoip::~CVoip()
{
    close(m_Voipfd);
    memset(m_szVoipIP, 0,IP_LEN);
}


void CVoip::StartVOIP()
{
    g_Global.m_Network.AddLog("---StartVOIP");

    if(!m_bValid)
    {
        if(InitSocket())
        {
            strcpy(m_szVoipIP, g_Global.m_strVoipServerIP.data());
            StartVoipWork();

            m_vSend.GetSipInfo();
        }
    }
    else
    {
        m_vSend.GetSipInfo();
    }
}


bool CVoip::InitSocket()
{
#if defined(Q_OS_LINUX)
    if(m_bValid)
    {
        return true;
    }

    m_Voipfd = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if(m_Voipfd == -1)
    {
        perror("socket failed:");
        return false;
    }

    sockaddr_in addr;
    socklen_t len = sizeof(addr);
    bzero(&addr, len);
    addr.sin_family = AF_INET;
    addr.sin_port = htons(VOIP_PORT);
    addr.sin_addr.s_addr = inet_addr(g_Global.m_strVoipServerIP.data());          // 待修改为获取自身IP地址

    int nRet = connect(m_Voipfd, (sockaddr*)&addr, len);
    if(nRet == -1)
    {
        LOG(FORMAT("voip connect failed : %s", strerror(errno)), LV_INFO);
        m_bValid = false;
        return false;
    }

    m_bValid = true;
    LOG("voip is connect!", LV_INFO);
#endif
    return true;
}

// 处理收到的voip服务器数据
void CVoip::HandleVOIPData(const char *data, int nLen)
{
    m_vHandle.HandleVoipData(data, nLen);
}

// 发送数据到VOIP服务器
void CVoip::SendData(const char *data, int nLen)
{
#if defined(Q_OS_LINUX)
    if(m_bValid)
    {
        char szBuf[BUF_LEN] = {0};
        sprintf(szBuf, POST, m_szVoipIP, VOIP_PORT, nLen, data);
        strcat(szBuf,"\r\n");
        LOG(FORMAT("----------send to voip server: \n%s", data), LV_INFO);

        // 添加上http头字段，待处理 post
        send(m_Voipfd, szBuf, strlen(szBuf), 0);
    }
#endif
}


void CVoip::StartVoipWork()
{
    pthread_t pth;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&pth, &attr, VoipPthread, (void*)this);
    pthread_attr_destroy(&attr);
}


void *CVoip::VoipPthread(void *lparam)
{
#if defined(Q_OS_LINUX)
    CVoip* pVoip = (CVoip*)lparam;
    int voipFd = pVoip->GetVoipFd();
    char   szBuf[BUF_LEN] = {0};

    struct epoll_event ev,events[MAX_EPOLL_EVENTS];
    int epfd = epoll_create(MAX_EPOLL_EVENTS);

    ev.data.fd = voipFd;
    ev.events  = EPOLLIN|EPOLLERR|EPOLLHUP;
    epoll_ctl(epfd, EPOLL_CTL_ADD, voipFd, &ev);

    int nfds;
    while(1)
    {
        nfds = epoll_wait(epfd, events, MAX_EPOLL_EVENTS, -1);
        if(nfds == -1)
        {
            printf("epoll_wait failed %s\n",strerror(errno));
            if(errno!=EINTR)
            {
               break;
            }
            continue;
        }
        for(int i=0; i<nfds; i++)
        {
            if(events[i].events & EPOLLIN)
            {
                memset(szBuf, 0,BUF_LEN);
                int fd = events[i].data.fd;
                int nLen = 0;
                if((nLen=recv(fd, szBuf, BUF_LEN, 0)) <= 0)
                {

                    perror("recv failed : ");

                    CMyString strLog;
                    strLog.Format("VoipPthread recv failed : %d", nLen);
                    g_Global.m_Network.AddLog(strLog);
                    epoll_ctl(epfd, EPOLL_CTL_DEL, fd, events);
                    close(fd);
                    goto END;
                }
                else
                {
                    pVoip->HandleVOIPData(szBuf, nLen);
                }
            }
            else if(events[i].events & EPOLLERR)
            {
                int fd = events[i].data.fd;
                epoll_ctl(epfd, EPOLL_CTL_DEL, fd, events);
                close(fd);
                goto END;
            }
            else if(events[i].events & EPOLLHUP)
            {
                int fd = events[i].data.fd;
                epoll_ctl(epfd, EPOLL_CTL_DEL, fd, events);
                close(fd);
                goto END;
            }
        }
    }

END:
    pVoip->m_bValid = false;
    pVoip->StartVOIP();
#endif
    return NULL;
}
