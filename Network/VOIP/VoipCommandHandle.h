#ifndef CVOIPCOMMANDHANDLE_H
#define CVOIPCOMMANDHANDLE_H
#include <string>
#include "Tools/cJSON.h"

using std::string;

#define  MAX_VOIP_DATALEN       1024*8


class CVoipCommandHandle
{
public:
    CVoipCommandHandle();

    // 处理接收到VOIP服务器的数据
    void    HandleVoipData(const char* szData, int nLen);

    // 解析处理接收到VOIP服务器的数据
    void    ParseVOIPData(const char* szData, int nLen);

    // 2.1	获取设备状态
    void    HandleGetSipStatus(string strData);

    // 2.2	对讲
    void    HandleTalkback(string strData);

    //  请求广播回复
    void    HandleResponePage(string strData);

    // 监听
    void    HandleResponseListen(string strData);

    // 挂断
    void    HandleResponseHangup(string strData);

private:
    char    m_szBuf[MAX_VOIP_DATALEN];      // 保存未解析的voip数据
    int       m_nLen;
};

#endif // CVOIPCOMMANDHANDLE_H
