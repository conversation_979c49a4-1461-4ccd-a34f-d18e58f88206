#include "stdafx.h"
#include "VoipCommandHandle.h"
#include "VoipProtocol.h"
#include "Model/Other/VoipSipInfo.h"

CVoipCommandHandle::CVoipCommandHandle()
{

}

// 处理接收到VOIP服务器的数据
void CVoipCommandHandle::HandleVoipData(const char *szData, int nLen)
{
    static char buf[MAX_BUF_LEN*4+1] = {0};
    static int start = 0;
    static int count = -1;
    int pos = 0;  // szData pos

    while (pos < nLen)
    {
            if (szData[pos] == '{')
            {
                count = ((count < 0) ? 1 : count+1);
            }

            if (count < 0)
            {
                pos++;
                continue;
            }

           if (szData[pos] == '}')
            {
                count--;
            }

           if(start >= MAX_BUF_LEN*4+1)
           {
               start = 0;
           }

            buf[start++] = szData[pos];
            if (count == 0)
            {
                ParseVOIPData(buf,  start);
                count = -1;
                start = 0;
            }

            pos++;
    }
}


// 解析处理接收到VOIP服务器的数据
void CVoipCommandHandle::ParseVOIPData(const char *szData, int nLen)
{
    char* szBuf = new char[nLen+1];
    memcpy(szBuf, szData, nLen);
    szBuf[nLen] = 0;

    cJSON* root    = cJSON_Parse(szBuf);
    if(root == NULL)  return;

    cJSON* jsCommand = cJSON_GetObjectItem(root, "command");
    cJSON* jsComID = cJSON_GetObjectItem(root, "command_id");
    if(jsCommand == NULL)   
    {
        cJSON_Delete(root);
        return;
    }
    string strCommand = jsCommand->valuestring;

    // 获取设备状态
    if(strCommand == "device_status")
    {
            HandleGetSipStatus(szBuf);
    }

    if(jsComID != NULL)
    {
        LOG("\n----------recv from voip server : \n", LV_INFO);
        LOG(szBuf, LV_INFO);

        // 对讲
        if(strCommand == "talkback")
        {
               HandleTalkback(szBuf);
        }

        // 广播
        else if(strCommand == "page")
        {
                HandleResponePage(szBuf);
        }

        // 监听
        else if(strCommand == "spy")
        {
                HandleResponseListen(szBuf);
        }

        // 挂断
        else if(strCommand == "hangup")
        {
                HandleResponseHangup(szBuf);
        }
    }

    delete []szBuf;
    cJSON_Delete(root);
}

SipStatus GetVoipSipStatus(int status)
{
    if (status == VOIP_SIP_OFFLINE)
    {
        return SIP_NULL;  // 离线
    }
    else if (status == VOIP_SIP_IDLE)
    {
        return SIP_LOG_IN; // 空闲
    }
    else
    {
        return SIP_CALLING; // 繁忙
    }
}


// 2.1	获取设备状态
void CVoipCommandHandle::HandleGetSipStatus(string strData)
{
        cJSON* root = cJSON_Parse(strData.data());

        cJSON* jsValue = cJSON_GetObjectItem(root, "value");
        cJSON* jsPackNum = cJSON_GetObjectItem(root, "pack_num");
        cJSON* jsPackID  = cJSON_GetObjectItem(root, "pack_id");
        cJSON* jsNumber = cJSON_GetObjectItem(root, "number");
        if(jsValue==NULL || jsPackNum==NULL || jsPackID==NULL || jsNumber==NULL)     
        {
            cJSON_Delete(root);
            return;
        }
        int   nPackNum = jsPackNum->valueint;
        int   nPackID  = jsPackID->valueint;
        int   nNumber  = jsNumber->valueint;

        if(nNumber == 0)        // 避免SIP服务器刚开启还没有数据的错误
        {
            cJSON_Delete(root);
            return;
        }

        SipInfo sipInfoArray[nNumber];
        bool    IsInValidChara = false;     // 账户名是否无效字符

        // 获取sip账号信息
        int nCount = 0;
        for(int i=0; i<nNumber; i++)
        {
            cJSON* jsSipInfo = cJSON_GetArrayItem(jsValue, i);
            if(jsSipInfo == NULL)  return;

            cJSON* jsExten  = cJSON_GetObjectItem(jsSipInfo, "exten");
            cJSON* jsStatus = cJSON_GetObjectItem(jsSipInfo, "status");
            if(jsExten==NULL || jsStatus==NULL)         return;
            string strExten = jsExten->valuestring;
            string strStatus = jsStatus->valuestring;
            int nExten = atoi(strExten.data());

            if(nExten > 0 && nExten < 65535)
            {
                SipInfo sip(strExten, strStatus);
                sipInfoArray[i] = sip;
                nCount++;


                // 查找分区里的SIP信息，更新
                LPCSection pSection = g_Global.m_Sections.GetSectionBySip(strExten);
                if (pSection != NULL)
                {
                    SipStatus sipStatus = GetVoipSipStatus(sip.m_nStatus);

                    // SIP状态改变，转发
                    if (sipStatus != pSection->m_SipInfo.m_nSipStaus)
                    {
                        LOG("\n----------recv from voip server : \n", LV_INFO);
                        LOG(strData.data(), LV_INFO);
                        pSection->m_SipInfo.m_nSipStaus = sipStatus;
                        g_Global.m_WebNetwork.ForwardSectionInfoToWeb(DEVICE_SECTION, pSection, NULL, TRUE);
                    }
                }
            }
            else
            {
                IsInValidChara = true;
            }
        }

        if(nNumber == 1 && IsInValidChara )     // 移除tm-li...字符账户
        {
            cJSON_Delete(root);
            return;
        }

        // 添加SIP信息到内存
        g_Global.m_VoipInfo.AddSipInfo(nNumber, sipInfoArray);

         // 分发所有sipInfo到web终端
        g_Global.m_WebNetwork.ForwardSipInfo(nPackNum, nPackID, nCount, sipInfoArray, NULL);

        // 判断是否有正在发起组播的sip账号，如果有，则把所有该组播的sip全部挂断（设置成空闲）
        if(nNumber==1 && sipInfoArray[0].m_strStatus=="Idle")
        {
                g_Global.m_WebNetwork.m_WebQueues.CheckSipPage(nNumber, sipInfoArray);
        }
        if(nNumber==1 && sipInfoArray[0].m_strStatus=="Unavailable")
        {
                g_Global.m_WebNetwork.m_WebQueues.CheckSipPage(nNumber, sipInfoArray, true);
        }
        cJSON_Delete(root);
}


int GetSipResult(int nStatus)
{
        int nSipStatus = EC_SUCCESS;
        switch(nStatus)
        {
            case 0:     nSipStatus = EC_SUCCESS;                              break;
            case 1:     nSipStatus = EC_SIP_INUSE;                            break;
            case 2:     nSipStatus = EC_SIP_OFFLINE;                        break;
            case 3:     nSipStatus = EC_SIP_ERROR;                           break;
            case 4:     nSipStatus = EC_SIP_NOTEXIST;                      break;
            case 5:     nSipStatus = EC_SIP_RULE_NOTEXIST;            break;
            case 6:     nSipStatus = EC_SIP_RULE_NOTUNIFY;           break;
            default:    nSipStatus = EC_SIP_ERROR;                           break;
        }
        return nSipStatus;
}

// 2.2	对讲
void CVoipCommandHandle::HandleTalkback(string strData)
{
    cJSON* root    = cJSON_Parse(strData.data());
    cJSON* jsComID = cJSON_GetObjectItem(root, "command_id");
    cJSON* jsResult = cJSON_GetObjectItem(root, "result");
    if(jsResult == NULL || jsComID == NULL)     return;

    string strComID = jsComID->valuestring;
    int nResult = jsResult->valueint;

    // 回复分控设备，保留，待处理
    g_Global.m_WebNetwork.m_WebQueues.ReponseCommand(strComID, GetSipResult(nResult));
    cJSON_Delete(root);
}

//  请求广播回复
void CVoipCommandHandle::HandleResponePage(string strData)
{
        cJSON* root  =  cJSON_Parse(strData.data());
        cJSON* jsType      = cJSON_GetObjectItem(root, "type");
        cJSON* jsCommandID = cJSON_GetObjectItem(root, "command_id");
        cJSON* jsPack_id   = cJSON_GetObjectItem(root, "pack_id");
        cJSON* jsValue     = cJSON_GetObjectItem(root, "value");
        cJSON* jsResult    = cJSON_GetObjectItem(root, "result");

        if(jsType==NULL || jsPack_id==NULL || jsValue==NULL || jsResult==NULL || jsCommandID==NULL)
        {
            cJSON_Delete(root);
            return;
        }
        string  strType = jsType->valuestring;
        int     nType = (strType == "multicast") ? 1 : 2;
        int     nPackID = jsPack_id->valueint;
        string  strValue = jsValue->valuestring;
        int     nResult  = jsResult->valueint;
        string  strComID = jsCommandID->valuestring;

        // 广播类型为组播，且sip数据不为空
        // 执行组播操作
        if(nType == 1 && strValue.length() > 0 && nResult == 0)
        {
            g_Global.m_WebNetwork.m_WebQueues.ExecutePage(strComID, strValue, GetSipResult(nResult));
        }

        // 回复Web终端
        LPCWebSection pWebSection = g_Global.m_WebNetwork.m_WebQueues.GetWebSectionByComID(strComID);
        if(pWebSection != NULL)
        {
            g_Global.m_WebNetwork.m_WebSend.WebResponsePaging(nType, CWebQueues::GetnRtpType(strValue), nPackID, pWebSection, GetSipResult(nResult));
        }
        else
        {
            LOG("pWebSection is NULL", LV_INFO);
        }

        // 加入日志
        g_Global.m_WebNetwork.m_WebQueues.InsertLog(strComID, GetSipResult(nResult));

        // 只有当广播类型为组播且返回成功时不移除数据，
        // 当发起方挂断时需要通知设备空闲后再移除
        if(GetSipResult(nResult) == EC_SUCCESS && nType == 1)
        {

        }
        else
        {
            g_Global.m_WebNetwork.m_WebQueues.RemoveQueue(strComID);
        }
        cJSON_Delete(root);
}

// 监听
void CVoipCommandHandle::HandleResponseListen(string strData)
{
        cJSON* root    = cJSON_Parse(strData.data());
        cJSON* jsComID = cJSON_GetObjectItem(root, "command_id");
        cJSON* jsResult = cJSON_GetObjectItem(root, "result");
        if(jsComID == NULL || jsResult == NULL)     
        {
            cJSON_Delete(root);
            return;
        }
        string strComID = jsComID->valuestring;
        int nResult = jsResult->valueint;

        // 回复分控设备，保留，待处理
        g_Global.m_WebNetwork.m_WebQueues.ReponseCommand(strComID, GetSipResult(nResult));
        cJSON_Delete(root);
}

// 挂断
void CVoipCommandHandle::HandleResponseHangup(string strData)
{
        cJSON* root    = cJSON_Parse(strData.data());
        cJSON* jsComID = cJSON_GetObjectItem(root, "command_id");
        cJSON* jsExten = cJSON_GetObjectItem(root, "exten");
        cJSON* jsResult = cJSON_GetObjectItem(root, "result");
        if(jsComID == NULL || jsResult == NULL || jsExten == NULL)     
        {
            cJSON_Delete(root); 
            return;
        }
        string strComID = jsComID->valuestring;
        string strExten = jsExten->valuestring;
        int nResult = jsResult->valueint;

        // 回复分控设备，保留，待处理
        g_Global.m_WebNetwork.m_WebQueues.ReponseCommand(strComID, GetSipResult(nResult), strExten);

        // 移除队列组播数据
        if(nResult == 0)
        {
            SipInfo sipInfoArray[1];
            SipInfo sip(strExten, "Idle");
            sipInfoArray[0] = sip;
            g_Global.m_WebNetwork.m_WebQueues.CheckSipPage(1, sipInfoArray, true);
        }
        cJSON_Delete(root);
}





