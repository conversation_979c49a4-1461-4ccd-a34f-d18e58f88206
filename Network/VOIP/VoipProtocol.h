#ifndef CVOIPPROTOCOL_H
#define CVOIPPROTOCOL_H

#include <string>
#include "Tools/cJSON.h"
using std::string;




class CVoipProtocol
{
public:
    CVoipProtocol();

public:
    // 	获取SIP设备状态
    static string  CmdGetSipStatus();

    // 客户端请求对讲
    static string   CmdRequestTalkback(string strSipNum1,
                                                                 string strSipNum2,
                                                                 string strComID);

    // 客户端请求广播/寻呼
    static  string  CmdRequestPaging(string   strType,              // 寻呼类型
                                                             string   strComID,          // 命令ID
                                                             int         nPageCount,     // 包总数
                                                             int         nPage,               // 当前包序号
                                                             string    strExten,           // 主动寻呼的号码
                                                             int         nExtenCount,   // 此页包含的设备数，最多20个分区
                                                             string*  strExtens);        // 接收广播/寻呼设备的号码

    // 客户端请求监听
    static   string     CmdRequestListen(string  strExten1,         // 监听方
                                                                string  strExten2,         // 被监听方
                                                                string strComID,          // 命令ID
                                                                string  strValue);          // 是否进行录音

    // 客户端请求挂断
    static    string     CmdRequestHangup(string strExten,      // 挂断号码
                                                                    string strComID);   // 命令ID
};

#endif // CVOIPPROTOCOL_H
