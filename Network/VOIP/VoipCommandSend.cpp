#include "stdafx.h"
#include "VoipCommandSend.h"
#include "VoipProtocol.h"

CVoipCommandSend::CVoipCommandSend()
{

}

CVoipCommandSend::~CVoipCommandSend()
{

}

// 获取设备状态
void CVoipCommandSend::GetSipInfo()
{
        string strBuf = CVoipProtocol::CmdGetSipStatus();

        g_Global.m_Voip.SendData(strBuf.data(), strBuf.length());
}

 // 请求对讲
void CVoipCommandSend::RequestTalkback(string strNum1, string strNum2, string strComID)
{
        string strBuf = CVoipProtocol::CmdRequestTalkback(strNum1, strNum2, strComID);

        g_Global.m_Voip.SendData(strBuf.data(), strBuf.length());
}

// 请求(广播)寻呼
void CVoipCommandSend::RequestPaging(string  strPagingNum,       // 发起寻呼的设备号码
                                    int        nPagType,              // 寻呼类型
                                    string   strComID,             // 命令ID
                                    int        nPageCount,         // 包总数
                                    int        nPage,                   //  当前包序号
                                    int        nExtenCount,        // 成员个数（<=20）
                                    string* strExtens)             //  sip设备列表
{
        LOG("CVoipCommandSend", LV_INFO);
        string strPagType = (nPagType==1) ? "multicast" : "page";

        string strBuf = CVoipProtocol::CmdRequestPaging(strPagType,
                                                      strComID,
                                                      nPageCount,
                                                      nPage,
                                                      strPagingNum,
                                                      nExtenCount,
                                                      strExtens);

        g_Global.m_Voip.SendData(strBuf.data(), strBuf.length());
}

// 请求监听
void CVoipCommandSend::RequestListen(string strExten1, string strExten2, string strComID, int nValue)
{
        string strValue = (nValue == 0) ? "no_record" : "record";
        string strBuf = CVoipProtocol::CmdRequestListen(strExten1, strExten2, strComID, strValue);

        g_Global.m_Voip.SendData(strBuf.data(), strBuf.length());
}

// 请求挂断
void CVoipCommandSend::RequestHangup(string strExten, string strComID)
{
        string strBuf = CVoipProtocol::CmdRequestHangup(strExten, strComID);

        g_Global.m_Voip.SendData(strBuf.data(), strBuf.length());
}

