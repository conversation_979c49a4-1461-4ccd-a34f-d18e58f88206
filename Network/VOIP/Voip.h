#ifndef VOIP_H
#define VOIP_H

#include "VoipCommandHandle.h"
#include "VoipCommandSend.h"

#define  IP_LEN  16
#define  VOIP_MULTICAST_PORT      50123

class CVoip
{
public:
    CVoip();
    ~CVoip();

    bool  IsValid()         { return m_bValid; }
    void  StartVOIP();

    bool  InitSocket();
    int   GetVoipFd()       { return m_Voipfd; }


// 常用操作
    // 处理收到的voip服务器数据
    void  HandleVOIPData(const char* data, int nLen);     // 数据段

    // 发送数据到VOIP服务器
    void  SendData(const char* data,
                   int         nLen);

private:
// 线程开启
    // 开启接收voip 接收发送线程，http长连接
    void  StartVoipWork();

    static void* VoipPthread(void *lparam);

private:
    int   m_Voipfd;                 // 用于与voip服务器连接的socket套接字

    char  m_szVoipIP[IP_LEN];       // voip服务器IP

public:
    bool  m_bValid;                 // 是否已连接上VOIP服务器

    CVoipCommandHandle m_vHandle;   // voip数据处理
    CVoipCommandSend   m_vSend;     // voip 数据发送

};



#endif // VOIP_H
