#ifndef VOIPCOMMANDSEND_H
#define VOIPCOMMANDSEND_H

#include <iostream>

using namespace std;

class CVoipCommandSend
{
public:
        CVoipCommandSend();
        ~CVoipCommandSend();

        // 获取设备状态
        void    GetSipInfo();

        // 请求对讲
        void    RequestTalkback(string strNum1,         // 主叫方
                                 string strNum2,        //  被叫方
                                 string strComID);

        // 请求(广播)寻呼
        void    RequestPaging(string  strPagingNum,       // 发起寻呼的设备号码
                             int        nPagType,              // 寻呼类型
                             string   strComID,             // 命令ID
                             int        nPageCount,         // 包总数
                             int        nPage,                   //  当前包序号
                             int        nExtenCount,        // 成员个数（<=20）
                             string* strExtens);            //  sip设备列表

        // 请求监听
        void    RequestListen(string  strExten1,
                            string   strExten2,
                            string strComID,
                            int        nValue = 0);

        // 请求挂断
        void    RequestHangup(string strExten, string strComID);

};

#endif // VOIPCOMMANDSEND_H
