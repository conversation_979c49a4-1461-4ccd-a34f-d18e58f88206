#include "stdafx.h"
#include "Network.h"
#include "Protocol/CommandSend.h"
#include "Tools/tools.h"
#include <math.h>
#include "Tools/ComputerInfo.h"
#include "Tools/Control/CRegister.h"
#include "Tools/Control/CTTSTrial.h"
#include "Web/NodeJSManager.h"

#if defined(Q_OS_WIN32)
//网络信息
#include "winperf.h"
#include <WinSock2.h>
#include <Iphlpapi.h>
#endif

// 条件变量,用来设置超时
pthread_cond_t pcSec = PTHREAD_COND_INITIALIZER;
pthread_cond_t pcMs  = PTHREAD_COND_INITIALIZER;
pthread_cond_t pcTimer = PTHREAD_COND_INITIALIZER;


// 互斥锁，锁住条件变量


CNetwork::CNetwork(void)
{
    m_CmdSend.SetNetwork(this);
    m_CmdHandle.SetNetwork(this);

    m_hasForwardAllDevicesStatus = FALSE;

    m_bNetworkIsReady=false;
}

CNetwork::~CNetwork(void)
{
    //20220427 静态初始化的互斥锁不需要,也不能用pthread_mutex_destroy销毁锁，否则将出错
    //pthread_mutex_destroy(&PlaylistUpgradeMutex);
}

const char* CNetwork::GetHostIP()
{
    #if defined(Q_OS_LINUX)
    const char*hostIP=CNetworkTool::GetHostIP();
    if( strcmp(g_Global.m_szNetworkIP,hostIP) )
    {
        sprintf(g_Global.m_szNetworkIP,"%s",hostIP);
    }
    return g_Global.m_szNetworkIP;
    #else
    if( strlen(g_Global.m_szNetworkIP)>0 )      //windows版本不需要多次获取
    {
        return g_Global.m_szNetworkIP;
    }
    //多网卡时，对应网卡加入组播时一定要绑定，否则接收不到组播
    PIP_ADAPTER_INFO pIpAdapterInfo = new IP_ADAPTER_INFO();//PIP_ADAPTER_INFO结构体指针存储本机网卡信息
    unsigned long stSize = sizeof(IP_ADAPTER_INFO);         //得到结构体大小,用于GetAdaptersInfo参数
    int nRel = GetAdaptersInfo(pIpAdapterInfo, &stSize);     //调用GetAdaptersInfo函数,填充pIpAdapterInfo指针变量;其中stSize参数既是一个输入量也是一个输出量
    
    int netCardNum = 0;//记录网卡数量
    int IPnumInNetCard = 0; //记录每张网卡上的IP地址数量

    if (ERROR_BUFFER_OVERFLOW == nRel)
    {
        //如果函数返回的是ERROR_BUFFER_OVERFLOW
        //则说明GetAdaptersInfo参数传递的内存空间不够,同时其传出stSize,表示需要的空间大小
        //这也是说明为什么stSize既是一个输入量也是一个输出量
        delete pIpAdapterInfo;//释放原来的内存空间
        pIpAdapterInfo = (PIP_ADAPTER_INFO)new BYTE[stSize];//重新申请内存空间用来存储所有网卡信息
        nRel = GetAdaptersInfo(pIpAdapterInfo, &stSize);    //再次调用GetAdaptersInfo函数,填充pIpAdapterInfo指针变量
    }

    PIP_ADAPTER_INFO pIpAdapterInfo_origin=pIpAdapterInfo;

    if (ERROR_SUCCESS == nRel)
    {
        //输出网卡信息
        //可能有多网卡,因此通过循环去判断
        while (pIpAdapterInfo)
        {
            IP_ADDR_STRING *pIpAddrString2 = &(pIpAdapterInfo->IpAddressList);
            g_Global.m_bTotalNetCardCnt++;
            /******Add 20210807*********/
            if(	pIpAddrString2 && strcmp(pIpAddrString2->IpAddress.String,"0.0.0.0") )   //没有连接上的网卡不统计网卡数目
            {
                netCardNum++;
            }
            else
            {
                pIpAdapterInfo = pIpAdapterInfo->Next;
                continue;
            }
            printf("netCardNum:%d,Description=%s\n",netCardNum,pIpAdapterInfo->Description);       
            /******Add 20210807*********/
#if 1
            if( strstr( pIpAdapterInfo->Description,"Virtual" ) || strstr( pIpAdapterInfo->Description,"WSL" ) )
            {
                pIpAdapterInfo = pIpAdapterInfo->Next;
                continue;
            }
#else   //test
            if( strstr( pIpAdapterInfo->Description,"VMnet1" ) )
            {
                pIpAdapterInfo = pIpAdapterInfo->Next;
                continue;
            }
#endif

            IPnumInNetCard=0;

            switch (pIpAdapterInfo->Type)
            {
            case MIB_IF_TYPE_OTHER:
                break;
            case MIB_IF_TYPE_ETHERNET:
                break;
            case MIB_IF_TYPE_TOKENRING:
                break;
            case MIB_IF_TYPE_FDDI:
                break;
            case MIB_IF_TYPE_PPP:
                break;
            case MIB_IF_TYPE_LOOPBACK:
                break;
            case MIB_IF_TYPE_SLIP:
                break;
            default:
                break;
            }
            //可能网卡有多IP,因此通过循环去判断
            IP_ADDR_STRING *pIpAddrString = &(pIpAdapterInfo->IpAddressList);
            int found_valid_ip=0;
            do
            {
#if 1
                //printf("NetCard_IP_id:%d\n",IPnumInNetCard);
                //printf("IP:%s\n",pIpAddrString->IpAddress.String);
                //printf("subAddress:%s\n",pIpAddrString->IpMask.String);
#endif
                if(	strcmp(pIpAddrString->IpAddress.String,"0.0.0.0") && strcmp(pIpAddrString->IpAddress.String,"127.0.0.1") )
                {
                    //网络模式，DHCP or Static
                    int isDHCP=pIpAdapterInfo->DhcpEnabled;
                    //printf("IP Mode:%s\n",isDHCP?"DHCP":"STATIC");

                    if(!IPnumInNetCard)
                    {
                        sprintf(g_Global.m_netCardList[g_Global.m_bValidNetCardCnt].m_szMAC,"%02x:%02x:%02x:%02x:%02x:%02x",pIpAdapterInfo->Address[0],pIpAdapterInfo->Address[1],pIpAdapterInfo->Address[2],\
                            pIpAdapterInfo->Address[3],pIpAdapterInfo->Address[4],pIpAdapterInfo->Address[5]);
                        sprintf(g_Global.m_netCardList[g_Global.m_bValidNetCardCnt].m_szGateWay,"%s",pIpAdapterInfo->GatewayList.IpAddress.String);
                    }
                    sprintf(g_Global.m_netCardList[g_Global.m_bValidNetCardCnt].m_szIPInfo[IPnumInNetCard].m_szIP,"%s",pIpAddrString->IpAddress.String);
                    sprintf(g_Global.m_netCardList[g_Global.m_bValidNetCardCnt].m_szIPInfo[IPnumInNetCard].m_szSubAddress,"%s",pIpAddrString->IpMask.String);

                    IPnumInNetCard++;
                    found_valid_ip=1;
                    if(IPnumInNetCard>=5)  //每张网卡最多记录2个有效IP
                    {
                        break;
                    }
                }
                pIpAddrString = pIpAddrString->Next;

            } while (pIpAddrString);

            if(found_valid_ip)
            {
                g_Global.m_bValidNetCardCnt++;
                printf("g_Global.m_bValidNetCardCnt=%d\n",g_Global.m_bValidNetCardCnt);
            }

            if(g_Global.m_bValidNetCardCnt>=5)   //最多记录5张有效网卡
            {
                break;
            }
            pIpAdapterInfo = pIpAdapterInfo->Next;
        }
    }
    //释放内存空间
    if (pIpAdapterInfo_origin)
    {
        delete pIpAdapterInfo_origin;
    }


    //获取DNS服务器信息
    if(g_Global.m_bValidNetCardCnt >0)
    {
         FIXED_INFO *fi = (FIXED_INFO *)GlobalAlloc(GPTR,sizeof( FIXED_INFO));
         ULONG ulOutBufLen = sizeof(FIXED_INFO);
         DWORD ret = ::GetNetworkParams(fi, &ulOutBufLen);
         if(ret != ERROR_SUCCESS)
         {
             GlobalFree(fi);
             fi = (FIXED_INFO *) GlobalAlloc( GPTR, ulOutBufLen );
             ret = ::GetNetworkParams(fi, &ulOutBufLen);
             if(ret != ERROR_SUCCESS)
             {
              printf("Get Dns server failed");
             }
         }

         //暂时将多个网卡dns合并成一个
         for(int i=0;i<5;i++)
         {
            sprintf(g_Global.m_netCardList[i].m_szDNS,"%s",fi->DnsServerList.IpAddress.String);
         }
         GlobalFree(fi);
    }

    if(g_Global.m_bValidNetCardCnt>0)
    {
        //判断是否有传入指定网卡，如果有，找到并设置为目标网卡
        if(!g_Global.m_strBindIpAddress.empty())
        {
            bool foundIp=false;
            for(int i=0;i<5;i++)
            {
                for(int j=0;j<5;j++)
                {
                    if(g_Global.m_strBindIpAddress == g_Global.m_netCardList[i].m_szIPInfo[j].m_szIP)
                    {
                        g_Global.m_bValidNetCardId = i;
                        g_Global.m_netCardList[i].m_nValidIPId = j;
                        foundIp=true;
                        break;
                    }
                }
                if(foundIp)
                {
                    break;
                }
            }
        }

        int ipId=g_Global.m_netCardList[g_Global.m_bValidNetCardId].m_nValidIPId;
        sprintf(g_Global.m_szNetworkIP,"%s",g_Global.m_netCardList[g_Global.m_bValidNetCardId].m_szIPInfo[ipId].m_szIP);
        return g_Global.m_szNetworkIP;
    }
    else
    {
        return "";
    }
    #endif
}


const char* CNetwork::GetHostMac()
{
    #if defined(Q_OS_LINUX)
    return CNetworkTool::GetHostMac();
    #else
    return g_Global.m_netCardList[g_Global.m_bValidNetCardId].m_szMAC;
    #endif
}

#if 0
void* TimerProHeartbeat(void* dwTime __attribute__((__unused__)))
{
    while(1)
    {
        bool bLink = IsLinkNetwork();

        if (bLink != g_Global.m_bLinkInternet)
        {
            g_Global.m_bLinkInternet = bLink;

            string strBuf = CWebProtocol::CmdResponseGetHeartbeatInfo(g_Global.m_bLinkInternet);

            g_Global.m_WebNetwork.m_WebSend.ForwardDataToWeb(NULL, strBuf);
        }

        usleep(5000000);  // 5s
        //break;
    }
}
#endif

void* TimerProTimerScheme(void* dwTime __attribute__((__unused__)))
{
    vector<StTodayTimerP> PreTimerVec;
    #if defined(Q_OS_LINUX)
    CLxTimer preTime;
    preTime.GetCurrentTimeT();
    #else
    LARGE_INTEGER preTime;
    LARGE_INTEGER m_frequency;			// 硬件支持的高精度计数器的频率
    QueryPerformanceFrequency(&m_frequency);
    QueryPerformanceCounter(&preTime);
    #endif

    bool register_by_limitDate = (g_Global.m_RegisterLimitDate.GetYear() >= 2023);
    while(1)
    {
        //usleep(800000);  // 800ms
        CTime tNow = CTime::GetCurrentTimeT();
        static time_t count = 0;
        count++;

        #if SUPPORT_SOFTDOG
        if( APP_IS_SOFTDOOG )
        {
            if( tNow > g_Global.m_softDog.GetLimitDate() )
			{
				//加密狗超出时间限制
                g_Global.m_softDog.StatusChange();
			}
        }
        else if( APP_IS_REGISTER )
        {
            if(register_by_limitDate)
            {
                //printf("check date!:getYear=%d,getMonth=%d,getDay=%d\n",g_Global.m_RegisterLimitDate.GetYear(),g_Global.m_RegisterLimitDate.GetMonth(),g_Global.m_RegisterLimitDate.GetDay());
                if( tNow > g_Global.m_RegisterLimitDate )
                {
                    CRegister::RegisterStatusChange();
                }
            }
        }
        #endif

        #if APP_IS_LZY_COMMERCE_VERSION
        // 每隔10秒检查TTS试用状态，如果正在试用但已过期，则重置授权状态
        if(count % 10 == 0)
        {
            if (g_Global.b_tts_basic_Authorized == 2) {  // 2=未授权但正在试用
                CTTSTrial ttsTrial;
                if (!ttsTrial.IsTrialValid()) {
                    g_Global.b_tts_basic_Authorized = 3;  // 3=未授权且试用期结束
                    printf("TTS Trial Expired: Authorization status changed to TRIAL_EXPIRED\n");
                    // 记录试用期结束日志
                    g_Global.m_logTable.InsertLog(CMyString("System"),
                                                CMyString("TTS Trial"),
                                                LT_RUNNING_STATE,
                                                LANG_STR(LANG_SECTION_ZONE_GROUP, "Trial Expired", ("试用期结束")));
                }
            }
        }
        #endif

        //如果是备用服务器，局域网内如果存在多台服务器，那么不处理定时点
        #if IS_BACKUP_SERVER
        if(!g_Global.m_serverSync.IfBakcupServerChangeToMaster())
        {
            continue;
        }
        #endif

        // 刚开机需要根据定时点时间段来恢复定时点（延长时间才能保证分区上线）
        if (count >= 15)
        {
            // 集中模式
            if (WP_IS_CENTRALIZED)
            {
                // 检测定时点
                g_Global.m_PlayQueue.CheckTimers(tNow);
                //检测电源时序器,3s发一次
                if(count % 1 == 0)
                {
                    g_Global.m_TimerScheme.CheckTimersSequenceStatus(tNow);
                }
                vector<StTodayTimerP> TimerVec;
                g_Global.m_TimerScheme.GetTodayTimer(tNow,TimerVec);
                if(!CTimerScheme::IsTimeVecEqual(PreTimerVec,TimerVec)) //不相等才发送给WEB
                {
                    PreTimerVec=TimerVec;
                    printf("PreTimerVec!=CurtimerVec\n");
                    g_Global.m_WebNetwork.ForwardTodayTimerInfoToWeb(NULL,TimerVec);
                }
            }
        }

        uint32_t lTimeSleep = 800;
        #if defined(Q_OS_LINUX)
            CLxTimer currentTime;
            currentTime.GetCurrentTimeT();
            CLxTimer m_timer_diff = (currentTime - preTime);
            double  ms_diff_ms=m_timer_diff.GetTimer()*1000.0;
		#else
		    LARGE_INTEGER currentTime;
            QueryPerformanceCounter(&currentTime);
            LARGE_INTEGER m_timer_diff;
            m_timer_diff.QuadPart += (currentTime.QuadPart - preTime.QuadPart);
            double ms_diff_ms = ((currentTime.QuadPart - preTime.QuadPart)*1000.0)/(double)m_frequency.QuadPart;
		#endif

        if(ms_diff_ms>=1000.0)
        {
            lTimeSleep=0;
        }
        else
        {
            lTimeSleep=800;
        }
        preTime = currentTime;
        //printf("ms_diff_ms=%f,TimerSleep=%d\n",ms_diff_ms,lTimeSleep);
        usleep(lTimeSleep*1000);
    }
}



void* TimerSectionStatusChange(void* dwTime __attribute__((__unused__)))
{
    map<string, int> m_vec_Sections;			    // 分区状态变化的分区
    while(1)
    {
        usleep(300000);  // 300ms
        QMutexLocker locker(&g_Global.m_Sections.sectionStatusQMutex);
        if( m_vec_Sections != g_Global.m_Sections.m_vec_Sections_Vary )
        {
            m_vec_Sections = g_Global.m_Sections.m_vec_Sections_Vary;
            if(m_vec_Sections.size() == 0)
            {
                continue;
            }
            map<string, int>::iterator iter;
            vector<string> vecSectionMac;
            for(iter = m_vec_Sections.begin(); iter != m_vec_Sections.end(); iter++) 
            {
                //取出相应的sections
                LPCSection lpSection=g_Global.m_Sections.GetSectionByMac((iter->first).data());
                if(lpSection!=NULL)
                {
                    //加入到待发送序列
                   vecSectionMac.push_back(lpSection->GetMac());
                }
            }
            //printf("vecSections.size()=%d.\n",vecSectionMac.size());
            g_Global.m_WebNetwork.ForwardVarySectionInfoToWeb(DEVICE_SECTION,vecSectionMac,NULL,false);
            g_Global.m_Network.ForwardVarySectionsStatusToControlDevices(vecSectionMac,NULL);

            g_Global.m_Sections.DelVarySection();
            m_vec_Sections.clear();
        }
    }
}


void* TimerProcCheckStatus(void* dwTime __attribute__((__unused__)))
{
    //int* Sec = (int*)dwTime;

    while(1)
    {
        /*
        // pthread_cond_timedwait实现超时处理，节省系统性能
        pthread_mutex_lock(&PlaylistUpgradeMutex);
        CLxTimer t;
        t.GetCurrentTimeT();
        t.AddTimer(0, 1000*1000);   // 秒, 微秒

        struct timespec tsp; //线程睡眠时间
        tsp.tv_sec  = t.GetSec();
        tsp.tv_nsec = t.GetUsec()*1000; // 纳秒

        pthread_cond_timedwait(&pcSec, &PlaylistUpgradeMutex, &tsp);
        pthread_mutex_unlock(&PlaylistUpgradeMutex);
        */
        usleep(1000000);

        if(g_Global.m_nPlayUnitTimeout++ >=5)
        {
            LOG("g_Global.m_nPlayUnitTimeout >= 5,exit!", LV_INFO);
            exit(0);
        }

        CTime time = CTime::GetCurrentTimeT();
        // 如果是正点，需要新建新一天的日志文件
        if(time.GetHour()==0 && time.GetMinute()==0 && time.GetSecond()==0)
        {
            if(WRITE_LOG)
            {
                g_Global.CreateLogFile();
            }
            //恢复所有定时点的单次取消
            g_Global.m_TimerScheme.ResetTimerSingleCancel();

            #if SUPPORT_CALL_RECORD
            // 清理过期的通话记录及录音文件
            g_Global.m_RecordManager.CleanupExpiredRecords();
            #endif
        }

        if(time.GetSecond() % 2 == 0)
        {
            // 搜索大屏设备在线     待修改
            #if 0  //disable tablet search!
            g_Global.m_WebNetwork.m_WebSend.SearchOnlineTablet();
            #endif

            #if defined(Q_OS_LINUX)
            NetCardInfo temp_netcard;
            CNetworkTool::GetValidNetCardInfo(&temp_netcard);
            if( strcmp(g_Global.m_szNetCard.ifaName,temp_netcard.ifaName) )
            {
                //存在新的网卡，那么需要重启App,如果只是拔掉网线，那么不处理
                if( strlen(temp_netcard.ifaName)>0 )
                {
                    printf("NetCard change:ifr=%s,ip:%s\n",temp_netcard.ifaName,temp_netcard.ifaAddr);
                    printf("NetCard changed!,restart App!\n");
                    _exit(0);
                }
            }
            //同一网卡，但是IP变了，需要更新
            else if(strcmp(g_Global.m_szNetCard.ifaAddr,temp_netcard.ifaAddr))
            {
                if(strlen(temp_netcard.ifaAddr)>0)
                {
                    CNetwork::GetHostIP();
                    CNetworkTool::GetValidNetCardInfo(&g_Global.m_szNetCard);
                }
            }
            #endif
        }

        if (time.GetSecond()%2 == 0)
        {
            // 检测设备掉线情况
            g_Global.m_Network.DevicesCheckOffline(time.GetTime());
            #if 0  //disable tablet check!
            g_Global.m_WebNetwork.TabletsCheckOffline(time.GetTime());
            #endif
            //g_Global.m_WebNetwork.WebCheckOffline(time.GetTime());
        }

        //如果是备用服务器，局域网内如果存在多台服务器，那么不处理
        #if IS_BACKUP_SERVER
        if(!g_Global.m_serverSync.IfBakcupServerChangeToMaster())
        {
            continue;
        }
        #endif

        // 每10分钟同步一次时间
        if(time.GetMinute()%10==0 && time.GetSecond()==0)
        {
            g_Global.m_Network.m_CmdSend.CmdSyncronizeTime(); // 广播同步时间
        }
        
        // 每隔10秒发一次搜索设备的命令
        if (time.GetSecond()%10 == 0)
        {
            bool canSearch=true;
            #if IS_BACKUP_SERVER
            if(!g_Global.m_serverSync.IfBakcupServerChangeToMaster())
            {
                canSearch=false;
            }
            #endif
            if(canSearch)
            {
                // 定时搜索设备目的是：防止终端判断主机掉线
                g_Global.m_Network.m_CmdSend.CmdSearchOnlineDevice();
            }
        }

#if 0
        if(time.GetSecond()%1 == 0)
        {
            // 定时检测AUX工作状态
            g_Global.m_Network.CheckAUXWorking();
        }
#endif

        if (time.GetSecond()%2 == 0)
        {
            // 检测获取文件信息
            g_Global.m_Network.DeviceCheckGetFileInfo();
        }

        if (time.GetSecond()%1 == 0)
        {
            #if SUPPORT_NOISE_DETECTOR
            // 每隔一秒获取解码终端的自适应音量值
            g_Global.m_Network.DevicesCheckNoiseDetectorsVolume(time.GetTime());   
            #endif

            // 检测文件更新
            g_Global.m_Network.DeviceCheckNeedUpdateFile();

            // 定时检查Web端是否需要更新文件
            g_Global.m_Network.CheckWebNeedUpdateFile();

            // 检测音频采集器使用情况
            g_Global.m_Network.DeviceCheckAudioCollector();

#if 0       //开机不需要主动转发分区信息，20201026 考虑后还是先加上，主要是为了避免开机前10s大量分区同时上线时这些指令单个发往控制设备，容易造成控制设备卡顿
            //后续在处理搜索设备指令时处理不要每次都发送单个分区状态，这样就可以去除此处。
            // 把所有的分区和采集器状态发送给所有的分控设备
            g_Global.m_Network.ForwardAllDevicesStatusToAllControlDevices();
#endif
        }

        if(time.GetSecond() == 0)        // 每60秒检测待删除文件
        {
            g_Global.m_fileManager.CheckDelFile();
        }

        if(time.GetSecond() % 3 == 0)         // 每3秒分发一次网络信息
        {
            g_Global.m_WebNetwork.ForwardNetworkInfo();
        }

        
    }
}

void* TimerProcPlayTask(void* dwTime __attribute__((__unused__)))
{
    //int* Ms = (int*)dwTime;  //间隔毫秒数
    int count = 0;

    while(1)
    {
        // pthread_cond_timedwait实现超时处理，节省系统性能
        // 在设置系统时间(时间往前设置07:00->06:00)时，该计时会堵塞，直到计时到之前时间为止
        /*
        pthread_mutex_lock(&pmPlayTask);

        // 每休眠一次 误差大几率加一毫秒
        CLxTimer t;
        t.GetCurrentTimeT();     // 非线程安全
        t.AddTimer(0, 200*1000);

        struct timespec tsp; //线程睡眠时间
        tsp.tv_sec  = t.GetSec();
        tsp.tv_nsec = t.GetUsec()*1000; // 纳秒

        pthread_cond_timedwait(&pcMs, &pmPlayTask, &tsp);
        pthread_mutex_unlock(&pmPlayTask);
        */
        // 获取时间
        struct timeval tl;
        gettimeofday(&tl, NULL);
        // tv_sec*1000在32位系统上会溢出ctime_t的取值范围,这里使用longlong型
        ctime_t tNow = (ctime_t)(tl.tv_sec)*1000 + tl.tv_usec/1000; // 获取毫秒数

        g_Global.m_PlayQueue.CheckPlayUnits(tNow);

        //如果是定时任务，那么需要继续检查播放任务(保证多个定时任务之间的连续性)
        int count=50;
        while(--count)
        {
            bool isTimerTask=g_Global.m_PlayQueue.CheckPlayTasks(tNow);
            if(!isTimerTask)
            {
                break;
            }
        }

        usleep(100000);

        count++;
    }
}


/*----             计时器 保留，待修改              ------*/
void TimerThread(void* lpParam __attribute__((__unused__)))
{
    pthread_t pthSec;
    pthread_t pthMs;
    pthread_t pthHb;
    pthread_t pthst;

    // 线程可分离状态 可分离线程会在主线程退出占用系统资源，根据实际情况注释掉
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);

    //int TimerSec = 1000;    传递整型参数出现异常错误
    //int TimerMs  = 200;
    int sec = pthread_create(&pthSec, &attr, TimerProcCheckStatus, NULL);
    int ms = pthread_create(&pthMs, &attr, TimerProcPlayTask, NULL);
    int ts   = pthread_create(&pthMs, &attr, TimerProTimerScheme, NULL);
    int st = pthread_create(&pthst, &attr, TimerSectionStatusChange, NULL);
    //int hb   = pthread_create(&pthHb, &attr, TimerProHeartbeat, NULL);

    pthread_attr_destroy(&attr);

    if(sec != 0)
    {
        perror("create sec");
    }

    if(ms != 0)
    {
        perror("create ms");
    }

    if(ts != 0)
    {
        perror("create ts");
    }

/*
    if(hb != 0)
    {
        perror("create hb");
    }
*/
    usleep(20);
}



/*********************************************************/

// 网络开始工作
bool CNetwork::StartWorking()
{
    g_Global.SetFixedData();

    // UDP网络始终开启
    UdpStartWorking();

    //备用服务器开机时不启动TCP服务端，等待接管了主服务器后再启动
    #if !IS_BACKUP_SERVER
    // TCP网络初始化
    TcpStartWorking();
    #endif

    //开启定时器 保留，待修改 zhuyg
    TimerThread(NULL);
    //sleep(10);

    // 启动播放歌曲任务线程  *
    g_Global.m_PlayQueue.m_SongPlayer.StartWorking();

    // 启动webSocket
    g_Global.m_WebNetwork.StartWebWorking();

    // 开启监控
    g_Global.m_MonProc.Init();

    // 开启VOIP客户端
    //g_Global.m_Voip.StartVOIP();

    // 开启CLI *
    m_CLIServer.StartWorking();

#if SUPPORT_TCP_DEFAULT
    g_Global.m_Network.m_kcpSockets.StartWorking();
#endif


#if SUPPORT_SERVER_SYNC
    g_Global.m_serverSync.Init();
#endif

#if SUPPORT_NET_RADIO
    // 初始化电台管理器
    g_Global.m_RadioManager.InitRadioData();
    // 初始化NodeJS服务
    NodeJSManager::getInstance()->startRadioJS();
#endif

    return TRUE;
}

// UDP网络开始工作
bool CNetwork::UdpStartWorking()
{
    #if 0
    // Linux服务器
    m_pSocketMulticast = m_udpSockets.CreateSocket(MULTICAST_RECV_PORT, OnRecvUdpData, this);

    if (m_pSocketMulticast == NULL)
    {
        return FALSE;
    }
    else
    {
        m_pSocketMulticast->JoinMulticast(MULTICAST_RECV_IP);
    }


    m_pSocketUnicast = m_udpSockets.CreateSocket(UDP_PORT, OnRecvUdpData, this);

    if (m_pSocketUnicast == NULL)
    {
        return FALSE;
    }
#endif

    m_MySocketMulticast.StartWorking(g_Global.m_szNetworkIP,MULTICAST_RECV_PORT,CM_MULTICAST,OnRecvUdpData,MULTICAST_RECV_IP);
	m_MySocketUnicast.StartWorking(g_Global.m_szNetworkIP,UDP_PORT,CM_UNICAST,OnRecvUdpData,NULL);

#if SUPPORT_TCP_DEFAULT
    m_MyUdpKCPUnicast.StartWorking(g_Global.m_szNetworkIP,g_Global.m_KCP_PORT,CM_UNICAST,OnRecvKcpData,NULL);
	m_MyUdpWebPagingUnicast.StartWorking(g_Global.m_szNetworkIP,g_Global.m_KCP_PORT+1,CM_UNICAST,OnRecvWebStreamData,NULL);  
#endif
    /*************************************************************/


    m_CmdSend.StartCommand(NULL);

    m_bNetworkIsReady=true;

    return TRUE;
}

// TCP 保留，待修改
bool CNetwork::TcpServerStartWorking()
{
#if 0
    m_TcpServer.SetSubpackage(true);
    return m_TcpServer.StartListen(g_Global.m_TCP_PORT, OnTcpServerCallback, this);
#endif

    return m_MyTcpServer.StartWorking(g_Global.m_szNetworkIP,g_Global.m_TCP_PORT);
}

// 退出网络
void CNetwork::StopWorking(void)
{
    UdpStopWorking();
    TcpServerStopWorking();
}

// 退出UDP网络
void CNetwork::UdpStopWorking(void)
{
#if defined(Q_OS_LINUX)
    // Linux服务器
    m_udpSockets.CleanSockets();
#endif
}

// 退出TCP网络 TCP 保留，待修改
void CNetwork::TcpServerStopWorking(void)
{
    m_TcpServer.StopListen();
}

void CNetwork::ExitWorking(void)
{
    // 退出集中模式要修改成分布模式
    if (WP_IS_CENTRALIZED)
    {
        g_Global.m_WorkPattern = WP_DISTRIBUTION;
        DeviceChangeWorkPattern(TRUE);
    }

    // 清除播放任务和退出网络
    g_Global.m_PlayQueue.Clear();

    // 要调用m_SongPlayer.StopWorking，否则程序可能会卡住
    g_Global.m_PlayQueue.m_SongPlayer.StopWorking();

    StopWorking();
}

/************************************************************/

// 发送数据 TCP 保留，待修改
bool CNetwork::SendData(void*           lpData,								// 数据
                        int             nLen,								// 数据长度
                        CSection&       device,								// 设备
                        const char*		szUdpIP,							// 目标IP（UDP）
                        unsigned short	uUdpPort,							// 端口（UDP）
                        int             nRepeatCount)						// 重复次数
{
    if(device.GetDeviceModel() == MODEL_CONTROL_SERVER)
    {
        //return SendTcpData(lpData, nLen, device.m_pSocketObj);
    }
    else
    {
        ushort  command     = CharsToShort((char*)lpData);      // 命令
        int mustSendUDP=0;
        if( (command == CMD_REBOOT || command == CMD_IP_INFO || command == CMD_NETWORK_MODE || command == CMD_FIRMWARE_UPGRADE || command == CMD_RESET_DATA ) ) //&& !device.m_pSocketObj  )    //device没有连接上TCP且目标指令为重启设备或者改变IP属性
        {
            mustSendUDP=1;
        }
        if(mustSendUDP)
        {
            SendUdpData(lpData, nLen, szUdpIP, uUdpPort, nRepeatCount);
        }
        
        if(device.IsTcpMode()) //&& device.m_pSocketObj)
        {
            SendTcpData(lpData, nLen, device.m_pSocketObj);
        }
        else if(!mustSendUDP)
        {
            SendUdpData(lpData, nLen, szUdpIP, uUdpPort, nRepeatCount);
        }

        #if 0
        if ( (NETWORK_IS_TCP || device.IsTcpMode() ) && !mustSendUDP)
        {
            return SendTcpData(lpData, nLen, device.m_pSocketObj);
        }
        else
        {
            return SendUdpData(lpData, nLen, szUdpIP, uUdpPort, nRepeatCount);
        }
        #endif
    }
    return true;
}


bool CNetwork::SendWebData(void*          lpData,			// 数据
                           int            nLen,				// 数据长度
                           CWebSection&   device __attribute__((unused)),           // 设备
                           const char*    szUdpIP,			// 目标IP（UDP）
                           unsigned short uUdpPort,			// 端口（UDP）
                           int            nRepeatCount)		// 重复次数)
{
    if (NETWORK_IS_TCP)
    {
        // TCP发送，待处理，zhuyg
        //return SendTcpData(lpData, nLen, device.m_pSocketObj);
        return SendTcpData(lpData, nLen, NULL);
    }
    else
    {
        return SendUdpData(lpData, nLen, szUdpIP, uUdpPort, nRepeatCount);
    }
    return false;
}

// TCP 保留，待修改
bool CNetwork::SendTcpData(	void*			lpData,						// 数据
                            int				nLen,						// 数据长度
                            LPS_SOCKET_OBJ	sockobj)					// socket对象
{
    if (sockobj != NULL && nLen>0 && lpData != NULL)
    {
        //先判断还有多少位未发送完成的数据，如果超过，那么不发送音频流(其他指令还是保留，避免遗漏重要控制指令)
        ushort  command  = CharsToShort(&((char*)lpData)[0]);      // 命令
        if(command == CMD_STREAM_SOURCE || command == CMD_PAGING_STREAM ||\
            command == CMD_WEB_PAGING_STREAM || command == CMD_AUDIO_COLLECTOR_STREAM_TCP ||\
            command == CMD_AUDIO_MIXER_STREAM)
        {
            int pendingDataLen=0;
            if((pendingDataLen=m_MyTcpServer.GetPendingDataLength(sockobj))>=32768)
            {
                //printf("Error:tcp pendingDataLen=%d\n",pendingDataLen);
                return false;
            }
        }
        return m_MyTcpServer.SendDataToClient((char*)lpData, nLen, sockobj);
    }

    return FALSE;
}

bool CNetwork::SendUdpData(void*            lpData,		// 数据
                           int              nLen,		// 数据长度
                           const char*      szIP,		// IP地址
                           unsigned short	uPort,		// 端口
                           int		nRepeatCount)		// 重复次数
{
    if( lpData == NULL || !(nLen>0 && nLen<=1450) )
    {
        printf("SendUdpData error,nLen=%d\n",nLen);
        return false;
    }

    // UDP单播
    if (uPort == UDP_PORT || uPort == RESTART_PORT)
    {
        return m_MySocketUnicast.SendData(lpData, nLen, szIP, uPort, nRepeatCount);
    }
    // 广播
    else if (uPort == MULTICAST_SEND_PORT || uPort == MULTICAST_RECV_PORT)
    {
        ushort  command  = CharsToShort(&((char*)lpData)[0]);      // 命令
        
        return m_MySocketMulticast.SendData(lpData, nLen, szIP, uPort, nRepeatCount);
    }else
    {
        return m_MySocketUnicast.SendData(lpData, nLen, szIP, uPort, nRepeatCount);
    }

    return FALSE;
}

/***********************************************************/

// 改变网络模式（TCP/UDP）
void CNetwork::TcpStartWorking(void)
{
    if(!TcpServerStartWorking())
    {
        NOTIFY("TCP Start failed!");
    }
    printf("TCP Start succeed!");
}

// 设备改变工作模式(集中模式、分布模式)
void CNetwork::DeviceChangeWorkPattern(bool bExitApp)
{
    for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
    {
        int nDeviceCount = g_Global.m_pAllDevices[i]->GetSecCount();

        for (int j=0; j<nDeviceCount; ++j)
        {
            CSection& device = g_Global.m_pAllDevices[i]->GetSection(j);

            // 工作模式不一样
            if (device.IsOnline()
                && device.GetWorkPattern() != WP_UNKNOWN
                && device.GetWorkPattern() != g_Global.m_WorkPattern)
            {
                //device.SetPlayID(-1);

                // 停止
                m_CmdSend.CmdSetIdleStatus(device, bExitApp ? 3 : 1);

                // 需要设置工作模式
                m_CmdSend.CmdSetWorkPattern(device, g_Global.m_WorkPattern, bExitApp ? 3 : 1);
            }
        }
    }
}

// 正在播放的分区设备变成空闲状态
void CNetwork::DevicePlayingToIdleStatus(void)
{
    int nSecCount = g_Global.m_Sections.GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        CSection& device = g_Global.m_Sections.GetSection(i);

        // 如果正在播放歌曲，则停止播放
        if (device.GetProSource() == PRO_LOCAL_PLAY)
        {
            device.SetPlayID(-1);

            // 停止
            m_CmdSend.CmdSetIdleStatus(device);
        }
    }
}

// 全部在线设备执行命令(TCP)
void CNetwork::DevicesAllSendTcpData(char* data, int len)
{
    for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
    {
        int nDeviceCount = g_Global.m_pAllDevices[i]->GetSecCount();

        for (int j=0; j<nDeviceCount; ++j)
        {
            CSection& device = g_Global.m_pAllDevices[i]->GetSection(j);

            if (device.IsOnline() && device.m_pSocketObj != NULL)
            {
                SendTcpData(data, len, device.m_pSocketObj);
            }
        }
    }
}

#if SUPPORT_NOISE_DETECTOR
// 检测解码终端的自适应音量值
void CNetwork::DevicesCheckNoiseDetectorsVolume(ctime_t tNow)
{
    //先获取所有噪声自适应器的分区
    vector<CMyString> vecAllSections = g_Global.m_NoiseDetectors.GetAllSectionsFromAllNoiseDetectors();
    //便利所有分区，找到在线的
    for(int i=0;i<vecAllSections.size();i++)
    {
        CSection* pSection = g_Global.m_Sections.GetSectionByMac(vecAllSections[i]);
        if(pSection && pSection->IsOnline())
        {
            //空闲状态下才更新音量(理论上是要大于3秒，最稳妥)
            if(pSection->IsIdle())
            {
                if(tNow - pSection->m_tNoiseDetectorLastTime >= 3)
                {
                    pSection->m_nNoiseDetectorSectionVolume = g_Global.m_NoiseDetectors.GetSectionVolumeFromAllNoiseDetectors(pSection->GetMac());
                    //pSection->m_tNoiseDetectorLastTime = tNow;
                    //printf("DevicesCheckNoiseDetectorsVolume:mac=%s,volume=%d\n",pSection->GetMac(),pSection->m_nNoiseDetectorSectionVolume);
                }
            }
            else
            {
                pSection->m_tNoiseDetectorLastTime = tNow;
            }
        }
    }
}
#endif

// 检测各种设备掉线情况
void CNetwork::DevicesCheckOffline(ctime_t tNow)
{
    bool	toSearchDevice	= FALSE;

    // 检测各种设备掉线情况
    for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
    {
        // DSP9312
        if (i == DEVICE_INTERCOM_STATION)
        {
            continue;
        }

        int nDeviceCount = g_Global.m_pAllDevices[i]->GetSecCount();

        for (int j=0; j<nDeviceCount; ++j)
        {
            CSection& device = g_Global.m_pAllDevices[i]->GetSection(j);

            if (device.IsOnline())
            {
                toSearchDevice = DeviceCheckOffline(device, tNow);

                // 如果工作模式和系统的不匹配，则需要设置
                WorkPattern workPattern = device.GetWorkPattern();
                if (workPattern != WP_UNKNOWN && workPattern != g_Global.m_WorkPattern)
                {
                    m_CmdSend.CmdSetWorkPattern(device, g_Global.m_WorkPattern);

                    //如果是寻呼台，模式不一样了（重启）发送时间
                    if(device.GetDevType() == DEVICE_PAGER)
                    {
                        g_Global.m_Network.m_CmdSend.CmdSyncronizeTime(device);
                    }
                }

                // 如果音频传输方式与系统的不匹配，则需要设置
                Audiocast auiocast = device.GetAudicast();
                if (auiocast != AUDIOCAST_UNKNOWN && auiocast != g_Global.m_Audiocast)
                {
                    //m_CmdSend.CmdAudiocastMode(device, g_Global.m_Audiocast);
                }
            }
            else if (device.IsSectionDevice() && device.m_PlayedRecently.IsTimeout(tNow))
            {
                if(g_Global.m_bNeedResumeLocalPlay)
                {
                    // 如果没有其它分区播放此歌曲  CS 2019-5-7 (掉线后恢复播放)
                    int nPlayID = device.m_PlayedRecently.m_nPlayID;
                    if ( nPlayID > 0
                         && !g_Global.m_Sections.HasPlaySections(nPlayID)
                         && !g_Global.m_Sections.HasPrePlaySections(nPlayID))
                    {
                        // 停止播放歌曲
                        g_Global.m_PlayQueue.m_SongPlayer.SetPlayStatus(nPlayID, SPS_STOP,true);

                        // 如果是定时点，需要停止定时点
                        CTimePoint* pTimePoint = g_Global.m_TimerScheme.GetTimePointByPlayID(nPlayID);

                        if (pTimePoint != NULL)
                        {
                            pTimePoint->SetPlayID(-1);
                            CMyString strLog;
                            strLog.Format("%s Reset Play ID - DevicesCheckOffline", pTimePoint->GetName().C_Str());
                            g_Global.m_Network.AddLog(strLog);
                        }
                    }
                }

                device.m_PlayedRecently.ResetTimeout();
            }
        }
    }
}

// 检测各种设备掉线情况
bool CNetwork::DeviceCheckOffline(CSection& device, ctime_t tNow)
{
    bool toSearchDevice = FALSE;

    if (device.IsOnline())
    {
        ctime_t	span = abs(tNow - device.GetLatestTime());
            
        int offinlie_time=OFFLINE_TIME;
        if(device.IsTcpMode())
        {
            offinlie_time=OFFLINE_TIME_TCP;
            #if 1   //20210706取消KCP心跳超时检测，主动离线无效，应该是本地路由器策略问题
                    //20220927 启用超时检测，用来判定开始KCP正常，后来KCP不通的问题。
            pthread_mutex_lock(&device.sectionMutex);
            if(device.m_kcpsocket)
            {
                ctime_t	span_kcp=abs(tNow-device.m_kcpsocket->GetHeartBeatTime());
                if( span_kcp >= offinlie_time + 10 )  //KCP心跳超时,使用TCP发流
                {
                    if(device.m_kcpsocket->GetDesPort())
                    {
                        device.m_kcpsocket->SetDesPort(0);
                        printf("Device %s KCP heartBeatTimeout...\n",device.GetMac());
                    }
                }
            }
            pthread_mutex_unlock(&device.sectionMutex);
            #endif
        }
        if (span >= offinlie_time)
        {
            //printf("span : %ld\n", span);
            // CS 2019-5-7  (掉线后恢复播放)
            if(g_Global.m_bNeedResumeLocalPlay && (device.GetProSource() == PRO_LOCAL_PLAY || device.GetProSource() == PRO_TIMING))
            {
                device.m_PlayedRecently.m_nPlayID = device.GetPlayID();
            }
            printf("Device %s offline timeout...\n",device.GetMac());
            DeviceOffline(device);
        }
        else if (span >= (offinlie_time - 5))	//后面5秒要发搜索设备命令
        {
            toSearchDevice = TRUE;
        }
    }

    return toSearchDevice;
}

// 分区掉线
void CNetwork::DeviceOffline(CSection& device)
{
    pthread_mutex_lock(&device.sectionMutex);
    if(!device.IsOnline())    //如果已经离线，退出
    {
        pthread_mutex_unlock(&device.sectionMutex);
        return;
    }
    device.m_PlayedRecently.m_nRecentSrc = device.GetProSource();
    device.m_PlayedRecently.m_tOffline = CTime::GetCurrentTimeT().GetTime();
    device.m_PlayedRecently.m_nPlayID = device.GetPlayID();

    // 如果分区正在监听，而且正在播放歌曲
    if (device.IsListening() && device.GetPlayID() > 0)
    {
        g_Global.m_PlayQueue.m_SongPlayer.StopListenInSong(&device);
    }

    // 集中模式，分区掉线需要停止播放任务
    if (WP_IS_CENTRALIZED)
    {
        g_Global.m_PlayQueue.StopSectionPlaySource(&device);
    }

#if 1
    if(device.m_pSocketObj!=NULL)
    {
         printf("clean m_pSocketObj2......\n");
        g_Global.m_Network.m_MyTcpServer.ClearSocketObj(device.m_pSocketObj);
        device.m_pSocketObj=NULL;
    }
#endif

    //删除KCP端口
    if(device.m_kcpsocket!=NULL)
    {
        printf("clean m_kcpsocket2......\n");
        g_Global.m_Network.m_kcpSockets.CleanSocket(device.m_kcpsocket);
        device.m_kcpsocket=NULL;
    }

    // 重置数据
    device.ResetData();

    pthread_mutex_unlock(&device.sectionMutex);

    // 分区设备掉线，需要更新界面
    if (device.IsSectionDevice())
    {
        // 分区设备掉线，需要告诉分控设备
        ForwardSectionsStatusToControlDevices(&device, NULL);
        g_Global.m_WebNetwork.ForwardSectionInfoToWeb(DEVICE_SECTION, &device, NULL);
    }
    // 音频采集器掉线
    else if (device.IsAudioCollectorDevice())
    {
        // 需要告诉分控设备
        ForwardAllCollectorsStatusToControlDevices(&device, NULL);
        g_Global.m_WebNetwork.ForwardSectionInfoToWeb(DEVICE_AUDIO_COLLECTOR, &device, NULL);
    }
    // 网络时序器掉线
    else if (device.IsPowerSequenceDevice())
    {
        // 需要告诉分控设备
        //ForwardAllCollectorsStatusToControlDevices(&device, NULL);
        g_Global.m_WebNetwork.ForwardSectionInfoToWeb(DEVICE_SEQUENCE_POWER, &device, NULL);
    }
    // 如果消防采集器掉线，则把触发的分区停止触发
    else if (device.IsFireCollectorDevice())
    {
        UINT	uCount = g_Global.m_Sections.GetSecCount();
        for (UINT i=0; i<uCount; ++i)
        {
            CSection& section = g_Global.m_Sections.GetSection(i);

            if (section.GetProSource() == PRO_ALARM && strcmp(section.GetTriggerFireColMac(), device.m_netInfo.m_szMac) == 0)
            {
                m_CmdSend.CmdSetIdleStatus(section);
            }
        }

        // 需要告诉分控设备
        //ForwardAllCollectorsStatusToControlDevices(&device, NULL);
        g_Global.m_WebNetwork.ForwardSectionInfoToWeb(DEVICE_FIRE_COLLECTOR, &device, NULL);
    }
    #if SUPPORT_PAGER_CALL
    else if (device.GetDeviceModel() == MODEL_PAGER_A || device.GetDeviceModel() == MODEL_PAGER_B || device.GetDeviceModel() == MODEL_PAGER_C)    //寻呼台掉线
    {
        g_Global.m_Network.m_CmdSend.CmdForwardPagerStatus(NULL,&device);
        g_Global.m_WebNetwork.ForwardSectionInfoToWeb(device.GetDevType(), &device, NULL);

        #if SUPPORT_CALL_RECORD
        {
            //判断这个话筒是否有未完成的通话录音
            // 查找该设备的录音会话
            CMyString strCallIdPattern;
            strCallIdPattern.Format("PAGING_%s_", device.GetMac());
            // 获取匹配的录音会话
            std::vector<CMyString> matchingSessions = g_Global.m_RecordManager.GetRecordSessionsByPrefix(strCallIdPattern);
            bool bFoundSession = false;
            // 如果找到了会话，需要停止
            if (!matchingSessions.empty())
            {
                // 获取第一个匹配的会话信息
                RecordSession session;
                if (g_Global.m_RecordManager.GetRecordSession(matchingSessions[0], session))
                {
                    g_Global.m_RecordManager.StopRecordByMacPrefix(strCallIdPattern, RS_INTERRUPTED);
                }
            }
        }
        #endif
    }
    #endif
    else
    {
        // 加入web回调通知分区更新，保留,待处理
        g_Global.m_WebNetwork.ForwardSectionInfoToWeb(device.GetDevType(), &device, NULL);
    }


    CMyString strTip;
    strTip.Format((char*)("%s: %s is offline."), device.GetUTFName().data(), CProtocol::GetDescriptionDevice(device.GetDeviceModel()).C_Str());
    AddLog(strTip);

    // Linux服务器
    g_Global.m_logTable.InsertLog(CMyString(device.GetMac()),
                                  CMyString(device.GetName()),
                                  LT_RUNNING_STATE,
                                  LANG_STR(LANG_SECTION_ZONE_GROUP, "Offline", ("离线")));
}

// 分控服务器掉线
void CNetwork::ServerOffline(CSection &device)
{
    LOG("分控服务器掉线", LV_ERROR);

    // 重置数据
    device.ResetData();

    // 分控服务器掉线，需要告诉Web端
    g_Global.m_WebNetwork.ForwardSectionInfoToWeb(DEVICE_SERVER, &device, NULL);

    CMyString strTip;
    strTip.Format((char*)("%s: %s is offline."), device.GetUTFName().data(), CProtocol::GetDescriptionDevice(device.GetDeviceModel()).C_Str());
    AddLog(strTip);

    // Linux服务器
    g_Global.m_logTable.InsertLog(CMyString(device.GetMac()),
                                  CMyString(device.GetName()),
                                  LT_RUNNING_STATE,
                                  LANG_STR(LANG_SECTION_ZONE_GROUP, "Offline", ("离线")));
}


// 定时检查获取文件信息的状况
void CNetwork::DeviceCheckGetFileInfo(void)
{
    // 分区设备、寻呼台、移动控制设备、消防采集器、分控服务器
    CSections *pDevices[] = {&g_Global.m_Sections, &g_Global.m_Pagers, &g_Global.m_ControlDevices, &g_Global.m_FireCollectors,&g_Global.m_SequencePower,&g_Global.m_Servers};
    int nDeviceTypeCount = sizeof(pDevices)/sizeof(*pDevices);

    for (int i=0; i<nDeviceTypeCount; ++i)
    {
        int nSecCount = pDevices[i]->GetSecCount();

        for (int j=0; j<nSecCount; ++j)
        {
            CSection& device = pDevices[i]->GetSection(j);

            if (!device.IsOnline())
            {
                continue;
            }

            // 分区设备
            if (device.IsSectionDevice() && (device.m_bSupportSyncFile || device.m_strMemory == ""))  // CS 2019-5 （兼容不支持同步文件的设备）
            {
                #if 0 //普通分区设备不需要同步文件
                // 分区需要保存分组
                if (device.NeedGetFileInfo(DT_GROUP))
                {
                    m_CmdSend.CmdGetFileInfo(FILE_GROUP, device);
                }

                if (device.NeedGetFileInfo(DT_PLAYLIST))
                {
                    m_CmdSend.CmdGetFileInfo(FILE_PLAYLIST, device);
                }

                if (device.NeedGetFileInfo(DT_TIMER))
                {
                    m_CmdSend.CmdGetFileInfo(FILE_TIMER, device);
                }

                // 如果容量信息为空，则需要查询
                if (device.m_strMemory == (""))
                {
                    m_CmdSend.CmdGetFlashInfo(device);
                }
                #endif
            }
            // 控制设备
            else if (device.IsControlDevice())
            {
                if (device.NeedGetFileInfo(DT_PLAYLIST))
                {
                    m_CmdSend.CmdGetFileInfo(FILE_PLAYLIST, device);
                }

                if (device.NeedGetFileInfo(DT_GROUP))
                {
                    m_CmdSend.CmdGetFileInfo(FILE_GROUP, device);
                }

                if (device.NeedGetFileInfo(DT_AUDIO_COLLECTOR))
                {
                    m_CmdSend.CmdGetFileInfo(FILE_AUDIO_COLLECTOR, device);
                }

                if (device.NeedGetFileInfo(DT_USER))
                {
                    m_CmdSend.CmdGetFileInfo(FILE_USER, device);
                }

                // 支持寻呼台间对讲的版本才查询该寻呼台文件信息
                #if APP_IS_SUPPORT_INTERCOM_BETWEEN_PAGER
                    #if SUPPORT_PAGER_CALL
                    if (device.IsSupportCallDevice() && device.NeedGetFileInfo(DT_PAGER))
                    {
                        m_CmdSend.CmdGetFileInfo(FILE_PAGER, device);
                    }
                    #endif
                #endif



#if 0           //不需要同步定时文件
                if (device.NeedGetFileInfo(DT_TIMER))
                {
                    m_CmdSend.CmdGetFileInfo(FILE_TIMER, device);
                }
#endif
                // TCP或者UDP的集中模式分区由主机管理，自动同步分区数据
                if ((NETWORK_IS_TCP || WP_IS_CENTRALIZED) && device.NeedGetFileInfo(DT_SECTION))
                {
                    m_CmdSend.CmdGetFileInfo(FILE_SECTION, device);
                }
            }
            // 消防报警器
            else if (device.IsFireCollectorDevice())
            {
                if (device.NeedGetFileInfo(DT_FIRE_COLLECTOR))
                {
                    m_CmdSend.CmdGetFileInfo(FILE_FIRE_COLLECTOR, device);
                }

                // 如果还未获取到触发模式
                if (!device.m_pFireCollector->HasInitTriggerMode())
                {
                    m_CmdSend.CmdAlarmMode(device, 0xFF, 0);
                }

                // 如果还未获取到触发状态
                if (!device.m_pFireCollector->HasInitTriggerState())
                {
                    m_CmdSend.CmdGetAlarmState(device);
                }
            }
            //电源时序器
            else if (device.IsPowerSequenceDevice())
            {
                #if 0
                if (device.NeedGetFileInfo(DT_SEQUENCE_POWER))
                {
                    m_CmdSend.CmdGetFileInfo(FILE_SEQUENCE_POWER, device);
                }
                #endif
                // 如果还未获取到通道信息
                if (!device.m_pSequencePower->HasInitOK())
                {
                    printf("Not Init OK...\n");
                    m_CmdSend.CmdGetSequencePowerInfo(device);
                }
            }
        }
    }
}


// 定时检查文件是否需要同步
void CNetwork::DeviceCheckNeedUpdateFile(void)
{
    // 分区设备、寻呼台、移动控制设备、消防采集器、分控服务器
    CSections	*pDevices[]			= {&g_Global.m_Sections, &g_Global.m_Pagers, &g_Global.m_ControlDevices, &g_Global.m_FireCollectors, &g_Global.m_Servers};
    int			nDeviceTypeCount	= sizeof(pDevices)/sizeof(*pDevices);

    for (int i=0; i<nDeviceTypeCount; ++i)
    {
        int nDeviceCount = pDevices[i]->GetSecCount();

        for (int j=0; j<nDeviceCount; ++j)
        {
            CSection& device = pDevices[i]->GetSection(j);

            if (!device.IsOnline())
            {
                continue;
            }

            // 分区设备
            if (device.IsSectionDevice())
            {
                #if 0  //jms,对于终端，不需要下发文件信息
                bool bNeedUpdate = device.NeedUpdateSectionFile();

                // 有变动，才更新
                if (bNeedUpdate != device.IsNeedUpdate())
                {
                    device.SetNeedUpdate(bNeedUpdate);

                    // 状态改变告诉分控设备
                    ForwardSectionsStatusToControlDevices(&device, NULL);
                }
                #endif
            }
            // 控制设备
            else if (device.IsControlDevice() && ( !( device.IsTcpMode() && device.m_pSocketObj==NULL )) )
            {
                // 播放列表
                //20230614  未登录时不检查播放列表文件
                if (device.NeedUpdateFile(DT_PLAYLIST))
                {
                    printf("device:%s need update FILE_PLAYLIST\n",device.GetMac());
                    m_CmdSend.CmdUpdateFile(FILE_PLAYLIST, device);
                }

                // 分组
                if (device.NeedUpdateFile(DT_GROUP))
                {
                    printf("device:%s need update FILE_GROUP\n",device.GetMac());
                    m_CmdSend.CmdUpdateFile(FILE_GROUP, device);
                }

                // 音频采集
                if (device.NeedUpdateFile(DT_AUDIO_COLLECTOR) && device.IsUdpMode())    //为了兼容以前的旧寻呼台程序，寻呼台如果处于TCP模式，则不下发音频采集器信息
                {
                    printf("device:%s need update FILE_AUDIO_COLLECTOR\n",device.GetMac());
                    //m_CmdSend.CmdUpdateFile(FILE_AUDIO_COLLECTOR, device);
                    m_CmdSend.CmdSendAudioList(device);
                }

                // 用户文件
                if (device.NeedUpdateFile(DT_USER))
                {
                    printf("device:%s need update FILE_USER\n",device.GetMac());
                    m_CmdSend.CmdUpdateFile(FILE_USER, device);
                }

                // 支持寻呼台间对讲的版本才发送寻呼台设备信息
                #if APP_IS_SUPPORT_INTERCOM_BETWEEN_PAGER
                    #if SUPPORT_PAGER_CALL
                    // 寻呼台文件
                    if (device.IsSupportCallDevice() && device.NeedUpdateFile(DT_PAGER))
                    {
                        printf("device need update FILE_PAGER\n");
                        m_CmdSend.CmdUpdateFile(FILE_PAGER, device);
                    }
                    #endif
                #endif

                #if 0      //不发送定时文件
                // 定时信息
                if (device.NeedUpdateFile(DT_TIMER))
                {
                    m_CmdSend.CmdUpdateFile(FILE_TIMER, device);
                }
                #endif
                // TCP或者UDP的集中模式分区由主机管理
                if ((NETWORK_IS_TCP || WP_IS_CENTRALIZED) && device.NeedUpdateFile(DT_SECTION))
                {
                    printf("device need update FILE_SECTION\n");
                    m_CmdSend.CmdUpdateFile(FILE_SECTION, device);
                }
            }
            // 消防报警器（）
            else if (device.IsFireCollectorDevice())
            {
                if (device.NeedUpdateFile(DT_FIRE_COLLECTOR))
                {
                    m_CmdSend.CmdUpdateFile(FILE_FIRE_COLLECTOR, device);
                }
            }
            #if 0
            //电源时序器
            else if (device.IsPowerSequenceDevice())
            {
                if (device.NeedUpdateFile(DT_SEQUENCE_POWER))
                {
                     m_CmdSend.CmdUpdateFile(FILE_SEQUENCE_POWER, device);
                }
            }
            #endif
            
            #if 0
            // 分控服务器
            else if(device.GetDeviceModel() == MODEL_CONTROL_SERVER)
            {
                if (device.NeedUpdateFile(DT_PLAYLIST))
                {
                    m_CmdSend.CmdSRCUpdateFile(FILE_PLAYLIST, device);
                }

                if(device.NeedGetFileInfo(DT_TIMER))
                {
                    m_CmdSend.CmdSRCUpdateFile(FILE_TIMER, device);
                }
            }
            #endif

#if 0
            // 文件同步通知web
            if(device.IsSectionDevice())
            {
                // 通知Web终端
                if(device.IsOnline() && device.m_bSupportSyncFile)
                {
                    if(device.NeedUpdateFile(DT_PLAYLIST))
                    {
                            string strFileDateTime = g_Global.m_PlayList.GetDateTime().C_Str();

                            g_Global.m_WebNetwork.ForwardDeviceNeedUpdateFile(NULL, FILE_PLAYLIST, strFileDateTime, device.GetMac(), device.GetDeviceModel());
                    }
                    if(device.NeedUpdateFile(DT_TIMER))
                    {
                            string strFileDateTime = g_Global.m_TimerScheme.GetDateTime().C_Str();

                            g_Global.m_WebNetwork.ForwardDeviceNeedUpdateFile(NULL, FILE_TIMER, strFileDateTime, device.GetMac(), device.GetDeviceModel());
                    }
                    if(device.NeedUpdateFile(DT_GROUP))
                    {
                            string strFileDateTime = g_Global.m_Groups.GetDateTime().C_Str();

                            g_Global.m_WebNetwork.ForwardDeviceNeedUpdateFile(NULL, FILE_GROUP, strFileDateTime, device.GetMac(), device.GetDeviceModel());
                    }
                }
            }
#endif
        }
    }
}

// 定时检查Web端是否需要更新文件
void CNetwork::CheckWebNeedUpdateFile()
{
        /*if(g_Global.m_Groups.GetNeedNotify())
        {
                g_Global.m_Groups.SetNeedNotify(false);
                //g_Global.m_WebNetwork.ForwardUpdateFileInfo(FILE_GROUP);
        }

        if(g_Global.m_PlayList.GetNeedNotify())
        {
                g_Global.m_PlayList.SetNeedNotify(false);
                g_Global.m_WebNetwork.ForwardUpdateFileInfo(FILE_PLAYLIST);
        }

        if(g_Global.m_TimerScheme.GetNeedNotify())
        {
                g_Global.m_TimerScheme.SetNeedNotify(false);
                //g_Global.m_WebNetwork.ForwardUpdateFileInfo(FILE_TIMER);
        }*/
}

// 定时检查采集器使用状况
void CNetwork::DeviceCheckAudioCollector(void)
{
    int nAudioCount = g_Global.m_AudioCollectors.GetSecCount();

    for (int i=0; i<nAudioCount; ++i)
    {
        CSection& audioCollector = g_Global.m_AudioCollectors.GetSection(i);

        if (audioCollector.IsOnline())
        {
            // 检测有没有分区使用该音频采集器
            BOOL isFound=FALSE;
            int channelSelect=0;
            for (int i=0;i<AUDIO_COLLECTOR_CHANNELS;i++)
            {
                CSection* pSection = g_Global.m_Sections.GetSectionBySrcID(audioCollector.m_pAudioCollector->GetSourceID()+i);
                 if(pSection)
                 {
                    isFound=TRUE;
                    channelSelect += pow(2,i);
                 }
            }
            m_CmdSend.CmdSendAudioState(audioCollector, isFound  ? TRUE : FALSE,channelSelect);
        }
    }
}


// 定时检测AUX工作状态
void CNetwork::CheckAUXWorking()
{
#if defined(Q_OS_LINUX)
    BOOL IsAuxWorking = FALSE;
    LPCSection pAudioSection = g_Global.m_AudioCollectors.GetSectionByMac(CNetwork::GetHostMac());
    if(g_Global.m_AuxPlay.IsWorking())
    {
        if(pAudioSection != NULL)
        {
            int nCount = g_Global.m_Sections.GetSecCount();

            for (int i=0; i<nCount; i++)
            {
                CSection& device = g_Global.m_Sections.GetSection(i);

                ProgramSource ProSource = device.GetProSource();
                if(CProtocol::IsAudioCollectorSrc(ProSource)
                   && ProSource == pAudioSection->m_pAudioCollector->GetSourceID())
                {
                    IsAuxWorking = TRUE;
                    break;
                }
            }

            if(!IsAuxWorking)
            {
                g_Global.m_AuxPlay.StopAUXWorking();
                NOTIFY("stop");
            }
        }
        else
        {
            g_Global.InitDefaultConfig();
            g_Global.m_AuxPlay.StopAUXWorking();
        }
    }
#endif
}

/*
// 定时检查SIP设备SIP登录状态，如果掉线则登录  zhuyg
void CNetwork::DeviceCheckSipInfo(void)
{
        for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
        {
            int nDeviceCount = g_Global.m_pAllDevices[i]->GetSecCount();

            for (int j=0; j<nDeviceCount; ++j)
            {
                CSection& device = g_Global.m_pAllDevices[i]->GetSection(j);

                if (device.IsOnline() && device.IsSipDevice())
                {
                    if(!device.m_SipInfo.HasGetLogInfo())
                    {

                    }
                }
            }
        }
}
*/

// 改变播放模式（集中模式）
void CNetwork::DeviceChangePlayMode(PlayMode playMode)
{
    // 寻呼台、移动控制设备等分控设备
    CSections *pDevices[] = {&g_Global.m_Pagers, &g_Global.m_ControlDevices};
    int nDeviceTypeCount = sizeof(pDevices)/sizeof(*pDevices);

    for (int i=0; i<nDeviceTypeCount; ++i)
    {
        int nDeviceCount = pDevices[i]->GetSecCount();

        for (int j=0; j<nDeviceCount; ++j)
        {
            CSection& device = pDevices[i]->GetSection(j);

            if (device.IsOnline())
            {
                m_CmdSend.CmdSetPlayMode(playMode, device);
            }
        }
    }
}

// 检测设备是否非空闲
bool CNetwork::DevicesCheckNotIlde(CSections* pDevices, unsigned char dt)
{
    if (pDevices == NULL)
    {
        return FALSE;
    }

    int nCount = pDevices->GetSecCount();

    for (int i=0; i<nCount; i++)
    {
        CSection& device = pDevices->GetSection(i);

        // 如果指定类型，则判断该类型需要更新时才返回TRUE
        if (dt != 0xFF && device.NeedUpdateFile((DATETIME_FILE)dt) && !device.IsIdle())
        {
            return TRUE;
        }
        // 如果不指定类型，则只要不是空闲状态，直接返回TRUE
        else if (dt == 0xFF && device.IsOnline() && !device.IsIdle())
        {
            return TRUE;
        }
    }

    return FALSE;
}

// 定时获取分区状态
void CNetwork::GetDeviceStatus()
{
    int nCount = g_Global.m_Sections.GetSecCount();

    for (int i=0; i<nCount; i++)
    {
        CSection& device = g_Global.m_Sections.GetSection(i);

        if(device.IsOnline())
        {
            g_Global.m_Network.m_CmdSend.CmdGetStatus();
        }
    }

}


// 转发分区状态信息到分控设备
void CNetwork::ForwardSectionsStatusToControlDevices(	CSection* pSection,			// pSection = NULL时，则为全部分区
                                                        CSection* pControlDevice)	// pControlDevice = NULL时，则为全部分控设备
{
    // 如果是UDP下的分布模式
    // 或者不是把所有分区信息转发到所有分控设备的命令, 并且还没转发守全部分区状态
    #if 0
    if ((NETWORK_IS_UDP && WP_IS_DISTRIBUTION) || ((pSection != NULL || pControlDevice != NULL) && !m_hasForwardAllDevicesStatus))
    {
        return;
    }
    #endif

    // 全部分控设备
    if (pControlDevice == NULL)
    {
        // 寻呼台、移动控制设备等分控设备
        CSections *pDevices[] = {&g_Global.m_Pagers, &g_Global.m_ControlDevices};
        int nDeviceTypeCount = sizeof(pDevices)/sizeof(*pDevices);

        //LOG(FORMAT("nDeviceTypeCount = %d", nDeviceTypeCount), LV_ERROR);
        for (int i=0; i<nDeviceTypeCount; ++i)
        {
            int nDeviceCount = pDevices[i]->GetSecCount();

            //LOG(FORMAT("nDeviceCount = %d", nDeviceCount), LV_ERROR);
            for (int j=0; j<nDeviceCount; ++j)
            {
                CSection& controlDevice = pDevices[i]->GetSection(j);
                // 分控设备已经获取到了分区文件信息
                if (!(controlDevice.IsOnline() && !controlDevice.NeedGetFileInfo(DT_SECTION)))
                {
                    continue;
                }
                //判断账户信息
                string device_account=controlDevice.GetUserAccount();
                LPCUserInfo pUser=g_Global.m_Users.GetUserByAccount(device_account);
                if(pUser == NULL)
                {
                    continue;
                }
                if(pSection!=NULL)  //单个分区设备的状态
                {
                    //判断此分区是否属于该用户的绑定列表内，如果不属于，那么不发送
                    if(pUser->HasFoundSectionMac(pSection->GetMac()))
                    {
                        m_CmdSend.CmdForwardStatus(controlDevice, pSection);
                    }
                }
                else            //账户内所有分区设备的状态
                {
                    vector<string> vecSectionMac;
                    for(int i=0;i<g_Global.m_Sections.GetSecCount();i++)
                    {
                        CSection& device = g_Global.m_Sections.GetSection(i);
                        vecSectionMac.push_back(device.GetMac());
                    }
                    pUser->GetValidSectionMacArray(vecSectionMac);
                    if(vecSectionMac.size() == 0)
                        continue;

                    m_CmdSend.CmdForwardStatusVary(controlDevice, vecSectionMac);
                }
            }

        }

    }
    // 指定分控设备
    else
    {
        // 分控设备已经获取到了分区文件信息
        if (!(pControlDevice->IsOnline() && !pControlDevice->NeedGetFileInfo(DT_SECTION)))
        {
            return;
        }
        //判断账户信息
        string device_account=pControlDevice->GetUserAccount();
        LPCUserInfo pUser=g_Global.m_Users.GetUserByAccount(device_account);
        if(pUser == NULL)
        {
            return;
        }
        if(pSection!=NULL)  //单个分区设备的状态
        {
            //判断此分区是否属于该用户的绑定列表内，如果不属于，那么不发送
            if(pUser->HasFoundSectionMac(pSection->GetMac()))
            {
                m_CmdSend.CmdForwardStatus(*pControlDevice, pSection);
            }
        }
        else            //账户内所有分区设备的状态
        {
            vector<string> vecSectionMac;
            for(int i=0;i<g_Global.m_Sections.GetSecCount();i++)
            {
                CSection& device = g_Global.m_Sections.GetSection(i);
                vecSectionMac.push_back(device.GetMac());
            }
            pUser->GetValidSectionMacArray(vecSectionMac);
            if(vecSectionMac.size() == 0)
                return;

            m_CmdSend.CmdForwardStatusVary(*pControlDevice, vecSectionMac);
        }
    }

}





void CNetwork::ForwardVarySectionsStatusToControlDevices(	vector<string> &vecSectionMac,			// pSection = NULL时，则为全部分区
                                                        CSection* pControlDevice)	// pControlDevice = NULL时，则为全部分控设备
{
    // 如果是UDP下的分布模式
    // 或者不是把所有分区信息转发到所有分控设备的命令, 并且还没转发守全部分区状态
    #if 0
    if ((NETWORK_IS_UDP && WP_IS_DISTRIBUTION) || ((vecSections.size() !=0 || pControlDevice != NULL) && !m_hasForwardAllDevicesStatus))
    {
        return;
    }
    #endif

    // 全部分控设备
    if (pControlDevice == NULL)
    {
        // 寻呼台、移动控制设备等分控设备
        CSections *pDevices[] = {&g_Global.m_Pagers, &g_Global.m_ControlDevices};
        int nDeviceTypeCount = sizeof(pDevices)/sizeof(*pDevices);

        //LOG(FORMAT("nDeviceTypeCount = %d", nDeviceTypeCount), LV_ERROR);
        for (int i=0; i<nDeviceTypeCount; ++i)
        {
            int nDeviceCount = pDevices[i]->GetSecCount();

            //LOG(FORMAT("nDeviceCount = %d", nDeviceCount), LV_ERROR);
            for (int j=0; j<nDeviceCount; ++j)
            {
                CSection& controlDevice = pDevices[i]->GetSection(j);
                // 分控设备已经获取到了分区文件信息
                if (!(controlDevice.IsOnline() && !controlDevice.NeedGetFileInfo(DT_SECTION)))
                {
                    continue;
                }
                //判断账户信息
                string device_account=controlDevice.GetUserAccount();
                LPCUserInfo pUser=g_Global.m_Users.GetUserByAccount(device_account);
                if(pUser == NULL)
                {
                    continue;
                }
                vector<string> vecSectionMacc=vecSectionMac;
                pUser->GetValidSectionMacArray(vecSectionMacc);
                if(vecSectionMacc.size() == 0)
                    continue;

                m_CmdSend.CmdForwardStatusVary(controlDevice, vecSectionMacc);
            
            }

        }
    }
}


void CNetwork::ForwardAllCollectorsStatusToControlDevices(	CSection* pCollector,		// pCollector = NULL时，则为全部音频采集器
                                                            CSection* pControlDevice)	// pControlDevice = NULL时，则为全部分控设备
{
    //暂时用不到，无需转发音频采集器状态
    return;
    // 如果是UDP下的分布模式
    // 或者不是把音频采集器信息转发到所有分控设备的命令, 并且还没转发守全部设备状态
    if ((NETWORK_IS_UDP && WP_IS_DISTRIBUTION) || ((pCollector != NULL || pControlDevice != NULL) && !m_hasForwardAllDevicesStatus))
    {
        return;
    }

    // 全部分控设备
    if (pControlDevice == NULL)
    {
        // 寻呼台、移动控制设备等分控设备
        CSections *pDevices[] = {&g_Global.m_Pagers, &g_Global.m_ControlDevices};
        int nDeviceTypeCount = sizeof(pDevices)/sizeof(*pDevices);

        for (int i=0; i<nDeviceTypeCount; ++i)
        {
            int nDeviceCount = pDevices[i]->GetSecCount();

            for (int j=0; j<nDeviceCount; ++j)
            {
                CSection& controlDevice = pDevices[i]->GetSection(j);

                // 分控设备已经获取到了音频采集器文件信息
                if (controlDevice.IsOnline() && !controlDevice.NeedGetFileInfo(DT_AUDIO_COLLECTOR))
                {
                    m_CmdSend.CmdForwardAudioCollector(controlDevice, pCollector);
                }
            }
        }

    }
    // 指定分控设备
    else
    {
        // 分控设备已经获取到了音频采集器文件信息
        if (pControlDevice->IsOnline() && !pControlDevice->NeedGetFileInfo(DT_AUDIO_COLLECTOR))
        {
            m_CmdSend.CmdForwardAudioCollector(*pControlDevice, pCollector);
        }
    }
}

void CNetwork::ForwardAllDevicesStatusToAllControlDevices()
{
    const	int maxCount	= 2;	// 最大次数  zhuyg  原10
    static	int count		= 0;	// 转发次数

    // 如果UDP下的分布模式，或者已经超过转发次数，则不用转发  (NETWORK_IS_UDP && WP_IS_DISTRIBUTION) ||
    if (count >= maxCount)
    {
        return;
    }

    ForwardSectionsStatusToControlDevices(NULL, NULL);
    ForwardAllCollectorsStatusToControlDevices(NULL, NULL);

#if 0
    g_Global.m_WebNetwork.ForwardSectionInfoToWeb(DEVICE_SECTION, NULL, NULL);
    g_Global.m_WebNetwork.ForwardSectionInfoToWeb(DEVICE_AUDIO_COLLECTOR, NULL, NULL);
    g_Global.m_WebNetwork.ForwardSectionInfoToWeb(DEVICE_FIRE_COLLECTOR, NULL, NULL);
#endif

    count++;

    // 一共转发三次
    if (count == maxCount)
    {
        m_hasForwardAllDevicesStatus = TRUE;
    }


}


// 设备改变音频传输方式
void CNetwork::DeviceChangeAudiocast()
{
    // 正在播放的分区设备变成空闲状态
    DevicePlayingToIdleStatus();

    int nSecCount = g_Global.m_Sections.GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        CSection& device = g_Global.m_Sections.GetSection(i);

        if (device.IsOnline()
            && device.GetAudicast() != AUDIOCAST_UNKNOWN
            && device.GetAudicast() != g_Global.m_Audiocast)
        {
            // 设置音频传输方式
            //m_CmdSend.CmdAudiocastMode(device, g_Global.m_Audiocast);
        }
    }
}

#if 0
// 单播音频数据流发送到设备
void CNetwork::StreamSourceToDevices(int            nPlayID,		// 播放ID
                                     const char*	pData,			// 数据内容
                                     unsigned short	nLen,			// 数据长度
                                     CUDPSocket* pUdpSocket)
{
    int nSecCount = g_Global.m_Sections.GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        CSection& device = g_Global.m_Sections.GetSection(i);


        if (device.IsOnline()
            && device.GetPlayID() == nPlayID)
        {
            m_CmdSend.CmdStreamSource(device, pData, nLen, pUdpSocket);
        }
    }
}
#endif

// KCP单播音频数据流发送到设备
void CNetwork::StreamKCPToDevices(int            nPlayID,		// 播放ID
                                     const char*	pData,			// 数据内容
                                     unsigned short	nLen,			// 数据长度
                                     CKCPSocket* pkcpSocket)
{
    bool existDeviceNeedSendStream=false;
    int nSecCount = g_Global.m_Sections.GetSecCount();
    for (int i=0; i<nSecCount; ++i)
    {
        CSection& device = g_Global.m_Sections.GetSection(i);

        if (device.IsOnline() && device.GetPlayID() == nPlayID && device.IsTcpMode() && device.GetIsExistLocalSong() == false)
        {
            //非寻呼音源音源才发送TCP音频流
            if( !(device.GetProSource() == PRO_PAGING ) )
            {
                existDeviceNeedSendStream=true;
                if(device.m_kcpsocket && device.m_kcpsocket->GetDesPort() && device.m_kcpsocket->GetSendValid() && !STREAM_ALLWAYS_USED_TCP)
                {
                    //printf("Send kcp...\n");
                    device.m_kcpsocket->SendData(1,pData,nLen);
                }
                else
                {
                    //printf("Send tcp...\n");
                    //printf("KCP port=0,send TCP:%d...\n",nLen);
                    char* cbuf = new char[nLen];//足够长
                    memcpy(cbuf,pData,nLen);
                    SendTcpData(cbuf, nLen, device.m_pSocketObj);
                    delete[] cbuf;
                }
            }
        }
    }
    if(existDeviceNeedSendStream)
    {
        //printf("StreamKCPToDevices:nLen=%d\n",nLen);
    }
}

void CNetwork::StreamAUXToDevices(const char*	pData,              // 数据内容
                                  unsigned short	nLen)			// 数据长度

{
    int nSecCount = g_Global.m_Sections.GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        //CSection& device = g_Global.m_Sections.GetSection(i);

    }
}



/*********************************************************************/
// 接收到KCP数据
void  CNetwork::OnRecvKcpData(const char*       pData,			// 收到的数据
                              unsigned short	   nLen,		// 数据长度
                              void*     pUserDefined,           // 自定义数据
                              const char*       szIP,			// IP地址
                              unsigned short	nPort)			// 端口
{
    //KCP数据上报处理，主要是UDP心跳包
    //如果判断数据是哪个KCP，拆包判断，前4个字节代表kcp id
    //根据KCP id找到对应的分区，如果分区不存在，则返回。如果存在，更新至kcp input，使其更新窗口
    
    //如果存在分区m_kcpPort=nPort;
    
    //printf("OnRecvKcpData:szIP=%s,port=%d,nLen=%d\n",szIP,nPort,nLen);
    IUINT32 conv;
    memcpy(&conv, pData, 4);

    //printf("OnRecvKcpData:nLen=%d\n",nLen);
    if(conv == 0)     //非KCP包，心跳包
    {
        //前4个字节为0,后四个字节为conv
        memcpy(&conv, pData+4, 4);
        //printf("OnRecvKcpData:heartBeat,conv=%ld...\n",conv);
        g_Global.m_Network.m_kcpSockets.HeartBeat(pData,nLen,szIP,nPort,conv);
    }
    else
    {
        g_Global.m_Network.m_kcpSockets.InputData(pData,nLen,szIP,nPort,conv);
    }
}



#if SUPPORT_WEB_PAGING
// 接收到KCP数据
void  CNetwork::OnRecvWebStreamData(const char*       pData,			// 收到的数据
                              unsigned short	   nLen,		// 数据长度
                              void*     pUserDefined,           // 自定义数据
                              const char*       szIP,			// IP地址
                              unsigned short	nPort)			// 端口
{
   if(nLen >= 9 && nLen<1450)
   {
       CNetwork *pNetwork		 = (CNetwork *)pUserDefined;
       unsigned short	 command = CharsToShort(pData);
       DeviceModel	devModel	 = (DeviceModel)pData[4];

       pNetwork->m_CmdHandle.HandleWebPagerCommand(pData, nLen, szIP, nPort);

       //应答，避免被当作UDP FLOOD
       //printf("OnRecvWebStreamData:len=%d,szIP=%s,nPort=%d\n",nLen,szIP,nPort);
       #if 0
       if(nLen>=100+16)
       {
         g_Global.m_Network.m_MyUdpWebPagingUnicast.SendData(pData+100,16,szIP,nPort,1);
       }
       else
       {
         g_Global.m_Network.m_MyUdpWebPagingUnicast.SendData(pData,9,szIP,nPort,1);
       }
       #endif
   }
}
#endif


/*********************************************************************/
// 接收到UDP数据
void  CNetwork::OnRecvUdpData(const char*       pData,			// 收到的数据
                              unsigned short	   nLen,		// 数据长度
                              void*     pUserDefined,           // 自定义数据
                              const char*       szIP,			// IP地址
                              unsigned short	nPort)			// 端口
{
    // 包最小为9
    //if(!g_bExitApp && pUserDefined != NULL && nLen >= 9)
    if( pUserDefined != NULL && nLen >= 9 && nLen<1450 )
    {
        CNetwork *pNetwork		 = (CNetwork *)pUserDefined;
        unsigned short	 command = CharsToShort(pData);
        DeviceModel	devModel	 = (DeviceModel)pData[4];

        pNetwork->m_CmdHandle.HandleControlCommand(pData, nLen, szIP, nPort);
    }
}

// 接收到TCP数据 tcp 保留，待修改
void CNetwork::OnTcpServerCallback(LPS_SOCKET_OBJ sockobj,	// SOCKET对象
                                   void* pUserDefined,		// 自定义数据
                                   int	 sockOper)		// socket操作
{
    CNetwork *pNetwork	= (CNetwork *)pUserDefined;

    // 收到数据
    if (sockOper == SERVER_SOCKET_RECV)
    {
        if( pUserDefined != NULL && sockobj->datalen >= 9)
        {
            USHORT	 command	= CharsToShort(sockobj->buf);
            printf("OnTcpServerCallback,command=0x%04x,ip=%s\n",command,sockobj->ip);
            //if (NETWORK_IS_TCP)	// TCP
            //{
                pNetwork->m_CmdHandle.HandleControlCommand(sockobj->buf, sockobj->datalen, sockobj->ip, 0, sockobj);
            //}

            /*
                    // 获取客户的IP与端口
                    SOCKADDR_IN	sockAddr;
                    int len = sizeof(sockAddr);
                    getpeername(sockobj->sock, (SOCKADDR*)&sockAddr, &len);
                    char* szIP	= inet_ntoa(sockAddr.sin_addr);
                    USHORT port = ntohs(sockAddr.sin_port);
                    */
        }
        // 关闭socket
        else if(sockOper == SERVER_SOCKET_COLSE)
        {
            printf("sockOper == SERVER_SOCKET_COLSE...\n");
            CSection* pDevice = pNetwork->m_CmdHandle.GetOnlineDeviceBySockObj(sockobj);

            if (pDevice != NULL)
            {
                pNetwork->DeviceOffline(*pDevice);
            }
        }
    }
}

/********************************************************************/
// 打印数据
void CNetwork::ShowDataString(const char* szBuf, int nLen)
{
    LOG(FORMAT("ShowDataString len = %d",nLen), LV_INFO);
    CMyString strTip = (""), str = ("");
    for (int i=0; i<nLen; ++i)
    {
        str.Format((char*)("%02X "), szBuf[i]&0xFF);
        strTip += str;
    }

    AddLog(strTip);
    LOG(strTip.C_Str(), LV_INFO);
}

void CNetwork::AddLog(CMyString strLog, bool bAddToList)
{
    CTime	ct = CTime::GetCurrentTimeT();
    CMyString	strTip;

    strTip.Format((char*)("【%02d:%02d:%02d】%s"), ct.GetHour(), ct.GetMinute(), ct.GetSecond(), strLog.C_Str());

    if (bAddToList)
    {
        // 回调函数 保留，待修改
        //g_Global.m_MsgHandle.OnMsgHandle(UM_PANE_TIP, (WPARAM)&strTip, 0);
    }

    if (WRITE_LOG)
    {
        g_Global.m_Log.WriteLog(strLog.C_Str());
    }

}










