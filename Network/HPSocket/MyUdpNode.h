#ifndef MYUDPNODE_H
#define MYUDPNODE_H

#include <QtCore/qglobal.h>

#if defined(Q_OS_LINUX)
#include <Network/HPSocket/HPSocket.h>


// 接收数据的回调函数
typedef void(UDPNODE_CALLBACK)(const char*		pData,			// 收到的数据
                           unsigned short	nLen,			// 数据长度
                           void*		pUserDefined,           // 自定义数据
                           const char*		szIP,			// IP地址
                           unsigned short	nPort);			// 端口

class MyUdpNode : public CUdpNodeListener
{
    public:
    CUdpNodePtr m_udpNode;
    UDPNODE_CALLBACK* pFunRecvData;
    virtual EnHandleResult OnPrepareListen(IUdpNode* pSender, SOCKET soListen);

	virtual EnHandleResult OnReceive(IUdpNode* pSender, LPCTSTR lpszRemoteAddress, USHORT usRemotePort, const BYTE* pData, int iLength);

	virtual EnHandleResult OnSend(IUdpNode* pSender, LPCTSTR lpszRemoteAddress, USHORT usRemotePort, const BYTE* pData, int iLength);

	virtual EnHandleResult OnError(IUdpNode* pSender, EnSocketOperation enOperation, int iErrorCode, LPCTSTR lpszRemoteAddress, USHORT usRemotePort, const BYTE* pBuffer, int iLength);

	virtual EnHandleResult OnShutdown(IUdpNode* pSender);

    IBOOL SendData(const void*			lpData,						// 数据
                    int			nLen,						// 数据长度
                    const char*	szHost,						// IP地址
                    unsigned short	uPort,					// 端口
                    int			nRepeatCount=1);				// 重复次数



    bool StartWorking(LPCTSTR bindAddress,int port,EnCastMode castMode,UDPNODE_CALLBACK* pFunRecvData,LPCTSTR lpszCastAddress,unsigned int workThreadCnt=0);
    void StopWorking();


    MyUdpNode();
    ~MyUdpNode();
};
#else

#include <Network/HPSocket/HPSocket4C.h>


// 接收数据的回调函数
typedef void(UDPNODE_CALLBACK)(const char*		pData,			// 收到的数据
                           unsigned short	nLen,			// 数据长度
                           void*		pUserDefined,           // 自定义数据
                           const char*		szIP,			// IP地址
                           unsigned short	nPort);			// 端口

class MyUdpNode
{
    public:

    UDPNODE_CALLBACK* pFunRecvData;

    static EnHandleResult __HP_CALL OnPrepareListen(HP_UdpNode pSender, SOCKET soListen);

    static EnHandleResult __HP_CALL OnReceive(HP_UdpNode pSender, LPCTSTR lpszRemoteAddress, USHORT usRemotePort, const BYTE* pData, int iLength);

    static EnHandleResult __HP_CALL OnSend(HP_UdpNode pSender, LPCTSTR lpszRemoteAddress, USHORT usRemotePort, const BYTE* pData, int iLength);

    static EnHandleResult __HP_CALL OnError(HP_UdpNode pSender, EnSocketOperation enOperation, int iErrorCode, LPCTSTR lpszRemoteAddress, USHORT usRemotePort, const BYTE* pBuffer, int iLength);

    static EnHandleResult __HP_CALL OnShutdown(HP_UdpNode pSender);

    bool SendData(const void*			lpData,						// 数据
                    int			nLen,						// 数据长度
                    const char*	szHost,						// IP地址
                    unsigned short	uPort,					// 端口
                    int			nRepeatCount=1);				// 重复次数


    void SetBufferPoolConfig(DWORD dwFreeBufferPoolSize,DWORD dwFreeBufferPoolHold);
    bool StartWorking(LPCTSTR bindAddress,int port,EnCastMode castMode,UDPNODE_CALLBACK* pFunRecvData,LPCTSTR lpszCastAddress,unsigned int workThreadCnt=0);
    void StopWorking();


    MyUdpNode();
    ~MyUdpNode();

public:
    HP_UdpNode m_udpNode;
    HP_UdpNodeListener m_pListener;//监听器
};

#endif






#endif // MYUDPNODE_H
