#include <stdio.h>
#include <string.h>
#include "MyUdpNode.h"
#include "stdafx.h"

#if defined(Q_OS_LINUX)
EnHandleResult MyUdpNode::OnPrepareListen(IUdpNode* pSender, SOCKET soListen)
{
    TCHAR szAddress[50];
    int iAddressLen = sizeof(szAddress) / sizeof(TCHAR);
    USHORT usPort;

    pSender->GetLocalAddress(szAddress, iAddressLen, usPort);

    return HR_OK;
}

EnHandleResult MyUdpNode::OnReceive(IUdpNode* pSender, LPCTSTR lpszRemoteAddress, USHORT usRemotePort, const BYTE* pData, int iLength)
{
    pFunRecvData((const char *)pData,iLength,&g_Global.m_Network,lpszRemoteAddress,usRemotePort);
    return HR_IGNORE;
}

EnHandleResult MyUdpNode::OnSend(IUdpNode* pSender, LPCTSTR lpszRemoteAddress, USHORT usRemotePort, const BYTE* pData, int iLength)
{
    return HR_IGNORE;
}

EnHandleResult MyUdpNode::OnError(IUdpNode* pSender, EnSocketOperation enOperation, int iErrorCode, LPCTSTR lpszRemoteAddress, USHORT usRemotePort, const BYTE* pBuffer, int iLength)
{
    printf("MyUdpNode:OnError...\n");
    return HR_IGNORE;
}

EnHandleResult MyUdpNode::OnShutdown(IUdpNode* pSender)
{
    printf("MyUdpNode:OnShutdown...\n");
    return HR_IGNORE;
}


IBOOL MyUdpNode::SendData(const void*			lpData,						// 数据
                     int			nLen,						// 数据长度
                     const char*	szHost,						// IP地址
                     unsigned short	uPort,						// 端口
                     int			nRepeatCount)				// 重复次数
{
    if( lpData == NULL || nLen>1450 )
    {
        printf("MyUdpNode::SendData error,nLen=%d\n",nLen);
        return true;
    }
	m_udpNode->Send(szHost,uPort,(BYTE *)lpData,nLen,0);
    return true;
}



bool MyUdpNode::StartWorking(LPCTSTR bindAddress,int bindPort,EnCastMode castMode,UDPNODE_CALLBACK* pFunRecvData,LPCTSTR lpszCastAddress,unsigned int workThreadCnt)
{
    m_udpNode->SetMaxDatagramSize(1450);
    //注意：此处工作线程数非常重要，WAV歌曲播放发送数据如果是多线程并发，如果传输没有使用ARQ算法，那么会导致包乱序，所以传输音频时一定要用单线程。
    //todo  转发寻呼、采集卡等音频流时也应该使用单线程,
    //最好的处理方法是使用KCP算法。
    if(workThreadCnt!=0)
        m_udpNode->SetWorkerThreadCount(workThreadCnt);
    if(this == &g_Global.m_Network.m_MyUdpKCPUnicast)
    {
        printf("kcpUnicast SetFreeBufferPoolSize,size=%d,hold=%d...\n",m_udpNode->GetFreeBufferPoolSize(),m_udpNode->GetFreeBufferPoolHold());
        m_udpNode->SetFreeBufferPoolSize(2048);
        m_udpNode->SetFreeBufferPoolHold(4096);
    }
    if (m_udpNode->Start(NULL,bindPort,castMode,lpszCastAddress))
    {
        printf("Success Start UdpNode:%d!\n",bindPort);
        this->pFunRecvData = pFunRecvData;
        return true;
    }
    else
    {
        printf("Erro Start Server:%d!\n",m_udpNode->GetLastError());
        return false;
    }
}

void MyUdpNode::StopWorking()
{
    printf("StopWorking...\n");
    m_udpNode->Stop();
}


MyUdpNode::MyUdpNode():m_udpNode(this)
{

}
MyUdpNode::~MyUdpNode()
{

}
#else


EnHandleResult __HP_CALL MyUdpNode::OnPrepareListen(HP_UdpNode pSender, SOCKET soListen)
{
    return HR_OK;
}

EnHandleResult __HP_CALL MyUdpNode::OnReceive(HP_UdpNode pSender, LPCTSTR lpszRemoteAddress, USHORT usRemotePort, const BYTE* pData, int iLength)
{
    UDPNODE_CALLBACK *FunRecv = (UDPNODE_CALLBACK *)::HP_UdpNode_GetExtra(pSender);
    if(FunRecv)
        FunRecv((const char *)pData,iLength,&g_Global.m_Network,lpszRemoteAddress,usRemotePort);
    return HR_IGNORE;
}

EnHandleResult __HP_CALL MyUdpNode::OnSend(HP_UdpNode pSender, LPCTSTR lpszRemoteAddress, USHORT usRemotePort, const BYTE* pData, int iLength)
{
    return HR_IGNORE;
}

EnHandleResult __HP_CALL MyUdpNode::OnError(HP_UdpNode pSender, EnSocketOperation enOperation, int iErrorCode, LPCTSTR lpszRemoteAddress, USHORT usRemotePort, const BYTE* pBuffer, int iLength)
{
    printf("MyUdpNode:OnError...\n");
    return HR_IGNORE;
}

EnHandleResult __HP_CALL MyUdpNode::OnShutdown(HP_UdpNode pSender)
{
    printf("MyUdpNode:OnShutdown...\n");
    return HR_IGNORE;
}


bool MyUdpNode::SendData(const void*			lpData,						// 数据
                     int			nLen,						// 数据长度
                     const char*	szHost,						// IP地址
                     unsigned short	uPort,						// 端口
                     int			nRepeatCount)				// 重复次数
{
    if( lpData == NULL || nLen>1450 )
    {
        printf("MyUdpNode::SendData error,nLen=%d\n",nLen);
        return true;
    }
    ushort  command    = CharsToShort((const char* )lpData);      // 命令
    ::HP_UdpNode_Send(m_udpNode,szHost,uPort,(BYTE *)lpData,nLen);
    //printf("send cmd 0x%04x,nLen=%d....\n",command,nLen);
    return true;
}



bool MyUdpNode::StartWorking(LPCTSTR bindAddress,int bindPort,EnCastMode castMode,UDPNODE_CALLBACK* pFunRecvData,LPCTSTR lpszCastAddress,unsigned int workThreadCnt)
{
    ::HP_UdpNode_SetMaxDatagramSize(m_udpNode,1450);
    if(workThreadCnt!=0)
        ::HP_UdpNode_SetWorkerThreadCount(m_udpNode,workThreadCnt);
    //如果第一张网卡就是有效网卡，那么绑定任意地址
    LPCTSTR bindAddr = NULL;
    if(g_Global.m_strBindIpAddress.empty())
    {
        bindAddr = g_Global.m_bTotalNetCardCnt ==1?NULL:bindAddress;
    }
    else
    {
        bindAddr = g_Global.m_szNetworkIP;
    }
    if(bindAddr)
    {
        printf("UDP Start:bindAddr=%s,bindPort=%d\n",bindAddr,bindPort);
    }
    else
    {
        printf("UDP Start:bindAddr NULL,bindPort=%d\n",bindPort);
    }
    if (::HP_UdpNode_StartWithCast(m_udpNode,bindAddr,bindPort,castMode,lpszCastAddress))
    {
        printf("Success Start UdpNode:%d!\n",bindPort);
        //printf("maxDataGramSize=%ld\n",m_udpNode->GetMaxDatagramSize());
        this->pFunRecvData = pFunRecvData;
        ::HP_UdpNode_SetExtra(m_udpNode,(PVOID)pFunRecvData);
        return true;
    }
    else
    {
        printf("Erro Start Server:%d!\n",::HP_UdpNode_GetLastError(m_udpNode));
        return false;
    }
}

void MyUdpNode::SetBufferPoolConfig(DWORD dwFreeBufferPoolSize,DWORD dwFreeBufferPoolHold)
{
    //m_udpNode->SetFreeBufferPoolSize(dwFreeBufferPoolSize);
    //m_udpNode->SetFreeBufferPoolHold(dwFreeBufferPoolHold);
}

void MyUdpNode::StopWorking()
{
    printf("UDP StopWorking...\n");
    ::HP_UdpNode_Stop(m_udpNode);
}


MyUdpNode::MyUdpNode()
{
    m_pListener = Create_HP_UdpNodeListener();
    m_udpNode = Create_HP_UdpNode(m_pListener);
    ::HP_Set_FN_UdpNode_OnReceive(m_pListener,OnReceive);
}
MyUdpNode::~MyUdpNode()
{
    // 销毁 Socket 对象
    ::Destroy_HP_UdpNode(m_udpNode);
    // 销毁监听器对象
    ::Destroy_HP_UdpNodeListener(m_pListener);
}




#endif
