#include <stdio.h>
#include <string.h>
#include "MyTCPServer.h"
#include "stdafx.h"

#define MAX_TCP_PKG_NUM 20

#if defined(Q_OS_LINUX)

typedef struct
{
    int pkg_len;
    BYTE pkg_data[TCP_PKG_MAX_LEN];
    BYTE pkg_buffer_array_data[MAX_TCP_PKG_NUM][TCP_PKG_MAX_LEN];
    int pkg_buffer_array_len[MAX_TCP_PKG_NUM];
    BYTE pkg_buffer_array_valid[MAX_TCP_PKG_NUM];
    BYTE pkg_buffer_read_pos;
    BYTE pkg_buffer_write_pos;
    char remote_ip[32];
    CONNID ConnID;
    S_SOCKET_OBJ sockobj;
}stTcpPkg;

EnHandleResult MyTCPServer::OnPrepareListen(ITcpServer* pSender, SOCKET soListen) { return HR_IGNORE;}//printf(__func__); return HR_IGNORE; }

EnHandleResult MyTCPServer::OnAccept(ITcpServer* pSender, CONNID dwConnID, UINT_PTR soClient)
{
    char remote_ip_addr[32]={0};
    int remote_ip_len=32;
    unsigned short remote_port=0;
    m_server->GetRemoteAddress(dwConnID,remote_ip_addr,remote_ip_len,remote_port);
    printf("TCP_SERVER:ConnId=%d,OnAccept:remote_ip_addr=%s,ip_len=%d,port=%d\n",dwConnID,remote_ip_addr,remote_ip_len,remote_port);
    stTcpPkg *tcpPkg=(stTcpPkg *)malloc(sizeof(stTcpPkg));
    memset(tcpPkg,0,sizeof(stTcpPkg));
    tcpPkg->pkg_len= 0;
    memset(tcpPkg->remote_ip,0,sizeof(tcpPkg->remote_ip));
    strcpy(tcpPkg->remote_ip,remote_ip_addr);
    tcpPkg->ConnID=dwConnID;
    tcpPkg->sockobj.dwConnID= tcpPkg->ConnID;
    m_server->SetConnectionExtra(dwConnID,(void *)tcpPkg);
    return HR_OK;
}

EnHandleResult MyTCPServer::OnHandShake(ITcpServer* pSender, CONNID dwConnID)
{
    //printf("TCP_SERVER:OnHandShake\n");
    return HR_OK;
}


EnHandleResult MyTCPServer::OnReceive(ITcpServer* pSender, CONNID dwConnID, const BYTE* pData, int iLength)
{
    //需要解决粘包的问题
    stTcpPkg *Pkg=NULL;
    m_server->GetConnectionExtra(dwConnID,(PVOID*)&Pkg);
    if(Pkg == NULL)
        return HR_ERROR;
    int		pos = 0;
    while(pos < iLength)
    {
        if(Pkg->pkg_len + (iLength - pos) <= TCP_PKG_HEAD_LEN) // 原有的数据+收到的数据都不够包头长度
        {
            if(Pkg->pkg_len+(iLength - pos) > MAX_BUF_LEN )     //超出包长范围，返回
            {
                Pkg->pkg_len = 0;
                return HR_OK;
            }
            memcpy(Pkg->pkg_data + Pkg->pkg_len, pData + pos, iLength - pos);
            Pkg->pkg_len += (iLength - pos);
            pos += iLength - pos;
        }
        else
        {
            if(Pkg->pkg_len < TCP_PKG_HEAD_LEN) // 原有的数据不够包头长，所以要构成包头
            {
                memcpy(Pkg->pkg_data + Pkg->pkg_len, pData + pos, TCP_PKG_HEAD_LEN-Pkg->pkg_len);
                pos += TCP_PKG_HEAD_LEN-Pkg->pkg_len;
                Pkg->pkg_len = TCP_PKG_HEAD_LEN; // 包头
            }

            //USHORT	dataLen	= CharsToShort(sockobj->buf + 6);				// 数据长度（+6位置是固定的）
            USHORT	dataLen = ((BYTE)*(Pkg->pkg_data + 6))*256 + (BYTE)*(Pkg->pkg_data + 7); // 数据长度（+6位置是固定的）
            WORD   idleLen	= dataLen - (Pkg->pkg_len - TCP_PKG_HEAD_LEN) + 1;	// 一个包剩余的长度,+1是检验位

            if(iLength - pos < idleLen) // 收到的数据部分凑不够完整的包
            {
                if(Pkg->pkg_len+(iLength - pos) > MAX_BUF_LEN )     //超出包长范围，返回
                {
                    Pkg->pkg_len = 0;
                    return HR_OK;
                }
                memcpy(Pkg->pkg_data+Pkg->pkg_len, pData + pos, iLength - pos);
                Pkg->pkg_len += iLength - pos;
                pos += iLength - pos;
            }
            else	// 可以构成完整的包
            {
                if(Pkg->pkg_len+(idleLen) > MAX_BUF_LEN )     //超出包长范围，返回
                {
                    Pkg->pkg_len = 0;
                    return HR_OK;
                }
                memcpy(Pkg->pkg_data+Pkg->pkg_len, pData + pos, idleLen);
                Pkg->pkg_len += idleLen;
                pos += idleLen;

                USHORT	 command	= CharsToShort((char *)Pkg->pkg_data);
                //printf("OnTcpServerCallback,command=0x%04x,ip=%s\n",command,Pkg->remote_ip);

                Pkg->sockobj.ip = Pkg->remote_ip;
                Pkg->sockobj.datalen = Pkg->pkg_len;
                Pkg->sockobj.buf =  (char *)Pkg->pkg_data;
                if(command == CMD_CALL_VIDEO_STREAM || command == CMD_CALLING_AUDIOSTREAM ||\
                    command == CMD_PAGING_STREAM || command == CMD_WEB_PAGING_STREAM ||\
                    command == CMD_AUDIO_COLLECTOR_STREAM_TCP || command == CMD_AUDIO_MIXER_STREAM)
                {
                    g_Global.m_Network.m_CmdHandle.HandleControlCommand((char *)Pkg->pkg_data, Pkg->pkg_len, Pkg->remote_ip, 0,&Pkg->sockobj);
                }
                else
                {
                    Pkg->pkg_buffer_array_len[Pkg->pkg_buffer_write_pos]=Pkg->sockobj.datalen;
                    memcpy(Pkg->pkg_buffer_array_data[Pkg->pkg_buffer_write_pos],Pkg->sockobj.buf,Pkg->sockobj.datalen);
                    Pkg->pkg_buffer_array_valid[Pkg->pkg_buffer_write_pos] = 1;
                    Pkg->pkg_buffer_write_pos++;
                    if(Pkg->pkg_buffer_write_pos>=MAX_TCP_PKG_NUM)
                    {
                        Pkg->pkg_buffer_write_pos=0;
                    }
                }

                //g_Global.m_Network.m_CmdHandle.HandleControlCommand((char *)Pkg->pkg_data, Pkg->pkg_len, Pkg->remote_ip, 0,&Pkg->sockobj);
                // // 构成一个包，把数据传给回调函数去处理
                // if(m_pFunRecvData != NULL)
                // {
                //     m_pFunRecvData(sockobj, m_pUserDefined, SERVER_SOCKET_RECV);
                // }

                Pkg->pkg_len = 0; // 构成一个包后，重置为0，准备下一个包
            }
        }
    }

    return HR_OK;
}
#if 0
EnHandleResult MyTCPServer::OnReceive(ITcpServer* pSender, CONNID dwConnID, int iLength)
{
    printf("TCP_SERVER:OnReceive:%d\n",iLength);
    return HR_OK;
}
#endif

EnHandleResult MyTCPServer::OnSend(ITcpServer* pSender, CONNID dwConnID, const BYTE* pData, int iLength)
{
    //printf("TCP_SERVER:OnSend:%d\n",iLength);
    return HR_OK;
}
EnHandleResult MyTCPServer::OnShutdown(ITcpServer* pSender)
{
    printf("TCP_SERVER:OnShutdown\n");
    CONNID connidList[2048]={0};
    DWORD connidCount=2048;
    m_server->GetAllConnectionIDs(connidList,connidCount);
    for(int i=0;i<connidCount;i++)
    {
        stTcpPkg *Pkg=NULL;
        m_server->GetConnectionExtra(connidList[i],(PVOID*)&Pkg);
        if( Pkg!=NULL )
        {
            free(Pkg);
        }
    }
    return HR_OK;
}

EnHandleResult MyTCPServer::OnClose(ITcpServer* pSender, CONNID dwConnID, EnSocketOperation enOperation, int iErrorCode)
{
    printf("TCP_SERVER:OnClose:%d\n",iErrorCode);
    stTcpPkg *Pkg=NULL;
    pthread_mutex_lock(&connMutex);
    m_server->GetConnectionExtra(dwConnID,(PVOID*)&Pkg);
    if(Pkg!=NULL)
    {
        CSection* pDevice =  g_Global.m_Network.m_CmdHandle.GetOnlineDeviceBySockObj(&Pkg->sockobj);
        if (pDevice != NULL && pDevice->IsOnline())
        {
            printf("DeviceOffline:ip=%s,mac=%s...\n",pDevice->GetIP(),pDevice->GetMac());
            #if SUPPORT_AUDIO_MIXER
            if(pDevice->GetDeviceModel() == MODEL_AUDIO_MIXER_DECODER || pDevice->GetDeviceModel() == MODEL_AUDIO_MIXER_ENCODER\
               || pDevice->GetDeviceModel() == MODEL_AUDIO_MIXER_DECODER_C || pDevice->GetDeviceModel() == MODEL_AUDIO_MIXER_ENCODER_C)
            {
                //先断开编码器，再断开解码器
                if(pDevice->GetDeviceModel() == MODEL_AUDIO_MIXER_DECODER || pDevice->GetDeviceModel() == MODEL_AUDIO_MIXER_DECODER_C)
                {
                    CSection* pDevice_encoder = NULL;
                    if(pDevice->GetDeviceModel() == MODEL_AUDIO_MIXER_DECODER)
                        pDevice_encoder =  g_Global.m_Network.m_CmdHandle.GetOnlineDeviceBySockObjAndModel(&Pkg->sockobj,MODEL_AUDIO_MIXER_ENCODER);
                    else if(pDevice->GetDeviceModel() == MODEL_AUDIO_MIXER_DECODER_C)
                        pDevice_encoder =  g_Global.m_Network.m_CmdHandle.GetOnlineDeviceBySockObjAndModel(&Pkg->sockobj,MODEL_AUDIO_MIXER_ENCODER_C);
                    if(pDevice_encoder)
                    {
                        g_Global.m_Network.DeviceOffline(*pDevice_encoder);
                        pDevice->m_pSocketObj = NULL;   //上面的编码器已经删除了此socket，此时不能再次删除了
                    }
                    g_Global.m_Network.DeviceOffline(*pDevice);
                }
                else if(pDevice->GetDeviceModel() == MODEL_AUDIO_MIXER_ENCODER || pDevice->GetDeviceModel() == MODEL_AUDIO_MIXER_ENCODER_C)
                {
                    g_Global.m_Network.DeviceOffline(*pDevice);
                    CSection* pDevice_decoder = NULL;
                    if(pDevice->GetDeviceModel() == MODEL_AUDIO_MIXER_ENCODER)
                        pDevice_decoder =  g_Global.m_Network.m_CmdHandle.GetOnlineDeviceBySockObjAndModel(&Pkg->sockobj,MODEL_AUDIO_MIXER_DECODER);
                    else if(pDevice->GetDeviceModel() == MODEL_AUDIO_MIXER_ENCODER_C)
                        pDevice_decoder =  g_Global.m_Network.m_CmdHandle.GetOnlineDeviceBySockObjAndModel(&Pkg->sockobj,MODEL_AUDIO_MIXER_DECODER_C);
                    if(pDevice_decoder)
                    {
                        pDevice_decoder->m_pSocketObj = NULL;   //上面的编码器已经删除了此socket，此时不能再次删除了
                        g_Global.m_Network.DeviceOffline(*pDevice_decoder);
                    }
                }
            }
            else
            {
                g_Global.m_Network.DeviceOffline(*pDevice);
            }
            #else
            g_Global.m_Network.DeviceOffline(*pDevice);
            #endif
        }

        free(Pkg);
    }
    pthread_mutex_unlock(&connMutex);
    return HR_OK;
}


bool MyTCPServer::Disconnect(CONNID dwConnID)
{
    printf("MyTCPServer:Disconnect\n");
    if(m_server->IsConnected(dwConnID))
        return m_server->Disconnect(dwConnID);
    return false;
}



void MyTCPServer::ClearSocketObj(LPS_SOCKET_OBJ SockObj)		// 清除某一个socket
{
    if(!SockObj)
        return;
    printf("MyTCPServer:ClearSocketObj\n");
    Disconnect(SockObj->dwConnID);
}


bool	MyTCPServer::SendDataToClient(	char*                      data,               // 数据
                         int                            len,                 // 数据长度
                         LPS_SOCKET_OBJ	pClientObj)	// 客户端socket对象
{
    if(pClientObj!=NULL && pClientObj->dwConnID!=0)
    {
        m_server->Send(pClientObj->dwConnID,(BYTE *)data,len);
    }
    return true;
}


bool MyTCPServer::StartWorking(LPCTSTR bindAddress,int port)
{
    if(this == &g_Global.m_Network.m_MyTcpServer)
    {
        #if 0
        #define DEFAULT_TCP_SERVER_ACCEPT_SOCKET_COUNT	300
        /* 默认对象缓存池大小 */
        #define DEFAULT_OBJECT_CACHE_POOL_SIZE	600
        /* 默认对象缓存池回收阀值 */
        #define DEFAULT_OBJECT_CACHE_POOL_HOLD	600
        /* 默认内存块缓存池大小 */
        #define DEFAULT_BUFFER_CACHE_POOL_SIZE	1024
        /* 默认内存块缓存池回收阀值 */
        #define DEFAULT_BUFFER_CACHE_POOL_HOLD	1024
        #endif

        m_server->SetMaxConnectionCount(3000);
        //不更改默认值，默认最大并发accept 300也没问题
        //m_server->SetAcceptSocketCount(2000);
        //m_server->SetFreeBufferObjPool(2048);
        //m_server->SetFreeBufferObjHold(2048);

        printf("Success Start TcpServer:%d!\n",port);
        //printf("Tcp:WorkerThreadCount=%d\n",m_server->GetWorkerThreadCount());

        StartTcpDataThread();
    }
    if (m_server->Start("0.0.0.0", port))
    {
        printf("Success Start Server!\n");
        return true;
    }
    else
    {
        printf("Erro Start Server!\n");
        return false;
    }
}

void MyTCPServer::StopWorking()
{
    printf("StopWorking...\n");
    m_server->Stop();
}


MyTCPServer::MyTCPServer():m_server(this)
{
    connMutex = PTHREAD_MUTEX_INITIALIZER;
}
MyTCPServer::~MyTCPServer()
{

}


int MyTCPServer::GetPendingDataLength(LPS_SOCKET_OBJ	pClientObj)
{
    int iPending=0;
    if(pClientObj!=NULL && pClientObj->dwConnID!=0)
    {
        if(!m_server->GetPendingDataLength(pClientObj->dwConnID, iPending))
        {
            iPending=-1;
        }
    }
    return iPending;
}


void *MyTCPServer::TcpDataThread(void *lpParam)
{
    CONNID connidList[2048]={0};
    while(g_Global.m_Network.m_MyTcpServer.m_server)
    {
        //此处使用信号量是不是更好？接收到一个包，post一次。
        DWORD connidCount=2048;
        pthread_mutex_lock(&g_Global.m_Network.m_MyTcpServer.connMutex);
        bool isOk=g_Global.m_Network.m_MyTcpServer.m_server->GetAllConnectionIDs(connidList,connidCount);
        #if 0
        if(connidCount>0 && isOk)
        {
           printf("connidCount=%d\n",connidCount);
        }
        #endif

        for(int i=0;i<connidCount;i++)
        {
            stTcpPkg *Pkg=NULL;
            if(g_Global.m_Network.m_MyTcpServer.m_server->IsConnected(connidList[i]))
            {
                if(g_Global.m_Network.m_MyTcpServer.m_server->GetConnectionExtra(connidList[i],(PVOID*)&Pkg))
                {
                    if(Pkg && Pkg->pkg_buffer_array_valid[Pkg->pkg_buffer_read_pos])
                    {
                        USHORT	 command	= CharsToShort((char *)Pkg->pkg_data);
                        //printf("TcpDataThread:command=0x%04x,ip=%s\n",command,Pkg->remote_ip);
                        g_Global.m_Network.m_CmdHandle.HandleControlCommand((char *)Pkg->pkg_buffer_array_data[Pkg->pkg_buffer_read_pos], Pkg->pkg_buffer_array_len[Pkg->pkg_buffer_read_pos], Pkg->remote_ip, 0,&Pkg->sockobj);
                        if(g_Global.m_Network.m_MyTcpServer.m_server->IsConnected(connidList[i]))
                        {
                            Pkg->pkg_buffer_array_valid[Pkg->pkg_buffer_read_pos]=0;
                            Pkg->pkg_buffer_read_pos++;
                            if(Pkg->pkg_buffer_read_pos>=MAX_TCP_PKG_NUM)
                            {
                                Pkg->pkg_buffer_read_pos=0;
                            }
                        }
                    }
                }
            }
        }
        pthread_mutex_unlock(&g_Global.m_Network.m_MyTcpServer.connMutex);
        usleep(5000);
    }
    return NULL;
}



bool MyTCPServer::StartTcpDataThread()
{
    printf("StartTcpDataThread...\n");
    pthread_t pid;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&pid, &attr, TcpDataThread, NULL);
    pthread_attr_destroy(&attr);
    return TRUE;
}



#else

pthread_mutex_t connMutex = PTHREAD_MUTEX_INITIALIZER;

typedef struct
{
    int pkg_len;
    BYTE pkg_data[TCP_PKG_MAX_LEN];
    BYTE pkg_buffer_array_data[MAX_TCP_PKG_NUM][TCP_PKG_MAX_LEN];
    int pkg_buffer_array_len[MAX_TCP_PKG_NUM];
    BYTE pkg_buffer_array_valid[MAX_TCP_PKG_NUM];
    BYTE pkg_buffer_read_pos;
    BYTE pkg_buffer_write_pos;
    char remote_ip[32];
    CONNID ConnID;
    S_SOCKET_OBJ sockobj;
}stTcpPkg;

EnHandleResult MyTCPServer::OnPrepareListen(HP_TcpServer pSender, SOCKET soListen) { return HR_IGNORE;}//printf(__func__); return HR_IGNORE; }

EnHandleResult MyTCPServer::OnAccept(HP_TcpServer pSender, CONNID dwConnID, UINT_PTR soClient)
{
    char remote_ip_addr[32]={0};
    int remote_ip_len=32;
    unsigned short remote_port=0;
    ::HP_Server_GetRemoteAddress(pSender,dwConnID,remote_ip_addr,&remote_ip_len,&remote_port);
    printf("TCP_SERVER:OnAccept:remote_ip_addr=%s,ip_len=%d,port=%d\n",remote_ip_addr,remote_ip_len,remote_port);
    stTcpPkg *tcpPkg=(stTcpPkg *)malloc(sizeof(stTcpPkg));
    memset(tcpPkg,0,sizeof(stTcpPkg));
    tcpPkg->pkg_len= 0;
    memset(tcpPkg->remote_ip,0,sizeof(tcpPkg->remote_ip));
    strcpy(tcpPkg->remote_ip,remote_ip_addr);
    tcpPkg->ConnID=dwConnID;
    tcpPkg->sockobj.dwConnID= tcpPkg->ConnID;
    ::HP_Server_SetConnectionExtra(pSender,dwConnID,(void *)tcpPkg);
    return HR_OK;
}

EnHandleResult MyTCPServer::OnHandShake(HP_TcpServer pSender, CONNID dwConnID)
{
    //printf("TCP_SERVER:OnHandShake\n");
    return HR_OK;
}


EnHandleResult MyTCPServer::OnReceive(HP_TcpServer pSender, CONNID dwConnID, const BYTE* pData, int iLength)
{
    //需要解决粘包的问题
    stTcpPkg *Pkg=NULL;
    ::HP_Server_GetConnectionExtra(pSender,dwConnID,(PVOID*)&Pkg);
    if(Pkg == NULL)
        return HR_ERROR;
    int		pos = 0;
    while(pos < iLength)
    {
        if(Pkg->pkg_len + (iLength - pos) <= TCP_PKG_HEAD_LEN) // 原有的数据+收到的数据都不够包头长度
        {
            if(Pkg->pkg_len+(iLength - pos) > MAX_BUF_LEN )     //超出包长范围，返回
            {
                Pkg->pkg_len = 0;
                return HR_OK;
            }
            memcpy(Pkg->pkg_data + Pkg->pkg_len, pData + pos, iLength - pos);
            Pkg->pkg_len += (iLength - pos);
            pos += iLength - pos;
        }
        else
        {
            if(Pkg->pkg_len < TCP_PKG_HEAD_LEN) // 原有的数据不够包头长，所以要构成包头
            {
                memcpy(Pkg->pkg_data + Pkg->pkg_len, pData + pos, TCP_PKG_HEAD_LEN-Pkg->pkg_len);
                pos += TCP_PKG_HEAD_LEN-Pkg->pkg_len;
                Pkg->pkg_len = TCP_PKG_HEAD_LEN; // 包头
            }

            //USHORT	dataLen	= CharsToShort(sockobj->buf + 6);				// 数据长度（+6位置是固定的）
            USHORT	dataLen = ((BYTE)*(Pkg->pkg_data + 6))*256 + (BYTE)*(Pkg->pkg_data + 7); // 数据长度（+6位置是固定的）
            WORD   idleLen	= dataLen - (Pkg->pkg_len - TCP_PKG_HEAD_LEN) + 1;	// 一个包剩余的长度,+1是检验位

            if(iLength - pos < idleLen) // 收到的数据部分凑不够完整的包
            {
                if(Pkg->pkg_len+(iLength - pos) > MAX_BUF_LEN )     //超出包长范围，返回
                {
                    Pkg->pkg_len = 0;
                    return HR_OK;
                }
                memcpy(Pkg->pkg_data+Pkg->pkg_len, pData + pos, iLength - pos);
                Pkg->pkg_len += iLength - pos;
                pos += iLength - pos;
            }
            else	// 可以构成完整的包
            {
                if(Pkg->pkg_len+(idleLen) > MAX_BUF_LEN )     //超出包长范围，返回
                {
                    Pkg->pkg_len = 0;
                    return HR_OK;
                }
                memcpy(Pkg->pkg_data+Pkg->pkg_len, pData + pos, idleLen);
                Pkg->pkg_len += idleLen;
                pos += idleLen;

                USHORT	 command	= CharsToShort((char *)Pkg->pkg_data);
                //printf("OnTcpServerCallback,command=0x%04x,ip=%s\n",command,Pkg->remote_ip);


                Pkg->sockobj.ip = Pkg->remote_ip;
                Pkg->sockobj.datalen = Pkg->pkg_len;
                Pkg->sockobj.buf =  (char *)Pkg->pkg_data;
                //printf("pkg_buffer_write_pos=%d,dataLen=%d\n",Pkg->pkg_buffer_write_pos,Pkg->sockobj.datalen);
                Pkg->pkg_buffer_array_len[Pkg->pkg_buffer_write_pos]=Pkg->sockobj.datalen;
                memcpy(Pkg->pkg_buffer_array_data[Pkg->pkg_buffer_write_pos],Pkg->sockobj.buf,Pkg->sockobj.datalen);
                Pkg->pkg_buffer_array_valid[Pkg->pkg_buffer_write_pos] = 1;
                Pkg->pkg_buffer_write_pos++;
                if(Pkg->pkg_buffer_write_pos>=MAX_TCP_PKG_NUM)
                {
                    Pkg->pkg_buffer_write_pos=0;
                }

                //g_Global.m_Network.m_CmdHandle.HandleControlCommand((char *)Pkg->pkg_data, Pkg->pkg_len, Pkg->remote_ip, 0,&Pkg->sockobj);
                // // 构成一个包，把数据传给回调函数去处理
                // if(m_pFunRecvData != NULL)
                // {
                //     m_pFunRecvData(sockobj, m_pUserDefined, SERVER_SOCKET_RECV);
                // }

                Pkg->pkg_len = 0; // 构成一个包后，重置为0，准备下一个包
            }
        }
    }

    return HR_OK;
}
#if 0
EnHandleResult MyTCPServer::OnReceive(ITcpServer* pSender, CONNID dwConnID, int iLength)
{
    printf("TCP_SERVER:OnReceive:%d\n",iLength);
    return HR_OK;
}
#endif

EnHandleResult MyTCPServer::OnSend(HP_TcpServer pSender, CONNID dwConnID, const BYTE* pData, int iLength)
{
    //printf("TCP_SERVER:OnSend:%d\n",iLength);
    return HR_OK;
}
EnHandleResult MyTCPServer::OnShutdown(HP_TcpServer pSender)
{
    printf("TCP_SERVER:OnShutdown\n");
    CONNID connidList[1024]={0};
    DWORD connidCount=1024;
    ::HP_Server_GetAllConnectionIDs(pSender,connidList,&connidCount);
    for(int i=0;i<connidCount;i++)
    {
        stTcpPkg *Pkg=NULL;
       ::HP_Server_GetConnectionExtra(pSender,connidList[i],(PVOID*)&Pkg);
        if( Pkg!=NULL )
        {
            free(Pkg);
        }
    }
    return HR_OK;
}

EnHandleResult MyTCPServer::OnClose(HP_TcpServer pSender, CONNID dwConnID, EnSocketOperation enOperation, int iErrorCode)
{
    printf("TCP_SERVER:OnClose\n");
    stTcpPkg *Pkg=NULL;
    pthread_mutex_lock(&connMutex);
    ::HP_Server_GetConnectionExtra(pSender,dwConnID,(PVOID*)&Pkg);
    if(Pkg!=NULL)
    {
        CSection* pDevice =  g_Global.m_Network.m_CmdHandle.GetOnlineDeviceBySockObj(&Pkg->sockobj);
        if (pDevice != NULL && pDevice->IsOnline())
        {
            printf("DeviceOffline:ip=%s,mac=%s...\n",pDevice->GetIP(),pDevice->GetMac());
            g_Global.m_Network.DeviceOffline(*pDevice);
        }

        free(Pkg);
    }
    pthread_mutex_unlock(&connMutex);
    return HR_OK;
}


bool MyTCPServer::Disconnect(CONNID dwConnID)
{
    printf("MyTCPServer:Disconnect\n");
    if(::HP_Server_IsConnected(m_server,dwConnID))
        return ::HP_Server_Disconnect(m_server,dwConnID,true);
    return false;
}



void MyTCPServer::ClearSocketObj(LPS_SOCKET_OBJ SockObj)		// 清除某一个socket
{
    if(!SockObj)
        return;
    printf("MyTCPServer:ClearSocketObj\n");
    Disconnect(SockObj->dwConnID);
}


bool	MyTCPServer::SendDataToClient(	char*                      data,               // 数据
                         int                            len,                 // 数据长度
                         LPS_SOCKET_OBJ	pClientObj)	// 客户端socket对象
{
    if(pClientObj!=NULL && pClientObj->dwConnID!=0)
    {
        ::HP_Server_Send(m_server,pClientObj->dwConnID,(BYTE *)data,len);
    }
    return true;
}



bool MyTCPServer::StartWorking(LPCTSTR bindAddress,int bindPort)
{
    //20221112开始工作前需要设置，否则报错
    ::HP_Server_SetFreeBufferObjPool(m_server,2048);
    ::HP_Server_SetFreeBufferObjHold(m_server,2048);

    //如果第一张网卡就是有效网卡，那么绑定任意地址
    LPCTSTR bindAddr = NULL;
    if(g_Global.m_strBindIpAddress.empty())
    {
        bindAddr = g_Global.m_bTotalNetCardCnt ==1?NULL:bindAddress;
    }
    else
    {
        bindAddr = g_Global.m_szNetworkIP;
    }
    if(bindAddr)
    {
        printf("TCP Start:bindAddr=%s,bindPort=%d\n",bindAddr,bindPort);
    }
    else
    {
        printf("TCP Start:bindAddr NULL,bindPort=%d\n",bindPort);
    }
    if (::HP_Server_Start(m_server,bindAddr, bindPort))
    {
        printf("Success Start TcpServer:%d!\n",bindPort);
        //printf("Tcp:WorkerThreadCount=%d\n",::HP_Server_GetWorkerThreadCount(m_server));

        StartTcpDataThread();
        return true;
    }
    else
    {
        printf("Error Start TCP port:%d,errorCode=%d\n",bindPort,::HP_Server_GetLastError(m_server));
        return false;
    }
}

void MyTCPServer::StopWorking()
{
    printf("TCP StopWorking...\n");
    ::HP_Server_Stop(m_server);
}


MyTCPServer::MyTCPServer()
{
    m_pListener = ::Create_HP_TcpServerListener();
    m_server = ::Create_HP_TcpServer(m_pListener);
    ::HP_Set_FN_Server_OnAccept(m_pListener,OnAccept);
    ::HP_Set_FN_Server_OnClose(m_pListener,OnClose);
    ::HP_Set_FN_Server_OnShutdown(m_pListener,OnShutdown);
    ::HP_Set_FN_Server_OnReceive(m_pListener,OnReceive);
}
MyTCPServer::~MyTCPServer()
{
    // 销毁 Socket 对象
    ::Destroy_HP_TcpServer(m_server);
    // 销毁监听器对象
    ::Destroy_HP_TcpServerListener(m_pListener);
}


int MyTCPServer::GetPendingDataLength(LPS_SOCKET_OBJ pClientObj)
{
    int iPending=0;
    if(pClientObj!=NULL && pClientObj->dwConnID!=0)
    {
        if(::HP_Agent_GetPendingDataLength(m_server,pClientObj->dwConnID,&iPending) == false)
        {
            iPending=-1;
        }
    }
    return iPending;
}



void *MyTCPServer::TcpDataThread(void *lpParam)
{
    CONNID connidList[1024]={0};
    HP_TcpServer tcp_server=(HP_TcpServer)lpParam;
    while(tcp_server)
    {
        //此处使用信号量是不是更好？接收到一个包，post一次。
        DWORD connidCount=1024;
        pthread_mutex_lock(&connMutex);

        bool isOk=::HP_Server_GetAllConnectionIDs(tcp_server,connidList,&connidCount);
        #if 0
        if(connidCount>0 && isOk)
        {
           printf("connidCount=%d\n",connidCount);
        }
        #endif

        for(int i=0;i<connidCount;i++)
        {
            stTcpPkg *Pkg=NULL;
            if(::HP_Server_IsConnected(tcp_server,connidList[i]))
            {
                if(::HP_Server_GetConnectionExtra(tcp_server,connidList[i],(PVOID*)&Pkg))
                {
                    if(Pkg && Pkg->pkg_buffer_array_valid[Pkg->pkg_buffer_read_pos])
                    {
                        USHORT	 command	= CharsToShort((char *)Pkg->pkg_data);
                        //printf("TcpDataThread:command=0x%04x,ip=%s\n",command,Pkg->remote_ip);
                        g_Global.m_Network.m_CmdHandle.HandleControlCommand((char *)Pkg->pkg_buffer_array_data[Pkg->pkg_buffer_read_pos], Pkg->pkg_buffer_array_len[Pkg->pkg_buffer_read_pos], Pkg->remote_ip, 0,&Pkg->sockobj);
                        if(::HP_Server_IsConnected(tcp_server,connidList[i]))
                        {
                            Pkg->pkg_buffer_array_valid[Pkg->pkg_buffer_read_pos]=0;
                            Pkg->pkg_buffer_read_pos++;
                            if(Pkg->pkg_buffer_read_pos>=MAX_TCP_PKG_NUM)
                            {
                                Pkg->pkg_buffer_read_pos=0;
                            }
                        }
                    }
                }
            }
        }
        pthread_mutex_unlock(&connMutex);
        usleep(10000);
    }
    return NULL;
}



bool MyTCPServer::StartTcpDataThread()
{
    printf("StartTcpDataThread...\n");
    pthread_t pid;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    if(m_server == g_Global.m_Network.m_MyTcpServer.m_server)
    {
        pthread_create(&pid, &attr, TcpDataThread, (void*)m_server);
    }
    pthread_attr_destroy(&attr);
    return TRUE;
}


#endif

