#ifndef MYTCPSERVER_H
#define MYTCPSERVER_H

#include <QtCore/qglobal.h>

#if defined(Q_OS_LINUX)
#include <Network/HPSocket/HPSocket.h>
#include "TcpServer.h"

#define TCP_PKG_MAX_LEN     1450
#define TCP_PKG_HEAD_LEN	8



class MyTCPServer : public CTcpServerListener
{
    public:
    CTcpServerPtr m_server;
    virtual EnHandleResult OnPrepareListen(ITcpServer* pSender, SOCKET soListen);
    virtual EnHandleResult OnAccept(ITcpServer* pSender, CONNID dwConnID, UINT_PTR soClient);
    virtual EnHandleResult OnHandShake(ITcpServer* pSender, CONNID dwConnID);
    virtual EnHandleResult OnReceive(ITcpServer* pSender, CONNID dwConnID, const BYTE* pData, int iLength);
    //virtual EnHandleResult OnReceive(ITcpServer* pSender, CONNID dwConnID, int iLength);
    virtual EnHandleResult OnSend(ITcpServer* pSender, CONNID dwConnID, const BYTE* pData, int iLength);
    virtual EnHandleResult OnShutdown(ITcpServer* pSender);
    virtual EnHandleResult OnClose(ITcpServer* pSender, CONNID dwConnID, EnSocketOperation enOperation, int iErrorCode);

    void ClearSocketObj(LPS_SOCKET_OBJ SockObj);		// 清除某一个socket
    bool SendDataToClient(	char*                      data,               // 数据
                         int                            len,                 // 数据长度
                         LPS_SOCKET_OBJ	pClientObj);	// 客户端socket对象

    bool StartWorking(LPCTSTR bindAddress,int port);
    void StopWorking();
    

    bool Disconnect(CONNID dwConnID);

    MyTCPServer();
    ~MyTCPServer();

    int GetPendingDataLength(LPS_SOCKET_OBJ	pClientObj);
    static void *TcpDataThread(void *lpParam);
    bool StartTcpDataThread();
    private:
    pthread_mutex_t    connMutex;
};

#else

#include <Network/HPSocket/HPSocket4C.h>
#include "TcpServer.h"

#define TCP_PKG_MAX_LEN     1450
#define TCP_PKG_HEAD_LEN	8



class MyTCPServer
{
public:
    static EnHandleResult __HP_CALL OnPrepareListen(HP_TcpServer pSender, SOCKET soListen);
    static EnHandleResult __HP_CALL OnAccept(HP_TcpServer pSender, CONNID dwConnID, UINT_PTR soClient);
    static EnHandleResult __HP_CALL OnHandShake(HP_TcpServer pSender, CONNID dwConnID);
    static EnHandleResult __HP_CALL OnReceive(HP_TcpServer pSender, CONNID dwConnID, const BYTE* pData, int iLength);
    static EnHandleResult __HP_CALL OnSend(HP_TcpServer pSender, CONNID dwConnID, const BYTE* pData, int iLength);
    static EnHandleResult __HP_CALL OnShutdown(HP_TcpServer pSender);
    static EnHandleResult __HP_CALL OnClose(HP_TcpServer pSender, CONNID dwConnID, EnSocketOperation enOperation, int iErrorCode);

    void ClearSocketObj(LPS_SOCKET_OBJ SockObj);		// 清除某一个socket
    bool SendDataToClient(	char*                      data,               // 数据
                         int                            len,                 // 数据长度
                         LPS_SOCKET_OBJ	pClientObj);	// 客户端socket对象

    bool StartWorking(LPCTSTR bindAddress,int bindPort);
    void StopWorking();
    

    bool Disconnect(CONNID dwConnID);

    MyTCPServer();
    ~MyTCPServer();

    int GetPendingDataLength(LPS_SOCKET_OBJ	pClientObj);
    static void *TcpDataThread(void *lpParam);
    bool StartTcpDataThread();
public:
    HP_TcpServer m_server;
    HP_TcpServerListener m_pListener;//监听器
};


#endif



#endif // MYTCPSERVER_H
