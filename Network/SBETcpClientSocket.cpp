#include "SBETcpClientSocket.h"


bool CSBETcpClientSocket::ConnectServer(	LPSTR	lpszServerIP,
                                    int		nServerPort,
                                    TCP_CLIENT_CALLBACK* pFunRecvData,
                                    void*	pUserDefined)
{
    return true;
}

// 往服务器发送数据
bool	CSBETcpClientSocket::SendData(char*	data, int len, LPC_SOCKET_OBJ pClientObj){return true;}

// 断开服务器的连接
void	CSBETcpClientSocket::DisconnectServer(void){}
