#include "SBETcpServerSocket.h"

CSBETcpServerSocket::CSBETcpServerSocket()
{

}


// 开始监听客户端连接
bool	CSBETcpServerSocket::StartListen( int	nBindPort,
                            TCP_SERVER_CALLBACK* pFunRecvData,
                            void* pUserDefined)
{

}

// 停止监听客户端连接
void	CSBETcpServerSocket::StopListen(void)
{

}

// 往指定客户端发送数据
bool	CSBETcpServerSocket::SendDataToClient(	char*                      data,               // 数据
                                        int                            len,                 // 数据长度
                                        LPS_SOCKET_OBJ	pClientObj)	// 客户端socket对象
{
    return true;
}
// 清除客户端socket（StopListen也会自动清除所有的客户端socket）
void	CleanClient(LPS_SOCKET_OBJ pClientObj)
{

}

