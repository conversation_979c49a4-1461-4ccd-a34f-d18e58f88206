#ifndef MONPROC_H
#define MONPROC_H

#include <QtCore/qglobal.h>

#include <sys/types.h>

#include <pthread.h>
#include <string.h>
#if defined(Q_OS_LINUX)
#include <sys/ipc.h>
#include <sys/msg.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <sys/un.h>
#endif
#include <stdio.h>
#include "Model/Device/Monitor.h"
#include "MonitorCommandHandle.h"




class CMonitorProcess
{
public:
    CMonitorProcess();
    ~CMonitorProcess();

    int       GetGID()    { return m_msgid;  }
    int       GetFD()     { return m_sockfd; }

/***************************************************/

public:
    /*--------------      监控启动      ----------------------*/
    // 初始化监控进程
    void    Init();

    // 处理接收到的消息队列的消息
    void    HandleRecvMessage(char* message, int nLen);

    // 发送命令到监控后端服务器
    void    SendMessageToSer(char* message, ushort uLen);

/*------------------------------------------------------------*/

    // 创建消息队列
    bool    CreateMessageQueue();

    // 创建socket
    bool    CreateSocket();

    // epoll  队列
    void    CtlEpollEvent(int fd, bool flag);

    // 开启监控接收线程，循环接收消息队列数据
    void    StartMonitorPthread();

    // 接收消息队列数据线程
    static void*   MonitorPthread(void* lparam);

public:
    int       m_sockfd;    // fd
    int       m_epollFd;   // epoll
    int       m_accFd;     // 接入Socket

private:
    int       m_msgid;    // 消息队列引用标识符

    bool      m_bValid;   // 消息队列是否有效

    CMonitorCommandHandle m_pHandle;     // 监控消息处理类

};



#endif // MONPROC_H
