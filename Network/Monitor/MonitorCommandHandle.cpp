#include "stdafx.h"
#include "MonitorCommandHandle.h"
#include "MonitorProtocol.h"
#include <string.h>


CMonitorCommandHandle::CMonitorCommandHandle()
{
    // 监控事件优先级
    m_EventPriority[EVENT_NULL] = 10;           // 空事件
    m_EventPriority[EVENT_MOVE_DETECT] = 20;    // 移动侦测
    m_EventPriority[EVENT_MOVE_FAST] = 30;      // 快速移动
    m_EventPriority[EVENT_GOODS_LEAVE] = 40;    // 物品遗留
    m_EventPriority[EVENT_WARP_INVADE] = 50;    // 越线入侵
    m_EventPriority[EVENT_AREA_INVADE] = 60;    // 区域入侵
    m_EventPriority[EVENT_PARK_DETECT] = 70;    // 停车检测
    m_EventPriority[EVENT_ROUND_DETECT] = 80;   // 徘徊检测
    m_EventPriority[EVENT_GOODS_FLIT] = 90;     // 物体搬移
}

/***************************************************/
// 处理控制命令
void CMonitorCommandHandle::HandleControlCommand(const char*	pData)		// 数据区
{
    unsigned short  command     = CharsToShort(&pData[0]);      // 命令
    unsigned char	nReserved	= (unsigned char)pData[3];		// 保留字
    unsigned short	uDataLen	= CharsToShort(&pData[4]);		// 数据长度
    unsigned char	dataPos		= 6;

    switch (command)
    {
    // 3.1	已搜索大华监控设备
    case MON_FORWARD_ACCOUNT_INFO:
    {
        HandleGetMonitorOnline(&pData[dataPos], uDataLen);
    }
        break;

    // 3.2	下发监控设备状态
    case MON_FORWARD_STATUS:
    {
        HandleGetMonitorStatus(&pData[dataPos], uDataLen);
    }
        break;

    // 3.3 SDK程序已连接上监控设备
    case MON_CONNECT_SUCCESS:
    {
        HandleMonSuccessConnect(&pData[dataPos], uDataLen);
    }
        break;
#if 0
    // 3.5	向客户端上报事件
    case MON_UNLOAD_EVENT_INFO:
    {
        HandleReportEvent(&pData[dataPos], uDataLen);
        break;
    }
#endif
    default:
        break;
    }

}

// 3.1 已搜索大华监控设备上线
void CMonitorCommandHandle::HandleGetMonitorOnline(const char *pData, int nLen)
{
    char szMac[MAX_MAC_LEN] = {0};    // 监控设备Mac地址
    char szIP[MAX_IP_LEN] = {0};      // 监控设备IP地址
    unsigned short  uPort = 0;        // 监控设备端口
    int pos = 0;

    int MacLen = pData[pos];
    memcpy(szMac, &pData[++pos], MacLen);
    pos += MacLen;
    //printf("mac : %s\n", szMac);

    int ipLen = pData[pos++];
    memcpy(szIP, &pData[pos++], ipLen);
    pos += ipLen;
    //printf("ip : %s\n", szIP);

    uPort = CharsToShort(&pData[pos]);
    //printf("uPort : %d\n", uPort);

    // 根据Mac地址获取监控类
    LPCMonitorInfo  pMonitor = g_Global.m_Monitors.GetMonitorByMac(szMac);
    char szBuf[MAX_MSGSIZE] = {0};
    int len = 0;

    if(!IsIPFormat(szIP))
    {
        NOTIFY("Monitor IP is NULL");
        return;
    }


    // 监控上线设置
    bool    bChanged = false;           // 数据是否改变
    bool    bWritedFile = false;
    if(pMonitor != NULL)
    {
        if((strcmp(pMonitor->GetIP(), szIP) != 0))
        {
            // 如果监控名称与IP相同
            if(strlen(pMonitor->GetIP()) == 0 || strcmp(pMonitor->GetIP(), pMonitor->GetName()) == 0)
            {
                pMonitor->SetName(szIP);
            }
            pMonitor->SetIP(szIP);
            bWritedFile = true;
        }

        if(pMonitor->GetPort() != uPort)
        {
#if defined(Q_OS_LINUX)
            pMonitor->SetPort(uPort);
#endif
            bChanged = true;
        }

        if (pMonitor->GetMonStatus() == MS_OFFLINE)
        {
            pMonitor->SetMonStatus(MS_NOT_LOGIN);
            bChanged = true;
        }
    }
    else
    {
        // 保存监控设备信息
        CMonitorInfo monitor("admin", "admin", szMac, szIP, szIP);
        monitor.SetIsAutoAdd(true);

#if defined(Q_OS_LINUX)
        monitor.SetPort(uPort);
#endif
        monitor.SetMonStatus(MS_NOT_LOGIN);

        for(int i=1; i<EVENT_COUNT; i++)
        {
            CMonitorEvent event((EventType)i);
            monitor.AddEvent(event);
        }
#if defined(Q_OS_LINUX)
        g_Global.m_Monitors.AddMonitorQ(monitor);
#endif
        bWritedFile = true;

        pMonitor = &monitor;    //20210113
    }

    if (bWritedFile)
    {
        g_Global.WriteXmlFile(FILE_MONITOR);
    }

    if(bWritedFile || bChanged)
    {
        CMyString strLog;
        strLog.Format("HandleGetMonitorOnline - %s status : %d", pMonitor->GetName(), pMonitor->GetMonStatus());
        g_Global.m_Network.AddLog(strLog);

        // 主机下发监控设备信息到分控设备 是否要判断 TCP/UDP 集中分布模式  ？
        g_Global.m_WebNetwork.ForwardMonitorInfo(NULL, pMonitor);
    }

    // 如果监控不是第一次登陆且账户和密码已设置，再回复监控SDK
    if(pMonitor != NULL &&
       strlen(pMonitor->GetAccount()) > 0 &&
       strlen(pMonitor->GetPassword()) > 0)
    {
        len = CMonitorProtocol::MonRespondAccountInfo(szBuf,
                                                      szMac,
                                                      pMonitor->GetAccount(),
                                                      pMonitor->GetPassword());
        // 使用消息队列回复监控后端服务器
        g_Global.m_MonProc.SendMessageToSer(szBuf, len);
    }
}

// 3.2	下发监控设备状态
void CMonitorCommandHandle::HandleGetMonitorStatus(const char *pData, int nLen)
{
    char szMac[MAX_MAC_LEN] = {0};    // 监控设备Mac地址

    int pos = 0;
    int MacLen = pData[pos++];
    memcpy(szMac, &pData[pos], MacLen);
    pos += MacLen;

    // 状态
    int nStatus = pData[pos];
    MonitorStatus ms;
    if(nStatus == 0)      ms = MS_OFFLINE;
    else if(nStatus == 1) ms = MS_NOT_LOGIN;
    else                  ms = MS_IDLE;

    /*
    CMyString strLog;
    strLog.Format("monitor status : : %d", ms);
    g_Global.m_Network.AddLog(strLog);
    */

    LPCMonitorInfo  pMonitor = g_Global.m_Monitors.GetMonitorByMac(szMac);
    if(pMonitor != NULL && pMonitor->GetMonStatus() != ms)
    {
        pMonitor->SetMonStatus(ms);

        CMyString strLog;
        strLog.Format("HandleGetMonitorStatus - %s status : %d", pMonitor->GetName(), ms);
        g_Global.m_Network.AddLog(strLog);

        strLog.Format("\n----------recv from monitor server : monitor name = %s, status = %d\n", pMonitor->GetName(), ms);
        LOG(strLog.C_Str(), LV_INFO);

        g_Global.m_WebNetwork.ForwardMonitorInfo(NULL, pMonitor);
    }
}

// 3.3	SDK程序已连接上监控设备
void CMonitorCommandHandle::HandleMonSuccessConnect(const char *pData, int nLen)
{
    printf("******  HandleMonSuccessConnect  ******\n");
    char szMac[MAX_MAC_LEN] = {0};    // 监控设备Mac地址

    int pos = 0;
    int MacLen = pData[pos];
    memcpy(szMac, &pData[++pos], MacLen);
    pos += MacLen;

    LPCMonitorInfo  pMonitor = g_Global.m_Monitors.GetMonitorByMac(szMac);
    if(pMonitor != NULL)
    {
        //pMonitor->SetOnline(true);
        pMonitor->SetMonStatus(MS_IDLE);

        g_Global.m_Network.AddLog("HandleMonSuccessConnect - ForwardMonitorInfo");

        g_Global.m_WebNetwork.ForwardMonitorInfo(NULL, pMonitor);

        NOTIFY("Monitor login success");
    }
}

// 3.4	SDK程序未连接上监控设备
void CMonitorCommandHandle::HandleMonFailConnect(const char *pData, int nLen)
{
    printf("******  HandleMonFailConnect  ******\n");
    char szMac[MAX_MAC_LEN] = {0};    // 监控设备Mac地址

    int pos = 0;
    int MacLen = pData[pos];
    memcpy(szMac, &pData[++pos], MacLen);
    pos += MacLen;
    LPCMonitorInfo  pMonitor = g_Global.m_Monitors.GetMonitorByMac(szMac);
    if(pMonitor != NULL)
    {
        //pMonitor->SetOnline(false);
        pMonitor->SetMonStatus(MS_NOT_LOGIN);

        g_Global.m_Network.AddLog("HandleMonFailConnect - ForwardMonitorInfo");

        g_Global.m_WebNetwork.ForwardMonitorInfo(NULL, pMonitor);
        NOTIFY("Monitor login failed");
    }
}


// 3.5 向客户端上报事件
void CMonitorCommandHandle::HandleReportEvent(const char *pData, int nLen)
{
    //printf("*********     HandleReportEvent        ***********\n");
    EventType eventType;                        // = EVENT_UNKNOWN;    // 事件类型
    char szDateTime[MAX_TIME_LEN] = {0};        // 时间
    char szMac[MAC_LEN] = {0};                   // Mac地址
    unsigned char uInvDirection = 0;          // 入侵方向

    int pos = 0;

    int MacLen = pData[pos];
    memcpy(szMac, &pData[++pos], MacLen);
    pos += MacLen;

    eventType = (EventType)pData[pos++];

    memcpy(szDateTime, &pData[pos], MAX_TIME_LEN);
    pos += MAX_TIME_LEN;
    //printf("time : %s\n", szDateTime);

    uInvDirection = pData[pos];
    //printf("Direction : %d\n", uInvDirection);

    // 保存事件信息
    //CMonitorEvent *pEvent = new CMonitorEvent(eventType, szDateTime, uInvDirection);
    TriggerEvent triggerEvent;
    triggerEvent.eventType = eventType;
    triggerEvent.strDateTime = szDateTime;
    triggerEvent.direction = uInvDirection;

    LPCMonitorInfo pMonitor = g_Global.m_Monitors.GetMonitorByMac(szMac);
    if(pMonitor == NULL)
    {
        LOG("pMonitor is NULL", LV_INFO);
        return;
    }

//    if(!IsDateTimeFormat(szDateTime))        // 时间格式是否正确
//    {
//        return;
//    }
    
    if(eventType>=pMonitor->GetEventCount())
    {
        printf("eventType>=pMonitor->GetEventCount()...\n");
        LOG("eventType>=pMonitor->GetEventCount()...", LV_INFO);
        return;
    }
    CMonitorEvent &event = pMonitor->GetEvent(eventType - 1);

    ctime_t eventTime = pMonitor->GetEventTime(eventType);
    CTime tCurTime = CTime::GetCurrentTimeT();
    ctime_t curTime = tCurTime.GetTime();
    //NOTIFY("curTime : %d", curTime);
    //NOTIFY("eventTime : %d", pMonitor->GetEventTime(eventType));

    ctime_t span = curTime - eventTime;
    //printf("span : %d\n", span);

    if(span > MAX_TIME_SPAN)      // 同类型事件时间间隔超过固定时间，才通知设备
    {
           // 封装协议 转发到分控设备
           //g_Global.m_WebNetwork.ForwardMonitorEvent(NULL, szMac, triggerEvent);
/*
           // 加入日志
           CMyString strLog;
           strLog.Format("Monitor:%s Trigger %s", szMac, CMonitorProtocol::GetDescriptionEvent(triggerEvent.eventType).C_Str());
           g_Global.m_Network.AddLog(strLog);


           // 播放事件音效
           CMyString strSound = "/Data/Program/Common/Music/事件触发.wav";
           CMyString strPathName = g_Global.m_strHttpRootDir + strSound;
           uint uSecCount = g_Global.m_Sections.GetSecCount();

           if (g_Global.m_SongTool.IsPathExist(CMyString(strPathName)) &&
               g_Global.m_SongTool.IsStreamFormat(strPathName))
           {
               if (WP_IS_CENTRALIZED)	// 集中模式
               {
                   UINT	uCount		= 0;
                   UINT	pSecIndexs[MAX_SECTION_COUNT_FORMAL] = {0};

                   for (UINT i=0; i<uSecCount; ++i)
                   {
                       CSection &device = g_Global.m_Sections.GetSection(i);

                       if (device.IsOnline() && device.IsIdle())
                       {
                           pSecIndexs[uCount++] = device.GetID() - 1;
                       }

                       //printf("uCount : %d\n", uCount);
                       // 设置事件未触发状态
                       //event.SetTriggering(true);
                   }

                   // 告警加入播放队列中
                   CPlayTask playTask(SOURCE_PLAY, strPathName, pSecIndexs, uCount);
                   g_Global.m_PlayQueue.PushPlayTask(playTask);

                   // 设置触发事件,事件改变时更改触发事件，事件不变时更新触发事件时间
                   pMonitor->SetTriggerEvent(triggerEvent);
           }
          }
           // 设置事件时间
           event.SetLatestTime(newTime);
*/

           if(event.IsToStartWorking(tCurTime))            // 检查时间是否在 设置时间段内
           {
               // 播放事件音效
               CMyString strSound = event.GetSoundPath().data();
               CMyString strPathName = g_Global.m_strHttpRootDir + strSound;
               UINT   uSecCount = event.GetSecCount();

               if (g_Global.m_SongTool.IsPathExist(CMyString(strPathName)) &&
                   g_Global.m_SongTool.IsStreamFormat(strPathName))
               {
                   if (WP_IS_CENTRALIZED)	// 集中模式
                   {
                       UINT	uCount		= 0;
                       UINT	pSecIndexs[MAX_SECTION_COUNT_FORMAL] = {0};

                       for (UINT i=0; i<uSecCount; ++i)
                       {
                           CSection* pSection = g_Global.m_Sections.GetSectionByMac(event.GetSecMac(i).data());

                           if (pSection != NULL && pSection->IsOnline() &&
                              ( pSection->GetProSource() != PRO_EVENT ||
                              ( pSection->GetProSource() == PRO_EVENT && m_EventPriority[eventType] > m_EventPriority[pSection->GetCurTriggerEventType()])) )   // 触发事件的优先级是否高于 分区当前事件优先级
                           {
                               //NOTIFY("monitor IP: %s", pSection->GetIP());
                               pSection->SetCurTriggerEventType(eventType);
                               pSecIndexs[uCount++] = pSection->GetID() - 1;
                           }

                           // 设置事件未触发状态
                           //event.SetTriggering(true);
                       }

                       //NOTIFY("加入播放队列");
                       // 告警加入播放队列中
                       CPlayTask playTask(SOURCE_EVENT, {strPathName}, pSecIndexs, uCount,SUPER_USER_NAME,0, -1, -1, event.GetVolume());
                       g_Global.m_PlayQueue.PushPlayTask(playTask);

                       // 设置触发事件,事件改变时更改触发事件，事件不变时更新触发事件时间
                       pMonitor->SetTriggerEvent(triggerEvent);
                   }
               }
           }
           else
           {
               printf("event out of time\n");
           }

           pMonitor->SetEventTime(eventType, tCurTime.GetTime());
    }

}


