#include "stdafx.h"
#include "MonitorProtocol.h"
#include "Global/Const.h"
#include <string.h>

unsigned char CMonitorProtocol::cmdSequence = 0;

CMonitorProtocol::CMonitorProtocol()
{

}

// 获取监控事件描述
CMyString CMonitorProtocol::GetDescriptionEvent(EventType et)
{
    CMyString strEventType = ("Unknown Event Type");

    switch(et)
    {
    case EVENT_MOVE_DETECT:
        strEventType = LANG_STR(LANG_SECTION_DIALOG, "Event Move", ("移动侦测"));
        break;

    case EVENT_WARP_INVADE:
        strEventType = LANG_STR(LANG_SECTION_DIALOG, "Event Warp", ("越线入侵"));
        break;

    case EVENT_AREA_INVADE:
        strEventType = LANG_STR(LANG_SECTION_DIALOG, "Event area", ("区域入侵"));
        break;

    case EVENT_GOODS_LEAVE:
        strEventType = LANG_STR(LANG_SECTION_DIALOG, "Event leave", ("物品遗留"));
        break;

    case EVENT_MOVE_FAST:
        strEventType = LANG_STR(LANG_SECTION_DIALOG, "Event Fast", ("快速移动"));
        break;

    case EVENT_PARK_DETECT:
        strEventType = LANG_STR(LANG_SECTION_DIALOG, "Event Park", ("停车检测"));
        break;

    case EVENT_ROUND_DETECT:
        strEventType = LANG_STR(LANG_SECTION_DIALOG, "Event Round", ("徘徊检测"));
        break;

    case EVENT_GOODS_FLIT:
        strEventType = LANG_STR(LANG_SECTION_DIALOG, "Event Flit", ("物体搬移"));
        break;

    default:
        break;
    }

    return strEventType;
}

char CMonitorProtocol::GetChecksum(const char *data, int nLen)
{
    // 数据长度为0
    if (nLen <= 0)
    {
        return 0;
    }

    char checksum = data[0];

    for (int i = 1; i < nLen; ++i)
    {
        // 异或和
        checksum = (char)(checksum ^ data[i]);
    }

    return checksum;
}


unsigned short CMonitorProtocol::ControlCommand(char *buf,
                                                unsigned short cmd,
                                                const char *data,
                                                unsigned short uDataLen)
{
    buf[0]  = cmd/256;			// 请求命令
    buf[1]  = cmd%256;
    buf[2]  = cmdSequence++;	// 包序号
    buf[3]  = 0x00;				// 保留位
    buf[4]  = uDataLen/256;		// 数据长度
    buf[5]	= uDataLen%256;

    // 数据内容为0
    if (uDataLen == 0)
    {
        buf[6] = 0x00;	// 检验码为0
    }
    else
    {
        memcpy(&buf[6], data, uDataLen);
        // 校验码
        buf[6+uDataLen] = CMonitorProtocol::GetChecksum(data, uDataLen);
    }

    // 返回整条命令长度
    return (6+uDataLen+1);
}

// 3.1	已搜索大华监控设备
unsigned short CMonitorProtocol::MonRespondAccountInfo(char *buf,
                                                                                                        const char *szMac,
                                                                                                        const char *szAccount,
                                                                                                        const char *szPassword)
{
    char data[MAX_BUF_LEN] = {0};
    int pos = 0;

    int nMacLen = strlen(szMac);
    data[pos] = nMacLen;
    memcpy(&data[++pos], szMac, nMacLen);
    pos += nMacLen;

    int nAccountLen = strlen(szAccount);
    data[pos++] = nAccountLen;

    memcpy(&data[pos], szAccount, nAccountLen);
    pos += nAccountLen;

    int nPwdLen = strlen(szPassword);
    data[pos++] = nPwdLen;

    memcpy(&data[pos], szPassword, nPwdLen);
    pos += nPwdLen;

    return CMonitorProtocol::ControlCommand(buf,
                                            MON_FORWARD_ACCOUNT_INFO,
                                            data,
                                            pos);
}

// 3.7	客户端上线向服务端（SDK）获取监控信息
unsigned short CMonitorProtocol::MonRequestMonitorInfo(char *buf)
{
    return CMonitorProtocol::ControlCommand(buf,
                                            MON_REQUEST_MONITORINFO,
                                            NULL,
                                            0);
}

// 3.8	客户端向服务端（SDK）请求断开登录监控摄像头
unsigned short CMonitorProtocol::MonRequestDisconnect(char* buf,          // 命令缓存区
                                                      LPCSTR  szMac)      // Mac地址
{
    char data[MAX_BUF_LEN] = {0};
    int pos = 0;

    int nMacLen = strlen(szMac);
    data[pos] = nMacLen;
    memcpy(&data[++pos], szMac, nMacLen);
    pos += nMacLen;

    return CMonitorProtocol::ControlCommand(buf,
                                            MON_REQUEST_DISCONNECT,
                                            data,
                                            pos);
}








