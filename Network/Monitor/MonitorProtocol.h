#ifndef MONITORPROTOCOL_H
#define MONITORPROTOCOL_H

#include "Global/CType.h"
#include "Tools/CMyString.h"

#define MAX_TIME_LEN   20
#define  MONITOR_PORT   554

// 监控服务端控制命令
typedef enum
{
    MON_FORWARD_STATUS          = 0x0010,       // 下发监控设备状态
    MON_FORWARD_ACCOUNT_INFO	= 0x0012,       // 搜索到大华监控设备,SDK请求账户信息
    MON_CONNECT_SUCCESS         = 0x0013,       // SDK程序已连接上监控设备
    MON_CONNECT_FAILED          = 0x0014,       // SDK程序未连接上监控设备
    MON_UNLOAD_EVENT_INFO       = 0x0015,       // SDK向客户端上报事件

    MON_REQUEST_MONITORINFO     = 0x0017,       // 客户端上线向服务端（SDK）获取监控信息
    MON_REQUEST_DISCONNECT      = 0x0019        // 客户端向服务端（SDK）请求断开登录监控摄像头

}MonitorControlCommand;

// 2.3 事件类型
typedef enum
{
    //EVENT_UNKNOWN     = -1,            // 未知事件
    EVENT_NULL        = 0x00,            // 空事件
    EVENT_MOVE_DETECT = 0x01,            // 移动侦测
    EVENT_WARP_INVADE,                   // 越线入侵
    EVENT_AREA_INVADE,                   // 区域入侵
    EVENT_GOODS_LEAVE,                   // 物品遗留
    EVENT_MOVE_FAST,                     // 快速移动
    EVENT_PARK_DETECT,                   // 停车检测
    EVENT_ROUND_DETECT,                  // 徘徊检测
    EVENT_GOODS_FLIT,                    // 物体搬移

    EVENT_COUNT
}EventType;


class CMonitorProtocol
{
public:
    CMonitorProtocol();

    // 获取监控事件描述
    static CMyString	GetDescriptionEvent(EventType et);

    // 获取校验码
    static char GetChecksum(const char*	data,			// 数据内容
                            int		    nLen);			// 数据长度


    // 构成控制命令
    static unsigned short	 ControlCommand(char*	buf,                // 命令缓存区
                                            unsigned short	cmd,        // 命令
                                            const char*	data,           // 数据内容
                                            unsigned short	uDataLen);	// 数据长度

    // 3.1	已搜索大华监控设备
    static unsigned short  MonRespondAccountInfo(char*  buf,             // 命令缓存区
                                                 LPCSTR szMac,           // Mac地址
                                                 LPCSTR szAccount,       // 监控设备登录账户
                                                 LPCSTR szPassword);     // 监控设备登录密码

    // 3.7	客户端上线向服务端（SDK）获取监控信息
    static  unsigned short MonRequestMonitorInfo(char*  buf);             // 命令缓存区

    // 3.8	客户端向服务端（SDK）请求断开登录监控摄像头
    static  unsigned short MonRequestDisconnect(char* buf,          // 命令缓存区
                                                LPCSTR  szMac);     // Mac地址

public:
    static unsigned char  cmdSequence;  // 命令序号
};





#endif // MONITORPROTOCOL_H


