#ifndef MONITORCOMMANDHANDLE_H
#define MONITORCOMMANDHANDLE_H

#include <iostream>
#include <map>
#include "Global/GlobalMethod.h"
#include "Lib/TcpClient.h"
#include "Network/Monitor/MonitorProtocol.h"
//#include "Network/Network.h"

class CNetwork;

class CMonitorCommandHandle
{
public:
    CMonitorCommandHandle();

    // 处理命令
    void    HandleControlCommand(const char*	pData);	 // 数据区

    // 3.1 已搜索大华监控设备上线
    void    HandleGetMonitorOnline(const char* pData,    // 负载数据
                                   int         nLen);    // 负载长度

    // 3.2	下发监控设备状态
    void    HandleGetMonitorStatus(const char* pData,    // 负载数据
                                   int         nLen);    // 负载长度

    // 3.3	SDK程序已连接上监控设备
    void    HandleMonSuccessConnect(const char* pData,    // 负载数据
                                    int         nLen);    // 负载长度

    // 3.4	SDK程序未连接上监控设备
    void    HandleMonFailConnect(const char* pData,    // 负载数据
                                 int         nLen);    // 负载长度

    // 3.5 向客户端上报事件
    void    HandleReportEvent(const char* pData,    // 负载数据
                              int         nLen);    // 负载长度

private:
        map<EventType, BYTE> m_EventPriority;           // 事件优先级

};

#endif // MONITORCOMMANDHANDLE_H
