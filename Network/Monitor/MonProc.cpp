#include "stdafx.h"
#include "MonProc.h"
#include <stdio.h>
#include <errno.h>
#include <unistd.h>


#define MAX_EVENTS_COUNT 500
#define TIMEWAIT 5

CMonitorProcess::CMonitorProcess()
{
    m_bValid = false;
    //m_strDateTime = "";

    m_epollFd = -1;
    m_accFd = -1;
    m_sockfd = -1;
}

CMonitorProcess::~CMonitorProcess()
{

}


void CMonitorProcess::Init()
{
    /*
    m_bValid = CreateMessageQueue();   // 创建消息队列

    if(m_bValid)
    {
        StartMonitorPthread();      // 开启接收线程

        char  szBuf[MAX_BUF_LEN] = {0};
        int nLen = CMonitorProtocol::MonRequestMonitorInfo(szBuf);      // 连接成功，请求数据
        SendMessageToSer(szBuf, nLen);
    }
    */
    //如果是云端版本或者没有启用监控，那么关闭监控服务
#if defined(Q_OS_LINUX)
   if( IS_SYSTEM_IN_CLOUD || !g_Global.m_bEnableMonitor)
   {
        printf("Monitor Disable!\n");
        return;
   }
#else
    return; //如果是windows版本，退出
#endif

    m_bValid = CreateSocket();

    if(m_bValid)
    {
        StartMonitorPthread();      // 开启接收线程
    }
}

// 创建消息队列
bool CMonitorProcess::CreateMessageQueue()
{
#if defined(Q_OS_LINUX)
    key_t unique_key;

    unique_key = ftok(PROJ_PATH, 0x6666);
    if(unique_key < 0)
    {
        LOG("ftok failed", LV_ERROR);
        perror("ftok");
        //return false;
    }

    if((m_msgid=msgget(unique_key, IPC_CREAT|0666)) == -1)
    {
        LOG("megget error", LV_ERROR);
        return false;
    }
#endif
    return true;
}


bool CMonitorProcess::CreateSocket()
{
#if defined(Q_OS_LINUX)
    struct sockaddr_un address;

    //unlink(SOCKET_PATH); /*删除原有server_socket对象*/
    remove(SOCKET_PATH);
    /*创建socket,AF_UNIX通信协议,SOCK_STREAM数据方式*/
    m_sockfd = socket(AF_UNIX, SOCK_STREAM, 0);
    if (-1 == m_sockfd)
    {
        perror("socket");
        exit(-1);
    }

    address.sun_family = AF_UNIX;
    strcpy(address.sun_path, SOCKET_PATH);
    int len = sizeof(address);

    /*向服务器发送连接请求*/
    //result = connect(sockfd, (struct sockaddr *)&address, len);
    if(bind(m_sockfd, (sockaddr*)&address, sizeof(address)) == -1)
    {
        perror("monitor bind socket failed!");
        close(m_sockfd);
        return false;
    }

    if(listen(m_sockfd, MAX_LISTEN_COUNT) == -1)
    {
        perror("Monitor listen socket failed!");
        close(m_sockfd);
        return false;
    }

    m_epollFd = epoll_create(MAX_EVENTS_COUNT);
    CtlEpollEvent(m_sockfd, true);

    LOG("Monitor server is run", LV_INFO);
#endif
    return true;
}

// 处理接收到的消息队列的消息
void CMonitorProcess::HandleRecvMessage(char *message, int nLen)
{
    if(nLen > 6)
    {
        m_pHandle.HandleControlCommand(message);
    }
}


// 发送命令到监控后端服务器
void CMonitorProcess::SendMessageToSer(char  *message,
                                       ushort  uLen)
{
#if defined(Q_OS_LINUX)
    if(m_bValid)
    {
     /*   msgbuf_t msg;
        msg.msgtype = CIL_SER;

        bzero(msg.msgtext, MAX_MSGSIZE);
        memcpy(msg.msgtext, message, uLen);

        if(msgsnd(m_msgid, (struct msgbuf*)&msg,uLen+1,0) == -1)
        {
            LOG("msgsnd error!", LV_ERROR);
            return;
        }
        */

        printf("SendMessageToSer\n");
        for(int i=0; i<uLen; i++)
        {
            printf("%d ", message[i]);
        }
        printf("*************************\n");
        send(m_accFd, message, uLen, 0);
    }
    else
    {
        //Init();
        CreateSocket();
    }
#endif
}


// 开启监控接收线程，循环接收消息队列数据
void CMonitorProcess::StartMonitorPthread()
{
    pthread_t pth;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&pth, &attr, MonitorPthread, (void*)this);
    pthread_attr_destroy(&attr);
}


void *CMonitorProcess::MonitorPthread(void *lparam)
{
#if defined(Q_OS_LINUX)
    CMonitorProcess* pMonProc = (CMonitorProcess*)lparam;

    //int msgid = pMonitor->GetGID();
    int  sockFd = pMonProc->GetFD();
    int nLen = 0;

        struct sockaddr_un client_addr;
        memset(&client_addr, 0,sizeof(client_addr));
        socklen_t cliLen = 0;
        int nfds = 0;
        int fd = 0;
        int bufLen = 0;
        struct epoll_event events[255];

        char szBuf[MAX_BUF_LEN] = {0};
        while(g_Global.m_bEnableMonitor)
        {
            nfds = epoll_wait(pMonProc->m_epollFd, events, MAX_EVENTS_COUNT, TIMEWAIT);
            if(nfds == -1)
            {
                printf("epoll_wait failed %s\n",strerror(errno));
                if(errno!=EINTR)
                {
                break;
                }
                continue;
            }
            //printf("nfds = %d\n", nfds);
            for(int i=0; i<nfds; i++)
            {
                if(events[i].data.fd == sockFd)
                {
                    fd = accept(sockFd, (sockaddr*)&client_addr, &cliLen);
                    if(fd > 2)
                    {
                        pMonProc->CtlEpollEvent(fd, true);    // 新的连接，加入epoll
                        char  szBuf[MAX_BUF_LEN] = {0};
                        // 发送MON_REQUEST_MONITORINFO返回MON_FORWARD_ACCOUNT_INFO（一个个返回所有的监控设备）
                        // 如果新上线的监控设备，主动返回MON_FORWARD_ACCOUNT_INFO
                        int nLen = CMonitorProtocol::MonRequestMonitorInfo(szBuf);      // 连接成功，请求数据
                        pMonProc->SendMessageToSer(szBuf, nLen);
                    }
                    else
                    {
                        printf("accept by : %d failed\n", fd);
                    }
                    continue;
                }

                if(events[i].events & EPOLLHUP)
                {
                    if((fd = events[i].data.fd) < 0)
                        continue;
                    pMonProc->CtlEpollEvent(fd, false);
                }

                else if(events[i].events & EPOLLERR)
                {
                    if((fd = events[i].data.fd) < 0)
                        continue;
                    pMonProc->CtlEpollEvent(fd, false);
                }

                if(events[i].events & EPOLLIN)
                {
                    if((fd = events[i].data.fd) < 0)
                        continue;

                    memset(szBuf,0, sizeof(szBuf));
                    if((bufLen=recv(fd, szBuf, MAX_BUF_LEN, MSG_DONTWAIT)) <= 0)
                    {
                        ERROR_LOG("monitor fd=%d read error\n", fd);
                        if( bufLen<0 &&(errno == EAGAIN||errno == EWOULDBLOCK||errno == EINTR) )
                        {
                            //正常情况，重新读取即可
                        }
                        else    //非上述情况表示接收出错，需要关闭socket
                        {
                            //关闭此socket
                            pMonProc->CtlEpollEvent(fd, false);
                        }
                        continue;
                    }

                    //printf("buf len: %d \n", bufLen);
                    pMonProc->HandleRecvMessage(szBuf, bufLen);
                 }
            }
        }
 #endif
 printf("Exit MonitorPthread...\n");
    return NULL;
}


// epoll  队列
void CMonitorProcess::CtlEpollEvent(int fd, bool flag)
{
#if defined(Q_OS_LINUX)
        struct epoll_event ev;
        ev.data.fd = fd;
        ev.events  = flag ? (EPOLLIN|EPOLLHUP|EPOLLERR) : 0;

        int nRet = epoll_ctl(m_epollFd, flag ? EPOLL_CTL_ADD : EPOLL_CTL_DEL, fd, &ev);
        if(nRet == -1)
        {
                //printf("fd : %d\n", fd);
                perror("epoll_ctl  failed : ");
                return;
        }

        if(flag)
        {
            //SetNoblock(fd);
            if(fd != m_sockfd)
            {
                printf("fd: %d join epoll loop\n", fd);
                m_accFd = fd;
            }
        }
        else
        {
            close(fd);
            printf("fd: %d quit epoll loop\n", fd);
        }
 #endif
}










