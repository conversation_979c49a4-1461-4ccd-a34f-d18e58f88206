#ifndef SERVER_SYNC_H
#define SERVER_SYNC_H

#include <QtCore/qglobal.h>
#include <string>
#include <QMutex>
#include <QMutexLocker>

#define SYNC_SERVER_TIMEOUT_THRESHOLD  8                      //约4秒，500ms轮询一次

//主备服务器状态枚举定义
enum {
    MASTER_SERVER_CONNECTED_STANDBY_SERVER                       = 0,      //主服务器已连接到备用服务器
    MASTER_SERVER_CONNECTING_STANDBY_SERVER                      = 1,      //主服务器正在连接备用服务器
    MASTER_SERVER_DISABLE_SERVER_SYNC_FUNCTION                   = 2,      //主服务器未启用主备服务器功能
    MASTER_SERVER_NOT_SPECIFY_STANDBY_SERVER                     = 3,      //主服务器未指定备用服务器
    MASTER_SERVER_SERVER_SYNC_OFFLINE                            = 4,      //主服务器指定的备用服务器离线
    MASTER_SERVER_CONNECT_STANDBY_SERVER_WAIT_FOR_AUTH           = 5,      //主服务器连接备用服务器错误(等待授权)
    MASTER_SERVER_CONNECT_STANDBY_SERVER_FAILED_BY_AUTH          = 6,      //主服务器连接备用服务器失败(授权失败，对方拒绝,后面不再尝试，除非备用服务器重新上线)
    MASTER_SERVER_CONNECT_STANDBY_SERVER_FAILED_BY_VERSION       = 7,      //主服务器连接备用服务器错误(版本号不匹配)
    MASTER_SERVER_CONNECT_STANDBY_SERVER_FAILED_BY_NETWORK       = 8,      //主服务器连接备用服务器失败(网络异常)
};

//备用服务器如何判断已被主服务器连接上了呢，通过主服务器应答的状态判断就好了。
enum {
    STANDBY_SERVER_CONNECTED_MASTER_SERVER                        = 20,      //备用服务器已被主服务器连接
    STANDBY_SERVER_NOT_CONNECTED_MASTER_SERVER                    = 21,      //备用服务器未被主服务器连接
    STANDBY_REFUSED_TO_BE_CONNECTED                               = 22,      //备用服务器拒绝被连接（常见于主服务器请求连接时的信息不匹配，与MASTER_SERVER_CONNECT_STANDBY_SERVER_FAILED_BY_AUTH对应）
    STANDBY_SERVER_CHANGED_TO_MASTER_SERVER                       = 23,      //备用服务器已切换到主服务器(未检测到主服务器)
};

//SSH免密码登录结果状态
enum {
    SSH_ACCESS_AUTH_SUCCEED                                       = 0,      //SSH登录成功
    SSH_ACCESS_CONNECT_TIMEOUT                                    = 1,      //SSH连接超时
    SSH_ACCESS_AUTH_DENIED                                        = 2,      //SSH连接被拒绝（授权异常）
};

typedef struct {
    string      m_strServerIP;                     // 服务器IP地址
    char	    m_szMac[SEC_MAC_LEN];			   // 服务器MAC
    char        m_szVersion[32];                   // 服务器版本号
    bool        m_bIsBackupServer;                 // 是否为备用服务器
    int         m_nServerStatus;                   // 服务器状态
    int         m_bServerOnline;                   // 服务器是否在线
    int         m_nServerTimeoutCnt;               // 服务器超时计数（超过SYNC_SERVER_TIMEOUT_THRESHOLD后认为服务器离线）
}st_netServer;

class CServerSync
{
public:
    CServerSync(void);
    ~CServerSync(void);

// 网络启动与停止工作
public:

    static void* ServerSyncCheckTask(void* arg);

    // 初始化
    bool  Init(void);

    // 开始lsyncd工作
    bool	StartLsyncdWorking(void);

    // 停止lsyncd工作
    void	StopLsyncdWorking(void);

    // 打开服务器同步功能
    void	EnableServerSyncFunc(bool isEnable,string backup_server_ip);

    // 读取INI配置
    void ReadIniConfig();

    // 保存INI配置
    void    SaveIniConfig();

    //更新服务器信息
    bool Update_NetServer_Info(st_netServer &m_NetServer);

    bool GetNetServerCnt() { return m_vecNetServer.size();}

    int GetOnlineNetServerCnt();            //获取总在线的服务器数量
    int GetOnlineMasterServerCnt();         //获取在线的主服务器数量
    int GetOnlineStandyByServerCnt();       //获取在线的备用服务器数量

    bool IsStandByServerOnline();           //判断备用服务器是否在线

    //判断备用服务器的版本号是否和当前主服务器的匹配
    bool IsStandByServerVersionMatch();

    //获取备用服务器的mac
    char* GetStandByServerMac();

    //判断备用服务器是否切换至主服务器模式
    bool IfBakcupServerChangeToMaster();

    //比较两个NetServer结构体是否相等
    static bool IsNetServerStructEqual(st_netServer *netServer1,st_netServer *netServer2);

    //检测SSH是否可以免密码登录(主服务器用于检测备用服务器是否准备就绪)
    static int GetSSHLoginWithoutPasswordResult(const std::string& host,const std::string& userName);
    //检测操作系统用户密码是否匹配(备用服务器用于检测主备服务器配置时用户输入的用户名、密码是否匹配)
    static bool checkSystemUserAndPassword(const std::string& username, const std::string& password);
    //获取操作系统的第一个普通用户（除root，备用服务器使用）
    static string getSystemFirstNormalUser();
    //读取root账户的sshKey公钥(用于主服务器发送给备用服务器，实现ssh免密码登录)
    static string read_rootUser_ssh_key();
    //检查SSH authorizedKeysFile，并写入指定的publicKey(备用服务器用于将主服务器发送下来的publicKey写入自己用户目录，实现主服务器的免密码登录SSH)
    static bool checkSSHKeyAndWriteNew(const std::string& username, const std::string& publicKey);

public:
    int             m_bMasterSwitch;                 //启用主开关
    int             m_nServerStatus;                 //当前服务器状态
    string          m_strBackupServerHostIp;         //备份服务器的IP
    string          m_strBackupServerUser;           //备份服务器的用户名
    string          m_strBackupServerPassword;       //备份服务器的密码（暂未用到）
    string          m_strMasterServerHostIp;         //主服务器的IP
    vector<st_netServer> m_vecNetServer;             //网络上的服务器集合

    string          m_strSSHPublicKey;               //SSH公钥

    QMutex          m_qMutex_NetServer;              //互斥锁

    QProcess *lsyncdProcess;

};


#endif