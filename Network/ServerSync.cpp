#include "stdafx.h"
#include "ServerSync.h"
#include <shadow.h>
#include <pwd.h>
#include <libssh/libssh.h>

#if SUPPORT_SERVER_SYNC

CServerSync::CServerSync()
{
    m_bMasterSwitch = 0;
    #if !IS_BACKUP_SERVER
    m_nServerStatus = MASTER_SERVER_NOT_SPECIFY_STANDBY_SERVER;
    #else
    m_nServerStatus = STANDBY_SERVER_NOT_CONNECTED_MASTER_SERVER;
    #endif

    lsyncdProcess=NULL;
}

CServerSync::~CServerSync()
{
    
}

    //比较两个NetServer结构体是否相等
bool CServerSync::IsNetServerStructEqual(st_netServer *netServer1,st_netServer *netServer2)
{
    bool isEqual=true;
    if( netServer1->m_strServerIP!=netServer2->m_strServerIP ||
        strcmp(netServer1->m_szMac,netServer2->m_szMac) ||
        strcmp(netServer1->m_szVersion,netServer2->m_szVersion) ||
        netServer1->m_bIsBackupServer!=netServer2->m_bIsBackupServer ||
        netServer1->m_nServerStatus!=netServer2->m_nServerStatus ||
        netServer1->m_bServerOnline!=netServer2->m_bServerOnline
      )
    {
        isEqual=false;
    }
    return isEqual;
}

//更新服务器信息
bool CServerSync::Update_NetServer_Info(st_netServer &m_NetServer)
{
    //首先判断mac是否存在，不存在，则直接加入，存在则更新
    QMutexLocker locker(&m_qMutex_NetServer);
    bool bFoundServer=false;
    int i=0;
    for(;i<m_vecNetServer.size();i++)
    {
        if(strcmp(m_vecNetServer[i].m_szMac,m_NetServer.m_szMac) == 0)
        {
            bFoundServer=true;
            break;
        }
    }

    //如果已经找到了服务器，更新信息即可
    if(bFoundServer)
    {
        if(!CServerSync::IsNetServerStructEqual(&m_vecNetServer[i],&m_NetServer))
        {
            printf("strServerIP:%s,szMac=%s,version=%s,isBackupServer=%d,serverStatus=%d\n",
                    m_NetServer.m_strServerIP.data(),m_NetServer.m_szMac,m_NetServer.m_szVersion,m_NetServer.m_bIsBackupServer,m_NetServer.m_nServerStatus);
            
            //如果是主服务器，判断是否是当前的备用服务器
            #if !IS_BACKUP_SERVER
            if(m_NetServer.m_bServerOnline && m_NetServer.m_bIsBackupServer && m_NetServer.m_strServerIP == m_strBackupServerHostIp)
            {
                if(m_bMasterSwitch)
                {
                    //备用服务器之前是已连接，现在突然不是了，代表可能是瞬间重启了，需要处理
                    if(m_vecNetServer[i].m_nServerStatus == STANDBY_SERVER_CONNECTED_MASTER_SERVER && m_vecNetServer[i].m_nServerStatus!=m_NetServer.m_nServerStatus)
                    {
                        m_nServerStatus = MASTER_SERVER_CONNECTING_STANDBY_SERVER;
                    }
                    //备用服务器的状态是拒绝被连接，那么当前的主服务器也应该设置对应的状态
                    if(m_NetServer.m_nServerStatus == STANDBY_REFUSED_TO_BE_CONNECTED)
                    {
                        //对方拒绝，此时不应该再试
                        m_nServerStatus = MASTER_SERVER_CONNECT_STANDBY_SERVER_FAILED_BY_AUTH;
                    }
                }
            }
            #else
            //如果是备用服务器，且主服务器的状态是已经连接到备用服务器，那么设置自身状态为已连接
            //暂时先不记录主服务器了，默认局域网内最多一台主服务器,需完善
            if(m_NetServer.m_bServerOnline && !m_NetServer.m_bIsBackupServer && m_NetServer.m_strServerIP == m_strMasterServerHostIp)
            {
                if(m_NetServer.m_nServerStatus == MASTER_SERVER_CONNECTED_STANDBY_SERVER)
                {
                    m_nServerStatus = STANDBY_SERVER_CONNECTED_MASTER_SERVER;
                }
                else if(m_vecNetServer[i].m_nServerStatus == MASTER_SERVER_CONNECTED_STANDBY_SERVER)   //如果主服务器的状态变更了，而当前备用服务器的状态是已经连接到主服务器，那么恢复状态
                {
                    if(m_nServerStatus == STANDBY_SERVER_CONNECTED_MASTER_SERVER)
                    {
                        if(m_NetServer.m_nServerStatus == MASTER_SERVER_CONNECT_STANDBY_SERVER_FAILED_BY_AUTH)
                        {
                            m_nServerStatus = STANDBY_REFUSED_TO_BE_CONNECTED;
                        }
                        else
                        {
                            m_nServerStatus = STANDBY_SERVER_NOT_CONNECTED_MASTER_SERVER;
                        }
                    }
                }
            }
            #endif
            m_vecNetServer[i]=m_NetServer;
        }
        //不管相不相等，服务器的在线状态以及超时计数都应该赋值
        m_vecNetServer[i].m_nServerTimeoutCnt = m_NetServer.m_nServerTimeoutCnt;
        m_vecNetServer[i].m_bServerOnline = m_NetServer.m_bServerOnline;
    }
    else
    {
        printf("bNotFoundServer,push...\n");
        printf("strServerIP:%s,szMac=%s,version=%s,isBackupServer=%d,serverStatus=%d\n",
        m_NetServer.m_strServerIP.data(),m_NetServer.m_szMac,m_NetServer.m_szVersion,m_NetServer.m_bIsBackupServer,m_NetServer.m_nServerStatus);
        m_vecNetServer.push_back(m_NetServer);
    }
    return true;
}

int CServerSync::GetOnlineNetServerCnt()
{
    QMutexLocker locker(&m_qMutex_NetServer);
    int i=0;
    int onlineCnt=0;
    for(;i<m_vecNetServer.size();i++)
    {
        if(m_vecNetServer[i].m_bServerOnline)
        {
            onlineCnt++;
        }
    }
    return onlineCnt;
}

int CServerSync::GetOnlineMasterServerCnt()
{
    QMutexLocker locker(&m_qMutex_NetServer);
    int i=0;
    int onlineCnt=0;
    for(;i<m_vecNetServer.size();i++)
    {
        if(m_vecNetServer[i].m_bServerOnline && !m_vecNetServer[i].m_bIsBackupServer)
        {
            onlineCnt++;
        }
    }
    return onlineCnt;
}


int CServerSync::GetOnlineStandyByServerCnt()
{
    QMutexLocker locker(&m_qMutex_NetServer);
    int i=0;
    int onlineCnt=0;
    for(;i<m_vecNetServer.size();i++)
    {
        if(m_vecNetServer[i].m_bServerOnline && m_vecNetServer[i].m_bIsBackupServer)
        {
            onlineCnt++;
        }
    }
    return onlineCnt;
}

//判断备用服务器是否在线
bool CServerSync::IsStandByServerOnline()      
{
    QMutexLocker locker(&m_qMutex_NetServer);
    int i=0;
    for(;i<m_vecNetServer.size();i++)
    {
        if(m_vecNetServer[i].m_bServerOnline && m_vecNetServer[i].m_bIsBackupServer && m_vecNetServer[i].m_strServerIP == m_strBackupServerHostIp)
        {
            return true;
        }
    }
    return false;
}


//判断备用服务器的版本号是否和当前主服务器的匹配
bool CServerSync::IsStandByServerVersionMatch()
{
    QMutexLocker locker(&m_qMutex_NetServer);
    int i=0;
    for(;i<m_vecNetServer.size();i++)
    {
        if(m_vecNetServer[i].m_bServerOnline && m_vecNetServer[i].m_bIsBackupServer && m_vecNetServer[i].m_strServerIP == m_strBackupServerHostIp)
        {
            //todo 此处版本号不需要完全匹配，可以判断>=某个版本
            if(strcmp(m_vecNetServer[i].m_szVersion,VERSION) ==0 )
            {
                return true;
            }
            break;
        }
    }
    return false;
}

//获取备用服务器的mac
char* CServerSync::GetStandByServerMac()
{
    QMutexLocker locker(&m_qMutex_NetServer);
    int i=0;
    for(;i<m_vecNetServer.size();i++)
    {
        if(m_vecNetServer[i].m_bServerOnline && m_vecNetServer[i].m_bIsBackupServer && m_vecNetServer[i].m_strServerIP == m_strBackupServerHostIp)
        {
            return m_vecNetServer[i].m_szMac;
        }
    }
    return NULL;
}

//判断备用服务器是否切换至主服务器模式
bool CServerSync::IfBakcupServerChangeToMaster()
{
    if(m_nServerStatus == STANDBY_SERVER_CHANGED_TO_MASTER_SERVER)
    {
        return true;
    }
    return false;
}


void* CServerSync::ServerSyncCheckTask(void* arg)
{
    CServerSync *serverSync = (CServerSync*)arg;
    #if !IS_BACKUP_SERVER
    //主服务器在延时前先广播信息，提前通知备用服务器
    g_Global.m_Network.m_CmdSend.CmdBoradcasHostInfo(1);
    #endif
    sleep(2);  //延时2秒再去判断
    int pre_server_status=serverSync->m_nServerStatus;
    #if IS_BACKUP_SERVER
    int pre_onlineMasterServer_cnt=serverSync->GetOnlineMasterServerCnt();
    int standByServer_change_to_master_timeout_cnt=0;   //计时器，以便在备用服务器在检测到没有主服务器在线时延时替代主服务器
    //备用服务器，判断sshd进程是否已经打开，没有打开的话，先启用服务，再启动服务
    if(!IsProcessRun((char*)"sshd"))
    {
        printf("The sshd process is not running, ready to start the service!\n");
        system("systemctl enable ssh");
        system("systemctl start ssh"); 
    }
    #else
    int lsyncd_timeout_cnt=0;   //主服务器已连接备用服务器，lsyncd的超时计数
    #endif
    while(1)
    {
        //定时发送本服务器信息
        g_Global.m_Network.m_CmdSend.CmdBoradcasHostInfo(1);
        
        QMutexLocker locker(&serverSync->m_qMutex_NetServer);
        for(int i=0;i<serverSync->m_vecNetServer.size();i++)
        {
            st_netServer &netServer=serverSync->m_vecNetServer[i];
            if(netServer.m_bServerOnline)
            {
                netServer.m_nServerTimeoutCnt++;
                if(netServer.m_nServerTimeoutCnt >= SYNC_SERVER_TIMEOUT_THRESHOLD)
                {
                    netServer.m_bServerOnline = false;
                    netServer.m_nServerTimeoutCnt = 0;
                }
            }
        }
        locker.unlock();

        if(serverSync->m_nServerStatus == MASTER_SERVER_DISABLE_SERVER_SYNC_FUNCTION)
        {
            //当前服务器状态变化，通知WEB(管理员账户)
            if(pre_server_status!=serverSync->m_nServerStatus)
            {
                pre_server_status = serverSync->m_nServerStatus;
                g_Global.m_WebNetwork.ForwardBackupServerInfoToWeb(NULL);
            }
            usleep(500000);
            continue;
        }

#if IS_BACKUP_SERVER
        //上一次检测到有主服务器，现在一台都没有了，需要停止应用，然后重新启动，以便替代主服务器的功能
        if(pre_onlineMasterServer_cnt>0)
        {
            if(serverSync->GetOnlineMasterServerCnt() == 0)
            {
                printf("OnlineMasterServerCnt=0,need restart to replace master server!\n");
                pre_onlineMasterServer_cnt=0;
                //先通知WEB,再退出应用
                g_Global.m_WebNetwork.RequestUserReLogin("", RG_AUTH_CHANGER);
                usleep(400000);
                exit(0);
            }
        }
        //上一次没有检测到主服务器，现在有了，且当前代替了主服务器，那么需要停止应用，然后重新启动，以便停止备用服务器的工作。
        if(pre_onlineMasterServer_cnt == 0)   //原来在线服务器数量为0
        {
             //现在，有主服务器上线了,且原来服务器的状态是已经接管到主服务器，那么需要退出
            if(serverSync->GetOnlineMasterServerCnt()>0 && serverSync->m_nServerStatus == STANDBY_SERVER_CHANGED_TO_MASTER_SERVER)
            {
                printf("real master server is online,need restart to standby!\n");
                //先通知WEB,再退出应用
                g_Global.m_WebNetwork.RequestUserReLogin("", RG_AUTH_CHANGER);
                usleep(400000);
                exit(0);
            }
        }

        pre_onlineMasterServer_cnt=serverSync->GetOnlineMasterServerCnt();

        //如果当前在线主服务器数量为0
        if(serverSync->GetOnlineMasterServerCnt() == 0)
        {
            if(serverSync->m_nServerStatus != STANDBY_SERVER_CHANGED_TO_MASTER_SERVER)
            {
                standByServer_change_to_master_timeout_cnt++;
                if(standByServer_change_to_master_timeout_cnt >= 4)     //2秒还没有收到
                {
                    standByServer_change_to_master_timeout_cnt = 0;
                    serverSync->m_nServerStatus = STANDBY_SERVER_CHANGED_TO_MASTER_SERVER;
                    
                    static bool bFirstTakeOverMasterServer=false;
                    //第一次接管主服务器，需要启动TCP服务
                    if(!bFirstTakeOverMasterServer)
                    {
                        bFirstTakeOverMasterServer=true;
                        g_Global.m_Network.TcpStartWorking();
                    }
                }
            }
            else
            {
                standByServer_change_to_master_timeout_cnt = 0;
            }
        }
        else
        {
            standByServer_change_to_master_timeout_cnt = 0;
        }
#else

         //判断对应IP的备用服务器是否已经在线
        if(!serverSync->IsStandByServerOnline())
        {
            if(serverSync->m_nServerStatus == MASTER_SERVER_CONNECTED_STANDBY_SERVER)
            {
                //如果之前是连接状态，那么需要断开lsyncd+ssh，待处理
                serverSync->StopLsyncdWorking();
            }
            if(serverSync->m_nServerStatus!=MASTER_SERVER_NOT_SPECIFY_STANDBY_SERVER)
            {
                serverSync->m_nServerStatus = MASTER_SERVER_SERVER_SYNC_OFFLINE;
            }
        }
        else
        {
            if(serverSync->m_nServerStatus == MASTER_SERVER_SERVER_SYNC_OFFLINE)
            {
                serverSync->m_nServerStatus = MASTER_SERVER_CONNECTING_STANDBY_SERVER;
            }
        }
        //如果主服务器目前的状态是正在连接或者连接超时，那么确认
        if(serverSync->m_nServerStatus == MASTER_SERVER_CONNECTING_STANDBY_SERVER || serverSync->m_nServerStatus == MASTER_SERVER_CONNECT_STANDBY_SERVER_FAILED_BY_NETWORK
            || serverSync->m_nServerStatus == MASTER_SERVER_CONNECT_STANDBY_SERVER_FAILED_BY_VERSION)
        {
            //判断版本号是否一致，不一致不连接,判断
            if(!serverSync->IsStandByServerVersionMatch())
            {
                serverSync->m_nServerStatus = MASTER_SERVER_CONNECT_STANDBY_SERVER_FAILED_BY_VERSION;
            }
            else
            {
                //进行连接尝试
                //20230809 为了让备用服务器能够接知道主服务器的信息（主要是mac），不能直接连接，需要再次发送私有协议确认
                //int ssh_login_result=CServerSync::GetSSHLoginWithoutPasswordResult(serverSync->m_strBackupServerHostIp.c_str(),serverSync->m_strBackupServerUser.c_str());
                int ssh_login_result=CServerSync::GetSSHLoginWithoutPasswordResult(serverSync->m_strBackupServerHostIp.c_str(),"");
                if(ssh_login_result == SSH_ACCESS_CONNECT_TIMEOUT)      //连接超时
                {
                    serverSync->m_nServerStatus = MASTER_SERVER_CONNECT_STANDBY_SERVER_FAILED_BY_NETWORK;
                }
                else if(ssh_login_result == SSH_ACCESS_AUTH_DENIED)     //授权失败
                {
                    serverSync->m_nServerStatus = MASTER_SERVER_CONNECT_STANDBY_SERVER_WAIT_FOR_AUTH;
                }
                else if(ssh_login_result == SSH_ACCESS_AUTH_SUCCEED)    //授权成功,20230809改变后永远不能进入此处了，因为用户名传入错误
                {
                    serverSync->m_nServerStatus = MASTER_SERVER_CONNECTED_STANDBY_SERVER;
                }
            }
        }
        else if(serverSync->m_nServerStatus == MASTER_SERVER_CONNECT_STANDBY_SERVER_WAIT_FOR_AUTH)
        {
            //本机root账户的公钥然后发送给对方服务器，请求备用服务器确认并把公钥加入，备用服务器顺便把用户名回传。
            g_Global.m_Network.m_CmdSend.CmdConnectStandByServer(serverSync->m_strBackupServerHostIp.data(),serverSync->GetStandByServerMac(),serverSync->m_strSSHPublicKey.data(),1);
        }

        //已连接上备用服务器，需要周期性检查lsyncd进程是否存在，如果不存在，需要重新启动
        if(serverSync->m_nServerStatus == MASTER_SERVER_CONNECTED_STANDBY_SERVER)
        {
            if(!IsProcessRun((char*)"lsyncd"))
            {
                lsyncd_timeout_cnt++;
                if(lsyncd_timeout_cnt>=6)   //连续3秒未找到lsyncd进程，重新启动
                {
                    lsyncd_timeout_cnt=0;
                    //启动lsyncd进行同步，记得先设置QT环境变量
                    serverSync->StartLsyncdWorking();
                }
            }
            else
            {
                lsyncd_timeout_cnt=0;
            }
        }
        else
        {
            lsyncd_timeout_cnt=0;
        }
#endif

        //todo 超时检测，比如说等待授权后，多久没有变化，要变成授权失败，先不处理，因为基本不存在此情况
        
        //当前服务器状态变化，通知WEB（管理员账户）
        if(pre_server_status!=serverSync->m_nServerStatus)
        {
            printf("OnlineMasterServerCnt=%d,OnlineStandyByServerCnt=%d,m_nServerStatus=%d\n",serverSync->GetOnlineMasterServerCnt(),
                    serverSync->GetOnlineStandyByServerCnt(),serverSync->m_nServerStatus);
            pre_server_status = serverSync->m_nServerStatus;
            g_Global.m_WebNetwork.ForwardBackupServerInfoToWeb(NULL);
            //加入日志
            CMyString strLogContents;
            strLogContents.Format("ServerSync status:%d", serverSync->m_nServerStatus);
            g_Global.m_logTable.InsertLog(	SUPER_USER_NAME,
                                        SUPER_USER_NAME,
                                        LT_ADVANCED_LOG,
                                        strLogContents);
        }

        usleep(500000);
    }
}


bool CServerSync::Init()
{
    //读取INI配置文件
    ReadIniConfig();

    //获取本机root用户SSH公钥
    m_strSSHPublicKey=CServerSync::read_rootUser_ssh_key();

#if !IS_BACKUP_SERVER
    //变态变化
    if(m_bMasterSwitch == 0)
    {
        m_nServerStatus = MASTER_SERVER_DISABLE_SERVER_SYNC_FUNCTION;
    }
    else if(m_strBackupServerHostIp.size() == 0 || !IsIPFormat(m_strBackupServerHostIp.data()))
    {
        m_nServerStatus = MASTER_SERVER_NOT_SPECIFY_STANDBY_SERVER;
    }
    else
    {
        m_nServerStatus = MASTER_SERVER_SERVER_SYNC_OFFLINE;        //一开始先将备用服务器置为离线
    }
#else
    m_nServerStatus = STANDBY_SERVER_NOT_CONNECTED_MASTER_SERVER;
#endif


    //创建检测线程
    pthread_t pid;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);

    pthread_create(&pid, &attr, ServerSyncCheckTask, this);
    pthread_attr_destroy(&attr);

    return true;
}

bool CServerSync::StartLsyncdWorking()
{
    printf("StartLsyncdWorking...\n");
    //判断lsyncd进程是否存在，存在则杀掉进程
    StopLsyncdWorking();

    if(lsyncdProcess==NULL)
    {
        lsyncdProcess = new QProcess; //lsyncd进程
    }
    else
    {
        printf("lsyncdProcess->state()=%d\n",lsyncdProcess->state());
        if (lsyncdProcess->state() != QProcess::NotRunning) 
        {
            //不能用kill，否则再次启动会提示QProcess::start: Process is already running
            lsyncdProcess->close();
        }
    }

    // 设置QProcess的输出策略为合并输出,可以看到qstring的打印
    //lsyncdProcess->setProcessChannelMode(QProcess::MergedChannels);
    //可以选择将QProcess的进程模式设置为QProcess::ForwardedChannels，这将使得子进程在父进程关闭时也随之关闭。
    lsyncdProcess->setProcessChannelMode(QProcess::ForwardedChannels);

    //设置工作路径
    CMyString workPath = g_Global.m_strRunDirPath+HTTP_FOLDER_SEPARATOR+"Sync";
    lsyncdProcess->setWorkingDirectory(workPath.Data());

    //启动lsyncd进行同步，记得先设置QT环境变量
    string lsyncd_host;
    lsyncd_host="LSYNCD_HOST="+m_strBackupServerUser+"@"+m_strBackupServerHostIp;
    lsyncdProcess->setEnvironment(QStringList() << lsyncd_host.data());
    CMyString lsyncd_path="lsyncd";
    CMyString lsyncd_conf_path="lsyncd.conf";
    QStringList params;
    params<<lsyncd_conf_path.Data();
    lsyncdProcess->start(lsyncd_path.Data(),params);
    #if 0
    lsyncdProcess->waitForFinished(-1);
    // 读取进程输出
    QByteArray output = lsyncdProcess->readAll();
    // 打印输出消息
    qDebug() << "Process Output: " << output;
    delete(lsyncdProcess);
    #endif
    printf("lsyncdProcess finish...\n");
    return true;
}

void CServerSync::StopLsyncdWorking()
{
    StopProcess((char*)"lsyncd");
    StopProcess((char*)"rsync");
}

// 打开服务器同步功能
void	CServerSync::EnableServerSyncFunc(bool isEnable,string backup_server_ip)
{
    if(isEnable == 0)
    {
        m_nServerStatus = MASTER_SERVER_DISABLE_SERVER_SYNC_FUNCTION;
        m_bMasterSwitch = isEnable;
    }
    else
    {
        if(!IsIPFormat(backup_server_ip.data()))
        {
            m_nServerStatus = MASTER_SERVER_NOT_SPECIFY_STANDBY_SERVER;
        }
        else
        {
            m_strBackupServerHostIp = backup_server_ip;                 //记录IP
            m_bMasterSwitch = isEnable;
            m_nServerStatus = MASTER_SERVER_SERVER_SYNC_OFFLINE;        //一开始先将备用服务器置为离线
        }
    }
    StopLsyncdWorking();
    SaveIniConfig();
}


void CServerSync::ReadIniConfig()
{
    g_Global.m_IniConfig.GetValue(CONFIG_FILE_SECTION_SERVER_SYNC, CONFIG_FILE_SECTION_SERVER_SYNC_SWITCH, m_bMasterSwitch);
    CMyString strBackupServerHostIP;
    g_Global.m_IniConfig.GetValue(CONFIG_FILE_SECTION_SERVER_SYNC, CONFIG_FILE_SECTION_SERVER_SYNC_HOST, strBackupServerHostIP);
    CMyString strBackupServerUser;
    g_Global.m_IniConfig.GetValue(CONFIG_FILE_SECTION_SERVER_SYNC, CONFIG_FILE_SECTION_SERVER_SYNC_USER, strBackupServerUser);

    m_strBackupServerHostIp=strBackupServerHostIP.Data();
    m_strBackupServerUser=strBackupServerUser.Data();

    printf("ServerSync_ReadIniConfig:HostIp=%s,user=%s\n",m_strBackupServerHostIp.data(),m_strBackupServerUser.data());
}

void CServerSync::SaveIniConfig()
{
    g_Global.m_IniConfig.SetValue(CONFIG_FILE_SECTION_SERVER_SYNC, CONFIG_FILE_SECTION_SERVER_SYNC_SWITCH, m_bMasterSwitch);
    g_Global.m_IniConfig.SetValue(CONFIG_FILE_SECTION_SERVER_SYNC, CONFIG_FILE_SECTION_SERVER_SYNC_HOST, m_strBackupServerHostIp.data());
    g_Global.m_IniConfig.SetValue(CONFIG_FILE_SECTION_SERVER_SYNC, CONFIG_FILE_SECTION_SERVER_SYNC_USER, m_strBackupServerUser.data());
    g_Global.m_IniConfig.Write();
}


//检测SSH是否可以免密码登录(主服务器用于检测备用服务器是否准备就绪)
int CServerSync::GetSSHLoginWithoutPasswordResult(const std::string& host,const std::string& userName)
{
    ssh_session session = ssh_new();
    if (session == nullptr) {
        return SSH_ACCESS_CONNECT_TIMEOUT;
    }

    int port = 22;

    struct timeval timeout;
    timeout.tv_sec = 1;     //超时时间1秒
    timeout.tv_usec = 0;
    
    int rc = ssh_options_set(session, SSH_OPTIONS_TIMEOUT, &timeout);
    if (rc != SSH_OK) {
        printf("ssh_options_set failed 1\n");
        ssh_free(session);
        return SSH_ACCESS_CONNECT_TIMEOUT;
    }
    rc = ssh_options_set(session, SSH_OPTIONS_HOST, host.c_str());
    if (rc != SSH_OK) {
        printf("ssh_options_set failed 2\n");
        ssh_free(session);
        return SSH_ACCESS_CONNECT_TIMEOUT;
    }
    rc = ssh_options_set(session, SSH_OPTIONS_PORT, &port);
    if (rc != SSH_OK) {
        printf("ssh_options_set failed 3\n");
        ssh_free(session);
        return SSH_ACCESS_CONNECT_TIMEOUT;
    }
    rc = ssh_options_set(session, SSH_OPTIONS_STRICTHOSTKEYCHECK, "no");
    if (rc != SSH_OK) {
        printf("ssh_options_set failed 4\n");
        ssh_free(session);
        return SSH_ACCESS_CONNECT_TIMEOUT;
    }
    
    rc = ssh_connect(session);
    if (rc != SSH_OK) {
        printf("ssh_options_set failed 5\n");
        ssh_disconnect(session);
        ssh_free(session);
        return SSH_ACCESS_CONNECT_TIMEOUT;
    }

    rc = ssh_options_set(session, SSH_OPTIONS_USER, userName.c_str());
    if (rc != SSH_OK) {
        printf("ssh_options_set failed 6\n");
        ssh_disconnect(session);
        ssh_free(session);
        return SSH_ACCESS_AUTH_DENIED;
    }

    rc = ssh_userauth_autopubkey(session, nullptr);
    if (rc != SSH_OK) {
        printf("ssh_options_set failed 7\n");
        ssh_disconnect(session);
        ssh_free(session);
        return SSH_ACCESS_AUTH_DENIED;
    }
    
    printf("ssh_options_set succeed!\n");

    ssh_disconnect(session);
    ssh_free(session);

    return SSH_ACCESS_AUTH_SUCCEED;
}


//检测操作系统用户密码是否匹配(备用服务器用于检测主备服务器配置时用户输入的用户名、密码是否匹配)
bool CServerSync::checkSystemUserAndPassword(const std::string& username, const std::string& password) {
    // 获取系统用户信息
    struct spwd *sp = getspnam(username.c_str());
    if (sp == nullptr) {
        printf("User not found.\n");
        return false;
    }

    // 对输入密码进行加密,静态分配，无需销毁
    char* encryptedPassword = crypt(password.c_str(), sp->sp_pwdp);
    // 将加密后的密码与系统用户密码进行比较
    if (strcmp(encryptedPassword, sp->sp_pwdp) == 0) {
        printf("Password matched..\n");
        return true;
    } else {
        printf("Password not matched..\n");
        return false;
    }
}


//获取操作系统的第一个普通用户（除root，备用服务器使用）
string CServerSync::getSystemFirstNormalUser() {
    struct passwd *user;
    string userName;
    while ((user = getpwent()) != NULL) {
        if (strcmp(user->pw_shell, "/bin/bash") == 0 && strcmp(user->pw_name, "root") != 0) {
            printf("getSystemFirstNormalUser:%s\n",user->pw_name);
            userName=user->pw_name;
            break;
        }
    }
    endpwent();

    return userName;
}

//读取root账户的sshKey公钥(用于主服务器发送给备用服务器，实现ssh免密码登录)
string CServerSync::read_rootUser_ssh_key()
{
    string strPublicKey;
    std::string sshDir = "/root/.ssh";
    std::string privateKeyFile = sshDir + "/id_rsa";
    std::string publicKeyFile = sshDir + "/id_rsa.pub";

    // 检测 ~/.ssh 目录是否存在，如果不存在则创建
    if (!IsExistDir(sshDir.data())) {
        std::string createDirCmd = "mkdir -p " + sshDir;
        system(createDirCmd.c_str());
    }

    // 判断私钥和公钥文件是否都存在
    if (IsExistFile(privateKeyFile.data()) && IsExistFile(publicKeyFile.data())) {
        // 读取公钥文件内容
        std::ifstream publicKey(publicKeyFile);
        std::string publicKeyValue((std::istreambuf_iterator<char>(publicKey)),
                                    std::istreambuf_iterator<char>());
        
        strPublicKey=publicKeyValue;
    } else {
        // 生成密钥对
        std::string generateKeysCmd = "ssh-keygen -t rsa -N '' -f " + privateKeyFile + " -q";
        system(generateKeysCmd.c_str());

        if (IsExistFile(privateKeyFile.data()) && IsExistFile(publicKeyFile.data())) {
            // 读取公钥文件内容
            std::ifstream publicKey(publicKeyFile);
            std::string publicKeyValue((std::istreambuf_iterator<char>(publicKey)),
                                        std::istreambuf_iterator<char>());
            strPublicKey=publicKeyValue;
        }
    }

    //std::cout << "Public Key Value: " << strPublicKey << std::endl;
    return strPublicKey;
}


//检查SSH authorizedKeysFile，并写入指定的publicKey(备用服务器用于将主服务器发送下来的publicKey写入自己用户目录，实现主服务器的免密码登录SSH)
bool CServerSync::checkSSHKeyAndWriteNew(const std::string& username, const std::string& publicKey) {
    std::string sshDir = "/home/" + username + "/.ssh";
    std::string authorizedKeysFile = sshDir + "/authorized_keys";

    // 获取系统用户信息
    struct spwd *sp = getspnam(username.c_str());
    if (sp == nullptr) {
        printf("User not found.\n");
        return false;
    }

    struct passwd *user;
    uid_t user_id=0;
    uid_t group_id=0;
    while ((user = getpwent()) != NULL) {
        if (strcmp(user->pw_name, username.data()) == 0) {
            user_id = user->pw_uid;
            group_id = user->pw_gid;
            break;
        }
    }
    endpwent();


    // 判断.ssh目录是否存在,如果不存在，则创建
    struct stat statBuffer;
    if (stat(sshDir.c_str(), &statBuffer) != 0) {
        // .ssh目录不存在，先使用777权限创建该目录
        if (mkdir(sshDir.c_str(), 0777) != 0) {
            std::cerr << "Failed to create .ssh directory" << std::endl;
            return false;
        }
        //已经创建成功，那么需要改变用户的所有者和所属组以及相应的用户权限
        // 先使用 chown 函数来更改目录的所有者和所属组为对应用户
        int result = chown(sshDir.data(), user_id, group_id);
        if (result == 0) {
        printf("Successfully changed owner and group:%s!\n",sshDir.c_str());
        } else {
            printf("Error changing owner and group:%s!\n",sshDir.c_str());
        }
        //设置文件夹权限为700（标准）
        chmod(sshDir.c_str(), S_IRUSR | S_IWUSR | S_IXUSR);
    }

    // 判断authorized_keys文件是否存在
    if (stat(authorizedKeysFile.c_str(), &statBuffer) != 0) {
        // authorized_keys文件不存在，创建该文件
        std::ofstream file(authorizedKeysFile);
        if (!file.is_open()) {
            std::cerr << "Failed to create authorized_keys file" << std::endl;
            return false;
        }
        file.close();

        //已经创建成功，那么需要改变用户的所有者和所属组以及相应的用户权限
        // 先使用 chown 函数来更改目录的所有者和所属组为对应用户
        int result = chown(authorizedKeysFile.data(), user_id, group_id);
        if (result == 0) {
        printf("Successfully changed owner and group:%s!\n",authorizedKeysFile.c_str());
        } else {
            printf("Error changing owner and group:%s!\n",authorizedKeysFile.c_str());
        }
        //设置文件夹权限为600（标准）
        chmod(authorizedKeysFile.c_str(), S_IRUSR | S_IWUSR);
    }

    // 检查公钥是否已存在于authorized_keys文件中
    std::ifstream file(authorizedKeysFile);
    if (!file.is_open()) {
        std::cerr << "Failed to open authorized_keys file" << std::endl;
        return false;
    }

    string publicKeyNoLineBreak = publicKey;
    // 去掉换行符
    publicKeyNoLineBreak.erase(remove(publicKeyNoLineBreak.begin(), publicKeyNoLineBreak.end(), '\n'), publicKeyNoLineBreak.end());

    std::string line;
    while (getline(file, line)) {
        if (strstr(line.data(),publicKeyNoLineBreak.data())!=NULL) {
            file.close();
            printf("checkSSHKeyAndWriteNew:found public key!\n");
            return true;  // 公钥已存在
        }
    }
    file.close();

    // 公钥不存在，将其附加到authorized_keys文件尾部
    std::ofstream outfile(authorizedKeysFile, std::ios_base::app);
    if (!outfile.is_open()) {
        std::cerr << "Failed to append to authorized_keys file" << std::endl;
        //outfile << "\n";
        outfile << std::endl;
        return false;
    }

    outfile << publicKey << std::endl;
    outfile.close();

    printf("checkSSHKeyAndWriteNew OK!\n");

    return true;
}

#endif