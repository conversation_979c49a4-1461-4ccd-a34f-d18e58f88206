#ifndef NETWORK_H
#define NETWORK_H

#include <QtCore/qglobal.h>

//#include "Lib/UDPSocket.h"
#include "Lib/SBEUdpSocket.h"
//#include "Lib/SBETcpSelectClient.h"
#include "Lib/SBETcpClientSocket.h"
#include "Lib/SBETcpServerSocket.h"
#include "Model/Device/WebSection.h"
#include "Protocol/Protocol.h"
#include "Network/Protocol/CommandSend.h"
#include "Network/Protocol/CommandHandle.h"
#include "Network/Kcp/kcp.h"
#include "CLI/CLIServer.h"
#include <QMutex>
#include "Network/HPSocket/MyTCPServer.h"
#include "Network/HPSocket/MyUdpNode.h"

/************************************************************************/
/* 网络管理类：包括处理SOCKET与协议                                     */
/************************************************************************/

class CSection;
class CSections;

class CNetwork
{
public:
    CNetwork(void);
    ~CNetwork(void);

// 网络启动与停止工作
public:
    //获取本机IP
    static const char* GetHostIP();
    //获取本机MAC
    static const char* GetHostMac();

    // 网络开始工作
    bool	StartWorking(void);

    // UDP网络开始工作
    bool	UdpStartWorking(void);

    // TCP服务器网络开始工作
    bool	TcpServerStartWorking(void);

    // 退出网络
    void	StopWorking(void);

    // 退出UDP网络
    void	UdpStopWorking(void);

    // 退出TCP服务器网络
    void	TcpServerStopWorking(void);

    // 退出应用程序
    void	ExitWorking(void);

// 发送数据
public:
    bool	SendData(void*          lpData,								// 数据
                     int            nLen,								// 数据长度
                     CSection&      device,								// 设备
                     const char*	szUdpIP,							// 目标IP（UDP）
                     unsigned short	uUdpPort,							// 端口（UDP）
                     int            nRepeatCount = 1);					// 重复次数

    // 主要用于主机向大屏发送JSON数据
    bool    SendWebData(void*          lpData,							// 数据
                        int            nLen,							// 数据长度
                        CWebSection&   device,                          // 设备
                        const char*    szUdpIP,							// 目标IP（UDP）
                        unsigned short uUdpPort,						// 端口（UDP）
                        int            nRepeatCount = 1);				// 重复次数);

    // TCP服务器 保留，待修改
    bool	SendTcpData(void*			lpData,						// 数据
                        int				nLen,						// 数据长度
                        LPS_SOCKET_OBJ	sockobj);					// socket对象

    bool	SendUdpData(void*	lpData,								// 数据
                        int		nLen,								// 数据长度
                        const char*	szIP,							// IP地址
                        unsigned short	uPort,						// 端口
                        int		nRepeatCount);						// 重复次数


// 设备操作
public:

    // 设备改变网络模式
    void	TcpStartWorking(void);

    // 设备改变工作模式(集中模式、分布模式)
    void	DeviceChangeWorkPattern(bool bExitApp = FALSE);

    // 正在播放的分区设备变成空闲状态
    void	DevicePlayingToIdleStatus(void);

    // 全部在线设备执行命令(TCP)
    void	DevicesAllSendTcpData(char* data, int len);

    // 检测解码终端的自适应音量值
    void    DevicesCheckNoiseDetectorsVolume(ctime_t tNow);

    // 检测各种设备掉线情况
    void	DevicesCheckOffline(ctime_t tNow);
    bool	DeviceCheckOffline(CSection& device, ctime_t tNow);

    // 设备掉线
    void	DeviceOffline(CSection& device);

    // 分控服务器掉线
    void    ServerOffline(CSection& device);

    // 定时检查获取文件信息的状况
    void	DeviceCheckGetFileInfo(void);

    // 定时检查文件是否需要同步
    void	DeviceCheckNeedUpdateFile(void);

    // 定时检查Web端是否需要更新文件
    void    CheckWebNeedUpdateFile(void);

    // 定时检查采集器使用状况
    void	DeviceCheckAudioCollector(void);

    // 定时检测AUX工作状态
    void    CheckAUXWorking();

    // 改变播放模式（集中模式）
    void	DeviceChangePlayMode(PlayMode playMode);

    // 检测设备是否非空闲
    bool	DevicesCheckNotIlde(CSections* pDevices, unsigned char dt = 0xFF);

    // 定时获取分区状态
    void    GetDeviceStatus();

    // 转发分区状态信息到分控设备
    void	ForwardSectionsStatusToControlDevices(	CSection* pSection,				// pSection = NULL时，则为全部分区
                                                    CSection* pControlDevice);		// pControlDevice = NULL时，则为全部分控设备
    void    ForwardVarySectionsStatusToControlDevices(	vector<string> &vecSectionMac,			// pSection = NULL时，则为全部分区
                                                        CSection* pControlDevice);	// pControlDevice = NULL时，则为全部分控设备
    void	ForwardAllCollectorsStatusToControlDevices(	CSection* pCollector,		// pCollector = NULL时，则为全部音频采集器
                                                        CSection* pControlDevice);	// pControlDevice = NULL时，则为全部分控设备
    void	ForwardAllDevicesStatusToAllControlDevices();

    // 设备改变音频传输方式
    void	DeviceChangeAudiocast();

#if 0
    // 单播音频数据流到设备
    void	StreamSourceToDevices(	int		nPlayID,		// 播放ID
                                    const char*	pData,		// 数据内容
                                    unsigned short	nLen,	// 数据长度
                                    CUDPSocket* pUdpSocket);
#endif
    // KCP单播音频数据流发送到设备
    void    StreamKCPToDevices(int            nPlayID,		// 播放ID
                                     const char*	pData,			// 数据内容
                                     unsigned short	nLen,			// 数据长度
                                     CKCPSocket* pkcpSocket);

    // 单播AUX音频数据流到设备  zhuyg
    void	StreamAUXToDevices(const char*	pData,			// 数据内容
                               unsigned short	nLen);		// 数据长度
      // 接收到KCP数据
    static void  OnRecvKcpData( const char*	 pData,         // 收到的数据
                                unsigned short nLen,          // 数据长度
                                void*          pUserDefined,  // 自定义数据
                                const char*	 szIP,          // IP地址
                                unsigned short nPort);        // 端口

#if SUPPORT_WEB_PAGING
    // 接收到WEB STREAM数据
    static void  OnRecvWebStreamData( const char*	 pData,         // 收到的数据
                                unsigned short nLen,          // 数据长度
                                void*          pUserDefined,  // 自定义数据
                                const char*	 szIP,          // IP地址
                                unsigned short nPort);        // 端口
#endif
// 接收数据
private:

    // 接收到UDP数据
    static void  OnRecvUdpData( const char*	 pData,         // 收到的数据
                                unsigned short nLen,          // 数据长度
                                void*          pUserDefined,  // 自定义数据
                                const char*	 szIP,          // IP地址
                                unsigned short nPort);        // 端口

    // 接收到TCP数据 tcp 保留，待修改
    static void  OnTcpServerCallback(LPS_SOCKET_OBJ sockobj,	// SOCKET对象
                                     void* pUserDefined,		// 自定义数据
                                     int	  sockOper);		// socket操作

// 其它函数
public:
    // 打印数据
    void	ShowDataString(const char* szBuf, int nLen);

    // 添加日志信息
    void	AddLog(CMyString strLog, bool bAddToList = FALSE);
public:
    // 保留，待修改
    CCommandSend		m_CmdSend;							// 命令发送类
    CCommandHandle      m_CmdHandle;						// 回应的命令处理类

    // TCP Socket 保留，待修改
    CSBETcpServerSocket	m_TcpServer;                        // TCP服务器
    CSBETcpClientSocket m_TcpClient;                        // TCP客户端

    MyTCPServer         m_MyTcpServer;                      // My TCP服务器

    bool				m_hasForwardAllDevicesStatus;       // 转发设备信息状态标志

    CLIServer    m_CLIServer;                               // CLI
public:

    // Linux服务器

    // UDP Sokcet
    MyUdpNode               m_MySocketUnicast;                      // My点对点
    MyUdpNode               m_MySocketMulticast;                     // My组播
    MyUdpNode               m_MyUdpKCPUnicast;                      // KCP（用于TCP下的音频流单播）
    MyUdpNode               m_MyUdpWebPagingUnicast;                // APP寻呼（用于WEBPaging音频流单播）

    CUDPSocket*             m_pSocketGpsUnicast;					// GPS对点对
    CUDPSocket*             m_pSocketGpsBroadcast;					// GPS广播
    CSBEUdpSockets          m_udpSockets;							// 以上的SOCKET集合

    CUDPSocket*             m_pSocketPagingMulticast;				// 组播转发寻呼台的音频流

    //KCP
    CKCPSockets             m_kcpSockets;							// KCP sockets集合

    // 分控服务器
    CSBETcpServerSocket     m_TcpHostServer;
    CSBETcpClientSocket     m_TcpHostClient;

    bool                    m_bNetworkIsReady;                         //网络已经准备好

public:
    //pthread_mutex_t         PlaylistUpgradeMutex;
};




#endif // NETWORK_H
