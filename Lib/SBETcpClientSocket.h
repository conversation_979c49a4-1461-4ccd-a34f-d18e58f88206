#ifndef CSBETcpClientSocket_H
#define CSBETcpClientSocket_H

#include <QtCore/qglobal.h>

#include"TcpClient.h"

#if defined(Q_OS_LINUX)
class CSBETcpClientSocket : public CTcpClient
{
public:
    // 连接服务器
    bool	ConnectServer(	LPSTR	lpszServerIP,
                                        int		nServerPort,
                                        TCP_CLIENT_CALLBACK* pFunRecvData = NULL,
                                        void*	pUserDefined = NULL);

    // 往服务器发送数据
    bool	SendData(char*	data, int len, LPC_SOCKET_OBJ pClientObj);

    // 断开服务器的连接
    void	DisconnectServer(void);
};
#else

class CSBETcpClientSocket
{
public:
    // 连接服务器
    bool	ConnectServer(	LPSTR	lpszServerIP,
                                        int		nServerPort,
                                        TCP_CLIENT_CALLBACK* pFunRecvData = NULL,
                                        void*	pUserDefined = NULL);

    // 往服务器发送数据
    bool	SendData(char*	data, int len, LPC_SOCKET_OBJ pClientObj);

    // 断开服务器的连接
    void	DisconnectServer(void);
};


#endif


#endif // CSBETcpClientSocket_H
