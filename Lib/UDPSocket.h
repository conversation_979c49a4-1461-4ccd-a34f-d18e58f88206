#ifndef UDPSOCKET_H
#define UDPSOCKET_H
#if defined(Q_OS_LINUX)
#include <sys/socket.h>
#include <arpa/inet.h>
#include <sys/epoll.h>
#endif
#include <unistd.h>
#include <vector>
#include <string.h>
#include <strings.h>
#include <pthread.h>
#include <errno.h>

using namespace std;

#define  MAX_EVENT_COUNT  256
#define  MAX_BUF_LEN      1450
#define  MAX_IP_LEN       16
//#define  LPSTR            char*

// 接收数据的回调函数
typedef void(UDP_CALLBACK)(const char*		pData,			// 收到的数据
                           unsigned short	nLen,			// 数据长度
                           void*		pUserDefined,           // 自定义数据
                           const char*		szIP,			// IP地址
                           unsigned short	nPort);			// 端口


/************************************************************************/
/*                                                                      */
/*  CUDPSocket : Sokcet通讯类                                           */
/*                                                                      */
/*  实现点对点、组播数据的发送与接收                                    */
/*                                                                      */
/************************************************************************/
class CUDPSocket
{
public:
    CUDPSocket();
    ~CUDPSocket();

    // 网络
    bool	CreateSocket(unsigned short	uBindPort,					// 绑定端口
                         UDP_CALLBACK*	pFunRecvData = NULL,		// 收到数据后的回调函数
                         void*			pUserDefined = NULL);		// 自定义数据

    void    CleanSocket(void);										// 清理socket

    // 组播
    bool	JoinMulticast(const char* multicastIP);					// 加入组播
    bool	LeaveMulticast();										// 离开组播

    // 广播
    bool	EnableBroadcast();										// 开启广播模式

    // 数据处理
    int		HandleIO(void);										// 处理事件的到来
    int		RecvData(void);										// 接收数据
    bool	SendData(void*			lpData,						// 数据
                     int			nLen,						// 数据长度
                     const char*	szHost,						// IP地址
                     unsigned short	uPort,						// 端口
                     int			nRepeatCount);				// 重复次数

public:
    // 网络
    unsigned short  m_uBindPort;        // 绑定端口
    int             m_socket;           // 处理命令套接字
	#if defined(Q_OS_LINUX)
    epoll_event     m_event;            // epoll事件
	#endif
private:
    // 组播
    bool		m_hasJoinMulticast;	// 加入组播标志
    char		m_szMulticastIP[16];	// 组播地址

    // 广播
    bool		m_isBroadcast;		// 开启广播标志

    // 数据处理
    UDP_CALLBACK*	m_pFunRecvData;		// 收到数据后的回调函数
    void*		m_pUserDefined;		// 用户自定义数据

};


/************************************************************************/
/*                                                                      */
/*  CUDPSockets : Sokcet通讯集合                                        */
/*                                                                      */
/*  负责对所有socket的管理                                              */
/*                                                                      */
/************************************************************************/

class CUDPSockets
{
public:
    CUDPSockets(void);
    ~CUDPSockets(void);

public:
    CUDPSocket*	CreateSocket(unsigned short	uBindPort	= 0,		// 创建socket
                             UDP_CALLBACK*	pFunRecvData = NULL,	// 收到数据后的回调函数
                             void*			pUserDefined = NULL);	// 自定义数据

    void	CleanSockets(void);										// 清除socket

    // 工作线程
    bool	StartWorkThread();										// 启动工作线程
    static  void*   WorkThread(void* lpParam);      // 工作线程处理过程
    void        ExitWorkThread(void);

public:
    vector<CUDPSocket*> m_pSockets;

public:
    // 网络 epoll事件数组
	#if defined(Q_OS_LINUX)
    epoll_event     m_Events[MAX_EVENT_COUNT];
	#endif
    // 工作线程
    pthread_t       m_pWorkThread;

    // 用于处理socket的epoll专用描述符
    int             m_epfd;

};




#endif // UDPSOCKET_H
