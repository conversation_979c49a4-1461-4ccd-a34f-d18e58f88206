#ifndef CTCPSERVER_H
#define CTCPSERVER_H

#include<stdio.h>
#include<sys/types.h>
#if defined(Q_OS_LINUX)
#include<sys/socket.h>
#include<sys/epoll.h>
#include<netinet/in.h>
#include<arpa/inet.h>
#endif
#include<unistd.h>

#include<fcntl.h>
#include<string.h>
#include<stdlib.h>
#include<pthread.h>
#include<list>
using std::list;

#define LISTEN_QUEUE_SIZE   256
#define MAX_EVENTS_SIZE      1024
#define MAX_IP_LEN     16
#define MAX_BUF_LEN  1450
#define MAXIMUM_WAIT_OBJECTS  64

typedef unsigned char BYTE;
typedef unsigned short USHORT;
typedef unsigned short WORD;

enum
{
    SERVER_SOCKET_RECV	= 1,		// SOCKET接收到数据
    SERVER_SOCKET_COLSE,			// SOCKET关闭
};

//每次接收都分配一个缓冲区对象，
typedef struct tagS_BUFFER_OBJ
{
    char*					buf;					    // 数据缓冲区
    int						buflen;				// 缓冲区数据的大小
	#if defined(Q_OS_LINUX)
    sockaddr_in	    addr;					// 数据来源地址 (UDP)
	#endif
    int						addrlen;				// 地址长度
    struct tagS_BUFFER_OBJ*	next;

} S_BUFFER_OBJ, *LPS_BUFFER_OBJ;

// 每个socket分配的对象
typedef struct tagS_SOCKET_OBJ
{
    #if 0
    int                                       sock;					// Socket 句柄
    //HANDLE					           hEvent;					// 事件句柄
    bool                                    bListen;				// socket是否监听（TCP）
    bool                                    bClosed;				// 标志连接是否关闭
	#if defined(Q_OS_LINUX)
    sockaddr_in                        addr;					// 客户端的地址
	#endif
    int					                	addrlen;				// 地址长度
    LPS_BUFFER_OBJ			    pending,				// 发送缓冲区列表头部
                                                pendingtail;		// 列表的尾部
    pthread_mutex_t		        csSocket;				// 对数据起保护作用的关键段(互斥量)
    struct tagS_SOCKET_OBJ	*next,				    // socket对象列表
                                                *prev;
    #endif
    // 原有基础上新添加变量
    char*                                   ip;				     	// IP地址
    char*                                   buf;				    	// 缓冲区（主要处理粘包）
    int                                        datalen;				// 数据长度

    unsigned long                            dwConnID;

} S_SOCKET_OBJ, *LPS_SOCKET_OBJ;

typedef void(TCP_SERVER_CALLBACK)(LPS_SOCKET_OBJ sockObj, void* pUserDefined, int sockOper);  // 回调函数

class CTcpServer
{
public:
    CTcpServer();

    bool	  StartWorking(	int         nBindPort,
                                        TCP_SERVER_CALLBACK* pFunRecvData = NULL,
                                        void*    pUserDefined	= NULL);

    void  StopWorking(void);

    // 创建监听客户端连接的套接字
    bool   CreateListenSock(int nBindPort);

    // 关闭监听客户端连接的套接字(客户端套接字也会关闭)
    void   CloseListenSock(void);

    // 工作线程（监听IO）
    static void* WorkThread(void*  lpParam);

    // 发送数据
    bool	 SendData(	char*                      data,			    // 数据
                                int                           len,			    // 数据长度
                                LPS_SOCKET_OBJ	sockobj);		// socket对象

   void	ClearSocketObj(LPS_SOCKET_OBJ SockObj);		// 清除某一个socket

   void	SetSubpackage(bool flag);					// 是否拆开粘包

   int    GetClientCount(void)    {return m_ClientSockObj.size();}  //获取已连接的客户端数量

private:

    LPS_BUFFER_OBJ	GetBufferObj(int buflen);
    void			FreeBufferObj(LPS_BUFFER_OBJ BufObj);
    LPS_SOCKET_OBJ	GetSocketObj(int s, bool bListen);
    void			FreeSocketObj(LPS_SOCKET_OBJ SockObj);

    LPS_SOCKET_OBJ	FindSocketObj(list<LPS_SOCKET_OBJ> ClientSockObjList, int nSock);
    void			EnqueueBufferObj(LPS_SOCKET_OBJ SockObj, LPS_BUFFER_OBJ BufObj, bool bAtHead);
    LPS_BUFFER_OBJ	DequeueBufferObj(LPS_SOCKET_OBJ SockObj);
    int			ReceivePendingData(LPS_SOCKET_OBJ SockObj);
    int			SendPendingData(LPS_SOCKET_OBJ SockObj);
    //int			HandleIo(LPS_THREAD_OBJ ThreadObj, LPS_SOCKET_OBJ SockObj);

    // 拆开粘包
    void			Subpackage(	LPS_SOCKET_OBJ	sockobj,// socket对象
                                            LPS_BUFFER_OBJ	bufobj, // 缓冲区对象（包含数据）
                                            int				len);	// 数据长度

private:

     TCP_SERVER_CALLBACK*	 m_pFunRecvData;				// 回调函数
     void*                                  m_pUserDefined;				// 自定义参数

     bool                                    m_bIsWorking;
     int                                       m_nListenSock;                                          // 监听客户端连接的套接字
     list<LPS_SOCKET_OBJ>      m_ClientSockObj;                                      // 客户端socket对象列表
     int                                       m_nEpfd;                                                    // epoll句柄
	 #if defined(Q_OS_LINUX)
     struct epoll_event             m_ClientEvents[MAX_EVENTS_SIZE];        // 客户端事件集合
	 #endif
     pthread_t                           m_listenThread;

     bool					                 m_bSubpackage;				// 是否拆开粘包
};

#endif // CTCPSERVER_H
