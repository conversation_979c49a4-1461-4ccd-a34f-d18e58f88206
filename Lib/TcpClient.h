#ifndef CTCPCLIENT_H
#define CTCPCLIENT_H

#include<stdio.h>
#include<sys/types.h>
#if defined(Q_OS_LINUX)
#include<sys/socket.h>
#include<sys/epoll.h>
#include<netinet/in.h>
#include<arpa/inet.h>
#endif
#include<unistd.h>
#include<fcntl.h>
#include<string.h>
#include<stdlib.h>
#include<pthread.h>

#if defined(Q_OS_LINUX)
#define SOCKET_ERROR         -1
#define NO_ERROR                 0
#endif
#define MAX_EVENTS_SIZE     1024
#define MAX_IP_LEN               16
#define MAX_BUF_LEN            1450

typedef unsigned char   BYTE;
typedef unsigned short USHORT;
typedef unsigned short WORD;
typedef char*                 LPSTR;

enum
{
    CLIENT_SOCKET_RECV	= 1,		           // SOCKET接收到数据
    CLIENT_SOCKET_CONNECTED,		        // SOCKET连接上服务器
    CLIENT_SOCKET_DISCONNECTED,		// SOCKET与服务器失连
};

// 每次接收都分配一个缓冲区对象，
typedef struct tagC_BUFFER_OBJ
{
    char*					buf;					// 数据缓冲区
    int						buflen;					// 缓冲区数据的大小
    struct tagC_BUFFER_OBJ*	next;

} C_BUFFER_OBJ, *LPC_BUFFER_OBJ;

// 客户端socket对象
typedef struct tagC_SOCKET_OBJ
{
    int                                     sock;                          // Socket 句柄
    //HANDLE					hEvent;					// 事件句柄
    LPC_BUFFER_OBJ             pending,                   // 发送的冲区列表头部
                                              pendingtail;              // 列表的尾部
    pthread_mutex_t             CritSecSocket;			// 对数据起保护作用的关键段

    char                                   *ip;                            // IP地址
    char                                   *buf;                         // 缓冲区（主要处理粘包）
    int                                     datalen;                     // 数据长度

} C_SOCKET_OBJ, *LPC_SOCKET_OBJ;

class      CTcpClient;
typedef CTcpClient*	LPCTcpClient;

typedef void(TCP_CLIENT_CALLBACK)(LPC_SOCKET_OBJ sockObj, void* pUserDefined, int sockOper);

class CTcpClient
{
public:
    CTcpClient();
    virtual ~CTcpClient(void);

public:
    bool			StartWorking(	int		nProtocol,
                                            LPSTR	lpszServerIP,
                                            int		nServerPort,
                                            TCP_CLIENT_CALLBACK* pFunRecvData = NULL,
                                            void*	pUserDefined = NULL);
    void			StopWorking(void);

    LPC_SOCKET_OBJ	CreateSocket();
    bool			SendData(char*	data, int len, LPC_SOCKET_OBJ SockObj);
    bool			HasConnectedServer(void);

    void			SetSubpackage(bool flag);			// 是否拆开粘包

private:
    static void* ConnectThread(void* lpParam);
    static void* WorkThread(void* lpParam);

    LPC_BUFFER_OBJ	GetBufferObj(int buflen);
    void			FreeBufferObj(LPC_BUFFER_OBJ BufObj);
    LPC_SOCKET_OBJ	GetSocketObj(int sock);
    void			FreeSocketObj(LPC_SOCKET_OBJ SockObj);
    void			EnqueueBufferObj(LPC_SOCKET_OBJ SockObj, LPC_BUFFER_OBJ BufObj, bool bAtHead);
    LPC_BUFFER_OBJ	DequeueBufferObj(LPC_SOCKET_OBJ SockObj);
    int				ConnectServer(LPC_SOCKET_OBJ SockObj, bool bConnected);

    int				ReceivePendingData(LPC_SOCKET_OBJ SockObj);
    int				SendPendingData(LPC_SOCKET_OBJ SockObj);

    int				HandleIo(LPC_SOCKET_OBJ SockObj);

    // 拆开粘包
    void			Subpackage(	LPC_SOCKET_OBJ	sockobj,// socket对象
                                            LPC_BUFFER_OBJ	bufobj, // 缓冲区对象（包含数据）
                                            int				len);	// 数据长度
public:
    LPC_SOCKET_OBJ			m_pSocket;

private:
    bool                        m_isWorking;				// 工作标志
    int                           m_nProtocol;				// 协议
	#if defined(Q_OS_LINUX)
    sockaddr_in	        m_SockAddrServer;
	#endif
    int                           m_nAddrLen;
    bool                        m_bConnectedServer;

    pthread_t 				m_hThreadWork;
    pthread_t 				m_hThreadConnect;

    void*					    m_pUserDefined;
    TCP_CLIENT_CALLBACK*	m_pFunRecvData;

    bool					        m_bSubpackage;				// 是否拆开粘包
};

#endif // CTCPCLIENT_H
