#ifndef CSBETCPSERVERSOCKET_H
#define CSBETCPSERVERSOCKET_H

#include <QtCore/qglobal.h>

#include "TcpServer.h"

#if defined(Q_OS_LINUX)
class CSBETcpServerSocket : public CTcpServer
{
public:
    CSBETcpServerSocket();
    ~CSBETcpServerSocket();

    // 开始监听客户端连接
    bool	StartListen( int	nBindPort,
                                TCP_SERVER_CALLBACK* pFunRecvData = NULL,
                                void* pUserDefined	= NULL);

    // 停止监听客户端连接
    void	StopListen(void);

    // 往指定客户端发送数据
    bool	SendDataToClient(	char*                      data,               // 数据
                                            int                            len,                 // 数据长度
                                            LPS_SOCKET_OBJ	pClientObj);	// 客户端socket对象

    // 清除客户端socket（StopListen也会自动清除所有的客户端socket）
    void	CleanClient(LPS_SOCKET_OBJ pClientObj);
};
#else

class CSBETcpServerSocket
{
public:
    CSBETcpServerSocket();

    // 开始监听客户端连接
    bool	StartListen( int	nBindPort,
                                TCP_SERVER_CALLBACK* pFunRecvData = NULL,
                                void* pUserDefined	= NULL);

    // 停止监听客户端连接
    void	StopListen(void);

    // 往指定客户端发送数据
    bool	SendDataToClient(	char*                      data,               // 数据
                                            int                            len,                 // 数据长度
                                            LPS_SOCKET_OBJ	pClientObj);	// 客户端socket对象

    // 清除客户端socket（StopListen也会自动清除所有的客户端socket）
    void	CleanClient(LPS_SOCKET_OBJ pClientObj);
};

#endif

#endif // CSBETCPSERVERSOCKET_H
