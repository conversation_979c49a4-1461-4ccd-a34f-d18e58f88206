#ifndef SBEUDPSOCKET_H
#define SBEUDPSOCKET_H

#include "UDPSocket.h"

class CSBEUdpSockets:public CUDPSockets
{
public:
    // 创建socket
    CUDPSocket*	CreateSocket(unsigned short	uBindPort	 = 0,	  // 创建socket
                             UDP_CALLBACK*	pFunRecvData = NULL,  // 收到数据后的回调函数
                             void*		pUserDefined = NULL); // 自定义数据

    // 清除所有创建的socket
    void	CleanSockets(void);

    // 开始接收数据（如果不调用，则只能发送数据）
    bool	StartRecvUdpData(void);
};

#endif // SBEUDPSOCKET_H
