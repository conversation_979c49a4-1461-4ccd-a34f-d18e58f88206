#ifndef NETWORKTOOL_H
#define NETWORKTOOL_H

#include <QtCore/qglobal.h>

typedef struct
{
    char ifaName[64];   //网络接口名
    char ifaAddr[16];       //网卡IP地址
}NetCardInfo;

class CNetworkTool
{
public:
    CNetworkTool(void);
    ~CNetworkTool(void);

public:
#if defined(Q_OS_LINUX)
    static void GetValidNetCardInfo(NetCardInfo *netcard);              // 获取本机netcardinfo(first)
    static const char *	GetHostIP();									// 获取本机IP地址(返回第一个网卡地址)
    static const char * GetNetMask();                                   // 获取本机子网掩码(返回第一个子网掩码地址)
    static bool		IsHostIP(const char * szIP);					// 判断IP地址是否为本机IP（所有网卡）
    static const char *	GetGatewayByIP(const char * szIP);				// 通过IP地址获取默认网关
    static const char *	GetHostMac();									// 获取MAC地址
#else
    static const char *	GetHostIP(){return "0.0.0.0";};									// 获取本机IP地址(返回第一个网卡地址)
    static bool		IsHostIP(const char * szIP){return true;};					// 判断IP地址是否为本机IP（所有网卡）
    static const char *	GetGatewayByIP(const char * szIP){return "***********";};				// 通过IP地址获取默认网关
    static const char *	GetHostMac(){return "00:00:00:00:00:00";};									// 获取MAC地址
#endif

};



#endif // NETWORKTOOL_H
