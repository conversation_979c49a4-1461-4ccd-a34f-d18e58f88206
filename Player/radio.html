<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频流播放器</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .player-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .player-controls {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .status {
            margin-left: 15px;
            color: #666;
        }
        .buffer-indicator {
            height: 5px;
            background-color: #e0e0e0;
            border-radius: 3px;
            margin-top: 10px;
            overflow: hidden;
        }
        .buffer-progress {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
        }
        .error-message {
            color: #f44336;
            margin-top: 10px;
            display: none;
        }
        .debug-info {
            margin-top: 15px;
            font-size: 12px;
            color: #666;
            font-family: monospace;
        }
        .url-input {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .connection-config {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .connection-config h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }
        .config-row {
            display: flex;
            align-items: center;
            margin: 10px 0;
            gap: 10px;
        }
        .config-row label {
            min-width: 80px;
            font-weight: bold;
            color: #555;
        }
        .config-input {
            flex: 1;
            padding: 6px 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        .config-input:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
        }
        .test-btn {
            padding: 6px 15px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .test-btn:hover {
            background-color: #1976D2;
        }
        .test-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .test-result {
            margin-left: 10px;
            font-size: 14px;
            font-weight: bold;
        }
        .test-result.success {
            color: #4CAF50;
        }
        .test-result.error {
            color: #f44336;
        }
        .test-result.testing {
            color: #FF9800;
        }
        .connection-status {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
            background-color: #ccc;
        }
        .connection-status.connected {
            background-color: #4CAF50;
        }
        .connection-status.disconnected {
            background-color: #f44336;
        }
    </style>
</head>
<body>
    <div class="player-container">
        <h1>音频流播放器</h1>
        <div class="player-controls">
            <span class="connection-status" id="connectionStatus"></span>
            <button id="playButton">播放</button>
            <button id="stopButton" disabled>停止</button>
            <span class="status" id="status">未连接</span>
            <span class="status" id="playTime">播放时长: 00:00</span>
        </div>
        <div>
            <label for="radioUrl">电台URL:</label>
            <input type="text" id="radioUrl" class="url-input" 
                   value="https://live.ximalaya.com/radio-first-page-app/live/74/64.m3u8" />
        </div>
        <div class="connection-config">
            <h3>WebSocket连接配置</h3>
            <div class="config-row">
                <label for="wsHost">服务器地址:</label>
                <input type="text" id="wsHost" class="config-input" value="************" placeholder="例如: *************" />
            </div>
            <div class="config-row">
                <label for="wsPort">端口号:</label>
                <input type="number" id="wsPort" class="config-input" value="8085" placeholder="例如: 8085" min="1" max="65535" />
            </div>
            <div class="config-row">
                <button id="testConnectionButton" class="test-btn">测试连接</button>
                <span id="connectionTest" class="test-result"></span>
            </div>
        </div>
        <div class="buffer-indicator">
            <div class="buffer-progress" id="bufferProgress"></div>
        </div>
        <div class="error-message" id="errorMessage"></div>
        <div class="debug-info" id="debugInfo"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const playButton = document.getElementById('playButton');
            const stopButton = document.getElementById('stopButton');
            const statusElement = document.getElementById('status');
            const bufferProgress = document.getElementById('bufferProgress');
            const errorMessage = document.getElementById('errorMessage');
            const debugInfo = document.getElementById('debugInfo');
            const connectionStatus = document.getElementById('connectionStatus');
            const radioUrlInput = document.getElementById('radioUrl');
            const playTimeElement = document.getElementById('playTime');
            const wsHostInput = document.getElementById('wsHost');
            const wsPortInput = document.getElementById('wsPort');
            const testConnectionButton = document.getElementById('testConnectionButton');
            const connectionTestResult = document.getElementById('connectionTest');
            
            let socket = null;
            let audioContext = null;
            let mediaSource = null;
            let sourceBuffer = null;
            let audioQueue = [];
            let isPlaying = false;
            let isConnected = false;
            let bufferTarget = 10; // 目标缓冲区大小（秒）
            let minBuffer = 3; // 最小缓冲区大小（秒）
            let isBuffering = false;
            let lastBufferCheck = 0;
            let debugLog = [];
            let heartbeatInterval = null;
            let playTimeInterval = null;
            let playStartTime = null;
            let audio = null; // 添加全局audio变量
            const HEARTBEAT_INTERVAL = 10000; // 10秒发送一次心跳
            
            // 保存配置到localStorage
            function saveConfig() {
                const config = {
                    wsHost: wsHostInput.value.trim(),
                    wsPort: wsPortInput.value.trim(),
                    radioUrl: radioUrlInput.value.trim()
                };
                localStorage.setItem('radioConfig', JSON.stringify(config));
                addDebugLog('配置已保存');
            }
            
            // 从localStorage加载配置
            function loadConfig() {
                try {
                    const savedConfig = localStorage.getItem('radioConfig');
                    if (savedConfig) {
                        const config = JSON.parse(savedConfig);
                        wsHostInput.value = config.wsHost || '************';
                        wsPortInput.value = config.wsPort || '8085';
                        radioUrlInput.value = config.radioUrl || '';
                        addDebugLog('配置已加载');
                    } else {
                        // 设置默认值
                        wsHostInput.value = '************';
                        wsPortInput.value = '8085';
                    }
                } catch (error) {
                    addDebugLog(`加载配置失败: ${error}`);
                    // 设置默认值
                    wsHostInput.value = '************';
                    wsPortInput.value = '8085';
                }
            }
            
            // 获取WebSocket连接URL
            function getWebSocketUrl() {
                const host = wsHostInput.value.trim() || '************';
                const port = wsPortInput.value.trim() || '8085';
                return `ws://${host}:${port}`;
            }
            
            // 测试WebSocket连接
            function testConnection() {
                const wsUrl = getWebSocketUrl();
                testConnectionButton.disabled = true;
                connectionTestResult.textContent = '测试中...';
                connectionTestResult.className = 'test-result testing';
                
                addDebugLog(`测试连接: ${wsUrl}`);
                
                const testSocket = new WebSocket(wsUrl);
                
                const timeout = setTimeout(() => {
                    testSocket.close();
                    connectionTestResult.textContent = '连接超时';
                    connectionTestResult.className = 'test-result error';
                    testConnectionButton.disabled = false;
                    addDebugLog('连接测试超时');
                }, 5000);
                
                testSocket.onopen = () => {
                    clearTimeout(timeout);
                    connectionTestResult.textContent = '连接成功';
                    connectionTestResult.className = 'test-result success';
                    testConnectionButton.disabled = false;
                    addDebugLog('连接测试成功');
                    testSocket.close();
                };
                
                testSocket.onerror = (error) => {
                    clearTimeout(timeout);
                    connectionTestResult.textContent = '连接失败';
                    connectionTestResult.className = 'test-result error';
                    testConnectionButton.disabled = false;
                    addDebugLog(`连接测试失败: ${error}`);
                };
                
                testSocket.onclose = () => {
                    clearTimeout(timeout);
                    if (connectionTestResult.textContent === '测试中...') {
                        connectionTestResult.textContent = '连接失败';
                        connectionTestResult.className = 'test-result error';
                        testConnectionButton.disabled = false;
                        addDebugLog('连接测试失败');
                    }
                };
            }
            
            // 添加调试日志
            function addDebugLog(message) {
                const timestamp = new Date().toLocaleTimeString();
                debugLog.unshift(`[${timestamp}] ${message}`);
                if (debugLog.length > 10) debugLog.pop();
                debugInfo.textContent = debugLog.join('\n');
            }
            
            // 格式化时间显示
            function formatTime(seconds) {
                const minutes = Math.floor(seconds / 60);
                const secs = Math.floor(seconds % 60);
                return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }
            
            // 更新播放时长
            function updatePlayTime() {
                if (playStartTime) {
                    const elapsed = (Date.now() - playStartTime) / 1000;
                    playTimeElement.textContent = `播放时长: ${formatTime(elapsed)}`;
                }
            }
            
            // 开始播放时长计时
            function startPlayTimeCounter() {
                playStartTime = Date.now();
                if (playTimeInterval) {
                    clearInterval(playTimeInterval);
                }
                playTimeInterval = setInterval(updatePlayTime, 1000);
                updatePlayTime(); // 立即更新一次
            }
            
            // 停止播放时长计时
            function stopPlayTimeCounter() {
                if (playTimeInterval) {
                    clearInterval(playTimeInterval);
                    playTimeInterval = null;
                }
                playStartTime = null;
                playTimeElement.textContent = '播放时长: 00:00';
            }
            
            // 更新连接状态指示器
            function updateConnectionStatus(connected) {
                isConnected = connected;
                if (connected) {
                    connectionStatus.classList.remove('disconnected');
                    connectionStatus.classList.add('connected');
                } else {
                    connectionStatus.classList.remove('connected');
                    connectionStatus.classList.add('disconnected');
                }
            }
            
            // 初始化音频上下文
            function initAudioContext() {
                try {
                    // 先关闭现有的音频上下文（如果存在）
                    if (audioContext) {
                        audioContext.close().catch(e => console.error('关闭音频上下文失败:', e));
                    }
                    
                    // 先停止现有的音频播放
                    if (audio) {
                        audio.pause();
                        audio.currentTime = 0;
                        audio.src = '';
                        audio = null;
                    }
                    
                    audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    mediaSource = new MediaSource();
                    
                    mediaSource.addEventListener('sourceopen', () => {
                        try {
                            sourceBuffer = mediaSource.addSourceBuffer('audio/mpeg');
                            sourceBuffer.addEventListener('updateend', processQueue);
                            sourceBuffer.addEventListener('error', (e) => {
                                addDebugLog(`SourceBuffer错误: ${e}`);
                                // 尝试恢复
                                if (sourceBuffer.buffered.length > 0) {
                                    const start = sourceBuffer.buffered.start(0);
                                    const end = sourceBuffer.buffered.end(0);
                                    sourceBuffer.remove(start, end);
                                }
                            });
                            addDebugLog('MediaSource已打开，SourceBuffer已创建');
                        } catch (e) {
                            addDebugLog(`创建SourceBuffer失败: ${e}`);
                            showError(`初始化音频失败: ${e.message}`);
                        }
                    });
                    
                    audio = new Audio(); // 使用全局变量
                    audio.src = URL.createObjectURL(mediaSource);
                    
                    // 监听音频事件
                    audio.addEventListener('waiting', () => {
                        addDebugLog('音频等待缓冲');
                        statusElement.textContent = '缓冲中...';
                        isBuffering = true;
                    });
                    
                    audio.addEventListener('playing', () => {
                        addDebugLog('音频开始播放');
                        statusElement.textContent = '正在播放';
                        isBuffering = false;
                        startPlayTimeCounter(); // 开始计时
                    });
                    
                    audio.addEventListener('error', (e) => {
                        addDebugLog(`音频错误: ${e}`);
                        showError(`播放错误: ${e.message}`);
                    });
                    
                    // 尝试播放
                    const playPromise = audio.play();
                    if (playPromise !== undefined) {
                        playPromise.catch(e => {
                            addDebugLog(`自动播放失败: ${e}`);
                            // 不自动播放，等待用户交互
                        });
                    }
                    
                    return true;
                } catch (e) {
                    addDebugLog(`初始化音频上下文失败: ${e}`);
                    showError(`初始化音频失败: ${e.message}`);
                    return false;
                }
            }
            
            // 处理音频队列
            function processQueue() {
                if (!sourceBuffer || sourceBuffer.updating) return;
                
                // 检查缓冲区状态
                checkBufferStatus();
                
                if (audioQueue.length > 0 && !sourceBuffer.updating) {
                    // 如果缓冲区太小，优先填充缓冲区
                    if (getBufferedSeconds() < minBuffer) {
                        addDebugLog(`缓冲区不足 (${getBufferedSeconds().toFixed(1)}s)，优先填充`);
                    }
                    
                    const chunk = audioQueue.shift();
                    try {
                        sourceBuffer.appendBuffer(chunk);
                        addDebugLog(`添加数据块到缓冲区，队列剩余: ${audioQueue.length}`);
                    } catch (e) {
                        addDebugLog(`添加缓冲区失败: ${e}`);
                        
                        if (e.name === 'QuotaExceededError') {
                            // 缓冲区已满，清理一部分
                            if (sourceBuffer.buffered.length > 0) {
                                const start = sourceBuffer.buffered.start(0);
                                const end = sourceBuffer.buffered.end(0);
                                // 保留最近的数据
                                const keepStart = Math.max(start, end - bufferTarget);
                                if (keepStart > start) {
                                    addDebugLog(`清理缓冲区: ${start} -> ${keepStart}`);
                                    sourceBuffer.remove(start, keepStart);
                                    // 延迟后重试
                                    setTimeout(() => {
                                        try {
                                            sourceBuffer.appendBuffer(chunk);
                                        } catch (retryError) {
                                            addDebugLog(`重试添加缓冲区失败: ${retryError}`);
                                        }
                                    }, 100);
                                }
                            }
                        }
                    }
                }
            }
            
            // 获取当前缓冲的秒数
            function getBufferedSeconds() {
                if (!sourceBuffer || sourceBuffer.buffered.length === 0) return 0;
                
                const currentTime = mediaSource.duration ? mediaSource.currentTime : 0;
                const bufferedEnd = sourceBuffer.buffered.end(sourceBuffer.buffered.length - 1);
                return Math.max(0, bufferedEnd - currentTime);
            }
            
            // 检查缓冲区状态
            function checkBufferStatus() {
                const now = Date.now();
                // 限制检查频率
                if (now - lastBufferCheck < 1000) return;
                lastBufferCheck = now;
                
                const bufferedSeconds = getBufferedSeconds();
                
                // 更新缓冲进度条
                const percentage = Math.min(100, (bufferedSeconds / bufferTarget) * 100);
                bufferProgress.style.width = `${percentage}%`;
                
                // 如果缓冲区不足，标记为缓冲状态
                if (bufferedSeconds < minBuffer && !isBuffering) {
                    addDebugLog(`缓冲区不足: ${bufferedSeconds.toFixed(1)}s`);
                    statusElement.textContent = '缓冲中...';
                    isBuffering = true;
                } else if (bufferedSeconds >= minBuffer && isBuffering) {
                    addDebugLog(`缓冲区恢复: ${bufferedSeconds.toFixed(1)}s`);
                    statusElement.textContent = '正在播放';
                    isBuffering = false;
                }
            }
            
            // 发送心跳包
            function sendHeartbeat() {
                if (socket && socket.readyState === WebSocket.OPEN) {
                    try {
                        socket.send(JSON.stringify({
                            type: 'play_radio_heartbeat'
                        }));
                        addDebugLog('发送心跳包');
                    } catch (e) {
                        addDebugLog(`发送心跳包失败: ${e}`);
                    }
                }
            }
            
            // 开始心跳定时器
            function startHeartbeat() {
                // 清除现有定时器
                if (heartbeatInterval) {
                    clearInterval(heartbeatInterval);
                }
                
                // 立即发送一次心跳
                sendHeartbeat();
                
                // 设置定时发送心跳
                heartbeatInterval = setInterval(sendHeartbeat, HEARTBEAT_INTERVAL);
            }
            
            // 停止心跳定时器
            function stopHeartbeat() {
                if (heartbeatInterval) {
                    clearInterval(heartbeatInterval);
                    heartbeatInterval = null;
                }
            }
            
            // 连接WebSocket
            function connectWebSocket() {
                try {
                    // 关闭现有连接
                    if (socket) {
                        socket.close();
                    }
                    
                    const wsUrl = getWebSocketUrl();
                    addDebugLog(`连接WebSocket: ${wsUrl}`);
                    
                    socket = new WebSocket(wsUrl);
                    socket.binaryType = 'arraybuffer';
                    
                    socket.onopen = () => {
                        addDebugLog('WebSocket连接已建立');
                        statusElement.textContent = '已连接';
                        updateConnectionStatus(true);
                        hideError();
                        
                        // 连接成功后开始心跳
                        startHeartbeat();
                    };
                    
                    socket.onmessage = (event) => {
                        if (typeof event.data === 'string') {
                            try {
                                const message = JSON.parse(event.data);
                                addDebugLog(`收到消息: ${JSON.stringify(message)}`);
                                
                                // 处理不同类型的消息
                                if (message.type === 'connection') {
                                    addDebugLog(`连接确认: ${message.message}`);
                                    statusElement.textContent = message.message;
                                } else if (message.type === 'status') {
                                    addDebugLog(`状态更新: ${message.message}`);
                                    if (message.message === '转码已开始') {
                                        statusElement.textContent = '正在播放';
                                    } else if (message.message === '转码已停止') {
                                        statusElement.textContent = '已停止';
                                        // 如果正在播放，自动停止
                                        if (isPlaying) {
                                            stopStreaming();
                                        }
                                    }
                                } else if (message.type === 'error') {
                                    addDebugLog(`错误: ${message.message}`);
                                    showError(message.message);
                                } else if (message.type === 'heartbeat') {
                                    addDebugLog(`心跳确认: ${message.timestamp}`);
                                }
                            } catch (e) {
                                addDebugLog(`解析消息失败: ${e}`);
                            }
                        } else if (event.data instanceof ArrayBuffer) {
                            // 限制队列大小，防止内存溢出
                            if (audioQueue.length > 50) {
                                audioQueue = audioQueue.slice(-30);
                                addDebugLog('音频队列过长，已清理');
                            }
                            
                            audioQueue.push(event.data);
                            processQueue();
                        }
                    };
                    
                    socket.onclose = () => {
                        addDebugLog('WebSocket连接已关闭');
                        statusElement.textContent = '连接已断开';
                        updateConnectionStatus(false);
                        stopHeartbeat();
                        
                        if (isPlaying) {
                            // 尝试重新连接
                            addDebugLog('尝试重新连接...');
                            setTimeout(connectWebSocket, 3000);
                        }
                    };
                    
                    socket.onerror = (error) => {
                        addDebugLog(`WebSocket错误: ${error}`);
                        showError('连接错误，请检查服务器状态');
                        updateConnectionStatus(false);
                    };
                    
                } catch (e) {
                    addDebugLog(`连接失败: ${e}`);
                    showError(`连接失败: ${e.message}`);
                    updateConnectionStatus(false);
                }
            }
            
            // 开始播放
            function startStreaming() {
                addDebugLog('开始播放');
                debugLog = []; // 清空日志
                
                const radioUrl = radioUrlInput.value.trim();
                if (!radioUrl) {
                    showError('请输入电台URL');
                    return;
                }
                
                if (!initAudioContext()) {
                    return;
                }
                
                connectWebSocket();
                
                isPlaying = true;
                playButton.disabled = true;
                stopButton.disabled = false;
                statusElement.textContent = '连接中...';
                
                // 连接成功后发送播放命令
                const sendPlayCommand = () => {
                    if (socket && socket.readyState === WebSocket.OPEN) {
                        try {
                            socket.send(JSON.stringify({
                                type: 'play_radio_event',
                                event: 1,
                                radio_url: radioUrl
                            }));
                            addDebugLog(`发送播放命令: ${radioUrl}`);
                        } catch (e) {
                            addDebugLog(`发送播放命令失败: ${e}`);
                            // 延迟后重试
                            setTimeout(sendPlayCommand, 1000);
                        }
                    } else {
                        // 如果连接还未建立，延迟后重试
                        setTimeout(sendPlayCommand, 500);
                    }
                };
                
                sendPlayCommand();
            }
            
            // 停止播放
            function stopStreaming() {
                addDebugLog('停止播放');
                
                // 立即停止音频播放
                if (audio) {
                    audio.pause();
                    audio.currentTime = 0;
                    // 清空音频源，立即停止播放
                    audio.src = '';
                    audio = null;
                }
                
                // 停止播放时长计时
                stopPlayTimeCounter();
                
                // 发送停止命令
                if (socket && socket.readyState === WebSocket.OPEN) {
                    try {
                        socket.send(JSON.stringify({
                            type: 'play_radio_event',
                            event: 0
                        }));
                        addDebugLog('发送停止命令');
                    } catch (e) {
                        addDebugLog(`发送停止命令失败: ${e}`);
                    }
                }
                
                isPlaying = false;
                stopHeartbeat();
                
                if (socket) {
                    socket.close();
                    socket = null;
                }
                
                // 清理MediaSource和SourceBuffer
                if (sourceBuffer) {
                    try {
                        // 清空缓冲区
                        if (sourceBuffer.buffered.length > 0) {
                            const start = sourceBuffer.buffered.start(0);
                            const end = sourceBuffer.buffered.end(sourceBuffer.buffered.length - 1);
                            sourceBuffer.remove(start, end);
                        }
                    } catch (e) {
                        addDebugLog(`清理SourceBuffer失败: ${e}`);
                    }
                    sourceBuffer = null;
                }
                
                if (mediaSource && mediaSource.readyState === 'open') {
                    try {
                        mediaSource.endOfStream();
                    } catch (e) {
                        addDebugLog(`结束MediaSource失败: ${e}`);
                    }
                    mediaSource = null;
                }
                
                if (audioContext) {
                    audioContext.close().catch(e => console.error('关闭音频上下文失败:', e));
                    audioContext = null;
                }
                
                playButton.disabled = false;
                stopButton.disabled = true;
                statusElement.textContent = '已停止';
                updateConnectionStatus(false);
                bufferProgress.style.width = '0%';
                audioQueue = []; // 清空音频队列
                isBuffering = false;
            }
            
            // 显示错误信息
            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.style.display = 'block';
            }
            
            // 隐藏错误信息
            function hideError() {
                errorMessage.style.display = 'none';
            }
            
            // 事件监听器
            playButton.addEventListener('click', startStreaming);
            stopButton.addEventListener('click', stopStreaming);
            testConnectionButton.addEventListener('click', testConnection);
            
            // 配置变更时自动保存
            wsHostInput.addEventListener('input', saveConfig);
            wsPortInput.addEventListener('input', saveConfig);
            radioUrlInput.addEventListener('input', saveConfig);
            
            // 页面加载时初始化
            loadConfig();
            
            // 页面卸载时清理资源
            window.addEventListener('beforeunload', () => {
                // 停止播放时长计时
                stopPlayTimeCounter();
                
                // 发送停止命令
                if (socket && socket.readyState === WebSocket.OPEN) {
                    try {
                        socket.send(JSON.stringify({
                            type: 'play_radio_event',
                            event: 0
                        }));
                    } catch (e) {
                        console.error('发送停止命令失败:', e);
                    }
                }
                
                // 立即停止音频播放
                if (audio) {
                    audio.pause();
                    audio.src = '';
                }
                
                if (socket) {
                    socket.close();
                }
                if (audioContext) {
                    audioContext.close();
                }
                stopHeartbeat();
            });
            
            // 定期检查缓冲区状态
            setInterval(() => {
                if (isPlaying) {
                    checkBufferStatus();
                }
            }, 1000);
        });
    </script>
</body>
</html>