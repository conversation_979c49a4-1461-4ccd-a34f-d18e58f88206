const WebSocket = require('ws');
const ffmpeg = require('child_process').spawn;
const { PassThrough } = require('stream');

// 创建WebSocket服务器
const wss = new WebSocket.Server({ 
    port: 8085,
    perMessageDeflate: false  // 禁用压缩，减少CPU开销
});

// 客户端连接管理
const clients = new Map(); // 使用Map来存储每个客户端的状态
const HEARTBEAT_TIMEOUT = 15000; // 15秒心跳超时

// 客户端状态类
class ClientSession {
    constructor(ws) {
        this.ws = ws;
        this.ffmpegProcess = null;
        this.audioStream = null;
        this.currentRadioUrl = null;
        this.lastHeartbeatTime = Date.now();
        this.dataHandler = null;
    }

    // 启动FFmpeg进程
    startFFmpeg(radioUrl) {
        // 如果已有进程，先完全清理
        if (this.ffmpegProcess || this.audioStream) {
            console.log(`客户端 ${this.getClientId()} 清理现有资源`);
            this.stopFFmpeg();
            // 给一点时间让清理完成
            setTimeout(() => this._doStartFFmpeg(radioUrl), 100);
            return;
        }
        
        this._doStartFFmpeg(radioUrl);
    }

    // 实际启动FFmpeg进程的方法
    _doStartFFmpeg(radioUrl) {
        // 保存当前电台URL
        this.currentRadioUrl = radioUrl;
        console.log(`客户端 ${this.getClientId()} 启动FFmpeg进程，源URL: ${radioUrl}`);

        // 优化后的FFmpeg参数
        const args = [
            // 日志级别设置为error，减少日志输出
            '-loglevel', 'error',
            // 输入选项
            '-fflags', '+genpts+discardcorrupt',  // 生成时间戳，丢弃损坏的数据
            '-thread_queue_size', '512',         // 增加线程队列大小
            '-analyzeduration', '1000000',       // 分析持续时间1秒
            '-probesize', '1000000',             // 探测大小1MB
            // 输入源
            '-i', radioUrl,
            // 重连选项
            '-reconnect', '1',
            '-reconnect_at_eof', '1',
            '-reconnect_streamed', '1',
            '-reconnect_delay_max', '5',
            // 超时设置
            '-timeout', '10000000',              // 10秒超时
            '-stimeout', '10000000',             // 10秒流超时
            // 音频编码选项
            '-vn',                               // 无视频
            '-c:a', 'libmp3lame',                // 使用MP3编码
            '-b:a', '64k',                      // 比特率
            '-ar', '44100',                      // 采样率
            '-ac', '2',                          // 声道数
            // 缓冲区设置
            '-bufsize', '256k',                  // 增加缓冲区大小
            '-max_muxing_queue_size', '1024',    // 最大复用队列大小
            // 输出选项
            '-flush_packets', '1',               // 立即刷新包
            '-f', 'mp3',                         // 输出格式
            '-movflags', '+faststart',           // 快速启动
            '-'                                  // 输出到标准输出
        ];

        try {
            this.ffmpegProcess = ffmpeg('ffmpeg', args);
            
            // 创建音频流
            this.audioStream = new PassThrough({
                highWaterMark: 1024 * 1024  // 1MB缓冲区
            });

            // 设置数据处理器
            this.dataHandler = (chunk) => {
                if (this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send(chunk, { binary: true });
                }
            };

            // 处理FFmpeg输出
            this.ffmpegProcess.stdout.on('data', (chunk) => {
                // 检查音频流是否仍然存在，防止竞态条件
                if (!this.audioStream) {
                    console.log(`客户端 ${this.getClientId()} 音频流已清理，忽略数据块`);
                    return;
                }
                
                // 将数据传递给音频流
                try {
                    if (!this.audioStream.write(chunk)) {
                        // 如果缓冲区满了，暂停读取
                        if (this.ffmpegProcess && this.ffmpegProcess.stdout) {
                            this.ffmpegProcess.stdout.pause();
                        }
                        
                        // 再次检查音频流是否存在
                        if (this.audioStream) {
                            this.audioStream.once('drain', () => {
                                if (this.ffmpegProcess && this.ffmpegProcess.stdout) {
                                    this.ffmpegProcess.stdout.resume();
                                }
                            });
                        }
                    }
                } catch (err) {
                    console.error(`客户端 ${this.getClientId()} 写入音频流失败:`, err);
                }
            });

            // 添加数据监听器
            this.audioStream.on('data', this.dataHandler);

            // 处理错误
            this.ffmpegProcess.stderr.on('data', (data) => {
                // 只记录真正的错误，忽略HLS相关的警告
                const errorStr = data.toString();
                if (errorStr.includes('error') || errorStr.includes('Error') || 
                    errorStr.includes('failed') || errorStr.includes('Failed')) {
                    console.error(`客户端 ${this.getClientId()} FFmpeg错误: ${errorStr}`);
                }
            });

            // 处理进程退出
            this.ffmpegProcess.on('close', (code) => {
                console.log(`客户端 ${this.getClientId()} FFmpeg进程退出，代码: ${code}`);
                
                // 避免重复处理
                if (!this.ffmpegProcess) {
                    return;
                }
                
                this.ffmpegProcess = null;
                
                // 通知客户端
                this.sendMessage({ 
                    type: 'status', 
                    message: 'FFmpeg进程已退出',
                    code: code
                });
                
                // 1秒后断开客户端连接并清理资源
                setTimeout(() => {
                    console.log(`客户端 ${this.getClientId()} FFmpeg进程退出，1秒后断开连接`);
                    this.disconnectClient();
                }, 1000);
            });

            // 处理进程错误
            this.ffmpegProcess.on('error', (err) => {
                console.error(`客户端 ${this.getClientId()} FFmpeg进程错误: ${err}`);
                
                // 避免重复处理
                if (!this.ffmpegProcess) {
                    return;
                }
                
                this.ffmpegProcess = null;
                
                // 通知客户端
                this.sendMessage({ 
                    type: 'error', 
                    message: 'FFmpeg进程错误',
                    error: err.message
                });
                
                // 1秒后断开客户端连接并清理资源
                setTimeout(() => {
                    console.log(`客户端 ${this.getClientId()} FFmpeg进程错误，1秒后断开连接`);
                    this.disconnectClient();
                }, 1000);
            });

            // 通知客户端转码已开始
            this.sendMessage({ 
                type: 'status', 
                message: '转码已开始',
                url: radioUrl
            });

        } catch (err) {
            console.error(`客户端 ${this.getClientId()} 启动FFmpeg失败:`, err);
            this.ffmpegProcess = null;
            // 通知客户端启动失败
            this.sendMessage({ 
                type: 'error', 
                message: '启动FFmpeg失败',
                error: err.message
            });
        }
    }

    // 停止FFmpeg进程
    stopFFmpeg() {
        console.log(`客户端 ${this.getClientId()} 停止FFmpeg进程`);
        
        // 先清理音频流，防止FFmpeg进程继续写入
        if (this.audioStream && this.dataHandler) {
            this.audioStream.removeListener('data', this.dataHandler);
            this.dataHandler = null;
        }
        
        // 销毁音频流
        if (this.audioStream) {
            try {
                this.audioStream.destroy();
            } catch (err) {
                console.error(`客户端 ${this.getClientId()} 销毁音频流失败:`, err);
            }
            this.audioStream = null;
        }
        
        // 然后停止FFmpeg进程
        if (this.ffmpegProcess) {
            try {
                this.ffmpegProcess.kill('SIGTERM');
            } catch (err) {
                console.error(`客户端 ${this.getClientId()} 终止FFmpeg进程失败:`, err);
            }
            this.ffmpegProcess = null;
        }
        
        this.currentRadioUrl = null;
        
        // 通知客户端转码已停止
        this.sendMessage({ 
            type: 'status', 
            message: '转码已停止'
        });
    }

    // 发送消息给客户端
    sendMessage(data) {
        if (this.ws.readyState === WebSocket.OPEN) {
            try {
                this.ws.send(JSON.stringify(data));
            } catch (err) {
                console.error(`发送消息到客户端 ${this.getClientId()} 失败:`, err);
            }
        }
    }

    // 获取客户端ID（用于日志）
    getClientId() {
        return this.ws._socket ? `${this.ws._socket.remoteAddress}:${this.ws._socket.remotePort}` : 'unknown';
    }

    // 断开客户端连接并清理资源
    disconnectClient() {
        console.log(`主动断开客户端 ${this.getClientId()} 连接`);
        
        try {
            // 清理所有资源
            this.cleanup();
            
            // 从客户端列表中移除
            if (clients.has(this.ws)) {
                clients.delete(this.ws);
            }
            
            // 关闭WebSocket连接
            if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
                this.ws.close(1000, 'FFmpeg进程异常，服务器主动断开连接');
            }
        } catch (err) {
            console.error(`断开客户端 ${this.getClientId()} 连接时发生错误:`, err);
        }
    }

    // 清理资源
    cleanup() {
        console.log(`客户端 ${this.getClientId()} 清理资源`);
        
        // 停止FFmpeg进程（这会清理音频流）
        this.stopFFmpeg();
        
        // 确保所有资源都被清理
        if (this.audioStream) {
            try {
                this.audioStream.destroy();
            } catch (err) {
                console.error(`客户端 ${this.getClientId()} 清理时销毁音频流失败:`, err);
            }
            this.audioStream = null;
        }
        
        if (this.dataHandler) {
            this.dataHandler = null;
        }
        
        this.currentRadioUrl = null;
    }

    // 检查心跳超时
    checkHeartbeatTimeout() {
        const now = Date.now();
        if (now - this.lastHeartbeatTime > HEARTBEAT_TIMEOUT && this.ffmpegProcess) {
            console.log(`客户端 ${this.getClientId()} 心跳超时，停止FFmpeg进程`);
            this.stopFFmpeg();
        }
    }
}
// 处理新的客户端连接
wss.on('connection', (ws) => {
    console.log('新的客户端连接');
    
    // 创建客户端会话
    const clientSession = new ClientSession(ws);
    clients.set(ws, clientSession);
    
    // 发送连接确认
    clientSession.sendMessage({ 
        type: 'connection', 
        message: '连接成功',
        status: '空闲'
    });

    // 处理客户端消息
    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);
            console.log(`收到客户端 ${clientSession.getClientId()} 消息:`, data);
            
            // 处理播放命令
            if (data.type === 'play_radio_event') {
                if (data.event === 1 && data.radio_url) {
                    // 开始转码
                    clientSession.startFFmpeg(data.radio_url);
                } else if (data.event === 0) {
                    // 停止转码
                    clientSession.stopFFmpeg();
                }
            }
            // 处理心跳包
            else if (data.type === 'play_radio_heartbeat') {
                clientSession.lastHeartbeatTime = Date.now();
                // 回复心跳确认
                clientSession.sendMessage({ 
                    type: 'heartbeat', 
                    timestamp: clientSession.lastHeartbeatTime
                });
            }
        } catch (err) {
            console.error(`解析客户端 ${clientSession.getClientId()} 消息失败:`, err);
        }
    });

    // 处理连接关闭
    ws.on('close', () => {
        console.log(`客户端 ${clientSession.getClientId()} 断开连接`);
        clientSession.cleanup();
        clients.delete(ws);
    });

    // 处理错误
    ws.on('error', (err) => {
        console.error(`客户端 ${clientSession.getClientId()} WebSocket错误:`, err);
        clientSession.cleanup();
        clients.delete(ws);
    });
});

// 处理服务器错误
wss.on('error', (err) => {
    console.error('WebSocket服务器错误:', err);
});

// 启动心跳检查定时器
const heartbeatInterval = setInterval(() => {
    clients.forEach((clientSession) => {
        clientSession.checkHeartbeatTimeout();
    });
}, 5000);

// 优雅关闭
process.on('SIGINT', () => {
    console.log('收到SIGINT，正在关闭服务器...');
    
    // 清除心跳检查定时器
    if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
    }
    
    // 关闭所有客户端连接和FFmpeg进程
    clients.forEach((clientSession, ws) => {
        clientSession.cleanup();
        if (ws.readyState === WebSocket.OPEN) {
            ws.close();
        }
    });
    
    // 关闭WebSocket服务器
    wss.close(() => {
        console.log('WebSocket服务器已关闭');
        process.exit(0);
    });
});

// 处理未捕获的异常
process.on('uncaughtException', (err) => {
    console.error('未捕获的异常:', err);
    // 尝试恢复 - 清理所有客户端的FFmpeg进程
    clients.forEach((clientSession) => {
        clientSession.cleanup();
    });
});

console.log('WebSocket服务器已启动，监听端口: 8085');
console.log('等待客户端发送播放命令...');
console.log('每个客户端连接现在可以独立请求转码');