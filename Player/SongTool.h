#ifndef SONGTOOL_H
#define SONGTOOL_H


#include "Global/CType.h"
#include "SongUnit.h"


class CSongTool
{
public:
    CSongTool();
    virtual ~CSongTool(void);

public:
    // 歌曲时间长度
    SongHeader	GetSongInfo(CMyString strPathName);

    // 歌曲大小（字节）
    ULONG	GetSize(CMyString strPathName);

    // 是否为可推送格式（MP3或者WAV）
    BOOL	IsStreamFormat(CMyString strPathName);
    // 是否存在本地
    BOOL	IsPathExist(CMyString  strPathName);

};

#endif // SONGTOOL_H
