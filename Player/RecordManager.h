#ifndef RECORDMANAGER_H
#define RECORDMANAGER_H

#include <iostream>
#include <map>
#include <vector>
#include <memory>
#include <thread>
#include <mutex>
#include <atomic>
#include <queue>
#include <condition_variable>
#include "Global/Const.h"
#include "Global/CType.h"
#include "Tools/tools.h"
#include "Tools/G722/g722_decoder.h"
#include "Database/RecordTable.h"

#if defined(Q_OS_LINUX)
#include <mpg123.h>
#else
#include "mpg123.h"
#endif

#if SUPPORT_CALL_RECORD

// 录音配置
#define RECORD_SAMPLE_RATE          16000
#define RECORD_BIT_DEPTH            16
#define RECORD_CHANNELS             1
#define RECORD_FRAME_SIZE           512
#define RECORD_BUFFER_SIZE          8192

// 录音文件目录配置
#define RECORD_ROOT_DIR             "Records"
#define RECORD_DAILY_SUBDIR_FORMAT  "%Y%m%d"
#define RECORD_HOURLY_SUBDIR_FORMAT "%H"
#define RECORD_FILE_PREFIX          "REC"
#define RECORD_FILE_EXT             ".mp3"

#define MAX_RECORD_SESSIONS         32      // 最大同时录音会话数
#define AUDIO_BUFFER_SIZE           4096    // 音频缓冲区大小
#define RECORD_FILE_DIR             "Record" // 录音文件目录

// 音频数据缓冲区
typedef struct
{
    unsigned char data[AUDIO_BUFFER_SIZE];
    int length;
    unsigned long timestamp;
} AudioBuffer;

// 录音会话信息
typedef struct
{
    CMyString       strCallId;          // 通话ID
    CMyString       strCallerMac;       // 主叫方MAC
    CMyString       strCallerName;      // 主叫方名称
    CMyString       strCalleeList;      // 被叫方列表
    RecordType      recordType;         // 录音类型
    AudioFormat     audioFormat;        // 音频格式
    int             nSampleRate;        // 采样率
    int             nChannels;          // 声道数
    int             nBitRate;           // 比特率
    
    bool            bIsRecording;       // 是否正在录音
    unsigned long   ulStartTime;       // 开始时间戳
    unsigned long   ulEndTime;         // 结束时间戳
    
    CMyString       strTempFile;        // 临时文件路径
    CMyString       strFinalFile;       // 最终文件路径
    FILE*           pTempFile;          // 临时文件句柄
    
    G722_DEC_CTX*   pG722Decoder;       // G.722解码器
    
    std::queue<AudioBuffer> audioQueue; // 音频数据队列
    std::mutex      queueMutex;         // 队列互斥锁
    
} RecordSession;

class CRecordManager
{
public:
    CRecordManager();
    ~CRecordManager();

public:
    // 初始化录音管理器
    bool Initialize();
    
    // 清理录音管理器
    void Cleanup();
    
    // 开始录音
    bool StartRecord(const CMyString& strCallId, 
                     const CMyString& strCallerMac,
                     const CMyString& strCallerName,
                     const CMyString& strCalleeList,
                     RecordType recordType,
                     AudioFormat audioFormat = AF_PCM,
                     int nSampleRate = 32000,
                     int nChannels = 1,
                     int nBitRate = 64000);
    
    // 结束录音
    bool EndRecord(const CMyString& strCallId, RecordStatus recordStatus = RS_COMPLETED);
    
    // 根据MAC地址前缀停止录音（用于寻呼停止）
    bool StopRecordByMacPrefix(const CMyString& strMacPrefix, RecordStatus recordStatus = RS_COMPLETED);
    
    // 添加音频数据
    bool AddAudioData(const CMyString& strCallId, const unsigned char* pData, int nLength);
    
    // 检查录音会话是否存在
    bool IsRecordSessionExists(const CMyString& strCallId);
    
    // 获取录音会话信息
    bool GetRecordSession(const CMyString& strCallId, RecordSession& session);
    
    // 获取匹配指定前缀的录音会话ID列表
    std::vector<CMyString> GetRecordSessionsByPrefix(const CMyString& strPrefix);
    
    // 设置数据库管理器
    void SetDbManager(CDataBase* pDatabase);

    // 清理过期录音文件
    void CleanupExpiredRecords();

    // 获取录音文件路径
    static CMyString GetRecordFilePath(const CTime& time, const CMyString& strCallId);

    // 获取录音文件目录
    static CMyString GetRecordDirectory(const CTime& time);

    // 创建录音目录
    bool CreateRecordDirectory(const CMyString& strDir);
    
    // 获取当前时间戳（毫秒）
    unsigned long GetCurrentTimestamp();

private:
    // 创建录音会话
    bool CreateRecordSession(const CMyString& strCallId,
                             const CMyString& strCallerMac,
                             const CMyString& strCallerName,
                             const CMyString& strCalleeList,
                             RecordType recordType,
                             AudioFormat audioFormat,
                             int nSampleRate,
                             int nChannels,
                             int nBitRate);
    
    // 销毁录音会话
    void DestroyRecordSession(const CMyString& strCallId);
    
    // 生成录音文件路径
    CMyString GenerateRecordFilePath(const CMyString& strCallId, AudioFormat audioFormat);
    
    // 音频处理线程
    static void AudioProcessThread(CRecordManager* pManager);
    
    // 处理音频数据
    void ProcessAudioData();
    
    // 处理单个会话的音频数据
    void ProcessSessionAudio(RecordSession& session);
    
    // G.722音频解码
    bool DecodeG722Audio(RecordSession& session, const unsigned char* pInputData, int nInputLength,
                         unsigned char* pOutputData, int& nOutputLength);
    
    // 将PCM转换为MP3
    bool ConvertPCMToMP3(const CMyString& strPCMFile, const CMyString& strMP3File,
                         int nSampleRate, int nChannels, int nBitRate);
    
    // 计算录音时长（秒）
    int CalculateRecordDuration(unsigned long ulStartTime, unsigned long ulEndTime);
    
    // 将CallId中的冒号替换为下划线，以兼容Windows文件系统
    static CMyString SanitizeCallIdForFilename(const CMyString& strCallId);

public:
    CRecordTable                        m_recordTable;      // 录音数据表
private:
    std::map<CMyString, RecordSession*> m_recordSessions;   // 录音会话映射
    std::mutex                          m_sessionMutex;     // 会话互斥锁
    
    bool                                m_bInitialized;     // 是否已初始化
    bool                                m_bRunning;         // 是否正在运行
    
    std::thread*                        m_pAudioThread;     // 音频处理线程
    std::condition_variable             m_audioCondition;   // 音频处理条件变量
    std::mutex                          m_audioMutex;       // 音频处理互斥锁
    
    CDataBase*                          m_pDatabase;        // 数据库管理器
    
    CMyString                           m_strRecordDir;     // 录音文件根目录

    // MPG123相关
    bool                                                m_bMpg123Initialized;
};

#endif

#endif // RECORDMANAGER_H