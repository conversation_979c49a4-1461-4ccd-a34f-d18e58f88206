#include "stdafx.h"
#include "SongUnit.h"


/////////////////////////////////////////////////////////////////

CSongUnit::CSongUnit(int nPlayID)
{
    m_pUdpSocket	= NULL;
    m_nPlayID		= nPlayID;

    m_nPlayType     = 1;
    m_nPlayCount    = 1;

	#if defined(Q_OS_WIN32)
    QueryPerformanceFrequency(&m_frequency);
	#endif
    InitData();
    mpg123_init();

    m_nWavFileDataPos = sizeof(wave_t);

    m_csSongUnit = PTHREAD_MUTEX_INITIALIZER;

    b_multicast_use_new_cmd=false;
}


CSongUnit::~CSongUnit(void)
{
    // socket 保留，待修改
    if (m_pUdpSocket != NULL)
    {
        m_pUdpSocket->StopWorking();
        delete m_pUdpSocket;
        m_pUdpSocket = NULL;
    }

    FreePlay();
    mpg123_exit();

    //pthread_mutex_destroy(&m_csSongUnit);
}


void    CSongUnit::InitData()
{
    m_nList         = -1;
    m_nSong         = -1;
    m_playStatus    = SPS_NEXT;
    m_SongFormat    = FORMAT_OTHER;
    m_tStopPlay     = 0;
    m_nVolume       = -1;
    m_nSource       = SOURCE_STOP;
    m_mh            = NULL;

#if defined(Q_OS_LINUX) 
    m_timeStart     = 0;
    m_timePause     = 0;
#else
    m_timeStart.QuadPart = 0;
    m_timePause.QuadPart = 0;
#endif

    memset(m_streamBuf, 0,STREAM_BUF_SIZE);
}

// 开始播放 保留，待修改测试 zhuyg
bool CSongUnit::InitPlay(CMyString	strPathName)
{
    bool isOK	= FALSE;
    bool bInit	= FALSE;

    InitData();

    m_strPathName	= strPathName;
    m_SongFormat	= GetSongFormat();

    if (m_SongFormat == FORMAT_OTHER)
    {
        NOTIFY("m_SongFormat == FORMAT_OTHER");
        g_Global.m_Network.AddLog("m_SongFormat == FORMAT_OTHER");
        return FALSE;
    }

    if(IS_SYSTEM_IN_CLOUD)
    {
        CMyString strlbrcPathName=strPathName+SONG_LOW_RATE_EXT_NAME;
        if(IsExistFile(strlbrcPathName.Data()))
        {
            printf("InitPlay:%s\n",strlbrcPathName.Data());
            strPathName=strlbrcPathName;
        }
    }

    //从songlist里面获取md5的值
    //去除http路径前缀
    CMyString strPathName2 = strPathName;
    CMyString zeroChar="";
    strPathName2.Replace(g_Global.m_strHttpRootDir,zeroChar);
    //strPathNameS.erase(std::remove(strPathNameS.begin(), strPathNameS.end(), g_Global.m_strHttpRootDir));
    m_strSongMd5 = g_Global.m_SongManager.GetMd5ByPathName(strPathName2.C_Str());
    
    if(m_strSongMd5.length() == 0)
    {
        m_strSongMd5 = GetFileMd5(strPathName.Data());
    }
    printf("m_nPlayID=%d,strPathName=%s,Md5=%s\n",m_nPlayID,strPathName.Data(),m_strSongMd5.data());
    


    m_uPort = 50000 + m_nPlayID;
	#if defined(Q_OS_LINUX)
    sprintf(m_szMulticastIP, "230.230.232.%d", m_nPlayID);
	#else
	sprintf(m_szMulticastIP, "230.230.233.%d", m_nPlayID);
	#endif

    #if IS_BACKUP_SERVER
    sprintf(m_szMulticastIP, "230.230.234.%d", m_nPlayID);
    #endif

    //打开音频文件
    if(m_fileSong.Open(strPathName.Data(),"rb"))
    {
        // WAV文件
        if (m_SongFormat == FORMAT_WAV)
        {
            bInit = InitWav(strPathName);
        }
        // MP3文件
        else
        {
            bInit = InitMP3(strPathName);
        }

        if (bInit)
        {
            bInit = IsValidParam(m_SongHeader);
        }

        if (bInit)
        {
            // 初始化播放状态
            m_nPlayedFrames = 0;
            //m_playStatus = SPS_STOP;


            if(m_pUdpSocket == NULL)
            {
#if 0
                m_pUdpSocket = new CUDPSocket();

                if(!m_pUdpSocket->CreateSocket(0, NULL, NULL))	// 不绑定端口
                {
                    delete m_pUdpSocket;
                    m_pUdpSocket = NULL;
                    isOK = FALSE;

                    NOTIFY("------m_pUdpSocket->CreateSocket failed");
                    g_Global.m_Network.AddLog("------m_pUdpSocket->CreateSocket failed");
                }
                else
                {
                    // socket 保留，待修改
                    isOK = m_pUdpSocket->JoinMulticast(m_szMulticastIP);
                }
#endif
                m_pUdpSocket = new MyUdpNode();
                isOK=m_pUdpSocket->StartWorking(g_Global.m_szNetworkIP,0,CM_UNICAST,NULL,m_szMulticastIP,1);
            }
            else
            {
                isOK = TRUE;
            }
        }
        else
        {
            NOTIFY("------bInit = false");
            g_Global.m_Network.AddLog("------bInit = false");
        }
        
        if (m_SongFormat != FORMAT_WAV)
        {
            m_fileSong.Close();
        }
    }
    else
    {
        CMyString strLog;
        strLog.Format("-----open file %s failed", strPathName.Data());
        NOTIFY(strLog.C_Str());
        g_Global.m_Network.AddLog(strLog);
    }

    if (!isOK)
    {
        NOTIFY("------isOK = false");
        g_Global.m_Network.AddLog("------isOK = false");
    }

    return isOK;

}

void    CSongUnit::FreePlay()
{
    // 只有mp3格式的时候，m_mh才不会为NULL
    pthread_mutex_lock(&m_csSongUnit);
    if (m_mh != NULL)
    {
        //一些清理工作，顺序不要颠倒
        mpg123_close(m_mh);
        mpg123_delete(m_mh);

        m_mh = NULL;
    }
    pthread_mutex_unlock(&m_csSongUnit);
}


// 设置播放状态 保留，定时器，待修改
void    CSongUnit::SetPlayStatus(SongPS status)
{
    //pthread_mutex_lock(&m_csSongUnit);

    if(status == SPS_PLAY)
    {
        if(m_playStatus == SPS_PAUSE_AUTO || m_playStatus == SPS_PAUSE_MANUAL)
        {
		#if defined(Q_OS_LINUX)
            CLxTimer currentTime;
            currentTime.GetCurrentTimeT();
            m_timeStart += (currentTime - m_timePause);
		#else
		    LARGE_INTEGER currentTime;
            QueryPerformanceCounter(&currentTime);
            m_timeStart.QuadPart += (currentTime.QuadPart - m_timePause.QuadPart);
		#endif

        }
        else
        {
            // 设置状态的时候，才开始计时
        }
    }
    else if(status == SPS_PAUSE_AUTO || status == SPS_PAUSE_MANUAL)
    {
        if(m_playStatus == SPS_PLAY)
        {
			#if defined(Q_OS_LINUX)
            m_timePause.GetCurrentTimeT();       // 获取停止时间
			#else
			QueryPerformanceCounter(&m_timePause);
			#endif
        }
    }
    else if(status == SPS_STOP || status == SPS_NEXT)
    {
        //保留，待修改 zhuyg
        //if(!m_fileSong.IsValid())
        #if 0
        CMyString strLog;
        strLog.Format("Close File : %s", m_strPathName.C_Str());
        g_Global.m_Network.AddLog(strLog);
        #endif
        FreePlay();
        if(m_fileSong.IsValid())
        {
            m_fileSong.Close();
        }
    }

    m_playStatus = status;

    //pthread_mutex_unlock(&m_csSongUnit);
}


void    CSongUnit::Replay(void)
{
    // 初始化播放状态
	#if defined(Q_OS_LINUX)
    m_timeStart.GetCurrentTimeT();       //保留，待修改 zhuyg
	#else
	QueryPerformanceCounter(&m_timeStart);	// 现在的时间为歌曲播放开始时间
	#endif
    m_nPlayedFrames = 0;

    if (m_SongFormat == FORMAT_MP3)     // mp3
    {
        mpg123_seek(m_mh, 0, SEEK_SET);
    }
    else                                // wav
    {
        m_nFileOffset = m_nWavFileDataPos;
    }

}

BYTE CSongUnit::GetVolume(void)
{
    return m_nVolume;
}

void CSongUnit::SetVolume(BYTE vol)
{
    m_nVolume = vol;
}

// 得到播放ID
int CSongUnit::GetPlayID()
{
    return m_nPlayID;
}

// 播放模式
int CSongUnit::GetPlayMode()
{
    return m_PlayMode;
}

// 播放模式
void CSongUnit::SetPlaymode(int playMode)
{
    m_PlayMode=playMode;
}

USHORT	CSongUnit::GetPlayPort()
{
    return m_uPort;
}

LPCSTR	CSongUnit::GetPlayIP()
{
    return m_szMulticastIP;
}

void CSongUnit::StartCounter()
{
	#if defined(Q_OS_LINUX)
    m_timeStart.GetCurrentTimeT();
	#else
	QueryPerformanceCounter(&m_timeStart);
	#endif
}

#if SUPPORT_MANUALTASK_PROGRESS
// 获取歌曲时间长度(S)
unsigned int CSongUnit::GetTotalDuration()
{
    return GetTotalFrames() * m_lfDurationPerFrame;
    //return m_SongHeader.nDuration;
}

unsigned int CSongUnit::GetCurrentPlayTime()
{
    return (unsigned int)(m_lfDurationPerFrame*m_nPlayedFrames);
}

bool  CSongUnit::SetUnitProgress(int playTime)
{
    NOTIFY("SetUnitProgress:playId=%d,status=%d,playTime=%d,duration=%d,totalFrames=%d,PerFrame=%f,m_nPlayedFrames=%d\n",
            GetPlayID(),m_playStatus,playTime,GetTotalDuration(),GetTotalFrames(),m_lfDurationPerFrame,m_nPlayedFrames);
    pthread_mutex_lock(&m_csSongUnit);
    //非播放状态不处理，播放时间不正确也不处理
    if(m_playStatus != SPS_PLAY || playTime<0 || playTime>GetTotalDuration())
    {
        pthread_mutex_unlock(&m_csSongUnit);
        return false;
    }

    //比如mp3，一帧=0.026s，那么我需要控制播放时间到10s，已经播放的帧数应该等于10/0.026s=384
    m_nPlayedFrames = playTime/m_lfDurationPerFrame;

    #if defined(Q_OS_LINUX)
        CLxTimer currentTime;
        currentTime.GetCurrentTimeT();
        CLxTimer interval_time;
        interval_time.m_timeval.tv_sec = playTime;
        interval_time.m_timeval.tv_nsec = 0;
        m_timeStart = (currentTime - interval_time);
    #else
        LARGE_INTEGER currentTime;
        QueryPerformanceCounter(&currentTime);
        LARGE_INTEGER intervalTime;
        intervalTime.QuadPart = playTime * m_frequency.QuadPart;
        m_timeStart.QuadPart = (currentTime.QuadPart - intervalTime.QuadPart);
    #endif

    // 调用 mpg123_seek 设置播放进度
    if(m_SongFormat == FORMAT_MP3)
    {
        long seekResult = mpg123_seek_frame(m_mh, m_nPlayedFrames, SEEK_SET);
        if (seekResult >= MPG123_OK) {
            printf("mpg123_seek ok...\n");
        } else {
            printf("mpg123_seek failed:%d...\n",seekResult);
        }
    }
    else
    {
        m_nFileOffset = m_nPlayedFrames*WAV_PER_FRAME_SIZE;	// 指向下一帧
    }

    printf("m_lfDurationPerFrame=%f,m_nPlayedFrames=%d\n",m_lfDurationPerFrame,m_nPlayedFrames);
    pthread_mutex_unlock(&m_csSongUnit);
    
    return true;
}

#endif


// 获取总共帧数
unsigned int CSongUnit::GetTotalFrames()
{
    return m_nTotalFramesNum;
}

// 歌曲当前帧数
unsigned int  CSongUnit::GetCurrentFrame()
{
    return  m_nCurrentFramesNum;
}

unsigned int CSongUnit::GetSongLength()
{
    return m_fileSong.GetLength();
}

string CSongUnit::GetSongMd5()
{
    return m_strSongMd5;
}


CMyString	CSongUnit::GetPathName(void)
{
    return m_strPathName;
}


// 得到歌曲格式
SongFormat	CSongUnit::GetSongFormat()
{
    SongFormat songFormat;

    CMyString strName=m_strPathName;
    if(IS_SYSTEM_IN_CLOUD)
    {
        CMyString strlbrcPathName=strName+SONG_LOW_RATE_EXT_NAME;
        if(IsExistFile(strlbrcPathName.Data()))
        {
            strName=strlbrcPathName;
        }
    }
    CMyString strExtension = strName.Mid(strName.ReverseFind(('.'))+1);
    strExtension.MakeUpper();

    if (strExtension == ("MP3"))
    {
        songFormat = FORMAT_MP3;
    }
    else if (strExtension == ("WAV"))
    {
        songFormat = FORMAT_WAV;
    }
    else
    {
        songFormat = FORMAT_OTHER;
    }

    return songFormat;
}


// 得到歌曲文件头信息
song_header_t CSongUnit::GetSongHeader()
{
    return m_SongHeader;
}

// 得到播放列表文件
PlayFrom CSongUnit::GetPlayFrom()
{
    return m_PlayFrom;
}

void CSongUnit::SetPlayFrom(PlayFrom playFrom)
{
    m_PlayFrom = playFrom;
}

void CSongUnit::SetUserAccount(string strUserAccount)
{
    if( g_Global.m_Users.IsExistUser(strUserAccount) )
    {
        m_strUserAccount=strUserAccount;
    }
}


// 设置歌曲位置
void CSongUnit::SetSongPos(int nList, int nSong)
{
    m_nList = nList, m_nSong = nSong;
}

int CSongUnit::GetListIndex()
{
    return m_nList;
}

int CSongUnit::GetSongIndex()
{
    return m_nSong;
}

void CSongUnit::SetStopPlayTime(ctime_t tStopPlay)
{
    m_tStopPlay = tStopPlay;
}

ctime_t  CSongUnit::GetStopPlayTime()
{
    return m_tStopPlay;
}

unsigned char CSongUnit::GetSource(void)
{
    return m_nSource;
}

void CSongUnit::SetSource(unsigned char src)
{
    m_nSource = src;
}

// 播放状态
SongPS CSongUnit::GetPlayStatus()
{
    return m_playStatus;
}

bool CSongUnit::IsValidParam(song_header_t header)
{
    unsigned int	samp_freq	= header.samp_freq;		// 采样频率
    SHORT	channels	= header.channels;		// 通道数，单声道为1，双声道为2
    SHORT	bit_samp	= header.bit_samp;		// bits per sample (又称量化位数)

    bool	isValid = TRUE;

    if(!(samp_freq == 8000	|| samp_freq == 11025 || samp_freq == 12000 ||
        samp_freq == 16000	|| samp_freq == 22050 || samp_freq == 24000 ||
        samp_freq == 32000	|| samp_freq == 44100 || samp_freq == 48000 ||
        samp_freq == 88200	|| samp_freq == 96000))
    {
        isValid = FALSE;
    }
    else if(!( channels == 1 || channels == 2))
    {
        isValid = FALSE;
    }
    else if(!(bit_samp == 16 || bit_samp == 24 || bit_samp == 32))
    {
        isValid = FALSE;
    }

    return isValid;
}


// 查找子数组
int FindDataPos(const char *data, int datalen, const char *subData, int sublen)
{
    int dataPos = 0;
    //int subPos = 0;

    while (dataPos < datalen)
    {
        int i = 0;
        for (i=0; i<sublen; ++i)
        {
            if (data[dataPos+i] != subData[i])
            {
                break;
            }
        }

        if (i == sublen)
        {
            return dataPos + 8; // "data" + size + data
        }

        dataPos++;
    }

    return -1;
}


bool CSongUnit::InitWav(CMyString	strPathName)
{
    wave_t w;
    // ? 保留，待修改
    int nRead = m_fileSong.Read(&w, sizeof(wave_t));

    m_SongHeader.samp_freq	= w.header.samp_freq;	// 采样率
    m_SongHeader.channels	= w.header.channels;	// 声道数
    m_SongHeader.bit_samp	= w.header.bit_samp;	// 位数

    // 歌曲时间长度（S）
    int time = w.length/w.header.byte_rate;

    // 采样每帧所需要的时间（S）
    m_lfDurationPerFrame = 1.0 * WAV_PER_FRAME_SIZE/w.header.byte_rate;

    // 每帧多少个字节
    m_nBytesPerFrame = WAV_PER_FRAME_SIZE;

    // 记下偏移量
    //m_nFileOffset = sizeof(wave_t);

    // WAV的数据位置不是固定的，44/78/146
    m_fileSong.SeekToBegin();
    char header[1001] = {0};
    m_fileSong.Read(header, 1000);

    int dataPos = FindDataPos(header, 1000, "data", 4);
    if (dataPos >= 0)
    {
        m_nWavFileDataPos = dataPos;
    }

    m_nFileOffset = m_nWavFileDataPos;


    //获取总帧数
    m_nTotalFramesNum = (unsigned int)(m_fileSong.GetLength() / m_nBytesPerFrame);
    m_nTotalFramesNum = (m_nTotalFramesNum>=5)?m_nTotalFramesNum-5:0;
    printf("TotalFramesNum=%d\n",m_nTotalFramesNum);
    m_nCurrentFramesNum=0;

    return TRUE;
}


bool CSongUnit::InitMP3(CMyString	strPathName)
{
    int ret;
    m_mh = mpg123_new(NULL, &ret);

    if (MPG123_OK != ret)
    {
        g_Global.m_Network.AddLog("MPG123_OK != ret");
        NOTIFY("MPG123_OK != ret");
        return FALSE;
    }

    //检测格式是否正确
    if (MPG123_OK != mpg123_open(m_mh, strPathName.Data()))
    {
        g_Global.m_Network.AddLog("MPG123_OK != mpg123_open");
        NOTIFY("MPG123_OK != mpg123_open");
        return FALSE;
    }

    //获取总帧数,获取完需要close并重新OPEN mpg123,否则异常
    mpg123_seek( m_mh, 0, SEEK_END );
    m_nTotalFramesNum = mpg123_tellframe( m_mh ); //获取总帧数
    printf("TotalFramesNum=%d\n",m_nTotalFramesNum);
    //mpg123_seek( m_mh, 0, SEEK_SET );
    m_nCurrentFramesNum=0;

    mpg123_close(m_mh);

    if (MPG123_OK != mpg123_open(m_mh, strPathName.Data()))
    {
        g_Global.m_Network.AddLog("MPG123_OK != mpg123_open");
        NOTIFY("MPG123_OK != mpg123_open");
        return FALSE;
    }


    long rate;
    int	channels;
    int encoding;

    if (MPG123_OK != mpg123_param(m_mh, MPG123_REMOVE_FLAGS, MPG123_IGNORE_INFOFRAME, 0.))
    {
        g_Global.m_Network.AddLog("MPG123_OK != mpg123_param");
        NOTIFY("MPG123_OK != mpg123_param");
        return FALSE;
    }

    //获得每秒采样率，声道数，以及编码格式
    if (MPG123_OK != mpg123_getformat(m_mh, &rate, &channels, &encoding))
    {
        g_Global.m_Network.AddLog("MPG123_OK != mpg123_getformat");
        NOTIFY("MPG123_OK != mpg123_getformat");
        return FALSE;
    }

    //mpg123只支持这两种MP3编码格式，已经够了，大部分就是这样的
    if(encoding != MPG123_ENC_SIGNED_16 && encoding != MPG123_ENC_FLOAT_32)
    {
        CMyString strLog;
        strLog.Format("MPG123_OK encoding = %d", encoding);
        g_Global.m_Network.AddLog(strLog);
        NOTIFY(strLog.C_Str());
        return FALSE;
    }

    // 采样位数，采样值

    //1 字节(也就是8bit) 只能记录 256 个数, 也就是只能将振幅划分成 256 个等级;
    //2 字节(也就是16bit) 可以细到 65536 个数, 这已是 CD 标准了;
    //4 字节(也就是32bit) 能把振幅细分到 4294967296 个等级, 实在是没必要了.

    m_SongHeader.samp_freq	= rate;		// 采样率
    m_SongHeader.channels	= channels;	// 声道数
    m_SongHeader.bit_samp	= 16;		// 采样位数

    // 采样每帧所需要的时间（S）
    m_lfDurationPerFrame = mpg123_tpf(m_mh);
    NOTIFY("InitMP3:SongId=%d,PathName=%s,perFrame=%f",GetPlayID(),strPathName.Data(),m_lfDurationPerFrame);

    // 每帧多少个字节
    struct mpg123_frameinfo mi;
    mpg123_info(m_mh, &mi);
    m_nBytesPerFrame = mi.framesize;

    return TRUE;
}





#define HEAD_LEN 4

// 得到下一帧数据
bool CSongUnit::GetNextFrame(unsigned char ** pData, int *len)
{
    //bzero(m_streamBuf, STREAM_BUF_SIZE);

    if (m_SongFormat == FORMAT_MP3)
    {
        int ret = 0;
        pthread_mutex_lock(&m_csSongUnit);
        if(m_mh == NULL)
        {
           pthread_mutex_unlock(&m_csSongUnit);
           return FALSE;
        }
        while( m_nPlayedFrames == 0 || (ret = mpg123_framebyframe_next(m_mh)) == MPG123_OK || ret == MPG123_NEW_FORMAT )
        {
            unsigned long header;
            unsigned char *bodydata;
            size_t bodybytes;

            if(mpg123_framedata(m_mh, &header, &bodydata, &bodybytes) == MPG123_OK)
            {
                m_nCurrentFramesNum++;
                //printf("frame:%d\n",m_nCurrentFramesNum);
                // Need to extract the 4 header bytes from the native storage in the correct order.
                unsigned char hbuf[HEAD_LEN] = {0};
                for(int i=0; i<HEAD_LEN; ++i)
                {
                    hbuf[i] = (unsigned char) ((header >> ((3-i)*8)) & 0xff);
                }

                memcpy(m_streamBuf, hbuf, HEAD_LEN);
                memcpy(m_streamBuf + HEAD_LEN, bodydata, bodybytes);

                *len	= bodybytes + HEAD_LEN;
                *pData	= m_streamBuf;

                pthread_mutex_unlock(&m_csSongUnit);
                return TRUE;
            }
            else
            {
                pthread_mutex_unlock(&m_csSongUnit);
                return FALSE;
            }
        }
        pthread_mutex_unlock(&m_csSongUnit);
    }
    else if (m_SongFormat == FORMAT_WAV)
    {
        pthread_mutex_lock(&m_csSongUnit);
        // 读取文件数据
        m_fileSong.Seek(m_nFileOffset,  SEEK_SET);

        int frameLen = m_fileSong.Read(m_streamBuf, WAV_PER_FRAME_SIZE);

        if (frameLen > 0 && m_nFileOffset+WAV_PER_FRAME_SIZE*6 < m_fileSong.GetLength())
        {
            *len	= frameLen;
            *pData	= m_streamBuf;

            m_nFileOffset += frameLen;	// 指向下一帧

            m_nCurrentFramesNum++;
            //printf("frame:%d\n",m_nCurrentFramesNum);
            pthread_mutex_unlock(&m_csSongUnit);
            return TRUE;
        }
        else
        {
            pthread_mutex_unlock(&m_csSongUnit);
            return FALSE;
        }
    }

    return FALSE;
}

int CSongUnit::GetSpecifiedPlayNextSongIndex(int baseSongIndex)
{
    int nNextSong = baseSongIndex;
    if( !(GetSource() == SOURCE_SPECIFIED_PLAY || GetSource() == SOURCE_SPECIFIED_ALARM) )
    {
        return -1;
    }
    int songCount=m_vecStrPathNames.size();
    printf("GetSpecifiedPlayNextSongIndex:songCount=%d,playMode=%d\n",songCount,m_PlayMode);
    if(m_PlayMode == PM_ORDER || m_PlayMode == PM_LIST_CYCLE)      //顺序播放还要判断播放次数
    {
        ++nNextSong;
        if (nNextSong >= songCount)
        {
            if(m_PlayMode == PM_ORDER)
            {
                m_nPlayCount--;
                if(m_nPlayCount>0)
                {
                    nNextSong=0;
                }
                else
                {
                    nNextSong=-1;
                }
            }
            else if(m_PlayMode == PM_LIST_CYCLE)
            {
                nNextSong = 0;
            }
        }
    }
    else if(m_PlayMode == PM_RANDOM) //随机播放
    {
        //srand((unsigned int)time(NULL));
        struct timespec spec;
        clock_gettime(CLOCK_MONOTONIC, &spec);
        unsigned long long timeTv = (unsigned long long)spec.tv_sec * 1000000 + (unsigned long long)spec.tv_nsec / 1000;
        srand((unsigned int)(timeTv & 0xFFFFFFFF));  // 使用低32位作为种子

        nNextSong = rand()%songCount;
    }
    return nNextSong;
}

// 发送数据 保留，待修改
bool CSongUnit::SendStreamData(bool isUseMulticastNewCmd, unsigned int &nextUTime)
{
    //    if (m_fileSong.m_hFile == (HANDLE)0xffffffff)
    //    {
    //        return FALSE;
    //    }
    // zhuyg

    #if 1
    if(!m_fileSong.IsValid() && m_SongFormat == FORMAT_WAV)
    {
        return FALSE;
    }
    #endif

    BOOL isOK = TRUE;
    // 待测试修改 zhuyg
	#if defined(Q_OS_LINUX)
    CLxTimer curTime,t;
    curTime.GetCurrentTimeT();

    // 计算本次需要播放的帧数

    t = curTime - m_timeStart;
    //printf("ttt=%f\n",t.GetTimer());
    int frameCount = (int)(t.GetTimer()/m_lfDurationPerFrame - m_nPlayedFrames);
#if 0
    double kkkk1=t.GetTimer()/m_lfDurationPerFrame;
    int kkk=(int)kkkk1;
    if (kkkk1-kkk>=0.5)
    {
        kkk+=1;
    }
    int frameCount = (kkk-m_nPlayedFrames)>0?(kkk-m_nPlayedFrames):0;
    //if(frameCount!=frameCount_old)
        //printf("frameCount=%d,old=%d\n",frameCount,frameCount_old);
#endif
	#else
	LARGE_INTEGER curTime;
    QueryPerformanceCounter(&curTime);

    // 计算本次需要播放的帧数
    double dCurPlaySec = (curTime.QuadPart - m_timeStart.QuadPart) / (double)m_frequency.QuadPart;
    int frameCount = (int)(dCurPlaySec / m_lfDurationPerFrame - m_nPlayedFrames);
#if 0
    double kkkk1=(double)(curTime.QuadPart - m_timeStart.QuadPart)
        / (double)(m_frequency.QuadPart) / m_lfDurationPerFrame;
    int kkk=(int)kkkk1;
    if (kkkk1-kkk>=0.5)
    {
        kkk+=1;
    }
    int frameCount = (kkk-m_nPlayedFrames)>0?(kkk-m_nPlayedFrames):0;
#endif
	#endif

    LPBYTE  streamBuf = {0};
    int     streamBytes;

    /*************************************************************/
#if 1
    if(frameCount<-4)
    {
        isOK = FALSE;
    }
#endif

    for(int i = 0; i < frameCount; i++)
    {
        // 得到下一帧的数据
        if(GetNextFrame(&streamBuf, &streamBytes))
        {
            if (m_pUdpSocket != NULL)
            {
                
                #if 0
                    if (m_SongFormat == FORMAT_WAV)
                    {
                        char szBuf[MAX_BUF_LEN] = {0};
                        int	len	= CProtocol::ControlCommand(szBuf,CMD_STREAM_SOURCE,(const char*)streamBuf,streamBytes);
                        szBuf[2]  = m_nPlayedFrames;	// 包序号

                        if (m_pUdpSocket->SendData(szBuf, len, m_szMulticastIP, m_uPort, 1))
                        {
                            if (m_nPlayedFrames == 0)
                            {
                                g_Global.m_Network.AddLog("Start Send Frame : Multicast");
                            }
                        } 
                    }
                    else if (m_pUdpSocket->SendData(streamBuf, streamBytes, m_szMulticastIP, m_uPort, 1))
                    {
                        if (m_nPlayedFrames == 0)
                        {
                            g_Global.m_Network.AddLog("Start Send Frame : Multicast");
                        }
                    } 
                #else      // 先发送组播

                //判断系统内在线分区有没有存在使用UDP模式的设备，如果有，那么还需要发送组播
                if(g_Global.m_Sections.HasUdpSectionOnline())
                {
                    if(!isUseMulticastNewCmd)
                    {
                        if (m_pUdpSocket->SendData(streamBuf, streamBytes, m_szMulticastIP, m_uPort, 1))
                        {
                            if (m_nPlayedFrames == 0)
                            {
                                g_Global.m_Network.AddLog("Start Send Frame : Multicast");
                            }
                        }
                    }
                    else
                    {
                        //支持新的，那么用新的命令
                        char szBuf[MAX_BUF_LEN] = {0};
                        int	len	= CProtocol::ControlCommand(szBuf,CMD_STREAM_SOURCE_MULTICAST_NEW,(const char*)streamBuf,streamBytes);
                        //加入当前帧号
                        szBuf[2]  = m_nCurrentFramesNum/256;	            // 包序号
                        szBuf[3]  = m_nCurrentFramesNum%256;				// 保留位
                        if (m_pUdpSocket->SendData(szBuf, len, m_szMulticastIP, m_uPort, 1))
                        {
                            if (m_nPlayedFrames == 0)
                            {
                                g_Global.m_Network.AddLog("Start Send Frame : Multicast");
                            }
                        }
                    }
                }
                #endif     // 再发送KCP包给目前网络模式为TCP的终端
                #if 1
                char szBuf[MAX_BUF_LEN] = {0};
                int	len	= CProtocol::ControlCommand(szBuf,CMD_STREAM_SOURCE,(const char*)streamBuf,streamBytes);
                
                //20220601 KCP加入当前帧号
                szBuf[2]  = m_nCurrentFramesNum/256;	            // 包序号
                szBuf[3]  = m_nCurrentFramesNum%256;				// 保留位
                
                #endif
                g_Global.m_Network.StreamKCPToDevices(m_nPlayID, (const char*)szBuf, len, NULL);
            }
            else
            {
                // 加入到日志文件
                CMyString strLog;
                strLog.Format(("m_pUdpSocket == NULL : %s"), GetPathName().C_Str());
                g_Global.m_Network.AddLog(strLog);

                isOK = FALSE;
                break;
            }

            m_nPlayedFrames++;
        }
        else
        {
            CMyString strLog;
            strLog.Format(("End Send Frame : %d"),	m_nPlayedFrames);
            g_Global.m_Network.AddLog(strLog);

            isOK = FALSE;
            break;
        }
    }

    if(isOK == true)
    {
        //t.gettimer代表当前距离开始播放的时间。
        #if defined(Q_OS_LINUX)
        nextUTime = ((double)((m_nPlayedFrames + 1)*m_lfDurationPerFrame - (t.GetTimer()))*1000.0);
        if(nextUTime == 0)
            nextUTime = 1;
        //printf("frameCount=%d,m_nPlayedFrames=%d,nextUTime=%d\n",frameCount,m_nPlayedFrames,nextUTime);
        //printf("needTime=%f,t.GetTimer()=%f\n",(m_nPlayedFrames + 1)*m_lfDurationPerFrame,t.GetTimer());
        #else
        nextUTime = ((double)((m_nPlayedFrames + 1)*m_lfDurationPerFrame - (dCurPlaySec))*1000.0);
        if(nextUTime == 0)
            nextUTime = 1;
        //printf("frameCount=%d,m_nPlayedFrames=%d,nextUTime=%d\n",frameCount,m_nPlayedFrames,nextUTime);
        //printf("needTime=%f,t.GetTimer()=%f\n",(m_nPlayedFrames + 1)*m_lfDurationPerFrame,dCurPlaySec);
        #endif
    }

    return isOK;

}




