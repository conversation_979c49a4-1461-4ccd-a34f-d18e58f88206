#include "stdafx.h"
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <math.h>
#include "AUXPlay.h"
#if defined(Q_OS_LINUX)
#define DEFAULT_CHANNELS         (1)
#define DEFAULT_SAMPLE_RATE      (16000)
#define DEFAULT_SAMPLE_LENGTH    (16)
#define DEFAULT_DURATION_TIME    (50)

#define DEFAULT_AUX_MULTICAST_IP        ("**************")
#define DEFAULT_AUX_MULTICAST_PORT      50380


/*********************************************************************/

CAUX::CAUX()
{

}

CAUX::~CAUX()
{

}

int CAUX::SNDWAV_P_GetFormat(WAVContainer_t *wav, snd_pcm_format_t *snd_format)
{
    if (LE_SHORT(wav->format.format) != WAV_FMT_PCM)
        return -1;

    switch (LE_SHORT(wav->format.sample_length)) {
    case 16:
        *snd_format = SND_PCM_FORMAT_S16_LE;
        break;
    case 8:
        *snd_format = SND_PCM_FORMAT_U8;
        break;
    default:
        *snd_format = SND_PCM_FORMAT_UNKNOWN;
        break;
    }

    return 0;
}

ssize_t CAUX::SNDWAV_ReadPcm(SNDPCMContainer_t *sndpcm, size_t rcount)
{
    ssize_t r;
    size_t result = 0;
    size_t count = rcount;
    uint8_t *data = sndpcm->data_buf;

    if (count != sndpcm->chunk_size) {
        count = sndpcm->chunk_size;
    }

    while (count > 0)
    {
        r = snd_pcm_readi(sndpcm->handle, data, count);

        if (r == -EAGAIN || (r >= 0 && (size_t)r < count)) {
            snd_pcm_wait(sndpcm->handle, 1000);
        } else if (r == -EPIPE) {
            snd_pcm_prepare(sndpcm->handle);
            fprintf(stderr, "<<<<<<<<<<<<<<< Buffer Underrun >>>>>>>>>>>>>>>\n");
            return 0;
        } else if (r == -ESTRPIPE) {
            fprintf(stderr, "<<<<<<<<<<<<<<< Need suspend >>>>>>>>>>>>>>>\n");
            return 0;
        } else if (r < 0) {
            fprintf(stderr, "Error snd_pcm_writei: [%s] [%ld]\n", snd_strerror(r), r);
            return 0;
        }

        if (r > 0) {
            result += r;
            count -= r;
            data += r * sndpcm->bits_per_frame / 8;
        }
    }
    ;
    return rcount;

}

int CAUX::SNDWAV_SetParams(SNDPCMContainer_t *sndpcm, WAVContainer_t *wav)
{
    snd_pcm_hw_params_t *hwparams;
    snd_pcm_format_t format;
    uint32_t exact_rate;
    uint32_t buffer_time, period_time;

    /* Allocate the snd_pcm_hw_params_t structure on the stack. */
    snd_pcm_hw_params_alloca(&hwparams);

    /* Init hwparams with full configuration space */
    if (snd_pcm_hw_params_any(sndpcm->handle, hwparams) < 0) {
        fprintf(stderr, "Error snd_pcm_hw_params_any/n");
        goto ERR_SET_PARAMS;
    }

    if (snd_pcm_hw_params_set_access(sndpcm->handle, hwparams, SND_PCM_ACCESS_RW_INTERLEAVED) < 0) {
        fprintf(stderr, "Error snd_pcm_hw_params_set_access/n");
        goto ERR_SET_PARAMS;
    }

    /* Set sample format */
    if (SNDWAV_P_GetFormat(wav, &format) < 0) {
        fprintf(stderr, "Error get_snd_pcm_format/n");
        goto ERR_SET_PARAMS;
    }
    if (snd_pcm_hw_params_set_format(sndpcm->handle, hwparams, format) < 0) {
        fprintf(stderr, "Error snd_pcm_hw_params_set_format/n");
        goto ERR_SET_PARAMS;
    }
    sndpcm->format = format;

    /* Set number of channels */
    if (snd_pcm_hw_params_set_channels(sndpcm->handle, hwparams, LE_SHORT(wav->format.channels)) < 0) {
        fprintf(stderr, "Error snd_pcm_hw_params_set_channels/n");
        goto ERR_SET_PARAMS;
    }
    sndpcm->channels = LE_SHORT(wav->format.channels);

    /* Set sample rate. If the exact rate is not supported */
    /* by the hardware, use nearest possible rate.         */
    exact_rate = LE_INT(wav->format.sample_rate);
    if (snd_pcm_hw_params_set_rate_near(sndpcm->handle, hwparams, &exact_rate, 0) < 0) {
        fprintf(stderr, "Error snd_pcm_hw_params_set_rate_near/n");
        goto ERR_SET_PARAMS;
    }
    if (LE_INT(wav->format.sample_rate) != exact_rate) {
        fprintf(stderr, "The rate %d Hz is not supported by your hardware./n ==> Using %d Hz instead./n",
            LE_INT(wav->format.sample_rate), exact_rate);
    }

    if (snd_pcm_hw_params_get_buffer_time_max(hwparams, &buffer_time, 0) < 0) {
        fprintf(stderr, "Error snd_pcm_hw_params_get_buffer_time_max/n");
        goto ERR_SET_PARAMS;
    }
    if (buffer_time > 500000) buffer_time = 500000;
    period_time = buffer_time / 4;

    if (snd_pcm_hw_params_set_buffer_time_near(sndpcm->handle, hwparams, &buffer_time, 0) < 0) {
        fprintf(stderr, "Error snd_pcm_hw_params_set_buffer_time_near/n");
        goto ERR_SET_PARAMS;
    }

    if (snd_pcm_hw_params_set_period_time_near(sndpcm->handle, hwparams, &period_time, 0) < 0) {
        fprintf(stderr, "Error snd_pcm_hw_params_set_period_time_near/n");
        goto ERR_SET_PARAMS;
    }

    /* Set hw params */
    if (snd_pcm_hw_params(sndpcm->handle, hwparams) < 0) {
        fprintf(stderr, "Error snd_pcm_hw_params(handle, params)/n");
        goto ERR_SET_PARAMS;
    }

    snd_pcm_hw_params_get_period_size(hwparams, &sndpcm->chunk_size, 0);
    snd_pcm_hw_params_get_buffer_size(hwparams, &sndpcm->buffer_size);
    if (sndpcm->chunk_size == sndpcm->buffer_size) {
        fprintf(stderr, ("Can't use period equal to buffer size (%lu == %lu)/n"), sndpcm->chunk_size, sndpcm->buffer_size);
        goto ERR_SET_PARAMS;
    }

    sndpcm->bits_per_sample = snd_pcm_format_physical_width(format);
    sndpcm->bits_per_frame = sndpcm->bits_per_sample * LE_SHORT(wav->format.channels);
    NOTIFY("bits_per_frame : %d", sndpcm->bits_per_frame);
    NOTIFY("bits_per_sample : %d", sndpcm->bits_per_sample);
    NOTIFY("channels : %d", wav->format.channels);
    NOTIFY("chunk_size : %d", sndpcm->chunk_size);

    sndpcm->chunk_bytes = sndpcm->chunk_size * sndpcm->bits_per_frame / 8;
    NOTIFY("chunk_bytes : %d", sndpcm->chunk_bytes);

    /* Allocate audio data buffer */
    sndpcm->data_buf = (uint8_t *)malloc(sndpcm->chunk_bytes);
    if (!sndpcm->data_buf) {
        fprintf(stderr, "Error malloc: [data_buf]/n");
        goto ERR_SET_PARAMS;
    }

    return 0;

ERR_SET_PARAMS:
    return -1;
}

int CAUX::SNDWAV_PrepareWAVParams(WAVContainer_t *wav)
{
    bool isDefault = FALSE;
    LPCSection pAudioSection = g_Global.m_AudioCollectors.GetSectionByMac(CNetwork::GetHostMac());
    if(pAudioSection == NULL)
    {
        isDefault = TRUE;
    }

    uint16_t channels = isDefault ? DEFAULT_CHANNELS : pAudioSection->m_pAudioCollector->GetChannel();
    uint16_t sample_rate = isDefault ? DEFAULT_SAMPLE_RATE : pAudioSection->m_pAudioCollector->GetSampleRate();
    uint16_t sample_length = isDefault ? DEFAULT_SAMPLE_LENGTH : pAudioSection->m_pAudioCollector->GetSampleFMT();
    uint32_t duration_time = DEFAULT_DURATION_TIME;

    /* Const */
    wav->header.magic = WAV_RIFF;
    wav->header.type = WAV_WAVE;
    wav->format.magic = WAV_FMT;
    wav->format.fmt_size = LE_INT(16);
    wav->format.format = LE_SHORT(WAV_FMT_PCM);
    wav->chunk.type = WAV_DATA;

    /* User definition */
    wav->format.channels = LE_SHORT(channels);
    wav->format.sample_rate = LE_INT(sample_rate);
    wav->format.sample_length = LE_SHORT(sample_length);

    /* See format of wav file */
    wav->format.blocks_align = LE_SHORT(channels * sample_length / 8);
    wav->format.bytes_p_second = LE_INT((uint16_t)(wav->format.blocks_align) * sample_rate);

    wav->chunk.length = LE_INT(duration_time * (uint32_t)(wav->format.bytes_p_second));
    wav->header.length = LE_INT((uint32_t)(wav->chunk.length) +
                                sizeof(wav->chunk) + sizeof(wav->format) + sizeof(wav->header) - 8);


    return 0;
}

int CAUX::WAV_P_CheckValid(WAVContainer_t *container)
{
    if (container->header.magic != WAV_RIFF ||
        container->header.type != WAV_WAVE ||
        container->format.magic != WAV_FMT ||
        container->format.fmt_size != LE_INT(16) ||
        (container->format.channels != LE_SHORT(1) && container->format.channels != LE_SHORT(2)) ||
        container->chunk.type != WAV_DATA) {
        fprintf(stderr, "non standard wav file.\n");
        return -1;
    }

    return 0;
}

int CAUX::WAV_WriteHeader(int fd, WAVContainer_t *container)
{
    if (WAV_P_CheckValid(container) < 0)
        return -1;

    if (write(fd, &container->header, sizeof(container->header)) != sizeof(container->header) ||
        write(fd, &container->format, sizeof(container->format)) != sizeof(container->format) ||
        write(fd, &container->chunk, sizeof(container->chunk)) != sizeof(container->chunk)) {

        fprintf(stderr, "Error WAV_WriteHeader\n");
        return -1;
    }

    return 0;
}


/***********************************************************************/

CAUXPlay::CAUXPlay()
{
    m_bPthExit = TRUE;
    m_bWorking = FALSE;
    m_bUpdate = TRUE;
    m_csAUX = PTHREAD_MUTEX_INITIALIZER;
    m_pUdpSocket	= NULL;
    memcpy(m_szMulticastIP, DEFAULT_AUX_MULTICAST_IP, sizeof(DEFAULT_AUX_MULTICAST_IP));

    m_Stream.ApplyStreamBuffNumber = AUDIO_STREAM_BUFF_NUMBER_MAX;
    m_Stream.StreamBuffSize = AUDIO_STREAM_BUFF_SIZE_BASE * AUDIO_STREAM_BUFF_SIZE_MAX;
    m_Stream.CurrentIndexReadPos = 0;
    m_Stream.CurrentIndexWritePos = 0;
    m_Stream.CurrentReadIndex = 0;
    m_Stream.CurrentWriteIndex = 0;
    for(int i=0; i<m_Stream.ApplyStreamBuffNumber; i++)
    {
        m_Stream.StreamBuff[i] = (unsigned char*)malloc(m_Stream.StreamBuffSize);
        bzero(m_Stream.StreamBuff[i], m_Stream.StreamBuffSize);
    }
}

bool CAUXPlay::StartAUXWorking()
{
    CMyString strLog;

    if(m_bWorking && !m_bUpdate)    // 正在工作或配置改变
    {
        return TRUE;
    }
    else
    {
        StopAUXWorking();
    }

    strLog = "start aux play";
    g_Global.m_Network.AddLog(strLog);

    FreeAUX();
    if(!InitPlay())
    {
        NOTIFY("aux play init play failed");

        strLog = "aux play init failed";
        g_Global.m_Network.AddLog(strLog);
        return FALSE;
    }

    if(!InitAlsa())
    {
        NOTIFY("alsa initplay failed");

        strLog = "alsa play init failed";
        g_Global.m_Network.AddLog(strLog);
        return FALSE;
    }

    m_bWorking = TRUE;
    m_bUpdate = FALSE;

    StartCollectAudio();
    return TRUE;
}

void CAUXPlay::StopAUXWorking()
{
    // 线程里停止，在主线程停止不了，原因未知
    StopCollectAudio();
}

void CAUXPlay::ClearAudioBuf()
{
    m_ctime = 0;
    m_Stream.CurrentIndexReadPos = 0;
    m_Stream.CurrentIndexWritePos = 0;
    m_Stream.CurrentReadIndex = 0;
    m_Stream.CurrentWriteIndex = 0;
    for(int i=0; i<m_Stream.ApplyStreamBuffNumber; i++)
    {
        memset(m_Stream.StreamBuff[i], 0, m_Stream.StreamBuffSize);
    }
}


bool CAUXPlay::InitPlay()
{
    ClearAudioBuf();

    LPCSection pAudioSection = g_Global.m_AudioCollectors.GetSectionByMac(CNetwork::GetHostMac());
    if(pAudioSection == NULL)
    {
        printf("pAudioSection is NULL");
        return FALSE;
    }
    m_uPort = pAudioSection->m_pAudioCollector->GetPort();

    if( m_pUdpSocket == NULL)
    {
        m_pUdpSocket = new CUDPSocket();

        if(!m_pUdpSocket->CreateSocket(m_uPort, NULL, NULL))	// 不绑定端口
        {
            NOTIFY("aux udpsocket create failed");
            delete m_pUdpSocket;
            m_pUdpSocket = NULL;
            return FALSE;
        }
        else
        {
            // socket 保留，待修改
            if(!m_pUdpSocket->JoinMulticast(DEFAULT_AUX_MULTICAST_IP))
            {
                NOTIFY("joinMulticast failed");
                return FALSE;
            }
        }
    }
    else
    {
        return TRUE;
    }

    return TRUE;
}

bool CAUXPlay::InitAlsa()
{
    //
    char *devicename = (char *)"default";

    if (snd_output_stdio_attach(&record.log, stderr, 0) < 0) {
        NOTIFY("Error snd_output_stdio_attach\n");
        return FALSE;
    }

    if (int nErr = snd_pcm_open(&record.handle, devicename, SND_PCM_STREAM_CAPTURE, 0) < 0) {
        NOTIFY("Error snd_pcm_open [ %s] [%d] [%s]\n", devicename, nErr, strerror(nErr));
        return FALSE;
    }

    if (m_Aux.SNDWAV_PrepareWAVParams(&wav) < 0) {
        NOTIFY("Error SNDWAV_PrepareWAVParams\n");
        return FALSE;
    }

    if (m_Aux.SNDWAV_SetParams(&record, &wav) < 0) {
        NOTIFY("Error set_snd_pcm_params\n");
        return FALSE;
    }

    snd_pcm_dump(record.handle, record.log);

    m_lfDurationPerFrame = 1.0 * AUX_PER_FRAME_SIZE/wav.format.bytes_p_second;


    return TRUE;
}

bool CAUXPlay::FreeAUX()
{
    if (record.data_buf) { free(record.data_buf); record.data_buf = NULL;}
    if (record.log) { snd_output_close(record.log); record.log = NULL; }
    if (record.handle) { snd_pcm_close(record.handle); record.handle = NULL; }

    ClearAudioBuf();

    return TRUE;
}


bool CAUXPlay::AddStreamData(BYTE *data, int len)
{
    int pos = 0;
    while(pos < len)
    {
        int  curWriteSursize = m_Stream.StreamBuffSize - m_Stream.CurrentIndexWritePos;   // 当前写入数组剩余大小
        if(curWriteSursize > (len-pos))
        {
            memcpy(&m_Stream.StreamBuff[m_Stream.CurrentWriteIndex][m_Stream.CurrentIndexWritePos], &data[pos], len-pos);
            m_Stream.CurrentIndexWritePos += (len-pos);

            pos = len;
        }
        else
        {
            memcpy(&m_Stream.StreamBuff[m_Stream.CurrentWriteIndex][m_Stream.CurrentIndexWritePos], &data[pos], curWriteSursize);
            m_Stream.CurrentWriteIndex++;
            m_Stream.CurrentWriteIndex %= m_Stream.ApplyStreamBuffNumber;
            pos += curWriteSursize;

            m_Stream.CurrentIndexWritePos += curWriteSursize;
        }

        m_Stream.CurrentIndexWritePos %= m_Stream.StreamBuffSize;
    }

    SendStreamData();
    return true;
}



// 发送数据 保留，待修改
bool CAUXPlay::SendStreamData()
{
    BOOL isOK = TRUE;

    // 待测试修改 zhuyg
    CLxTimer curTime,t;

    // 获取时间
    struct timeval tl;
    gettimeofday(&tl, NULL);
    // tv_sec*1000在32位系统上会溢出ctime_t的取值范围,这里使用longlong型
    ctime_t tNow = (ctime_t)(tl.tv_sec)*1000 + tl.tv_usec/1000; // 获取毫秒数

    if(m_ctime == 0)
    {
        m_ctime = tNow;
    }

    // 计算本次与上次播放间隔
    ctime_t span = tNow - m_ctime;
    int frameCount = (int)span/(m_lfDurationPerFrame*1000);

    LPBYTE  streamBuf = {0};
    int     streamBytes;


    /*************************************************************/

    for(int i=0; i<frameCount; i++)
    {
        if(GetFrame(&streamBuf, &streamBytes))
        {
            if (m_pUdpSocket != NULL)
            {
                char szBuf[MAX_BUF_LEN] = {0};
                int  len = CProtocol::StreamAudioCollect(szBuf, (const char*)streamBuf, streamBytes);
                // 发送到分区
                if (m_pUdpSocket->SendData(szBuf, len, m_szMulticastIP, m_uPort, 1))
                {
                    //write(m_fd, streamBuf, streamBytes);
                }
                else
                {
                    //NOTIFY("send failed");
                }
            }
            else
            {
                // 加入到日志文件
                CMyString strLog;
                strLog.Format("aux m_pUdpSocket == NULL");
                g_Global.m_Network.AddLog(strLog);

            }
        }
        else
        {

        }
    }

    if(frameCount > 0)
    {
        m_ctime += m_lfDurationPerFrame*1000*frameCount;
    }
    else
    {

    }

    return isOK;

}

bool CAUXPlay::GetFrame(BYTE **pData, int *len)
{
//    ERROR("GetFrame()");
//    bzero(m_szSendBuf, sizeof(m_szSendBuf));
//    NOTIFY("CurrentWriteIndex : %d", m_Stream.CurrentWriteIndex);
//    NOTIFY("CurrentReadIndex : %d", m_Stream.CurrentReadIndex);
//    NOTIFY("CurrentIndexWritePos : %d", m_Stream.CurrentIndexWritePos);
//    NOTIFY("CurrentIndexReadPos : %d", m_Stream.CurrentIndexReadPos);

    // 当前读数组索引不等于写数组索引,读写数组不一致时，才取出数据
    if(m_Stream.CurrentReadIndex != m_Stream.CurrentWriteIndex)
    {
        // 数组剩余数据大于1帧内容
        if(m_Stream.StreamBuffSize - m_Stream.CurrentIndexReadPos > AUX_PER_FRAME_SIZE)
        {
            memcpy(m_szSendBuf, &m_Stream.StreamBuff[m_Stream.CurrentReadIndex][m_Stream.CurrentIndexReadPos], AUX_PER_FRAME_SIZE);
            m_Stream.CurrentIndexReadPos += AUX_PER_FRAME_SIZE;
        }
        else
        {
            int bufLen = m_Stream.StreamBuffSize - m_Stream.CurrentIndexReadPos;
            memcpy(m_szSendBuf, &m_Stream.StreamBuff[m_Stream.CurrentReadIndex][m_Stream.CurrentIndexReadPos], bufLen);
            m_Stream.CurrentIndexReadPos = 0;
            m_Stream.CurrentReadIndex++;
            m_Stream.CurrentReadIndex %= m_Stream.ApplyStreamBuffNumber;

            if(m_Stream.CurrentReadIndex != m_Stream.CurrentWriteIndex)
            {
                memcpy(&m_szSendBuf[bufLen], &m_Stream.StreamBuff[m_Stream.CurrentReadIndex][m_Stream.CurrentIndexReadPos], AUX_PER_FRAME_SIZE - bufLen);
                m_Stream.CurrentIndexReadPos += (AUX_PER_FRAME_SIZE - bufLen);
            }
            else
            {

            }
        }
    }
    else
    {
        return FALSE;
    }

    *pData = m_szSendBuf;
    *len = AUX_PER_FRAME_SIZE;
    return TRUE;
}

void CAUXPlay::StartCollectAudio()
{
    pthread_attr_t  attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);

    pthread_create(&m_pth, &attr, AUXPthread, (void*)this);
    pthread_attr_destroy(&attr);
}

void   CalcAux(BYTE *RX_BUF, int readBytes)
{
    int ndb = 0;
    short int value;
    int i;
    long v = 0;
    for(i=0; i<readBytes; i+=2)
    {
        memcpy((char*)&value, RX_BUF+i, 1);
        memcpy((char*)&value+1, RX_BUF+i+1, 1);
        v += abs(value);
    }

    v = v/(readBytes/2);
    if(v != 0) {
        ndb = (int)(20.0*log10((double)v / 65535.0 ));
    }
    else {
        ndb = -96;
    }

}


void *CAUXPlay::AUXPthread(void *lparam)
{
    CAUXPlay* pThis = (CAUXPlay*)lparam;
    pThis->SetPthExit(FALSE);

    off64_t rest;
    unsigned int c, frame_size;
    rest = pThis->wav.chunk.length;

    while(pThis->IsWorking())
    {
        frame_size = pThis->record.chunk_bytes * 8 / pThis->record.bits_per_frame;

        if (pThis->m_Aux.SNDWAV_ReadPcm(&pThis->record, frame_size) != frame_size)
            break;

        pThis->AddStreamData(pThis->record.data_buf, pThis->record.chunk_bytes);

    }

    snd_pcm_drain(pThis->record.handle);
    pThis->FreeAUX();
    return NULL;
}

void CAUXPlay::StopCollectAudio()
{
    pthread_t pth;
    pthread_create(&pth, NULL, AUXExitPthread, (void*)this);
    pthread_join(pth, NULL);    //等待线程结束
}

void *CAUXPlay::AUXExitPthread(void *lparam)
{
    CAUXPlay* pThis = (CAUXPlay*)lparam;

    pThis->Stop();
    return NULL;
}

void CAUXPlay::Stop()
{
    if(!m_bPthExit)
    {
        m_bWorking = FALSE;
    }
    usleep(500000);
}


#endif






