#ifndef SONGPLAY_H
#define SONGPLAY_H

#include "SongUnit.h"
#include <vector>
#include <pthread.h>
#include "Model/Device/Section.h"
#include <shared_mutex>

#define MAX_PLAY_COUNT  200  // 最多同时播放的歌曲

class CSongPlayer
{
public:
    CSongPlayer();
    ~CSongPlayer(void);

public:
    // 开始播放歌曲
    BOOL	StartWorking(void);

    // 停止播放歌曲
    void    StopWorking(void);

    // 播放线程
    static  void*   WorkThread(void* lpParam);

public:
    // 添加歌曲播放
    CSongUnit*	AddSongUnit(bool m_bmulticastNewCommand,unsigned char source,CMyString	pathName,int PlayMode=PM_ORDER,int nPrePlayID = -1,bool needLock=false);

    // 移除歌曲播放
    BOOL    RemoveSongUnit(int playID);

    // 清除歌曲播放
    void	ClearSongUnits();

    // 通过ID来查找播放歌曲
    CSongUnit * GetSongUnitByPlayID(int playID);

    // 判断播放ID是否有效
    bool IsPlayIDValid(int playID);

public:
    bool    StartListenInSong(CSection* pSection);
    bool    StopListenInSong(CSection* pSection);
    //在pSection没有更新playId前更新监听设备的PlayID,不适用于一般情况
    bool    UpdateListenInSong(CSection *pSection,CSongUnit *pSongUnit);
    // 设置播放状态
    BOOL	SetPlayStatus(int playID, SongPS status, bool needLock=false);

    void GetManualTask(vector<StManualTask>& manualTaskVec);
    bool IsManualTaskEqual(vector<StManualTask>& manualTaskVec1,vector<StManualTask>& manualTaskVec2);
    void PrintManualTask(vector<StManualTask>& manualTaskVec);
#if SUPPORT_REMOTE_CONTROLER
    void SetRemoteControlerTask(CMyString remoteMac,int event);
    void StopRemoteControlerTask(CMyString remoteMac);
#endif
    shared_timed_mutex m_csSongPlayer;
private:
    BOOL				m_isWorking;
    pthread_t           m_pth;

    CSongUnit*          m_pSongUnits[MAX_PLAY_COUNT];
    BOOL				m_IdUsed[MAX_PLAY_COUNT];

    list<CSongUnit*>     m_LSongUnits;  //创建一个空链表listName
};



#endif // SONGPLAY_H
