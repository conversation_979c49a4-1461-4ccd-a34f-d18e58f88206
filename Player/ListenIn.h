#ifndef LISTENIN_H
#define LISTENIN_H

#include <QtCore/qglobal.h>

#include <mpg123.h>
#if defined(Q_OS_LINUX)
#include <alsa/asoundlib.h>
#endif
#include "Global/CType.h"
#include "Model/Device/Section.h"
#include "Lib/SBEUdpSocket.h"
#include "Lib/SBETcpClientSocket.h"
#include "Lib/SBETcpServerSocket.h"


#define		OUT_BUF_COUNT		8
//#define		OUT_BUF_SIZE		(1152*4)
#define		OUT_BUF_SIZE		(1024 * 16)		// 解码后最多是16倍

#define       MON_SEC_MAC         18

// 歌曲文件头信息
typedef struct SongInfo
{
    BOOL	isMP3;			// 是否为MP3
    UINT32	samp_freq;		// 采样频率
    SHORT	channels;		// 通道数，单声道为1，双声道为2
    SHORT	bit_samp;		// bits per sample (又称量化位数)

}song_info_t;


class CListenIn
{
public:
    CListenIn();
    virtual ~CListenIn();

    // listen  zhuyg
    /*
    int       GetSecCount();
    string  GetSec(int index);
    void    AddSec(string strMac);
    void    RemoveSec(string strMac);
    void    ClearSec();
    */

public:
    bool    InitListenIn(song_info_t songInfo, unsigned int uPort = 26203);
    //bool    InitListenIn(song_info_t songInfo, CSection* pSection);
    void    ExitNetwork();
    bool    IsWorking();
    bool    StartWorking();
    bool    StopWorking();
    void    Playing(LPBYTE lpBuf, INT nLen);

    static  void  WorkThread(LPVOID lpParam);

private:
    LPBYTE			m_pBuffer[OUT_BUF_COUNT];
    //WAVEHDR	    m_WaveHdr[OUT_BUF_COUNT];

    //HWAVEOUT		m_hWaveOut;
    //WAVEFORMATEX	m_WaveForm;
    BOOL			m_isWorking;
    //int			    m_sockListen;
    //HANDLE			m_hThreadWork;
    //pthread_t    m_pThreadWork;
    pthread_mutex_t     m_csListen;

    BOOL			m_bExit;
    vector<string>   m_vecListenSec;      // 监听该分区的设备Mac列表

private:
    song_info_t		m_SongInfo;

private:
    mpg123_handle	*m_mh;

    int				m_nDataLen;
    unsigned char	m_dataBuf[OUT_BUF_SIZE];

    // socket相关

public:
    static void  OnRecvData(LPCSTR	pData,			// 收到的数据
                            unsigned short	nLen,	// 数据长度
                            VOID*	pUserDefined,	// 自定义数据
                            LPCSTR	szIP,			// IP地址
                            unsigned short	nPort);	// 端口

private:
    //CSBEUdpSockets	m_udpSockets;
    //CUDPSocket*		m_pUdpSocket;			// SOCKET

};

#endif // LISTENIN_H



