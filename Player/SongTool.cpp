#include "stdafx.h"
#include "SongTool.h"
#include <mpg123.h>
#include <string>
#include <sys/stat.h>
#include <unistd.h>
#include "Tools/MyCFile.h"
#include <QFileInfo>

using namespace std;


CSongTool::CSongTool()
{
    mpg123_init();
}


CSongTool::~CSongTool()
{
    mpg123_exit();
}

BOOL    IsValidParam(song_header_t header)
{
    UINT32	samp_freq	= header.samp_freq;		// 采样频率
    SHORT	channels	= header.channels;		// 通道数，单声道为1，双声道为2
    SHORT	bit_samp	= header.bit_samp;		// bits per sample (又称量化位数)

    BOOL    isValid = TRUE;


    if(!(samp_freq == 8000	|| samp_freq == 11025 || samp_freq == 12000 ||
         samp_freq == 16000	|| samp_freq == 22050 || samp_freq == 24000 ||
         samp_freq == 32000	|| samp_freq == 44100 || samp_freq == 48000 ||
         samp_freq == 88200	|| samp_freq == 96000))
    {
        isValid = FALSE;
    }
    else if(!( channels == 1 || channels == 2))
    {
        isValid = FALSE;
    }

    else if(!(bit_samp == 16 || bit_samp == 24 || bit_samp == 32))
    {
        isValid = FALSE;
    }

    return isValid;
}

int FindLengthPos(const char *data, int datalen, const char *subData, int sublen)
{
    int dataPos = 0;
    //int subPos = 0;

    while (dataPos < datalen)
    {
        int i = 0;
        for (i=0; i<sublen; ++i)
        {
            if (data[dataPos+i] != subData[i])
            {
                break;
            }
        }

        if (i == sublen)
        {
            return dataPos + 4; // "data" + size + data
        }

        dataPos++;
    }

    return -1;
}


// 歌曲时间长度
SongHeader CSongTool::GetSongInfo(CMyString strPathName)
{
    int     nDuration = 0;
    song_header_t   songHeader;
    memset(&songHeader,0,sizeof(songHeader));

    CMyString strExt = strPathName.Mid(strPathName.ReverseFind(('.'))+1);
    strExt.MakeUpper();
    printf("GetSongInfo:%s\n",strPathName.Data());
    // 用mpg123去获取歌曲长度
    if(strExt != "MP3" && strExt !="WAV")
    {
        return songHeader;
    }
    else
    {
        MyCFile   fileSong;       // 歌曲文件

        //打开文件
        if(fileSong.Open(strPathName.Data(), "rb"))
        {
            // WAV文件
            if(strExt == "WAV")
            {
                wave_t w;
                int nRead = fileSong.Read(&w, sizeof(wave_t));

                // WAV的数据尺寸位置不是固定的，44/78/146
                fileSong.SeekToBegin();
                char header[1001] = {0};
                fileSong.Read(header, 1000);

                int dataPos = FindLengthPos(header, 1000, "data", 4);
                if (dataPos >= 0)
                {
                    w.length = *(int*)&header[dataPos];
                }
                else
                {
                    return songHeader;
                }

                const BYTE riffValid[] = {'R', 'I', 'F', 'F'};
                const BYTE waveValid[] = {'W', 'A', 'V', 'E'};

                if(memcmp((char*)w.header.riff,riffValid,4))
                {
                    printf("wav:riff error!\n");
                    return songHeader;
                }
                if(memcmp((char*)w.header.wave_flag,waveValid,4))
                {
                    printf("wav:WAVE error!\n");
                    return songHeader;
                }
                // 歌曲时间长度（S）
                printf("wav:samp_freq=%d\n",w.header.samp_freq);
                printf("wav:bit_samp=%d\n",w.header.bit_samp);
                if(w.header.byte_rate == 0 || w.header.samp_freq == 0 || w.header.bit_samp == 0)
                {
                    printf("wav:parm error!\n");
                    return songHeader;
                }
                nDuration = w.length/w.header.byte_rate;

                songHeader.samp_freq	= w.header.samp_freq;	// 采样率
                songHeader.channels		= w.header.channels;	// 声道数
                songHeader.bit_samp		= w.header.bit_samp;	// 位数
                songHeader.bitrate      = w.header.byte_rate*8/1000; // 比特率kbps
                songHeader.nDuration    = nDuration;

                LOG(FORMAT("w.length : %d", w.length), LV_INFO);
                LOG(FORMAT("w.header.byte_rate : %d", w.header.byte_rate), LV_INFO);
                LOG(FORMAT("songHeader.samp_freq : %d", songHeader.samp_freq), LV_INFO);
                LOG(FORMAT("songHeader.channels : %d", songHeader.channels), LV_INFO);
                LOG(FORMAT("songHeader.bit_samp : %d", songHeader.bit_samp), LV_INFO);
            }

            // MP3文件
            else
            {
                int ret;
                mpg123_handle *m_mh = mpg123_new(NULL, &ret);

                if (MPG123_OK != ret)
                {
                    return songHeader;
                }

                if(MPG123_OK != mpg123_open(m_mh,strPathName.Data()))
                {
                    return songHeader;
                }

                long rate;
                int	channels;
                int encoding;

                // 为m_mh设定一个参数到指定键值中
                // MPG123_REMOVE_FLAGS		删除一些标志
                // MPG123_IGNORE_INFOFRAME	不要解析LAME/Xing信息框架,将其视为正常的MPEG数据
                if (MPG123_OK != mpg123_param(m_mh, MPG123_REMOVE_FLAGS, MPG123_IGNORE_INFOFRAME, 0.))
                {
                    return songHeader;
                }

                //获得每秒采样率，声道数，以及编码格式
                if (MPG123_OK != mpg123_getformat(m_mh, &rate, &channels,&encoding))
                {
                    return songHeader;
                }

                //mpg123只支持这两种MP3编码格式，已经够了，大部分就是这样的
                if(encoding != MPG123_ENC_SIGNED_16 && encoding != MPG123_ENC_FLOAT_32)
                {
                    return songHeader;
                }

                // 采样每帧所需要的时间（S）
                double lfDurationPerFrame = mpg123_tpf(m_mh);

                // 每帧多少个字节
                struct mpg123_frameinfo mi;
                mpg123_info(m_mh, &mi);

                // 歌曲时间长度（S）
                //nDuration = (int)(fileSong.GetLength()*1.0/mi.framesize * lfDurationPerFrame);
                mpg123_seek( m_mh, 0, SEEK_END );
                unsigned int m_nTotalFramesNum = mpg123_tellframe( m_mh ); //获取总帧数
                nDuration = (int)(m_nTotalFramesNum*lfDurationPerFrame);
                //printf("lfDurationPerFrame=%f,m_nTotalFramesNum=%d,nDuration=%d\n",lfDurationPerFrame,m_nTotalFramesNum,nDuration);

                songHeader.samp_freq	= rate;		// 采样率
                songHeader.channels		= channels;	// 声道数
                songHeader.bit_samp		= 16;		// 位数
                if(mi.vbr == MPG123_VBR)
                {
                    //VBR通过计算文件大小和时长来确定平均比特率
                    songHeader.bitrate  = (int)(fileSong.GetLength()*1.0/nDuration/1024*8);
                }
                else
                {
                    songHeader.bitrate  = mi.bitrate;   //固定比特率kbps
                }
                songHeader.nDuration    = nDuration;

                LOG(FORMAT("w.header.byte_rate : %d", songHeader.bitrate), LV_INFO);
                LOG(FORMAT("songHeader.samp_freq : %d", songHeader.samp_freq), LV_INFO);
                LOG(FORMAT("songHeader.channels : %d", songHeader.channels), LV_INFO);
                LOG(FORMAT("songHeader.bit_samp : %d", songHeader.bit_samp), LV_INFO);

                //一些清理工作，顺序不要颠倒
                mpg123_close(m_mh);
                mpg123_delete(m_mh);
            }

            if (!IsValidParam(songHeader))
            {
                memset(&songHeader,0,sizeof(songHeader));
                return songHeader;
            }

            fileSong.Close();
        }
        else
        {
            printf("file open error...\n");
        }

    }

    return songHeader;
}

// 歌曲大小（字节）
ULONG   CSongTool::GetSize(CMyString strPathName)
{
    QFile file(strPathName.Data());
    if(file.exists())
    {
        return file.size();
    }
    return 0;
}


// 是否为可推送格式（MP3或者WAV）
BOOL	CSongTool::IsStreamFormat(CMyString strPathName)
{
    CMyString strExtension = strPathName.Mid(strPathName.ReverseFind(('.'))+1);

    strExtension.MakeUpper();

    return (strExtension == ("MP3") || strExtension == ("WAV"));
}


// 是否存在本地 保留，待修改
BOOL	CSongTool::IsPathExist(CMyString  strPathName)
{
    QFileInfo file(strPathName.Data());
    //qDebug()<<strPathName.Data();
    if(file.exists()==false)        //可以直接识别UTF8文件，所以无需转换成GBK
    {
        CMyString strLog;
        strLog.Format("IsPathExist : PathName is not exist : %s", strPathName.C_Str());
        LOG(strLog.C_Str(), LV_INFO);
        g_Global.m_Network.AddLog(strLog);
        return false;
    }

    return true;
}














