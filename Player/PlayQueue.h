#ifndef PLAYQUEUE_H
#define PLAYQUEUE_H

#include <queue>
#include <map>
#include "Global/Const.h"
#include "Tools/CMyString.h"
#include "SongUnit.h"
#include "SongPlay.h"
#include "Model/Device/Section.h"
#if SUPPORT_REMOTE_CONTROLER
#include "Model/Device/RemoteControl.h"
#endif

#define SOURCE_NULL		0		// 空值，证明不需要播放音源（有定时但没歌曲）
#define SOURCE_PLAY		1		// 播放本地歌曲
#define SOURCE_TIMER	2		// 定时
#define SOURCE_BELL		3		// 钟声
#define SOURCE_ALARM	4		// 告警
#define SOURCE_EVENT    5       // 事件
#define SOURCE_REMOTE_PLAY   6       // 遥控器播放,可自定义歌曲播放，此处只用于区分播放逻辑，实际上发送给终端的音源还是SOURCE_PLAY（为了兼容旧终端）
#define SOURCE_SPECIFIED_PLAY   7    //指定歌曲网络点播，此处只用于区分播放逻辑，实际上发送给终端的音源还是SOURCE_PLAY（为了兼容旧终端）
#define SOURCE_SPECIFIED_ALARM   8   //指定歌曲消防告警，此处只用于区分播放逻辑，实际上发送给终端的音源还是SOURCE_ALARM（为了兼容旧终端）

#define SOURCE_TIMER_STOP 0x0D	// 定时点停止
#define SOURCE_NEXT		0x0E	// 下一个音源
#define SOURCE_STOP		0x0F	// 停止播放

//20211225 增加两个上下曲音源
#define SOURCE_PRE_MANUAL  0x10     //手动上一个节目
#define SOURCE_NXT_MANUAL  0x11     //手动下一个节目

#define DURATION_STOP_TO_PLAY	400	// 从停止到播放的时间间隔，终端停止需要时间
#define DURATION_NEXT_PLAY_TASK	0	// 两个播放任务之间至少间隔的时间（为了防止频繁点击按钮响应不过来而强制拉长时间）


// 播放任务

class   CPlayTask
{
public:
    CPlayTask(void) {}
    CPlayTask(unsigned char source,
              vector<CMyString> strPathNames,
              unsigned int* pSecIndexs,
              unsigned int uSecCount,
              string strUserAccount = "admin",
              int m_nPlayMode = 0,
              int nListIndex = -1,
              int nSongIndex = -1,
              unsigned char nVolume = -1,
              int play_type = 1, // 播放类型，1:播放歌曲，2:TTS(用于区分播放完毕后是否删除TTS歌曲)
              int play_count = 1,   //播放次数；顺序播放时有效
              PlayFrom  playFrom = PLAYLIST_FORM_LOCAL);
    ~CPlayTask(void) {}

public:
    bool	operator==(CPlayTask &playTask);

public:
    unsigned char	GetSource(void)		{	return m_source;		}
    CMyString       GetPathName(void)
    {
        if(m_vecStrPathNames.size()>0) return m_vecStrPathNames[0];
        else return "";	
    }
    vector<CMyString> GetVecPathNames(void) { return m_vecStrPathNames;}
    unsigned int*	GetSecIndexs(void)	{	return m_pSecIndexs;	}
    unsigned int	GetSecCount(void)	{	return m_uSecCount;		}
    int             GetListIndex(void)	{	return m_nListIndex;	}
    int             GetSongIndex(void)	{	return m_nSongIndex;	}
    unsigned char   GetVolume(void)     {   return m_uVolume;       }
    string          GetUserAccount()    {   return m_strUserAccount; }
    int             GetPlayMode(void)   {   return m_nPlayMode;}
    void            SetPlayMode(int playMode) { m_nPlayMode = playMode; }
    PlayFrom        GetPlayFrom(void)  {   return m_PlayFrom;      }

    int             GetPlayCount(void)   {   return m_nPlayCount;}
    int             GetPlayType(void)    {   return m_nPlayType; }

    void            SetPlayID(int nPlayID){	m_nPlayID = nPlayID;	}
    int             GetPlayID(void)		{	return m_nPlayID;		}
    void            SetTimerPointID(int nTpID) {	m_nTimerPointID = nTpID;	}
    int             GetTimerPointID(void)	{	return m_nTimerPointID;	}
#if SUPPORT_REMOTE_CONTROLER
    void            SetRemoteControlTask(stRemoteTaskInfo &remoteTask )   {m_stRemoteTask = remoteTask;}
    stRemoteTaskInfo&   GetRemoteControlTask()  {return m_stRemoteTask;}
#endif

private:
    unsigned char	m_source;		// 音源
    vector<CMyString>   m_vecStrPathNames;	// 曲目路径数组
    unsigned int	m_pSecIndexs[MAX_SECTION_COUNT_FORMAL];	// 选中分区索引
    unsigned int	m_uSecCount;	// 选中分区数量
    unsigned char   m_uVolume;      // 音量   zhuyg

    int             m_nPlayCount;     //播放次数
    int             m_nPlayType;      //播放类型，1:播放歌曲，2:TTS(用于区分播放完毕后是否删除TTS歌曲)

    int		m_nListIndex;	// 歌曲列表的索引，只有播放音源为SOURCE_PLAY才有作用
    int		m_nSongIndex;	// 歌曲的索引，只有播放音源为SOURCE_PLAY才有作用

    string  m_strUserAccount;   //账户名
    int     m_nPlayMode;        //播放模式，只有播放音源为SOURCE_PLAY才有作用

    PlayFrom m_PlayFrom;    // 播放来自哪个播放列表，分控播放时才有作用

    int		m_nPlayID;		// 播放ID，用于下一个任务NextPlayTask
    int		m_nTimerPointID;
#if SUPPORT_REMOTE_CONTROLER
    stRemoteTaskInfo  m_stRemoteTask;       //远程遥控播放任务
#endif
};


/************************************************************/

// 播放队列（集中模式）
class CPlayQueue
{
public:
    CPlayQueue(void);
    ~CPlayQueue(void);
    void	Clear(void);

public:

    // 添加播放任务（每个播放任务，会执行一个播放单元）
    bool	PushPlayTask(CPlayTask& playTask);

    // 检测播放任务
    bool	CheckPlayTasks(ctime_t tNow);

    // 检测播放单元
    void	CheckPlayUnits(ctime_t tNow);

    // 检测定时点(本地)
    void	CheckTimers(CTime& t);

    // 分区停止播放音源
    void	StopSectionPlaySource(CSection* pSection);			// 指定某个分区

    // 播放歌曲结束
    void	PlaySourceEnd(int playID);

    // 手动播放任务上下曲
    void   PlaySourceMuanualPreNext(int playID,int action);

    // 分区音源变化
    void	SectoinSourceChange(CSection& section, ProgramSource newSrc);

    // 结束定时点音源
    void	StopTimerPoint(CTimePoint& timePoint,bool needLock=false);

    // 获取节目源优先级
    BYTE    GetProSourcePriority(BYTE proSource)   {return m_SoucePriority[proSource];}

    // 获取节目源优先级
    void    SetProSourcePriority(BYTE proSource,BYTE priority)   {m_SoucePriority[proSource] = priority;}

private:
    // 移除播放任务
    void	PopPlayTask();

    // 添加播放单元到队尾（每个播放任务，会执行一个播放单元）
    void	PushPlayUnit(int pSongId);

    // 将播放单元从队头删除
    void	PopPlayUnit();

    //  发送播放命令
    void	SendPlayCommand(unsigned char src, CSongUnit *pSrcUnit, CSection* pSection);

    // 更新播放信息
    void	UpdatePlayInfo(unsigned char src, CSongUnit *pSrcUnit, int nList, int nSong);

    // 分区播放音源
    bool	StartPlayTask(CPlayTask& playTask);

    // 停止播放音源
    void	StopPlayTask(CPlayTask& playTask);

    // 下一个音源
    void	NextPlayTask(CPlayTask& playTask);

    // 音源上下曲（手动)
    void	PreNextPlayTask(CPlayTask& playTask,int action);

    // 开始定时点音源
    void	StartTimerPlayTask(CPlayTask& playTask);

#if SUPPORT_REMOTE_CONTROLER
    // 开始遥控器播放音源
    void	StartRemoteControlPlayTask(CPlayTask& playTask);
#endif

public:
    CSongPlayer			m_SongPlayer;			// 播放歌曲管理类

private:
    // 待修改 zhuyg 是否定义全局锁
    pthread_mutex_t     m_csPlayUnits;
    pthread_mutex_t     m_csPlayTasks;

    queue<int>	m_PlayUnitQueue;		// 即将播放歌曲的队列
    queue<CPlayTask>	m_PlayTaskQueue;		// 播放任务的队列

    map<BYTE, BYTE>		m_SoucePriority;

};

#endif // PLAYQUEUE_H
