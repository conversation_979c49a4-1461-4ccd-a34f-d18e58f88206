#include "stdafx.h"
#include "Tools/sha1.h"
#include <chrono>
#include <algorithm>
#include <QDir>
#include <QProcess>
#include <sys/time.h>
#include <QFileInfo>
#include "RecordManager.h"

#if defined(Q_OS_LINUX)
#include <sys/stat.h>
#include <unistd.h>
#else
#include <direct.h>
#include <io.h>
#endif

#if SUPPORT_CALL_RECORD
CRecordManager::CRecordManager()
{
    m_bInitialized = false;
    m_bRunning = false;
    m_pAudioThread = NULL;
    m_pDatabase = NULL;
    m_strRecordDir = RECORD_FILE_DIR;
    m_bMpg123Initialized = false;
}

CRecordManager::~CRecordManager()
{
    Cleanup();
}

bool CRecordManager::Initialize()
{
    if (m_bInitialized)
    {
        return true;
    }

    // 设置录音文件根目录
    m_strRecordDir = g_Global.m_strFolderPath + "/" + HTTP_FOLDER_ADATA + "/" + RECORD_ROOT_DIR;
    
    // 创建录音根目录
    if (!CreateRecordDirectory(m_strRecordDir))
    {
        LOG(FORMAT("Failed to create record directory: %s", m_strRecordDir.Data()), LV_ERROR);
        return false;
    }

    // 初始化数据库表
    if (m_pDatabase != NULL)
    {
        m_recordTable.SetDbManager(m_pDatabase);
        if (!m_recordTable.Create())
        {
            LOG("Failed to create record table", LV_ERROR);
            return false;
        }
    }

    // 初始化MPG123
#if defined(Q_OS_LINUX)
    if (mpg123_init() == MPG123_OK)
    {
        m_bMpg123Initialized = true;
    }
#endif

    m_bRunning = true;
    m_bInitialized = true;

    // 启动音频处理线程
    m_pAudioThread = new std::thread(AudioProcessThread, this);

    LOG("Record manager initialized successfully", LV_INFO);
    return true;
}

void CRecordManager::Cleanup()
{
    if (!m_bInitialized)
    {
        return;
    }

    m_bRunning = false;

    // 停止音频处理线程
    if (m_pAudioThread != NULL)
    {
        m_audioCondition.notify_all();
        if (m_pAudioThread->joinable())
        {
            m_pAudioThread->join();
        }
        delete m_pAudioThread;
        m_pAudioThread = NULL;
    }

    // 清理所有录音会话
    {
        std::lock_guard<std::mutex> lock(m_sessionMutex);
        for (auto& pair : m_recordSessions)
        {
            DestroyRecordSession(pair.first);
        }
        m_recordSessions.clear();
    }

    // 清理MPG123
#if defined(Q_OS_LINUX)
    if (m_bMpg123Initialized)
    {
        mpg123_exit();
        m_bMpg123Initialized = false;
    }
#endif

    m_bInitialized = false;
    LOG("Record manager cleanup completed", LV_INFO);
}

bool CRecordManager::StartRecord(const CMyString& strCallId,
                                 const CMyString& strCallerMac,
                                 const CMyString& strCallerName,
                                 const CMyString& strCalleeList,
                                 RecordType recordType,
                                 AudioFormat audioFormat,
                                 int nSampleRate,
                                 int nChannels,
                                 int nBitRate)
{
    if (!m_bInitialized)
    {
        LOG("RecordManager not initialized", LV_ERROR);
        return false;
    }

    if (IsRecordSessionExists(strCallId))
    {
        LOG(FORMAT("Record session already exists for CallId: %s", strCallId.Data()), LV_WARNING);
        return false;
    }

    // 创建录音会话
    if (!CreateRecordSession(strCallId, strCallerMac, strCallerName, strCalleeList,
                             recordType, audioFormat, nSampleRate, nChannels, nBitRate))
    {
        LOG(FORMAT("Failed to create record session for CallId: %s", strCallId.Data()), LV_ERROR);
        return false;
    }

    // 在数据库中开始录音记录
    if (m_pDatabase != NULL)
    {
        m_recordTable.StartRecord(strCallId, strCallerMac, strCallerName, strCalleeList,
                                  recordType, audioFormat, nSampleRate, nChannels, nBitRate);
    }

    LOG(FORMAT("Started recording for CallId: %s", strCallId.Data()), LV_INFO);
    return true;
}

bool CRecordManager::EndRecord(const CMyString& strCallId, RecordStatus recordStatus)
{
    if (!IsRecordSessionExists(strCallId))
    {
        LOG(FORMAT("No active recording for CallId: %s", strCallId.Data()), LV_WARNING);
        return false;
    }

    RecordSession* pSession = NULL;
    {
        std::lock_guard<std::mutex> lock(m_sessionMutex);
        auto it = m_recordSessions.find(strCallId);
        if (it != m_recordSessions.end())
        {
            pSession = it->second;
            if(pSession->bIsRecording == false)
            {
                LOG(FORMAT("Recording for CallId: %s is already ended", strCallId.Data()), LV_WARNING);
                return false;
            }
            pSession->bIsRecording = false;
        }
    }

    if (pSession != NULL)
    {
        // 等待音频处理完成
        std::this_thread::sleep_for(std::chrono::milliseconds(50));

        int nDuration = 0;
        int nFileSize = 0;
        CMyString strFinalFile;
        if(recordStatus == RS_COMPLETED)
        {
            // 计算录音时长
            nDuration = CalculateRecordDuration(pSession->ulStartTime, GetCurrentTimestamp());
            bool bSuccess = false;

            if (pSession->strTempFile.GetLength() > 0)
            {
                strFinalFile = GetRecordFilePath(CTime::GetCurrentTimeT(), strCallId);
                
                // 创建目录
                CMyString strDir = GetRecordDirectory(CTime::GetCurrentTimeT());
                CreateRecordDirectory(strDir);

                // 转换PCM为MP3
                if (ConvertPCMToMP3(pSession->strTempFile, strFinalFile, 
                                pSession->nSampleRate, pSession->nChannels, pSession->nBitRate))
                {
                    nFileSize = GetFileSize(strFinalFile.Data());
                    bSuccess = true;
                    recordStatus = RS_COMPLETED;
                }
                else
                {
                    recordStatus = RS_ERROR;
                }

                // 删除临时文件
                if (IsExistFile(pSession->strTempFile.Data()))
                {
                    remove(pSession->strTempFile.Data());
                }
            }

            // 更新数据库记录
            if (m_pDatabase != NULL)
            {
                //将strFinalFile去除前面的目录，取http后面的目录
                strFinalFile = strFinalFile.Right(strFinalFile.GetLength() - g_Global.m_strFolderPath.GetLength());
                m_recordTable.EndRecord(strCallId, strFinalFile, nFileSize, nDuration, recordStatus);
            }
        }
        else
        {
            m_recordTable.EndRecord(strCallId, strFinalFile, nFileSize, nDuration, recordStatus);
        }

        // 销毁录音会话
        DestroyRecordSession(strCallId);

        LOG(FORMAT("Ended recording for CallId: %s, Status: %s, Duration: %d seconds",
                  strCallId.Data(), CRecordTable::GetRecordStatusDescription(recordStatus).Data(), nDuration), LV_INFO);
    }

    return true;
}

bool CRecordManager::StopRecordByMacPrefix(const CMyString& strMacPrefix, RecordStatus recordStatus)
{
    std::vector<CMyString> sessionsToStop;
    
    // 查找匹配的录音会话
    {
        std::lock_guard<std::mutex> lock(m_sessionMutex);
        for (auto it = m_recordSessions.begin(); it != m_recordSessions.end(); ++it)
        {
            const CMyString& strCallId = it->first;
            // 检查是否以指定前缀开始
            if (strncmp(strCallId.Data(), strMacPrefix.Data(), strlen(strMacPrefix.Data())) == 0)
            {
                sessionsToStop.push_back(strCallId);
            }
        }
    }
    
    // 停止匹配的录音会话
    bool bSuccess = true;
    for (const CMyString& strCallId : sessionsToStop)
    {
        if (!EndRecord(strCallId, recordStatus))
        {
            bSuccess = false;
            //LOG(FORMAT("Failed to stop record session: %s", strCallId.Data()), LV_ERROR);
        }
    }
    if(bSuccess)
    {
        LOG(FORMAT("Stopped %d record sessions with MAC prefix: %s", 
                sessionsToStop.size(), strMacPrefix.Data()), LV_INFO);
    }
    
    return bSuccess;
}

bool CRecordManager::AddAudioData(const CMyString& strCallId, const unsigned char* pData, int nLength)
{
    if (!IsRecordSessionExists(strCallId) || pData == NULL || nLength <= 0)
    {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_sessionMutex);
    auto it = m_recordSessions.find(strCallId);
    if (it == m_recordSessions.end() || !it->second->bIsRecording)
    {
        return false;
    }

    RecordSession* pSession = it->second;

    // 创建音频缓冲区
    AudioBuffer buffer;
    if (nLength > AUDIO_BUFFER_SIZE)
    {
        nLength = AUDIO_BUFFER_SIZE;
    }
    
    memcpy(buffer.data, pData, nLength);
    buffer.length = nLength;
    buffer.timestamp = GetCurrentTimestamp();

    // 添加到音频队列
    {
        std::lock_guard<std::mutex> queueLock(pSession->queueMutex);
        pSession->audioQueue.push(buffer);
    }

    // 通知音频处理线程
    m_audioCondition.notify_one();

    return true;
}

bool CRecordManager::IsRecordSessionExists(const CMyString& strCallId)
{
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    return m_recordSessions.find(strCallId) != m_recordSessions.end();
}

bool CRecordManager::GetRecordSession(const CMyString& strCallId, RecordSession& session)
{
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    auto it = m_recordSessions.find(strCallId);
    if (it != m_recordSessions.end())
    {
        RecordSession* pSession = it->second;
        
        // 手动复制可复制的成员，避免复制mutex等不可复制的成员
        session.strCallId = pSession->strCallId;
        session.strCallerMac = pSession->strCallerMac;
        session.strCallerName = pSession->strCallerName;
        session.strCalleeList = pSession->strCalleeList;
        session.recordType = pSession->recordType;
        session.audioFormat = pSession->audioFormat;
        session.nSampleRate = pSession->nSampleRate;
        session.nChannels = pSession->nChannels;
        session.nBitRate = pSession->nBitRate;
        session.bIsRecording = pSession->bIsRecording;
        session.ulStartTime = pSession->ulStartTime;
        session.ulEndTime = pSession->ulEndTime;
        session.strTempFile = pSession->strTempFile;
        session.strFinalFile = pSession->strFinalFile;
        session.pTempFile = pSession->pTempFile;
        session.pG722Decoder = pSession->pG722Decoder;
        
        // 注意：不复制audioQueue和queueMutex，因为这些是线程相关的
        
        return true;
    }
    return false;
}

std::vector<CMyString> CRecordManager::GetRecordSessionsByPrefix(const CMyString& strPrefix)
{
    std::vector<CMyString> matchingSessions;
    
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    for (auto it = m_recordSessions.begin(); it != m_recordSessions.end(); ++it)
    {
        const CMyString& strCallId = it->first;
        // 检查是否以指定前缀开始
        if (strncmp(strCallId.Data(), strPrefix.Data(), strlen(strPrefix.Data())) == 0)
        {
            matchingSessions.push_back(strCallId);
        }
    }
    
    return matchingSessions;
}

void CRecordManager::SetDbManager(CDataBase* pDatabase)
{
    m_pDatabase = pDatabase;
    m_recordTable.SetDbManager(pDatabase);
}

void CRecordManager::CleanupExpiredRecords()
{
    if (m_pDatabase != NULL)
    {
        m_recordTable.ClearExpiredData();
    }
}

// 添加辅助函数：将CallId中的冒号替换为下划线，以兼容Windows文件系统
/*static*/ CMyString CRecordManager::SanitizeCallIdForFilename(const CMyString& strCallId)
{
    CMyString strSanitized = strCallId;
    // 将冒号替换为下划线
    strSanitized.Replace(":", "_");
    return strSanitized;
}

CMyString CRecordManager::GetRecordFilePath(const CTime& time, const CMyString& strCallId)
{
    CMyString strDir = GetRecordDirectory(time);
    CMyString strFileName;
    // 使用处理后的CallId来避免Windows文件名中的冒号问题
    CMyString strSanitizedCallId = SanitizeCallIdForFilename(strCallId);
    strFileName.Format("%s_%s_%s%s", 
                      RECORD_FILE_PREFIX, 
                      strSanitizedCallId.Data(),
                      time.Format("%H%M%S").Data(),
                      RECORD_FILE_EXT);
    
    return strDir + "/" + strFileName;
}

CMyString CRecordManager::GetRecordDirectory(const CTime& time)
{
    CMyString strDir;
    strDir.Format("%s/%s/%s/%s",
                  g_Global.m_strFolderPath.Data(),
                  HTTP_FOLDER_ADATA,
                  RECORD_ROOT_DIR,
                  time.Format(RECORD_DAILY_SUBDIR_FORMAT).Data());
    return strDir;
}

bool CRecordManager::CreateRecordDirectory(const CMyString& strDir)
{
    if (IsExistDir(strDir.Data()))
    {
        return true;
    }

#if defined(Q_OS_LINUX)
    if (mkdir(strDir.Data(), 0755) == 0)
    {
        return true;
    }
#else
    if (_mkdir(strDir.Data()) == 0)
    {
        return true;
    }
#endif

    // 尝试递归创建目录
    CMyString strParentDir = strDir;
    int nPos = strParentDir.ReverseFind('/');
    if (nPos > 0)
    {
        strParentDir = strParentDir.Left(nPos);
        if (CreateRecordDirectory(strParentDir))
        {
#if defined(Q_OS_LINUX)
            return (mkdir(strDir.Data(), 0755) == 0);
#else
            return (_mkdir(strDir.Data()) == 0);
#endif
        }
    }

    return false;
}

bool CRecordManager::CreateRecordSession(const CMyString& strCallId,
                                         const CMyString& strCallerMac,
                                         const CMyString& strCallerName,
                                         const CMyString& strCalleeList,
                                         RecordType recordType,
                                         AudioFormat audioFormat,
                                         int nSampleRate,
                                         int nChannels,
                                         int nBitRate)
{
    RecordSession* pSession = new RecordSession();
    if (pSession == NULL)
    {
        return false;
    }

    // 初始化会话信息
    pSession->strCallId = strCallId;
    pSession->strCallerMac = strCallerMac;
    pSession->strCallerName = strCallerName;
    pSession->strCalleeList = strCalleeList;
    pSession->recordType = recordType;
    pSession->audioFormat = audioFormat;
    pSession->nSampleRate = nSampleRate;
    pSession->nChannels = nChannels;
    pSession->nBitRate = nBitRate;
    pSession->bIsRecording = true;
    pSession->ulStartTime = GetCurrentTimestamp();
    pSession->ulEndTime = 0;
    pSession->pTempFile = NULL;
    pSession->pG722Decoder = NULL;

    // 生成临时文件路径
    CMyString strTempDir = g_Global.m_strTempFolderPath;
    
    // 使用处理后的CallId来避免Windows文件名中的冒号问题
    CMyString strSanitizedCallId = SanitizeCallIdForFilename(strCallId);
    pSession->strTempFile.Format("%s/record_%s_%ld.pcm", 
                                strTempDir.Data(), 
                                strSanitizedCallId.Data(), 
                                pSession->ulStartTime);

    // 创建临时文件
    pSession->pTempFile = fopen(pSession->strTempFile.Data(), "wb");
    if (pSession->pTempFile == NULL)
    {
        delete pSession;
        return false;
    }

    // 如果是G.722编码，创建解码器
    if (audioFormat == AF_G722)
    {
        pSession->pG722Decoder = g722_decoder_new(64000, 0);
        if (pSession->pG722Decoder == NULL)
        {
            fclose(pSession->pTempFile);
            delete pSession;
            return false;
        }
    }

    // 生成最终文件路径
    pSession->strFinalFile = GetRecordFilePath(CTime::GetCurrentTimeT(), strCallId);

    // 添加到会话列表
    {
        std::lock_guard<std::mutex> lock(m_sessionMutex);
        m_recordSessions[strCallId] = pSession;
    }

    return true;
}

void CRecordManager::DestroyRecordSession(const CMyString& strCallId)
{
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    auto it = m_recordSessions.find(strCallId);
    if (it != m_recordSessions.end())
    {
        RecordSession* pSession = it->second;
        
        // 关闭文件
        if (pSession->pTempFile != NULL)
        {
            fclose(pSession->pTempFile);
            pSession->pTempFile = NULL;
        }

        // 清理G.722解码器
        if (pSession->pG722Decoder != NULL)
        {
            g722_decoder_destroy(pSession->pG722Decoder);
            pSession->pG722Decoder = NULL;
        }

        // 清理音频队列
        {
            std::lock_guard<std::mutex> queueLock(pSession->queueMutex);
            while (!pSession->audioQueue.empty())
            {
                pSession->audioQueue.pop();
            }
        }

        delete pSession;
        m_recordSessions.erase(it);
    }
}

void CRecordManager::AudioProcessThread(CRecordManager* pManager)
{
    if (pManager == NULL)
    {
        return;
    }

    LOG("Audio record process thread started", LV_INFO);
    
    while (pManager->m_bRunning)
    {
        std::unique_lock<std::mutex> lock(pManager->m_audioMutex);
        
        // 等待音频数据或停止信号
        pManager->m_audioCondition.wait_for(lock, std::chrono::milliseconds(100));
        
        if (!pManager->m_bRunning)
        {
            break;
        }
        
        // 处理音频数据
        pManager->ProcessAudioData();
    }
    
    LOG("Audio process thread stopped", LV_INFO);
}

void CRecordManager::ProcessAudioData()
{
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    
    for (auto& pair : m_recordSessions)
    {
        if (pair.second->bIsRecording)
        {
            ProcessSessionAudio(*(pair.second));
        }
    }
}

void CRecordManager::ProcessSessionAudio(RecordSession& session)
{
    std::lock_guard<std::mutex> queueLock(session.queueMutex);
    
    while (!session.audioQueue.empty())
    {
        AudioBuffer buffer = session.audioQueue.front();
        session.audioQueue.pop();
        
        if (session.audioFormat == AF_G722)
        {
            // G.722解码为PCM
            unsigned char pcmData[AUDIO_BUFFER_SIZE * 4]; // G.722解码后数据会增大
            int nPcmLength = 0;
            
            if (DecodeG722Audio(session, buffer.data, buffer.length, pcmData, nPcmLength))
            {
                // 写入PCM数据到临时文件
                if (session.pTempFile != NULL && nPcmLength > 0)
                {
                    fwrite(pcmData, 1, nPcmLength, session.pTempFile);
                    fflush(session.pTempFile);
                }
            }
        }
        else if (session.audioFormat == AF_PCM)
        {
            // 直接写入PCM数据
            if (session.pTempFile != NULL)
            {
                fwrite(buffer.data, 1, buffer.length, session.pTempFile);
                fflush(session.pTempFile);
            }
        }
    }
}

bool CRecordManager::DecodeG722Audio(RecordSession& session, const unsigned char* pInputData, int nInputLength,
                                     unsigned char* pOutputData, int& nOutputLength)
{
    if (session.pG722Decoder == NULL || pInputData == NULL || pOutputData == NULL)
    {
        nOutputLength = 0;
        return false;
    }

    // G.722解码
    nOutputLength = g722_decode(session.pG722Decoder, pInputData, nInputLength, (short*)pOutputData);
    nOutputLength *= 2; // 转换为字节数（每个样本2字节）
    
    return nOutputLength > 0;
}

bool CRecordManager::ConvertPCMToMP3(const CMyString& strPCMFile, const CMyString& strMP3File,
                                     int nSampleRate, int nChannels, int nBitRate)
{
    // 使用ffmpeg转换PCM为MP3
    QProcess process;
    QStringList arguments;
    
    arguments << "-f" << "s16le"  // 输入格式：16位小端PCM
              << "-ar" << QString::number(nSampleRate)  // 采样率
              << "-ac" << QString::number(nChannels)    // 声道数
              << "-i" << strPCMFile.Data()              // 输入文件
              << "-acodec" << "libmp3lame"              // 音频编码器
              << "-ab" << QString("%1k").arg(nBitRate / 1000)  // 比特率
              << "-y"                                   // 覆盖输出文件
              << strMP3File.Data();                     // 输出文件
    
    #if defined(Q_OS_LINUX)
    QString program = "ffmpeg";
    #else
    CMyString ffmpegPath= g_Global.m_strRunDirPath+"/Tools/ffmpeg.exe";
    QString program = ffmpegPath.Data();
    #endif

    process.start(program, arguments);
    process.waitForFinished(30000); // 等待最多30秒
    
    if (process.exitCode() == 0)
    {
        // 检查输出文件是否存在
        QFileInfo fileInfo(strMP3File.Data());
        return fileInfo.exists() && fileInfo.size() > 0;
    }
    else
    {
        LOG(FORMAT("FFmpeg conversion failed: %s", process.readAllStandardError().constData()), LV_ERROR);
        return false;
    }
}

unsigned long CRecordManager::GetCurrentTimestamp()
{
#if defined(Q_OS_LINUX)
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return tv.tv_sec * 1000 + tv.tv_usec / 1000; // 毫秒
#else
    struct _timeb tb;
    _ftime(&tb);
    return tb.time * 1000 + tb.millitm; // 毫秒
#endif
}

int CRecordManager::CalculateRecordDuration(unsigned long ulStartTime, unsigned long ulEndTime)
{
    if (ulEndTime <= ulStartTime)
    {
        return 0;
    }
    
    return (ulEndTime - ulStartTime) / 1000; // 转换为秒
}
#endif