#include "stdafx.h"
#include "SongPlay.h"
#include "PlayQueue.h"


CSongPlayer::CSongPlayer()
{
    // zhuyg
    m_isWorking = FALSE;

    memset(m_IdUsed, FALSE, sizeof(m_IdUsed));
    #if 0
    for (int i=0; i<MAX_PLAY_COUNT; ++i)
    {
        m_pSongUnits[i] = new CSongUnit(i+1);
    }
    #endif
}

CSongPlayer::~CSongPlayer(void)
{
    StopWorking();
    ClearSongUnits();

    //20220427 静态初始化的互斥锁不需要,也不能用pthread_mutex_destroy销毁锁，否则将出错
    //pthread_mutex_destroy(&m_csSongPlayer);
}


// 开始播放歌曲
bool CSongPlayer::StartWorking(void)
{
    if(m_isWorking)
    {
        return FALSE;
    }

    // 线程属性设置 保留，待修改 zhuyg
    //pthread_t pth;
    //pthread_attr_t attr;
    //pthread_attr_init(&attr);

    int nRet = pthread_create(&m_pth, NULL, WorkThread,(void*)this);
    if(nRet != 0)
    {
        return FALSE;
    }

    return TRUE;

}


// 停止播放歌曲
void CSongPlayer::StopWorking(void)
{
    if(m_isWorking)
    {
        // 线程退出，待修改
        m_isWorking = FALSE;
        pthread_cancel(m_pth);
        pthread_join(m_pth,NULL);
    }
    
}


void *CSongPlayer::WorkThread(void *lpParam)
{
    CSongPlayer* pSongPlayer = (CSongPlayer*)lpParam;
    pSongPlayer->m_isWorking = TRUE;

    vector<StManualTask> PreManualTaskVec;

    while(pSongPlayer->m_isWorking)
    {
        unique_lock<shared_timed_mutex> ulk_songPlayer(pSongPlayer->m_csSongPlayer);

        uint32_t lTimeSleep = 50;
        static uint32_t lTimeSleepTotal=0;

        list<CSongUnit*>::iterator iter;
        int playUnitCount=0;
        for(iter = pSongPlayer->m_LSongUnits.begin(); iter != pSongPlayer->m_LSongUnits.end() && playUnitCount++<MAX_PLAY_COUNT;iter++)
        {
            CSongUnit *pSongUnit = *iter;
            if(pSongUnit->GetPlayStatus() == SPS_PLAY)	// 正在播放
            {
                //printf("T2\n");
                unsigned int nextUTime=0;
                bool IsSendStream = pSongUnit->SendStreamData(pSongUnit->GetIsMulticastUseNewCmd(), nextUTime);
                if(IsSendStream && (nextUTime < lTimeSleep)) 
                {
                    lTimeSleep = nextUTime;
                }
                if (!IsSendStream)	// 播放数据流结束
                {
                    if (pSongUnit->GetSource() == SOURCE_ALARM) // 报警声就重复播放
                    {
                        // 加入到日志文件
                        CMyString strTip;
                        strTip.Format(("Replay Source alarm : %s"), pSongUnit->GetPathName().C_Str());
                        g_Global.m_Network.AddLog(strTip);

                        pSongUnit->Replay();
                    }
                    else
                    {
                        //如果当前播放ID已失效，则不再继续
                         if ( pSongUnit->GetPlayStatus() == SPS_STOP )
                        {
                            continue;
                        }
                        //如果没有分区在使用（比如主机控制分区停止），停止创建下一首播放任务。
                        if( !(g_Global.m_Sections.HasPlaySections(pSongUnit->GetPlayID()) || g_Global.m_Sections.HasPrePlaySections(pSongUnit->GetPlayID())) )
                        {
                            if( !g_Global.m_Sections.HasOfflinePlaySection(pSongUnit->GetPlayID()) )
                            {
                                printf("PlayId:%d,No section,STOP...\n",pSongUnit->GetPlayID());
                                pSongPlayer->SetPlayStatus(pSongUnit->GetPlayID(), SPS_STOP);
                                break;
                            }
                        }
                        // 加入到日志文件
                        CMyString strTip;
                        strTip.Format(("Stop Play Source %d : %s,ID=%d"), pSongUnit->GetSource(), pSongUnit->GetPathName().C_Str(),pSongUnit->GetPlayID());
                        g_Global.m_Network.AddLog(strTip);

                        pSongPlayer->SetPlayStatus(pSongUnit->GetPlayID(), SPS_NEXT);

                        // 添加下一首歌曲的播放任务，原来只能播放一首当前歌曲
                        //g_Global.m_PlayQueue.PlaySourceEnd(pSongUnit->GetPlayID());

                        CPlayTask playTask(SOURCE_NEXT, {""}, NULL, 0,pSongUnit->GetUserAccount(),pSongUnit->GetPlayMode());
                        playTask.SetPlayID(pSongUnit->GetPlayID());
                        g_Global.m_PlayQueue.PushPlayTask(playTask);
                        #if 0
                        // 加入到日志文件
                        strTip.Format(("PushPlayTask:ID=%d"),pSongUnit->GetPlayID());
                        g_Global.m_Network.AddLog(strTip);
                        #endif
                    }
                }
            }
        }

        ulk_songPlayer.unlock();

#if SUPPORT_MANUALTASK_MANAGEMENT
        vector<StManualTask> manualTaskVec;
        if(lTimeSleepTotal >= 400)
        {
            lTimeSleepTotal=0;
            pSongPlayer->GetManualTask(manualTaskVec);
            if(!pSongPlayer->IsManualTaskEqual(PreManualTaskVec,manualTaskVec)) //不相等才发送给WEB
            {
                PreManualTaskVec=manualTaskVec;
                //printf("PreManualTaskVec!=manualTaskVec\n");
                pSongPlayer->PrintManualTask(manualTaskVec);
                g_Global.m_WebNetwork.ForwardManualTaskInfoToWeb(NULL,manualTaskVec);
            }
        }
#endif

        g_Global.m_nPlayUnitTimeout=0;
        
        //printf("nextTime=%d\n",lTimeSleep);
        // 保留，待修改
        lTimeSleepTotal += lTimeSleep;
        //printf("usleepTime=%d\n",lTimeSleep);
        usleep(lTimeSleep*1000);
    }

    // 保留，待修改
    //SetEvent(pSongPlayer->m_hThreadExit);
    return 0;

}

// 添加歌曲播放
CSongUnit* CSongPlayer::AddSongUnit(bool m_bmulticastNewCommand,unsigned char source,CMyString	strPathName,int PlayMode,int nPrePlayID,bool needLock)
{
    CSongUnit* pSongUnit = NULL;
    printf("AddSongUnit:source=%d,strPathName=%s,nPrePlayID=%d\n",source,strPathName.Data(),nPrePlayID);

    unique_lock<shared_timed_mutex> ulk_songPlayer;
    if(needLock)
    {
        ulk_songPlayer = unique_lock<shared_timed_mutex>(m_csSongPlayer);
    }

    int i = 0;

    if (nPrePlayID >= 1)
    {
        i = nPrePlayID - 1;

        //判断是否存在
        // 初始化成功
        pSongUnit = GetSongUnitByPlayID(nPrePlayID);
        if (pSongUnit != NULL)
        {
            printf("pSongUnit Found,stop PreSong!!!\n");
            //先停止播放，用来关掉原来的文件句柄
            pSongUnit->SetPlayStatus(SPS_STOP);

            if(pSongUnit->InitPlay(strPathName))
            {
                m_IdUsed[i] = TRUE;
                //相同的播放单元无需变更播放模式
            }
            else
            {
                delete pSongUnit;
                pSongUnit=NULL;
            }
        }
    }
    else
    {
        // 初始化成功
        for (i = 0; i < MAX_PLAY_COUNT; ++i)
        {
            // 未使用
            if (!m_IdUsed[i])
            {
                printf("create playId:%d\n",i+1);
                pSongUnit = new CSongUnit(i+1);
                if (pSongUnit->InitPlay(strPathName))
                {
                    m_IdUsed[i] = TRUE;

                    pSongUnit->SetIsMulticastUseNewCmd(m_bmulticastNewCommand);

                    //pSongUnit->SetUnitStatus(1);
                    pSongUnit->SetPlaymode(PlayMode);
                    m_LSongUnits.push_back(pSongUnit);
                }
                else
                {
                    delete pSongUnit;
                    pSongUnit=NULL;
                }
                break;
            }
        }
    }

    if (i == MAX_PLAY_COUNT)
    {
        g_Global.m_Network.AddLog("----------- i == MAX_PLAY_COUNT");
        NOTIFY("----------- i == MAX_PLAY_COUNT");
    }

    //return (m_LSongUnits.back());
    return pSongUnit;
}

// 移除歌曲播放
bool CSongPlayer::RemoveSongUnit(int playID)
{
    bool isOK = FALSE;

    g_Global.m_Sections.ClearOfflinePlaySection(playID);

    list<CSongUnit*>::iterator iter;
    for(iter = m_LSongUnits.begin(); iter != m_LSongUnits.end(); iter++)
    {
        CSongUnit *pSongUnit = *iter;
        if ( pSongUnit->GetPlayID() == playID )
        {
            //pSongUnit->FreePlay();

            printf("remove SongUnit:%d\n",playID);
            //要先释放内存再从列表remove此元素
            delete pSongUnit;
            m_LSongUnits.erase(iter);
            isOK = TRUE;
            m_IdUsed[playID-1] = FALSE;
            break;
        }
    }

    return isOK;

}


// 清除歌曲播放
void CSongPlayer::ClearSongUnits()
{
    unique_lock<shared_timed_mutex> ulk_songPlayer(m_csSongPlayer);

    // 释放内存

    list<CSongUnit*>::iterator iter;
    for(iter = m_LSongUnits.begin(); iter != m_LSongUnits.end();)
    {
        CSongUnit *pSongUnit = *iter;
        delete pSongUnit;
        iter=m_LSongUnits.erase(iter);
    }

    for (size_t i=0; i<MAX_PLAY_COUNT; ++i)
    {
        m_pSongUnits[i] = NULL;
    }
}


// 通过ID来查找播放歌曲
CSongUnit* CSongPlayer::GetSongUnitByPlayID(int playID)
{
    if (playID <= 0 || playID > MAX_PLAY_COUNT)
    {
        return NULL;
    }

    list<CSongUnit*>::iterator iter;
    for(iter = m_LSongUnits.begin(); iter != m_LSongUnits.end(); iter++)
    {
        CSongUnit *pSongUnit = *iter;
        if ( pSongUnit->GetPlayID() == playID )
        {
            return pSongUnit;
        }
    }
    return NULL;
}


// 判断播放ID是否有效
bool CSongPlayer::IsPlayIDValid(int playID)
{
    if (playID <= 0 || playID > MAX_PLAY_COUNT)
    {
        return false;
    }
    else
    {
        return m_IdUsed[playID - 1];
    }
}

// 设置播放状态
bool CSongPlayer::SetPlayStatus(int playID, SongPS status, bool needLock)
{
    if(playID<=0)
    {
        return false;
    }
    bool isOK = FALSE;

    unique_lock<shared_timed_mutex> ulk_songPlayer;
    shared_lock<shared_timed_mutex> slk_songPlayer;
    if(needLock)
    {
        if(status == SPS_STOP)
        {
            ulk_songPlayer = unique_lock<shared_timed_mutex>(g_Global.m_PlayQueue.m_SongPlayer.m_csSongPlayer);
        }
        else
        {
            slk_songPlayer = shared_lock<shared_timed_mutex>(g_Global.m_PlayQueue.m_SongPlayer.m_csSongPlayer);
        }
    }


    CSongUnit *pSongUnit = GetSongUnitByPlayID(playID);

    if (pSongUnit != NULL)
    {
        pSongUnit->SetPlayStatus(status);

        if (status == SPS_STOP)
        {
            #if 1
            printf("RemoveSongUnit:playID=%d\n",playID);

            //如果是播放特定歌曲音源，且播放类型时tts，停止播放单元后需要删除临时文件
            bool  isSpecifiedPlay = (pSongUnit->GetSource());
            int   playType    = pSongUnit->GetPlayType(); //播放类型，1为歌曲播放，2为TTS
            if(isSpecifiedPlay && playType == 2)
            {
                //删除临时文件
                RemoveFile(pSongUnit->GetPathName().Data());
            }

            RemoveSongUnit(playID);
            #else
            printf("Stop:playID=%d\n",playID);
            //pSongUnit->SetUnitStatus(0);
            #endif
        }

        isOK = TRUE;
    }

    return isOK;
}

bool CSongPlayer::StartListenInSong(CSection *pSection)
{
    if(pSection == NULL)
    {
        return FALSE;
    }

    bool isOK = FALSE;

    CSongUnit *pSongUnit = GetSongUnitByPlayID(pSection->GetPlayID());

    if (pSongUnit != NULL)
    {
        // 设置监听
        song_info_t songInfo;
        song_header_t songHeader = pSongUnit->GetSongHeader();
        songInfo.isMP3 = (pSongUnit->GetSongFormat() == FORMAT_MP3);
        songInfo.samp_freq = songHeader.samp_freq;
        songInfo.channels = songHeader.channels;
        songInfo.bit_samp = songHeader.bit_samp;

        // Linux服务器 监听歌曲  保留，待修改
        int nCount = pSection->GetListenCount();
        for(int i = 0; i < nCount; i++)
        {
            LPCSection pListenSec = g_Global.m_Sections.GetSectionByMac(pSection->GetListenSec(i).data());
            if(pListenSec != NULL && pListenSec->GetPlayID() != pSection->GetPlayID())
            {
                song_header_t	songHeader	= pSongUnit->GetSongHeader();
                CMyString		strName		= GetNameByPathName(pSongUnit->GetPathName(), TRUE);

                g_Global.m_Network.m_CmdSend.CmdNotifyStreamSource(*pListenSec,
                                                                   pSongUnit->GetSource(),
                                                                   pSongUnit->GetSongFormat(),
                                                                   songHeader.samp_freq,
                                                                   (unsigned char)songHeader.bit_samp,
                                                                   (unsigned char)songHeader.channels,
                                                                   strName.C_Str(),
                                                                   pSongUnit->GetPlayIP(),
                                                                   pSongUnit->GetPlayPort(),
                                                                   pSongUnit->GetVolume(),
                                                                   pSongUnit->GetSongLength(),
                                                                   pSongUnit->GetTotalFrames(),
                                                                   pSongUnit->GetCurrentFrame(),
                                                                   pSongUnit->GetSongMd5(),
                                                                   pSongUnit->GetIsMulticastUseNewCmd());

                // 更新分区播放信息
                pListenSec->SetPlayID(pSongUnit->GetPlayID());

                pSection->SetListening(TRUE);

                //如果播放单元是暂停状态，那么立即给监听音箱设置暂停状态
                if( pSongUnit->GetPlayStatus() == SPS_PAUSE_MANUAL)
                {
                    g_Global.m_Network.m_CmdSend.CmdSetSingleDevicePlayStatus(PS_PAUSE, *pListenSec);
                }

                isOK = TRUE;
            }
        }
    }

    return isOK;
}

// 停止监听歌曲
bool CSongPlayer::StopListenInSong(CSection *pSection)
{
    if(pSection == NULL)
    {
        return FALSE;
    }

    bool isOK = FALSE;

    pSection->SetListening(FALSE);
    int nCount = pSection->GetListenCount();

    for(int i=0; i < nCount; i++)
    {
        string strMac = pSection->GetListenSec(i);
        LPCSection pListenSec = g_Global.m_Sections.GetSectionByMac(strMac.data());
        if(pListenSec != NULL && pSection->GetPlayID() == pListenSec->GetPlayID())
        {
            g_Global.m_Network.m_CmdSend.CmdSetIdleStatus(*pListenSec);
            pListenSec->SetPlayID(-1);
        }
    }
    pSection->ClearListenSec();

    return isOK;
}

//在pSection没有更新playId前更新监听设备的PlayID,不适用于一般情况
bool CSongPlayer::UpdateListenInSong(CSection *pSection,CSongUnit *pSongUnit)
{
    if(pSection == NULL)
    {
        return FALSE;
    }

    bool isOK = FALSE;

    if (pSongUnit != NULL)
    {
        // 设置监听
        song_info_t songInfo;
        song_header_t songHeader = pSongUnit->GetSongHeader();
        songInfo.isMP3 = (pSongUnit->GetSongFormat() == FORMAT_MP3);
        songInfo.samp_freq = songHeader.samp_freq;
        songInfo.channels = songHeader.channels;
        songInfo.bit_samp = songHeader.bit_samp;

        // Linux服务器 监听歌曲  保留，待修改
        int nCount = pSection->GetListenCount();
        for(int i = 0; i < nCount; i++)
        {
            LPCSection pListenSec = g_Global.m_Sections.GetSectionByMac(pSection->GetListenSec(i).data());
            if(pListenSec != NULL && pSection->GetPlayID() == pListenSec->GetPlayID())
            {
                if(pListenSec->GetPlayID() != pSongUnit->GetPlayID())
                {
                    song_header_t	songHeader	= pSongUnit->GetSongHeader();
                    CMyString		strName		= GetNameByPathName(pSongUnit->GetPathName(), TRUE);

                    g_Global.m_Network.m_CmdSend.CmdNotifyStreamSource(*pListenSec,
                                                                    pSongUnit->GetSource(),
                                                                    pSongUnit->GetSongFormat(),
                                                                    songHeader.samp_freq,
                                                                    (unsigned char)songHeader.bit_samp,
                                                                    (unsigned char)songHeader.channels,
                                                                    strName.C_Str(),
                                                                    pSongUnit->GetPlayIP(),
                                                                    pSongUnit->GetPlayPort(),
                                                                    pSongUnit->GetVolume(),
                                                                    pSongUnit->GetSongLength(),
                                                                    pSongUnit->GetTotalFrames(),
                                                                    pSongUnit->GetCurrentFrame(),
                                                                    pSongUnit->GetSongMd5(),
                                                                    pSongUnit->GetIsMulticastUseNewCmd());

                    // 更新分区播放信息
                    pListenSec->SetPlayID(pSongUnit->GetPlayID());

                    pSection->SetListening(TRUE);
                    isOK = TRUE;
                }
            }
        }
    }

    return isOK;
}









//获取手动任务列表
void CSongPlayer::GetManualTask(vector<StManualTask>& manualTaskVec)
{
    list<CSongUnit*>::iterator iter;
    int index=0;
    shared_lock<shared_timed_mutex> slk_songPlayer(m_csSongPlayer);
    for(iter = m_LSongUnits.begin(); iter != m_LSongUnits.end(); iter++)
    {
        CSongUnit *pSongUnit = *iter;
        
        StManualTask task;
        task.nSource = pSongUnit->GetSource();
        //目前只获取网络点播任务
        if( task.nSource != SOURCE_PLAY )
        {
            continue;
        }
        task.nPlayID = pSongUnit->GetPlayID();
        task.nPlayMode = pSongUnit->GetPlayMode();
        task.nPlayStatus = pSongUnit->GetPlayStatus();
        if(task.nPlayStatus == SPS_NEXT)
            task.nPlayStatus = SPS_PLAY;
        if(task.nPlayStatus == SPS_PAUSE_AUTO)
            task.nPlayStatus = SPS_PAUSE_MANUAL;
        task.strCurPathName = pSongUnit->GetPathName();
        /*********暂保留**************/
        task.nPlayList = pSongUnit->GetListIndex();
        task.nPlaySong = pSongUnit->GetSongIndex();
        /*********暂保留**************/
        task.strTaskName.Format("%s%d",LANG_STR(LANG_SECTION_ZONE_GROUP, "Temporary Task", ("临时任务")).Data(),index+1);
        task.strUserAccount = pSongUnit->GetUserAccount();

        #if SUPPORT_MANUALTASK_PROGRESS
        task.nCurPlayTime = pSongUnit->GetCurrentPlayTime();
        task.nSongDuration = pSongUnit->GetTotalDuration();
        #endif
        
        g_Global.m_Sections.GetPlaySectionsVec(task.nPlayID,task.vSections);

        manualTaskVec.push_back(task);
        index++;
    }

}




//对比两个手动任务是否相等
bool CSongPlayer::IsManualTaskEqual(vector<StManualTask>& manualTaskVec1,vector<StManualTask>& manualTaskVec2)
{
    if(manualTaskVec1.size()!=manualTaskVec2.size())
        return false;
    int nTaskCount = manualTaskVec1.size();
    bool result=true;
    for(int i=0;i<nTaskCount;i++)
    {
        if(manualTaskVec1[i].nPlayID != manualTaskVec2[i].nPlayID)
        {
            result=false;
        }
        else if(manualTaskVec1[i].nSource != manualTaskVec2[i].nSource)
        {
            result=false;            
        }
        else if(manualTaskVec1[i].nPlayMode != manualTaskVec2[i].nPlayMode)
        {
            result=false;
        }
        else if(manualTaskVec1[i].nPlayStatus != manualTaskVec2[i].nPlayStatus && manualTaskVec1[i].nPlayStatus != SPS_NEXT && manualTaskVec2[i].nPlayStatus != SPS_NEXT )
        {
            //printf("eq4:PreStatus=%d,CurStatus=%d\n",manualTaskVec1[i].nPlayStatus,manualTaskVec2[i].nPlayStatus);
            result=false;
        }
        else if(manualTaskVec1[i].strUserAccount != manualTaskVec2[i].strUserAccount)
        {
            result=false;
        }
        else if(manualTaskVec1[i].strTaskName != manualTaskVec2[i].strTaskName)
        {
            result=false;
        }
        else if(manualTaskVec1[i].strCurPathName != manualTaskVec2[i].strCurPathName)
        {
            result=false;
        }
        else if(manualTaskVec1[i].vSections.size() != manualTaskVec2[i].vSections.size())
        {
            result=false;
        }
        else
        {
            int secCnt=manualTaskVec1[i].vSections.size();
            for(int t=0;t<secCnt;t++)
            {
                if( strcmp(manualTaskVec1[i].vSections[t].c_str(),manualTaskVec2[i].vSections[t].c_str()) )
                {
                    result=false;
                    break;
                }
            }

            if(result == true)
            {
                //如果同一首歌曲循环播放，那么重新开始播放后也要发送一次
                //简单判断方法：原来的歌曲播放时间比现在的歌曲播放时间更大，那么代表重新开始播放
                //但是如果用户人为拖动进度条到前面，那么也无法避免，不过多发一次无影响
                if(manualTaskVec1[i].nCurPlayTime > manualTaskVec2[i].nCurPlayTime)
                {
                    result=false;
                }
                else
                {
                    manualTaskVec1[i].nCurPlayTime = manualTaskVec2[i].nCurPlayTime;
                    //printf("manualTaskVec1[i].nCurPlayTime=%d,manualTaskVec2[i].nCurPlayTime=%d\n",manualTaskVec1[i].nCurPlayTime,manualTaskVec2[i].nCurPlayTime);
                }
            }
        }
        if(result == false)
        {
            break;
        }
    }
    return result;
}




void CSongPlayer::PrintManualTask(vector<StManualTask>& manualTaskVec)
{
    int nTaskCount = manualTaskVec.size();
    printf("Manual Task Cnt:%d\n",nTaskCount);
    #if 0
    for(int i=0;i<nTaskCount;i++)
    {
        printf("[%d] Id:%d,User=%s,PathName=%s,PlayMode=%d,Status=%d\n",i,manualTaskVec[i].nPlayID,manualTaskVec[i].strUserAccount.data(),manualTaskVec[i].strCurPathName.Data(),manualTaskVec[i].nPlayMode,manualTaskVec[i].nPlayStatus);
    }
    #endif
}

#if SUPPORT_REMOTE_CONTROLER
//设置远程遥控器任务
void CSongPlayer::SetRemoteControlerTask(CMyString remoteMac,int event)
{
    vector<CSongUnit> vecRemoteSongUnit;
    list<CSongUnit*>::iterator iter;

    //全部处于播放，就是播放状态,否则就是暂停状态。
    bool isAllTaskInPlay=true;

    //根据MAC获取对应的遥控器
    LPCSection lpRemoteControlerSec = g_Global.m_RemoteControlers.GetSectionByMac(remoteMac);
    if(!lpRemoteControlerSec)
        return;
    vector<CMyString> vecMac;
    unsigned int nSectionCount = lpRemoteControlerSec->m_pRemoteControler->GetAllTaskSelectedSections(vecMac);

    if(event == RCA_STOP || event == RCA_VOL_ADD || event == RCA_VOL_MIN)
    {
        unsigned int pSecIndexs[MAX_SECTION_COUNT_FORMAL] = {0};

        if(event == RCA_STOP)
        {
            // 获取到分区ID
            unsigned int uCount = 0;
            for (unsigned int i=0; i<nSectionCount; ++i)
            {
                CSection* pSection = g_Global.m_Sections.GetSectionByMac(CMyString(vecMac[i]));

                if (pSection != NULL && pSection->IsOnline())
                {
                    pSecIndexs[uCount++] = pSection->GetID() - 1;

                    //停止，重置最近音源
                    if(pSection->GetProSource() == PRO_LOCAL_PLAY || pSection->GetProSource() == PRO_TIMING || CProtocol::IsAudioCollectorSrc(pSection->GetProSource()))
                    {
                        pSection->m_PlayedRecently.m_nRecentSrc = PRO_IDLE;
                    }
                    pSection->m_PlayedRecently.m_bSongTimerEndResumeAc = PRO_IDLE;
                }

                if(uCount >= MAX_SECTION_COUNT_FORMAL)
                    break;
            }
            //将分区设置为空闲
            if(WP_IS_CENTRALIZED)
            {
                CPlayTask playTask(SOURCE_STOP, {""}, pSecIndexs, uCount);
                g_Global.m_PlayQueue.PushPlayTask(playTask);
            }

            g_Global.m_Network.m_CmdSend.CmdSetIdleStatus(pSecIndexs, uCount);
        }
        else if(event == RCA_VOL_ADD || event == RCA_VOL_MIN)
        {
            bool isVolUp=(event == RCA_VOL_ADD);
            for(int i=0;i<vecMac.size();i++)
            {
                LPCSection lpSection = g_Global.m_Sections.GetSectionByMac(vecMac[i]);
                if(lpSection && lpSection->IsOnline() && lpSection->GetPlayID()>0)
                {
                    g_Global.m_Network.m_CmdSend.CmdSetVolumeAddMin(isVolUp, 5, *lpSection);
                }
            }
        }

        return;
    }

    if(event == RCA_PAUSE_RESUME)
    {
        for(iter = m_LSongUnits.begin(); iter != m_LSongUnits.end(); iter++)
        {
            CSongUnit *pSongUnit = *iter;

            if( pSongUnit->GetSource() != SOURCE_REMOTE_PLAY )
            {
                continue;
            }

            stRemoteTaskInfo &remoteTaskInfo=pSongUnit->GetRemoteControlTask();
            CRemoteControlTask *remoteTask=NULL;

            //不是特定的遥控器，不处理
            if(remoteTaskInfo.strMac != remoteMac)
            {
                continue;
            }

            else if (remoteTaskInfo.nTaskID <=0 || remoteTaskInfo.nTaskID > lpRemoteControlerSec->m_pRemoteControler->GetTaskCnt())
            {
                continue;
            }
            else
            {
                remoteTask=lpRemoteControlerSec->m_pRemoteControler->lpGetTaskDetail(remoteTaskInfo.nTaskID);
            }

            int playStatus = pSongUnit->GetPlayStatus();
            if( playStatus == SPS_PAUSE_AUTO || playStatus == SPS_PAUSE_MANUAL )
            {
                isAllTaskInPlay = false;
            }
        }
    }
    
    for(iter = m_LSongUnits.begin(); iter != m_LSongUnits.end(); iter++)
    {
        CSongUnit *pSongUnit = *iter;

        if( pSongUnit->GetSource() != SOURCE_REMOTE_PLAY )
        {
            continue;
        }

        stRemoteTaskInfo &remoteTaskInfo=pSongUnit->GetRemoteControlTask();
        CRemoteControlTask *remoteTask=NULL;

        //不是特定的遥控器，不处理
        if(remoteTaskInfo.strMac != remoteMac)
        {
            continue;
        }

        if (remoteTaskInfo.nTaskID <=0 || remoteTaskInfo.nTaskID > lpRemoteControlerSec->m_pRemoteControler->GetTaskCnt())
        {
            continue;
        }
        else
        {
            remoteTask=lpRemoteControlerSec->m_pRemoteControler->lpGetTaskDetail(remoteTaskInfo.nTaskID);
        }

        if(event == RCA_PAUSE_RESUME)
        {
            SongPS nPlayStatus = isAllTaskInPlay?SPS_PAUSE_MANUAL:SPS_PLAY;
            pSongUnit->SetPlayStatus(nPlayStatus);

            // 所有的分区都会轮询一遍
            for (int j=0; j<nSectionCount; ++j)
            {
                CSection& section  = g_Global.m_Sections.GetSection(j);

                if (section.IsOnline() && pSongUnit->GetPlayID() == section.GetPlayID())
                {
                    if( nPlayStatus == SPS_PLAY )
                    {
                        if(section.GetProSource() == PRO_LOCAL_PLAY)
                        {
                            //从暂停到播放
                            song_header_t	songHeader	= pSongUnit->GetSongHeader();
                            CMyString		strName		= GetNameByPathName(pSongUnit->GetPathName(), TRUE);;

                            // 下发播放命令
                            g_Global.m_Network.m_CmdSend.CmdNotifyStreamSource(
                                section,
                                pSongUnit->GetSource(),
                                pSongUnit->GetSongFormat(),
                                songHeader.samp_freq,
                                (BYTE)songHeader.bit_samp,
                                (BYTE)songHeader.channels,
                                strName.C_Str(),
                                pSongUnit->GetPlayIP(),
                                pSongUnit->GetPlayPort(),
                                pSongUnit->GetVolume(),
                                pSongUnit->GetSongLength(),
                                pSongUnit->GetTotalFrames(),
                                pSongUnit->GetCurrentFrame(),
                                pSongUnit->GetSongMd5(),
                                pSongUnit->GetIsMulticastUseNewCmd(),
                                0x00);	// 确认标志 0x00新播放 0x01继续播放（发送继续播放命令时，设备会延迟播放，导致声音不同步）
                        }
                    }
                    else if(nPlayStatus == SPS_PAUSE_MANUAL )
                    {
                        //从播放到暂停
                        // 给分区发送暂停命令
                        if(section.GetProSource() == PRO_LOCAL_PLAY)
                        {
                            g_Global.m_Network.m_CmdSend.CmdSetSingleDevicePlayStatus(PS_PAUSE, section);
                        }
                    }
                }
            }
        }
        else if(event == RCA_PRE_SONG || event == RCA_NXT_SONG)
        {
            CPlayTask playTask((event == RCA_PRE_SONG)?SOURCE_PRE_MANUAL:SOURCE_NXT_MANUAL, {""}, NULL, 0,pSongUnit->GetUserAccount(),pSongUnit->GetPlayMode());
            playTask.SetPlayID(pSongUnit->GetPlayID());
            g_Global.m_PlayQueue.PushPlayTask(playTask);
        }
    }
}

//停止遥控器任务
void CSongPlayer::StopRemoteControlerTask(CMyString remoteMac)
{
    list<CSongUnit*>::iterator iter;
    for(iter = m_LSongUnits.begin(); iter != m_LSongUnits.end(); iter++)
    {
        CSongUnit *pSongUnit = *iter;
        if( pSongUnit->GetSource() != SOURCE_REMOTE_PLAY )
        {
            continue;
        }

        stRemoteTaskInfo &remoteTaskInfo=pSongUnit->GetRemoteControlTask();
        //不是特定的遥控器，不处理
        if(remoteTaskInfo.strMac != remoteMac)
        {
            continue;
        }

        int playId=pSongUnit->GetPlayID();
        SetPlayStatus(playId,SPS_STOP);
        //todo 立即将播放id为当前songunit的播放id的分区设置空闲状态，参考手动任务播放状态处理。
        
    }
}
#endif