#ifndef SONGUNIT_H
#define SONGUNIT_H

#include <QtCore/qglobal.h>

#if defined(Q_OS_LINUX)
#include <mpg123.h>
#else
#include "mpg123.h"
#endif
#include "Global/CType.h"
#include "Tools/tools.h"
#include "Tools/LxTimer.h"
#include "ListenIn.h"

#include "Global/Const.h"

#if SUPPORT_REMOTE_CONTROLER
#include "Model/Device/RemoteControl.h"
#endif

#include "string.h"

#define STREAM_BUF_SIZE		(1024 *3)
#define WAV_PER_FRAME_SIZE	1152
#define DECODE_BUF_SIZE		(STREAM_BUF_SIZE * 10)


// 播放来自本地或上级主机播放列表
typedef enum
{
    PLAYLIST_FORM_LOCAL,        // 播放来自本地播放列表
    PLAYLIST_FORM_HIGHER_HOST   // 播放来自上级主机播放列表
}PlayFrom;



// 歌曲播放状态
typedef enum
{
    SPS_PLAY	= 1,	// 播放
    SPS_PAUSE_MANUAL   = 2,	// 暂停(手动)
    SPS_PAUSE_AUTO   = 3,	// 暂停(自动)
    SPS_STOP    = 4,	// 停止
    SPS_NEXT            // 等待下一曲

}SongPS;


// 歌曲播放状态
typedef enum
{
    SAC_PRE	= 1,	    // 上一曲
    SAC_NEXT   = 2,	    // 下一曲
}SongAction;

// 歌曲格式
typedef enum
{
    FORMAT_MP3 = 1,
    FORMAT_WAV,
    FORMAT_OTHER,

}SongFormat;

// 歌曲文件头信息
typedef struct SongHeader
{
    unsigned int	samp_freq;		// 采样频率
    SHORT           channels;		// 通道数，单声道为1，双声道为2
    SHORT           bit_samp;		// bits per sample (又称量化位数)
    unsigned int    bitrate;        // Bitrate of the frame (kbps)
    unsigned int    nDuration;      // 歌曲长度
}song_header_t;



typedef struct WaveHeader
{
    BYTE	riff[4];		// 资源交换文件标志
    UINT32	size;			// 从下个地址开始到文件结尾的字节数
    BYTE	wave_flag[4];	// wave文件标识
    BYTE	fmt[4];			// 波形格式标识
    UINT	fmt_len;		// 过滤字节(一般为00000010H)
    SHORT	tag;			// 格式种类，值为1时，表示PCM线性编码
    SHORT	channels;		// 通道数，单声道为1，双声道为2
    UINT32	samp_freq;		// 采样频率
    UINT32	byte_rate;		// 数据传输率 (每秒字节＝采样频率×每个样本字节数)
    SHORT	block_align;	// 块对齐字节数 = channles * bit_samp / 8
    SHORT	bit_samp;		// bits per sample (又称量化位数)

} wave_header_t;


typedef struct WaveStruct
{
    //（1）文件头格式
    wave_header_t header;
    //（2）数据格式
    BYTE	data_flag[4];	// 数据标识符
    UINT32	length;			// 采样数据总数
//	UINT32 *pData;			// 数据部分
} wave_t;



//手动任务结构体
typedef struct
{
    string          strUserAccount;         // 创建的用户名
    CMyString       strTaskName;            // 任务名称（暂保留）
    int				nPlayID;				// 播放ID，1－200
    BYTE			nSource;			    // 音源值（暂保留）
    vector<string> vSections;			    // 分区信息
    CMyString       strCurPathName;         // 当前歌曲歌曲文件路径名
    int             nPlayList;              // 播放列表号（暂保留）
    int             nPlaySong;              // 播放歌曲号（暂保留）
    int             nPlayMode;              // 播放模式
    int             nPlayStatus;            // 播放状态
#if SUPPORT_MANUALTASK_PROGRESS
    int             nSongDuration;          // 播放歌曲总时长
    int             nCurPlayTime;           // 当前播放时间
#endif
}StManualTask;


class CSongUnit
{
public:
    CSongUnit(int nPlayID);
    virtual ~CSongUnit();
    void    InitData();

public:
    // 开始播放
    BOOL    InitPlay(CMyString strPathName);
    void    FreePlay();
    BOOL    InitWav(CMyString strPathName);
    BOOL    InitMP3(CMyString strPathName);
    BOOL	IsValidParam(song_header_t header);

    // 歌曲信息
    CMyString       GetPathName();
    SongFormat      GetSongFormat();
    song_header_t   GetSongHeader();
    PlayFrom        GetPlayFrom();
    void            SetPlayFrom(PlayFrom playFrom);

    //账户信息
    string GetUserAccount()    { return m_strUserAccount; }
    void   SetUserAccount(string strUserAccount);

    // 设置歌曲位置
    void    SetSongPos(int nList, int nSong);
    int     GetListIndex();
    int		GetSongIndex();
    // ctime_t 类型与源码有区别  zhuyg
    void    SetStopPlayTime(ctime_t  tStopPlay);
    ctime_t GetStopPlayTime();
    BYTE    GetSource(void);
    void    SetSource(BYTE src);

    // 播放状态
    SongPS  GetPlayStatus();
    void	SetPlayStatus(SongPS status);
    void	Replay(void);		// 重新播放

    // 播放音量
    BYTE	GetVolume(void);
    void	SetVolume(BYTE vol);

    // 发送数据
    BOOL    SendStreamData(bool isUseMulticastNewCmd, unsigned int &nextTime);

    // 播放ID
    int		GetPlayID();
    //播放模式
    int     GetPlayMode();

    void    SetPlaymode(int playMode);


    vector<CMyString> GetVecPathNames(void) { return m_vecStrPathNames;}
    void SetVecPathNames(vector<CMyString> vecPathNames) { m_vecStrPathNames=vecPathNames;}
    
    int     GetPlayType()   {return m_nPlayType;}
    void    SetPlayType(int playType)   {m_nPlayType = playType;}

    int     GetPlayCount()   {return m_nPlayCount;}
    void    SetPlayCount(int playCount)   {m_nPlayCount = playCount;}
    

    // SOCKET相关
    USHORT  GetPlayPort();
    LPCSTR  GetPlayIP();

    void	StartCounter(void);		// 开始计时，加上这句是为了测试播放

    int     GetUnitStatus(){ return m_nUnitStatus;}
    void    SetUnitStatus(int status){ m_nUnitStatus = status;}

#if SUPPORT_MANUALTASK_PROGRESS
    // 歌曲总时长(S)
    unsigned int	GetTotalDuration();
    // 歌曲当前播放时间
    unsigned int	GetCurrentPlayTime();
    // 设置播放进度
    bool    SetUnitProgress(int playTime);
#endif
    // 歌曲总共帧数
    unsigned int  GetTotalFrames();
    // 歌曲当前帧数
    unsigned int  GetCurrentFrame();
    // 歌曲文件大小
    unsigned int GetSongLength();
    //歌曲md5
    string GetSongMd5();

    #if SUPPORT_REMOTE_CONTROLER
    void            SetRemoteControlTask(stRemoteTaskInfo remoteTask )   {m_stRemoteTask = remoteTask;}
    stRemoteTaskInfo&   GetRemoteControlTask()  {return m_stRemoteTask;}
    #endif

    int GetSpecifiedPlayNextSongIndex(int baseSongIndex);

    bool GetIsMulticastUseNewCmd() {return b_multicast_use_new_cmd;}
    void SetIsMulticastUseNewCmd(bool b_useMulticastNewCmd) {b_multicast_use_new_cmd=b_useMulticastNewCmd;}

private:

    // 得到下一帧数据
    BOOL	GetNextFrame(BYTE ** pData, int *len);



private:
    SongFormat		m_SongFormat;			// 歌曲格式
    song_header_t	m_SongHeader;			// 歌曲文件头信息
    int				m_nList;				// 在哪个列表（定时播放时没用）
    int				m_nSong;				// 在列表中哪首歌曲
    ctime_t         m_tStopPlay;			// 停止播放时间

    // 播放信息
    int				m_nPlayID;				// 播放ID，1－200
    int             m_PlayMode;             // 播放模式
    SongPS			m_playStatus;			// 播放状态
    BYTE			m_nSource;				// 音源值
    BYTE			m_nVolume;				// 音量值

    int             m_nPlayType;            // 播放类型
    int             m_nPlayCount;           // 播放次数
    // 歌曲文件信息
    CMyString       m_strPathName;          // 歌曲文件路径名
    vector<CMyString>   m_vecStrPathNames;     // 歌曲文件集合
    MyCFile			m_fileSong;				// 歌曲文件
    PlayFrom        m_PlayFrom;             // 播放来自哪个播放列表，分控播放时才有作用

    int				m_nWavFileDataPos;		// WAV数据位置
    int				m_nFileOffset;			// 文件播放的偏移量（字节数）
    double          m_lfDurationPerFrame;	// 采样每帧所需要的时间（S）
    int				m_nBytesPerFrame;		// 每一帧有多少个字节
    int				m_nPlayedFrames;		// 从开始播放至今，放了多帧。改变播放进度后重新开始计数。

    unsigned int    m_nTotalFramesNum;         // 总共frames个数
    unsigned int    m_nCurrentFramesNum;       // 当前frames计数

    string          m_strSongMd5;              // 歌曲MD5

    // 歌曲播放时间 保留，待修改 高精度计时器
	#if defined(Q_OS_LINUX) 
    CLxTimer         m_timeStart;             // 歌曲开始时间，暂停歌曲后变成恢复后的时间
    CLxTimer         m_timePause;             // 歌曲结束时间
	#else
	LARGE_INTEGER         m_timeStart;             // 歌曲开始时间，暂停歌曲后变成恢复后的时间
    LARGE_INTEGER         m_timePause;             // 歌曲结束时间
    LARGE_INTEGER         m_frequency;			// 硬件支持的高精度计数器的频率
	#endif
	
    string          m_strUserAccount;         // 账户名

    // Socket相关信息 socket 保留，待修改
    MyUdpNode*		m_pUdpSocket;			// SOCKET
    USHORT			m_uPort;                        // 组播端口
    CHAR            m_szMulticastIP[16];            // 组播IP
    BYTE            m_streamBuf[STREAM_BUF_SIZE];   // 数据缓冲区

    mpg123_handle	*m_mh;

    int m_nUnitStatus;                    //歌曲播放单元状态  0未使用 1使用中

    #if SUPPORT_REMOTE_CONTROLER
    stRemoteTaskInfo  m_stRemoteTask;       //远程遥控播放任务
    #endif

    pthread_mutex_t     m_csSongUnit;


    bool b_multicast_use_new_cmd;           //组播使用新的命令字

};


#endif // SONGUNIT_H
