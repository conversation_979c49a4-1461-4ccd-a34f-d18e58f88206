#ifndef AUXPLAY_H
#define AUXPLAY_H

#include <QtCore/qglobal.h>

#if defined(Q_OS_LINUX)
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include "Tools/CTime.h"

#include <alsa/asoundlib.h>
#include "SongUnit.h"

typedef unsigned char  uint8_t;
typedef unsigned short uint16_t;
typedef unsigned int   uint32_t;
//typedef long long off64_t;

#if __BYTE_ORDER == __LITTLE_ENDIAN
#define COMPOSE_ID(a,b,c,d)	((a) | ((b)<<8) | ((c)<<16) | ((d)<<24))
#define LE_SHORT(v)		      (v)
#define LE_INT(v)		        (v)
#define BE_SHORT(v)		      bswap_16(v)
#define BE_INT(v)		        bswap_32(v)
#elif __BYTE_ORDER == __BIG_ENDIAN
#define COMPOSE_ID(a,b,c,d)	((d) | ((c)<<8) | ((b)<<16) | ((a)<<24))
#define LE_SHORT(v)		      bswap_16(v)
#define LE_INT(v)		        bswap_32(v)
#define BE_SHORT(v)		      (v)
#define BE_INT(v)		        (v)
#else
#error "Wrong endian"
#endif

#define WAV_RIFF		COMPOSE_ID('R','I','F','F')
#define WAV_WAVE		COMPOSE_ID('W','A','V','E')
#define WAV_FMT			COMPOSE_ID('f','m','t',' ')
#define WAV_DATA		COMPOSE_ID('d','a','t','a')

/* WAVE fmt block constants from Microsoft mmreg.h header */
#define WAV_FMT_PCM             0x0001
#define WAV_FMT_IEEE_FLOAT      0x0003
#define WAV_FMT_DOLBY_AC3_SPDIF 0x0092
#define WAV_FMT_EXTENSIBLE      0xfffe

/* Used with WAV_FMT_EXTENSIBLE format */
#define WAV_GUID_TAG		"/x00/x00/x00/x00/x10/x00/x80/x00/x00/xAA/x00/x38/x9B/x71"

/* it's in chunks like .voc and AMIGA iff, but my source say there
   are in only in this combination, so I combined them in one header;
   it works on all WAVE-file I have
 */
typedef struct WAVHeader {
    uint32_t magic;		/* 'RIFF' */
    uint32_t length;		/* filelen */
    uint32_t type;		/* 'WAVE' */
} WAVHeader_t;

typedef struct WAVFmt {
    uint32_t magic;  /* 'FMT '*/
    uint32_t fmt_size; /* 16 or 18 */
    uint16_t format;		/* see WAV_FMT_* */
    uint16_t channels;
    uint32_t sample_rate;	/* frequence of sample */
    uint32_t bytes_p_second;
    uint16_t blocks_align;	/* samplesize; 1 or 2 bytes */
    uint16_t sample_length;	/* 8, 12 or 16 bit */
} WAVFmt_t;

typedef struct WAVFmtExtensible {
    WAVFmt_t format;
    uint16_t ext_size;
    uint16_t bit_p_spl;
    uint32_t channel_mask;
    uint16_t guid_format;	/* WAV_FMT_* */
    uint8_t guid_tag[14];	/* WAV_GUID_TAG */
} WAVFmtExtensible_t;

typedef struct WAVChunkHeader {
    uint32_t type;		/* 'data' */
    uint32_t length;		/* samplecount */
} WAVChunkHeader_t;

typedef struct WAVContainer {
    WAVHeader_t header;
    WAVFmt_t format;
    WAVChunkHeader_t chunk;
} WAVContainer_t;


typedef struct SNDPCMContainer {
    snd_pcm_t *handle;
    snd_output_t *log;
    snd_pcm_uframes_t chunk_size;
    snd_pcm_uframes_t buffer_size;
    snd_pcm_format_t format;
    uint16_t channels;
    unsigned int chunk_bytes;
    unsigned int bits_per_sample;
    unsigned int bits_per_frame;

    uint8_t *data_buf;
} SNDPCMContainer_t;

#define  AUX_STREAM_BUF_SIZE   (1024*4)
#define  AUX_PER_FRAME_SIZE     1024



//音频数据缓存数组最大个数
#define AUDIO_STREAM_BUFF_NUMBER_MAX 	20      //数组个数申请最大值
#define AUDIO_STREAM_BUFF_SIZE_BASE 	1024    //缓存数组以1Kb为基数分配内存
#define AUDIO_STREAM_BUFF_SIZE_MAX 	 	8       //音频数据缓存数组最大9Kb

typedef struct stAudioStreamRecvBuff
{
    unsigned int  ApplyStreamBuffNumber;//申请的数组个数
    unsigned int  StreamBuffSize; 		//每个数据缓存的大小
    //unsigned int  StreamRecvTimes; 		//接收数据的次数
    //unsigned int  StreamRecvTimesMax; 	//一组数据接收计次最大值
    unsigned int  CurrentIndexReadPos; 	//当前数组读取位置
    unsigned int  CurrentIndexWritePos; //当前数组写入位置
    unsigned int  CurrentReadIndex; 	//当前读取的第几个数组
    unsigned int  CurrentWriteIndex;    //当前写入的第几个数组
    unsigned char *StreamBuff[AUDIO_STREAM_BUFF_NUMBER_MAX];    //数据缓存 该数据包的接收数据大小
}stAudioStreamRecvBuff;


class  CAUX
{
public:
    CAUX();
    ~CAUX();

    int        SNDWAV_P_GetFormat(WAVContainer_t *wav, snd_pcm_format_t *snd_format);

    ssize_t    SNDWAV_ReadPcm(SNDPCMContainer_t *sndpcm, size_t rcount);

    int        SNDWAV_SetParams(SNDPCMContainer_t *sndpcm, WAVContainer_t *wav);

    int        SNDWAV_PrepareWAVParams(WAVContainer_t *wav);

    int        WAV_P_CheckValid(WAVContainer_t *container);

    int        WAV_WriteHeader(int fd, WAVContainer_t *container);

public:
    SNDPCMContainer_t record;
};



class CAUXPlay
{
public:
    CAUXPlay();
    bool    StartAUXWorking();
    void    StopAUXWorking();

    void    SetWorking(bool bWorking)    { m_bWorking = bWorking; }
    bool    IsWorking()                  { return m_bWorking;     }
    void    SetPthExit(bool bPthExit)    { m_bPthExit = bPthExit; }
    bool    IsUpdate()                   { return m_bUpdate;      }
    void    SetUpdate(bool bUpdate)      { m_bUpdate = bUpdate;   }

private:
    void    StartCollectAudio();
    static void* AUXPthread(void* lparam);

    void    StopCollectAudio();
    static void* AUXExitPthread(void* lparam);

    void    Stop();
private:
    bool    InitPlay();
    bool    InitAlsa();

    bool    FreeAUX();
    void    ClearAudioBuf();

    bool    AddStreamData(BYTE* data, int len);
    bool    SendStreamData();
    bool    GetFrame(BYTE ** pData, int *len);
    void    SNDWAV_Record(BYTE ** pData, int *len);

public:
    CAUX    m_Aux;
    WAVContainer_t wav;
    SNDPCMContainer_t record;

private:
    bool    m_bWorking;          // 是否工作
    pthread_mutex_t     m_csAUX;
    bool    m_bPthExit;          // aux线程是否退出
    bool    m_bUpdate;           // 是否更新数据，重新启动
    pthread_t m_pth;

    //CLxTimer    m_timeStart;           // 上次发送时间，用于计算时间间隔
    ctime_t  m_ctime;
    double   m_lfDurationPerFrame;
    int      m_fd;

    // Socket相关信息 socket 保留，待修改
    CUDPSocket*		m_pUdpSocket;                // SOCKET
    USHORT          m_uPort;                     // 组播端口
    CHAR            m_szMulticastIP[16];         // 组播IP

    stAudioStreamRecvBuff m_Stream;
    BYTE            m_szSendBuf[STREAM_BUF_SIZE];   // 数据缓冲区
};

#endif

#endif // AUXPLAY_H
