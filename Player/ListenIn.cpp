#include "ListenIn.h"

CListenIn::CListenIn()
{
    for(int i=0; i<OUT_BUF_COUNT; ++i)
    {
        m_pBuffer[i] = new BYTE[OUT_BUF_SIZE];
        if(m_pBuffer[i] == NULL)
        {
            exit(0);
        }
    }

    m_isWorking = FALSE;
    m_bExit		= FALSE;

    m_SongInfo.isMP3		= TRUE;
    m_SongInfo.samp_freq	= 44100;
    m_SongInfo.channels		= 2;
    m_SongInfo.bit_samp		= 16;

    //初始化操作
    int ret = mpg123_init();//初始化库
    m_mh = mpg123_new(NULL, &ret);//创建对象
    mpg123_param(m_mh, MPG123_VERBOSE, 2, 0);//解码设置
    mpg123_open_feed(m_mh);//打开

    m_csListen = PTHREAD_MUTEX_INITIALIZER;
}

CListenIn::~CListenIn()
{
    m_bExit = TRUE;

    if(m_isWorking)
    {
        StopWorking();
    }

    ExitNetwork();

    for(int i=0; i<OUT_BUF_COUNT; ++i)
    {
        delete[] m_pBuffer[i];
        m_pBuffer[i] = NULL;
    }

    //一些清理工作，顺序不要颠倒
    mpg123_close(m_mh);
    mpg123_delete(m_mh);
    mpg123_exit();
}

/*
int CListenIn::GetSecCount()
{
    return m_vecListenSec.size();
}

string CListenIn::GetSec(int index)
{
    if(index >= m_vecListenSec.size())
    {
        return "";
    }

    return m_vecListenSec[index];
}

void CListenIn::AddSec(string strMac)
{
    pthread_mutex_lock(&m_csListen);
    bool isExist = FALSE;
    vector<string>::iterator iter = m_vecListenSec.begin();
    for(; iter != m_vecListenSec.end(); iter++)
    {
        if(*iter == strMac)
        {
            isExist = TRUE;
            break;
        }
    }

    if(!isExist)
    {
        m_vecListenSec.push_back(strMac);
    }

    pthread_mutex_unlock(&m_csListen);
}

void CListenIn::RemoveSec(string strMac)
{
    pthread_mutex_lock(&m_csListen);
    vector<string>::iterator iter = m_vecListenSec.begin();
    for(; iter != m_vecListenSec.end(); iter++)
    {
        if(*iter == strMac)
        {
            m_vecListenSec.erase(iter);
            break;
        }
    }
    pthread_mutex_unlock(&m_csListen);
}

void CListenIn::ClearSec()
{
    pthread_mutex_lock(&m_csListen);
    m_vecListenSec.clear();
    pthread_mutex_unlock(&m_csListen);
}
*/

#if 0
bool CListenIn::InitListenIn(song_info_t songInfo, CSection *pSection)
{
    m_SongInfo = songInfo;

    if(pSection == NULL)
    {
        return FALSE;
    }
    m_pListenSection = pSection;

    return TRUE;
}
#endif

#if 1
bool CListenIn::InitListenIn(song_info_t songInfo, unsigned int uPort)
{
    m_SongInfo = songInfo;

    /*
    m_pUdpSocket = m_udpSockets.CreateSocket(uPort, OnRecvData, this);

    CHAR	szMulticastIP[16] = {0};	// 组播IP
    sprintf(szMulticastIP, "234.5.6.%d", uPort-50000+1);	// 因为是本地监听，所以端口不能和发送的端口一致，所以要+1
    if (m_pUdpSocket != NULL && !m_pUdpSocket->JoinMulticast(szMulticastIP))
    {
        return FALSE;
    }

    m_udpSockets.StartRecvUdpData();
    */

    return TRUE;
}
#endif

void  CListenIn::ExitNetwork(VOID)
{
    //close(m_sockListen);

    /*
    if(m_pThreadWork > 0)
    {
        //::TerminateThread(m_hThreadWork, 0);
        //::WaitForSingleObject(m_hThreadWork, INFINITE);
        //CloseHandle(m_hThreadWork);
        //m_hThreadWork = NULL;
        pthread_cancel(m_pThreadWork);
        m_pThreadWork = -1;
    }
    */

}

bool CListenIn::IsWorking()
{
    return m_isWorking;
}

bool CListenIn::StartWorking()
{
    return TRUE;
}

bool CListenIn::StopWorking()
{
    if(!m_isWorking)
    {
        return TRUE;
    }

    m_isWorking = FALSE;
    return true;
}

void CListenIn::Playing(LPBYTE lpBuf, int nLen)
{

}

void CListenIn::OnRecvData(LPCSTR	pData,			// 收到的数据
                           unsigned short	nLen,	// 数据长度
                           VOID*	pUserDefined,	// 自定义数据
                           LPCSTR	szIP,			// IP地址
                           unsigned short	nPort)  // 端口
{
    CListenIn *pListenIn = (CListenIn *)pUserDefined;

    pListenIn->Playing((LPBYTE)pData, nLen);
}

























