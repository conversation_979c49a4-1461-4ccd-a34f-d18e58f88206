#include "stdafx.h"
#include "PlayQueue.h"


CPlayTask::CPlayTask(unsigned char source,
                     vector<CMyString> strPathNames,
                     unsigned int* pSecIndexs,
                     unsigned int uSecCount,
                     string strUserAccount,
                     int nPlayMode,
                     int nListIndex,
                     int nSongIndex,
                     unsigned char nVolume,
                     int play_type,
                     int play_count,
                     PlayFrom  playFrom)
{
    m_source = source;
    m_vecStrPathNames = strPathNames;
    //m_vecStrPathName.assign(strPathName.begin(), strPathName.end());
    memcpy(m_pSecIndexs, pSecIndexs, sizeof(unsigned int)*uSecCount);
    m_uSecCount = uSecCount;

    m_nListIndex = nListIndex;
    m_nSongIndex = nSongIndex;
    m_uVolume = nVolume;        // zhuyg

    m_PlayFrom = playFrom;

    m_nPlayID = -1;
    m_nTimerPointID = 0;

    m_strUserAccount = strUserAccount;
    m_nPlayMode = nPlayMode;

    m_nPlayType = play_type;
    m_nPlayCount = play_count;
}


bool CPlayTask::operator==(CPlayTask &playTask)
{
    unsigned char	source		= playTask.GetSource();
    unsigned int*	pSecIndexs	= playTask.GetSecIndexs();
    unsigned int	uSecCount	= playTask.GetSecCount();

    if (uSecCount != m_uSecCount)
    {
        return FALSE;
    }

    // 选中分区要一致
    for (unsigned int i=0; i<uSecCount; ++i)
    {
        if (pSecIndexs[i] != m_pSecIndexs[i])
        {
            return FALSE;
        }
    }

    // 如果音源相同，或者其中一个音源为停止
    if (source == m_source || source == SOURCE_STOP || m_source == SOURCE_STOP)
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}



CPlayQueue::CPlayQueue(void)
{
    // 初始化
    m_csPlayTasks = PTHREAD_MUTEX_INITIALIZER;
    m_csPlayUnits = PTHREAD_MUTEX_INITIALIZER;

    // 音源优先级定义
    m_SoucePriority[PRO_IDLE]				= 00;
    m_SoucePriority[PRO_ANALOG_INPUT]		= 10;
    m_SoucePriority[PRO_100V]				= 11;
    m_SoucePriority[PRO_LOCAL_PLAY]			= 15;
    for (BYTE audioSrc=PRO_AUDIO_COLLECTOR_MIN; audioSrc<=PRO_AUDIO_COLLECTOR_MAX; ++audioSrc)
    {
        m_SoucePriority[audioSrc]			= 20;
    }
    m_SoucePriority[PRO_API_NET_RADIO]		= 21;
    m_SoucePriority[PRO_TIMING]				= 30;
    m_SoucePriority[PRO_API_TTS_MUSIC]		= 31;
    m_SoucePriority[PRO_PHONE_GATEWAY]		= 32;
    m_SoucePriority[PRO_AUDIO_MIXED]		= 40;
    m_SoucePriority[PRO_SIP_CALLING]		= 49;
    m_SoucePriority[PRO_CALL]				= 50;
    m_SoucePriority[PRO_EVENT]              = 60;
    m_SoucePriority[PRO_ALARM]				= 80;
    m_SoucePriority[PRO_PAGING]				= 90;

    m_SoucePriority[PRO_OFFLINE]			= 255;
 
}


CPlayQueue::~CPlayQueue(void)
{
    //20220427 静态初始化的互斥锁不需要,也不能用pthread_mutex_destroy销毁锁，否则将出错
    //pthread_mutex_destroy(&m_csPlayTasks);
    //pthread_mutex_destroy(&m_csPlayUnits);
}


void CPlayQueue::Clear(void)
{
    pthread_mutex_lock(&m_csPlayUnits);

    // 建立一个空队列，赋值给它
    queue<int> nullSongUnitQueue;
    m_PlayUnitQueue.swap(nullSongUnitQueue);

    pthread_mutex_unlock(&m_csPlayUnits);

    pthread_mutex_lock(&m_csPlayTasks);
    // 建立一个空队列，赋值给它
    queue<CPlayTask> nullPlayTaskQueue;
    m_PlayTaskQueue.swap(nullPlayTaskQueue);

    pthread_mutex_unlock(&m_csPlayTasks);
}


// 添加播放单元到队尾
void	CPlayQueue::PushPlayUnit(int pSongId)
{
    pthread_mutex_lock(&m_csPlayUnits);

    m_PlayUnitQueue.push(pSongId);

    pthread_mutex_unlock(&m_csPlayUnits);
}


// 将播放单元从队头删除
void	CPlayQueue::PopPlayUnit()
{
    if (m_PlayUnitQueue.size() > 0)
    {
        m_PlayUnitQueue.pop();
    }
}


// 检测播放任务(如果是定时任务，返回true)
bool CPlayQueue::CheckPlayTasks(ctime_t tNow)
{
    static ctime_t tLastTime = 0;
    bool isTimerTask=false;  //是否定时任务
    CPlayTask* frontPlayTask = NULL;  // 声明一个指针变量，用于存储队列的front元素
    pthread_mutex_lock(&m_csPlayTasks);
    if(m_PlayTaskQueue.size() > 0)
    {
        frontPlayTask = &m_PlayTaskQueue.front();
    }
    pthread_mutex_unlock(&m_csPlayTasks);
    if(frontPlayTask)
    {
        // ctime_t 单位：毫秒
        //if (tNow - tLastTime >= DURATION_NEXT_PLAY_TASK)
        {
            unique_lock<shared_timed_mutex> ulk_songPlayer(g_Global.m_PlayQueue.m_SongPlayer.m_csSongPlayer);

            tLastTime = tNow;

            unsigned char nTaskSource = frontPlayTask->GetSource();

            // 加入到日志文件
            #if 0
            CMyString strTip;
            strTip.Format(("Check PlayTask1:ID=%d,Source=%d,TotalSize=%d"),frontPlayTask->GetPlayID(),frontPlayTask->GetSource(),m_PlayTaskQueue.size());
            g_Global.m_Network.AddLog(strTip);
            #endif

            // 停止任务
            if (nTaskSource == SOURCE_STOP)
            {
                StopPlayTask(*frontPlayTask);
            }
            // 播放结束下一个任务
            else if (nTaskSource == SOURCE_NEXT)
            {
                NextPlayTask(*frontPlayTask);
            }
            // 手动上一曲/下一曲
            else if (nTaskSource == SOURCE_PRE_MANUAL || nTaskSource == SOURCE_NXT_MANUAL)
            {
                if(nTaskSource == SOURCE_PRE_MANUAL)
                    PreNextPlayTask(*frontPlayTask,SAC_PRE);
                else
                    PreNextPlayTask(*frontPlayTask,SAC_NEXT);
            }
            // 开始播放定时任务
            else if (nTaskSource == SOURCE_TIMER)
            {
                StartTimerPlayTask(*frontPlayTask);
                isTimerTask=true;
            }
            // 停止播放定时任务
            else if (nTaskSource == SOURCE_TIMER_STOP)
            {
                //StopTimerPlayTask(frontPlayTask);
            }
            #if SUPPORT_REMOTE_CONTROLER
            //遥控器任务
            else if(nTaskSource == SOURCE_REMOTE_PLAY)
            {
                StartRemoteControlPlayTask(*frontPlayTask);
            }
            #endif
            // 播放任务
            else if (nTaskSource != SOURCE_NULL)
            {
                StartPlayTask(*frontPlayTask);
            }
            #if 0
            // 加入到日志文件
            strTip.Format(("Check PlayTask2:ID=%d,Source=%d,TotalSize=%d"),frontPlayTask->GetPlayID(),frontPlayTask->GetSource(),m_PlayTaskQueue.size());
            g_Global.m_Network.AddLog(strTip);
            #endif
            ulk_songPlayer.unlock();
            
            PopPlayTask();
        }
    }
    else
    {
        tLastTime = 0;          // 如果没有任务，就置为0
    }

    return isTimerTask;
}


// 检测队列
void	CPlayQueue::CheckPlayUnits(ctime_t tNow)
{
    pthread_mutex_lock(&m_csPlayUnits);
    int count=30;
    static int testttt=0;
    while (m_PlayUnitQueue.size() > 0 && --count)
    {
        int pSongUnitId = m_PlayUnitQueue.front();

        shared_lock<shared_timed_mutex> slk_songPlayer(g_Global.m_PlayQueue.m_SongPlayer.m_csSongPlayer);

        CSongUnit* pFontSongUnit = g_Global.m_PlayQueue.m_SongPlayer.GetSongUnitByPlayID(pSongUnitId);
        if(!pFontSongUnit)
        {
            PopPlayUnit(); // 删除最前面的
            continue;
        }
        //printf("tNow=%lu,sotpTime=%lu\n",tNow,pFontSongUnit->GetStopPlayTime());
        // 加入队列的时间是从早到晚，所以最早的一直在前面
        ctime_t tDuration = tNow - pFontSongUnit->GetStopPlayTime();
        testttt++;
        if(testttt>20)
        {
            testttt=0;
            PopPlayUnit(); // 删除最前面的
            continue;
        }
        if (tDuration >= DURATION_STOP_TO_PLAY || pFontSongUnit->GetStopPlayTime()<0)
        {
            if(pFontSongUnit->GetPlayStatus() == SPS_NEXT)
            {
                // 加入到日志文件
                CMyString strTip;
                strTip.Format(("SongUnit Id:%d,Start Play Source %d : %s"), pFontSongUnit->GetPlayID(), pFontSongUnit->GetSource(), pFontSongUnit->GetPathName().C_Str());
                g_Global.m_Network.AddLog(strTip);

                if(pFontSongUnit->GetPlayStatus() != SPS_PAUSE_MANUAL && pFontSongUnit->GetPlayStatus() != SPS_PAUSE_AUTO)
                {
                    pFontSongUnit->StartCounter();
                    pFontSongUnit->SetPlayStatus(SPS_PLAY);
                }
            }
            else
            {
                // 加入到日志文件
                CMyString strTip;
                strTip.Format(("m_PlayUnitQueue playStatus error:%d"), pFontSongUnit->GetPlayStatus());
                g_Global.m_Network.AddLog(strTip);
            }
            
            PopPlayUnit(); // 删除最前面的
            testttt=0;
        }
        // 后面的时间一定比前面的小，所以直接退出循环
        else
        {
            break;
        }
    }
    pthread_mutex_unlock(&m_csPlayUnits);
}


// 分区播放音源
BOOL	CPlayQueue::StartPlayTask(CPlayTask& playTask)
{
    unsigned char	src			= playTask.GetSource();
    CMyString   strPathName     = playTask.GetPathName();
    unsigned int*	pSecIndexs	= playTask.GetSecIndexs();
    unsigned int	uSecCount	= playTask.GetSecCount();

    if (uSecCount > 0 && g_Global.m_SongTool.IsPathExist(strPathName) && g_Global.m_SongTool.IsStreamFormat(strPathName))
    {
        bool m_bmulticastNewCommand=true;
        for (unsigned int i=0; i<uSecCount; ++i)
        {
            CSection& section = g_Global.m_Sections.GetSection(pSecIndexs[i]);
            if(!section.IsDeviceSupportExtraFeature())
            {
                m_bmulticastNewCommand=false;
                break;
            }
        }
        // 添加到播放队列
        CSongUnit *pSongUnit = m_SongPlayer.AddSongUnit(m_bmulticastNewCommand,src,strPathName,playTask.GetPlayMode());

        if (pSongUnit == NULL)
        {
            return FALSE;
        }

        pSongUnit->SetSource(src);
        pSongUnit->SetVolume(playTask.GetVolume());     // zhuyg
        pSongUnit->SetPlayFrom(playTask.GetPlayFrom());

        pSongUnit->SetUserAccount(playTask.GetUserAccount());
        
        //********新增
        pSongUnit->SetVecPathNames(playTask.GetVecPathNames());
        pSongUnit->SetPlayType(playTask.GetPlayType());
        pSongUnit->SetPlayCount(playTask.GetPlayCount());
        
        //printf("StartPlayTask:GetVecPathNames.size=%d,playType=%d,playCount=%d\n",playTask.GetVecPathNames().size(),playTask.GetPlayType(),playTask.GetPlayCount());

        unsigned int nPlayCount = 0;

        for (unsigned int i=0; i<uSecCount; ++i)
        {
            CSection& section = g_Global.m_Sections.GetSection(pSecIndexs[i]);
            bool bPlay = FALSE;
            ProgramSource secSrc = section.GetProSource();

            if ( ((src == SOURCE_PLAY || src == SOURCE_SPECIFIED_PLAY)  && (m_SoucePriority[PRO_LOCAL_PLAY] >= m_SoucePriority[secSrc]))
                    || (src == SOURCE_TIMER && (m_SoucePriority[PRO_TIMING] >= m_SoucePriority[secSrc]))
                    || ((src == SOURCE_ALARM || src == SOURCE_SPECIFIED_ALARM) && (m_SoucePriority[PRO_ALARM] >= m_SoucePriority[secSrc]))
                    || (src == SOURCE_EVENT && (m_SoucePriority[PRO_EVENT] >= m_SoucePriority[secSrc])))     // zhuyg
            {
                // 符合播放条件
                bPlay = TRUE;
            }

            if (bPlay)
            {
                nPlayCount++;

                int playID = section.GetPlayID();
#if 1
                // 如果分区正在被监听，更新监听分区
                if(section.IsListening())
                {
                    m_SongPlayer.UpdateListenInSong(&section,pSongUnit);
                }
#endif


                //section.SetPlayID(pSongUnit->GetPlayID());
                if( playID != pSongUnit->GetPlayID())
                {
                    //如果是指定播放消防告警音源，那么需要保存playId以便恢复播放
                    if(src == SOURCE_SPECIFIED_ALARM)
                    {
                        if (secSrc == PRO_LOCAL_PLAY || secSrc == PRO_TIMING)
                        {
                            section.SetPrePlayID(playID);

                            // 如果是只有一个分区在播放该歌曲，则暂停播放歌曲
                            // 如果多个分区在播放该歌曲，则不需要暂停，因为其它分区还需要继续播放
                            if (g_Global.m_Sections.IsOnePlaySectionPlaying(section.GetPlayID()))
                            {
                                //获取当前播放ID的播放状态
                                CSongUnit *pSongUnit = g_Global.m_PlayQueue.m_SongPlayer.GetSongUnitByPlayID(section.GetPlayID());
                                if (pSongUnit != NULL)
                                {
                                    if( pSongUnit->GetPlayStatus() != SPS_PAUSE_MANUAL )
                                    {
                                        m_SongPlayer.SetPlayStatus(section.GetPlayID(), SPS_PAUSE_AUTO);
                                    }
                                }
                            }
                        }
                    }

                    // 分区播放ID重置
                    section.SetPlayID(-1);
                    // 如果没有其它分区播放此歌曲
                    if (playID > 0
                            && !g_Global.m_Sections.HasPlaySections(playID)
                            && !g_Global.m_Sections.HasPrePlaySections(playID))
                    {
                        printf("playID=%d,playTask.GetPlayID()=%d\n",playID,pSongUnit->GetPlayID());
                        // 停止播放该歌曲
                        m_SongPlayer.SetPlayStatus(playID, SPS_STOP);
                    }
                }
                //如果是指定歌曲播放，则设置分区音量为任务音量
                if(src == SOURCE_SPECIFIED_PLAY)
                {
                    g_Global.m_Network.m_CmdSend.CmdSetVolume(pSongUnit->GetVolume(), section);
                }
                // 发送命令
                SendPlayCommand(src, pSongUnit, &section);
            }
        }

        // 如果没有播放的分区
        if (nPlayCount == 0)
        {
            // 则把刚才的播放任务移除掉
             printf("TTTTTTTT3\n");
            m_SongPlayer.SetPlayStatus(pSongUnit->GetPlayID(), SPS_STOP);
        }
        else
        {
            if (src == SOURCE_PLAY)
            {
                //UpdatePlayInfo(src, pSongUnit, g_Global.m_PlayList.GetSelList(), g_Global.m_PlayList.GetSelSong());
                UpdatePlayInfo(src, pSongUnit, playTask.GetListIndex(), playTask.GetSongIndex());
            }
            else if (src == SOURCE_BELL || src == SOURCE_ALARM || src == SOURCE_EVENT)
            {
                UpdatePlayInfo(src, pSongUnit, -1, -1);
            }
            else if(src == SOURCE_SPECIFIED_PLAY || src == SOURCE_SPECIFIED_ALARM)
            {
                UpdatePlayInfo(src, pSongUnit, -1, 0);      //从第一首歌开始播放
            }
        }
    }

    return TRUE;

}


// 分区停止播放音源
void	CPlayQueue::StopPlayTask(CPlayTask& playTask)
{
    unsigned int*	pSecIndexs	= playTask.GetSecIndexs();
    unsigned int	uSecCount	= playTask.GetSecCount();

    // 设置分区空闲状态
    for (unsigned int i=0; i<uSecCount; ++i)
    {
        StopSectionPlaySource(&g_Global.m_Sections.GetSection(pSecIndexs[i]));
    }
}

// 下一个音源
void	CPlayQueue::NextPlayTask(CPlayTask& playTask)
{
    PlaySourceEnd(playTask.GetPlayID());
}

// 音源上下曲（手动)
void	CPlayQueue::PreNextPlayTask(CPlayTask& playTask,int action)
{
    PlaySourceMuanualPreNext(playTask.GetPlayID(),action);
}

// 开始定时点音源
void	CPlayQueue::StartTimerPlayTask(CPlayTask& playTask)
{
    if (playTask.GetTimerPointID() <=0 )
    {
        g_Global.m_Network.AddLog("Timer Point ID <= 0");
        return;
    }

    //CTimePoint& timePoint = *playTask.GetTimerPoint();
    CTimePoint& timePoint = g_Global.m_TimerScheme.GetTimer(g_Global.m_TimerScheme.GetCurScheme(), playTask.GetTimerPointID()-1);

    LPCSection lpAudioCollection=NULL;

    // 如果定时点的设备类型是电源时序器，那么不处理
    if(timePoint.GetDeviceType() == TIMER_DEVICE_TYPE_SEQUENCE_POWER)
    {
        printf("StartTimerPlayTask:device type=sequence power!\n");
        return;
    }

    if(timePoint.GetSourceType() == TIMER_SOURCE_TYPE_LOCAL_SONG && timePoint.GetPlayID() > 0)    //如果定时点已经处于播放状态，退出，避免二次进入
    {
        g_Global.m_Network.AddLog("timePoint.GetPlayID()>0,return!");
        return;
    }
    else if(timePoint.GetSourceType() == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
    {
        //判断音频采集器是否存在
        lpAudioCollection = g_Global.m_AudioCollectors.GetSectionByMac(timePoint.GetAudioCollector().mac);
        if(lpAudioCollection == NULL || !lpAudioCollection->IsOnline())
        {
            g_Global.m_Network.AddLog("AudioCollector is null or not online,return!");
            return;
        }
    }

    CMyString strLog;
    strLog.Format("StartTimerPlayTask : time point id = %d, name = %s", timePoint.GetID(), timePoint.GetName().C_Str());
    g_Global.m_Network.AddLog(strLog);

    if(timePoint.GetSourceType() == TIMER_SOURCE_TYPE_LOCAL_SONG)
    {
        int		nStartSong		= timePoint.GetStartSong(); // 获取刚开始的歌曲
        bool	toPlaySong		= FALSE; // 是否播放歌曲的标志
        CMyString strSongPathName;

        NEXT_SONG1:
        int playCount = timePoint.GetSongCount();
        bool songFound =false;
        playCount=playCount>10?10:playCount;
        while (playCount-- && nStartSong >= 0)
        {
            CSong& songToPlay = timePoint.GetSong(nStartSong);
            strSongPathName	= g_Global.m_strHttpRootDir + songToPlay.GetPathName();

            if (songToPlay.IsExist() && songToPlay.IsStreamFormat())
            {
                songFound=true;
                break;
            }
            else
            {
                nStartSong = timePoint.GetNextSong(nStartSong, TRUE);
            }
        }
        if(!songFound)
        {
            nStartSong=-1;
        }

        if (nStartSong >= 0)
        {
            unsigned int nPlayCount = 0;        //可以进行播放的分区数量（同时满足在线+音源优先级）
            bool bNeedPause = true;            //需要暂停播放
            unsigned int nExistSecCount = 0;    //实际存在的分区数量（不需要在线）

            bool m_bmulticastNewCommand=true;
            vector<CMyString> vecMac;
            int nSelSecCount = timePoint.GetSelectedSections(vecMac);
            for(int i=0; i<nSelSecCount; ++i)
            {
                // 查找分区
                CSection* pSection = g_Global.m_Sections.GetSectionByMac(vecMac[i]);
                if(!pSection->IsDeviceSupportExtraFeature())
                {
                    m_bmulticastNewCommand=false;
                    break;
                }
            }
            CSongUnit *pSongUnit = m_SongPlayer.AddSongUnit(m_bmulticastNewCommand,SOURCE_TIMER,strSongPathName,timePoint.GetPlayMode());

            if (pSongUnit == NULL)
            {
                CMyString strLog;
                CMyString strError = LANG_STR(LANG_SECTION_PLAY_LIST, "Error Parsing File", ("解析文件出现错误"));
                strLog.Format(("StartTimerPlayTask %s : %s"), strError.C_Str(), strSongPathName.C_Str());
                g_Global.m_Network.AddLog(strLog);

                LOG_INFO(strLog.C_Str());

                // 解析文件错误，继续跳到下一首
                goto NEXT_SONG1;
            }

            pSongUnit->SetSource(SOURCE_TIMER);
            if(timePoint.GetFollowDevice())
            {
                pSongUnit->SetVolume(-1);
            }
            else
            {
                pSongUnit->SetVolume(timePoint.GetVolume());
            }
            //vector<CMyString> vecMac;
            //int nSelSecCount = timePoint.GetSelectedSections(vecMac);


            bool source_paging_need_resume_timing=false;

            for(int i=0; i<nSelSecCount; ++i)
            {
                // 查找分区
                CSection* pSection = g_Global.m_Sections.GetSectionByMac(vecMac[i]);


                if (pSection == NULL)
                {
                    CMyString strLog;
                    strLog.Format("vecMac[%d] = %s,not found!", i, vecMac[i].C_Str());
                    g_Global.m_Network.AddLog(strLog);
        #if 0
                    for (int i=0; i<g_Global.m_Sections.GetSecCount(); ++i)
                    {
                        CSection& section = g_Global.m_Sections.GetSection(i);
                        CMyString strLog;
                        strLog.Format("section[%d].mac = %s", i, section.GetMac());
                        g_Global.m_Network.AddLog(strLog);

                    }
        #endif
                }
                else
                {
                    nExistSecCount++;
                }
                if (pSection != NULL && pSection->IsOnline())
                {
                    bNeedPause = false;     //只要定时点有一个分区在线，就开始播放定时，否则暂停播放
                    if (pSection->IsTimerInvalid())
                    {
                        CMyString strLog;
                        strLog.Format("zone %s timepoint is invalid", pSection->GetName());
                        LOG(strLog.C_Str(), LV_INFO);
                        g_Global.m_Network.AddLog(strLog);
                    }
                    else
                    {
                        ProgramSource secSrc = pSection->GetProSource();

                        //满足优先级，或者是采集器音源（如果是新版本，设置了高优先级模式，那么此处可以进入，以便解除信号触发后恢复定时音源）
                        if (m_SoucePriority[PRO_TIMING] >= m_SoucePriority[secSrc] || CProtocol::IsAudioCollectorSrc(secSrc))
                        {
                            nPlayCount++;
                            int playID = pSection->GetPlayID();

                            // 定时结束要恢复播放，所以要记下playID
                            if (secSrc == PRO_LOCAL_PLAY && g_Global.m_TimerScheme.GetTimePointsResumePlaying())
                            {
                                pSection->SetPrePlayID(playID);

                                // 如果是只有一个分区在播放该歌曲，则暂停播放歌曲
                                // 如果多个分区在播放该歌曲，则不需要暂停，因为其它分区还需要继续播放
                                if (g_Global.m_Sections.IsOnePlaySectionPlaying(pSection->GetPlayID()))
                                {
                                    //获取当前播放ID的播放状态
                                    CSongUnit *pSongUnit = g_Global.m_PlayQueue.m_SongPlayer.GetSongUnitByPlayID(pSection->GetPlayID());
                                    if (pSongUnit != NULL)
                                    {
                                        if( pSongUnit->GetPlayStatus() != SPS_PAUSE_MANUAL )
                                        {
                                            m_SongPlayer.SetPlayStatus(pSection->GetPlayID(), SPS_PAUSE_AUTO);
                                        }
                                    }
                                }
                            }

                            pSection->SetPlayID(-1);

                            // 如果没有其它分区播放此歌曲
                            if (playID > 0
                                    && !g_Global.m_Sections.HasPlaySections(playID)
                                    && !g_Global.m_Sections.HasPrePlaySections(playID))
                            {
                                // 停止播放该歌曲
                                m_SongPlayer.SetPlayStatus(playID, SPS_STOP);
                            }
        #if 0   //jms
                            // 执行定时点之前先设置分区器状态
                            int index = timePoint.GetSectionIndexByMac(CMyString(pSection->GetMac()));
                            if (index >= 0 && pSection->GetDeviceModel() == MODEL_DECODE_PLAYER_WITH_SPLITTER
                                    && timePoint.GetSplitterStatus(index) >= 0)
                            {
                                g_Global.m_Network.m_CmdSend.CmdSplitterStatus(*pSection, timePoint.GetSplitterStatus(index));
                            }
        #endif
                            // 发送执行定时的命令
                            SendPlayCommand(SOURCE_TIMER, pSongUnit, pSection);
                        }
                        //20220727 如果当前定时开始前有分区处于寻呼音源,那么需要记录，以便寻呼结束后进入定时音源
                        else if( secSrc == PRO_PAGING )
                        {
                            source_paging_need_resume_timing=true;
                            //先把当前播放ID设置为-1，否则如果寻呼开始前处于网络点播音源，寻呼结束后会优先恢复网络点播，而不是定时
                            int playID = pSection->GetPlayID();
                            pSection->SetPlayID(-1);
                            // 如果没有其它分区播放此歌曲
                            if (playID > 0
                                    && !g_Global.m_Sections.HasPlaySections(playID)
                                    && !g_Global.m_Sections.HasPrePlaySections(playID))
                            {
                                // 停止播放该歌曲
                                m_SongPlayer.SetPlayStatus(playID, SPS_STOP);
                            }
                            //再把之前播放ID设置为当前定时点的播放id，以便寻呼结束后恢复
                            pSection->SetPrePlayID(pSongUnit->GetPlayID());
                        }
                    }
                }
                //分区不在线，要将定时点的播放ID先设置成最近播放的播放ID（注意不能是SetPlayID和SetPrePlayID）,以便上线后恢复。
                else if(pSection != NULL)
                {
                    pSection->m_PlayedRecently.m_nRecentSrc = PRO_TIMING;
                    pSection->m_PlayedRecently.m_nPlayID = pSongUnit->GetPlayID();
                    //此处重置一下掉线时间，否则会影响下列情况下的时长判断：服务器重启后，分区一直不在线，但定时点启动了
                    pSection->m_PlayedRecently.m_tOffline = CTime::GetCurrentTimeT().GetTime();
                }
            }

            // 如果没有播放的分区,且没有需要从寻呼音源恢复到定时的分区
            //if (nPlayCount == 0 && !source_paging_need_resume_timing)
            //new
            // 如果不存在分区，则把刚才的播放任务移除掉
            // 否则不移除,因为如果定时点开始后，所有分区都处于离线状态时，此时也应该等待定时点结束时间时间到后才允许结束
            if(nExistSecCount == 0)
            {
                m_SongPlayer.SetPlayStatus(pSongUnit->GetPlayID(), SPS_STOP);
            }
            // 有需要播放的歌曲
            else
            {
                toPlaySong = TRUE;
                UpdatePlayInfo(SOURCE_TIMER, pSongUnit, -1, nStartSong);
                timePoint.SetPlayID(pSongUnit->GetPlayID());
#if 1
                //如果没有一个分区在线，那么先暂停播放
                if(bNeedPause)
                {
                    m_SongPlayer.SetPlayStatus(pSongUnit->GetPlayID(), SPS_PAUSE_AUTO);
                }
#endif
            }
        }

        #if 0
        // 如果没有歌曲播放，也要进入定时状态
        if (!toPlaySong)
        {
            vector<CMyString> vecMac;
            int nSelSecCount = timePoint.GetSelectedSections(vecMac);
            for(int i=0; i<nSelSecCount; ++i)
            {
                // 查找分区
                CSection* pSection = g_Global.m_Sections.GetSectionByMac(vecMac[i]);

                if (pSection != NULL && pSection->IsOnline() && !pSection->IsTimerInvalid())
                {
                    ProgramSource secSrc = pSection->GetProSource();

                    if (m_SoucePriority[PRO_TIMING] >= m_SoucePriority[secSrc])
                    {
                        CSongUnit songUnit(-1);
                        songUnit.SetSource(SOURCE_NULL);
                        songUnit.SetVolume(timePoint.GetVolume());
                        SendPlayCommand(SOURCE_TIMER, &songUnit, pSection);
                    }
                }
            }
        }
        #endif

    }
    else if(timePoint.GetSourceType() == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
    {
        vector<CMyString> vecMac;
        int nSelSecCount = timePoint.GetSelectedSections(vecMac);

        //先发送给采集器
        g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(*lpAudioCollection, lpAudioCollection->m_pAudioCollector,timePoint.GetAudioCollector().channelId);

        for(int i=0; i<nSelSecCount; ++i)
        {
            // 查找分区
            CSection* pSection = g_Global.m_Sections.GetSectionByMac(vecMac[i]);


            if (pSection == NULL)
            {
                CMyString strLog;
                strLog.Format("vecMac[%d] = %s,not found!", i, vecMac[i].C_Str());
                g_Global.m_Network.AddLog(strLog);
            }

            if (pSection != NULL && pSection->IsOnline())
            {
                if (pSection->IsTimerInvalid())
                {
                    CMyString strLog;
                    strLog.Format("zone %s timepoint is invalid", pSection->GetName());
                    LOG(strLog.C_Str(), LV_INFO);
                    g_Global.m_Network.AddLog(strLog);
                }
                else
                {
                    ProgramSource secSrc = pSection->GetProSource();

                    if (m_SoucePriority[PRO_TIMING] >= m_SoucePriority[secSrc])
                    {
                        unsigned char volume = timePoint.GetFollowDevice()?0xff:timePoint.GetVolume();
                        g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(*pSection,lpAudioCollection->m_pAudioCollector, timePoint.GetAudioCollector().channelId,0,true,volume);
                    }
                }
            }
        }
    }

}

// 结束定时点音源
//void	CPlayQueue::StopTimerPlayTask(CPlayTask& playTask)
void	CPlayQueue::StopTimerPoint(CTimePoint& timePoint,bool needLock)
{
    // 如果定时点的设备类型是电源时序器，那么不处理
    if(timePoint.GetDeviceType() == TIMER_DEVICE_TYPE_SEQUENCE_POWER)
    {
        printf("StopTimerPoint:device type=sequence power!\n");
        return;
    }

    /*
    if (playTask.GetTimerPointID() <=0 )
    {
        g_Global.m_Network.AddLog("Timer Point ID <= 0");
        return;
    }


    CTimePoint& timePoint = g_Global.m_TimerScheme.GetTimer(g_Global.m_TimerScheme.GetCurScheme(), playTask.GetTimerPointID()-1);
    */
   if(timePoint.GetSourceType() == TIMER_SOURCE_TYPE_LOCAL_SONG)
   {
        if(timePoint.GetPlayID() <=0 )
        {
            return;
        }
        // 停止歌曲播放
        m_SongPlayer.SetPlayStatus(timePoint.GetPlayID(), SPS_STOP,needLock);

        vector<CMyString> vecMac;
        int nSelSecCount = timePoint.GetSelectedSections(vecMac);
        for(int i=0; i<nSelSecCount; ++i)
        {
            // 查找分区
            CSection* pSection = g_Global.m_Sections.GetSectionByMac(vecMac[i]);

            if (pSection != NULL && pSection->IsOnline() && !pSection->IsTimerInvalid())
            {
                ProgramSource secSrc = pSection->GetProSource();

                if (m_SoucePriority[PRO_TIMING] >= m_SoucePriority[secSrc])	// 优先级低于定时的，都置为空
                {
                    if(pSection->GetPlayID() == timePoint.GetPlayID())      //PLAYID相等才停止，因为有可能中途手动停止了定时，然后又去手动点播其他歌曲了。
                    {
                        // 分区置为空闲状态
                        g_Global.m_Network.m_CmdSend.CmdSetIdleStatus(*pSection);
                    }
                }
                #if 0
                else if (secSrc == PRO_PAGING || secSrc == PRO_100V)	// 如果是寻呼或100V音源，也要清除PlayID
                {
                    pSection->SetPlayID(-1);
                }
                #endif
                if(timePoint.GetPlayID() == pSection->GetPlayID())
                {
                    pSection->SetPlayID(-1);
                }
            }
            else if(pSection != NULL && !pSection->IsOnline())
            {
                //如果原来处于定时音源的分区离线，这个时候也需要清除该离线分区的最近播放id，否则分区上线后可能会定时到其他播放Id为原来的歌曲
                if(timePoint.GetPlayID() == pSection->m_PlayedRecently.m_nPlayID)
                {
                    pSection->m_PlayedRecently.m_nPlayID = -1;
                    pSection->m_PlayedRecently.m_nRecentSrc = PRO_OFFLINE;
                }
            }
        }

        // 重置定时点播放信息
        timePoint.SetPlayID(-1);

        CMyString strLog;
        strLog.Format("%s Reset Play ID - Stop Timer PlayTask", timePoint.GetName().C_Str());
        g_Global.m_Network.AddLog(strLog);
   }
   else if(timePoint.GetSourceType() == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
   {
        LPCSection lpAudioCollection=g_Global.m_AudioCollectors.GetSectionByMac(timePoint.GetAudioCollector().mac);
        if(lpAudioCollection != NULL )
        {
            vector<CMyString> vecMac;
            int nSelSecCount = timePoint.GetSelectedSections(vecMac);
            for(int i=0; i<nSelSecCount; ++i)
            {
                // 查找分区
                CSection* pSection = g_Global.m_Sections.GetSectionByMac(vecMac[i]);

                // 定时点有效才能停止
                if (pSection != NULL && pSection->IsOnline() && !pSection->IsTimerInvalid())
                {
                    ProgramSource secSrc = pSection->GetProSource();

                    if(lpAudioCollection->m_pAudioCollector->GetSourceID() + timePoint.GetAudioCollector().channelId -1 == pSection->GetProSource())
                    {
                        // 分区置为空闲状态
                        g_Global.m_Network.m_CmdSend.CmdSetIdleStatus(*pSection);

                        pSection->m_PlayedRecently.m_nRecentSrc = PRO_IDLE;
                        pSection->m_PlayedRecently.m_bSongTimerEndResumeAc = PRO_IDLE;
                    }
                }
            }
        }
   }
}


#if SUPPORT_REMOTE_CONTROLER
// 开始遥控器播放音源
void	CPlayQueue::StartRemoteControlPlayTask(CPlayTask& playTask)
{
    stRemoteTaskInfo& remoteTaskInfo = playTask.GetRemoteControlTask();
    //查找遥控器
    CSection* pRemoteControler = g_Global.m_RemoteControlers.GetSectionByMac(remoteTaskInfo.strMac);
    if(pRemoteControler == NULL)
    {
        return;
    }
    if (remoteTaskInfo.nTaskID <=0 || remoteTaskInfo.nTaskID > pRemoteControler->m_pRemoteControler->GetTaskCnt())
    {
        return;
    }

    CRemoteControlTask &remoteTask=pRemoteControler->m_pRemoteControler->GetTaskDetail(remoteTaskInfo.nTaskID);

    if(remoteTask.GetSourceType() == TIMER_SOURCE_TYPE_LOCAL_SONG)
    {
        int		nStartSong		= remoteTask.GetStartSong(); // 获取刚开始的歌曲
        bool	toPlaySong		= FALSE; // 是否播放歌曲的标志
        CMyString strSongPathName;

        NEXT_SONG1:
        int playCount = remoteTask.GetSongCount();
        bool songFound =false;
        playCount=playCount>10?10:playCount;
        while (playCount-- && nStartSong >= 0)
        {
            CSong& songToPlay = remoteTask.GetSong(nStartSong);
            strSongPathName	= g_Global.m_strHttpRootDir + songToPlay.GetPathName();

            if (songToPlay.IsExist() && songToPlay.IsStreamFormat())
            {
                songFound=true;
                break;
            }
            else
            {
                nStartSong = remoteTask.GetNextSong(nStartSong,remoteTask.GetPlayMode());
            }
        }
        if(!songFound)
        {
            nStartSong=-1;
        }

        vector<CMyString> vecMac;
        int uSecCount=remoteTask.GetSelectedSections(vecMac);

        unsigned char src = SOURCE_REMOTE_PLAY;

        if (nStartSong >= 0 && uSecCount>0)
        {
            bool m_bmulticastNewCommand=true;
            for(int i=0; i<uSecCount; ++i)
            {
                // 查找分区
                CSection* pSection = g_Global.m_Sections.GetSectionByMac(vecMac[i]);
                if(!pSection->IsDeviceSupportExtraFeature())
                {
                    m_bmulticastNewCommand=false;
                    break;
                }
            }
            // 添加到播放队列
            CSongUnit *pSongUnit = m_SongPlayer.AddSongUnit(m_bmulticastNewCommand,src,strSongPathName,remoteTask.GetPlayMode());

            if (pSongUnit == NULL)
            {
                return;
            }

            pSongUnit->SetSource(src);
            pSongUnit->SetVolume(playTask.GetVolume());
            pSongUnit->SetPlayFrom(playTask.GetPlayFrom());

            pSongUnit->SetUserAccount(playTask.GetUserAccount());

            pSongUnit->SetRemoteControlTask(remoteTaskInfo);
            
            unsigned int nPlayCount = 0;

            for (unsigned int i=0; i<uSecCount; ++i)
            {
                LPCSection lpSection = g_Global.m_Sections.GetSectionByMac(vecMac[i]);
                bool bPlay = FALSE;
                ProgramSource secSrc = lpSection->GetProSource();

                if (m_SoucePriority[PRO_LOCAL_PLAY] >= m_SoucePriority[secSrc])
                {
                    // 符合播放条件
                    bPlay = TRUE;
                }

                if (bPlay)
                {
                    nPlayCount++;

                    int playID = lpSection->GetPlayID();
    #if 1
                    // 如果分区正在被监听，更新监听分区
                    if(lpSection->IsListening())
                    {
                        m_SongPlayer.UpdateListenInSong(lpSection,pSongUnit);
                    }
    #endif


                    //section.SetPlayID(pSongUnit->GetPlayID());
                    if( playID != pSongUnit->GetPlayID())
                    {
                        // 分区播放ID重置
                        lpSection->SetPlayID(-1);
                        // 如果没有其它分区播放此歌曲
                        if (playID > 0
                                && !g_Global.m_Sections.HasPlaySections(playID)
                                && !g_Global.m_Sections.HasPrePlaySections(playID))
                        {
                            printf("playID=%d,playTask.GetPlayID()=%d\n",playID,pSongUnit->GetPlayID());
                            // 停止播放该歌曲
                            m_SongPlayer.SetPlayStatus(playID, SPS_STOP);
                        }
                    }
                    
                    //设置分区音量为任务音量
                    g_Global.m_Network.m_CmdSend.CmdSetVolume(pSongUnit->GetVolume(), *lpSection);
                    // 发送播放命令
                    SendPlayCommand(SOURCE_REMOTE_PLAY, pSongUnit, lpSection);
                }
            }

            // 如果没有播放的分区
            if (nPlayCount == 0)
            {
                // 则把刚才的播放任务移除掉
                m_SongPlayer.SetPlayStatus(pSongUnit->GetPlayID(), SPS_STOP);
            }
            else
            {
                UpdatePlayInfo(src, pSongUnit, -1, nStartSong);
            }
        }
    }
    else if(remoteTask.GetSourceType() == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
    {
        //判断音频采集器是否存在
        LPCSection lpAudioCollection = g_Global.m_AudioCollectors.GetSectionByMac(remoteTask.GetAudioCollector().mac);
        if(lpAudioCollection == NULL || !lpAudioCollection->IsOnline())
        {
            g_Global.m_Network.AddLog("AudioCollector is null or not online,return!");
            return;
        }

        vector<CMyString> vecMac;
        int nSelSecCount = remoteTask.GetSelectedSections(vecMac);

        //先发送给采集器
        g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(*lpAudioCollection, lpAudioCollection->m_pAudioCollector,remoteTask.GetAudioCollector().channelId);

        for(int i=0; i<nSelSecCount; ++i)
        {
            // 查找分区
            CSection* pSection = g_Global.m_Sections.GetSectionByMac(vecMac[i]);

            if (pSection != NULL && pSection->IsOnline())
            {
                ProgramSource secSrc = pSection->GetProSource();

                //if (m_SoucePriority[PRO_TIMING] >= m_SoucePriority[secSrc])
                {
                    //unsigned char volume = remoteTask.GetFollowDevice()?0xff:timePoint.GetVolume();
                    unsigned char volume = 0xff;
                    g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(*pSection,lpAudioCollection->m_pAudioCollector, remoteTask.GetAudioCollector().channelId,0,false,volume);
                }
            }
        }
    }
}
#endif

void	CPlayQueue::StopSectionPlaySource(CSection* pSection)
{
    if (pSection != NULL)
    {
        int	playID  = pSection->GetPlayID();

        // 停止监听
        if (pSection->IsListening() && playID > 0)
        {
            //m_SongPlayer.StopListenInSong(section.GetPlayID());
            m_SongPlayer.StopListenInSong(pSection);
        }

        //记录PrePlayID
        int prePlayID=pSection->GetPrePlayID();

        // 分区播放ID重置
        pSection->SetPrePlayID(-1);
        pSection->SetPlayID(-1);

        printf("StopSectionPlaySource...\n");

        // 如果没有其它分区播放此歌曲
        if (playID > 0
                && !g_Global.m_Sections.HasPlaySections(playID)
                && !g_Global.m_Sections.HasPrePlaySections(playID))
        {
            // CS 2019-5-7 (掉线后恢复播放)
            if(g_Global.m_bNeedResumeLocalPlay
                && (g_Global.m_Sections.HasOfflinePlaySection(playID) || pSection->m_PlayedRecently.m_nPlayID == playID))
            {
                // 暂停播放该歌曲
                CSongUnit *pSongUnit = g_Global.m_PlayQueue.m_SongPlayer.GetSongUnitByPlayID(playID);
                if (pSongUnit != NULL)
                {
                    if( pSongUnit->GetPlayStatus() != SPS_PAUSE_MANUAL )
                    {
                        m_SongPlayer.SetPlayStatus(playID, SPS_PAUSE_AUTO);
                    }
                }
            }
            else
            {
                // 停止播放该歌曲
                printf("stop1 the songPlayID:%d\n",playID);
                m_SongPlayer.SetPlayStatus(playID, SPS_STOP);

                // 如果是定时点，需要停止定时点
                CTimePoint* pTimePoint = g_Global.m_TimerScheme.GetTimePointByPlayID(playID);

                if (pTimePoint != NULL)
                {
                    pTimePoint->SetPlayID(-1);
                    CMyString strLog;
                    strLog.Format("%s Reset Play ID - Stop Section Play Source", pTimePoint->GetName().C_Str());
                    g_Global.m_Network.AddLog(strLog);
                }
            }
        }
         if (prePlayID > 0
                && !g_Global.m_Sections.HasPlaySections(prePlayID)
                && !g_Global.m_Sections.HasPrePlaySections(prePlayID))
        {
            // 停止播放该歌曲
            printf("stop2 the songPlayID:%d\n",prePlayID);
            m_SongPlayer.SetPlayStatus(prePlayID, SPS_STOP);
        }
    }
}


//  发送播放命令
void	CPlayQueue::SendPlayCommand(unsigned char src, CSongUnit *pSrcUnit, CSection* pSection)
{
    if (pSection == NULL || pSrcUnit == NULL)
    {
        return;
    }
    
    #if SUPPORT_REMOTE_CONTROLER
    if(src == SOURCE_REMOTE_PLAY)
    {
        src = SOURCE_PLAY;
    }
    #endif
    if(src == SOURCE_SPECIFIED_PLAY)
    {
        src = SOURCE_PLAY;
    }
    if(src == SOURCE_SPECIFIED_ALARM)
    {
        src = SOURCE_ALARM;
    }

    // 节目源是空的
    if (pSrcUnit->GetSource() == SOURCE_NULL)
    {
        // 定时时间到，但没有歌曲播放，发送一条空命令，让设备保持定时状态
        g_Global.m_Network.m_CmdSend.CmdNotifyStreamSource(*pSection, src, 0, 0, 0, 0, NULL, NULL, 0, pSrcUnit->GetVolume(),pSrcUnit->GetSongLength(),pSrcUnit->GetTotalFrames(),pSrcUnit->GetCurrentFrame(),pSrcUnit->GetSongMd5(),false);
    }
    // 节目源不是停止，通知设备播放歌曲
    else if (pSrcUnit->GetSource() != SOURCE_STOP)
    {
        song_header_t	songHeader	= pSrcUnit->GetSongHeader();
        CMyString		strName		= GetNameByPathName(pSrcUnit->GetPathName(), TRUE);

        //如果分区当前音源是寻呼，继续发送尝试播放，而不是新的播放，否则会被拒绝，造成播放任务停止。
        if( !(pSection->GetProSource() == PRO_PAGING ) )
        {
            g_Global.m_Network.m_CmdSend.CmdNotifyStreamSource(*pSection,
                                                            src,
                                                            pSrcUnit->GetSongFormat(),
                                                            songHeader.samp_freq,
                                                            (unsigned char)songHeader.bit_samp,
                                                            (unsigned char)songHeader.channels,
                                                            strName.C_Str(),
                                                            pSrcUnit->GetPlayIP(),
                                                            pSrcUnit->GetPlayPort(),
                                                            pSrcUnit->GetVolume(),
                                                            pSrcUnit->GetSongLength(),
                                                            pSrcUnit->GetTotalFrames(),
                                                            pSrcUnit->GetCurrentFrame(),
                                                            pSrcUnit->GetSongMd5(),
                                                            pSrcUnit->GetIsMulticastUseNewCmd());
        }
        else
        {
            g_Global.m_Network.m_CmdSend.CmdNotifyStreamSource(*pSection,
                                                src,
                                                pSrcUnit->GetSongFormat(),
                                                songHeader.samp_freq,
                                                (unsigned char)songHeader.bit_samp,
                                                (unsigned char)songHeader.channels,
                                                strName.C_Str(),
                                                pSrcUnit->GetPlayIP(),
                                                pSrcUnit->GetPlayPort(),
                                                pSrcUnit->GetVolume(),
                                                pSrcUnit->GetSongLength(),
                                                pSrcUnit->GetTotalFrames(),
                                                pSrcUnit->GetCurrentFrame(),
                                                pSrcUnit->GetSongMd5(),
                                                pSrcUnit->GetIsMulticastUseNewCmd(),
                                                0x01);   // 确认标志
        }
        

        // 更新分区播放信息
        pSection->SetPlayID(pSrcUnit->GetPlayID());
    }

}


// 更新播放信息
void	CPlayQueue::UpdatePlayInfo(unsigned char src, CSongUnit *pSrcUnit, int nList, int nSong)
{
    // 未设置过播放信息
    //if (pSrcUnit->GetStopPlayTime() <= 0)
    {
        // 更新播放信息
        pSrcUnit->SetSongPos(nList, nSong);

        timeval tv;
        gettimeofday(&tv, NULL);
        ctime_t time = (ctime_t)(tv.tv_sec)*1000 + tv.tv_usec/1000;

        pSrcUnit->SetStopPlayTime(time);

        // 添加到播放列表
        PushPlayUnit(pSrcUnit->GetPlayID());
    }
}


// 检测定时点
void   CPlayQueue::CheckTimers(CTime& t)
{
    CScheme&	curScheme	= g_Global.m_TimerScheme.GetScheme(g_Global.m_TimerScheme.GetCurScheme());
    int			nTimerCount = curScheme.GetTimerCount();


    for (int i=0; i<nTimerCount; ++i)
    {
        CTimePoint& timePoint = curScheme.GetTimer(i);

        // 如果刚好到开始定时点时间
        if (timePoint.IsToStartWorking(t))
        {
            if( timePoint.GetSingleCancel() == false )
            {
                CMyString strLog;
                strLog.Format(("Start Timer ID = %d, name = %s: %04d-%02d-%02d %02d:%02d:%02d"),
                            timePoint.GetID(), timePoint.GetName().C_Str(),
                            t.GetYear(), t.GetMonth(), t.GetDay(), t.GetHour(), t.GetMinute(), t.GetSecond());
                g_Global.m_Network.AddLog(strLog);

                LOG_INFO(strLog.C_Str());

                CPlayTask playTask(SOURCE_TIMER, {""}, NULL, 0);
                //playTask.SetTimerPoint(&timePoint);
                playTask.SetTimerPointID(timePoint.GetID());
                PushPlayTask(playTask);
            }
        }
        else if (timePoint.IsSectionToReadyWorking(t))
        {
            //找到该定时点选择中的分区并通知提前打开短路输出
            vector<CMyString> vecMac;
            int nSelSecCount = timePoint.GetSelectedSections(vecMac);

            for(int i=0; i<nSelSecCount; ++i)
            {
                // 查找分区
                CSection* pSection = g_Global.m_Sections.GetSectionByMac(vecMac[i]);
                if (pSection != NULL && pSection->IsOnline() && !pSection->IsTimerInvalid())
                {
                    g_Global.m_Network.m_CmdSend.CmdNotifyDecoderReadyTiming(*pSection);
                }
            }
            //找到该定时点选择的时序器并通知提前打开对应的通道
        }
        // 如果刚好到停止定时点时间而且不是随机单曲播放（单曲是不管时间的，要讨论需不需要管时间）
        else if (timePoint.IsToStopWorking(t) && !timePoint.GetSinglePlay())
        {
            if( timePoint.GetSingleCancel() == false )
            {
                 CMyString strLog;
                strLog.Format(("Stop Timer Timer ID = %d, name = %s: %04d-%02d-%02d %02d:%02d:%02d"),
                            timePoint.GetID(),timePoint.GetName().C_Str(),
                            t.GetYear(), t.GetMonth(), t.GetDay(), t.GetHour(), t.GetMinute(), t.GetSecond());
                g_Global.m_Network.AddLog(strLog);

                /*
                CPlayTask playTask(SOURCE_TIMER_STOP, "", NULL, 0);
                playTask.SetTimerPointID(timePoint.GetID());
                PushPlayTask(playTask);
                */
                StopTimerPoint(timePoint,true);
            }
        }
    }
}


// 播放歌曲结束
void   CPlayQueue::PlaySourceEnd(int playID)
{
NOTIFY("PlaySourceEnd();");
    CSongUnit *pSongUnit = m_SongPlayer.GetSongUnitByPlayID(playID);

    // 停止该播放单元的监听
    //m_SongPlayer.StopListenInSong(playID);

    printf("PlaySourceEnd...\n");

    if (pSongUnit != NULL)
    {
        unsigned char		src	= pSongUnit->GetSource();
        int			nNextSong	= -1;
        int			nNextList	= pSongUnit->GetListIndex();
        printf("playID=%d,src=%d,nNextList=%d,name=%s,format=%d\n",pSongUnit->GetPlayID(),src,nNextList,pSongUnit->GetPathName().Data(),pSongUnit->GetSongFormat());
        bool		isTimer		= (src == SOURCE_TIMER);
        bool        isEvent     = (src == SOURCE_EVENT);
        #if SUPPORT_REMOTE_CONTROLER
        bool        isRemoteControl = (src == SOURCE_REMOTE_PLAY);
        CRemoteControlTask *remoteTask=NULL;
        #endif
        CTimePoint *pTimePoint	= NULL;
        bool        isTTS       = FALSE;      // zhuyg

        bool        isSpecifiedPlay = (src == SOURCE_SPECIFIED_PLAY || src == SOURCE_SPECIFIED_ALARM);
        int         playType    = pSongUnit->GetPlayType(); //播放类型，1为歌曲播放，2为TTS
        int         playCycle   = pSongUnit->GetPlayCount();//播放次数

NEXT_SONG2:
        // 定时播放
        if (isTimer)
        {
            pTimePoint = g_Global.m_TimerScheme.GetTimePointByPlayID(pSongUnit->GetPlayID());
            if(pTimePoint != NULL)
            {
                nNextSong  = pTimePoint->GetNextSong(pSongUnit->GetSongIndex());
            }
        }
        #if SUPPORT_REMOTE_CONTROLER
        else if(isRemoteControl)
        {
                stRemoteTaskInfo& remoteTaskInfo = pSongUnit->GetRemoteControlTask();
                //查找遥控器
                CSection* pRemoteControler = g_Global.m_RemoteControlers.GetSectionByMac(remoteTaskInfo.strMac);
                if(pRemoteControler == NULL)
                {
                    nNextSong = -1;
                }
                else if (remoteTaskInfo.nTaskID <=0 || remoteTaskInfo.nTaskID > pRemoteControler->m_pRemoteControler->GetTaskCnt())
                {
                    nNextSong = -1;
                }
                else
                {
                    remoteTask=pRemoteControler->m_pRemoteControler->lpGetTaskDetail(remoteTaskInfo.nTaskID);
                    nNextSong  = remoteTask->GetNextSong(pSongUnit->GetSongIndex(),remoteTask->GetPlayMode());
                }
        }
        #endif
        else if(isSpecifiedPlay)
        {
            nNextSong = pSongUnit->GetSpecifiedPlayNextSongIndex(pSongUnit->GetSongIndex());
            printf("pSongUnit->GetSongIndex()=%d,nNextSong1=%d\n",pSongUnit->GetSongIndex(),nNextSong);
        }
        // 事件触发
        else if(isEvent)
        {
            nNextSong = -1;
        }
        // 钟声与播放歌曲
        else
        {
            #if SUPPORT_MANUALTASK_PLAYMODE
            int playMode = pSongUnit->GetPlayMode();
            #else
            int playMode=g_Global.m_PlayList.GetPlayMode();
            LPCUserInfo   user= g_Global.m_Users.GetUserByAccount(pSongUnit->GetUserAccount());
            if(user!=NULL)
            {
                playMode = user->GetPlayMode();
            }
            #endif
            nNextSong = g_Global.m_PlayList.GetNextSong(pSongUnit->GetListIndex(), pSongUnit->GetSongIndex(),playMode);
#if 0
            if(nNextList >= 0)
            {
                // 如果是TTS列表播放，只播放一次 (事件，TTS)
                //printf("index : %d\n", pSongUnit->GetListIndex());
                CMyString strListName = g_Global.m_PlayList.GetListName(pSongUnit->GetListIndex());
                //puts(strListName.C_Str());
                if(strListName == TTS_LIST_NAME)
                {
                    nNextSong = -1;
                    isTTS = TRUE;
                }
            }
#endif
        }

        CMyString strNextPathName;	// 下一首歌的全路径名
        NOTIFY("nNextList : %d", nNextList);
        NOTIFY("nNextSong : %d", nNextSong);

        int playCount = 0;
        bool songFound =false;
        if(nNextSong>=0)
        {
            if (isTimer)
            {
                playCount = pTimePoint?pTimePoint->GetSongCount():0;
            }
            #if SUPPORT_REMOTE_CONTROLER
            else if(isRemoteControl)
            {
                if(remoteTask)
                {
                    playCount = remoteTask->GetSongCount();
                }
            }
            #endif
            else if(isSpecifiedPlay)
            {
                playCount = pSongUnit->GetVecPathNames().size();
            }
            else
            {
                playCount = g_Global.m_PlayList.GetListSongCount(nNextList);
            }
        }
        playCount=playCount>10?10:playCount;
        if( pSongUnit->GetListIndex() == 0 )    //TTS LIST,单独处理
        {
            playCount=playCount>MAX_SONGS_TTS_LIST_COUNT?MAX_SONGS_TTS_LIST_COUNT:playCount;
        }
        while (playCount-- && nNextSong >= 0)
        {
            CSong* pNextSong=NULL;
            CSong  song_specifiedPlay;

            if (isTimer)
            {
                if(pTimePoint)
                {
                    pNextSong = &pTimePoint->GetSong(nNextSong);
                }
            }
            #if SUPPORT_REMOTE_CONTROLER
            else if(isRemoteControl)
            {
                if(remoteTask)
                {
                    pNextSong = &remoteTask->GetSong(nNextSong);
                }
            }
            #endif
            else if(!isSpecifiedPlay)
            {
                pNextSong = &g_Global.m_PlayList.GetListSong(nNextList, nNextSong);

                //TTS LIST,单独处理
                LPCUserInfo pUser = g_Global.m_Users.GetUserByAccount(pSongUnit->GetUserAccount());
                if(pUser)
                {
                    if( pSongUnit->GetListIndex() == 0 )    //ListIndex=0
                    {
                        //如果播放单元不是超级管理员创建的且歌曲所有者不是管理员，才需要判断
                        if( strcmp(pSongUnit->GetUserAccount().data(),SUPER_USER_NAME) && strcmp(pNextSong->GetUserAccount().Data(),SUPER_USER_NAME) )
                        {
                            if( pNextSong->GetUserAccount() != CMyString(pSongUnit->GetUserAccount().data() ) && ((pUser->GetLimitCode() & USER_LIMITS_PLAYLIST) == 0) )
                            {
                                int playMode = pSongUnit->GetPlayMode();
                                nNextSong = g_Global.m_PlayList.GetNextSong(pSongUnit->GetListIndex(), nNextSong,playMode);
                                continue;
                            }
                        }
                    }
                }
            }

            if(isSpecifiedPlay)
            {
                //注意这里通过GetVecPathNames拿到的是包含根目录的完整路径的歌曲，而传入Csong的需要相对路径，所以需要处理一下
                strNextPathName=pSongUnit->GetVecPathNames().at(nNextSong);
                CMyString strNextHttpPath=strNextPathName;
                if(playType == 1)
                {
                    strNextHttpPath = GetHttpURLPathByPathName(strNextPathName);
                }
                song_specifiedPlay = CSong(strNextHttpPath);
                pNextSong=&song_specifiedPlay;
            }
            else
            {
                strNextPathName = g_Global.m_strHttpRootDir + pNextSong->GetPathName();
            }

            if(!pNextSong)
            {
                continue;
            }

            // 判断歌曲是否存在
            if (pNextSong->IsExist() && pNextSong->IsStreamFormat())
            {
                songFound=true;
                break;
            }
            // 如果不存在则继续寻找下一首
            else
            {
                if (isTimer)
                {
                    if(pTimePoint)
                    {
                        nNextSong  = pTimePoint->GetNextSong(nNextSong);
                    }
                }
                #if SUPPORT_REMOTE_CONTROLER
                else if(isRemoteControl)
                {
                    if(remoteTask)
                    {
                        nNextSong = remoteTask->GetNextSong(nNextSong,remoteTask->GetPlayMode());
                    }
                }
                #endif
                else if(isSpecifiedPlay)
                {
                    nNextSong = pSongUnit->GetSpecifiedPlayNextSongIndex(nNextSong);
                }
                else
                {
                    #if SUPPORT_MANUALTASK_PLAYMODE
                    int playMode = pSongUnit->GetPlayMode();
                    #else
                    int playMode=g_Global.m_PlayList.GetPlayMode();
                    LPCUserInfo   user= g_Global.m_Users.GetUserByAccount(pSongUnit->GetUserAccount());
                    if(user!=NULL)
                    {
                        playMode = user->GetPlayMode();
                    }
                    #endif
                    nNextSong = g_Global.m_PlayList.GetNextSong(pSongUnit->GetListIndex(), nNextSong,playMode);
                }
            }


        }

        if(!songFound)
        {
            nNextSong = -1;
        }

NOTIFY("strNextPathName : %s", strNextPathName.C_Str());
        unsigned int	pPlaySecs[MAX_SECTION_COUNT_FORMAL] = {0};
        unsigned int	uPlayCount	= 0;

        // 得到播放该歌曲的分区
        uPlayCount = g_Global.m_Sections.GetPlaySections(playID, pPlaySecs);

        // 无需继续播放
        if (nNextSong < 0)
        {
            //ERROR("无需继续播放");
            // 停止歌曲播放
            m_SongPlayer.SetPlayStatus(playID, SPS_STOP);

            // 定时数据为空
            if(isTimer && pTimePoint == NULL)
            {
                // 设置分区空闲状态
                for (unsigned int i=0; i<uPlayCount; ++i)
                {
                    CSection& section = g_Global.m_Sections.GetSection(pPlaySecs[i]);

                    section.SetPlayID(-1);
                    g_Global.m_Network.m_CmdSend.CmdSetIdleStatus(section);

                    //
                    if(section.IsListening())
                    {
                        m_SongPlayer.StopListenInSong(&section);
                    }
                }
            }
            // 如果是播放歌曲，或者定时播放到歌曲结束或是随机单曲播放   或者播放语音广播
            //else if (!isTimer || (isTimer && (pTimePoint->IsPlayToEnd() || pTimePoint->GetSinglePlay())) || isTTS || isEvent)
            if (!isTimer || (isTimer &&  pTimePoint != NULL) || isTTS || isEvent)
            {
                if (isTimer)
                {
                    //pTimePoint->SetPlayID(-1);
                    CMyString strLog;
                    strLog.Format("%s Reset Play ID - Timer : nNextSong < 0", pTimePoint->GetName().C_Str());
                    g_Global.m_Network.AddLog(strLog);

                    if(pTimePoint != NULL)
                        StopTimerPoint(*pTimePoint);
                }
                else    //如果是定时，上面已经将分区设置了空闲状态，不能再发送一遍，否则分区恢复歌曲播放后将又收到一个空闲状态
                {
                    // 设置分区空闲状态
                    for (unsigned int i=0; i<uPlayCount; ++i)
                    {
                        CSection& section = g_Global.m_Sections.GetSection(pPlaySecs[i]);

                        section.SetPlayID(-1);
                        g_Global.m_Network.m_CmdSend.CmdSetIdleStatus(section);

                        //
                        if(section.IsListening())
                        {
                            m_SongPlayer.StopListenInSong(&section);
                        }
                    }
                }
                if(g_Global.m_bNeedResumeLocalPlay)
                {
                    // CS 2019-5-8（掉线后恢复播放功能）
                    for(int j = 0; j < g_Global.m_Sections.GetSecCount(); j++)
                    {
                        CSection& section = g_Global.m_Sections.GetSection(j);

                        if(section.GetProSource() == PRO_OFFLINE && section.m_PlayedRecently.m_nPlayID == playID)
                        {
                            section.m_PlayedRecently.ResetTimeout();
                        }
                    }
                }
            }
        }
        // 继续播放下一曲
        else
        {
            // 把之前的playID传进去，一般会继续返回之前的CSongUnit
            CSongUnit *pNextSongUnit = m_SongPlayer.AddSongUnit(pSongUnit->GetIsMulticastUseNewCmd(),src,strNextPathName,pSongUnit->GetPlayMode(),playID);

            if (pNextSongUnit == NULL)
            {
                CMyString strLog;
                CMyString strError = LANG_STR(LANG_SECTION_PLAY_LIST, "Error Parsing File", ("解析文件出现错误"));
                strLog.Format(("PlaySourceEnd %s : %s"), strError.C_Str(), strNextPathName.C_Str());
                g_Global.m_Network.AddLog(strLog);
                NOTIFY(strLog.C_Str());

                // 设置新的位置，寻找下一曲
                pSongUnit->SetSongPos(pSongUnit->GetListIndex(), nNextSong);
                goto NEXT_SONG2;
            }
            else
            {
                if (pNextSongUnit->GetPlayID() != playID)
                {
                    CMyString strLog;
                    strLog.Format(("GetPlayID() != playID 停止歌曲播放 : %s"), strNextPathName.C_Str());
                    g_Global.m_Network.AddLog(strLog);
                    NOTIFY(strLog.C_Str());

                    // 停止歌曲播放
                    m_SongPlayer.SetPlayStatus(playID, SPS_STOP);
                }
            }

            pNextSongUnit->SetSource(src);

            if (isTimer && pTimePoint)
            {
                if(pTimePoint->GetFollowDevice())
                {
                    pNextSongUnit->SetVolume(-1);
                }
                else
                {
                    pNextSongUnit->SetVolume(pTimePoint->GetVolume());
                }
            }

            NOTIFY("SendPlayCommand count : %d", uPlayCount);
            // 指定的分区都播放下一首
            for (unsigned int i=0; i<uPlayCount; ++i)
            {
                CSection& section = g_Global.m_Sections.GetSection(pPlaySecs[i]);

                if(section.GetPlayStatus() == (PlayStatus)SPS_PAUSE_AUTO)        // CS 2019-4-29 （暂停播放功能）
                {
                    section.SetPlayID(pNextSongUnit->GetPlayID());    // 暂停状态下只设置播放任务ID，不下发播放命令
                }
                else
                {
                    SendPlayCommand(src, pNextSongUnit, &section);
                }
            }

            if(g_Global.m_bNeedResumeLocalPlay)
            {
                // CS 2019-5-8 掉线状态下设置播放任务ID（掉线后恢复播放功能）
                for(int j = 0; j < g_Global.m_Sections.GetSecCount(); j++)
                {
                    CSection& section = g_Global.m_Sections.GetSection(j);

                    if(section.GetProSource() == PRO_OFFLINE && section.m_PlayedRecently.m_nPlayID == playID)
                    {
                        section.m_PlayedRecently.m_nPlayID = pNextSongUnit->GetPlayID();
                    }
                }
            }
            
            if(uPlayCount==0)
            {
                //定时音源不需要暂停，需要跟随时间而进行
                if(pNextSongUnit->GetSource() != SOURCE_TIMER && pNextSongUnit->GetPlayStatus() != SPS_PAUSE_AUTO)
                {
                    printf("uPlayCount=0...,pause\n");
                    pNextSongUnit->SetPlayStatus(SPS_PAUSE_MANUAL);
                }
            }

            UpdatePlayInfo(src, pNextSongUnit, nNextList, nNextSong);


            if (isTimer && pTimePoint)
            {
                pTimePoint->SetPlayID(pNextSongUnit->GetPlayID());
            }
        }
    }
}




// 手动播放任务上下曲
void   CPlayQueue::PlaySourceMuanualPreNext(int playID,int action)
{
NOTIFY("PlaySourceMuanualPreNext();");
    CSongUnit *pSongUnit = m_SongPlayer.GetSongUnitByPlayID(playID);

    // 停止该播放单元的监听
    //m_SongPlayer.StopListenInSong(playID);

    if (pSongUnit != NULL)
    {
        unsigned char		src	= pSongUnit->GetSource();

        if( src != SOURCE_PLAY && src != SOURCE_REMOTE_PLAY)
        {
            return;
        }

        int			nNextSong	= -1;
        int			nNextList	= pSongUnit->GetListIndex();
        printf("playID=%d,nNextList=%d,name=%s,format=%d\n",pSongUnit->GetPlayID(),nNextList,pSongUnit->GetPathName().Data(),pSongUnit->GetSongFormat());
        bool		isTimer		= (src == SOURCE_TIMER);
        bool        isEvent     = (src == SOURCE_EVENT);
        #if SUPPORT_REMOTE_CONTROLER
        bool        isRemoteControl = (src == SOURCE_REMOTE_PLAY);
        CRemoteControlTask *remoteTask=NULL;
        #endif
        CTimePoint *pTimePoint	= NULL;
        bool        isTTS       = FALSE;      // zhuyg

NEXT_SONG2:
        // 定时播放
        if (isTimer)
        {
            pTimePoint = g_Global.m_TimerScheme.GetTimePointByPlayID(pSongUnit->GetPlayID());
            if(pTimePoint != NULL)
            {
                nNextSong  = pTimePoint->GetNextSong(pSongUnit->GetSongIndex());
            }
        }
        #if SUPPORT_REMOTE_CONTROLER
        else if(isRemoteControl)
        {
            stRemoteTaskInfo& remoteTaskInfo = pSongUnit->GetRemoteControlTask();
            //查找遥控器
            CSection* pRemoteControler = g_Global.m_RemoteControlers.GetSectionByMac(remoteTaskInfo.strMac);
            if(pRemoteControler == NULL)
            {
                nNextSong = -1;
            }
            else if (remoteTaskInfo.nTaskID <=0 || remoteTaskInfo.nTaskID > pRemoteControler->m_pRemoteControler->GetTaskCnt())
            {
                nNextSong = -1;
            }
            else
            {
                remoteTask=pRemoteControler->m_pRemoteControler->lpGetTaskDetail(remoteTaskInfo.nTaskID);

                int playMode = remoteTask->GetPlayMode();
                if(action == SAC_NEXT)
                {
                    if( playMode!=PM_RANDOM )
                        playMode = PM_LIST_CYCLE;
                    nNextSong = remoteTask->GetNextSong(pSongUnit->GetSongIndex(),playMode);
                }
                else
                {
                    nNextSong = remoteTask->GetPreSong(pSongUnit->GetSongIndex(),playMode);
                }
            }
        }
        #endif
        // 事件触发
        else if(isEvent)
        {
            nNextSong = -1;
        }
        // 钟声与播放歌曲
        else
        {
            int playMode = pSongUnit->GetPlayMode();
            
            if(action == SAC_NEXT)
            {
                if( playMode!=PM_RANDOM )
                    playMode = PM_LIST_CYCLE;
                nNextSong = g_Global.m_PlayList.GetNextSong(pSongUnit->GetListIndex(), pSongUnit->GetSongIndex(),playMode);
            }
            else
            {
                nNextSong = g_Global.m_PlayList.GetPreSong(pSongUnit->GetListIndex(), pSongUnit->GetSongIndex(),playMode);
            }
#if 0
            if(nNextList >= 0)
            {
                // 如果是TTS列表播放，只播放一次 (事件，TTS)
                //printf("index : %d\n", pSongUnit->GetListIndex());
                CMyString strListName = g_Global.m_PlayList.GetListName(pSongUnit->GetListIndex());
                //puts(strListName.C_Str());
                if(strListName == TTS_LIST_NAME)
                {
                    nNextSong = -1;
                    isTTS = TRUE;
                }
            }
#endif
        }

        CMyString strNextPathName;	// 下一首歌的全路径名
NOTIFY("nNextList : %d", nNextList);
NOTIFY("nNextSong : %d", nNextSong);

        int playCount = 0;
        bool songFound =false;
        if (isTimer)
        {
            playCount = pTimePoint->GetSongCount();
        }
        #if SUPPORT_REMOTE_CONTROLER
        else if(isRemoteControl)
        {
            if(remoteTask)
            {
                playCount = remoteTask->GetSongCount();
            }
        }
        #endif
        else
        {
            playCount = g_Global.m_PlayList.GetListSongCount(nNextList);
        }
        playCount=playCount>10?10:playCount;
        if( pSongUnit->GetListIndex() == 0 )    //TTS LIST,单独处理
        {
            playCount=playCount>MAX_SONGS_TTS_LIST_COUNT?MAX_SONGS_TTS_LIST_COUNT:playCount;
        }
        while (playCount-- && nNextSong >= 0)
        {
            CSong* pNextSong=NULL;

            if (isTimer)
            {
                pNextSong = &pTimePoint->GetSong(nNextSong);
            }
            #if SUPPORT_REMOTE_CONTROLER
            else if(isRemoteControl)
            {
                if(remoteTask)
                {
                    pNextSong = &remoteTask->GetSong(nNextSong);
                }
            }
            #endif
            else
            {
                pNextSong = &g_Global.m_PlayList.GetListSong(nNextList, nNextSong);

                //TTS LIST,单独处理
                LPCUserInfo pUser = g_Global.m_Users.GetUserByAccount(pSongUnit->GetUserAccount());
                if(pUser)
                {
                    if( pSongUnit->GetListIndex() == 0 )    //ListIndex=0
                    {
                        //如果播放单元不是超级管理员创建的且歌曲所有者不是管理员，才需要判断
                        if( strcmp(pSongUnit->GetUserAccount().data(),SUPER_USER_NAME) && strcmp(pNextSong->GetUserAccount().Data(),SUPER_USER_NAME) )
                        {
                            if( pNextSong->GetUserAccount() != CMyString(pSongUnit->GetUserAccount().data() ) && ((pUser->GetLimitCode() & USER_LIMITS_PLAYLIST) == 0) )
                            {
                                int playMode = pSongUnit->GetPlayMode();
                                if(action == SAC_NEXT)
                                {
                                    if( playMode!=PM_RANDOM )
                                        playMode = PM_LIST_CYCLE;
                                    nNextSong = g_Global.m_PlayList.GetNextSong(pSongUnit->GetListIndex(), nNextSong,playMode);
                                }
                                else
                                {
                                    nNextSong = g_Global.m_PlayList.GetPreSong(pSongUnit->GetListIndex(), nNextSong,playMode);
                                }
                                continue;
                            }
                        }
                    }
                }
            }

            strNextPathName = g_Global.m_strHttpRootDir + pNextSong->GetPathName();

            // 判断歌曲是否存在
            if (pNextSong->IsExist() && pNextSong->IsStreamFormat())
            {
                songFound=true;
                break;
            }
            // 如果不存在则继续寻找下一首
            else
            {
                if (isTimer)
                {
                    nNextSong  = pTimePoint->GetNextSong(nNextSong);
                }
                #if SUPPORT_REMOTE_CONTROLER
                else if(isRemoteControl)
                {
                    if(remoteTask)
                    {
                        int playMode = remoteTask->GetPlayMode();
                        if(action == SAC_NEXT)
                        {
                            if( playMode!=PM_RANDOM )
                                playMode = PM_LIST_CYCLE;
                            nNextSong = remoteTask->GetNextSong(pSongUnit->GetSongIndex(),playMode);
                        }
                        else
                        {
                            nNextSong = remoteTask->GetPreSong(pSongUnit->GetSongIndex(),playMode);
                        }
                    }
                }
                #endif
                else
                {
                    int playMode = pSongUnit->GetPlayMode();
                    if(action == SAC_NEXT)
                    {
                        if( playMode!=PM_RANDOM )
                            playMode = PM_LIST_CYCLE;
                        nNextSong = g_Global.m_PlayList.GetNextSong(pSongUnit->GetListIndex(), nNextSong,playMode);
                    }
                    else
                    {
                        nNextSong = g_Global.m_PlayList.GetPreSong(pSongUnit->GetListIndex(), nNextSong,playMode);
                    }
                }
            }
        }

        if(!songFound)
        {
            nNextSong = -1;
        }

NOTIFY("strNextPathName : %s", strNextPathName.C_Str());
        unsigned int	pPlaySecs[MAX_SECTION_COUNT_FORMAL] = {0};
        unsigned int	uPlayCount	= 0;

        // 得到播放该歌曲的分区
        uPlayCount = g_Global.m_Sections.GetPlaySections(playID, pPlaySecs);

        // 无需继续播放
        if (nNextSong < 0)
        {
            //ERROR("无需继续播放");
            // 停止歌曲播放
            m_SongPlayer.SetPlayStatus(playID, SPS_STOP);

            // 定时数据为空
            if(isTimer && pTimePoint == NULL)
            {
                // 设置分区空闲状态
                for (unsigned int i=0; i<uPlayCount; ++i)
                {
                    CSection& section = g_Global.m_Sections.GetSection(pPlaySecs[i]);

                    section.SetPlayID(-1);
                    g_Global.m_Network.m_CmdSend.CmdSetIdleStatus(section);

                    //
                    if(section.IsListening())
                    {
                        m_SongPlayer.StopListenInSong(&section);
                    }
                }
            }
            // 如果是播放歌曲，或者定时播放到歌曲结束或是随机单曲播放   或者播放语音广播
            //else if (!isTimer || (isTimer && (pTimePoint->IsPlayToEnd() || pTimePoint->GetSinglePlay())) || isTTS || isEvent)
            if (!isTimer || (isTimer &&  pTimePoint != NULL) || isTTS || isEvent)
            {
                if (isTimer)
                {
                    //pTimePoint->SetPlayID(-1);
                    CMyString strLog;
                    strLog.Format("%s Reset Play ID - Timer : nNextSong < 0", pTimePoint->GetName().C_Str());
                    g_Global.m_Network.AddLog(strLog);

                    if(pTimePoint != NULL)
                        StopTimerPoint(*pTimePoint);
                }
                else    //如果是定时，上面已经将分区设置了空闲状态，不能再发送一遍，否则分区恢复歌曲播放后将又收到一个空闲状态
                {
                    // 设置分区空闲状态
                    for (unsigned int i=0; i<uPlayCount; ++i)
                    {
                        CSection& section = g_Global.m_Sections.GetSection(pPlaySecs[i]);

                        section.SetPlayID(-1);
                        g_Global.m_Network.m_CmdSend.CmdSetIdleStatus(section);

                        //
                        if(section.IsListening())
                        {
                            m_SongPlayer.StopListenInSong(&section);
                        }
                    }
                }
                if(g_Global.m_bNeedResumeLocalPlay)
                {
                    // CS 2019-5-8（掉线后恢复播放功能）
                    for(int j = 0; j < g_Global.m_Sections.GetSecCount(); j++)
                    {
                        CSection& section = g_Global.m_Sections.GetSection(j);

                        if(section.GetProSource() == PRO_OFFLINE && section.m_PlayedRecently.m_nPlayID == playID)
                        {
                            section.m_PlayedRecently.ResetTimeout();
                        }
                    }
                }
            }
        }
        // 继续播放下一曲
        else
        {
            // 把之前的playID传进去，一般会继续返回之前的CSongUnit
            CSongUnit *pNextSongUnit = m_SongPlayer.AddSongUnit(pSongUnit->GetIsMulticastUseNewCmd(),src,strNextPathName,pSongUnit->GetPlayMode(),playID);

            if (pNextSongUnit == NULL)
            {
                CMyString strLog;
                CMyString strError = LANG_STR(LANG_SECTION_PLAY_LIST, "Error Parsing File", ("解析文件出现错误"));
                strLog.Format(("PlaySourceEnd %s : %s"), strError.C_Str(), strNextPathName.C_Str());
                g_Global.m_Network.AddLog(strLog);
                NOTIFY(strLog.C_Str());

                // 设置新的位置，寻找下一曲
                pSongUnit->SetSongPos(pSongUnit->GetListIndex(), nNextSong);
                goto NEXT_SONG2;
            }
            else
            {
                if (pNextSongUnit->GetPlayID() != playID)
                {
                    CMyString strLog;
                    strLog.Format(("GetPlayID() != playID 停止歌曲播放 : %s"), strNextPathName.C_Str());
                    g_Global.m_Network.AddLog(strLog);
                    NOTIFY(strLog.C_Str());

                    // 停止歌曲播放
                    m_SongPlayer.SetPlayStatus(playID, SPS_STOP);
                }
            }

            pNextSongUnit->SetSource(src);

            if (isTimer)
            {
                if(pTimePoint->GetFollowDevice())
                {
                    pNextSongUnit->SetVolume(-1);
                }
                else
                {
                    pNextSongUnit->SetVolume(pTimePoint->GetVolume());
                }
            }

            NOTIFY("SendPlayCommand count : %d", uPlayCount);
            // 指定的分区都播放下一首
            for (unsigned int i=0; i<uPlayCount; ++i)
            {
                CSection& section = g_Global.m_Sections.GetSection(pPlaySecs[i]);

                if(section.GetPlayStatus() == (PlayStatus)SPS_PAUSE_AUTO)        // CS 2019-4-29 （暂停播放功能）
                {
                    section.SetPlayID(pNextSongUnit->GetPlayID());    // 暂停状态下只设置播放任务ID，不下发播放命令
                }
                else
                {
                    SendPlayCommand(src, pNextSongUnit, &section);
                }
            }

            if(g_Global.m_bNeedResumeLocalPlay)
            {
                // CS 2019-5-8 掉线状态下设置播放任务ID（掉线后恢复播放功能）
                for(int j = 0; j < g_Global.m_Sections.GetSecCount(); j++)
                {
                    CSection& section = g_Global.m_Sections.GetSection(j);

                    if(section.GetProSource() == PRO_OFFLINE && section.m_PlayedRecently.m_nPlayID == playID)
                    {
                        section.m_PlayedRecently.m_nPlayID = pNextSongUnit->GetPlayID();
                    }
                }
            }

            UpdatePlayInfo(src, pNextSongUnit, nNextList, nNextSong);

            if (isTimer)
            {
                pTimePoint->SetPlayID(pNextSongUnit->GetPlayID());
            }
        }
    }
}


void   CPlayQueue::SectoinSourceChange(CSection& section, ProgramSource newSrc)
{
    ProgramSource curSrc = section.GetProSource();
    //printf("SectoinSourceChange:newSrc=%d,curSrc=%d\n",newSrc,curSrc);
    // 分区开始寻呼，
    if (newSrc == PRO_PAGING &&
        (curSrc == PRO_LOCAL_PLAY || curSrc == PRO_TIMING))		// 之前是本地播放或者定时播放
    {
        // 如果是只有一个分区在播放该歌曲，则暂停播放歌曲
        // 如果多个分区在播放该歌曲，则不需要暂停，因为其它分区还需要继续播放
        if (g_Global.m_Sections.IsOnePlaySectionPlaying(section.GetPlayID()))
        {
            CSongUnit *pSongUnit = g_Global.m_PlayQueue.m_SongPlayer.GetSongUnitByPlayID(section.GetPlayID());
            if (pSongUnit != NULL)
            {
                if( pSongUnit->GetPlayStatus() != SPS_PAUSE_MANUAL )
                {
                    m_SongPlayer.SetPlayStatus(section.GetPlayID(), SPS_PAUSE_AUTO);
                }
            }
        }
    }
    // 播放音频采集器音源
    else if (CProtocol::IsAudioCollectorSrc(newSrc) && curSrc == PRO_LOCAL_PLAY)	// 之前是本地播放
    {
        #if 0   //20231222 不停止分区的播放任务，因为采集器支持触发，原来在进行网络点播，采集触发停止后需要恢复播放
        // 停止该分区的播放任务
        g_Global.m_PlayQueue.StopSectionPlaySource(&section);
        #endif
    }
    // 分区变为空闲状态
    else if (newSrc == PRO_IDLE || newSrc == PRO_ANALOG_INPUT || newSrc == PRO_100V)
    {
        int try_play_pre=1;
        // 控制设备或其它原因使分区变成停止, 而且该分区有播放任务，变为空闲状态，则发送命令确认（其它设备停止该分区或者意外停止）
        if (section.GetPlayID() > 0)
        {
            try_play_pre=0;
            CSongUnit *pSrcUnit = m_SongPlayer.GetSongUnitByPlayID(section.GetPlayID());
            bool IsPlayIDValid=m_SongPlayer.IsPlayIDValid(section.GetPlayID());

            if (pSrcUnit != NULL && IsPlayIDValid)
            {
                song_header_t	songHeader      = pSrcUnit->GetSongHeader();
                CMyString		   strName		= GetNameByPathName(pSrcUnit->GetPathName(), TRUE);

                // 下发播放命令
                g_Global.m_Network.m_CmdSend.CmdNotifyStreamSource( section,
                                                                    pSrcUnit->GetSource(),
                                                                    pSrcUnit->GetSongFormat(),
                                                                    songHeader.samp_freq,
                                                                    (unsigned char)songHeader.bit_samp,
                                                                    (unsigned char)songHeader.channels,
                                                                    strName.C_Str(),
                                                                    pSrcUnit->GetPlayIP(),
                                                                    pSrcUnit->GetPlayPort(),
                                                                    pSrcUnit->GetVolume(),
                                                                    pSrcUnit->GetSongLength(),
                                                                    pSrcUnit->GetTotalFrames(),
                                                                    pSrcUnit->GetCurrentFrame(),
                                                                    pSrcUnit->GetSongMd5(),
                                                                    pSrcUnit->GetIsMulticastUseNewCmd(),
                                                                    0x01);   // 确认标志

                // 寻呼停止时恢复播放, 如果之前有播放歌曲/定时播放，则恢复播放
                // 如果定时已结束的话，已经在CheckTimer里置playID为-1，不会恢复定时播放的歌曲
                //20211228 注销掉音源限制，否则暂停状态下关闭设备再快速打开设备不会下发暂停状态
                //if (curSrc == PRO_PAGING)
                {
                    // 播放任务更新为播放状态
                    CSongUnit *pSongUnit = g_Global.m_PlayQueue.m_SongPlayer.GetSongUnitByPlayID(section.GetPlayID());
                    if (pSongUnit != NULL)
                    {
                        if( pSongUnit->GetPlayStatus() == SPS_PAUSE_AUTO )
                        {
                            m_SongPlayer.SetPlayStatus(section.GetPlayID(), SPS_PLAY);
                        }
                        else if(pSrcUnit->GetPlayStatus() == SPS_PAUSE_MANUAL)
                        {
                            g_Global.m_Network.m_CmdSend.CmdSetSingleDevicePlayStatus(PS_PAUSE, section);
                        }
                    }
                }
                
                CMyString strLog;
                strLog.Format("发送恢复播放命令 %s : source = %d, song name = %s，sample = %d, bit = %d, channel = %d, multicast ip = %s",
                        section.GetName(), pSrcUnit->GetSource(), strName.C_Str(),
                        songHeader.samp_freq, songHeader.bit_samp, songHeader.channels, pSrcUnit->GetPlayIP());
                g_Global.m_Network.AddLog(strLog);
                NOTIFY(strLog.C_Str());
            }
            else
            {
                printf("playID:%d is not valid!\n",section.GetPlayID());
                section.SetPlayID(-1);
                try_play_pre=1;
            }
        }
        // 定时结束后，如果之前有播放歌曲，则恢复播放
        if (  try_play_pre && section.GetPrePlayID() > 0 && g_Global.m_TimerScheme.GetTimePointsResumePlaying() )
        {
            CSongUnit *pSrcUnit = m_SongPlayer.GetSongUnitByPlayID(section.GetPrePlayID());
            bool IsPlayIDValid=m_SongPlayer.IsPlayIDValid(section.GetPrePlayID());

            if (pSrcUnit != NULL && IsPlayIDValid)
            {
                song_header_t	songHeader	= pSrcUnit->GetSongHeader();
                CMyString		strName		= GetNameByPathName(pSrcUnit->GetPathName(), TRUE);

                // 下发播放命令
                g_Global.m_Network.m_CmdSend.CmdNotifyStreamSource( section,
                                                                    pSrcUnit->GetSource(),
                                                                    pSrcUnit->GetSongFormat(),
                                                                    songHeader.samp_freq,
                                                                    (BYTE)songHeader.bit_samp,
                                                                    (BYTE)songHeader.channels,
                                                                    strName.C_Str(),
                                                                    pSrcUnit->GetPlayIP(),
                                                                    pSrcUnit->GetPlayPort(),
                                                                    pSrcUnit->GetVolume(),
                                                                    pSrcUnit->GetSongLength(),
                                                                    pSrcUnit->GetTotalFrames(),
                                                                    pSrcUnit->GetCurrentFrame(),
                                                                    pSrcUnit->GetSongMd5(),
                                                                    pSrcUnit->GetIsMulticastUseNewCmd(),
                                                                    0);


                // 播放任务更新为播放状态
                CSongUnit *pSongUnit = g_Global.m_PlayQueue.m_SongPlayer.GetSongUnitByPlayID(section.GetPrePlayID());
                if (pSongUnit != NULL)
                {
                    if( pSongUnit->GetPlayStatus() == SPS_PAUSE_AUTO )
                    {
                        m_SongPlayer.SetPlayStatus(section.GetPrePlayID(), SPS_PLAY);
                    }
                    else if( pSongUnit->GetPlayStatus() == SPS_PAUSE_MANUAL )
                    {
                        g_Global.m_Network.m_CmdSend.CmdSetSingleDevicePlayStatus(PS_PAUSE, section);
                    }
                }

                section.SetPlayID(section.GetPrePlayID());
                section.SetPrePlayID(-1);
            }
            else
            {
                printf("prePlayID:%d is not valid!\n",section.GetPrePlayID());
                section.SetPrePlayID(-1);
            }
            
        }
    }

    // 分区音源改变，处理监听
    if(section.IsListening())
    {
        if(curSrc == PRO_LOCAL_PLAY)
        {
            int nCount = section.GetListenCount();
            for(int i=0; i < nCount; i++)
            {
                string strMac = section.GetListenSec(i);
                LPCSection pListenSec = g_Global.m_Sections.GetSectionByMac(strMac.data());
                if(pListenSec != NULL && section.GetPlayID() == pListenSec->GetPlayID())
                {
                    g_Global.m_Network.m_CmdSend.CmdSetIdleStatus(*pListenSec);
                    pListenSec->SetPlayID(-1);
                }
            }
        }
        else if(newSrc == PRO_LOCAL_PLAY)
        {
            //m_SongPlayer.StartListenInSong(&section);
        }
    }

    // 当前是音频采集器音源，新的是定时歌曲播放
    if (CProtocol::IsAudioCollectorSrc(curSrc) && newSrc == PRO_TIMING)
    {
        //需要保存，以便定时歌曲完毕后恢复采集音源
        section.m_PlayedRecently.m_bSongTimerEndResumeAc = curSrc;
        //printf("kk1,curSrc=%d,newSrc=%d\n",curSrc,newSrc);
    }
    else if(!(curSrc == PRO_TIMING && (newSrc == PRO_IDLE || newSrc == PRO_ANALOG_INPUT)))    //其他情况重置，避免误恢复
    {
        //printf("kk2,curSrc=%d,newSrc=%d\n",curSrc,newSrc);
        section.m_PlayedRecently.m_bSongTimerEndResumeAc = PRO_IDLE;
    }
}


bool   CPlayQueue::PushPlayTask(CPlayTask& playTask)
{
    unsigned char	src			= playTask.GetSource();
    CMyString       strPathName = playTask.GetPathName();
    unsigned int*	pSecIndexs	= playTask.GetSecIndexs();
    unsigned int	uSecCount	= playTask.GetSecCount();

    int playMode = playTask.GetPlayMode();
    if(playMode == 0)
    {
        playMode=g_Global.m_PlayList.GetPlayMode();
        LPCUserInfo   user= g_Global.m_Users.GetUserByAccount(playTask.GetUserAccount());
        if(user!=NULL)
        {
            playMode = user->GetPlayMode();
        }
        playTask.SetPlayMode(playMode);
    }

    if (src == SOURCE_NEXT || src == SOURCE_TIMER || src == SOURCE_TIMER_STOP || src == SOURCE_PRE_MANUAL || src == SOURCE_NXT_MANUAL)
    {
        goto PUSH_TASK;
    }
    #if SUPPORT_REMOTE_CONTROLER
    if(src == SOURCE_REMOTE_PLAY)
    {
        goto PUSH_TASK;
    }
    #endif

    // 如果分区数量为0，则直接返回
    if (uSecCount == 0)
    {
        g_Global.m_Network.AddLog("Zone Count == 0");
        return TRUE;
    }

    if (src == SOURCE_STOP)
    {
        for (unsigned int i=0; i<uSecCount; ++i)
        {
            CSection& section = g_Global.m_Sections.GetSection(pSecIndexs[i]);

            // 如果分区正在被监听，而且正在播放歌曲，也停止监听设备
            if (section.IsListening() && section.GetPlayID() > 0)
            {
                //m_SongPlayer.StopListenInSong(section.GetPlayID());
                m_SongPlayer.StopListenInSong(&section);
            }
        }
    }

    // 如果不是停止命令，则判断歌曲文件有没有错误
    if (src != SOURCE_STOP && src != SOURCE_NULL && !g_Global.m_SongTool.GetSongInfo(strPathName).nDuration)
    {
        CMyString strLog;
        CMyString strError = LANG_STR(LANG_SECTION_PLAY_LIST, "Error Parsing File", ("解析文件出现错误"));
        strLog.Format(("PushPlayTask %s : %s"), strError.C_Str(), strPathName.C_Str());
        g_Global.m_Network.AddLog(strLog);

        return FALSE;
    }

PUSH_TASK:
    pthread_mutex_lock(&m_csPlayTasks);

#if 0   //这一段要去掉，否则如果两个相同的定时点，此处进来会导致段错误
    // 如果队列尾的播放任务和现在的任务一样，则删除掉，这样可以避免频繁点击播放会很久后才响应最后一个操作
    if (src == SOURCE_PLAY || src == SOURCE_STOP || src == SOURCE_REMOTE_PLAY || src == SOURCE_SPECIFIED_PLAY || src == SOURCE_SPECIFIED_ALARM)
    {
        if (m_PlayTaskQueue.size() > 0 && playTask == m_PlayTaskQueue.back())
        {
            m_PlayTaskQueue.pop_back();
        }
    }
#endif

    // 上面那句去掉是为了执行两个定时点（相同时间，不同分区，但playTask时的seccount都为0，所以判断为相同的任务）
    
    m_PlayTaskQueue.push(playTask);

    pthread_mutex_unlock(&m_csPlayTasks);

    return TRUE;
}



// 将播放任务从队头删除
void	CPlayQueue::PopPlayTask()
{
    // 要有数据才能pop，否则会出错
    pthread_mutex_lock(&m_csPlayTasks);
    if (m_PlayTaskQueue.size() > 0)
    {
        m_PlayTaskQueue.pop();
    }
    pthread_mutex_unlock(&m_csPlayTasks);
}


