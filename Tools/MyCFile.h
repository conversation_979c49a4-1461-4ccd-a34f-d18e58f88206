#ifndef CFILE_H
#define CFILE_H

#include <iostream>
#include <stdio.h>
#include <iomanip>
#include <fstream>
#include <QFile>

using namespace std;

// 一般使用以下的方式打开文件

// "rb"  以二进制方式读取
// "wb+" 读写打开或建立一个二进制文件，允许读和写,长度清0
// "a"   读写，添加到文件末尾，保留EOF符

class MyCFile
{
public:
    MyCFile();
    ~MyCFile();

    bool    IsValid(void) { return qfile->isOpen(); }
    int     GetLength(void) { return qfile->size(); }

    bool    Open(const char* szPathName, const char* mode);
    void    Close(void);

    int     Read(void* lpBuf, unsigned int count);
    int     Write(const void* lpBuf, unsigned int count);

    bool    SeekToBegin();
    bool    SeekToEnd();
    int     Seek(int nOffset, int seekPos);

private:
    int     GetFileSize(const char* fileName);

private:
    QFile    *qfile;

};






#endif // CFILE_H
