#include "xxtea.h"
#include <vector>
#include <string.h>

#define DELTA 0x9E3779B9
#define MX (((z>>5^y<<2) + (y>>3^z<<4)) ^ ((sum^y) + (key[(p&3)^e]^z)))

static void btea(uint32_t *v, int n, const uint32_t *key) {
    uint32_t y, z, sum;
    unsigned p, rounds, e;
    if (n > 1) {
        rounds = 6 + 52 / n;
        sum = 0;
        z = v[n - 1];
        do {
            sum += DELTA;
            e = (sum >> 2) & 3;
            for (p = 0; p < n - 1; ++p) {
                y = v[p + 1];
                z = v[p] += MX;
            }
            y = v[0];
            z = v[n - 1] += MX;
        } while (--rounds);
    } else if (n < -1) {
        n = -n;
        rounds = 6 + 52 / n;
        sum = rounds * DELTA;
        y = v[0];
        do {
            e = (sum >> 2) & 3;
            for (p = n - 1; p > 0; --p) {
                z = v[p - 1];
                y = v[p] -= MX;
            }
            z = v[n - 1];
            y = v[0] -= MX;
            sum -= DELTA;
        } while (--rounds);
    }
}

static std::string to_bytes(const uint32_t *src, size_t len) {
    std::string s(len * 4, '\0');
    memcpy(&s[0], src, len * 4);
    return s;
}

static std::vector<uint32_t> to_words(const std::string &s) {
    std::vector<uint32_t> v((s.size() + 3) >> 2);
    memcpy(v.data(), s.data(), s.size());
    return v;
}

std::string xxtea_encrypt(const std::string &text, const std::string &key) {
    if (text.empty()) return text;
    auto v = to_words(text);
    auto k = to_words(key);
    btea(v.data(), v.size(), k.data());
    return to_bytes(v.data(), v.size());
}

std::string xxtea_decrypt(const std::string &cipher, const std::string &key) {
    if (cipher.empty()) return cipher;
    auto v = to_words(cipher);
    if (v.empty()) return "";
    auto k = to_words(key);
    btea(v.data(), -v.size(), k.data());
    return to_bytes(v.data(), v.size());
}