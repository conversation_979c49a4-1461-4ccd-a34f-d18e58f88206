#ifndef LANGUAGE_H
#define LANGUAGE_H

#include "Tools/CMyString.h"
#include "Ini.h"
#include <iostream>
#include <vector>

#define LANG_SECTION_MAIN_MENU		("Main Menu")
#define LANG_SECTION_PLAY_LIST		("Playlist")
#define LANG_SECTION_ZONE_GROUP		("Zone&Group")
#define LANG_SECTION_CONTROL_PANE	("Control Panel")
#define LANG_SECTION_DIALOG			("Dialog")
#define LANG_SECTION_TIMING			("Timing")
#define LANG_SECTION_SIP			("SIP")
#define LANG_SECTION_PAGE           ("Page")


class CLanguage
{
public:
    CLanguage(void);
    ~CLanguage(void);

public:
    // 初始化
    void		InitLanguage(CMyString strPath);

    // 语言数目
    int			GetLanguageCount(void);

    // 指定语言名称
    CMyString		GetLanguageName(int index);

    // 获取当前选中的语言
    int			GetCheckedLanguage();

    // 是否为简体中文
    bool		IsSimpleChinese();

    // 设置当前选中的语言
    void		SetCheckedLanguage(CMyString strLangName);
    void		SetCheckedLanguage(int index);

    // 获取字符串
    CMyString&  GetString(const char* szSection, const char* szItem, CMyString strDefault = NULL);

private:
    void		LoadLanguageFile(CMyString strLangName);

private:
    CMyString			m_strPath;			// Language文件夹路径
    vector<CMyString>	m_languageNames;	// 语言名称列表
    CIni				m_languageFile;		// 选中语言INI文件
    CMyString			m_strCheckedLang;	// 选中的语言名称

    CMyString           m_strItem;
};

#endif // LANGUAGE_H
