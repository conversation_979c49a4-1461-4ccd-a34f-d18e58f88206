#ifndef UKEY_H
#define UKEY_H
#include <QtCore/qglobal.h>
#if defined(Q_OS_WIN32)
#include "windef.h"
typedef struct _SCSI_PASS_THROUGH_DIRECT {
    USHORT Length;
    UCHAR ScsiStatus;
    UCHA<PERSON> PathId;
    UCHAR TargetId;
    UCHA<PERSON> Lun;
    UCHA<PERSON> CdbLength;
    UCHAR SenseInfoLength;
    UCHAR DataIn;
    ULONG DataTransferLength;
    ULONG TimeOutValue;
    PVOID DataBuffer;
    ULONG SenseInfoOffset;
    UCHAR Cdb[16];
}SCSI_PASS_THROUGH_DIRECT, *PSCSI_PASS_THROUGH_DIRECT;

#define FILE_DEVICE_CONTROLLER          0x00000004

#define SCSI_IOCTL_DATA_OUT          0
#define SCSI_IOCTL_DATA_IN           1
#define SCSI_IOCTL_DATA_UNSPECIFIED  2

#define IOCTL_SCSI_BASE                 FILE_DEVICE_CONTROLLER
#define METHOD_BUFFERED                 0
#define METHOD_IN_DIRECT                1
#define METHOD_OUT_DIRECT               2
#define METHOD_NEITHER                  3

#define FILE_ANY_ACCESS                 0
#define FILE_SPECIAL_ACCESS    (FILE_ANY_ACCESS)
//#define FILE_READ_ACCESS          ( 0x0001 )    // file & pipe
//#define FILE_WRITE_ACCESS         ( 0x0002 )    // file & pipe

//#define CTL_CODE( DeviceType, Function, Method, Access ) (                 \
    ((DeviceType) << 16) | ((Access) << 14) | ((Function) << 2) | (Method) \
)
//typedef WCHAR OLECHAR;



#define IOCTL_SCSI_PASS_THROUGH         CTL_CODE(IOCTL_SCSI_BASE, 0x0401, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_SCSI_MINIPORT             CTL_CODE(IOCTL_SCSI_BASE, 0x0402, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_SCSI_GET_INQUIRY_DATA     CTL_CODE(IOCTL_SCSI_BASE, 0x0403, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_SCSI_GET_CAPABILITIES     CTL_CODE(IOCTL_SCSI_BASE, 0x0404, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_SCSI_PASS_THROUGH_DIRECT  CTL_CODE(IOCTL_SCSI_BASE, 0x0405, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_SCSI_GET_ADDRESS          CTL_CODE(IOCTL_SCSI_BASE, 0x0406, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_SCSI_RESCAN_BUS           CTL_CODE(IOCTL_SCSI_BASE, 0x0407, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_SCSI_GET_DUMP_POINTERS    CTL_CODE(IOCTL_SCSI_BASE, 0x0408, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_SCSI_FREE_DUMP_POINTERS   CTL_CODE(IOCTL_SCSI_BASE, 0x0409, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_IDE_PASS_THROUGH          CTL_CODE(IOCTL_SCSI_BASE, 0x040a, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)

typedef struct _SCSI_PASS_THROUGH_DIRECT_WITH_BUFFER {
    SCSI_PASS_THROUGH_DIRECT sptd;
    ULONG             Filler;      // realign buffer to double word boundary
    UCHAR             ucSenseBuf[32];
    } SCSI_PASS_THROUGH_DIRECT_WITH_BUFFER, *PSCSI_PASS_THROUGH_DIRECT_WITH_BUFFER;

#endif

#define DATATOOLEN	-3
#define ADDRERROR -4
#define USER_WRITE_PWD_ERROR -5
#define READ_PWD_ERROR -6
#define WRITE_PWD_ERROR -7
#define PinNotOpen -8
#define USER_READ_PWD_ERROR -9
#define NoASPI       -901
#define CANTLOADLIB  -902 
#define  CANTGETFUNC  -903

#define CANOPENEVENT  -905

#define DATAISNULL    -909
#define ALLOCMEMORYERROR -3000
#define ISNULL         -911

#define DISK_INF_LEN 0X24
#define SHORTTIMEOUT  1L*100L
#define SCSI_MSPGCD_RETALL 0x3f
#define DISK_SENSE_LEN 12

const unsigned char DISK_INF[DISK_INF_LEN]=                                      //磁盘信息
{
  0, 0x80, 
 0x00, 0x01, 0x1f, 0x00, 0x00, 0x00, 'i','K', 'e', 'y', '.', 0, 0, 0,
 'F','2', 'k', 'K', 'e', 'y', 'C', 'o', '.', ',', 0, 0, 0, 0, 0, 0,                                       
 '8', 0x2e, 0x30, 0x30
};


#endif // UKEY_H
