#ifndef SOFTDOG_H
#define SOFTDOG_H

#if defined(Q_OS_LINUX)
#include "D8.h"
#else
#include "D8_win32.h"
#include <dbt.h>
#endif
#include "Tools/CTime.h"

#define USB_HKEY_STR		"AAAAAAAA"			// 加密狗管理员登录密码
#define USB_LKEY_STR		"BBBBBBBB"			// 加密狗管理员登录密码

#define USB_SOFT_KEY		"MHISOFTDOG"
#define USB_SOFT_VERF		"1C41047D7496EFCF"

class CSoftdog
{
public:
	CSoftdog(void);
	~CSoftdog(void);

public:
	BOOL	Init();	 // 初始化，返回加密狗数量
	BOOL	StartWorking();
	static void *WorkThread(void *lpParam);
	BOOL	StopWorking();
	BOOL	HasFinded(void);

	BOOL	WriteSoftID(LPSTR szSoftID);
	BOOL	ReadSoftID(LPSTR szSoftID);

	BOOL 	CheckDate(char *KeyPath);

	void 	StatusChange();

	CTime	GetLimitDate(){	return m_LimitDate;}

#if defined(Q_OS_WIN32)
    int DoRegisterDeviceInterface();
#endif

private:
	void	ResetData(void);

public:
	BOOL	m_isWorking;
	HANDLE	m_hWorkThread;
	BOOL	m_bFinded;

	D8 		m_d8;
	int 	m_nTimeout;
	char    strDevicePath[MAX_PATH];

	CTime   m_LimitDate;

#if defined(Q_OS_WIN32)
	HDEVNOTIFY hDevNotify;
#endif
};


#endif

