#ifndef D8_H
#define D8_H
#include "Network/HPSocket/GlobalDef.h"
#include "libusb.h"
#include <semaphore.h>
#include "KeyDef.h"

#define PID 0X8762
#define VID 0X3689
#define PID_NEW 0X2020
#define VID_NEW 0X3689
#define PID_NEW_2 0X2020
#define VID_NEW_2 0X2020

#if 0
#define BYTE unsigned char
#define DWORD unsigned int
#define USHORT  unsigned short
#define UInt16 unsigned short
#define UINT16 unsigned short
#define INT16  short
#define Int16  short
#define INT32 int
#define UINT32 unsigned int
#define UINT8 unsigned char

#define MAX_PATH 260
#endif
#define BOOL bool

#define PERMS (mode_t)(S_IRUSR | S_IWUSR | S_IRGRP | S_IROTH)
#define FLAGS (O_CREAT)


class D8
{
public:
    D8();
    virtual ~D8();
    //
    int NT_GetIDVersion(int *Version,char *InPath);
    //
    int NT_GetVersionEx(int *Version,char *InPath);

    //
    int sWrite_2Ex_New(  DWORD in_data,DWORD *out_data,char *Path);
    int sWriteEx_New(  DWORD in_data,DWORD *out_data,char *Path);
    int sWrite_2Ex(  DWORD in_data,DWORD *out_data,char *Path);
    int sWriteEx(  DWORD in_data,DWORD *out_data,char *Path);
    int sRead(  DWORD *out_data,char *Path);
    int sWrite(  DWORD in_data,char *Path);
    int sWrite_2(  DWORD in_data,char *Path);

    //
    int YWrite(BYTE InData,short address,char * HKey,char *LKey,char *Path );
    //
    int YRead(BYTE *OutData,short address,char * HKey,char *LKey,char *Path );
    //
    int YWriteEx(BYTE *InData,short address,short len,char *HKey,char *LKey,char *Path );
    //
    int YReadEx(BYTE *OutData,short address,short len,char *HKey,char *LKey,char *Path );

    //
        int FindPort_3(  int start,DWORD in_data,DWORD verf_data,char *OutPath);
    //
    int FindPort_2(  int start,DWORD in_data,DWORD verf_data,char *OutPath);
    //
    int FindPort(  int start,char *OutPath);

    //
    int GetID(  DWORD *ID_1,DWORD *ID_2,char *InPath);

    //
    int YReadString(char *string ,short Address,int len,char * HKey,char *LKey,char *Path );
    //
    int YWriteString(char *InString,short Address,char * HKey,char *LKey,char *Path );
    //
    int SetWritePassword(char * W_HKey,char *W_LKey,char * new_HKey,char *new_LKey,char *InPath);
    //
    int SetReadPassword(char * W_HKey,char *W_LKey,char * new_HKey,char *new_LKey,char *InPath);

    //
    int SetCal_2(char *Key,char *InPath);
    //
    int EncString(  char *InString,char *OutString,char *Path);
    //
    int Cal(  BYTE *InBuf,BYTE *OutBuf,char *Path);

    //
    int SetCal_New(char *Key,char *InPath);
    //
    int Cal_New(  BYTE *InBuf,BYTE *OutBuf,char *Path);
    //
    int EncString_New(  char *InString,char *OutString,char *Path);

    //
    void StrDec(  char *InString,char *OutString,char *Key);
    void StrEnc(  char *InString,char *OutString,char *Key);
    void EnCode(   BYTE  * InData,BYTE  * OutData,  char *Key  );
    void DeCode(   BYTE  * InData, BYTE  * OutData, char *Key  );
    //
    void  DecBySoft(    BYTE  *   aData,   BYTE   *   aKey   )  ;
    void  EncBySoft(   BYTE  *   aData,  BYTE   *   aKey   )   ;

    //
     void HexStringToByteArray(char * InString,BYTE *out_data);
     void ByteArrayToHexString(BYTE *in_data,char * OutString,int len);

     //
    int ReSet(char *InPath);
   //
    int GetProduceDate(  char *OutDate,char *InPath);
    //
    int SetID(char * Seed,char *InPath);
    //
    int SetCal(char * W_HKey,char *W_LKey,char * new_HKey,char *new_LKey,char *InPath);
    void SnToProduceDate(char* InSn,char *OutProduceDate);
    void DecString(  char *InString,char *OutString,char *Key);

    //
    //
    int SetHidOnly(  BOOL IsHidOnly,char *InPath);
    //
    int SetUReadOnly( char *InPath);
    //
    int IsUReadOnly(BOOL *IsReadOnly,char *InPath);

    //
    int FindU_2(  int start,DWORD in_data,DWORD verf_data,char *OutPath);
    //
    int FindU(  int start,char *OutPath);

    //
    //
    int YT_GenKeyPair(char* PriKey,char *PubKeyX,char *PubKeyY,char *InPath);
    //
    int YtSetPin(char *old_pin,char *new_pin,char *InPath );
    //
    int Set_SM2_KeyPair(char *PriKey,char *PubKeyX,char *PubKeyY,char *sm2UserName,char *InPath );
    //
    int Get_SM2_PubKey(char *PubKeyX,char *PubKeyY,char *sm2UserName,char *InPath);
    //
    int SM2_EncBuf(BYTE *InBuf,BYTE *OutBuf,int inlen,char *InPath);
    //
    int SM2_DecBuf(BYTE *InBuf,BYTE *OutBuf,int inlen,char* pin,char *InPath);
    //
    int SM2_EncString(char *InString,char *OutString,char *InPath);
    //
    int SM2_DecString(char *InString,char *OutString,char* pin,char *InPath);
    //
    int GetChipID( char *OutChipID,char *InPath);
    void   GetZ(char *id,char *ecc_kx,char *ecc_ky,BYTE *OutBuf );
 void   GetE(BYTE *Z,BYTE  *Hashmsg,BYTE *OutBuf );
void   GetHashMsgValue(char *msg,BYTE *OutBuf );
int   YtVerfiy(char *id,char *msg,char *PubKeyX,char *PubKeyY,char *VerfiySign,BOOL *IsVailSign,char *InPath );
int   YtSign(char *msg,char *OutSign,char* pin,char *InPath );
int   YtSign_2(char *msg,char *OutSign,char* pin,char *InPath );


	int FindD8(int pos,char *VerfCode,char *KeyPath);
	int GetFuncVer(int *Version,char *InPath);

	//
	int  DownLoadBinFile(BOOL bIsEnc,char *BinFile,char *InPath);
	int  DownLoadData(BOOL bIsEnc,BYTE* Buf,DWORD BufLen,char *InPath);
    int  EncBinFile(char *BinFile,char *OutBinFile,char *Key);
	//
	int RunFuntion(char* FunctionName,char *Path );
	int ContinuRun(char *Path);

	//
	int  SetVar(BYTE *Buf,DWORD MemBeginPos,DWORD BufLen,char *InPath);
	//
	int  GetVar(BYTE *OutBuf,DWORD MemBeginPos,DWORD OutBufLen,char *InPath);

	//
	int  SetDownLodKey(char *OldKey,char* NewKey,char *OutVerfCode,char *InPath);
	int  OpenKey(char *VerfCode,char *InPath);//
	int  CloseKey(char *InPath);//


	//
	int  GetApiParam(BYTE *OutBuf,char *InPath);//
	int  SetApiParam(BYTE *Buf,DWORD InLen,char *InPath);//

	//
	int WriteEprom(BYTE *InBuf,DWORD Addr,DWORD len,char *HKey,char *LKey,char *InPath);
	int ReadEprom(BYTE *OutBuf,DWORD Addr,DWORD len,char *HKey,char *LKey,char *InPath);
	int NewSetReadPassword(char *OldWriteHKey,char *OldWriteLKey,char *NewHKey,char *NewLKey,char *InPath);
	int NewSetWritePassword(char *OldWriteHKey,char *OldWriteLKey,char *NewHKey,char *NewLKey,char *InPath);

	//
	int GetLimitDate(int *Year,BYTE *Month,BYTE *Day,char *InPath);
	int GetUserID(UINT16 *UserID,char *InPath);
	int GetLeaveNumber(UINT16 *LeaveNumber,char *InPath);
	int GetLeaveDays(INT32 *LeaveDay,char *CurDate,char *InPath);
	int CheckBind(BOOL bIsAdd,char *MacAddr,char *InPath);
	int CheckNumber(char *InPath);
	int CheckDate(char *InDate,char *InPath);
	int UpdateAuth(BYTE Flag,char *Auth,char *InPath);
	int DateAuth(UINT16 CurYear,BYTE CurMonth,BYTE CurDay,
		UINT16 LimitYear,BYTE LimitMonth,BYTE LimitDay,UINT16 UserID,
		char *Key,char *InPath);
	int NumberAuth(UINT16 Number,UINT16 UserID,char *Key,char *InPath);
	int BindAuth(BOOL bReBind,UINT8 BindCount,UINT16 UserID,char *Key,char *InPath);
	int GetBindInfo(BYTE *LimitBindCount,BYTE *AlreadyBindCount,char *InPath);

	void MakeBindAuth(BOOL bReBind,UINT8 BindCount,UINT16 UserID,char *Key,char *OutAuth);
	void MakeNumberAuth(UINT16 Number,UINT16 UserID,char *Key,char *OutAuth);
	void MakeDateAuth(INT16 CurYear,BYTE CurMonth,BYTE CurDay,
		UINT16 LimitYear,BYTE LimitMonth,BYTE LimitDay,UINT16 UserID,
		char *Key,char *OutAuth);

	int  ClearDyData(UINT32 DyMemBeginPos,char *InPath);
	int  SetDyString(UINT32 MemAddr,char *InString,char *InPath);
	int  SetDyArr(UINT32 MemAddr,BYTE *InBuf,D8_USHORT InLen,D8_USHORT TypeSize,char *InPath);
	int  SetDyArrString(UINT32 MemAddr,char *ArrStr[],D8_USHORT ArrLen,char *InPath);
	int  GetDyArrString(UINT32 MemAddr,char *OutString[],char *InPath);
	int  GetDyArr(UINT32 MemAddr,BYTE **OutBuf,char *InPath);
	int  GetDyString(UINT32 MemAddr,char **OutString,char *InPath);

    void CloseUsbHandle(char *DevicePath );
public:
     BOOL IsLibUsbInit;
     BOOL IsLoad;
	 static char D8_RUNSEM[50];
	 static int D8_RUNOVERTIME;
private:
    int NT_GetVersion(int *Version,char *InPath);
    int NT_Read(  BYTE * ele1,BYTE * ele2,BYTE * ele3,BYTE * ele4,char *Path);
    int NT_Write(  BYTE * ele1,BYTE * ele2,BYTE * ele3,BYTE * ele4,char *Path);
    int NT_Write_New(  BYTE * ele1,BYTE * ele2,BYTE * ele3,BYTE * ele4,char *InPath);
    int NT_Write_2_New(  BYTE * ele1,BYTE * ele2,BYTE * ele3,BYTE * ele4,char *InPath);
    int ReadDword(  DWORD *in_data,char *Path);
    int WriteDword(  DWORD *out_data,char *Path);
    int Y_Write(BYTE *InData,short address,short len,BYTE *password,char *Path );
    int Y_Read(BYTE *OutData,short address ,short len,BYTE *password,char *Path );
    int NT_FindPort_3(  int start,DWORD in_data,DWORD verf_data,char *OutPath);
    int NT_FindPort_2(  int start,DWORD in_data,DWORD verf_data,char *OutPath);
    int NT_FindPort(  int start,char *OutPath);
    int NT_GetID(  DWORD *ID_1,DWORD *ID_2,char *InPath);
    int NT_Write_2(  BYTE * ele1,BYTE * ele2,BYTE * ele3,BYTE * ele4,char *Path);
    int WriteDword_2(  DWORD *out_data,char *Path);
    int WriteDword_New(  DWORD *in_data,char *Path);
    int WriteDword_2_New(  DWORD *in_data,char *Path);
    int NT_SetCal_2(BYTE *InData,BYTE IsHi,char *Path );
    int NT_Cal(  BYTE * InBuf,BYTE *OutBuf,char *InPath);
    int NT_ReSet(char *Path );
    int isfindmydevice( int pos ,char *OutPath);
    int  F_GetVerEx(int *Version,char *InPath);
    int NT_Cal_New(  BYTE * InBuf,BYTE *OutBuf,char *InPath);
    int NT_SetCal_New(BYTE *InData,BYTE IsHi,char *Path );
    int NT_Set_SM2_KeyPair(BYTE *PriKey,BYTE *PubKeyX,BYTE *PubKeyY,char *sm2UserName,char *Path );
    int NT_Get_SM2_PubKey(BYTE *KGx,BYTE *KGy,char *sm2UserName,char *Path );
    int NT_GenKeyPair(BYTE* PriKey,BYTE *PubKey,char *Path );
    int NT_Set_Pin(char *old_pin,char *new_pin,char *Path );
    int NT_SM2_Enc(BYTE *inbuf,BYTE *outbuf,BYTE inlen,char *Path );
    int NT_SM2_Dec(BYTE *inbuf,BYTE *outbuf,BYTE inlen,char* pin,char *Path );
    int NT_Sign(BYTE *inbuf,BYTE *outbuf,char* pin,char *Path );
    int NT_Sign_2(BYTE *inbuf,BYTE *outbuf,char* pin,char *Path );
    int NT_Verfiy(BYTE *inbuf,BYTE *InSignBuf,BOOL *outbiao,char *Path );
    int  NT_GetChipID(  BYTE *OutChipID,char *InPath);
    int Sub_SetOnly(BOOL IsOnly,BYTE Flag,char *InPath);
    int NT_SetHidOnly(  BOOL IsHidOnly,char *InPath);
    int  NT_SetUReadOnly(char *InPath);
    int NT_SetID(  BYTE * InBuf,char *InPath);
    int Read(BYTE *OutData,short address,BYTE *password,char *Path );
    int Write(BYTE InData,short address,BYTE *password,char *Path );
    int NT_GetProduceDate(  BYTE *OutDate,char *InPath);
    int GetTrashBufLen(char * Path,int *OutLen);
    void myconvert(char *hkey,char *lkey,BYTE *out_data);
    DWORD HexToInt(char* s);
    void HexStringToByteArrayEx(char * InString,BYTE *in_data);
    int GetLen(char *InString);
    void SwitchByte2Char(char *outstring,BYTE *inbyte,int inlen);
    void SwitchChar2Byte(char *instring,BYTE *outbyte);
    void SwitchBigInteger2Byte(char *instring,BYTE *outbyte,int *outlen);
    int sFindPort(short pos,char *OutPath);
    int sSetHidOnly(BOOL IsHidOnly,char *KeyPath);
    int ReadVerfData(char *InPath);
    int NT_FindU_3(  int start,DWORD in_data,DWORD verf_data,char *OutPath);
    int NT_FindU_2(  int start,DWORD in_data,DWORD verf_data,char *OutPath );
    int NT_IsUReadOnly(BOOL *IsReadOnly,char *InPath);
    int Y_SetCal(BYTE *InData,short address,short len,BYTE *password,char *Path );
	//
	int HanldetransferEx(BYTE *InBuf,int InBufLen,BYTE *OutBuf,int OutBufLen,char *Path);
	int NT_DownLoadData(BYTE* Buf,BYTE BufLen,char *Path );
	int NT_StartDownLoad(BOOL bIsEnc,char *Path );
	int NT_RunFun(char* FunctionName,DWORD FunNameLen,char *Path );
	int NT_ContinuRun(char *Path);
	int NT_SetDownLodKey(char* OldKey,char* NewKey,char *OutVerfCode,char *Path );
	int NT_OpenKey(char *VerfCode,char *Path );
	int NT_GetVar(BYTE* OutBuf,DWORD MemBeginPos,BYTE OutBufLen,char *Path );
	int NT_SetVar(BYTE* Buf,DWORD MemBeginPos,BYTE BufLen,char *Path );
	int NT_GetApiParam(BYTE* OutBuf,BYTE *OutLen,char *Path );
	int NT_SetApiParam(BYTE* Buf,BYTE BufLen,char *Path );
	int NT_WriteEprom(BYTE *InBuf,DWORD Addr,BYTE len,char *HKey,char *LKey,char *Path);
	int NT_ReadEprom(BYTE* OutBuf,DWORD Addr,BYTE len,char *HKey,char *LKey,char *Path );
	void CovertPwd8(char *Pwd,char *Pwd8);
	int NT_SetPwd_28K(BOOL IsSetWritePwd,char *OldWriteHKey,char *OldWriteLKey,char *NewHKey,char *NewLKey,char *Path );
	int Sub_SetPwd_28K(BOOL IsSetWritePwd,char *OldWriteHKey,char *OldWriteLKey,char *NewHKey,char *NewLKey,char *InPath);
	int NT_GetFunctionVersion(int *FunctionVersion,char *InPath);
	int GetBufNoCarry(BYTE Cmd,BYTE *OutData,int len,char *InPath);
	int GetBufCarryData(BYTE Cmd,BYTE *Inbuf,int Inlen,BYTE *OutData,int Outlen,char *InPath);
	int SubAuth(BYTE Cmd,BYTE *Buf, char *Key,char *InPath);
	void CopyKeyToBuf(BYTE *KeyBuf,char *Key);
	void SubMakeBindAuth(BOOL bReBind,UINT8 BindCount,UINT16 UserID,BYTE *OutBuf);
	void SubMakeNumberAuth(UINT16 Number,UINT16 UserID,BYTE *OutBuf);
	void SubMakeAuth(BYTE *Buf,char *Key,char *OutAuth);
	void SubMakeDateAuth(UINT16 CurYear,BYTE CurMonth,BYTE CurDay,
    UINT16 LimitYear,BYTE LimitMonth,BYTE LimitDay,UINT16 UserID,BYTE *OutBuf);
    int Hanldetransfer(char *DevicePath,BYTE *array_in,int InLen,BYTE *array_out,int OutLen,int Report_size);
    int code_convert(const char *from_charset, const char *to_charset, const char *inbuf, size_t inlen, char *outbuf, size_t *outlen);
    int u2g(const char *inbuf,size_t inlen,char *outbuf,size_t *outlen);
    int g2u(const char *inbuf,size_t inlen,char *outbuf,size_t *outlen);
    int MyOpenPath(char *InPath,libusb_device_handle **DevHandle);
	int sem_timedwait_millsecs(sem_t *sem, long msecs);

	int  SubSetDyDataValueByMemAddr(UINT32 MemAddr,D8_SHORT index,BYTE *InBuf,D8_USHORT DataSize,char *InPat);
	int  SubGetDyDataValueByMemAddr(UINT32 MemAddr,D8_SHORT index,D8_USHORT pos,BYTE *OutBuf,D8_USHORT DataSize,char *InPath);
	int  GetDyDataSize(UINT32 MemAddr,D8_SHORT index,D8_USHORT *OutSize,char *InPath);
	int  GetDyValue(UINT32 MemAddr,D8_SHORT index,BYTE *OutBuf,D8_USHORT DataSize,char *InPath);
	int  SetDyValue(UINT32 MemAddr,D8_SHORT index,BYTE *InBuf,D8_USHORT DataSize,char *InPath);


};

#endif // D8_H
