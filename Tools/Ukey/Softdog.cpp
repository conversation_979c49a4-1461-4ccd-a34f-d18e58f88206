#include "stdafx.h"
#include "Softdog.h"
#include "Tools/Control/CRegister.h"


int libusb_event=-1;

#if defined(Q_OS_LINUX)
libusb_device_handle *handle = NULL;

static int LIBUSB_CALL hotplug_callback(libusb_context *ctx, libusb_device *dev, libusb_hotplug_event event, void *user_data)
{
	struct libusb_device_descriptor desc;
	int rc;
 
	(void)ctx;
	(void)dev;
	(void)event;
	(void)user_data;
 
	rc = libusb_get_device_descriptor(dev, &desc);
	if (LIBUSB_SUCCESS != rc) {
		fprintf (stderr, "Error getting device descriptor\n");
	}
 
	printf ("Device attached: %04x:%04x\n", desc.idVendor, desc.idProduct);
	
	if(((desc.idProduct==PID) && (desc.idVendor==VID)) ||
			((desc.idProduct==PID_NEW) && (desc.idVendor==VID_NEW)) ||
			((desc.idProduct==PID_NEW_2) && (desc.idVendor==VID_NEW_2)))
	{
		libusb_event=1;
	}
	
	return 0;
}
 
static int LIBUSB_CALL hotplug_callback_detach(libusb_context *ctx, libusb_device *dev, libusb_hotplug_event event, void *user_data)
{
	(void)ctx;
	(void)dev;
	(void)event;
	(void)user_data;
 
	printf ("Device detached\n");

	libusb_event=0;

	return 0;
}
#endif


CSoftdog::CSoftdog(void)
{
	ResetData();
}

void	CSoftdog::ResetData(void)
{
	m_isWorking		= FALSE;
	m_bFinded		= FALSE;
	m_nTimeout		= 0;
}


CSoftdog::~CSoftdog(void)
{
	StopWorking();
}

// 初始化，返回是否找到加密狗
BOOL	CSoftdog::Init()
{
	char DevicePath[MAX_PATH-1];//储存加密锁的设备路径。
	BOOL IsDateOK=false;
	//要找到对应发行的锁，请使用FindD8函数，在生成的RunFunc代码中就有这个代码
	//这个用于判断系统中是否存在着加密锁。不需要是指定的加密锁,
	if (m_d8.FindD8(0,(char *)USB_SOFT_VERF,DevicePath) == 0)
	{
		IsDateOK=CheckDate(DevicePath);
		if(m_bFinded == FALSE)
		{
			m_bFinded = TRUE;
			m_nTimeout = 0;
			printf("softDog:Found...\n");
		}
	}
	else
	{
		printf("softDog:Not Found...\n");
	}

#if defined(Q_OS_LINUX)
	//Init HotPlug
	libusb_hotplug_callback_handle hp[2];
	int product_id, vendor_id, class_id;
	int rc;
 
	vendor_id  = LIBUSB_HOTPLUG_MATCH_ANY;
	product_id = LIBUSB_HOTPLUG_MATCH_ANY;
	class_id   = LIBUSB_HOTPLUG_MATCH_ANY;
	
	libusb_event = -1;

	bool IsInit_succeed=TRUE;
	rc = libusb_init (NULL);
	if (rc < 0)
	{
		printf("failed to initialise libusb: %s\n", libusb_error_name(rc));
		IsInit_succeed=FALSE;
	}
 
	if (!libusb_has_capability (LIBUSB_CAP_HAS_HOTPLUG)) {
		printf ("Hotplug capabilites are not supported on this platform\n");
		libusb_exit (NULL);
		IsInit_succeed=FALSE;
	}
 
	rc = libusb_hotplug_register_callback (NULL, LIBUSB_HOTPLUG_EVENT_DEVICE_ARRIVED, 0, vendor_id,
		product_id, class_id, hotplug_callback, NULL, &hp[0]);
	if (LIBUSB_SUCCESS != rc) {
		fprintf (stderr, "Error registering callback 0\n");
		libusb_exit (NULL);
		IsInit_succeed=FALSE;
	}
 
	rc = libusb_hotplug_register_callback (NULL, LIBUSB_HOTPLUG_EVENT_DEVICE_LEFT, 0, vendor_id,
		product_id,class_id, hotplug_callback_detach, NULL, &hp[1]);
	if (LIBUSB_SUCCESS != rc) {
		fprintf (stderr, "Error registering callback 1\n");
		libusb_exit (NULL);
		IsInit_succeed=FALSE;
	}

	if(IsInit_succeed)
#endif
	{
		StartWorking();
	}
	if(m_bFinded)
	{
		if(IsDateOK)
		{
			g_Global.m_AppType = APP_SOFTDOG_VALID;
		}
		else
		{
			g_Global.m_AppType = APP_SOFTDOG_DATE_INVALID;
		}
	}
	return m_bFinded;
}

BOOL	CSoftdog::StartWorking()
{
	if (m_isWorking)
	{
		return FALSE;
	}
	
	m_isWorking = TRUE;

	pthread_t pid;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&pid, &attr, WorkThread, (void*)this);
    pthread_attr_destroy(&attr);

	return TRUE;
}


void *CSoftdog::WorkThread(void *lpParam)
{
	CSoftdog*  pSoftdog	= (CSoftdog *)lpParam;

	char DevicePath[MAX_PATH-1]={0};//储存加密锁的设备路径。
#if defined(Q_OS_LINUX)
	while (pSoftdog->m_isWorking) {
		int rc = libusb_handle_events (NULL);
		if (rc < 0)
			printf("libusb_handle_events() failed: %s\n", libusb_error_name(rc));

		if(libusb_event == 1)	//插入
		{
			libusb_event=-1;
			usleep(50000);
			if (g_Global.m_softDog.m_d8.FindD8(0,(char *)USB_SOFT_VERF,DevicePath) == 0)
			{
				BOOL IsDateOK=g_Global.m_softDog.CheckDate(DevicePath);
				//if(!g_Global.m_softDog.HasFinded() && IsDateOK)
				//20230817 新版本，即使日期超出，也要重新启动，便于区分状态
				if(!g_Global.m_softDog.HasFinded())
				{
					printf("softDog:ok...\n");
					g_Global.m_softDog.m_bFinded = TRUE;
					g_Global.m_softDog.m_nTimeout = 0;
					g_Global.m_softDog.StatusChange();
				}
			}
		}
		else if(libusb_event == 0)	//拔出
		{
			libusb_event=-1;
			usleep(50000);
			if (g_Global.m_softDog.m_d8.FindD8(0,(char *)USB_SOFT_VERF,DevicePath) != 0)
			{
				printf("softDog:unplug...\n");
				if(g_Global.m_softDog.HasFinded())
				{
					g_Global.m_softDog.m_bFinded = FALSE;
					g_Global.m_softDog.m_nTimeout = 0;
					g_Global.m_softDog.StatusChange();
				}
			}
		}
	}

#else

	while(pSoftdog->m_isWorking)
	{
        if (pSoftdog->m_d8.FindD8(0,(char *)USB_SOFT_VERF,DevicePath) == 0)
		{
			if(pSoftdog->m_bFinded == FALSE)
			{
				pSoftdog->m_bFinded = TRUE;
				pSoftdog->m_nTimeout = 0;

				pSoftdog->StatusChange();
			}
			sleep(3);
		}
		else
		{
			if( pSoftdog->m_bFinded	== TRUE)
			{
				pSoftdog->m_nTimeout++;
				if(pSoftdog->m_nTimeout	>= 2)
				{
					pSoftdog->m_bFinded = FALSE;
					pSoftdog->m_nTimeout = 0;
					printf("Not Found SoftDog...\n");

					pSoftdog->StatusChange();
				}
			}
			sleep(2);
		}
	}

#endif

	pSoftdog->ResetData();

	printf("softDog:WorkThread Exit...\n");
	return 0;
}

BOOL	CSoftdog::StopWorking()
{
	if (!m_isWorking)
	{
		return FALSE;
	}
	m_isWorking=FALSE;

	return TRUE;
}

BOOL	CSoftdog::HasFinded(void)
{
	return m_bFinded;
}


BOOL	CSoftdog::WriteSoftID(LPSTR szSoftID)
{
	if (!m_bFinded)
	{
		return FALSE;
	}

	int dwRetCode = 0;

	return (dwRetCode == 0);
}

BOOL	CSoftdog::ReadSoftID(LPSTR szSoftID)
{
	if (!m_bFinded)
	{
		return FALSE;
	}

	int dwRetCode = 0;

	return (dwRetCode == 0);
}


void CSoftdog::StatusChange()
{
	printf("SoftDog:StatusChange\n");
	CRegister::RegisterStatusChange();
}


BOOL CSoftdog::CheckDate(char *KeyPath)
{
    int Year=0;
    BYTE Month=0;
    BYTE Day=0;
	BOOL IsDateOK=TRUE;
    int ret=m_d8.GetLimitDate(&Year,&Month,&Day,KeyPath);
	if(ret == 0)
	{
		//printf("SoftDog1:Check Date succeed:Year=%d,Month=%d,Day=%d,ret=%d\n",Year,Month,Day,ret);
		if(Year<2022)
		{
			Year = 2099;
		}
		if(Month>12)
		{
			Month = 12;
		}
		if(Day>31)
		{
			Day = 1;
		}
		CTime  t_LimitDate(Year,Month,Day,23,59,59);

		m_LimitDate = t_LimitDate;

		CTime tNow = CTime::GetCurrentTimeT();
		if( tNow <= m_LimitDate )
		{
			printf("SoftDog2:Check Date succeed:Year=%d,Month=%d,Day=%d,ret=%d\n",m_LimitDate.GetYear(),m_LimitDate.GetMonth(),m_LimitDate.GetDay(),ret);
		}
		else
		{
			IsDateOK=FALSE;
			printf("SoftDog:Check Date Error!\n");
		}
	}
	return IsDateOK;
}