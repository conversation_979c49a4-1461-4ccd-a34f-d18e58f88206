#include "D8.h"
#include "UKey.h"
#include "KeyDef.h"
#include <stdio.h>
#include <stdlib.h>
#include <semaphore.h>
#include <fcntl.h>
#include <string.h>
#include <iconv.h>
#include "stdafx.h"


char D8::D8_RUNSEM[50]="D8Run";
int D8::D8_RUNOVERTIME=10000;
D8::D8()
{
    IsLibUsbInit=0;
}

D8::~D8()
{
    IsLibUsbInit=0;
}

#define OVER_TIME 10000
int D8::sem_timedwait_millsecs(sem_t *sem, long msecs)
{
	struct timespec ts;
	clock_gettime(CLOCK_REALTIME, &ts);
	long secs = msecs/1000;
	msecs = msecs%1000;

	long add = 0;
	msecs = msecs*1000*1000 + ts.tv_nsec;
	add = msecs / (1000*1000*1000);
	ts.tv_sec += (add + secs);
	ts.tv_nsec = msecs%(1000*1000*1000);

	return sem_timedwait(sem, &ts);
}

int D8::Hanldetransfer(char *DevicePath,BYTE *array_in,int InLen,BYTE *array_out,int OutLen,int Report_size)
{
   int ret;
   libusb_device_handle * DevHandle=NULL;
   ret=MyOpenPath(DevicePath,&DevHandle);
   if(ret!=0)return ret;
   sem_t * Sem =   sem_open("ex_sim",FLAGS , PERMS, 1);
   if(Sem==NULL)return -90;
   try
   {
	   sem_timedwait_millsecs(Sem,OVER_TIME);
	   if(InLen>0)
	   {
		   ret=libusb_control_transfer(DevHandle,0x21,9,0x0302,0,(unsigned char *)array_in,Report_size,0);
		   if(ret<1)
		   {
			   libusb_close(DevHandle);sem_post(Sem);sem_close(Sem);
			   return ErrSendData;
		   }
	   }
	   if(OutLen>0)
	   {
		   ret=libusb_control_transfer(DevHandle,0xa1,1,0x0301,0,(unsigned char *)array_out,Report_size,0);
		   if(ret<1)
		   {
			   libusb_close(DevHandle);sem_post(Sem);sem_close(Sem);
			   return ErrGetData;
		   }
	   }
	   libusb_close(DevHandle);sem_post(Sem);sem_close(Sem);
   }
   catch(...)
   {
	   libusb_close(DevHandle);sem_post(Sem);sem_close(Sem);
	   return -92;
   }
   return 0;
}

int D8::MyOpenPath(char *InPath,libusb_device_handle **DevHandle)
{
	char DevicePath[260];int ret;
	int r;size_t i = 0;
	ssize_t cnt;
	libusb_device **devs;
	struct libusb_device *dev;
	if(strlen(InPath)<1)
	{

		ret=isfindmydevice(0,DevicePath);
		if(ret!=0){return ret;}
	}
	else
	{
		strcpy(DevicePath,InPath);
	}

    if(!IsLibUsbInit)
    {
        r = libusb_init(NULL);
        if (r < 0)
        {
            return ErrlibusbInit;
        }
        IsLibUsbInit=true;
    }
#if defined(Q_OS_LINUX)
    #if SUPPORT_USB_GPS
    QMutexLocker locker(&g_Global.m_usbGps.libUsbGetDeviceListQMutex);
    #endif
#endif
	cnt = libusb_get_device_list(NULL, &devs);
	if (cnt < 0)
	{
		libusb_free_device_list(devs, 1);
		return  ErrGetDevList;
	}

	while ((dev = devs[i++]) != NULL)
	{
		struct libusb_device_descriptor desc;
		r = libusb_get_device_descriptor(dev, &desc);
		if (r < 0) {
			libusb_free_device_list(devs, 1);
			return   ErrGetDevDesc;
		}

		if(((desc.idProduct==PID) && (desc.idVendor==VID)) ||
			((desc.idProduct==PID_NEW) && (desc.idVendor==VID_NEW)) ||
			((desc.idProduct==PID_NEW_2) && (desc.idVendor==VID_NEW_2)))
		{

			char temp_Path[260];
			sprintf(temp_Path,"/dev/bus/usb/%03d/%03d %04x-%04x",
				libusb_get_bus_number(dev), libusb_get_device_address(dev),desc.idVendor, desc.idProduct);
			if(strcmp(DevicePath,temp_Path)==0)
			{
				int r=libusb_open(dev,DevHandle);
				if(r<0)
				{
					return   ErrlibusbOpen;
				}
				r=libusb_kernel_driver_active(*DevHandle,0);
				if(r==1)r=libusb_detach_kernel_driver(*DevHandle,0);
				r = libusb_claim_interface(*DevHandle, 0);
				if (r < 0) {
					libusb_close(*DevHandle);
					return   ErrlibusbClaimInterface;
				}
				libusb_free_device_list(devs, 1);
				return 0;
			}
			
		}
	}
	libusb_free_device_list(devs, 1);
	return  NotFoundKey;
}


int D8::NT_GetVersion(int *Version,char *DevicePath)
{
   int ret;
   BYTE array_in[500];BYTE array_out[500];

   
   

   array_in[1]=GETVERSION;
   ret= Hanldetransfer(DevicePath,array_in,1,array_out,1,ReportLen_Usua);
   if(ret!=0)return ret;
   *Version=array_out[0];
   return 0x0;
}

int D8::ReadDword(  DWORD *in_data,char *DevicePath)
{
   BYTE b[4];int result;
   DWORD t[4];
   result= NT_Read(&b[0],&b[1],&b[2],&b[3],DevicePath);
   t[0]=b[0];t[1]=b[1];t[2]=b[2];t[3]=b[3];
   *in_data=t[0]|(t[1]<<8)|(t[2]<<16)|(t[3]<<24);
   return result;
}

int D8::WriteDword(  DWORD *in_data,char *DevicePath)
{
   BYTE b[4];
   b[0]=(BYTE)*in_data;b[1]=(BYTE)(*in_data>>8);
   b[2]=(BYTE)(*in_data>>16);b[3]=(BYTE)(*in_data>>24);
   return NT_Write(&b[0],&b[1],&b[2],&b[3],DevicePath);
}

int D8::WriteDword_2(  DWORD *in_data,char *DevicePath)
{
   BYTE b[4];
   b[0]=(BYTE)*in_data;b[1]=(BYTE)(*in_data>>8);
   b[2]=(BYTE)(*in_data>>16);b[3]=(BYTE)(*in_data>>24);
   return NT_Write_2(&b[0],&b[1],&b[2],&b[3],DevicePath);
}

int D8::NT_Read(  BYTE * ele1,BYTE * ele2,BYTE * ele3,BYTE * ele4,char *DevicePath)
{
   int ret;
   BYTE array_out[500];

   
   

   ret= Hanldetransfer(DevicePath,NULL,0,array_out,4,ReportLen_Usua);
   if(ret!=0)return ret;
   *ele1=array_out[0];
   *ele2=array_out[1];
   *ele3=array_out[2];
   *ele4=array_out[3];
   return 0;
}

int D8::NT_Write(  BYTE * ele1,BYTE * ele2,BYTE * ele3,BYTE * ele4,char *DevicePath)
{
   int ret;
   BYTE array_in[500];

   
   

   array_in[1]=3;array_in[2]=*ele1;array_in[3]=*ele2;array_in[4]=*ele3;array_in[5]=*ele4;//	array_in[1]=*ele1;array_in[2]=*ele2;
   ret= Hanldetransfer(DevicePath,array_in,5,NULL,0,ReportLen_Usua);
   return ret;
}


int D8::NT_Write_2(  BYTE * ele1,BYTE * ele2,BYTE * ele3,BYTE * ele4,char *DevicePath)
{
   int ret;
   BYTE array_in[500];

   
   

   array_in[1]=4;array_in[2]=*ele1;array_in[3]=*ele2;array_in[4]=*ele3;array_in[5]=*ele4;//	array_in[1]=*ele1;array_in[2]=*ele2;
   ret= Hanldetransfer(DevicePath,array_in,5,NULL,0,ReportLen_Usua);
   return ret;
}

int D8::NT_Write_New(  BYTE * ele1,BYTE * ele2,BYTE * ele3,BYTE * ele4,char *DevicePath)
{
   int ret;
   BYTE array_in[500];
   
   

   array_in[1]=0x0A;array_in[2]=*ele1;array_in[3]=*ele2;array_in[4]=*ele3;array_in[5]=*ele4;//	array_in[1]=*ele1;array_in[2]=*ele2;
   ret= Hanldetransfer(DevicePath,array_in,5,NULL,0,ReportLen_Usua);
   return ret;
}


int D8::NT_Write_2_New(  BYTE * ele1,BYTE * ele2,BYTE * ele3,BYTE * ele4,char *DevicePath)
{
   int ret;
   BYTE array_in[500];
   
   

   array_in[1]=0xB;array_in[2]=*ele1;array_in[3]=*ele2;array_in[4]=*ele3;array_in[5]=*ele4;//	array_in[1]=*ele1;array_in[2]=*ele2;
   ret= Hanldetransfer(DevicePath,array_in,5,NULL,0,ReportLen_Usua);
   return ret;
}

int D8::NT_FindPort(  int start,char *OutPath)
{
   return isfindmydevice(start,OutPath);
}

int D8::NT_FindPort_2(  int start,DWORD in_data,DWORD verf_data,char *OutPath)
{
   int pos;DWORD out_data;int ret;
   for(pos=start;pos<127;pos++)
   {
       ret=isfindmydevice(pos,OutPath);
       if(ret!=0){return ret;}
       ret=WriteDword( &in_data,OutPath);
       if(ret!=0){continue;}
       ret=ReadDword( &out_data,OutPath);
       if(ret!=0){continue;}
       if(out_data==verf_data){return 0;}
   }
   return NOUSBKEY;
}

int D8::WriteDword_New(  DWORD *in_data,char *DevicePath)
{
   BYTE b[4];
   b[0]=(BYTE)*in_data;b[1]=(BYTE)(*in_data>>8);
   b[2]=(BYTE)(*in_data>>16);b[3]=(BYTE)(*in_data>>24);
   return NT_Write_New(&b[0],&b[1],&b[2],&b[3],DevicePath);
}

int D8::WriteDword_2_New(  DWORD *in_data,char *DevicePath)
{
   BYTE b[4];
   b[0]=(BYTE)*in_data;b[1]=(BYTE)(*in_data>>8);
   b[2]=(BYTE)(*in_data>>16);b[3]=(BYTE)(*in_data>>24);
   return NT_Write_2_New(&b[0],&b[1],&b[2],&b[3],DevicePath);
}

int D8::NT_FindPort_3(  int start,DWORD in_data,DWORD verf_data,char *OutPath)
{
   int pos;DWORD out_data;int ret;
   for(pos=start;pos<127;pos++)
   {
       ret=isfindmydevice(pos,OutPath);
       if(ret!=0){return ret;}
       ret=WriteDword_New( &in_data,OutPath);
       if(ret!=0){continue;;}
       ret=ReadDword( &out_data,OutPath);
       if(ret!=0){continue;}
       if(out_data==verf_data){return 0;}
   }
   return NOUSBKEY;
}

int D8::Read(BYTE *OutData,short address,BYTE *password,char *DevicePath )
{
    BYTE opcode=0x80;
        int ret;
    BYTE array_in[500],array_out[500];int n;

    
    

    if(address>495 || address<0)return ErrAddrOver;
    if(address>255){opcode=0xa0;address=address-256;}

    //array_in[1]要输入的数据长度，array_in[2]要接受的数据长度。array_in[3]指令，
    //array_in[n+3]数据
    array_in[1]=READBYTE;//read 0x10;write 0x11;
    array_in[2]=opcode;//0x01;//read 0x80 ;write 0x40;
    array_in[3]=(BYTE)address;
    for(n=5;n<14;n++)
    {
        array_in[n]=*password;
        password++;
    }
    ret= Hanldetransfer(DevicePath,array_in,13,array_out,2,ReportLen_Usua);
    if(ret!=0)return ret;
    if(array_out[0]!=0x53 )
    {
        return ErrReadPWD;//表示失败；
    }
    *OutData=array_out[1];
    return 0;
}

 int  D8::Write(BYTE InData,short address,BYTE *password,char *DevicePath )
{
    BYTE opcode=0x40;int ret;

    BYTE array_in[500],array_out[500];int n;

    
    

    if(address>511 || address<0)return ErrAddrOver;
    if(address>255){opcode=0x60;address=address-256;}

    array_in[1]=WRITEBYTE;//read 0x10;write 0x11;
    array_in[2]=opcode;//0x01;//read 0x80 ;write 0x40;
    array_in[3]=(BYTE)address;
    array_in[4]=InData;
    for(n=5;n<14;n++)
    {
        array_in[n]=*password;
        password++;
    }
    ret= Hanldetransfer(DevicePath,array_in,13,array_out,2,ReportLen_Usua);
    if(ret!=0)return ret;
    if(array_out[1]!=1 )
    {
        return ErrWritePWD;//表示失败；
    }
    return 0;
}

 int D8::Y_Read(BYTE *OutData,short address ,short len,BYTE *password,char *DevicePath )
{
    short addr_l,addr_h;int n;int ret;

    BYTE array_in[512],array_out[512];

    
    

    if(address>495 || address<0)return ErrAddrOver;
    if( len>255)return -87;
    if(len+address>511)return ErrAddrOver;
    {addr_h=(address>>8)<<1;addr_l=address&0xff;}

    //array_in[1]要输入的数据长度，array_in[2]要接受的数据长度。array_in[3]指令，
    //array_in[n+3]数据
    array_in[1]=YTREADBUF;//read 0x10;write 0x11;
    array_in[2]=(BYTE)addr_h;//0x01;//read 0x80 ;write 0x40;
    array_in[3]=(BYTE)addr_l;
    array_in[4]=(BYTE)len;
    for(n=0;n<8;n++)
    {
        array_in[5+n]=*password;
        password++;
    }
    ret= Hanldetransfer(DevicePath,array_in,13,array_out,len+1,ReportLen_Usua);
    if(ret!=0)return ret;

    if(array_out[0]!=0x0 )
    {
        return ErrReadPWD;//表示失败；
    }
    for(n=0;n<len;n++)
    {
        *OutData=array_out[n+1];
        OutData++;
    }
    return 0;
}

 int D8::Y_Write(BYTE *InData,short address,short len,BYTE *password,char *DevicePath )
{
    short addr_l,addr_h;int n;int ret;
    BYTE array_in[512],array_out[512];

    
    

    if(len>255)return -87;
    if(address+len-1>511 || address<0)return ErrAddrOver;
    {addr_h=(address>>8)<<1;addr_l=address&0xff;}

    array_in[1]=YTWRITEBUF;//read 0x10;write 0x11;
    array_in[2]=(BYTE)addr_h;//0x01;//read 0x80 ;write 0x40;
    array_in[3]=(BYTE)addr_l;
    array_in[4]=(BYTE )len;
    for(n=0;n<8;n++)
    {
        array_in[5+n]=*password;
        password++;
    }
    for(n=0;n<len;n++)
    {
        array_in[13+n]=*InData;
        InData++;
    }
    ret= Hanldetransfer(DevicePath,array_in,13+len,array_out,3,ReportLen_Usua);
    if(ret!=0)return ret;

    if(array_out[0]!=0x0)
    {
        return ErrWritePWD;//表示失败；
    }
    return 0;
}

int D8::isfindmydevice( int pos ,char *DevicePath)
{
	int count=0;
	int r;size_t i = 0;
	ssize_t cnt;
	libusb_device **devs;
	struct libusb_device *dev;
	libusb_device_handle *DevHandle;

    if(!IsLibUsbInit)
    {
        r = libusb_init(NULL);
        if (r < 0)
        {
            return ErrlibusbInit;
        }
        IsLibUsbInit=true;
    }
#if defined(Q_OS_LINUX)
    #if SUPPORT_USB_GPS
    QMutexLocker locker(&g_Global.m_usbGps.libUsbGetDeviceListQMutex);
    #endif
#endif
	cnt = libusb_get_device_list(NULL, &devs);
	if (cnt < 0)
	{
		libusb_free_device_list(devs, 1);return ErrGetDevList;
	}

	while ((dev = devs[i++]) != NULL)
	{
		struct libusb_device_descriptor desc;
		r = libusb_get_device_descriptor(dev, &desc);
		if (r < 0) {
			libusb_free_device_list(devs, 1);return ErrGetDevDesc;
		}

		if(((desc.idProduct==PID) && (desc.idVendor==VID)) ||
			((desc.idProduct==PID_NEW) && (desc.idVendor==VID_NEW)) ||
			((desc.idProduct==PID_NEW_2) && (desc.idVendor==VID_NEW_2)))
		{
			if(count==pos)
			{
				sprintf(DevicePath,"/dev/bus/usb/%03d/%03d %04x-%04x",/*2017-1-4*/
					libusb_get_bus_number(dev), libusb_get_device_address(dev),desc.idVendor, desc.idProduct);
				libusb_free_device_list(devs, 1);

				return 0;
			}
			count++;
		}

	}

	libusb_free_device_list(devs, 1);
	return NotFoundKey;
}

int D8::NT_GetID(  DWORD *ID_1,DWORD *ID_2,char *DevicePath)
{
       DWORD t[8];int ret;
   BYTE array_in[500];BYTE array_out[500];

   
   

   array_in[1]=GETID;
   ret= Hanldetransfer(DevicePath,array_in,1,array_out,8,ReportLen_Usua);
   if(ret!=0)return ret;
   t[0]=array_out[0];t[1]=array_out[1];t[2]=array_out[2];t[3]=array_out[3];
   t[4]=array_out[4];t[5]=array_out[5];t[6]=array_out[6];t[7]=array_out[7];
   *ID_1=t[3]|(t[2]<<8)|(t[1]<<16)|(t[0]<<24);
   *ID_2=t[7]|(t[6]<<8)|(t[5]<<16)|(t[4]<<24);
   return 0;
}

int D8::NT_Cal(  BYTE * InBuf,BYTE *OutBuf,char *DevicePath)
{
   int n;int ret;
   BYTE array_in[500];BYTE array_out[500];

   
   

   array_in[1]=CAL_TEA;
   for (n=2;n<10;n++)
   {
       array_in[n]=InBuf[n-2];
   }
   ret= Hanldetransfer(DevicePath,array_in,9,array_out,10,ReportLen_Usua);
   if(ret!=0)return ret;
   memcpy(OutBuf,array_out,8);
   if(array_out[8]!=0x55)
   {
       return ErrResult;
   }
   return 0;
}

int D8::NT_SetCal_2(BYTE *InData,BYTE IsHi,char *DevicePath )
{
      int n;int ret;
      BYTE array_in[30],array_out[500];
      BYTE opcode=SET_TEAKEY;

      
      

      array_in[1]=opcode;
      array_in[2]=IsHi;
      for(n=0;n<8;n++)
      {
          array_in[3+n]=*InData;
          InData++;
      }
      ret= Hanldetransfer(DevicePath,array_in,11,array_out,3,ReportLen_Usua);
      if(ret!=0)return ret;
      if(array_out[0]!=0x0)
      {
          return ErrResult;//表示失败；
      }
      return 0;
}

int D8::NT_ReSet(char *DevicePath )
{

       int ret;
      BYTE array_in[30],array_out[500];

      
      

      array_in[1]=MYRESET;
      ret= Hanldetransfer(DevicePath,array_in,2,array_out,1,ReportLen_Usua);
      if(ret!=0)return ret;
      if(ret!=0)return ret;
      if(array_out[0]!=0x0)
      {
          return ErrResult;//表示失败；
      }
      return 0;
}

int  D8::F_GetVerEx(int *Version,char *DevicePath)
{
       int ret;
   BYTE array_in[500];BYTE array_out[500];

   
   

   array_in[1]=GETVEREX;
   ret= Hanldetransfer(DevicePath,array_in,1,array_out,1,ReportLen_Usua);
   if(ret!=0)return ret;
   *Version=array_out[0];
   return 0x0;
}


int D8::NT_Cal_New(  BYTE * InBuf,BYTE *OutBuf,char *DevicePath)
{
   int n,ret;
   BYTE array_in[500];BYTE array_out[500];

   
   

   array_in[1]=CAL_TEA_2;
   for (n=2;n<10;n++)
   {
       array_in[n]=InBuf[n-2];
   }
   ret= Hanldetransfer(DevicePath,array_in,9,array_out,10,ReportLen_Usua);
   if(ret!=0)return ret;
   memcpy(OutBuf,array_out,8);
   if(array_out[8]!=0x55)
   {
       return ErrResult;
   }

   return 0;
}

int D8::NT_SetCal_New(BYTE *InData,BYTE IsHi,char *DevicePath )
{
    int n,ret;
    BYTE array_in[30],array_out[500];
    BYTE opcode=SET_TEAKEY_2;

    
    

    array_in[1]=opcode;
    array_in[2]=IsHi;
    for(n=0;n<8;n++)
    {
      array_in[3+n]=*InData;
      InData++;
    }
    ret= Hanldetransfer(DevicePath,array_in,11,array_out,3,ReportLen_Usua);
    if(ret!=0)return ret;
    if(array_out[0]!=0x0)
    {
        return ErrResult;//表示失败；
    }
    return 0;
}

int D8::NT_Set_SM2_KeyPair(BYTE *PriKey,BYTE *PubKeyX,BYTE *PubKeyY,char *sm2UserName,char *DevicePath )
{

      int ret;
      BYTE array_in[500],array_out[500];

      
      

      memset(array_in,0,256);

      array_in[1]=SET_ECC_KEY;
      memcpy(&array_in[2+ECC_MAXLEN*0],PriKey,ECC_MAXLEN);
      memcpy(&array_in[2+ECC_MAXLEN*1],PubKeyX,ECC_MAXLEN);
      memcpy(&array_in[2+ECC_MAXLEN*2],PubKeyY,ECC_MAXLEN);
      if(sm2UserName!=0)
      {
          memcpy(&array_in[2+ECC_MAXLEN*3],sm2UserName,USERID_LEN);
      }
      ret= Hanldetransfer(DevicePath,array_in,ECC_MAXLEN*3+2+USERID_LEN,array_out,1,ReportLen_Smart);
      if(ret!=0)return ret;
      if(array_out[0]!=0x20)return USBStatusFail;

      return 0;
}

int D8::NT_Get_SM2_PubKey(BYTE *KGx,BYTE *KGy,char *sm2UserName,char *DevicePath )
{

      int ret;
      BYTE array_in[500],array_out[500];

      array_out[0]=0xfb;

      
      

      array_in[1]=GET_ECC_KEY;
      ret= Hanldetransfer(DevicePath,array_in,2,array_out,ECC_MAXLEN*2+2+USERID_LEN,ReportLen_Smart);
      if(ret!=0)return ret;

      if(array_out[0]!=0x20)return USBStatusFail;
      memcpy(KGx,&array_out[1+ECC_MAXLEN*0],ECC_MAXLEN);
      memcpy(KGy,&array_out[1+ECC_MAXLEN*1],ECC_MAXLEN);
      memcpy(sm2UserName,&array_out[1+ECC_MAXLEN*2],USERID_LEN);

      return 0;
}


int D8::NT_GenKeyPair(BYTE* PriKey,BYTE *PubKey,char *DevicePath )
{

      int ret;
      BYTE array_in[500],array_out[500];

      array_out[0]=0xfb;

      
      

      array_in[1]=GEN_KEYPAIR;
      ret= Hanldetransfer(DevicePath,array_in,2,array_out,ECC_MAXLEN*3+2,ReportLen_Smart);
      if(ret!=0)return ret;

      if(array_out[0]!=0x20)
      {
          return FAILEDGENKEYPAIR;//表示读取失败；
      }
      memcpy(PriKey,&array_out[1+ECC_MAXLEN*0],ECC_MAXLEN);
      memcpy(PubKey,&array_out[1+ECC_MAXLEN*1],ECC_MAXLEN*2+1);
      return 0;
}

int D8::NT_Set_Pin(char *old_pin,char *new_pin,char *DevicePath )
{

      int ret;
      BYTE array_in[500],array_out[500];

      
      

      array_in[1]=SET_PIN;
      memcpy(&array_in[2+PIN_LEN*0],old_pin,PIN_LEN);
      memcpy(&array_in[2+PIN_LEN*1],new_pin,PIN_LEN);

      ret= Hanldetransfer(DevicePath,array_in,PIN_LEN*2+2,array_out,2,ReportLen_Smart);
      if(ret!=0)return ret;

      if(array_out[0]!=0x20)return USBStatusFail;
      if(array_out[1]!=0x20)return FAILPINPWD;
      return 0;
}


int D8::NT_SM2_Enc(BYTE *inbuf,BYTE *outbuf,BYTE inlen,char *DevicePath )
{

      int n;int ret;
      BYTE array_in[500],array_out[500];

      
      

      array_out[0]=0xfb;
      array_in[1]=MYENC;
      array_in[2]=inlen;
      for(n=0;n<inlen;n++)
      {
        array_in[3+n]=inbuf[n];
      }
      ret= Hanldetransfer(DevicePath,array_in,inlen+1+2,array_out,inlen+SM2_ADDBYTE+3,ReportLen_Smart);
      if(ret!=0)return ret;

      if(array_out[0]!=0x20)return USBStatusFail;
      if(array_out[1]==0)return FAILENC;

      memcpy(outbuf,&array_out[2],inlen+SM2_ADDBYTE);

      return 0;
}

int D8::NT_SM2_Dec(BYTE *inbuf,BYTE *outbuf,BYTE inlen,char* pin,char *DevicePath )
{

      int n;int ret;
      BYTE array_in[500],array_out[500];

      
      

      array_out[0]=0xfb;
      array_in[1]=MYDEC;
      memcpy(&array_in[2],pin,PIN_LEN);
      array_in[2+PIN_LEN]=inlen;
      for(n=0;n<inlen;n++)
      {
        array_in[2+PIN_LEN+1+n]=inbuf[n];
      }
      ret= Hanldetransfer(DevicePath,array_in,inlen+1+2+PIN_LEN,array_out,inlen-SM2_ADDBYTE+4,ReportLen_Smart);
      if(ret!=0)return ret;

      if(array_out[2]!=0x20)return FAILPINPWD;
      if(array_out[1]==0) return FAILENC;
      if(array_out[0]!=0x20)return USBStatusFail;
      memcpy(outbuf,&array_out[3],inlen-SM2_ADDBYTE);

      return 0;
}

int D8::NT_Sign(BYTE *inbuf,BYTE *outbuf,char* pin,char *DevicePath )
{

      int n;int ret;
      BYTE array_in[500],array_out[500];

      
      

      array_out[0]=0xfb;
      array_in[1]=YTSIGN;
      memcpy(&array_in[2],pin,PIN_LEN);
      for(n=0;n<32;n++)
      {
        array_in[2+PIN_LEN+n]=inbuf[n];
      }
      ret= Hanldetransfer(DevicePath,array_in,32+2+PIN_LEN,array_out,64+2,ReportLen_Smart);
      if(ret!=0)return ret;

      if(array_out[1]!=0x20)return FAILPINPWD;
      if(array_out[0]!=0x20)return USBStatusFail;
      memcpy(outbuf,&array_out[2],64);

      return 0;
}

int D8::NT_Sign_2(BYTE *inbuf,BYTE *outbuf,char* pin,char *DevicePath )
{

      int n;int ret;
      BYTE array_in[500],array_out[500];

      
      

      array_out[0]=0xfb;
      array_in[1]=YTSIGN_2;
      memcpy(&array_in[2],pin,PIN_LEN);
      for(n=0;n<32;n++)
      {
        array_in[2+PIN_LEN+n]=inbuf[n];
      }
      ret= Hanldetransfer(DevicePath,array_in,32+2+PIN_LEN,array_out,64+2,ReportLen_Smart);
      if(ret!=0)return ret;
      if(array_out[1]!=0x20)return FAILPINPWD;
      if(array_out[0]!=0x20)return USBStatusFail;
      memcpy(outbuf,&array_out[2],64);

      return 0;
}

int D8::NT_Verfiy(BYTE *inbuf,BYTE *InSignBuf,BOOL *outbiao,char *DevicePath )
{

      int n;int ret;
      BYTE array_in[500],array_out[500];

      
      

      array_out[0]=0xfb;
      array_in[1]=YTVERIFY;
      for(n=0;n<32;n++)
      {
        array_in[2+n]=inbuf[n];
      }
      for(n=0;n<64;n++)
      {
        array_in[2+32+n]=InSignBuf[n];
      }
      ret= Hanldetransfer(DevicePath,array_in,32+2+64,array_out,3,ReportLen_Smart);
      if(ret!=0)return ret;
      *outbiao=array_out[1];
      if(array_out[0]!=0x20)return USBStatusFail;

      return 0;
}

int  D8::NT_GetChipID(  BYTE *OutChipID,char *DevicePath)
{
   int ret;
   BYTE array_in[500];BYTE array_out[500];

   
   

   array_in[1]=GET_CHIPID;
   ret= Hanldetransfer(DevicePath,array_in,1,array_out,17,ReportLen_Smart);
   if(ret!=0)return ret;
   if(array_out[0]!=0x20)return USBStatusFail;
   memcpy(OutChipID,&array_out[1],16);

   return 0;
}

int D8::Sub_SetOnly(BOOL IsOnly,BYTE Flag,char *DevicePath)
{
   int ret;
   BYTE array_in[500];BYTE array_out[500];

   
   

   array_in[1]=Flag;
   if(IsOnly)array_in[2]=0;else array_in[2]=0xff;
   ret=Hanldetransfer(DevicePath,array_in,3,array_out,1,ReportLen_Smart);
   if(ret!=0)return ret;
   if(array_out[0]!=0x0)
   {
       return ErrResult;//表示失败；
   }
   return 0;
}

int D8::NT_SetHidOnly(  BOOL IsHidOnly,char *DevicePath)
{
   return Sub_SetOnly(IsHidOnly,SETHIDONLY,DevicePath);
}

int  D8::NT_SetUReadOnly(char *DevicePath)
{
   return Sub_SetOnly(true,SETREADONLY,DevicePath);
}


int D8::NT_GetIDVersion(int *Version,char *DevicePath)
{
    int ret;
    
    
    ret=(NT_GetVersion)(Version,DevicePath);
    
    
    return ret;
}

int D8::GetID(  DWORD *ID_1,DWORD *ID_2,char *DevicePath)
{
    int ret;
    
    
    ret=(NT_GetID)(ID_1,ID_2,DevicePath);
    
    
    return ret;
}

int D8::sRead(  DWORD *in_data,char *DevicePath)
{
    int ret;
    //////////////////////////////////////////////////////////////////////////////////

    
    
    ret=(ReadDword)(in_data,DevicePath);
    
    
    return ret;
}

int D8::sWrite(  DWORD out_data,char *DevicePath)
{
    int ret;
    
    
    ret=(WriteDword)(&out_data,DevicePath);
    
    
    return ret;
}

int D8::YWrite(BYTE InData,short address,char * HKey,char *LKey,char *DevicePath )
{
    if(address>495){return  -81;}
    BYTE ary1[8];
    myconvert(HKey,LKey,ary1);
    int ret;
    
    
    ret=(Write)(InData,address,ary1,DevicePath);
    
    
    return ret;
}

 int D8::YRead(BYTE *OutData,short address,char * HKey,char *LKey,char *DevicePath )
{
    if(address>495){return  -81;}
    BYTE ary1[8];
    myconvert(HKey,LKey,ary1);
    int ret;
    
    
    ret=(Read)(OutData,address,ary1,DevicePath);
    
    
    return ret;
}

int D8::YWriteEx(BYTE *InData,short Address,short len,char *HKey,char *LKey,char *DevicePath )
{
    int ret=0;BYTE password[8];int n,trashLen=8;int temp_leave, leave;
     if(Address+len-1>495 || Address<0)return -81;

     myconvert(HKey,LKey,password);

     

     
     temp_leave=Address-Address/trashLen*trashLen; leave=trashLen-temp_leave;
     if(leave>len){leave=len;}
     if(leave>0)
     {
         for(n=0;n<leave/trashLen;n++)
         {
             ret=(Y_Write)(InData+n*trashLen,Address+n*trashLen,trashLen,password,DevicePath);
             if(ret!=0){return  ret;}
         }
         if(leave-trashLen*n>0)
         {
             ret=(Y_Write)(InData+n*trashLen,Address+n*trashLen,(BYTE)(leave-n*trashLen),password,DevicePath);
             if(ret!=0){return  ret;}

         }
     }
     len=len-leave;Address=Address+leave;InData=InData+leave;
     if (len>0)
     {
         for(n=0;n<len/trashLen;n++)
         {
             ret=(Y_Write)(InData+n*trashLen,Address+n*trashLen,trashLen,password,DevicePath);
             if(ret!=0){return ret;}
         }
         if(len-trashLen*n>0)
         {
             ret=(Y_Write)(InData+n*trashLen,Address+n*trashLen,(BYTE)(len-n*trashLen),password,DevicePath);
             if(ret!=0){return ret;}

         }
     }
     
     
     return ret;
}

int D8::YReadEx(BYTE *OutData,short Address,short len,char *HKey,char *LKey,char *DevicePath )
{
    int ret;BYTE password[8];int n,trashLen=16;
    if(Address+len-1>495 || Address<0)return -81;

   myconvert(HKey,LKey,password);

    
    
    for(n=0;n<len/trashLen;n++)
    {
        ret=(Y_Read)(OutData+n*trashLen,Address+n*trashLen,trashLen,password,DevicePath);
        if(ret!=0){return ret;}
    }
    if(len-trashLen*n>0)
    {
        ret=(Y_Read)(OutData+n*trashLen,Address+n*trashLen,(len-trashLen*n),password,DevicePath);
        if(ret!=0){return ret;}

    }
    
    
    return ret;
}

int D8::FindPort_3(  int start,DWORD in_data,DWORD verf_data,char *OutPath)
{
    int ret;
    
    
    ret=(NT_FindPort_3)(start,in_data,verf_data,OutPath);
    
    
    return ret;
}

int D8::FindPort_2(  int start,DWORD in_data,DWORD verf_data,char *OutPath)
{
    int ret;
    
    
    ret=(NT_FindPort_2)(start,in_data,verf_data,OutPath);
    
    
    return ret;
}

int D8::FindPort(  int start,char *OutPath)
{
    int ret;
    
    
    ret=(NT_FindPort)(start,OutPath);
    
    
    return ret;
}

int D8::sWrite_2(  DWORD out_data,char *DevicePath)
{
    int ret;
    
    
    ret=(WriteDword_2)(&out_data,DevicePath);
    
    
    return ret;
}

void D8::myconvert(char *hkey,char *lkey,BYTE *out_data)
{
    DWORD z,z1;int n;
    z=HexToInt(hkey);
    z1=HexToInt(lkey);
    for(n=0;n<=3;n++)
    {
        *out_data=(BYTE)((z<<(n*8))>>24);
        out_data++;
    }
    for(n=0;n<=3;n++)
    {
        *out_data=(BYTE)((z1<<(n*8))>>24);
        out_data++;
    }
}

DWORD D8::HexToInt(char* s)
{
    char hexch[] = "0123456789ABCDEF";
    size_t i;
   DWORD r,n,k,j;
   char ch;

    k=1; r=0;
    for (i=strlen(s);  i>0; i--) {
    ch = s[i-1]; if (ch > 0x3f) ch &= 0xDF;
    n = 0;
        for (j = 0; j<16; j++)
        if (ch == hexch[j])
            n = j;
        r += (n*k);
        k *= 16;
    }
    return r;
}

int D8::SetReadPassword(char * W_HKey,char *W_LKey,char * new_HKey,char *new_LKey,char *DevicePath)
{

    BYTE ary1[8];BYTE ary2[8];
    myconvert(W_HKey,W_LKey,ary1);
    myconvert(new_HKey,new_LKey,ary2);
    int ret;int address=0x1f0;
    
    
    ret=(Y_Write)(ary2,address,8,ary1,DevicePath);
    if(ret!=0){return ret;}
    
    
    return ret;

}


int D8::SetWritePassword(char * W_HKey,char *W_LKey,char * new_HKey,char *new_LKey,char *DevicePath)
{
    BYTE ary1[8];BYTE ary2[8];
    myconvert(W_HKey,W_LKey,ary1);
    myconvert(new_HKey,new_LKey,ary2);
    int ret;int address=0x1f8;
    
    
    ret=(Y_Write)(ary2,address,8,ary1,DevicePath);
    if(ret!=0){return ret;}
    
    
    return ret;

}

int D8::YWriteString(char *InString,short Address,char * HKey,char *LKey,char *DevicePath )
{
    BYTE ary1[8];int n,trashLen=8;int ret=0,outlen,total_len;//int versionex;

    myconvert(HKey,LKey,ary1);

    outlen=strlen(InString);
    total_len=Address+outlen;
    if(total_len>495){return -47;}

    
    {
        
        for(n=0;n<outlen/trashLen;n++)
        {
            ret=(Y_Write)((BYTE *)(InString+n*trashLen),Address+n*trashLen,trashLen,ary1,DevicePath);
            if(ret!=0){return  ret;}
        }
        if(outlen-trashLen*n>0)
        {
            ret=(Y_Write)((BYTE *)(InString+n*trashLen),Address+n*trashLen,(BYTE)(outlen-n*trashLen),ary1,DevicePath);
            if(ret!=0){return  ret;}

        }
        
    }
    
    return ret;
}

int D8::YReadString(char *string ,short Address,int len,char * HKey,char *LKey,char *DevicePath )
{
    BYTE ary1[8];int n,trashLen=16;int ret=0;int total_len;//DWORD z,z1;int versionex;


        myconvert(HKey,LKey,ary1);

        total_len=Address+len;
        if(total_len>495){return -47;}

        
        {
            
            for(n=0;n<len/trashLen;n++)
            {
                ret=(Y_Read)((BYTE*)(string+n*trashLen),Address+n*trashLen,trashLen,ary1,DevicePath);
                if(ret!=0)
                {
                    
                    return ret;
                }
            }
            if(len-trashLen*n>0)
            {
                ret=(Y_Read)((BYTE*)(string+n*trashLen),Address+n*trashLen,(len-trashLen*n),ary1,DevicePath);
                if(ret!=0)
                {
                    
                    return ret;
                }

            }
            
        }
        
        return ret;
}

 void D8::ByteArrayToHexString(BYTE *in_data,char * OutString,int len)
{
    memset(OutString,0,len*2);
    char temp[5];int n;
    for (n=1;n<=len;n++)
    {
        sprintf(temp,"%02X",*in_data);
        in_data++;
        strcat(OutString,temp);
    }
}

 void D8::HexStringToByteArray(char * InString,BYTE *in_data)
{
    int len=strlen(InString);
    char temp[5];int n;
    for (n=1;n<=len;n=n+2)
    {
        memset(temp,0,sizeof(temp));
        strncpy(temp,&InString[n-1],2);
        *in_data=(BYTE)HexToInt(temp);
        in_data++;
    }
}

 void D8::HexStringToByteArrayEx(char * InString,BYTE *in_data)
{
    int len=strlen(InString);
    char temp[5];int n;
    if(len>32)len=32;
    for (n=1;n<=len;n=n+2)
    {
        memset(temp,0,sizeof(temp));
        strncpy(temp,&InString[n-1],2);
        *in_data=(BYTE)HexToInt(temp);
        in_data++;
    }

}

int D8::SetCal_2(char *Key,char *DevicePath)
{
    BYTE KeyBuf[16];
    memset(KeyBuf,0,16);
    HexStringToByteArrayEx(Key,KeyBuf);
    //注意，这里是地址互换的
    int ret;
    
    
    ret=(NT_SetCal_2)(&KeyBuf[8],0,DevicePath);
    if(ret!=0)goto error1;
    ret=(NT_SetCal_2)(&KeyBuf[0],1,DevicePath);
error1:
    
    
    return ret;

}

int D8::Cal(  BYTE *InBuf,BYTE *OutBuf,char *DevicePath)
{
    int ret;
    
    
    ret=NT_Cal(InBuf,OutBuf,DevicePath);
    
    
    return ret;
}

int D8::GetLen(char *InString)
{
    return strlen(InString)+1;
}

int D8::EncString(  char *InString,char *OutString,char *DevicePath)
{

    int ret;int n;BYTE *outbuf;
    int len=strlen(InString)+1;if(len<8)len=8;
    outbuf=new BYTE[len];
    memset(outbuf,0,len);
    memcpy(outbuf,InString,strlen(InString)+1);
    for(n=0;n<=(len-8);n=n+8)
    {
        ret=Cal((BYTE *)&outbuf[n],&outbuf[n],DevicePath);
        if(ret!=0){delete [] outbuf;return ret;}
    }
    ByteArrayToHexString(outbuf,OutString,len);
    delete [] outbuf;
    return ret;
}

void D8:: EncBySoft(   BYTE  *   aData,  BYTE   *   aKey   )
{
    const   unsigned   int   cnDelta   =   0x9E3779B9;
    register   unsigned   int   y   =   (   (   unsigned   int   *   )aData   )[0],   z   =   (   (   unsigned   int   *   )aData   )[1];
    register   unsigned   int   sum   =   0;
    unsigned   int   a   =   (   (   unsigned   int   *   )aKey   )[0],   b   =   (   (   unsigned   int   *   )aKey   )[1];
    unsigned   int   c   =   (   (   unsigned   int   *   )aKey   )[2],   d   =   (   (   unsigned   int   *   )aKey   )[3];
    int   n   =   32;

    while   (   n--   >   0   )
    {
        sum   +=   cnDelta;
        y   +=   ((   z   <<   4   )   +   a )  ^   (z   +   sum )  ^  ( (   z   >>   5   )   +   b);

        z   +=   ((   y   <<   4   )   +   c )  ^   (y   +   sum )  ^  ( (   y   >>   5   )   +   d);
    }
    (   (   unsigned   int   *   )aData   )[0]   =   y;
    (   (   unsigned   int   *   )aData   )[1]   =   z;
}

void D8:: DecBySoft(    BYTE  *   aData,   BYTE   *   aKey   )
{
    const   unsigned   int   cnDelta   =   0x9E3779B9;
    register   unsigned   int   y   =   (   (   unsigned   int   *   )aData   )[0],   z   =   (   (   unsigned   int   *   )aData   )[1];
    register   unsigned   int   sum   =   0xC6EF3720;
    unsigned   int   a   =   (   (   unsigned   int   *   )aKey   )[0],   b   =   (   (   unsigned   int   *   )aKey   )[1];
    unsigned   int   c   =   (   (   unsigned   int   *   )aKey   )[2],   d   =   (   (   unsigned   int   *   )aKey   )[3];
    int   n   =   32;
    while   (   n--   >   0   )
    {
        z   -=  ( (   y   <<   4   )   +   c )  ^  ( y   +   sum  ) ^ (  (   y   >>   5   )   +   d);
        y   -=  ( (   z   <<   4   )   +   a )  ^  ( z   +   sum  ) ^ (  (   z   >>   5   )   +   b);
        sum   -=   cnDelta;
    }
    (   (   unsigned   int   *   )aData   )[0]   =   y;
    (   (   unsigned   int   *   )aData   )[1]   =   z;
}

int D8::sWriteEx(  DWORD in_data,DWORD *out_data,char *DevicePath)
{
    int ret;
    
    
    ret=(WriteDword)(&in_data,DevicePath);
    if(ret!=0)goto error1;
    ret=(ReadDword)(out_data,DevicePath);
error1:
    
    
    return ret;
}

int D8::sWrite_2Ex(  DWORD in_data,DWORD *out_data,char *DevicePath)
{
    int ret;
    
    
    ret=(WriteDword_2)(&in_data,DevicePath);
    if(ret!=0)goto error1;
    ret=(ReadDword)(out_data,DevicePath);
error1:
    
    
    return ret;
}

int D8::sWriteEx_New(  DWORD in_data,DWORD *out_data,char *DevicePath)
{
    int ret;
    
    
    ret=(WriteDword_New)(&in_data,DevicePath);
    if(ret!=0)goto error1;
    ret=(ReadDword)(out_data,DevicePath);
error1:
    
    
    return ret;
}

int D8::sWrite_2Ex_New(  DWORD in_data,DWORD *out_data,char *DevicePath)
{
    int ret;
    
    
    ret=(WriteDword_2_New)(&in_data,DevicePath);
    if(ret!=0)goto error1;
    ret=(ReadDword)(out_data,DevicePath);

error1:
    
    
    return ret;
}

int D8::ReSet(char *DevicePath)
{

    int ret;
    
    

    ret=(NT_ReSet)(DevicePath);

    
    
    return ret;

}

 int D8::NT_GetVersionEx(int *version,char *DevicePath )
{
    int ret;
    
    
    ret=(F_GetVerEx)(version,DevicePath);
    
    
    return ret;
}

int D8::SetCal_New(char *Key,char *DevicePath)
{
    BYTE KeyBuf[16];
    memset(KeyBuf,0,16);
    HexStringToByteArrayEx(Key,KeyBuf);
    //注意，这里是地址互换的
    int ret;
    
    
    ret=(NT_SetCal_New)(&KeyBuf[8],0,DevicePath);
    if(ret!=0)goto error1;
    ret=(NT_SetCal_New)(&KeyBuf[0],1,DevicePath);
error1:
    
    
    return ret;

}

int D8::Cal_New(  BYTE *InBuf,BYTE *OutBuf,char *DevicePath)
{
    int ret;
    
    
    ret=NT_Cal_New(InBuf,OutBuf,DevicePath);
    
    
    return ret;
}

int D8::EncString_New(  char *InString,char *OutString,char *DevicePath)
{

    int ret;int n;BYTE *outbuf;
    int len=strlen(InString)+1;if(len<8)len=8;
    outbuf=new BYTE[len];
    memset(outbuf,0,len);
    memcpy(outbuf,InString,strlen(InString)+1);
    for(n=0;n<=(len-8);n=n+8)
    {
        ret=Cal_New((BYTE *)&outbuf[n],&outbuf[n],DevicePath);
        if(ret!=0){delete [] outbuf;return ret;}
    }
    ByteArrayToHexString(outbuf,OutString,len);
    delete [] outbuf;
    return ret;
}

void D8::SwitchByte2Char(char *outstring,BYTE *inbyte,int inlen)
{
   int n;char temp[3];
   memset(outstring,0,ECC_MAXLEN*2);
   for(n=0;n<inlen;n++)
   {
        sprintf(temp,"%02X",inbyte[n]);
        strcat(outstring,temp);
   }

}

void D8::SwitchChar2Byte(char *instring,BYTE *outbyte)
{
   int n;char temp[3];
   int inlen=strlen(instring)/2;
   for(n=0;n<inlen;n++)
   {
       temp[2]=(char)0;
       strncpy(temp,&instring[n*2],2);
        *outbyte=HexToInt(temp);
        outbyte++;
   }

}

int D8::YT_GenKeyPair(char* PriKey,char *PubKeyX,char *PubKeyY,char *DevicePath)
{

    int ret;BYTE b_PriKey[ECC_MAXLEN],b_PubKey[ECC_MAXLEN*2+1];//其中第一个字节是标志位，忽略
    
    
    ret=(NT_GenKeyPair)(b_PriKey,b_PubKey,DevicePath);
    
    
    memset(PriKey,0,2*ECC_MAXLEN+1);
    memset(PubKeyX,0,2*ECC_MAXLEN+1);
    memset(PubKeyY,0,2*ECC_MAXLEN+1);
    SwitchByte2Char(PriKey,b_PriKey,ECC_MAXLEN);
    SwitchByte2Char(PubKeyX,&b_PubKey[1],ECC_MAXLEN);
    SwitchByte2Char(PubKeyY,&b_PubKey[1+ECC_MAXLEN],ECC_MAXLEN);
    return ret;

}

int D8::Set_SM2_KeyPair(char *PriKey,char *PubKeyX,char *PubKeyY,char *sm2UserName,char *DevicePath )
{

    int ret;BYTE b_PriKey[ECC_MAXLEN],b_PubKeyX[ECC_MAXLEN],b_PubKeyY[ECC_MAXLEN];
    SwitchChar2Byte(PriKey,b_PriKey);
    SwitchChar2Byte(PubKeyX,b_PubKeyX);
    SwitchChar2Byte(PubKeyY,b_PubKeyY);
    
    
    ret=(NT_Set_SM2_KeyPair)(b_PriKey,b_PubKeyX,b_PubKeyY,sm2UserName,DevicePath);
    
    
    return ret;

}

int D8::Get_SM2_PubKey(char *PubKeyX,char *PubKeyY,char *sm2UserName,char *DevicePath)
{

    int ret;BYTE b_PubKeyX[ECC_MAXLEN],b_PubKeyY[ECC_MAXLEN];
    
    
    ret=(NT_Get_SM2_PubKey)(b_PubKeyX,b_PubKeyY,sm2UserName,DevicePath);
    
    
    memset(PubKeyX,0,2*ECC_MAXLEN+1);
    memset(PubKeyY,0,2*ECC_MAXLEN+1);
    SwitchByte2Char(PubKeyX,b_PubKeyX,ECC_MAXLEN);
    SwitchByte2Char(PubKeyY,b_PubKeyY,ECC_MAXLEN);
    return ret;

}

int D8::SM2_EncBuf(BYTE *InBuf,BYTE *OutBuf,int inlen,char *DevicePath)
{

    int ret=0,temp_inlen;
    
    
    while(inlen>0)
    {
        if(inlen>MAX_ENCLEN)
            temp_inlen=MAX_ENCLEN;
        else
            temp_inlen=inlen;
        ret=(NT_SM2_Enc)(InBuf,OutBuf,temp_inlen,DevicePath);
        if(ret!=0)goto err;
        inlen=inlen-MAX_ENCLEN;
        InBuf=InBuf+MAX_ENCLEN;
        OutBuf=OutBuf+MAX_DECLEN;
    }
err:
    
    
    return ret;

}

int D8::SM2_DecBuf(BYTE *InBuf,BYTE *OutBuf,int inlen,char* pin,char *DevicePath)
{

    int ret=0,temp_inlen;
    
    
    while(inlen>0)
    {
        if(inlen>MAX_DECLEN)
            temp_inlen=MAX_DECLEN;
        else
            temp_inlen=inlen;
        ret=(NT_SM2_Dec)(InBuf,OutBuf,temp_inlen,pin,DevicePath);
        if(ret!=0)goto err;
        inlen=inlen-MAX_DECLEN;
        InBuf=InBuf+MAX_DECLEN;
        OutBuf=OutBuf+MAX_ENCLEN;
    }
err:
    
    
    return ret;

}

int D8::SM2_EncString(char *InString,char *OutString,char *DevicePath)
{

    int inlen=strlen(InString)+1;
    int outlen=(inlen/(MAX_ENCLEN)+1)*SM2_ADDBYTE+inlen;
    BYTE *OutBuf=new BYTE[outlen];
    BYTE *p=OutBuf;
    int ret=0,temp_inlen;
    
    
    while(inlen>0)
    {
        if(inlen>MAX_ENCLEN)
            temp_inlen=MAX_ENCLEN;
        else
            temp_inlen=inlen;
        ret=(NT_SM2_Enc)((BYTE *)InString,OutBuf,temp_inlen,DevicePath);
        if(ret!=0)goto err;
        inlen=inlen-MAX_ENCLEN;
        InString=InString+MAX_ENCLEN;
        OutBuf=OutBuf+MAX_DECLEN;
    }
err:
    memset(OutString,0,2*outlen+1);
    ByteArrayToHexString(p,OutString,outlen);
    
    
    delete [] p;
    return ret;

}

int D8::SM2_DecString(char *InString,char *OutString,char* pin,char *DevicePath)
{

    int inlen=strlen(InString)/2;
    BYTE *InByte=new BYTE[inlen];
    BYTE *p=InByte;
    int ret=0,temp_inlen;
    SwitchChar2Byte(InString,InByte);
    
    
    while(inlen>0)
    {
        if(inlen>MAX_DECLEN)
            temp_inlen=MAX_DECLEN;
        else
            temp_inlen=inlen;
        ret=(NT_SM2_Dec)((BYTE *)InByte,(BYTE *)OutString,temp_inlen,pin,DevicePath);
        if(ret!=0)goto err;
        inlen=inlen-MAX_DECLEN;
        InByte=InByte+MAX_DECLEN;
        OutString=OutString+MAX_ENCLEN;
    }
err:
    
    
    delete [] p;
    return ret;

}

int D8::YtSetPin(char *old_pin,char *new_pin,char *DevicePath )
{
    int ret;
    
    
    ret=(NT_Set_Pin)(old_pin,new_pin,DevicePath);
    
    
    return ret;
}

void D8::SwitchBigInteger2Byte(char *instring,BYTE *outbyte,int *outlen)
{
    int n;char temp[3];
    int inlen=strlen(instring)/2;
    *outlen=0x20;

    for(n=0;n<inlen;n++)
    {
        temp[2]=(char)0;
        strncpy(temp,&instring[n*2],2);
        *outbyte=(BYTE)HexToInt(temp);
        outbyte++;
    }

}


int D8::GetChipID( char *OutChipID,char *DevicePath)
{
    int ret;BYTE b_OutChipID[16];
    
    
    ret=(NT_GetChipID)(b_OutChipID,DevicePath);
    
    
    memset(OutChipID,0,33);
    ByteArrayToHexString(b_OutChipID,OutChipID,16);
    return ret;
}


void D8::EnCode(   BYTE  * InData,BYTE  * OutData,  char *Key  )
{
    BYTE KeyBuf[16];
    memset(KeyBuf,0,16);
    HexStringToByteArrayEx(Key,KeyBuf);
    memcpy(OutData,InData,8);
    EncBySoft(OutData,KeyBuf);
}

void D8::DeCode(   BYTE  * InData, BYTE  * OutData, char *Key  )
{
    BYTE KeyBuf[16];
    memset(KeyBuf,0,16);
    HexStringToByteArrayEx(Key,KeyBuf);
    memcpy(OutData,InData,8);
    DecBySoft(OutData,KeyBuf);
}

void D8::StrDec(  char *InString,char *OutString,char *Key)
{
  int n;
  int len=strlen(InString)/2;
  BYTE KeyBuf[16];
  memset(KeyBuf,0,16);
  HexStringToByteArrayEx(Key,KeyBuf);
  HexStringToByteArray(InString,(BYTE *)OutString);
  for(n=0;n<=(len-8);n=n+8)
    {
      DecBySoft((BYTE *)&OutString[n],KeyBuf);
    }
  return ;
}

void D8::StrEnc(  char *InString,char *OutString,char *Key)
{
  int n;BYTE *outbuf;
  int len=strlen(InString)+1;if(len<8)len=8;
  BYTE KeyBuf[16];
  memset(KeyBuf,0,16);
  HexStringToByteArrayEx(Key,KeyBuf);
  outbuf=new BYTE[len];
  memset(outbuf,0,len);
  memcpy(outbuf,InString,strlen(InString)+1);
  for(n=0;n<=(len-8);n=n+8)
    {
      EncBySoft(&outbuf[n],KeyBuf);
    }
  ByteArrayToHexString(outbuf,OutString,len);
  delete [] outbuf;
}



/*代码转换:从一种编码转为另一种编码*/
int D8::code_convert(const char *from_charset, const char *to_charset, const char *inbuf, size_t inlen, char *outbuf, size_t *outlen)
{
    iconv_t cd;
    char **pin = ( char ** ) &inbuf;
    char **pout = &outbuf;
    cd = iconv_open(to_charset,from_charset);
    if (cd==0) return -1;
    memset(outbuf,0,*outlen);
    if (iconv(cd,pin,&inlen,pout,outlen)==(size_t)-1)
    {
        return -1;
    }
    iconv_close(cd);
    return 0;
}

/*UNICODE码转为GB2312码*/
int D8::u2g(const char *inbuf,size_t inlen,char *outbuf,size_t *outlen)
{
    return code_convert("utf-8","gb2312",inbuf,inlen,outbuf,outlen);
}

/*GB2312码转为UNICODE码*/
int D8::g2u(const char *inbuf,size_t inlen,char *outbuf,size_t *outlen)
{
    return code_convert("gb2312","utf-8",inbuf,inlen,outbuf,outlen);
}

void D8::CloseUsbHandle(char *DevicePath )
{
    
}


int D8::SetHidOnly(  BOOL IsHidOnly,char *DevicePath)
{
    int ret;
    
    
    ret=NT_SetHidOnly(IsHidOnly,DevicePath);
    
    
    return ret;
}
int D8::SetUReadOnly( char *DevicePath)
{
    int ret;
    
    
    ret=NT_SetUReadOnly(DevicePath);
    
    
    return ret;
}
int D8::NT_SetID(  BYTE * InBuf,char *DevicePath)
{
	int n,ret;
	BYTE array_in[500];BYTE array_out[500];

	
	

	array_in[1]=SETID;
	for (n=2;n<10;n++)
	{
		array_in[n]=InBuf[n-2];
	}
    ret=Hanldetransfer(DevicePath,array_in,9,array_out,1,ReportLen_Usua);
	if(ret!=0)return ret;
	if(array_out[0]!=0x0)
	{
		return ErrResult;//表示失败；
	}
	return 0;
}

int  D8::NT_GetProduceDate(  BYTE *OutDate,char *DevicePath)
{
	int ret;
	BYTE array_in[500];

	
	

	array_in[1]=GETSN;
    ret= Hanldetransfer(DevicePath,array_in,1,OutDate,8,ReportLen_Usua);
	return ret;
}

int D8::GetProduceDate(  char *OutDate,char *DevicePath)
{
	int ret;
	BYTE B_OutBDate[500+1];//注意，这里要加多一个字节
	
	
	ret=(NT_GetProduceDate)(B_OutBDate,DevicePath);
	
	
	ByteArrayToHexString(B_OutBDate,OutDate, 8);
	return ret;
}

int D8::SetID(char * Seed,char *DevicePath)
{

	int ret;
	BYTE KeyBuf[8];
	memset(KeyBuf,0,8);
	HexStringToByteArray(Seed,KeyBuf);

	
	
	ret=NT_SetID(KeyBuf,DevicePath);
	
	
	return ret;

}

int D8::Y_SetCal(BYTE *InData,short address,short len,BYTE *password,char *DevicePath )
{
	short addr_l,addr_h;int n;int ret;
	BYTE array_in[500],array_out[500];

	if(len>8)return -87;
	if(address+len-1>2047 || address<0)return ErrAddrOver;
	{addr_h=(address>>8)<<1;addr_l=address&0xff;}

	
	

	array_in[1]=SETCAL;//read 0x10;write 0x11;
	array_in[2]=(BYTE)addr_h;//0x01;//read 0x80 ;write 0x40;
	array_in[3]=(BYTE)addr_l;
	array_in[4]=(BYTE )len;
	for(n=0;n<8;n++)
	{
		array_in[5+n]=*password;
		password++;
	}
	for(n=0;n<len;n++)
	{
		array_in[13+n]=*InData;
		InData++;
	}
    ret= Hanldetransfer(DevicePath,array_in,13+len,array_out,1,ReportLen_Usua);
	if(ret!=0)return ret;

	if(array_out[0]!=0x0)
	{
		return ErrWritePWD;//表示写失败；
	}
	return 0;
}


int  D8::SetCal(char * W_HKey,char *W_LKey,char * new_HKey,char *new_LKey,char *DevicePath)
{

	BYTE ary1[8];BYTE ary2[8];	int ret;short address=0x7f0-8;

	myconvert(W_HKey,W_LKey,ary1);
	myconvert(new_HKey,new_LKey,ary2);

	

	
	ret=Y_SetCal(ary2,address,8,ary1,DevicePath);
	if(ret!=0){return ret;}
	
	
	return ret;

}


void  D8::SnToProduceDate(char* InSn,char *OutProduceDate)
{

   char temp[5]="";
   memset(temp,0,5);
   strncpy(temp,&InSn[0],2);
   int year=2000 + HexToInt(temp);
   strncpy(temp,&InSn[2],2);
   int month=  HexToInt(temp);
   strncpy(temp,&InSn[4],2);
   int day=  HexToInt(temp);
   strncpy(temp,&InSn[6],2);
   int hour=  HexToInt(temp);
   strncpy(temp,&InSn[8],2);
   int minutes=  HexToInt(temp);
   strncpy(temp,&InSn[10],2);
   int sec=  HexToInt(temp);
   strncpy(temp,&InSn[12],4);
   int sn=  HexToInt(temp);
    sprintf(OutProduceDate,"%d年%d月%d日%d时%d分%d秒--序号：%d", year,month,day,hour,minutes,sec,sn);
}

int D8::HanldetransferEx(BYTE *InBuf,int InBufLen,BYTE *OutBuf,int OutBufLen,char *DevicePath)
{
	int ret;
	libusb_device_handle * DevHandle=NULL;
	ret=MyOpenPath(DevicePath,&DevHandle);
	if(ret!=0)return ret;
	sem_t * Sem =   sem_open("ex_sim",FLAGS , PERMS, 1);

	try
	{
		sem_wait(Sem);
		if(InBufLen>0)
		{
			ret=libusb_control_transfer(DevHandle,0x21,9,0x0302,0,(unsigned char *)InBuf,InBufLen+1,0);
			if(ret<1)
			{
				libusb_close(DevHandle);sem_post(Sem);sem_close(Sem);
				return ErrSendData;
			}
		}
		if(OutBufLen>0)
		{
			ret=libusb_control_transfer(DevHandle,0xa1,1,0x0301,0,(unsigned char *)OutBuf,OutBufLen+1,0);
			if(ret<1)
			{
				libusb_close(DevHandle);sem_post(Sem);sem_close(Sem);
				return ErrGetData;
			}
		}
		libusb_close(DevHandle);sem_post(Sem);sem_close(Sem);
	}
	catch(...)
	{
		libusb_close(DevHandle);sem_post(Sem);sem_close(Sem);
		return -92;
	}

	memcpy(&ret,OutBuf,sizeof(short));
    if(ret>0)ret=ret-0x10000;
	return ret;
}

int D8::NT_DownLoadData(BYTE* Buf,BYTE BufLen,char *DevicePath )
{

	BYTE array_in[MAX_BUF_SIZE],array_out[MAX_BUF_SIZE];
	array_in[1]=YTDOWNLOAD;
	array_in[2]=BufLen;
	memcpy(&array_in[3],Buf,BufLen);
    return HanldetransferEx(array_in,2+BufLen,array_out,sizeof(short),DevicePath);

}

int  D8::DownLoadData(BOOL bIsEnc,BYTE* Buf,DWORD BufLen,char *DevicePath)
{
	
	DWORD n; int ret;DWORD SendLen;DWORD MaxDownloadSize;
	if(bIsEnc)
	{
		MaxDownloadSize=DOWNLOAD_SIZE/8*8;
	}
	else
	{
		MaxDownloadSize=DOWNLOAD_SIZE;
	}
	
	ret=NT_StartDownLoad(bIsEnc,DevicePath);
	if(ret==0)
	{
		for(n=0;n<BufLen;n=n+MaxDownloadSize)
		{
			SendLen=BufLen-n;
			if(SendLen>MaxDownloadSize)SendLen=MaxDownloadSize;
			ret=NT_DownLoadData(&Buf[n],(BYTE)SendLen,DevicePath);
			if(ret!=0)break;
		}
	}
	
	
	return ret;
}

void D8::CopyKeyToBuf(BYTE *KeyBuf,char *Key)
{
	int len=(int)strlen(Key);
	memset(KeyBuf,0xff,MAX_KEY_LEN);
	if(strlen(Key)>MAX_KEY_LEN)
	{
		memcpy(KeyBuf,Key,MAX_KEY_LEN);
	}
	else
	{
		memcpy(KeyBuf,Key,len);
	}
}


int D8::NT_StartDownLoad(BOOL bIsEnc,char *DevicePath )
{
	BYTE array_in[MAX_BUF_SIZE],array_out[MAX_BUF_SIZE];

	array_in[1]=START_DOWNLOAD;
	if(bIsEnc)
	{
		array_in[2]=1;
	}
	else
	{
		array_in[2]=0;
	}

    return HanldetransferEx(array_in,2,array_out,sizeof(short),DevicePath);

}

int D8::NT_RunFun(char* FunctionName,DWORD FunNameLen,char *DevicePath )
{

	BYTE array_in[MAX_BUF_SIZE],array_out[MAX_BUF_SIZE];
	array_in[1]=RUN_FUNCTION;
	memcpy(&array_in[2],FunctionName,FunNameLen);
    return HanldetransferEx(array_in,1+FunNameLen,array_out,sizeof(short),DevicePath);

}

int  D8::RunFuntion(char* FunctionName,char *DevicePath)
{
	int ret;
	int FunNameLen=(int)strlen(FunctionName)+1;
	if(FunNameLen>MAX_FUNNAME)
	{
		return FUNCTION_LENGTH_NAME_MORE_THEN_25;
	}
	
	
	ret=NT_RunFun(FunctionName,FunNameLen,DevicePath);
	
	
	return ret;
}

int D8::NT_SetDownLodKey(char* OldKey,char* NewKey,char *OutVerfCode,char *DevicePath )
{

	BYTE array_in[MAX_BUF_SIZE],array_out[MAX_BUF_SIZE];
	array_in[1]=SET_DOWNLOAD_KEY;
	
	//memcpy(&array_in[2],OldKey,len_1);
	//memcpy(&array_in[2+MAX_KEY_LEN],NewKey,len_2);
	//if(len_1==0)memset(&array_in[2],0xff,MAX_KEY_LEN);
	//if(len_2==0)memset(&array_in[2+MAX_KEY_LEN],0xff,MAX_KEY_LEN);
	CopyKeyToBuf(&array_in[2],OldKey);
	CopyKeyToBuf(&array_in[2+MAX_KEY_LEN],NewKey);
    int ret= HanldetransferEx(array_in,1+MAX_KEY_LEN*2,array_out,sizeof(short)+VERF_CODE_SIZE,DevicePath);
	ByteArrayToHexString(&array_out[sizeof(short)], OutVerfCode,VERF_CODE_SIZE);
	return ret;

}

int  D8::SetDownLodKey(char *OldKey,char* NewKey,char *OutVerfCode,char *DevicePath)
{
	int ret;
/*	int len_1=(int)strlen(OldKey);
	int len_2=(int)strlen(NewKey);
	if(len_1>MAX_KEY_LEN || len_2>MAX_KEY_LEN)
	{
		return OVER_KEY_LEN;
	}*/
	
	
	ret=NT_SetDownLodKey(OldKey,NewKey,OutVerfCode,DevicePath);
	
	
	return ret;
}

int D8::NT_OpenKey(char *VerfCode,char *DevicePath )
{

	BYTE array_in[MAX_BUF_SIZE],array_out[MAX_BUF_SIZE];
	array_in[1]=OPEN_KEY;

	HexStringToByteArray(VerfCode,&array_in[2]);
    return  HanldetransferEx(array_in,1+MAX_KEY_LEN,array_out,sizeof(short),DevicePath);
}

int  D8::OpenKey(char *VerfCode,char *DevicePath)
{
	int ret;
	int len=(int)strlen(VerfCode);
	if(len>MAX_KEY_LEN)
	{
		return OVER_KEY_LEN;
	}
	
	
	ret=NT_OpenKey(VerfCode,DevicePath);
	
	
	return ret;
}

int  D8::ContinuRun(char *InPath)
{

	return GetBufNoCarry(COUNTINU_RUNCTION,NULL,0,InPath);;
}


int  D8::CloseKey(char *DevicePath)
{
	
	return GetBufNoCarry(CLOSE_KEY,NULL,0,DevicePath);;
}


int D8::NT_SetVar(BYTE* Buf,DWORD MemBeginPos,BYTE BufLen,char *DevicePath )
{

	BYTE array_in[MAX_BUF_SIZE],array_out[MAX_BUF_SIZE];
	array_in[1]=SET_VAR;
	memcpy(&array_in[2],&MemBeginPos,sizeof(D8_USHORT));
	array_in[2+sizeof(D8_USHORT)]=BufLen;
	array_in[2+sizeof(D8_USHORT)+1]=0;//只用一个字节，用2个字节的作用是为了与DEBUGer相同
	memcpy(&array_in[2+sizeof(D8_USHORT)+sizeof(D8_USHORT)],Buf,BufLen);
    return HanldetransferEx(array_in,1+sizeof(D8_USHORT)+sizeof(D8_USHORT)+BufLen,array_out,sizeof(short),DevicePath);

}


int  D8::SetVar(BYTE *Buf,DWORD MemBeginPos,DWORD BufLen,char *DevicePath)
{
	int ret=0;DWORD n;DWORD SendLen;
	
	
	for(n=0;n<BufLen;n=n+TRANSFER_VAR_SIZE)
	{
		SendLen=BufLen-n;
		if(SendLen>TRANSFER_VAR_SIZE)SendLen=TRANSFER_VAR_SIZE;
		ret=NT_SetVar(&Buf[n],MemBeginPos,(BYTE)SendLen,DevicePath);
		if(ret!=0)break;
		MemBeginPos=MemBeginPos+TRANSFER_VAR_SIZE;
	}
	
	
	return ret;
}

int D8::NT_GetVar(BYTE* OutBuf,DWORD MemBeginPos,BYTE OutBufLen,char *DevicePath )
{
	int ret;
	BYTE array_in[MAX_BUF_SIZE],array_out[MAX_BUF_SIZE];
	array_in[1]=GET_VAR;
	memcpy(&array_in[2],&MemBeginPos,sizeof(D8_USHORT));
	array_in[2+sizeof(D8_USHORT)]=OutBufLen;
	array_in[2+sizeof(D8_USHORT)+1]=0;
    ret= HanldetransferEx(array_in,1+sizeof(D8_USHORT)+sizeof(D8_USHORT),array_out,sizeof(short)+OutBufLen,DevicePath);
	memcpy(OutBuf,&array_out[sizeof(short)],OutBufLen);
	return ret;

}


int  D8::GetVar(BYTE *OutBuf,DWORD MemBeginPos,DWORD OutBufLen,char *DevicePath)
{
	int ret=0;DWORD n;DWORD SendLen;
	
	
	for(n=0;n<OutBufLen;n=n+TRANSFER_VAR_SIZE)
	{
		SendLen=OutBufLen-n;
		if(SendLen>TRANSFER_VAR_SIZE)SendLen=TRANSFER_VAR_SIZE;
		ret=NT_GetVar(&OutBuf[n],MemBeginPos,(BYTE)SendLen,DevicePath);
		if(ret!=0)break;
		MemBeginPos=MemBeginPos+TRANSFER_VAR_SIZE;
	}
	
	
	return ret;
}

int D8::NT_GetApiParam(BYTE* OutBuf,BYTE *OutLen,char *DevicePath )
{
	int ret;
	BYTE array_in[MAX_BUF_SIZE],array_out[MAX_BUF_SIZE];
	array_in[1]=GET_API_PARAM;
	
    ret= HanldetransferEx(array_in,1,array_out,MAX_BUF_SIZE,DevicePath);
	*OutLen=array_out[sizeof(short)];
   if(*OutLen)
   {
	   memcpy(OutBuf,&array_out[sizeof(short)+1],*OutLen);
   }
   else
   {
	memcpy(OutBuf,&array_out[sizeof(short)+1],GETDATA_BUF_SIZE);
   }
	return ret;

}

int  D8::GetApiParam(BYTE *OutBuf,char *DevicePath)
{
	int ret=0;BYTE OutLen=0;DWORD count=0;
	
	
	while(OutLen==0)
	{
		ret=NT_GetApiParam(&OutBuf[count],&OutLen,DevicePath);
		count=count+GETDATA_BUF_SIZE;
		if(ret!=0)break;
	}
	
	
	return ret;
}

int D8::NT_SetApiParam(BYTE* Buf,BYTE BufLen,char *DevicePath )
{
	int ret;
	BYTE array_in[MAX_BUF_SIZE],array_out[MAX_BUF_SIZE];
	array_in[1]=SET_API_PARAM;
	array_in[2]=BufLen;
	memcpy(&array_in[3],Buf,BufLen);
    ret= HanldetransferEx(array_in,1+1+BufLen,array_out,sizeof(short),DevicePath);
	
	return ret;

}

int  D8::SetApiParam(BYTE *Buf,DWORD InLen,char *DevicePath)
{
	int ret=0;DWORD n;DWORD SendLen;
	
	
	for(n=0;n<InLen;n=n+GETDATA_BUF_SIZE)
	{
		SendLen=InLen-n;
		if(SendLen>GETDATA_BUF_SIZE)SendLen=GETDATA_BUF_SIZE;
		ret=NT_SetApiParam(&Buf[n],(BYTE)SendLen,DevicePath);
		if(ret!=0)break;
	}
	
	
	return ret;
}

int D8::FindD8(int pos,char *VerfCode,char *KeyPath)
{
	int ret;int count=0,D8_count=0;
    while (true)
	{
		ret=FindPort(count,KeyPath);
		if(ret!=0)return ret;
		ret=OpenKey(VerfCode,KeyPath);
		CloseKey(KeyPath);
		if(ret==0)
		{
			if(pos==D8_count)return 0;
			D8_count++;
		}
		count++;
	}

}

void D8::CovertPwd8(char *Pwd,char *Pwd8)
{
	UINT32 tmp=HexToInt(Pwd);
    sprintf(Pwd8,"%08x",tmp);
}


int  D8::WriteEprom(BYTE *InBuf,DWORD Addr,DWORD len,char *HKey,char *LKey,char *DevicePath)
{
	int ret=0;DWORD n;DWORD SendLen;
	char HKey_8[NEW_PWD_LEN]="",LKey_8[NEW_PWD_LEN]="";
	CovertPwd8(HKey,HKey_8);CovertPwd8(LKey,LKey_8);
	
	
	for(n=0;n<len;n=n+NEW_EPROM_TRANSFER_SIZE)
	{
		SendLen=len-n;
		if(SendLen>NEW_EPROM_TRANSFER_SIZE)SendLen=NEW_EPROM_TRANSFER_SIZE;
		ret=NT_WriteEprom(&InBuf[n],Addr+n,(BYTE)SendLen,HKey_8,LKey_8,DevicePath);
		if(ret!=0)break;
	}
	
	
	return ret;

	return ret;
}

int  D8::NT_WriteEprom(BYTE *InBuf,DWORD Addr,BYTE len,char *HKey,char *LKey,char *DevicePath)
{
	int ret;
	BYTE array_in[MAX_BUF_SIZE],array_out[MAX_BUF_SIZE];

	array_in[1]=WRITE_NEW_EPROM;
	memcpy(&array_in[2],&Addr,sizeof(D8_USHORT));
	array_in[2+sizeof(D8_USHORT)]=len;
	memcpy(&array_in[2+sizeof(D8_USHORT)+1],HKey,NEW_PWD_LEN);
	memcpy(&array_in[2+sizeof(D8_USHORT)+1+NEW_PWD_LEN],LKey,NEW_PWD_LEN);
	memcpy(&array_in[2+sizeof(D8_USHORT)+1+2*NEW_PWD_LEN],InBuf,len);
    ret= HanldetransferEx(array_in,1+sizeof(D8_USHORT)+1+2*NEW_PWD_LEN+len,array_out,sizeof(short),DevicePath);
	return ret;
}

int  D8::ReadEprom(BYTE *OutBuf,DWORD Addr,DWORD len,char *HKey,char *LKey,char *DevicePath)
{
	int ret=0;DWORD n;DWORD SendLen;
	char HKey_8[NEW_PWD_LEN]="",LKey_8[NEW_PWD_LEN]="";
	CovertPwd8(HKey,HKey_8);CovertPwd8(LKey,LKey_8);
	
	
	for(n=0;n<len;n=n+NEW_EPROM_TRANSFER_SIZE)
	{
		SendLen=len-n;
		if(SendLen>NEW_EPROM_TRANSFER_SIZE)SendLen=NEW_EPROM_TRANSFER_SIZE;
		ret=NT_ReadEprom(&OutBuf[n],Addr+n,(BYTE)SendLen,HKey_8,LKey_8,DevicePath);
		if(ret!=0)break;
	}
	
	
	return ret;
}

int D8::NT_ReadEprom(BYTE* OutBuf,DWORD Addr,BYTE len,char *HKey,char *LKey,char *DevicePath )
{
	int ret;
	BYTE array_in[MAX_BUF_SIZE],array_out[MAX_BUF_SIZE];
	array_in[1]=READ_NEW_EPROM;
	memcpy(&array_in[2],&Addr,sizeof(D8_USHORT));
	array_in[2+sizeof(D8_USHORT)]=len;
	memcpy(&array_in[2+sizeof(D8_USHORT)+1],HKey,NEW_PWD_LEN);
	memcpy(&array_in[2+sizeof(D8_USHORT)+1+NEW_PWD_LEN],LKey,NEW_PWD_LEN);
    ret= HanldetransferEx(array_in,1+sizeof(D8_USHORT)+1+2*NEW_PWD_LEN,array_out,sizeof(short)+len,DevicePath);
	memcpy(OutBuf,&array_out[sizeof(short)],len);
	return ret;

}

int  D8::NewSetReadPassword(char *OldWriteHKey,char *OldWriteLKey,char *NewHKey,char *NewLKey,char *DevicePath)
{
    return Sub_SetPwd_28K(false,OldWriteHKey,OldWriteLKey,NewHKey,NewLKey,DevicePath);
}

int  D8::NewSetWritePassword(char *OldWriteHKey,char *OldWriteLKey,char *NewHKey,char *NewLKey,char *DevicePath)
{
    return Sub_SetPwd_28K(true,OldWriteHKey,OldWriteLKey,NewHKey,NewLKey,DevicePath);
}

int  D8::Sub_SetPwd_28K(BOOL IsSetWritePwd,char *OldWriteHKey,char *OldWriteLKey,char *NewHKey,char *NewLKey,char *DevicePath)
{
	int  ret;
	char OldWriteHKey_8[NEW_PWD_LEN]="",OldWriteLKey_8[NEW_PWD_LEN]="";
	char NewHKey_8[NEW_PWD_LEN]="",NewLKey_8[NEW_PWD_LEN]="";
	CovertPwd8(OldWriteHKey,OldWriteHKey_8);CovertPwd8(OldWriteLKey,OldWriteLKey_8);
	CovertPwd8(NewHKey,NewHKey_8);CovertPwd8(NewLKey,NewLKey_8);
	
	
	ret=NT_SetPwd_28K(IsSetWritePwd,OldWriteHKey_8,OldWriteLKey_8,NewHKey_8,NewLKey_8,DevicePath);
	
	
	return ret;
}

int D8::NT_SetPwd_28K(BOOL IsSetWritePwd,char *OldWriteHKey,char *OldWriteLKey,char *NewHKey,char *NewLKey,char *DevicePath )
{
	int ret;
	BYTE array_in[MAX_BUF_SIZE],array_out[MAX_BUF_SIZE];
	array_in[1]=SET_PWD;
	array_in[2]=0;
	if(IsSetWritePwd)array_in[2]=1;
	memcpy(&array_in[3],OldWriteHKey,NEW_PWD_LEN);
	memcpy(&array_in[3+NEW_PWD_LEN],OldWriteLKey,NEW_PWD_LEN);
	memcpy(&array_in[3+2*NEW_PWD_LEN],NewHKey,NEW_PWD_LEN);
	memcpy(&array_in[3+3*NEW_PWD_LEN],NewLKey,NEW_PWD_LEN);
    ret= HanldetransferEx(array_in,1+1+4*NEW_PWD_LEN,array_out,sizeof(short),DevicePath);
	return ret;

}

int D8::GetFuncVer(int *Version,char *DevicePath)
{
	int ret;
    
	
	ret=(NT_GetFunctionVersion)(Version,DevicePath);
	
	
	return ret;
}

int D8::NT_GetFunctionVersion(int *FunctionVersion,char *DevicePath)
{
	int ret;
	BYTE array_in[MAX_BUF_SIZE];BYTE array_out[MAX_BUF_SIZE];
    
    
	array_in[1]=GETFUNCVER;
    ret= Hanldetransfer(DevicePath,array_in,1,array_out,3,3);
	if(ret!=0)return ret;
	*FunctionVersion=array_out[1];
	return 0;
}

int D8::GetBufCarryData(BYTE Cmd,BYTE *Inbuf,int Inlen,BYTE *OutData,int Outlen,char *DevicePath)
{
    int ret;
	BYTE array_in[MAX_BUF_SIZE];BYTE array_out[MAX_BUF_SIZE];
    //
    //

	array_in[1]=Cmd;
	if(Inlen>0)memcpy(&array_in[2],Inbuf,Inlen);
	
	
    ret= HanldetransferEx(array_in,Inlen+1,array_out,Outlen+sizeof(USHORT),DevicePath);
	
	
	if(Outlen>0)memcpy(OutData,&array_out[sizeof(short)],Outlen);

	return ret;
}

int D8::GetBufNoCarry(BYTE Cmd,BYTE *OutData,int len,char *DevicePath)
{
	return GetBufCarryData(Cmd,NULL,0,OutData,len,DevicePath);
}

int D8::GetLimitDate(int *Year,BYTE *Month,BYTE *Day,char *DevicePath)
{
	int ret;
	BYTE Buf[6];
	ret=GetBufNoCarry(GET_LIMIT_DATE,Buf,6,DevicePath);
	*Year=2000+Buf[0];
	*Month=Buf[1];
	*Day=Buf[2];
	return ret;
}

int D8::GetUserID(UINT16 *UserID,char *DevicePath)
{
	*UserID=0;
	return GetBufNoCarry(GET_USER_ID,(BYTE *)UserID,sizeof(UINT16),DevicePath);//这里只取两个字节
}


int D8::GetLeaveNumber(UINT16 *LeaveNumber,char *DevicePath)
{
	*LeaveNumber=0;
	return GetBufNoCarry(GET_LEAVE_NUMBER,(BYTE *)LeaveNumber,sizeof(UINT16),DevicePath);//这里只取两个字节
}


int D8::GetLeaveDays(INT32 *LeaveDay,char *CurDate,char *DevicePath)
{
	
	return GetBufCarryData(GET_LEAVE_DAYS,(BYTE *)CurDate,(int)(strlen(CurDate)+1),(BYTE *)LeaveDay,sizeof(UINT32),DevicePath);;
}

int D8::CheckBind(BOOL bIsAdd,char *MacAddr,char *DevicePath)
{
	BYTE BindBuf[MAX_BIND_MAC_SIZE+1]; BYTE OutBuf[1];int ret;
	BindBuf[0]=0;
	if(bIsAdd)BindBuf[0]=1;
	if((strlen(MacAddr)+1)>=MAX_BIND_MAC_SIZE)
	{
		return OVER_BIND_SIZE;
	}
	strcpy((char *)&BindBuf[1],MacAddr);
	ret= GetBufCarryData(CHECK_BIND,BindBuf,(int)(1+strlen(MacAddr)+1),OutBuf,1,DevicePath);
	if(ret!=0)return ret;
	return OutBuf[0];
}

int D8::CheckNumber(char *DevicePath)
{
	return GetBufNoCarry(CHECK_NUMBER,NULL,0,DevicePath);;
}

int D8::CheckDate(char *InDate,char *DevicePath)
{
	return GetBufCarryData(CHECK_DATE,(BYTE *)InDate,(int)(1+strlen(InDate)+1),NULL,0,DevicePath);;
}

int D8::SubAuth(BYTE Cmd,BYTE *Buf, char *Key,char *DevicePath)
{
	BYTE KeyBuf[MAX_KEY_LEN];
	CopyKeyToBuf(KeyBuf,Key);
	EncBySoft(Buf,KeyBuf);
	return GetBufCarryData(Cmd,Buf,8,NULL,0,DevicePath);
}


void D8::SubMakeDateAuth(UINT16 CurYear,BYTE CurMonth,BYTE CurDay,
                          UINT16 LimitYear,BYTE LimitMonth,BYTE LimitDay,UINT16 UserID,BYTE *OutBuf)
{
	if(CurYear>2000)CurYear=CurYear-2000;
	if(LimitYear>2000)LimitYear=LimitYear-2000;
	memcpy(OutBuf,&UserID,sizeof(UINT16));
	OutBuf[2]=(BYTE)CurYear;OutBuf[3]=CurMonth;OutBuf[4]=CurDay;
	OutBuf[5]=(BYTE)LimitYear;OutBuf[6]=LimitMonth;OutBuf[7]=LimitDay;
}

int D8::DateAuth(UINT16 CurYear,BYTE CurMonth,BYTE CurDay,
				 UINT16 LimitYear,BYTE LimitMonth,BYTE LimitDay,UINT16 UserID,
				 char *Key,char *DevicePath)
{
	BYTE Buf[8];
    SubMakeDateAuth(CurYear,CurMonth,CurDay,LimitYear,LimitMonth,LimitDay,UserID,Buf);
	return SubAuth(SET_DATE_AUTH,Buf,Key,DevicePath);
	
}

void D8::SubMakeNumberAuth(UINT16 Number,UINT16 UserID,BYTE *OutBuf)
{
	BYTE temp;
	memcpy(OutBuf,&UserID,sizeof(UINT16));
	//交换用户号
	temp=OutBuf[0];
	OutBuf[0]=OutBuf[1];
	OutBuf[1]=temp;

	OutBuf[2]=0;//时间授权标记
	memcpy(&OutBuf[3],&Number,sizeof(UINT16));
}

int D8::NumberAuth(UINT16 Number,UINT16 UserID,char *Key,char *DevicePath)
{
	BYTE Buf[8];
	SubMakeNumberAuth(Number,UserID,Buf);
	return SubAuth(SET_NUMBER_AUTH,Buf,Key,DevicePath);

}

int D8::BindAuth(BOOL bReBind,UINT8 BindCount,UINT16 UserID,char *Key,char *DevicePath)
{
	BYTE Buf[8];
	SubMakeBindAuth(bReBind,BindCount,UserID,Buf);
	return SubAuth(SET_BIND_AUTH,Buf,Key,DevicePath);

}

int D8::GetBindInfo(BYTE *LimitBindCount,BYTE *AlreadyBindCount,char *DevicePath)
{
	BYTE Buf[2];
	int ret=GetBufNoCarry(GET_BIND_INFO,Buf,2,DevicePath);
	*LimitBindCount=Buf[0];
	*AlreadyBindCount=Buf[1];
	return ret;
}

void D8::SubMakeAuth(BYTE *Buf,char *Key,char *OutAuth)
{
	BYTE KeyBuf[MAX_KEY_LEN];
	CopyKeyToBuf(KeyBuf,Key);
	EncBySoft(Buf,KeyBuf);
	ByteArrayToHexString(Buf,OutAuth,8);
}

void D8::SubMakeBindAuth(BOOL bReBind,UINT8 BindCount,UINT16 UserID,BYTE *OutBuf)
{
	BYTE temp;
	memcpy(OutBuf,&UserID,sizeof(UINT16));
	//交换用户号
	temp=OutBuf[0];
	OutBuf[0]=OutBuf[1];
	OutBuf[1]=temp;

	OutBuf[2]=1;//绑定授权标记
	OutBuf[3]=BindCount;
	OutBuf[4]=0;
	if(bReBind)OutBuf[4]=1;
}

void D8::MakeBindAuth(BOOL bReBind,UINT8 BindCount,UINT16 UserID,char *Key,char *OutAuth)
{
	BYTE Buf[8];
	SubMakeBindAuth(bReBind,BindCount,UserID,Buf);
	SubMakeAuth(Buf,Key,OutAuth);
}

void D8::MakeNumberAuth(UINT16 Number,UINT16 UserID,char *Key,char *OutAuth)
{
	BYTE Buf[8];
	SubMakeNumberAuth(Number,UserID,Buf);
	SubMakeAuth(Buf,Key,OutAuth);
}

void D8::MakeDateAuth(INT16 CurYear,BYTE CurMonth,BYTE CurDay,
					  UINT16 LimitYear,BYTE LimitMonth,BYTE LimitDay,UINT16 UserID,
					  char *Key,char *OutAuth)
{
	BYTE Buf[8];
    SubMakeDateAuth(CurYear,CurMonth,CurDay,LimitYear,LimitMonth,LimitDay,UserID,Buf);
	SubMakeAuth(Buf,Key,OutAuth);
}

int D8::UpdateAuth(BYTE Flag,char *Auth,char *DevicePath)
{
	BYTE Buf[8];
	HexStringToByteArray(Auth,Buf);
	Flag=Flag+SET_NUMBER_AUTH;
	return GetBufCarryData(Flag,Buf,8,NULL,0,DevicePath);

}
void D8::DecString(  char *InString,char *OutString,char *Key)
{
	int n;
	int len=strlen(InString)/2;
	BYTE KeyBuf[16];
	memset(KeyBuf,0,16);
	HexStringToByteArrayEx(Key,KeyBuf);
	HexStringToByteArray(InString,(BYTE *)OutString);
	for(n=0;n<=(len-8);n=n+8)
	{
		DecBySoft((BYTE *)&OutString[n],KeyBuf);
	}
	return ;
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
int  D8::GetDyDataSize(UINT32 MemAddr,D8_SHORT index,D8_USHORT *OutSize,char *InPath)
{
#define  GETDYDATASIZE_LEN sizeof(UINT32)+sizeof(D8_USHORT)
	int ret;BYTE InBuf[GETDYDATASIZE_LEN],OutBuf[sizeof(D8_USHORT)];
	memcpy(&InBuf,&MemAddr,sizeof(UINT32));
	memcpy(&InBuf[sizeof(UINT32)],&index,sizeof(D8_USHORT));
	ret= GetBufCarryData(GET_DY_DATA_SIZE,InBuf,GETDYDATASIZE_LEN,OutBuf,sizeof(D8_USHORT),InPath);
	memcpy(OutSize,OutBuf,sizeof(D8_USHORT));
	return ret;
}

int  D8::SubGetDyDataValueByMemAddr(UINT32 MemAddr,D8_SHORT index,D8_USHORT pos,BYTE *OutBuf,D8_USHORT DataSize,char *InPath)
{
#define  GETDYDATA_LEN sizeof(UINT32)+sizeof(D8_USHORT)*2
	int ret;BYTE InBuf[GETDYDATA_LEN];
	memcpy(&InBuf,&MemAddr,sizeof(UINT32));
	memcpy(&InBuf[sizeof(UINT32)],&index,sizeof(D8_USHORT));
	memcpy(&InBuf[sizeof(UINT32)+sizeof(D8_USHORT)],&pos,sizeof(D8_USHORT));
	ret= GetBufCarryData(GET_DY_DATA_VALUE,InBuf,GETDYDATA_LEN,OutBuf,DataSize,InPath);

	return ret;
}

int  D8::GetDyValue(UINT32 MemAddr,D8_SHORT index,BYTE *OutBuf,D8_USHORT InDataSize,char *InPath)
{
	int ret;int DataSize=InDataSize;
	D8_USHORT pos=0,Sendout_Len;
	while(DataSize>0)
	{
		Sendout_Len=128;
		if(DataSize<128)Sendout_Len=DataSize;
		ret=SubGetDyDataValueByMemAddr(MemAddr,index,pos,&OutBuf[pos],Sendout_Len,InPath);
		if(ret!=0)return ret;
		pos=pos+128;
		DataSize=DataSize-128;
	}
	return 0;

}

int  D8::GetDyArr(UINT32 MemAddr,BYTE **OutBuf,char *InPath)
{
	D8_USHORT ArrLen;int ret;
	ret=GetDyDataSize(MemAddr,-1,&ArrLen,InPath);
	if(ret!=0)return ret;
	*OutBuf=(BYTE *)calloc(ArrLen,sizeof(BYTE));
	return GetDyValue(MemAddr,-1,*OutBuf,ArrLen,InPath);
}

int  D8::GetDyString(UINT32 MemAddr,char **OutString,char *InPath)
{
	return GetDyArr(MemAddr,(BYTE **)OutString,InPath);
}

int D8::GetDyArrString(UINT32 MemAddr,char *OutString[],char *InPath)
{
	D8_USHORT ArrLen;int ret;int n;
	ret=GetDyDataSize(MemAddr,-1,&ArrLen,InPath);
	if(ret!=0)return ret;
	ArrLen=ArrLen/sizeof(D8_USHORT);//注意，这里一定要除以sizeof(D8_USHORT)
	for(n=0;n<ArrLen;n++)
	{
		D8_USHORT StrLen;
		ret=GetDyDataSize(MemAddr,n,&StrLen,InPath);
		if(ret!=0)return ret;
		OutString[n]=(char *)calloc(StrLen,sizeof(BYTE));
		ret=GetDyValue(MemAddr,n,(BYTE *)OutString[n],StrLen,InPath);
		if(ret!=0)return ret;
	}
	return 0;
}

int  D8::SubSetDyDataValueByMemAddr(UINT32 MemAddr,D8_SHORT index,BYTE *InBuf,D8_USHORT DataSize,char *InPath)
{
	int ret;BYTE array_out[MAX_BUF_SIZE];
	memcpy(array_out,&MemAddr,sizeof(UINT32));
	memcpy(&array_out[sizeof(UINT32)],&index,sizeof(D8_USHORT));
	memcpy(&array_out[sizeof(UINT32)+sizeof(D8_USHORT)],&DataSize,sizeof(D8_USHORT));
	memcpy(&array_out[sizeof(UINT32)+sizeof(D8_USHORT)*2],InBuf,DataSize);
	ret= GetBufCarryData(SET_DY_DATA_VALUE,array_out,sizeof(UINT32)+sizeof(D8_USHORT)*2+DataSize,NULL,0,InPath);

	return ret;
}

int  D8::SetDyValue(UINT32 MemAddr,D8_SHORT index,BYTE *InBuf,D8_USHORT InDataSize,char *InPath)
{
	int ret;int DataSize=InDataSize;
	D8_USHORT pos=0,Sendout_Len;
	while(DataSize>0)
	{
		Sendout_Len=128;
		if(DataSize<128)Sendout_Len=DataSize;
		ret=SubSetDyDataValueByMemAddr(MemAddr,index,&InBuf[pos],Sendout_Len,InPath);
		if(ret!=0)return ret;
		pos=pos+128;
		DataSize=DataSize-128;
	}
	return 0;
}

int  D8::ClearDyData(UINT32 DyMemBeginPos,char *InPath)
{
	BYTE array_out[MAX_BUF_SIZE];
	memcpy(array_out,&DyMemBeginPos,sizeof(D8_SHORT));
	return GetBufCarryData(CLEAR_DY,array_out,sizeof(D8_USHORT),NULL,0,InPath);

}

int  D8::SetDyString(UINT32 MemAddr,char *InString,char *InPath)
{
	return SetDyValue(MemAddr,-1,(BYTE *)InString,strlen(InString)+1,InPath);
}

int  D8::SetDyArr(UINT32 MemAddr,BYTE *InBuf,D8_USHORT InLen,D8_USHORT TypeSize,char *InPath)
{
	return SetDyValue(MemAddr,-1,InBuf,InLen*TypeSize,InPath);
}

int  D8::SetDyArrString(UINT32 MemAddr,char *ArrStr[],D8_USHORT ArrLen,char *InPath)
{
	D8_USHORT TotalLen=sizeof(D8_USHORT)*ArrLen;int n;
	BYTE *Buf=(BYTE *)calloc(ArrLen,sizeof(D8_USHORT));
	memset(Buf,0,TotalLen);
	int ret=SetDyValue(MemAddr,-1,Buf,TotalLen,InPath);
	free(Buf);
	if(ret!=0)return ret;
	for(n=0;n<ArrLen;n++)
	{
		ret=SetDyValue(MemAddr,n,(BYTE *)ArrStr[n],strlen(ArrStr[n])+1,InPath);
		if(ret!=0)return ret;
	}
	return 0;
}

