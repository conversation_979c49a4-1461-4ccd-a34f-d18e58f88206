#include "stdafx.h"
#include <sys/types.h>
#include <dirent.h>
#include "Language.h"

#define DEFAULT_LANG_NAME	("简体中文")

CLanguage::CLanguage()
{
    m_strCheckedLang = DEFAULT_LANG_NAME;
}

CLanguage::~CLanguage()
{

}

void CLanguage::InitLanguage(CMyString strPath)
{
    m_strPath = strPath;

    char	szSearch[STR_MAX_PATH] = {0};

    DIR* fdFind = NULL;

    strcpy(szSearch, strPath.C_Str());

    // 查找语言文件
    fdFind = opendir(szSearch);

    if (fdFind != NULL)
    {
        m_languageNames.clear();
        struct dirent *direntp;

        while ((direntp =  readdir(fdFind)) != NULL )
        {
            // 跳过当前目录以及父目录
            if(strcmp(direntp->d_name, ".") == 0 || strcmp(direntp->d_name, "..") == 0)
            {
                continue;
            }

            CMyString strNameWithExt = CMyString(direntp->d_name);
            CMyString strName = strNameWithExt.Left(strNameWithExt.Find("."));
            CMyString strType   = strNameWithExt.Right(strNameWithExt.GetLength() - strNameWithExt.Find("."));

            if(strcmp(strType.C_Str(), ".ini") == 0)
            {
                m_languageNames.push_back(strName);
            }
        }
        closedir(fdFind);
    }

    CMyString strLangName = DEFAULT_LANG_NAME;
    g_Global.m_IniConfig.GetValue(CONFIG_FILE_SECTION_LANGUAGE, CONFIG_FILE_ITEM_CHECKED_LANG, strLangName);

    BOOL bFind = FALSE;
    for (int i=0; i<GetLanguageCount(); ++i)
    {
        if (strLangName == GetLanguageName(i))
        {
            bFind = TRUE;
            break;
        }
    }

    m_strCheckedLang = (bFind ? strLangName : DEFAULT_LANG_NAME);

    //printf("strLangName=%s,m_strCheckedLang=%s\n",strLangName.Data(),m_strCheckedLang.Data());

    // 加载语言文件
    LoadLanguageFile(m_strCheckedLang);
}


int CLanguage::GetLanguageCount()
{
    return m_languageNames.size();
}


CMyString CLanguage::GetLanguageName(int index)
{
    if (index < 0 || index >= GetLanguageCount())
    {
        return DEFAULT_LANG_NAME;
    }

    return m_languageNames[index];
}


int CLanguage::GetCheckedLanguage()
{
    int nLangCount = GetLanguageCount();

    for (int i=0; i<nLangCount; ++i)
    {
        if (m_strCheckedLang == GetLanguageName(i))
        {
            return i;
        }
    }

    return -1;
}


void CLanguage::SetCheckedLanguage(CMyString strLangName)
{
    bool bFind = FALSE;

    for (int i=0; i<GetLanguageCount(); ++i)
    {
        if (strLangName == GetLanguageName(i))
        {
            bFind = TRUE;
            break;
        }
    }

    m_strCheckedLang = (bFind ? strLangName : DEFAULT_LANG_NAME);

    g_Global.m_IniConfig.SetValue(CONFIG_FILE_SECTION_LANGUAGE, CONFIG_FILE_ITEM_CHECKED_LANG, m_strCheckedLang.Data());
    g_Global.m_IniConfig.Write();


    LoadLanguageFile(m_strCheckedLang);
}


void CLanguage::SetCheckedLanguage(int index)
{
    SetCheckedLanguage(GetLanguageName(index));
}


void CLanguage::LoadLanguageFile(CMyString strLangName)
{
    CMyString strPathName;
    strPathName.Format((char*)("%s/%s.ini"), m_strPath.C_Str(), strLangName.C_Str());
    m_languageFile.m_cFileName = CStringToChar(strPathName);
    m_languageFile.Clear();
    m_languageFile.Read();
}

// 获取字符串（默认语言使用默认字符串）
CMyString& CLanguage::GetString(const char* szSection, const char* szItem, CMyString strDefault)
{
    if(m_languageFile.m_cFileName != NULL)
    {
        m_languageFile.GetValue(szSection, szItem, strDefault);
    }
    // 不能直接return strDefault会发生内存错误，strDefault是形参，临时变量在函数结束释放
    //CMyString *str = new CMyString(strDefault);
    //return *str;
    m_strItem = strDefault;
    return m_strItem;
}


bool CLanguage::IsSimpleChinese()
{
    return (m_strCheckedLang == ("简体中文"));
}

