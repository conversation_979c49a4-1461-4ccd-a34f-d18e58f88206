#ifndef CJSONRW_H
#define CJSONRW_H

#include <iostream>
#include <string>
#include "cJSON.h"

using namespace std;

class CJ<PERSON>NRead
{
public:
    CJ<PERSON>NRead(string strJson);
    ~CJ<PERSON>NRead();
    bool      IsValid()         { return m_bValid; }

    string    GetValueString(string field);
    int         GetValueInt(string field);
    double  GetValueDouble(string field);
    bool      GetValueBool(string field);

    string    GetArrayValueString(string field, int index);

private:
    cJSON* m_root;

    bool    m_bValid;
};


class CJSONWrite
{
public:
    CJSONWrite();
    ~CJSONWrite();

    void    AddObjectString(string cmd, string item);
    void    AddObjectInt(string cmd, int item);
    void    AddObjectDouble(string cmd, double item);

private:
    cJSON* m_root;
};


#endif // CJSONRW_H
