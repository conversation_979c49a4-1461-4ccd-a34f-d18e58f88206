#ifndef CMYSTRING_H
#define CMYSTRING_H

#include <iostream>
#include <stdio.h>
#include <stdarg.h>
#include <algorithm>
#include <string>
#include <string.h>
#include <memory>

using namespace std;

#define SZ_BUFFER_LENGTH 1280

class CMyString
{
public:
    CMyString(void);
    CMyString(const char * str);
    CMyString(string& str);
    CMyString(const CMyString& str);
    ~CMyString(void);

public:
    const  char* C_Str(void) const;
    const  char* Data(void) const;
    void		Format(const char *fmt, ...);
    void		AppendFormat(const char *fmt, ...);
    //void     ConvectUtf8Format(const char *fmt, ...);
    int			GetLength(void);
    CMyString	Left(int count);
    CMyString	Right(int count);
    CMyString	Mid(int first, int count);
    CMyString	Mid(int first);
    string::size_type			Find(const char* str);
    string::size_type         Find(char  str);
    string::size_type			Find(CMyString& str);
    string::size_type			ReverseFind(char ch);
    CMyString&	MakeUpper(void);
    CMyString&	MakeLower(void);
    void		Delete(int index, int count);
    void		Replace(const char* strOld, const char* strNew);
    void		Replace(CMyString& strOld, CMyString& strNew);

public:
    CMyString & operator = (const CMyString & str);
    CMyString & operator = (const char* str);
    CMyString & operator += (const CMyString & str);
    CMyString & operator += (const char * str);
    CMyString operator + (const CMyString & str);

    bool operator >  (const CMyString & str) const;
    bool operator >= (const CMyString & str) const;
    bool operator <  (const CMyString & str) const;
    bool operator <= (const CMyString & str) const;
    // ==相等运算符重载*********************************
	bool operator ==(const CMyString & str)const{ return (m_string == str.m_string);}
    bool operator == (const char * str) const;
    bool operator !=(const CMyString & str)const{ return (m_string != str.m_string);}
    bool operator != (const char * str) const;

    char& operator[](int idx);

    bool IsEmpty() const { return m_string.empty(); }

private:
    //char	m_szBuffer[512];
    //std::shared_ptr<char[]> m_szBuffer;
    string	m_string;

};

#endif // CMYSTRING_H
