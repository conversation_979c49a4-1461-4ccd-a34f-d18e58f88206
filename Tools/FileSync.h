#ifndef FILESYNC_H
#define FILESYNC_H

#include <QtCore/qglobal.h>

#include <iostream>
#if defined(Q_OS_LINUX)
#include <curl/curl.h>
#endif
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <string.h>
#include "Model/Device/Section.h"

using namespace std;




typedef struct SongFile_t
{
    CMyString strCurSyncSongName;    // 当前同步歌曲名
    int nAllSongCount;               // 一共需要同步的歌曲数目
    int nFinishedSongCount;          // 已同步完成的歌曲数目
    int nPercentage;                 // 正在同步的歌曲百分比
}SongFile_t;


bool    StartDownloadFile1(const char* szURL,       // URL
                          const char* szFilePathName);  // 保存文件路径名称

/******************************************/
/*  Curl下载类                             */
/******************************************/

class CCurlDownload
{
public:
    CCurlDownload();
    ~CCurlDownload();

    bool    InitCurl();
    void    FreeCurl();

    // 下载主机服务器文件
    bool    StartDownloadFile(const char* szURL,              // URL
                              const char* szFilePathName,     // 保存文件路径名称
                              void*	pUserDefined = NULL);     // 自定义数据

private:
    // 文件写入回调函数
    static size_t process_data(void *buffer, size_t size, size_t nmemb, void *user_p);

    // 下载进度回调函数
    static size_t my_progress_func(void *progress_data,  // 用户自定义参数
                                   double dltotal,    // 下载文件总大小
                                   double dlnow,      // 已下载大小
                                   double ultotal,    // 上传文件总大小
                                   double ulnow);     // 已上传大小

private:
#if defined(Q_OS_LINUX)
    CURL    *m_Curl;
#endif

};


/*************************************************/
/*  主机文件同步类，同步上级主机文件(播放列表，歌曲)       */
/*************************************************/

class CHostFileSync
{
public:
    CHostFileSync();
    ~CHostFileSync();
    void Init();

    CMyString   GetURLAddr()          { return m_strURLAddr;   }
    bool        IsSync()              { return m_bSync;        }
    void        SetSync(bool bSync)   { m_bSync = bSync;       }
    FileType    GetSyncFileType()     { return m_CurSyncFile;  }
    void        SetSyncFileType(FileType ft)     { m_CurSyncFile = ft; }

public:
    bool SyncFile(FileType ft, const char* szHttpURLAddr);

    // 歌曲文件下载时，上传歌曲同步进度
    void UploadSongProgress(double nPercentage);

private:
    // 同步xml文件
    void StartSyncXmlFilePthread();
    static void* SyncXmlFilePthread(void* lparam);

    // 同步歌曲文件
    void SyncSongFile(const char* szURLAddr);
public:
    SongFile_t m_tSongSyncInfo;    // 文件同步信息

private:
    bool      m_bSync;             // 是否同步状态
    FileType  m_CurSyncFile;       // 当前同步文件类型
    CMyString m_strURLAddr;

};



/*********************************************************/
/*  文件同步类，分区同步服务器文件控制(分组, 播放列表, 歌曲)       */
/*********************************************************/

class CFileSyncManager
{
public:
    CFileSyncManager();
    ~CFileSyncManager();
    bool     IsSync()               { return m_bSync;           }
    bool     IsStop()               { return m_bStopSync;       }
    void     SetSync(bool bSync)    { m_bSync = bSync;          }
    FileType GetSyncFt()            { return m_SyncFt;          }

    bool     StartSyncFile(FileType ft);
    void     StopSyncFile();
private:
    static  void*  SyncFilePth(void* lparam);

private:
    FileType  m_SyncFt;
    bool      m_bSync;
    bool      m_bStopSync;
};


#endif // FILESYNC_H




