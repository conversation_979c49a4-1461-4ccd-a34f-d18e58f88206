//因为std::remove是std标准库中的函数，要调用它必须加入头文件：#include <algorithm>
#include <algorithm>
#include "buildTime.h"

// 月份名称对应的数字形式
int getMonthNumber(const std::string& monthName) {
    if (monthName == "Jan") return 1;
    if (monthName == "Feb") return 2;
    if (monthName == "Mar") return 3;
    if (monthName == "Apr") return 4;
    if (monthName == "May") return 5;
    if (monthName == "Jun") return 6;
    if (monthName == "Jul") return 7;
    if (monthName == "Aug") return 8;
    if (monthName == "Sep") return 9;
    if (monthName == "Oct") return 10;
    if (monthName == "Nov") return 11;
    if (monthName == "Dec") return 12;
    return -1;  // 无效的月份
}

// 去除日期和时间中的空格和冒号
#define REMOVE_SPACES(str) str.erase(remove(str.begin(), str.end(), ' '), str.end())
#define REMOVE_COLONS(str) str.erase(remove(str.begin(), str.end(), ':'), str.end())


//获取APP编译时间
std::string get_app_build_time() {
    static std::string compileTime = "";
    if (compileTime.length() > 0)
        return compileTime;

    std::string compilationDate = __DATE__;
    std::string compilationTime = __TIME__;

    // 提取日期信息
    std::string year = compilationDate.substr(compilationDate.length() - 2);
    std::string month = std::to_string(getMonthNumber(compilationDate.substr(0, 3)));
    std::string day = compilationDate.substr(4, 2); // 从索引4提取两位

    // 去掉前导空格
    day.erase(std::remove(day.begin(), day.end(), ' '), day.end());

    // 补零
    if (month.length() < 2) month = "0" + month;
    if (day.length() < 2) day = "0" + day;

    // 提取时间信息
    std::string hour = compilationTime.substr(0, 2);
    std::string minute = compilationTime.substr(3, 2);

    // 拼接结果
    compileTime = year + month + day + hour + minute;
    return compileTime;
}

std::string get_build_month_day() {
    static std::string compileTime = "";
    if (compileTime.length() > 0)
        return compileTime;

    std::string compilationDate = __DATE__;
    std::string compilationTime = __TIME__;

    // 提取日期信息
    std::string year = compilationDate.substr(compilationDate.length() - 2);
    std::string month = std::to_string(getMonthNumber(compilationDate.substr(0, 3)));
    std::string day = compilationDate.substr(4, 2); // 从索引4提取两位

    // 去掉前导空格
    day.erase(std::remove(day.begin(), day.end(), ' '), day.end());

    // 补零
    if (month.length() < 2) month = "0" + month;
    if (day.length() < 2) day = "0" + day;

    // 提取时间信息
    std::string hour = compilationTime.substr(0, 2);
    std::string minute = compilationTime.substr(3, 2);

    // 拼接结果
    compileTime = month + day;
    return compileTime;
}


std::string get_version_identify(int nDateVerison) {
    std::string curDateVersion;
    switch(nDateVerison)
    {
        case 0:
        break;
        case 1:
        curDateVersion="a";
        break;
        case 2:
        curDateVersion="b";
        break;    
        case 3:
        curDateVersion="c";
        break;
        case 4:
        curDateVersion="d";
        break;
        case 5:
        curDateVersion="e";
        break;
        case 6:
        curDateVersion="f";
        break;
    }
    return curDateVersion;
}
