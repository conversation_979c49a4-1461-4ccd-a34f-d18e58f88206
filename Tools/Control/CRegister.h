#ifndef CREGISTER_H
#define CREGISTER_H

#include "iostream"
#include "Global/const.h"

using namespace std;



class CRegister
{
public:
    CRegister();

    static bool HasRegister();
    #if APP_IS_LZY_COMMERCE_VERSION
    static bool HasBasicTTSRegister();
    #endif
    static bool HasCloudControlRegister();

    static bool Register(CMyString strCode);
    #if APP_IS_LZY_COMMERCE_VERSION
    static bool TTS_Register(CMyString strCode);
    #endif
    static bool CloudControl_Register(CMyString strCode);
    static void RegisterStatusChange();
private:
    static void *StatusChange(void *parm);
};

#endif // CREGISTER_H





