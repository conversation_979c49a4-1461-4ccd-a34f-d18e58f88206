#ifndef TTS_H
#define TTS_H

#include <iostream>
#include <stdlib.h>
#include <stdio.h>
#include <unistd.h>
#include <errno.h>
#include <list>
#include <queue>
#include "qtts.h"
#include "msp_cmn.h"
#include "msp_errors.h"
#include "Tools/CMyString.h"
#include "Player/PlayQueue.h"

using namespace std;

/* wav音频头部格式 */
typedef struct _wave_pcm_hdr
{
    char            riff[4];                // = "RIFF"
    int				size_8;                 // = FileSize - 8
    char            wave[4];                // = "WAVE"
    char            fmt[4];                 // = "fmt "
    int				fmt_size;				// = 下一个结构体的大小 : 16

    short int       format_tag;             // = PCM : 1
    short int       channels;               // = 通道数 : 1
    int				samples_per_sec;        // = 采样率 : 8000 | 6000 | 11025 | 16000
    int				avg_bytes_per_sec;      // = 每秒字节数 : samples_per_sec * bits_per_sample / 8
    short int       block_align;            // = 每采样点字节数 : wBitsPerSample / 8
    short int       bits_per_sample;        // = 量化比特数: 8 | 16

    char            data[4];                // = "data";
    int				data_size;              // = 纯数据长度 : FileSize - 44
} wave_pcm_hdr;


typedef  enum tts_sample_rate
{
    Rate_6K = 6000,
    Rate_8K = 8000,
    Rate_11K = 11025,
    Rate_16K = 16000
}TTS_SampleRate;



class CTTSTask
{
public:
    CTTSTask(
             int nOnline = 1,             // 1：在线 2：离线
            string strAccount = "admin",   //账户名
             string strFileName = "",     // 文件名
             string strVoiceName = "",    // 发音人
             int nVolume = 50,       // 音量
             int nPitch = 50,        // 音调
             int nSpeed = 50,        // 语速
             int nRdn = 2,           // 音频数字发音方式
             TTS_SampleRate nRate = Rate_16K,   // 采样率
             CPlayTask *playTask = NULL);       // 播放任务
    bool    IsOnline()                      { return (m_nOnline==1);}
    int     GetVolume()                     { return m_nVolume;     }
    void    SetVolume(int nVolume)          { m_nVolume = nVolume;  }
    int     GetPitch()                      { return m_nPitch;      }
    void    SetPitch(int nPitch)            { m_nPitch = nPitch;    }
    int     GetSpeed()                      { return m_nSpeed;      }
    void    SetSpeed(int nSpeed)            { m_nSpeed = nSpeed;    }
    int     GetRdn()                        { return m_nRdn;        }
    void    SetRdn(int nRdn)                { m_nRdn = nRdn;        }
    string  GetVoiceName()                  { return m_VoiceName;   }
    void    SetVoiceName(string strName)    { m_VoiceName = strName;}
    string  GetSpeechText()                 { return m_strText;     }
    void    SetSpeechText(string strText);
    string  GetFileName()                   { return m_strFileName; }
    void    SetFileName(string strFileName) { m_strFileName = strFileName; }
    string  GetUID()                        { return m_strUID;      }
    void    SetUID(string strUID)           { m_strUID = strUID;    }
    CMyString GetSessionParam()             { return m_strSessionParam;    }

    string  GetAccount()                    {  return m_strAccount;}
    void  SetAccount(string strAccount)     {  m_strAccount = strAccount; }

    string GetCommand()                     {  return m_strCommand;}
    void SetCommand(string strCommand)      {  m_strCommand = strCommand;}

    CPlayTask &GetPlayTask()                { return m_playTask;}

    int     CheckParam();

private:


private:
    int    m_nOnline;             // 1：在线 2：离线
    int    m_nVolume;             // 音量
    TTS_SampleRate    m_nRate;    // 采样率
    int    m_nPitch;              // 音调
    int    m_nSpeed;              // 语速
    int    m_nRdn;                // 音频数字发音方式
    string m_VoiceName;           // 发音人
    string m_strText;             // 合成文本
    string m_strFileName;         // 文件名
    string m_strAccount;          // 账户名
    CMyString m_strSessionParam;  // 合成参数

    string m_strUID;              // 用户ID
    string m_strCommand;          // 命令，用于区分生成音频文件后的行为

    CPlayTask m_playTask;         // m_strCommand=play_specified_source时用到
};


class CTTSManager
{
public:
    CTTSManager();

    int    TextToSpeech(CTTSTask& tts);
    
#if defined(Q_OS_LINUX)
    int    TTSClientIndependent(CTTSTask& tts);
#endif

private:
    // 生成语音
    static  void *GeneratingThread(void* lpParam);

    pthread_mutex_t     m_TTSTaskMutex;

private:
    list<CTTSTask>  m_TTSTaskList;       // 合成任务
};


#endif // TTS_H





