#include "stdafx.h"
#include "TTS.h"
#include <string.h>
#include <QDir>
#include <signal.h>
#if defined(Q_OS_LINUX)
#include <spawn.h>
#endif

#define XUNFEI_TTS_APP_PATH "/mnt/yaffs2/voip/Networking/xunfeiTTS"
#define TTS_LOGIN_PARAMS  "appid = 5d1c566d, work_dir = /tmp/";

/* 默认wav音频头部数据 */
wave_pcm_hdr default_wav_hdr =
{
    { 'R', 'I', 'F', 'F' },
    0,
    {'W', 'A', 'V', 'E'},
    {'f', 'm', 't', ' '},
    16,
    1,
    1,
    16000,
    32000,
    2,
    16,
    {'d', 'a', 't', 'a'},
    0
};


// 在线发音人列表
char* VoiceNameOnArray[] = {(char *)"xiaoyan", (char *)"aisjiuxu", (char *)"aisxping", (char *)"aisjinger", (char *)"aisbabyxu"} ;

// 离线发音人列表
char* VoiceNameOffArray[] = {(char *)"xiaoyan", (char *)"xiaofeng"};


/* 文本合成 */
#if 0
int text_to_speech(const char* src_text, const char* des_path, const char* params)
{
    int          ret          = -1;
    FILE*        fp           = NULL;
    const char*  sessionID    = NULL;
    unsigned int audio_len    = 0;
    wave_pcm_hdr wav_hdr      = default_wav_hdr;
    int          synth_status = MSP_TTS_FLAG_STILL_HAVE_DATA;

    if (NULL == src_text || NULL == des_path)
    {
        printf("params is error!\n");
        return ret;
    }
    fp = fopen(des_path, "wb");
    if (NULL == fp)
    {
        printf("open %s error.\n", des_path);
        return ret;
    }
    /* 开始合成 */
    sessionID = QTTSSessionBegin(params, &ret);
    if (MSP_SUCCESS != ret)
    {
        printf("QTTSSessionBegin failed, error code: %d.\n", ret);
        fclose(fp);
        return ret;
    }


    ret = QTTSTextPut(sessionID, src_text, (unsigned int)strlen(src_text), NULL);
    if (MSP_SUCCESS != ret)
    {
        printf("QTTSTextPut failed, error code: %d.\n",ret);
        QTTSSessionEnd(sessionID, "TextPutError");
        fclose(fp);
        return ret;
    }
    printf("正在合成 ...\n");
    fwrite(&wav_hdr, sizeof(wav_hdr) ,1, fp); //添加wav音频头，使用采样率为16000


    while (1)
    {
        /* 获取合成音频 */
        const void* data = QTTSAudioGet(sessionID, &audio_len, &synth_status, &ret);
        if (MSP_SUCCESS != ret)
            break;
        if (NULL != data)
        {
            fwrite(data, audio_len, 1, fp);
            wav_hdr.data_size += audio_len; //计算data_size大小
        }
        if (MSP_TTS_FLAG_DATA_END == synth_status)
            break;

    }
    printf("\n");
    if (MSP_SUCCESS != ret)
    {
        printf("QTTSAudioGet failed, error code: %d.\n",ret);
        QTTSSessionEnd(sessionID, "AudioGetError");
        fclose(fp);
        return ret;
    }
    /* 修正wav文件头数据的大小 */
    wav_hdr.size_8 += wav_hdr.data_size + (sizeof(wav_hdr) - 8);

    /* 将修正过的数据写回文件头部,音频文件为wav格式 */
    fseek(fp, 4, 0);
    fwrite(&wav_hdr.size_8,sizeof(wav_hdr.size_8), 1, fp); //写入size_8的值
    fseek(fp, 40, 0); //将文件指针偏移到存储data_size值的位置
    fwrite(&wav_hdr.data_size,sizeof(wav_hdr.data_size), 1, fp); //写入data_size的值
    fclose(fp);
    fp = NULL;
    /* 合成完毕 */
    ret = QTTSSessionEnd(sessionID, "Normal");
    if (MSP_SUCCESS != ret)
    {
        printf("QTTSSessionEnd failed, error code: %d.\n",ret);
    }

    return ret;
}
#endif

int text_to_speech(const char* src_text, const char* des_path, const char* params)
{
    int          ret          = -1;
    FILE*        fp           = NULL;
    const char*  sessionID    = NULL;
    unsigned int audio_len    = 0;
    wave_pcm_hdr wav_hdr      = default_wav_hdr;
    int          synth_status = MSP_TTS_FLAG_STILL_HAVE_DATA;

    if (NULL == src_text || NULL == des_path)
    {
        printf("params is error!\n");
        return ret;
    }
    MyCFile file;
    if(!file.Open(des_path, "wb"))
    {
        printf("open %s error.\n", des_path);
        return -1;
    }

    /////////////////////////////////////////////////////////////

    //fwrite(&wav_hdr, sizeof(wav_hdr) ,1, fp); //添加wav音频头，使用采样率为16000
    file.Write(&wav_hdr, sizeof(wav_hdr));

    unsigned int nTextLen = strlen(src_text);
    unsigned int nPos = 0;
    unsigned int nStep = 6100;

    ret = MSP_SUCCESS;

    /* 开始合成 */
    sessionID = QTTSSessionBegin(params, &ret);
    if (MSP_SUCCESS != ret)
    {
        printf("QTTSSessionBegin failed, error code: %d.\n", ret);
        //fclose(fp);
        return ret;
    }

    while (nTextLen > 0) {
        /* 开始合成 */
        #if 0
        sessionID = QTTSSessionBegin(params, &ret);
        if (MSP_SUCCESS != ret)
        {
            printf("QTTSSessionBegin failed, error code: %d.\n", ret);
            //fclose(fp);
            return ret;
        }
        #endif 
        int nWirteLen = (nTextLen >= nStep ? nStep : nTextLen);
        ret = QTTSTextPut(sessionID, src_text + nPos, nWirteLen, NULL);
        if (MSP_SUCCESS != ret)
        {
            printf("QTTSTextPut failed, error code: %d.\n",ret);
            QTTSSessionEnd(sessionID, "TextPutError");
            //fclose(fp);
            return ret;
        }


        while (1)
        {
            /* 获取合成音频 */
            const void* data = QTTSAudioGet(sessionID, &audio_len, &synth_status, &ret);
            if (MSP_SUCCESS != ret)
            {
                break;
            }
            if (NULL != data)
            {
                //fwrite(data, audio_len, 1, fp);
                file.Write(data, audio_len);
                wav_hdr.data_size += audio_len; //计算data_size大小
            }
            if (MSP_TTS_FLAG_DATA_END == synth_status)
                break;

        }

        nTextLen -= nWirteLen;
        nPos += nWirteLen;

#if 0
        if (nTextLen > 0)
        {
            if (MSP_SUCCESS != ret)
            {
                printf("QTTSSessionEnd failed, error code: %d.\n",ret);
                //fclose(fp);
                ret = QTTSSessionEnd(sessionID, "Normal");
                return ret;
            }
        }
#endif
    }


    printf("\n");
    if (MSP_SUCCESS != ret)
    {
        printf("QTTSAudioGet failed, error code: %d.\n",ret);
        QTTSSessionEnd(sessionID, "AudioGetError");
        //fclose(fp);
        return ret;
    }
    /* 修正wav文件头数据的大小 */
    wav_hdr.size_8 += wav_hdr.data_size + (sizeof(wav_hdr) - 8);

    /* 将修正过的数据写回文件头部,音频文件为wav格式 */
    //fseek(fp, 4, 0);
    file.Seek(4,0);
    //fwrite(&wav_hdr.size_8,sizeof(wav_hdr.size_8), 1, fp); //写入size_8的值
    file.Write(&wav_hdr.size_8,sizeof(wav_hdr.size_8));
    //fseek(fp, 40, 0); //将文件指针偏移到存储data_size值的位置
    file.Seek(40,0);
    //fwrite(&wav_hdr.data_size,sizeof(wav_hdr.data_size), 1, fp); //写入data_size的值
     file.Write(&wav_hdr.data_size,sizeof(wav_hdr.data_size));
    //fclose(fp);
    fp = NULL;
    /* 合成完毕 */
    ret = QTTSSessionEnd(sessionID, "Normal");
    if (MSP_SUCCESS != ret)
    {
        printf("QTTSSessionEnd failed, error code: %d.\n",ret);
    }

    return ret;
}


CTTSTask::CTTSTask(
                   int nOnline,            // 1：在线 2：离线
                   string strAccount,       //账户名
                   string strFileName,     // 文件名
                   string strVoiceName,    // 发音人
                   int nVolume,            // 音量
                   int nPitch,             // 音调
                   int nSpeed,             // 语速
                   int nRdn,               // 音频数字发音方式
                   TTS_SampleRate nRate,   // 采样率 
                   CPlayTask *playTask)   // 播放任务
{
    m_nOnline = nOnline;
    m_strFileName = strFileName;
    m_VoiceName = strVoiceName;
    m_nVolume = nVolume;
    m_nRate = nRate;
    m_nPitch = nPitch;
    m_nSpeed = nSpeed;
    m_nRdn = nRdn;
    m_strAccount = strAccount;

    if(IsOnline())
    {
        m_strSessionParam.Format("voice_name = %s, text_encoding = utf8, sample_rate = %d, speed = %d, volume = %d, pitch = %d, rdn = %d",
                                 m_VoiceName.data(), m_nRate, m_nSpeed, m_nVolume, m_nPitch, m_nRdn);
    }
    else
    {
#if defined(Q_OS_LINUX)
        m_strSessionParam.Format("engine_type = local,voice_name=%s, text_encoding = UTF8, tts_res_path = fo|/mnt/yaffs2/voip/Networking/msc/res/tts/%s.jet;"
                                 "fo|/mnt/yaffs2/voip/Networking/msc/res/tts/common.jet, sample_rate = %d, speed = %d, volume = %d, pitch = %d, rdn = %d",
                                 m_VoiceName.data(), m_VoiceName.data(), m_nRate, m_nSpeed, m_nVolume, m_nPitch, m_nRdn);
#else
		CMyString voice_jet,common_jet;
        voice_jet.Format("%s/Res/tts/%s.jet",g_Global.m_strRunDirPath.Data(),m_VoiceName.data());
        common_jet.Format("%s/Res/tts/%s",g_Global.m_strRunDirPath.Data(),"common.jet");
        voice_jet.Replace("/","\\");
        common_jet.Replace("/","\\");

        printf("voice_jet=%s\n",voice_jet.Data());
        printf("common_jet=%s\n",common_jet.Data());
		
        m_strSessionParam.Format("engine_type = local,voice_name=%s, text_encoding = UTF8, tts_res_path = fo|%s;"
                         "fo|%s, sample_rate = %d, speed = %d, volume = %d, pitch = %d, rdn = %d",
                         m_VoiceName.data(),voice_jet.Data(),common_jet.Data(), m_nRate, m_nSpeed, m_nVolume, m_nPitch, m_nRdn);
#endif
    }

    if(playTask!=NULL)
    {
        m_playTask = *playTask;
    }
    else
    {
        //todo 暂不考虑m_playTask不存在的情况
    }

    m_strCommand = "text_to_speech";
}

void CTTSTask::SetSpeechText(string strText)
{
    // 音频前添加500ms间隔，音频后添加1秒间隔
    if(m_nOnline == 1)       // 在线
    {
        m_strText = m_strText = "[p500]" + strText + "[p1000]";
    }
    else    // 离线
    {
        m_strText = "[p500]" + strText + "[p1000]";
    }
}

int getUTF8LetterNumer(const char* s)
{
    int i=0;
    int count = 0;

    while(s[i++])
    {
        if ((s[i]&0xc0) != 0x80)
        {
            count++;
        }
    }

    return count;
}

#define MAX_ONLINE_CHARS    2000
#define MAX_OFFLINE_CHARS   10000 // max 1350


int CTTSTask::CheckParam()
{
    if(m_nPitch < 0 || m_nPitch > 100 ||
       m_nSpeed < 0 || m_nSpeed > 100 ||
       m_nVolume < 0 || m_nVolume > 100 ||
       m_strText.length() == 0 || m_strText.length() > 6100 )
    {
        LOG("Parm error", LV_INFO);
        return EC_TTS_PARAM_ERROR;
    }

    bool IsExistVoice = false;      // 判断发音人是否存在
    int nLetterNum = getUTF8LetterNumer(m_strText.c_str());
    if(m_nOnline == 1)
    {

        if(nLetterNum > MAX_ONLINE_CHARS)
        {
            LOG("online text length too long", LV_INFO);
            return EC_TTS_TEXT_MAX;
        }

        int nCount = sizeof(VoiceNameOnArray)/sizeof(char*);
        for(int i=0; i<nCount; i++)
        {
            LOG(VoiceNameOnArray[i], LV_INFO);
            if(strcmp(m_VoiceName.data(), VoiceNameOnArray[i]) == 0)
            {
                IsExistVoice = true;
                break;
            }
        }
    }
    else
    {
        if(nLetterNum > MAX_OFFLINE_CHARS)
        {
            LOG("offline text length too long", LV_INFO);
            return EC_TTS_TEXT_MAX;
        }

        int nCount = sizeof(VoiceNameOffArray)/sizeof(char*);
        for(int i=0; i<nCount; i++)
        {
            //LOG(VoiceNameOffArray[i], LV_INFO);
            if(strcmp(m_VoiceName.data(), VoiceNameOffArray[i]) == 0)
            {
                IsExistVoice = true;
                break;
            }
        }
    }

    if(!IsExistVoice)
    {
        LOG("have't voicename", LV_INFO);
        return EC_TTS_VOICE_NOTEXIST;
    }

    // 列表是否存在
    int nListIndex = g_Global.m_PlayList.FindListByName(TTS_LIST_NAME);
    if(nListIndex == -1)
    {
       g_Global.m_PlayList.AddList(CMyString(TTS_LIST_NAME),SUPER_USER_NAME);
       nListIndex = g_Global.m_PlayList.FindListByName(TTS_LIST_NAME);
       g_Global.m_PlayList.SetListDateTime(nListIndex, GetCurrentDateTime());
    }

    // 判断音频文件重名
    CMyString strHttpPathName;
    strHttpPathName.Format("/%s/%s/%s.wav", HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_MUSIC, m_strFileName.data());
    int songId=-1;
    if ( (songId=g_Global.m_PlayList.FindSongIdByList(strHttpPathName, nListIndex)) >=0 )
    {
        //return EC_TARGET_EXISTED;
        CSong song=g_Global.m_PlayList.GetListSong(nListIndex,songId);
        if(song.GetUserAccount() != m_strAccount)
        {
            m_strFileName = m_strFileName+"("+m_strAccount+")";
        }
    }

    return EC_SUCCESS;
}




/***********************************************/



CTTSManager::CTTSManager()
{
    m_TTSTaskMutex = PTHREAD_MUTEX_INITIALIZER;
}


#if defined(Q_OS_LINUX)
int    CTTSManager::TTSClientIndependent(CTTSTask& tts)
{
    int nResult = tts.CheckParam();
    if (nResult == EC_SUCCESS)
    {
        // 获取参数
        string login_params = TTS_LOGIN_PARAMS;
        string session_params = tts.GetSessionParam().Data();
        string speech_text = tts.GetSpeechText();

        CMyString strFilePath;
        string ttsName=tts.GetFileName();
        CMyString baseFolder;
        LOG(FORMAT("%s:TTS,Command=%s\n", tts.GetAccount().data(),tts.GetCommand().data()), LV_INFO);
        if(tts.GetCommand() == "text_to_speech")
        {
            baseFolder.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_MUSIC);
        }
        else if(tts.GetCommand() == "play_specified_source")
        {
            baseFolder.Format("%s", g_Global.m_strTempFolderPath.Data());
        }
        strFilePath.Format("%s/%s.wav", baseFolder.Data(), ttsName.data());

        string output_file = strFilePath.Data();

        // 设置 posix_spawn 参数
        const char* argv[] = {
            XUNFEI_TTS_APP_PATH,  // 可执行文件路径
            login_params.c_str(),
            session_params.c_str(),
            speech_text.c_str(),
            output_file.c_str(),
            nullptr  // 需要以 nullptr 结束
        };


        pthread_mutex_lock(&g_Global.m_TTSManager.m_TTSTaskMutex);

        pid_t pid;
        int status = posix_spawn(&pid, XUNFEI_TTS_APP_PATH, nullptr, nullptr, const_cast<char**>(argv), environ);
        if (status != 0) {
            perror("posix_spawn TTS failed");
            delete(&tts);
            pthread_mutex_unlock(&g_Global.m_TTSManager.m_TTSTaskMutex);
            return EC_ERROR;
        }
        
        // 父进程可以继续执行其他任务，或者等待子进程结束
        waitpid(pid, &status, 0);

        if (WIFEXITED(status)) {
            // 如果子进程正常退出
            int exitCode=WEXITSTATUS(status);
            std::cout << "TTS child process exited normally with exit code: " << exitCode << std::endl;
            
            CMyString strFilePath;
            string ttsName=tts.GetFileName();
            CMyString baseFolder;
            if(tts.GetCommand() == "text_to_speech")
            {
                baseFolder.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_MUSIC);
            }
            else if(tts.GetCommand() == "play_specified_source")
            {
                baseFolder.Format("%s", g_Global.m_strTempFolderPath.Data());
            }
            
            strFilePath.Format("%s/%s.wav", baseFolder.Data(), ttsName.data());

            bool bAddedSongs = false;
            bool localSongUpdate=false;
            if(exitCode == EC_SUCCESS && tts.GetCommand() == "text_to_speech")
            {
                // 添加音频文件到播放列表
                if(IsExistFile(strFilePath.C_Str()))
                {
                    song_header_t  songHeader=g_Global.m_SongTool.GetSongInfo(strFilePath);
                    int		nDuration	  = songHeader.nDuration;;
                    int		nSize		  = g_Global.m_SongTool.GetSize(strFilePath);

                    CMyString strHttpPathName = GetHttpURLPathByPathName(strFilePath);
                    CSong	song;
                    song.SetPathName(strHttpPathName);
                    song.SetDuration(nDuration);
                    song.SetSize(nSize);
                    song.SetBitRate(songHeader.bitrate);
                    song.SetAlive(true);
                    song.SetMd5(GetFileMd5(strFilePath.Data()));
                    song.SetUserAccount(tts.GetAccount().data());

                    if(nDuration > 0 && nSize > 0)
                    {
                        int nListIndex = g_Global.m_PlayList.FindListByName(TTS_LIST_NAME);
                        if (!g_Global.m_PlayList.FindSongByList(strHttpPathName, nListIndex))   // 判断重名
                        {
                            g_Global.m_PlayList.AddListSong(nListIndex, song);
                        }
                        else
                        {
                            g_Global.m_PlayList.UpdateSongInfo(song);
                        }
                        //判断是否属于低码率歌曲，如果不是，需要进行转换
                        if(song.GetBitRate() > SONG_LOW_BITRATE)
                        {
                            CMyString brcSongPath=strFilePath+SONG_LOW_RATE_EXT_NAME;
                            RemoveFile(brcSongPath.C_Str());
                            g_Global.m_PlayList.AddLowRateSong(song.GetPathName());
                        }
                        g_Global.m_PlayList.SetListDateTime(nListIndex, GetCurrentDateTime());
                        bAddedSongs=true;

                        #if SUPPORT_SONG_MANAGER
                        int result=g_Global.m_SongManager.AddSong(song);
                        localSongUpdate = result;
                        #endif
                    }
                    else
                    {
                        exitCode = EC_TTS_AUDIO_SHORT;
                        RemoveFile(strFilePath.C_Str());
                    }
                }
                else
                {
                    exitCode = EC_TARGET_NOTEXIST;
                }
            }

            // 回复终端
            CWebSection* pWebSection = g_Global.m_WebSections.GetWebSectionBySocketID(tts.GetUID());
            string strBuf;
            if(tts.GetCommand() == "text_to_speech")
            {
                strBuf = CWebProtocol::CmdText2Speech(tts.GetFileName(), exitCode);
                if(pWebSection != NULL)
                {
                    g_Global.m_WebNetwork.m_WebSend.CommandSend(strBuf.data(), *pWebSection);
                }
            }
            else if(tts.GetCommand() == "play_specified_source")
            {
                //应答
                strBuf = CWebProtocol::CmdResponsePlaySpecifiedSource(exitCode);
                if(pWebSection != NULL)
                {
                    g_Global.m_WebNetwork.m_WebSend.CommandSend(strBuf.data(), *pWebSection);
                }
                
                //开始指定播放TTS任务
                if(exitCode == EC_SUCCESS)
                {
                    //todo 判断getPlayTask参数是否正确
                    g_Global.m_PlayQueue.PushPlayTask(tts.GetPlayTask());
                }
            }

            if (bAddedSongs)
            {
                //g_Global.WriteXmlFile(FILE_PLAYLIST);
                //TTS list的账户属于管理员，所以此处要加入实际的用户
                g_Global.m_PlayList.PushWriteXmlTask(tts.GetAccount().data());
            }

            //判断本地歌曲有没有变化，如果有的话，发送给所有web
            #if SUPPORT_SONG_MANAGER
            if(localSongUpdate)
            {
                g_Global.m_WebNetwork.ForwardLocalSongInfoToWeb(NULL);
            }
            #endif

            nResult = exitCode;

        } else if (WIFSIGNALED(status)) {
            // 如果子进程因为信号退出
            int signal_num = WTERMSIG(status);  // 获取导致子进程终止的信号编号
            std::cerr << "TTS child process aborted with signal: " << signal_num << std::endl;
            if (signal_num == SIGABRT) {
                std::cerr << "The child process was terminated by SIGABRT (abort)." << std::endl;
            }

            // 回复终端
            CWebSection* pWebSection = g_Global.m_WebSections.GetWebSectionBySocketID(tts.GetUID());
            string strBuf;
            if(tts.GetCommand() == "text_to_speech")
            {
                strBuf = CWebProtocol::CmdText2Speech(tts.GetFileName(), EC_ERROR);
                if(pWebSection != NULL)
                {
                    g_Global.m_WebNetwork.m_WebSend.CommandSend(strBuf.data(), *pWebSection);
                }
            }
            else if(tts.GetCommand() == "play_specified_source")
            {
                //应答
                strBuf = CWebProtocol::CmdResponsePlaySpecifiedSource(EC_ERROR);
                if(pWebSection != NULL)
                {
                    g_Global.m_WebNetwork.m_WebSend.CommandSend(strBuf.data(), *pWebSection);
                }
            }

            nResult = EC_ERROR;

        } else {
            std::cerr << "TTS child process did not exit normally." << std::endl;

            // 回复终端
            CWebSection* pWebSection = g_Global.m_WebSections.GetWebSectionBySocketID(tts.GetUID());
            string strBuf;
            if(tts.GetCommand() == "text_to_speech")
            {
                strBuf = CWebProtocol::CmdText2Speech(tts.GetFileName(), EC_ERROR);
                if(pWebSection != NULL)
                {
                    g_Global.m_WebNetwork.m_WebSend.CommandSend(strBuf.data(), *pWebSection);
                }
            }
            else if(tts.GetCommand() == "play_specified_source")
            {
                //应答
                strBuf = CWebProtocol::CmdResponsePlaySpecifiedSource(EC_ERROR);
                if(pWebSection != NULL)
                {
                    g_Global.m_WebNetwork.m_WebSend.CommandSend(strBuf.data(), *pWebSection);
                }
            }

            nResult = EC_ERROR;
        }

        printf("tts Child end...\n");
        pthread_mutex_unlock(&g_Global.m_TTSManager.m_TTSTaskMutex);
    }
    
    delete(&tts);

    return nResult;
}
#endif


int CTTSManager::TextToSpeech(CTTSTask &tts)
{
    int nResult = tts.CheckParam();

    if(nResult == EC_SUCCESS)
    {
        pthread_t pth;
        pthread_attr_t  attr;
        pthread_attr_init(&attr);
        pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
        pthread_create(&pth, &attr, GeneratingThread, (void*)&tts);
        pthread_attr_destroy(&attr);
    }

    return nResult;
}

void *CTTSManager::GeneratingThread(void *lpParam)
{
    CTTSTask* tts = (CTTSTask*)lpParam;
    int         ret                  = MSP_SUCCESS;
    const char* login_params         = "appid = 5d1c566d, work_dir = /tmp/";//登录参数,appid与msc库绑定,请勿随意改动
    CMyString strFilePath;

    string ttsName=tts->GetFileName();
    CMyString baseFolder;
    LOG(FORMAT("%s:TTS,Command=%s\n", tts->GetAccount().data(),tts->GetCommand().data()), LV_INFO);
    if(tts->GetCommand() == "text_to_speech")
    {
        baseFolder.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_MUSIC);
    }
    else if(tts->GetCommand() == "play_specified_source")
    {
        baseFolder.Format("%s", g_Global.m_strTempFolderPath.Data());
    }
    
    strFilePath.Format("%s/%s.wav", baseFolder.Data(), ttsName.data());
    
/******************************************************************/
// 合成音频文件

    //此处需要加锁，否则可能1032错误；10132
    //MSP_ERROR_INVALID_OPERATION 无效的操作，上次服务未正常结束即启动下一次。识别OnResult  isLast = true或onError，合成onCompleted
    pthread_mutex_lock(&g_Global.m_TTSManager.m_TTSTaskMutex);

    static bool isLogin=false;
    /* 用户登录 */
    if(!isLogin)
    {
        ret = MSPLogin(NULL, NULL, login_params);//第一个参数是用户名，第二个参数是密码，第三个参数是登录参数，用户名和密码可在http://www.xfyun.cn注册获取
        if (MSP_SUCCESS != ret)
        {
            LOG(FORMAT("MSPLogin failed, error code: %d.\n", ret), LV_INFO);
            goto EXIT;//登录失败，退出登录
        }
        isLogin=true;
    }


    /* 文本合成 */
    LOG(FORMAT("开始合成 ..."), LV_INFO);
    //LOG(FORMAT("|%s|, %d", tts->GetSpeechText().data(), tts->GetSpeechText().length()), LV_INFO);
    //LOG(FORMAT("%s", strFilePath.C_Str()), LV_INFO);
    ret = text_to_speech(tts->GetSpeechText().data(), strFilePath.C_Str(), tts->GetSessionParam().C_Str());
    if (MSP_SUCCESS != ret)
    {
        LOG(FORMAT("text_to_speech failed, error code: %d.\n", ret), LV_INFO);
        goto EXIT;
    }
    LOG(FORMAT("合成完毕"), LV_INFO);

EXIT:
    //MSPLogout(); //退出登录

    pthread_mutex_unlock(&g_Global.m_TTSManager.m_TTSTaskMutex);

/******************************************************************/
    // 添加到播放列表
    int nResult;

    if (ret == MSP_SUCCESS)
    {
        nResult = EC_SUCCESS;
    }
    else if (ret == MSP_ERROR_TIME_OUT)
    {
        nResult = EC_TIMEOUT;
    }
    else
    {
        nResult = EC_ERROR;
    }

    bool bAddedSongs = false;
    bool localSongUpdate=false;
    if(nResult == EC_SUCCESS && tts->GetCommand() == "text_to_speech")
    {
        // 添加音频文件到播放列表
        if(IsExistFile(strFilePath.C_Str()))
        {
            song_header_t  songHeader=g_Global.m_SongTool.GetSongInfo(strFilePath);
            int		nDuration	  = songHeader.nDuration;;
            int		nSize		  = g_Global.m_SongTool.GetSize(strFilePath);

            CMyString strHttpPathName = GetHttpURLPathByPathName(strFilePath);
            CSong	song;
            song.SetPathName(strHttpPathName);
            song.SetDuration(nDuration);
            song.SetSize(nSize);
            song.SetBitRate(songHeader.bitrate);
            song.SetAlive(true);
            song.SetMd5(GetFileMd5(strFilePath.Data()));
            song.SetUserAccount(tts->GetAccount().data());

            if(nDuration > 0 && nSize > 0)
            {
                int nListIndex = g_Global.m_PlayList.FindListByName(TTS_LIST_NAME);
                if (!g_Global.m_PlayList.FindSongByList(strHttpPathName, nListIndex))   // 判断重名
                {
                    g_Global.m_PlayList.AddListSong(nListIndex, song);
                }
                else
                {
                    g_Global.m_PlayList.UpdateSongInfo(song);
                }
                //判断是否属于低码率歌曲，如果不是，需要进行转换
                if(song.GetBitRate() > SONG_LOW_BITRATE)
                {
                    CMyString brcSongPath=strFilePath+SONG_LOW_RATE_EXT_NAME;
                    RemoveFile(brcSongPath.C_Str());
                    g_Global.m_PlayList.AddLowRateSong(song.GetPathName());
                }
                g_Global.m_PlayList.SetListDateTime(nListIndex, GetCurrentDateTime());
                bAddedSongs=true;

                #if SUPPORT_SONG_MANAGER
                int result=g_Global.m_SongManager.AddSong(song);
                localSongUpdate = result;
                #endif
            }
            else
            {
                nResult = EC_TTS_AUDIO_SHORT;
                RemoveFile(strFilePath.C_Str());
            }
        }
        else
        {
            nResult = EC_TARGET_NOTEXIST;
        }
    }

    /************************************************************/
    LOG(FORMAT("ECODE: %d\n", nResult), LV_ERROR);

    // 回复终端
    CWebSection* pWebSection = g_Global.m_WebSections.GetWebSectionBySocketID(tts->GetUID());
    string strBuf;
    if(tts->GetCommand() == "text_to_speech")
    {
        strBuf = CWebProtocol::CmdText2Speech(tts->GetFileName(), nResult);
        if(pWebSection != NULL)
        {
            g_Global.m_WebNetwork.m_WebSend.CommandSend(strBuf.data(), *pWebSection);
        }
    }
    else if(tts->GetCommand() == "play_specified_source")
    {
        //应答
        strBuf = CWebProtocol::CmdResponsePlaySpecifiedSource(nResult);
        if(pWebSection != NULL)
        {
            g_Global.m_WebNetwork.m_WebSend.CommandSend(strBuf.data(), *pWebSection);
        }
        
        //开始指定播放TTS任务
        if(nResult == EC_SUCCESS)
        {
            //todo 判断getPlayTask参数是否正确
            g_Global.m_PlayQueue.PushPlayTask(tts->GetPlayTask());
        }
    }


    if (bAddedSongs)
    {
        //g_Global.WriteXmlFile(FILE_PLAYLIST);
        //TTS list的账户属于管理员，所以此处要加入实际的用户
        g_Global.m_PlayList.PushWriteXmlTask(tts->GetAccount().data());
    }

    //判断本地歌曲有没有变化，如果有的话，发送给所有web
    #if SUPPORT_SONG_MANAGER
    if(localSongUpdate)
    {
        g_Global.m_WebNetwork.ForwardLocalSongInfoToWeb(NULL);
    }
    #endif

    //释放指针
    delete(tts);

    return NULL;
}