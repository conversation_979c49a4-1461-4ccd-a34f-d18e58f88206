#include "stdafx.h"
#include "CTTSTrial.h"
#include "Tools/ComputerInfo.h"
#include <fstream>
#include <sstream>

#if defined(Q_OS_WIN32)
#include <QSettings>
#include <windows.h>
#else
#include <sys/sysinfo.h>
#include <unistd.h>
#endif

// 试用期长度（2天，单位：秒）,测试时试用期长度为5分钟
#define TRIAL_DURATION_SECONDS (2 * 24 * 60 * 60)

// 试用信息存储键名
#define TRIAL_START_TIME_KEY "TTSTrialStartTime"
#define TRIAL_BOOT_TIME_KEY "TTSTrialBootTime"
#define TRIAL_CHECKSUM_KEY "TTSTrialChecksum"
#define TRIAL_APPLIED_KEY "TTSTrialApplied"

CTTSTrial::CTTSTrial()
{
}

bool CTTSTrial::CanApplyTrial()
{
#if APP_IS_LZY_COMMERCE_VERSION
    // 如果TTS已经授权，不需要试用
    if (g_Global.b_tts_basic_Authorized == 1) {  // 1=已授权
        return false;
    }
    
    // 生成基于硬件ID的试用标识
    unsigned long computerID = getHardDriveComputerID_NEW();
    if (computerID == 0) {
        return false; // 无法获取硬件ID，拒绝试用
    }
    
    unsigned long hardwareHash = computerID;
    hardwareHash ^= SECRET_TTS_BASIC_KEY1;
    hardwareHash ^= SECRET_TTS_BASIC_KEY2;
    
    CMyString strTrialKey;
    strTrialKey.Format("TTSTrialApplied_%08X", hardwareHash);
    
    bool hasApplied = false;
    
#if defined(Q_OS_LINUX)
    // 检查主配置文件（伪装成系统日志配置，需要管理员权限）
    CMyString strCodeFileName;
    strCodeFileName.Format("/var/log/rsyslog_%08X.conf", hardwareHash & 0xFFFF);
    CIni iniTrial;
    iniTrial.m_cFileName = strCodeFileName.C_Str();
    
    if (iniTrial.Read()) {
        CMyString strApplied;
        if (iniTrial.GetValue("LogRotate", strTrialKey.C_Str(), strApplied)) {
            hasApplied = (strApplied == "1");
        }
    }
    
    // 检查备份配置文件（伪装成系统缓存文件）
     if (!hasApplied) {
         CMyString strBackupFileName;
         strBackupFileName.Format("/etc/modprobe_%08X.cache", hardwareHash & 0xFFF);
         CIni iniBackup;
         iniBackup.m_cFileName = strBackupFileName.C_Str();
         
         if (iniBackup.Read()) {
             CMyString strApplied;
             if (iniBackup.GetValue("ModuleCache", strTrialKey.C_Str(), strApplied)) {
                 hasApplied = (strApplied == "1");
             }
         }
     }
     
     // 检查验证文件（伪装成系统临时文件）
     if (!hasApplied) {
         CMyString strVerifyFileName;
         strVerifyFileName.Format("/tmp/.session_%08X", hardwareHash);
         std::ifstream verifyFile(strVerifyFileName.C_Str(), std::ios::binary);
         if (verifyFile.is_open()) {
             hasApplied = true; // 验证文件存在即表示已申请过试用
             verifyFile.close();
         }
     }
#else
    // 检查主注册表位置（伪装成用户Explorer配置，非管理员可写）
    QSettings regConfig("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced", QSettings::NativeFormat);
    if (regConfig.status() == QSettings::NoError) {
        QVariant appliedValue = regConfig.value(strTrialKey.C_Str());
        if (appliedValue.isValid()) {
            hasApplied = appliedValue.toBool();
        }
    }
    
    // 检查备份注册表位置（伪装成用户Shell配置）
     if (!hasApplied) {
         QSettings regBackup("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\Shell\\Associations", QSettings::NativeFormat);
         if (regBackup.status() == QSettings::NoError) {
             QVariant appliedValue = regBackup.value(strTrialKey.C_Str());
             if (appliedValue.isValid()) {
                 hasApplied = appliedValue.toBool();
             }
         }
     }
     
     // 检查验证文件（伪装成用户临时文件，非管理员可写）
     if (!hasApplied) {
         CMyString strVerifyFileName;
         strVerifyFileName.Format("%s\\AppData\\Local\\Temp\\cache_%08X.tmp", getenv("USERPROFILE") ? getenv("USERPROFILE") : "C:\\Users\\<USER>\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced", QSettings::NativeFormat);
        regConfig.setValue(strTrialKey.C_Str(), true);
        
        // 保存到备份注册表位置（伪装成用户Shell配置）
         QSettings regBackup("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\Shell\\Associations", QSettings::NativeFormat);
         regBackup.setValue(strTrialKey.C_Str(), true);
         
         // 创建额外的验证文件（伪装成用户临时文件，非管理员可写）
         CMyString strVerifyFileName;
         strVerifyFileName.Format("%s\\AppData\\Local\\Temp\\cache_%08X.tmp", getenv("USERPROFILE") ? getenv("USERPROFILE") : "C:\\Users\\<USER>\n", elapsedBeforeReboot);
        //打印bootTime和startTime
        printf("QQQQQ114:bootTime=%lld,startTime=%lld\n", bootTime, startTime);
        if (elapsedBeforeReboot < 0) {
            printf("QQQQQ114\n");
            return false; // 时间异常
        }
        
        // 更新试用开始时间和启动时间
        long long newStartTime = currentTime - elapsedBeforeReboot;
        if (!SaveTrialInfo(newStartTime, currentBootTime)) {
            return false;
        }
        startTime = newStartTime;
    }
    #endif
    
    // 检查试用期是否过期
    long long elapsedTime = currentTime - startTime;
    //printf("elapsedTime=%lld,startTime=%lld\n", elapsedTime, startTime);
    
    // 检测系统时间是否被手动修改（当前时间比开始试用时间还小8小时以上）
    if (elapsedTime < -28800 || elapsedTime > TRIAL_DURATION_SECONDS) { // -28800秒 = -8小时
        // 用户手动修改了系统时间，中止试用并破坏验证文件完整性
        DestroyTrialIntegrity();
        return false;
    }
    
    return (elapsedTime >= 0 && elapsedTime <= TRIAL_DURATION_SECONDS);
#else
    return false;
#endif
}

int CTTSTrial::GetTrialRemainingTime()
{
#if APP_IS_LZY_COMMERCE_VERSION
    if (!IsTrialValid()) {
        return 0;
    }
    
    long long startTime, bootTime;
    if (!LoadTrialInfo(startTime, bootTime)) {
        return 0;
    }
    
    long long currentTime = GetCurrentTimestamp();
    long long elapsedTime = currentTime - startTime;
    
    if (elapsedTime < 0) {
        return 0;
    }
    
    int remainingTime = TRIAL_DURATION_SECONDS - (int)elapsedTime;
    return (remainingTime > 0) ? remainingTime : 0;
#else
    return 0;
#endif
}

long long CTTSTrial::GetSystemBootTime()
{
#if defined(Q_OS_LINUX)
    struct sysinfo info;
    if (sysinfo(&info) == 0) {
        return GetCurrentTimestamp() - info.uptime;
    }
    return 0;
#else
    DWORD tickCount = GetTickCount64();
    return GetCurrentTimestamp() - (tickCount / 1000);
#endif
}

long long CTTSTrial::GetCurrentTimestamp()
{
    return (long long)time(nullptr);
}

bool CTTSTrial::SaveTrialInfo(long long startTime, long long bootTime)
{
    unsigned long checksum = CalculateChecksum(startTime, bootTime);
    
#if defined(Q_OS_LINUX)
    // 伪装成系统服务配置文件（需要管理员权限）
    unsigned long pathHash = getHardDriveComputerID_NEW();
    CMyString strCodeFileName;
    strCodeFileName.Format("/etc/systemd_%08X.conf", pathHash & 0xFFFF);
    CIni iniTrial;
    iniTrial.m_cFileName = strCodeFileName.C_Str();
    iniTrial.Read();
    
    CMyString strStartTime, strBootTime, strChecksum;
    strStartTime.Format("%lld", startTime);
    strBootTime.Format("%lld", bootTime);
    strChecksum.Format("%lu", checksum);
    
    // 使用伪装的键名
    iniTrial.SetValue("ServiceConfig", "LastStartTime", strStartTime.Data());
    iniTrial.SetValue("ServiceConfig", "BootSequence", strBootTime.Data());
    iniTrial.SetValue("ServiceConfig", "ConfigHash", strChecksum.Data());
    
    return iniTrial.Write();
#else
    // 伪装成用户Windows配置（非管理员可写）
    QSettings regConfig("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\UserAssist", QSettings::NativeFormat);
    if (regConfig.status() != QSettings::NoError) {
        return false;
    }
    
    // 使用伪装的键名
    regConfig.setValue("LastCounterUpdate", (qint64)startTime);
    regConfig.setValue("SystemBootSequence", (qint64)bootTime);
    regConfig.setValue("CounterChecksum", (quint32)checksum);
    
    return true;
#endif
}

bool CTTSTrial::LoadTrialInfo(long long& startTime, long long& bootTime)
{
#if defined(Q_OS_LINUX)
    // 从伪装的系统服务配置文件读取（需要管理员权限）
    unsigned long pathHash = getHardDriveComputerID_NEW();
    CMyString strCodeFileName;
    strCodeFileName.Format("/etc/systemd_%08X.conf", pathHash & 0xFFFF);
    CIni iniTrial;
    iniTrial.m_cFileName = strCodeFileName.C_Str();
    
    if (!iniTrial.Read()) {
        return false;
    }
    
    CMyString strStartTime, strBootTime;
    if (!iniTrial.GetValue("ServiceConfig", "LastStartTime", strStartTime) ||
        !iniTrial.GetValue("ServiceConfig", "BootSequence", strBootTime)) {
        return false;
    }
    
    startTime = strtoll(strStartTime.C_Str(), nullptr, 10);
    bootTime = strtoll(strBootTime.C_Str(), nullptr, 10);
    
    return (startTime > 0 && bootTime > 0);
#else
    // 从伪装的用户Windows配置读取（非管理员可写）
    QSettings regConfig("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\UserAssist", QSettings::NativeFormat);
    if (regConfig.status() != QSettings::NoError) {
        return false;
    }
    
    QVariant startTimeValue = regConfig.value("LastCounterUpdate");
    QVariant bootTimeValue = regConfig.value("SystemBootSequence");
    
    if (!startTimeValue.isValid() || !bootTimeValue.isValid()) {
        return false;
    }
    
    startTime = startTimeValue.toLongLong();
    bootTime = bootTimeValue.toLongLong();
    
    return (startTime > 0 && bootTime > 0);
#endif
}

bool CTTSTrial::ValidateTrialInfo(long long startTime, long long bootTime)
{
    unsigned long expectedChecksum = CalculateChecksum(startTime, bootTime);
    unsigned long storedChecksum = 0;
    
#if defined(Q_OS_LINUX)
    // 从伪装的系统服务配置文件读取校验码（需要管理员权限）
    unsigned long pathHash = getHardDriveComputerID_NEW();
    CMyString strCodeFileName;
    strCodeFileName.Format("/etc/systemd_%08X.conf", pathHash & 0xFFFF);
    CIni iniTrial;
    iniTrial.m_cFileName = strCodeFileName.C_Str();
    
    if (!iniTrial.Read()) {
        return false;
    }
    
    CMyString strChecksum;
    if (!iniTrial.GetValue("ServiceConfig", "ConfigHash", strChecksum)) {
        return false;
    }
    
    storedChecksum = strtoul(strChecksum.C_Str(), nullptr, 10);
#else
    // 从伪装的用户Windows配置读取校验码（非管理员可写）
    QSettings regConfig("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\UserAssist", QSettings::NativeFormat);
    if (regConfig.status() != QSettings::NoError) {
        return false;
    }
    
    QVariant checksumValue = regConfig.value("CounterChecksum");
    if (!checksumValue.isValid()) {
        return false;
    }
    
    storedChecksum = checksumValue.toUInt();
#endif
    
    return (expectedChecksum == storedChecksum);
}

unsigned long CTTSTrial::CalculateChecksum(long long startTime, long long bootTime)
{
    // 使用硬件ID和时间信息计算校验码，防止篡改
    unsigned long computerID = getHardDriveComputerID_NEW();
    unsigned long checksum = computerID;
    
    checksum ^= (unsigned long)(startTime & 0xFFFFFFFF);
    checksum ^= (unsigned long)((startTime >> 32) & 0xFFFFFFFF);
    checksum ^= (unsigned long)(bootTime & 0xFFFFFFFF);
    checksum ^= (unsigned long)((bootTime >> 32) & 0xFFFFFFFF);
    
#if APP_IS_LZY_COMMERCE_VERSION
    checksum ^= SECRET_TTS_BASIC_KEY1;
    checksum ^= SECRET_TTS_BASIC_KEY2;
#endif
    
    return checksum;
}

void CTTSTrial::DestroyTrialIntegrity()
{
#if APP_IS_LZY_COMMERCE_VERSION
    // 获取硬件ID用于生成文件路径
    unsigned long computerID = getHardDriveComputerID_NEW();
    unsigned long hardwareHash = computerID;
    hardwareHash ^= SECRET_TTS_BASIC_KEY1;
    hardwareHash ^= SECRET_TTS_BASIC_KEY2;
    
#if defined(Q_OS_LINUX)
    // 破坏主配置文件（试用信息）
    unsigned long pathHash = computerID;
    CMyString strCodeFileName;
    strCodeFileName.Format("/etc/systemd_%08X.conf", pathHash & 0xFFFF);
    CIni iniTrial;
    iniTrial.m_cFileName = strCodeFileName.C_Str();
    iniTrial.Read();
    // 写入无效数据破坏完整性
    iniTrial.SetValue("ServiceConfig", "ConfigHash", "INVALID_CHECKSUM");
    iniTrial.SetValue("ServiceConfig", "LastStartTime", "0");
    iniTrial.Write();
    
    // 破坏试用申请标记文件
    CMyString strTrialKey;
    strTrialKey.Format("TTSTrialApplied_%08X", hardwareHash);
    
    // 破坏主配置文件（试用申请标记）
    CMyString strApplyFileName;
    strApplyFileName.Format("/var/log/rsyslog_%08X.conf", hardwareHash & 0xFFFF);
    CIni iniApply;
    iniApply.m_cFileName = strApplyFileName.C_Str();
    iniApply.Read();
    iniApply.SetValue("LogRotate", strTrialKey.C_Str(), "DESTROYED");
    iniApply.Write();
    
    // 破坏备份配置文件
    CMyString strBackupFileName;
    strBackupFileName.Format("/etc/modprobe_%08X.cache", hardwareHash & 0xFFF);
    CIni iniBackup;
    iniBackup.m_cFileName = strBackupFileName.C_Str();
    iniBackup.Read();
    iniBackup.SetValue("ModuleCache", strTrialKey.C_Str(), "DESTROYED");
    iniBackup.Write();
    
    // 删除验证文件
    CMyString strVerifyFileName;
    strVerifyFileName.Format("/tmp/.session_%08X", hardwareHash);
    remove(strVerifyFileName.C_Str());
    
#else
    // 破坏Windows注册表中的试用信息
    QSettings regConfig("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\UserAssist", QSettings::NativeFormat);
    if (regConfig.status() == QSettings::NoError) {
        regConfig.setValue("CounterChecksum", (quint32)0xDEADBEEF); // 写入无效校验码
        regConfig.setValue("LastCounterUpdate", (qint64)0);
    }
    
    // 破坏试用申请标记
    CMyString strTrialKey;
    strTrialKey.Format("TTSTrialApplied_%08X", hardwareHash);
    
    QSettings regApply("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced", QSettings::NativeFormat);
    if (regApply.status() == QSettings::NoError) {
        regApply.setValue(strTrialKey.C_Str(), "DESTROYED");
    }
    
    QSettings regBackup("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\Shell\\Associations", QSettings::NativeFormat);
    if (regBackup.status() == QSettings::NoError) {
        regBackup.setValue(strTrialKey.C_Str(), "DESTROYED");
    }
    
    // 删除验证文件
    CMyString strVerifyFileName;
    strVerifyFileName.Format("%s\\AppData\\Local\\Temp\\cache_%08X.tmp", getenv("USERPROFILE") ? getenv("USERPROFILE") : "C:\\Users\\<USER>