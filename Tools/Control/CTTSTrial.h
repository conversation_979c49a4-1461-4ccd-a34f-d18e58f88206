#ifndef CTTSTRIAL_H
#define CTTSTRIAL_H

#include "iostream"
#include "Global/const.h"

using namespace std;

/**
 * TTS试用管理类
 * 提供3天一次性试用功能，防止系统时间修改漏洞
 * 支持Linux和Windows平台
 */
class CTTSTrial
{
public:
    CTTSTrial();

    // 检查是否可以申请试用
    static bool CanApplyTrial();
    
    // 申请试用（开始试用期）
    static bool ApplyTrial();
    
    // 检查试用是否有效
    static bool IsTrialValid();
    
    // 获取试用剩余时间（秒）
    static int GetTrialRemainingTime();
    
private:
    // 获取系统启动时间戳（防止时间修改）
    static long long GetSystemBootTime();
    
    // 获取当前时间戳
    static long long GetCurrentTimestamp();
    
    // 保存试用信息到本地
    static bool SaveTrialInfo(long long startTime, long long bootTime);
    
    // 读取试用信息
    static bool LoadTrialInfo(long long& startTime, long long& bootTime);
    
    // 验证试用信息的完整性
    static bool ValidateTrialInfo(long long startTime, long long bootTime);
    
    // 计算校验码
    static unsigned long CalculateChecksum(long long startTime, long long bootTime);
    
    // 破坏试用验证文件完整性（防止时间修改后重新试用）
    static void DestroyTrialIntegrity();
};

#endif // CTTSTRIAL_H