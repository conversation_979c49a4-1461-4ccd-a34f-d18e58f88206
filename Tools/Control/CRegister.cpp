#include "stdafx.h"
#include "CRegister.h"
#include "Tools/ComputerInfo.h"
#if APP_IS_LZY_COMMERCE_VERSION
#include "CTTSTrial.h"
#endif
#if defined(Q_OS_WIN32)
#include <QSettings>
#endif

CRegister::CRegister()
{

}

#if APP_IS_LZY_COMMERCE_VERSION
bool CRegister::HasBasicTTSRegister()
{
    CMyString strCode;
    CIni   iniRegister;
    CMyString strCodeFileName;

    #if defined(Q_OS_LINUX)
    strCodeFileName.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, REGISTER_FILE_NAME);
    iniRegister.m_cFileName = strCodeFileName.C_Str();
    if(iniRegister.Read() == FALSE)
    {
        return FALSE;
    }
    bool foundRegisterCode = iniRegister.GetValue(REGISTER_FILE_CONFIG, REGISTER_TTS_BASIC_CODE, strCode);
    if(!foundRegisterCode)
    {
        return FALSE;
    }
    #else
    QSettings regConfig("HKEY_CURRENT_USER\\Software\\IPNetworking\\Config",QSettings::NativeFormat);
    QSettings::Status status = regConfig.status();
     if (status != QSettings::NoError) {
        return FALSE;
    }
    QVariant registerCode = regConfig.value("TTSBasicCode");
    if(!registerCode.isValid()) {
        return FALSE;
    }
    QString registerCode_qstr = registerCode.toString();
    string registerCode_str = registerCode_qstr.toStdString();
    strCode = registerCode_str;
    #endif

    unsigned long ulCode =  strtoul(strCode.C_Str(),NULL,16);
    ulCode ^= SECRET_TTS_BASIC_KEY2;
    ulCode ^= SECRET_TTS_BASIC_KEY1;
    unsigned long computerID=getHardDriveComputerID_NEW();
    if(computerID && (ulCode == computerID))
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}
#endif



bool CRegister::HasCloudControlRegister()
{
    CMyString strCode;
    CIni   iniRegister;
    CMyString strCodeFileName;

    #if defined(Q_OS_LINUX)
    strCodeFileName.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, REGISTER_FILE_NAME);
    iniRegister.m_cFileName = strCodeFileName.C_Str();
    if(iniRegister.Read() == FALSE)
    {
        return FALSE;
    }
    bool foundRegisterCode = iniRegister.GetValue(REGISTER_FILE_CONFIG, REGISTER_CLOUD_CONTROL_CODE, strCode);
    if(!foundRegisterCode)
    {
        return FALSE;
    }
    #else
    QSettings regConfig("HKEY_CURRENT_USER\\Software\\IPNetworking\\Config",QSettings::NativeFormat);
    QSettings::Status status = regConfig.status();
     if (status != QSettings::NoError) {
        return FALSE;
    }
    QVariant registerCode = regConfig.value("CloudControlCode");
    if(!registerCode.isValid()) {
        return FALSE;
    }
    QString registerCode_qstr = registerCode.toString();
    string registerCode_str = registerCode_qstr.toStdString();
    strCode = registerCode_str;
    #endif

    unsigned long ulCode =  strtoul(strCode.C_Str(),NULL,16);
    ulCode ^= SECRET_CLOUD_CONTROL_KEY2;
    ulCode ^= SECRET_CLOUD_CONTROL_KEY1;
    unsigned long computerID=getHardDriveComputerID_NEW();
    if(computerID && (ulCode == computerID))
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}

bool CRegister::HasRegister()
{
    CIni   iniRegister;
    CMyString strCodeFileName;

    #if APP_IS_LZY_COMMERCE_VERSION
    g_Global.m_AppType = APP_FORMAL;

    //判断TTS功能是否已经授权
    bool hasRegister = HasBasicTTSRegister();
    if (hasRegister) {
        g_Global.b_tts_basic_Authorized = 1;  // 1=已授权
        printf("TTS Register Check: TRUE\n");
    } else {
        // 检查试用状态
        CTTSTrial ttsTrial;

        // 检查是否可以申请试用
        if (ttsTrial.CanApplyTrial())
        {
            g_Global.b_tts_basic_Authorized = 0;  // 未授权且未试用
        }
        else
        {
            g_Global.b_tts_basic_Authorized = 2;  // 默认先认为是未授权但正在试用，以便进行有效性判断
            // 检查试用是否仍然有效
            if (ttsTrial.IsTrialValid())
            {
                g_Global.b_tts_basic_Authorized = 2;  // 2=未授权但正在试用
                printf("TTS Trial Valid\n");
            }
            else
            {
                g_Global.b_tts_basic_Authorized = 3;  // 3=未授权且试用期结束
            }
        }
    }
    
    printf("TTS Valid=%s\n", g_Global.b_tts_basic_Authorized == 1 ? "AUTHORIZED" : 
           g_Global.b_tts_basic_Authorized == 2 ? "TRIALING" : g_Global.b_tts_basic_Authorized == 3 ? "TRIAL_EXPIRED" : "UNAUTHORIZED");

    g_Global.b_cloudControl_Authorized = true;   //龙之音商用版本强制云控制功能授权

    return TRUE;
    #endif

    //判断云控制功能是否已经授权
    g_Global.b_cloudControl_Authorized = HasCloudControlRegister();
    printf("Cloud Control Valid=%s\n",g_Global.b_cloudControl_Authorized?"TRUE":"FALSE");

#if defined(Q_OS_LINUX)
#if 0
    //如果是虚拟机，强制为已注册
    FILE * fp=NULL;
    char pRetMsg[1024]={0};
    if ((fp = popen("dmesg | grep \"Detected virtualization\"", "r") ) == NULL)
    {
        printf("Not Detected virtualization!\n");
    }
    else
    {
        fgets(pRetMsg,1024, fp);
        if(strlen(pRetMsg) >= strlen("Detected virtualization"))
        {
            printf("Detected virtualization...\n");
            g_Global.m_AppType = APP_VIRTUAL;
            return TRUE;
        }
        pclose(fp);
    }
#else
    //如果是虚拟机，强制为已注册
    FILE * fp=NULL;
    char pRetMsg[1024]={0};
    if ((fp = popen("systemd-detect-virt", "r") ) == NULL)
    {
        printf("Not Detected virtualization!\n");
    }
    else
    {
        fgets(pRetMsg,1024, fp);
        //不是null，那么代表是运行在虚拟环境
        printf("virtual=%s\n",pRetMsg);
        if(strstr(pRetMsg,"none") == NULL)
        {
            printf("Detected virtualization...\n");
            g_Global.m_AppType = APP_VIRTUAL;
            return TRUE;
        }
        pclose(fp);
    }
#endif
#else
    QProcess p;
    QString cmd = "wmic csproduct get vendor";
    p.start(cmd);
    p.waitForFinished();
    QString result = QString::fromLocal8Bit(p.readAllStandardOutput());
    if (result.contains("VMware, Inc")) {
        //如果是虚拟机，强制为已注册
        printf("system is run in virtual!\n");
        g_Global.m_AppType = APP_VIRTUAL;
        return TRUE;
    } else {
        printf("system is not run in virtual!\n");
    }

#endif

#if defined(Q_OS_LINUX)
    strCodeFileName.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, REGISTER_FILE_NAME);
    iniRegister.m_cFileName = strCodeFileName.C_Str();
    if(iniRegister.Read() == FALSE)
    {
        return FALSE;
    }
#else
     QSettings regConfig("HKEY_CURRENT_USER\\Software\\IPNetworking\\Config",QSettings::NativeFormat);
     QSettings::Status status = regConfig.status();
     if (status != QSettings::NoError) {
        return FALSE;
    }
#endif

    // 注册码
    CMyString strCode;
    bool foundRegisterCode=false;
#if defined(Q_OS_LINUX)
    foundRegisterCode = iniRegister.GetValue(REGISTER_FILE_CONFIG, REGISTER_FILE_ITER_CODE, strCode);
#else
    QVariant registerCode;
    QVariant licenseValue = regConfig.value("License");
    if (!licenseValue.isValid()) {
        //查看是否有新的字段，如果也没有，那么返回未注册
         registerCode = regConfig.value("RegisterCode");
         if(!registerCode.isValid()) {
            return FALSE;
         }
    } else {
        UINT sn=licenseValue.toInt();
        unsigned long computerID=getHardDriveComputerID_NEW();
        if(!(computerID && (sn == computerID)))
        {
            return FALSE;
        }
        //查看是否有新的字段，如果没有，返回注册成功
         registerCode = regConfig.value("RegisterCode");
         if(!registerCode.isValid()) {
            return TRUE;
         }
    }

    //判断新的字段
    foundRegisterCode=true;
    QString registerCode_qstr = registerCode.toString();
    string registerCode_str = registerCode_qstr.toStdString();
    strCode=registerCode_str;
#endif
    if (foundRegisterCode)
    {
        CMyString strSubCode=strCode;
        if(strCode.GetLength() == 8 || strCode.GetLength() == 16)
        {
            // 注册码16位的情况
            if(strCode.GetLength() == 16)
            {
                strSubCode.Format("%s%s%s", strCode.Mid(0, 2).C_Str(), strCode.Mid(6, 4).C_Str(), strCode.Mid(14, 2).C_Str());
                 //判断是否存在日期
                CMyString dateId_str=strCode.Mid(2,2);
                CMyString date_str=strCode.Mid(4,2)+strCode.Mid(10,3);
                CMyString checkSum_str=strCode.Mid(13,1);
                if(dateId_str == "LD")     //找到有效标识
                {
                    //获取日期
                    //printf("dateId_str=%s,date_str=%s,checkSum_str=%s\n",dateId_str.Data(),date_str.Data(),checkSum_str.Data());
                    //将checkSum_str从16进制字符转换为10进制整数
                    unsigned long checkSum_hex = strtoul(checkSum_str.C_Str(),NULL,16);
                    //printf("Found limit date identify!,checkcum=0x%x,checkSum_hex=%d\n",CProtocol::GetChecksum(date_str.Data(),5),checkSum_hex);
                    //printf("checksum_lowByte=%d\n",CProtocol::GetChecksum(date_str.Data(),5)&0x0F);
                    unsigned int lowByteVal=CProtocol::GetChecksum(date_str.Data(),5)&0x0F;
                    if(lowByteVal == checkSum_hex)
                    {
                        //正确
                        char year_str[3] = { date_str[0], date_str[1], '\0' };
                        int year = (int)strtol(year_str, NULL, 16) + 2000;
                        
                        char month_str[2] = { date_str[2], '\0' };
                        int month = (int)strtol(month_str, NULL, 16);
                        
                        char day_str[3] = { date_str[3], date_str[4], '\0' };
                        int day = (int)strtol(day_str, NULL, 16);

                        printf("Valid Date:year=%d,month=%d,day=%d\n",year,month,day);

                        CTime  t_LimitDate(year,month,day,23,59,59);

                        CTime tNow = CTime::GetCurrentTimeT();
                        if( tNow > t_LimitDate )
                        {
                            g_Global.m_AppType = APP_REGISTER_INVALID;
                            return FALSE;
                        }
                        else
                        {
                            g_Global.m_RegisterLimitDate = t_LimitDate;
                        }
                    }
                    else
                    {
                        //错误，注册失败
                        printf("limint date checksum failed!\n");
                        return FALSE;
                    }
                }
                else
                {
                    //没找到，但是是16位，暂且不处理，以防万一
                    printf("Not Found limit date identify!\n");
                }
            }
            unsigned long ulCode =  strtoul(strSubCode.C_Str(),NULL,16);
            ulCode ^= SECRET_KEY2;
            ulCode ^= SECRET_KEY1;
            unsigned long computerID=getHardDriveComputerID_NEW();
            if(computerID && (ulCode == computerID))
            {
                LOG("register ok!\n", LV_INFO);
                return TRUE;
            }
            #if defined(Q_OS_WIN32)
            unsigned long computerID_old=getHardDriveComputerID_OLD_WIN32();
            if(computerID_old && (ulCode == computerID_old))
            {
                LOG("register old ok!\n", LV_INFO);
                return TRUE;
            }
            #endif
        }
    }
    else{
        printf("error.....\n");
    }

    return FALSE;
}

bool CRegister::Register(CMyString strCode)
{
    //LOG(strCode.C_Str(), LV_INFO);
    CMyString strSubCode=strCode;
    bool isRegisterLimitDate=false;
    // 注册码16位的情况
    if(strCode.GetLength() == 16)
    {
        strSubCode.Format("%s%s%s", strCode.Mid(0, 2).C_Str(), strCode.Mid(6, 4).C_Str(), strCode.Mid(14, 2).C_Str());

        //判断是否存在日期
        CMyString dateId_str=strCode.Mid(2,2);
        CMyString date_str=strCode.Mid(4,2)+strCode.Mid(10,3);
        CMyString checkSum_str=strCode.Mid(13,1); 
        if(dateId_str == "LD")     //找到有效标识
        {
            //获取日期
            //printf("dateId_str=%s,date_str=%s,checkSum_str=%s\n",dateId_str.Data(),date_str.Data(),checkSum_str.Data());
            //将checkSum_str从16进制字符转换为10进制整数
            unsigned long checkSum_hex = strtoul(checkSum_str.C_Str(),NULL,16);
            //printf("Found limit date identify!,checkcum=0x%x,checkSum_hex=%d\n",CProtocol::GetChecksum(date_str.Data(),5),checkSum_hex);
            //printf("checksum_lowByte=%d\n",CProtocol::GetChecksum(date_str.Data(),5)&0x0F);
            unsigned int lowByteVal=CProtocol::GetChecksum(date_str.Data(),5)&0x0F;
            if(lowByteVal == checkSum_hex)
            {
                //正确
                char year_str[3] = { date_str[0], date_str[1], '\0' };
                int year = (int)strtol(year_str, NULL, 16) + 2000;
                
                char month_str[2] = { date_str[2], '\0' };
                int month = (int)strtol(month_str, NULL, 16);
                
                char day_str[3] = { date_str[3], date_str[4], '\0' };
                int day = (int)strtol(day_str, NULL, 16);

                printf("Valid Date:year=%d,month=%d,day=%d\n",year,month,day);
 
                isRegisterLimitDate=true;
            }
            else
            {
                //错误，注册失败
                printf("limint date checksum failed!\n");
                return FALSE;
            }
        }
        else
        {
            printf("Not Found limit date identify!\n");
        }
    }
    else if(strCode.GetLength() == 15)
    {
        strSubCode.Format("%s%s%s", strCode.Mid(0, 2).C_Str(), strCode.Mid(6, 4).C_Str(), strCode.Mid(14, 1).C_Str());
    }
    //LOG(strSubCode.C_Str(), LV_INFO);

    unsigned long ulCode = strtoul(strSubCode.Data(), NULL, 16);
    //LOG(FORMAT("ulCode1 = %x", ulCode), LV_INFO);
    ulCode ^= SECRET_KEY2;
    ulCode ^= SECRET_KEY1;
    //LOG(FORMAT("ulCode2 = %d", ulCode), LV_INFO);
    unsigned long computerID=getHardDriveComputerID_NEW();
    if(computerID && (ulCode == computerID))
    {
	#if defined(Q_OS_LINUX)
        CIni   iniRegister;
        CMyString strCodeFileName;
        strCodeFileName.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, REGISTER_FILE_NAME);
        iniRegister.m_cFileName = strCodeFileName.C_Str();
        iniRegister.Read();

        // 注册码
        if(isRegisterLimitDate)
        {
            iniRegister.SetValue(REGISTER_FILE_CONFIG, REGISTER_FILE_ITER_CODE, strCode.C_Str());
        }
        else
        {
            iniRegister.SetValue(REGISTER_FILE_CONFIG, REGISTER_FILE_ITER_CODE, strSubCode.C_Str());
        }
        iniRegister.Write();
	#else
	        //写入注册表
        QSettings regConfig("HKEY_CURRENT_USER\\Software\\IPNetworking\\Config",QSettings::NativeFormat);
        if(isRegisterLimitDate)
        {
            regConfig.setValue("RegisterCode", strCode.C_Str());
        }
        else
        {
            regConfig.setValue("RegisterCode", strSubCode.C_Str());
        }
        //regConfig.setValue("License", (UINT)ulCode);
	#endif

        return TRUE;
    }

    return FALSE;
}

#if APP_IS_LZY_COMMERCE_VERSION
bool CRegister::TTS_Register(CMyString strCode)
{
    //LOG(strCode.C_Str(), LV_INFO);
    CMyString strSubCode=strCode;
    bool isRegisterLimitDate=false;
    // 注册码16位的情况
    if(strCode.GetLength() == 16)
    {
        strSubCode.Format("%s%s%s", strCode.Mid(0, 2).C_Str(), strCode.Mid(6, 4).C_Str(), strCode.Mid(14, 2).C_Str());
    }
    else if(strCode.GetLength() == 15)
    {
        strSubCode.Format("%s%s%s", strCode.Mid(0, 2).C_Str(), strCode.Mid(6, 4).C_Str(), strCode.Mid(14, 1).C_Str());
    }
    //LOG(strSubCode.C_Str(), LV_INFO);

    unsigned long ulCode = strtoul(strSubCode.Data(), NULL, 16);
    //LOG(FORMAT("ulCode1 = %x", ulCode), LV_INFO);
    ulCode ^= SECRET_TTS_BASIC_KEY2;
    ulCode ^= SECRET_TTS_BASIC_KEY1;
    //LOG(FORMAT("ulCode2 = %d", ulCode), LV_INFO);
    unsigned long computerID=getHardDriveComputerID_NEW();
    if(computerID && (ulCode == computerID))
    {
	#if defined(Q_OS_LINUX)
        CIni   iniRegister;
        CMyString strCodeFileName;
        strCodeFileName.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, REGISTER_FILE_NAME);
        iniRegister.m_cFileName = strCodeFileName.C_Str();
        iniRegister.Read();

        iniRegister.SetValue(REGISTER_FILE_CONFIG, REGISTER_TTS_BASIC_CODE, strSubCode.C_Str());
        
        iniRegister.Write();
	#else
	    //写入注册表
        QSettings regConfig("HKEY_CURRENT_USER\\Software\\IPNetworking\\Config",QSettings::NativeFormat);
        regConfig.setValue("TTSBasicCode", strSubCode.C_Str());
	#endif

        g_Global.b_tts_basic_Authorized = 1;

        return TRUE;
    }

    return FALSE;
}
#endif

bool CRegister::CloudControl_Register(CMyString strCode)
{
    //LOG(strCode.C_Str(), LV_INFO);
    CMyString strSubCode=strCode;
    bool isRegisterLimitDate=false;
    // 注册码16位的情况
    if(strCode.GetLength() == 16)
    {
        strSubCode.Format("%s%s%s", strCode.Mid(0, 2).C_Str(), strCode.Mid(6, 4).C_Str(), strCode.Mid(14, 2).C_Str());
    }
    else if(strCode.GetLength() == 15)
    {
        strSubCode.Format("%s%s%s", strCode.Mid(0, 2).C_Str(), strCode.Mid(6, 4).C_Str(), strCode.Mid(14, 1).C_Str());
    }
    //LOG(strSubCode.C_Str(), LV_INFO);

    unsigned long ulCode = strtoul(strSubCode.Data(), NULL, 16);
    //LOG(FORMAT("ulCode1 = %x", ulCode), LV_INFO);
    ulCode ^= SECRET_CLOUD_CONTROL_KEY2;
    ulCode ^= SECRET_CLOUD_CONTROL_KEY1;
    //LOG(FORMAT("ulCode2 = %d", ulCode), LV_INFO);
    unsigned long computerID=getHardDriveComputerID_NEW();
    if(computerID && (ulCode == computerID))
    {
	#if defined(Q_OS_LINUX)
        CIni   iniRegister;
        CMyString strCodeFileName;
        strCodeFileName.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, REGISTER_FILE_NAME);
        iniRegister.m_cFileName = strCodeFileName.C_Str();
        iniRegister.Read();

        iniRegister.SetValue(REGISTER_FILE_CONFIG, REGISTER_CLOUD_CONTROL_CODE, strSubCode.C_Str());
        
        iniRegister.Write();
	#else
	    //写入注册表
        QSettings regConfig("HKEY_CURRENT_USER\\Software\\IPNetworking\\Config",QSettings::NativeFormat);
        regConfig.setValue("CloudControlCode", strSubCode.C_Str());
	#endif

        g_Global.b_cloudControl_Authorized = 1;

        return TRUE;
    }

    return FALSE;
}


void *CRegister::StatusChange(void *parm)
{
    printf("CRegister:StatusChange...\n");
    //先通知WEB,再退出应用
    g_Global.m_WebNetwork.RequestUserReLogin("", RG_AUTH_CHANGER);
    usleep(400000);
    #if defined(Q_OS_LINUX)
    system("sync");
    system("sync");
    system("sync");
    #endif
    _exit(0);
}

void CRegister::RegisterStatusChange()
{
    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr,StatusChange, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}
