#include "stdafx.h"
#include "FileManager.h"
#include "Global/GlobalMethod.h"
#include <pthread.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <time.h>

vector<string> vecSrc;
vector<string> vecDst;

CFileManager::CFileManager(void)
{
    m_bFailIfExists = true;
    m_pth = 0;
}


CFileManager::~CFileManager(void)
{
    m_srcFileArray.clear();
    m_dstFileArray.clear();
}

int CopyFile(const char* src, const char* des)
{
    int nRet = 0;
    FILE* pSrc = NULL, *pDes = NULL;
    pSrc = fopen(src, "r");
    pDes = fopen(des, "w+");

    if (pSrc && pDes)
    {
        int nLen = 0;
        char szBuf[1024] = {0};
        while((nLen = fread(szBuf, 1, sizeof szBuf, pSrc)) > 0)
        {
            fwrite(szBuf, 1, nLen, pDes);
        }

    }
    else
        nRet = -1;

    if (pSrc)
        fclose(pSrc), pSrc = NULL;


    if (pDes)
        fclose(pDes), pDes = NULL;

    return nRet;
}

bool	CFileManager::IsSyncDirectory(CMyString strPathName)	// 是否为同步的目录
{
    CMyString strSyncPath = g_Global.m_strFolderPath;
    strSyncPath += HTTP_FOLDER_SEPARATOR;
    strSyncPath += HTTP_FOLDER_ADATA;
    strSyncPath += HTTP_FOLDER_SEPARATOR;
    strSyncPath += HTTP_FOLDER_PROGRAM;

    return (strPathName.Find(strSyncPath) != string::npos);
}

void CFileManager::AddDelFile(string strDelFileName)
{
    m_strDelFileArray.push_back(strDelFileName);
}

void CFileManager::CheckDelFile()
{
    vector<string>::iterator iter = m_strDelFileArray.begin();
    for(; iter != m_strDelFileArray.end(); )
    {
        string strFilePath = *iter;
        struct stat st;
        int result = stat(strFilePath.data(), &st);
        if(result == -1)
        {
            iter=m_strDelFileArray.erase(iter);     //vector中在删除一个元素后，迭代器会自动指向下一个元素
                                                    //当 nums.erase(it)删除最后一个元素之后，it就变成了一个野指针，使用 it = nums.erase(it);避免出现野指针。
                                                    //删除最后一个元素时在nums.end()上执行 ++ 操作，会出现错误。加入if判断解决。
        }
        else
        {
            time_t FileATime = st.st_atime;     // 文件最后访问时间
            time_t CurTime;
            CurTime = time(NULL);

            if(CurTime - FileATime > MAX_DELETE_TIME_SPAN)  // 超过移除时间间隔
            {
                RemoveFile(strFilePath.data());
                iter=m_strDelFileArray.erase(iter);
            }
            else
            {
                iter++;
            }
        }
    }

}


int CFileManager::CopySongFiles(CPlayList* pPlaylist, int nListIndex, vector<string> vecPathName, BOOL bFailIfExists)
{
    m_bFailIfExists = bFailIfExists;

    if (vecPathName.size() == 0)
    {
        return 0;
    }

    m_srcFileArray.clear();
    m_dstFileArray.clear();
    vecSrc.clear();
    vecDst.clear();

    int nAddCount  = vecPathName.size();
    int nSongCount = pPlaylist->GetListSongCount(nListIndex);

    if (nAddCount + nSongCount > MAX_SONGS_PER_LIST_COUNT)
    {
        return 1;
    }

    // 先轮询一遍，检查有没有错误
    for (int i=0; i<nAddCount; ++i)
    {
        CMyString strPathName  = StringToCString(vecPathName[i]);
        CMyString strSongName  = GetNameByPathName(strPathName);
        CMyString strExt	   = strPathName.Mid(strPathName.ReverseFind(('.'))+1);
        strExt.MakeUpper();

        bool	isStreamFormat	= (strExt == ("MP3") || strExt == ("WAV"));
        int		nDuration		= g_Global.m_SongTool.GetSongInfo(strPathName).nDuration;

        // 扩展名
        if (WP_IS_CENTRALIZED && !isStreamFormat)
        {
            return 2;
        }
        // 如果歌曲名称带"+"号，无法添加
        else if (strSongName.Find('+') != string::npos)
        {
            return 3;
        }
        // 参数有误
        else if (WP_IS_CENTRALIZED && nDuration <= 0)
        {
            return 4;
        }
    }

    // 目标路径 zhuyg FOLER_SOURCE change
    CMyString strDstPath = g_Global.m_strFolderPath;
    strDstPath += HTTP_FOLDER_SEPARATOR;
    strDstPath += HTTP_FOLDER_ADATA;
    strDstPath += HTTP_FOLDER_SEPARATOR;
    strDstPath += HTTP_FOLDER_PROGRAM_OTHER;

    bool	bAllSyncDir = true;	// 默认全部歌曲是在同步的目录下

    for (int i=0; i<nAddCount; ++i)
    {
        CMyString strPathName = StringToCString(vecPathName[i]);
        int		nDuration	  = g_Global.m_SongTool.GetSongInfo(strPathName).nDuration;
        int		nSize		  = g_Global.m_SongTool.GetSize(strPathName);
        CMyString strSrcPathName, strDstPathName;

        // 如果不是同步的目录
        if (!IsSyncDirectory(strPathName))
        {
            // 构成新的绝对目标路径
            strSrcPathName = strPathName;
            strDstPathName = strDstPath + ("/");
            strDstPathName += strSrcPathName.Mid(strSrcPathName.ReverseFind(('/')) + 1);

            m_srcFileArray.push_back(CStringToString(strSrcPathName));
            m_dstFileArray.push_back(CStringToString(strDstPathName));
            vecSrc = m_srcFileArray;
            vecDst = m_dstFileArray;

            strPathName = strDstPathName;

            bAllSyncDir = false;
        }

        CMyString strHttpPathName = GetHttpURLPathByPathName(strPathName);

        // 如果不存在才添加
        if (!pPlaylist->FindSongByList(strHttpPathName, nListIndex))
        {
            // 加到内存
            CSong	song;
            song.SetPathName(strHttpPathName);
            song.SetDuration(nDuration);
            song.SetSize(nSize);
            pPlaylist->AddListSong(nListIndex, song);
        }
    }

    // 修改文件
    pPlaylist->SetListDateTime(nListIndex, GetCurrentDateTime());
    //g_Global.WriteXmlFile(FILE_PLAYLIST, false);       //zhuyg
    g_Global.m_PlayList.PushWriteXmlTask("");

    // 如果不是所有歌曲在同步目录下
    if (!bAllSyncDir)
    {
        pthread_t thr;

        pthread_attr_t  attr;
        pthread_attr_init(&attr);
        pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
        int ret = pthread_create(&thr, &attr, CopyThread, (void*)this);
        if(ret != 0)
        {
            LOG(FORMAT("Create thread error!  %s:%s", __FILE__, __FUNCTION__), LV_ERROR);
        }
        pthread_attr_destroy(&attr);
    }

    return 0;
}


void CFileManager::CopyOneFile(CMyString strSrcPathName, CMyString strDstPathName, bool bFailIfExists)
{
    pthread_t thr;
    m_bFailIfExists = bFailIfExists;

    m_srcFileArray.clear();
    m_dstFileArray.clear();
    vecSrc.clear();
    vecDst.clear();

    m_srcFileArray.push_back(CStringToString(strSrcPathName));
    m_dstFileArray.push_back(CStringToString(strDstPathName));
    vecSrc = m_srcFileArray;
    vecDst = m_dstFileArray;

    pthread_attr_t  attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    int ret = pthread_create(&thr, &attr, CopyThread, (void*)this);
    if(ret != 0)
    {
        LOG(FORMAT("Create thread error!  %s:%s",  __FILE__, __FUNCTION__), LV_ERROR);
    }
    pthread_attr_destroy(&attr);
}

// 不开启线程，避免多个文件复制时 把之前未复制的文件名替换掉,造成文件遗漏，以及方便文件复制完成后的处理
void CFileManager::CopyOneFile(CMyString strSrcPathName, CMyString strDstPathName)
{
    CopyFile(strSrcPathName.C_Str() , strDstPathName.C_Str());
}

void CFileManager::CopyFileArray(vector<string> &srcFileArray, vector<string> &dstFileArray)
{
    pthread_t thr;

    m_srcFileArray.clear();
    m_dstFileArray.clear();
    vecSrc.clear();
    vecDst.clear();

    m_srcFileArray = srcFileArray;
    m_dstFileArray = dstFileArray;
    vecSrc = m_srcFileArray;
    vecDst = m_dstFileArray;

    pthread_attr_t  attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    int ret = pthread_create(&thr, &attr, CopyThread, (void*)this);
    if(ret != 0)
    {
        LOG(FORMAT("Create thread error!  %s:%s",  __FILE__, __FUNCTION__), LV_ERROR);
    }
    pthread_attr_destroy(&attr);
}

void* CFileManager::CopyThread(void* lpParam)
{
    int nFileCount = vecSrc.size();

    for (int i=0; i<nFileCount; ++i)
    {
        CopyFile(vecSrc[i].data() , vecDst[i].data());//, pFileManager->m_bFailIfExists);
    }

    return NULL;
}

bool CFileManager::StartDownloadTask(string strMac, string strFileName)
{
    if(m_pth != 0)
    {
        // 检测线程存在
        int kill_rc = pthread_kill(m_pth, 0);
        if(kill_rc == ESRCH)          // 线程不存在
        {
            NOTIFY("线程不存在");
        }
        else if(kill_rc == EINVAL)  // 信号不合法
        {
            NOTIFY("信号不合法");
            return FALSE;
        }
        else    // 线程存活
        {
            NOTIFY("线程存活");
            return FALSE;
        }
    }

    logFile.strMac = strMac;
    logFile.strFileName = strFileName;

    pthread_attr_t  attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    if(pthread_create(&m_pth, &attr, DownloadLogFilePth, (void*)&logFile) < 0)
    {
        pthread_attr_destroy(&attr);
        return FALSE;
    }
    pthread_attr_destroy(&attr);
    return TRUE;
}

void *CFileManager::DownloadLogFilePth(void *lparam)
{
    logFile_t* logFile = (logFile_t*)lparam;

    while(1)
    {
        CSection* pSection = g_Global.m_Sections.GetSectionByMac(logFile->strMac.data());
        if(pSection != NULL && pSection->IsOnline())
        {
            CLogFile* pLogFile = pSection->m_LogFiles.FindLogFile(logFile->strFileName.data());
            if(pLogFile)
            {
                if(pLogFile->HasFinished())
                {
                    break;
                }
                else if(pLogFile->NextPackID() > 0)
                {
                    g_Global.m_Network.m_CmdSend.CmdGetLogFileData(*pSection, logFile->strFileName.data(), pLogFile->NextPackID());
                    sleep(1);
                }
            }
            else
            {
                break;
            }
        }
        else
        {
            break;
        }
    }

    pthread_exit((void*)0);
}
