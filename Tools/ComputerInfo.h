#ifndef COMPUTERINFO_H
#define COMPUTERINFO_H

#include <QtCore/qglobal.h>

#include <iostream>
#include <stdio.h>
#include <cstdio>
#include <cstring>
#include <cstdlib>
#if defined(Q_OS_LINUX)
#include <arpa/inet.h>
#endif
#include <string>
#include <fstream>
#include <unistd.h>

using namespace std;

// 获取CPUID，同批次CPU序列号相同，意义不大，暂时使用
//char* GetCPUID();

// 获取硬盘ID，但硬盘格式为SSCI貌似出错，可改成IDE测试下
#if defined(Q_OS_WIN32)
unsigned long getHardDriveComputerID_OLD_WIN32();
#endif
unsigned long getHardDriveComputerID_NEW();
// 获取CPUID
//string get_cpu_id_by_asm();
//unsigned long GetCPUID();




#endif // COMPUTERINFO_H
