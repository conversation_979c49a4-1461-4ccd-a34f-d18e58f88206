/*****************************************************/

#ifndef CTIME_H
#define CTIME_H

#include <iostream>
#include <time.h>
#include <sys/time.h>
using std::string;

#define ctime_t long long

// 前向声明
class CMyString;

typedef struct __SYSTEMTIME
{
    int nYear;
    int nMonth;
    int nDayOfWeek;
    int nDay;
    int nHour;
    int nMinute;
    int nSecond;
    int nMilliseconds;

} 	SYSTEMTIME_Q;

class CTimeSpan
{
public:
    CTimeSpan();
    CTimeSpan( ctime_t time);
    CTimeSpan(long lDays, int nHours, int nMins, int nSecs);

    long long GetDays() const;
    long long GetTotalHours() const;
    long GetHours() const;
    long long GetTotalMinutes() const;
    long GetMinutes() const;
    long long GetTotalSeconds() const;
    long GetSeconds() const;

    ctime_t GetTimeSpan() const;

    CTimeSpan operator+( CTimeSpan span) const;
    CTimeSpan operator-( CTimeSpan span) const;
    CTimeSpan& operator+=( CTimeSpan span);
    CTimeSpan& operator-=( CTimeSpan span);
    bool operator==( CTimeSpan span) const;
    bool operator!=( CTimeSpan span) const;
    bool operator<( CTimeSpan span) const;
    bool operator>( CTimeSpan span) const;
    bool operator<=( CTimeSpan span) const;
    bool operator>=( CTimeSpan span) const;

private:
    ctime_t m_timeSpan;
};

//=================================================================

class CTime
{
public:
    static CTime GetCurrentTimeT();

    CTime();
    CTime(ctime_t time);
    CTime(const char* szTime);
    CTime(
        int nYear,
        int nMonth,
        int nDay,
        int nHour,
        int nMin,
        int nSec,
        int nDST = -1);
    CTime(
        unsigned short wDosDate,
        unsigned short wDosTime,
        int nDST = -1);
    CTime(
        const SYSTEMTIME_Q& st,
        int nDST = -1);

    CTime& operator=(ctime_t time);

    CTime& operator+=(CTimeSpan span);
    CTime& operator-=(CTimeSpan span);

    CTimeSpan operator-(CTime time) const;
    CTime operator-(CTimeSpan span) const;
    CTime operator+(CTimeSpan span) const;

    bool operator==(CTime time) const;
    bool operator!=(CTime time) const;
    bool operator<(CTime time) const;
    bool operator>(CTime time) const;
    bool operator<=(CTime time) const;
    bool operator>=(CTime time) const;

    struct tm* GetLocalTm(struct tm* ptm) const;

    bool GetAsSystemTime(SYSTEMTIME_Q& st) const;

    ctime_t GetTime() const;

    int GetYear() const;
    int GetMonth() const;
    int GetDay() const;
    int GetHour() const;
    int GetMinute() const;
    int GetSecond() const;
    int GetDayOfWeek() const;

    CMyString Format(const char* pszFormat) const;

private:
    time_t m_time;
};

#endif // CTIME_H




