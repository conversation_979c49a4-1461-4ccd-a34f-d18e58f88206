#ifndef FILEMANAGER_H
#define FILEMANAGER_H

#include <iostream>
#include "tools.h"
#include <vector>
#include "Model/Other/PlayList.h"
#include "Global/CType.h"

using std::string;

typedef struct logFile_t
{
    string strMac;
    string strFileName;
}logFile_t;

#define MAX_DELETE_TIME_SPAN      30*60    // 删除文件时间与生成时间间隔 30min

class CFileManager
{
public:
    CFileManager(void);

    virtual ~CFileManager(void);

public:
    int		CopySongFiles(CPlayList* pPlaylist, int nListIndex, vector<string> vecPathName, bool bFailIfExists = TRUE);
    void	CopyOneFile(CMyString strSrcPathName, CMyString strDstPathName, bool bFailIfExists);
    void    CopyOneFile(CMyString strSrcPathName, CMyString strDstPathName);           //  zhuyg
    void    CopyFileArray(vector<string>& srcFileArray, vector<string>& dstFileArray);     //
    bool	IsSyncDirectory(CMyString strPathName);	// 是否为同步的目录

    void    AddDelFile(string strDelFileName);
    void    CheckDelFile();            //

    static  void *CopyThread(void* lpParam);

    // 日志文件
    bool   StartDownloadTask(string strMac, string strFileName);
    static  void*  DownloadLogFilePth(void* lparam);

public:
    logFile_t logFile;

private:
    bool				m_bFailIfExists;				// 如果目标已经存在，不拷贝（True），覆盖目标（False）
    vector<string>      m_srcFileArray;					// 源的歌曲列表
    vector<string>      m_dstFileArray;					// 目标的歌曲列表

    vector<string>      m_strDelFileArray;      // 待删除文件(生成的日志文件)

    pthread_t m_pth;
};



#endif // FILEMANAGER_H
