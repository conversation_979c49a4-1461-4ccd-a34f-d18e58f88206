/*************************************************/

#include "CTime.h"
#include <time.h>
#include <sys/time.h>
#include <stdio.h>
#include "CMyString.h"

CTimeSpan::CTimeSpan() : m_timeSpan(0)
{
}

CTimeSpan::CTimeSpan(long long time) : m_timeSpan(time)
{

}

CTimeSpan::CTimeSpan(long lDays, int nHours, int nMins, int nSecs)
{
    m_timeSpan = nSecs + 60*(nMins + 60*(nHours + 24*lDays));
}

long long CTimeSpan::GetDays() const
{
    return( m_timeSpan/(24*3600) );
}

long long CTimeSpan::GetTotalHours() const
{
    return( m_timeSpan/3600 );
}

long CTimeSpan::GetHours() const
{
    return( long( GetTotalHours()-(GetDays()*24) ) );
}

long long CTimeSpan::GetTotalMinutes() const
{
    return( m_timeSpan/60 );
}

long CTimeSpan::GetMinutes() const
{
    return( long( GetTotalMinutes()-(GetTotalHours()*60) ) );
}

long long CTimeSpan::GetTotalSeconds() const
{
    return( m_timeSpan );
}

long CTimeSpan::GetSeconds() const
{
    return( long( GetTotalSeconds()-(GetTotalMinutes()*60) ) );
}

ctime_t CTimeSpan::GetTimeSpan() const
{
    return( m_timeSpan );
}

CTimeSpan CTimeSpan::operator+(CTimeSpan span) const
{
    return( CTimeSpan( m_timeSpan+span.m_timeSpan ) );
}

CTimeSpan CTimeSpan::operator-(CTimeSpan span) const
{
    return( CTimeSpan( m_timeSpan-span.m_timeSpan ) );
}

CTimeSpan& CTimeSpan::operator+=(CTimeSpan span)
{
    m_timeSpan += span.m_timeSpan;
    return( *this );
}

CTimeSpan& CTimeSpan::operator-=(CTimeSpan span)
{
    m_timeSpan -= span.m_timeSpan;
    return( *this );
}

bool CTimeSpan::operator==(CTimeSpan span) const
{
    return( m_timeSpan == span.m_timeSpan );
}

bool CTimeSpan::operator!=(CTimeSpan span) const
{
    return( m_timeSpan != span.m_timeSpan );
}

bool CTimeSpan::operator<(CTimeSpan span) const
{
    return( m_timeSpan < span.m_timeSpan );
}

bool CTimeSpan::operator>(CTimeSpan span) const
{
    return( m_timeSpan > span.m_timeSpan );
}

bool CTimeSpan::operator<=(CTimeSpan span) const
{
    return( m_timeSpan <= span.m_timeSpan );
}

bool CTimeSpan::operator>=(CTimeSpan span) const
{
    return( m_timeSpan >= span.m_timeSpan );
}

//=================================================================

CTime CTime::GetCurrentTimeT()
{
    return (CTime(time(NULL)));
}

CTime::CTime()
{
    struct tm atm;

    atm.tm_sec = 0;
    atm.tm_min = 0;
    atm.tm_hour = 0;
    atm.tm_mday = 0;
    atm.tm_mon = 0;            // tm_mon is 0 based
    atm.tm_year =  - 1900;     // tm_year is 1900 based
    atm.tm_isdst = -1;

    m_time = mktime(&atm);
}

CTime::CTime(const char *szTime)
{
    SYSTEMTIME_Q sTime;
    sscanf(szTime, "%04d-%02d-%02d %02d:%02d:%02d",
           &sTime.nYear, &sTime.nMonth, &sTime.nDay, &sTime.nHour, &sTime.nMinute, &sTime.nSecond);

    struct tm atm;

    atm.tm_sec = sTime.nSecond;
    atm.tm_min = sTime.nMinute;
    atm.tm_hour = sTime.nHour;
    atm.tm_mday = sTime.nDay;
    atm.tm_mon = sTime.nMonth - 1;        // tm_mon is 0 based
    atm.tm_year = sTime.nYear - 1900;     // tm_year is 1900 based
    atm.tm_isdst = -1;

    m_time = mktime(&atm);
    printf("m_time : %ld\n", m_time);
}

CTime::CTime(ctime_t time) :
    m_time( time )
{
}

CTime::CTime(
    int nYear,
    int nMonth,
    int nDay,
    int nHour,
    int nMin,
    int nSec,
    int nDST)
{
    struct tm atm;

    atm.tm_sec = nSec;
    atm.tm_min = nMin;
    atm.tm_hour = nHour;
    atm.tm_mday = nDay;
    atm.tm_mon = nMonth - 1;        // tm_mon is 0 based
    atm.tm_year = nYear - 1900;     // tm_year is 1900 based
    atm.tm_isdst = nDST;

    m_time = mktime(&atm);
}

CTime::CTime(
    unsigned short wDosDate,
    unsigned short wDosTime,
    int nDST)
{
    struct tm atm;
    atm.tm_sec = (wDosTime & ~0xFFE0) << 1;
    atm.tm_min = (wDosTime & ~0xF800) >> 5;
    atm.tm_hour = wDosTime >> 11;

    atm.tm_mday = wDosDate & ~0xFFE0;
    atm.tm_mon = ((wDosDate & ~0xFE00) >> 5) - 1;
    atm.tm_year = (wDosDate >> 9) + 80;
    atm.tm_isdst = nDST;
    m_time = mktime(&atm);
}

CTime::CTime(
    const SYSTEMTIME_Q& sysTime,
    int nDST)
{
    if (sysTime.nYear < 1900)
    {
        ctime_t time0 = 0L;
        CTime timeT(time0);
        *this = timeT;
    }
    else
    {
        CTime timeT(
            sysTime.nYear, sysTime.nMonth, sysTime.nDay,
            sysTime.nHour, sysTime.nMinute,sysTime.nSecond,
            nDST);
        *this = timeT;
    }
}

CTime& CTime::operator=(ctime_t time)
{
    m_time = time;

    return( *this );
}

CTime& CTime::operator+=(CTimeSpan span)
{
    m_time += span.GetTimeSpan();

    return( *this );
}

CTime& CTime::operator-=(CTimeSpan span)
{
    m_time -= span.GetTimeSpan();

    return( *this );
}

CTimeSpan CTime::operator-(CTime time) const
{
    return( CTimeSpan( m_time-time.m_time ) );
}

CTime CTime::operator-(CTimeSpan span) const
{
    return( CTime( m_time-span.GetTimeSpan() ) );
}

CTime CTime::operator+(CTimeSpan span) const
{
    return( CTime( m_time+span.GetTimeSpan() ) );
}

bool CTime::operator==(CTime time) const
{
    return( m_time == time.m_time );
}

bool CTime::operator!=(CTime time) const
{
    return( m_time != time.m_time );
}

bool CTime::operator<(CTime time) const
{
    return( m_time < time.m_time );
}

bool CTime::operator>(CTime time) const
{
    return( m_time > time.m_time );
}

bool CTime::operator<=(CTime time) const
{
    return( m_time <= time.m_time );
}

bool CTime::operator>=(CTime time) const
{
    return( m_time >= time.m_time );
}

struct tm* CTime::GetLocalTm(struct tm* ptm) const
{
    if (ptm != NULL)
    {
        struct tm ptmTemp;

        if (localtime_r(&m_time, &ptmTemp) == NULL)
        {
            return NULL;    // indicates that m_time was not initialized!
        }

        *ptm = ptmTemp;
        return ptm;
    }

    return NULL;
}

bool CTime::GetAsSystemTime(SYSTEMTIME_Q& timeDest) const
{
    struct tm ttm;
    struct tm* ptm;

    ptm = GetLocalTm(&ttm);
    if(!ptm)
    {
        return false;
    }

    timeDest.nYear = (1900 + ptm->tm_year);
    timeDest.nMonth = (1 + ptm->tm_mon);
    timeDest.nDayOfWeek = ptm->tm_wday;
    timeDest.nDay = ptm->tm_mday;
    timeDest.nHour = ptm->tm_hour;
    timeDest.nMinute = ptm->tm_min;
    timeDest.nSecond = ptm->tm_sec;
    timeDest.nMilliseconds = 0;

    return true;
}

ctime_t CTime::GetTime() const
{
    return (ctime_t)m_time;
}

int CTime::GetYear() const
{
    struct tm ttm;
    struct tm * ptm;

    ptm = GetLocalTm(&ttm);
    return ptm ? (ptm->tm_year) + 1900 : 0 ;
}

int CTime::GetMonth() const
{
    struct tm ttm;
    struct tm * ptm;

    ptm = GetLocalTm(&ttm);
    return ptm ? ptm->tm_mon + 1 : 0;
}

int CTime::GetDay() const
{
    struct tm ttm;
    struct tm * ptm;

    ptm = GetLocalTm(&ttm);
    return ptm ? ptm->tm_mday : 0 ;
}

int CTime::GetHour() const
{
    struct tm ttm;
    struct tm * ptm;

    ptm = GetLocalTm(&ttm);
    return ptm ? ptm->tm_hour : -1 ;
}

int CTime::GetMinute() const
{
    struct tm ttm;
    struct tm * ptm;

    ptm = GetLocalTm(&ttm);
    return ptm ? ptm->tm_min : -1 ;
}

int CTime::GetSecond() const
{
    struct tm ttm;
    struct tm * ptm;

    ptm = GetLocalTm(&ttm);
    return ptm ? ptm->tm_sec : -1 ;
}

int CTime::GetDayOfWeek() const
{
    struct tm ttm;
    struct tm * ptm;

    ptm = GetLocalTm(&ttm);
    return ptm ? ptm->tm_wday + 1 : 0 ;
}

CMyString CTime::Format(const char* pszFormat) const
{
    if (pszFormat == NULL)
    {
        return CMyString("");
    }
    
    struct tm ttm;
    struct tm* ptm = GetLocalTm(&ttm);
    
    if (ptm == NULL)
    {
        return CMyString("");
    }
    
    char szBuffer[256] = {0};
    
    // 处理常用的格式字符串
    const char* src = pszFormat;
    char* dst = szBuffer;
    int remaining = sizeof(szBuffer) - 1;
    
    while (*src && remaining > 0)
    {
        if (*src == '%' && *(src + 1))
        {
            src++; // 跳过%
            switch (*src)
            {
                case 'Y': // 4位年份
                {
                    int written = snprintf(dst, remaining, "%04d", ptm->tm_year + 1900);
                    dst += written;
                    remaining -= written;
                    break;
                }
                case 'y': // 2位年份
                {
                    int written = snprintf(dst, remaining, "%02d", (ptm->tm_year + 1900) % 100);
                    dst += written;
                    remaining -= written;
                    break;
                }
                case 'm': // 月份 (01-12)
                {
                    int written = snprintf(dst, remaining, "%02d", ptm->tm_mon + 1);
                    dst += written;
                    remaining -= written;
                    break;
                }
                case 'd': // 日期 (01-31)
                {
                    int written = snprintf(dst, remaining, "%02d", ptm->tm_mday);
                    dst += written;
                    remaining -= written;
                    break;
                }
                case 'H': // 小时 (00-23)
                {
                    int written = snprintf(dst, remaining, "%02d", ptm->tm_hour);
                    dst += written;
                    remaining -= written;
                    break;
                }
                case 'M': // 分钟 (00-59)
                {
                    int written = snprintf(dst, remaining, "%02d", ptm->tm_min);
                    dst += written;
                    remaining -= written;
                    break;
                }
                case 'S': // 秒 (00-59)
                {
                    int written = snprintf(dst, remaining, "%02d", ptm->tm_sec);
                    dst += written;
                    remaining -= written;
                    break;
                }
                default:
                    // 不识别的格式字符，直接复制
                    if (remaining >= 2)
                    {
                        *dst++ = '%';
                        *dst++ = *src;
                        remaining -= 2;
                    }
                    break;
            }
            src++;
        }
        else
        {
            *dst++ = *src++;
            remaining--;
        }
    }
    
    *dst = '\0';
    return CMyString(szBuffer);
}




