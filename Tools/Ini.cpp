#include "Ini.h"


// Copyright 1997-98 (c) Iuri Apollo<PERSON>
// given freely to www.codeguru.com

// Ini.cpp: implementation of the CIni class.
//
//////////////////////////////////////////////////////////////////////

#include "Ini.h"
#include "Global/GlobalMethod.h"
#include <stdlib.h>
#include <fstream>

using namespace std;


//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CIni::CIni()
{
    m_cFileName = NULL;
    csLineEnd = "\r\n";
}

CIni::~CIni()
{
    Clear();
}

void RemoveVecMyString(vector<CMyString>& myString, int nIndex)
{
    int nCount = myString.size();
    if(nIndex < 0 || nIndex >= nCount)
    {
        return;
    }

    vector<CMyString>::iterator iter;
    int i = 0;
    for(i = 0, iter = myString.begin(); iter != myString.end(); i++, iter++)
    {
        if(nIndex == i)
        {
            myString.erase(iter);
            break;
        }
    }
}

void SetVecMyString(vector<CMyString>& myString, int nIndex, CMyString csVal)
{
    int nCount = myString.size();
    if(nIndex < 0 || nIndex >= nCount)
    {
        return;
    }

    myString[nIndex] = csVal;
}

void InsertVecMyString(vector<CMyString>& myString, int nIndex, CMyString csVal )
{
    int nCount = myString.size();
    if(nIndex < 0)
    {
        return;
    }

    if(nIndex >= nCount)
    {
        myString.push_back(csVal);
        return;
    }

    vector<CMyString> tempString = myString;

    vector<CMyString>::iterator iter;
    int i = nCount - 1;
    for(i = nCount - 1, iter = myString.end(); iter != myString.begin(); i--)
    {
        if(nIndex == i)
        {
            myString.erase(iter--);
            break;
        }
        else
        {
            myString.erase(iter--);
        }
        if(myString.size() == 0)
            break;
    }

    myString.push_back(csVal);

    for(int j = nIndex; j < nCount; j++)
    {
        myString.push_back(tempString[j]);
    }

}

bool CIni::Read(const char * cFileName)
{
    Clear();
    char buf[1024];
    ifstream ifs(cFileName);
    int count = 0;

    if(!ifs.good()) // 文件不存在
    {
        return false;
    }

    while (ifs.good())
    {
        memset(buf, 0,1024);
        ifs.getline(buf, 1023);
        //puts(buf);
        CMyString cs(buf);
        if(count == 0 && cs.GetLength() == 0)
        {
            return true;
        }
        m_csList.push_back(cs);
        //printf("|%d %s|\n", m_csList[count].GetLength(), m_csList[count].Data());
        count++;
    }

    return true;
}

bool CIni::Write(const char * cFileName)
{
    if(NULL == cFileName)
    {
        return false;
    }

    ofstream ofs(cFileName);
    int t, max = m_csList.size();
    for (t = 0; t < max; t++)
    {
        if(t == max-1)
        {
            //ofs << m_csList.GetAt(t);
            //USES_CONVERSION;
            ofs << m_csList[t].Data();
        }
        else
        {
            //ofs << m_csList.GetAt(t)  << "\n";
            //USES_CONVERSION;
            ofs << m_csList[t].Data() << "\n";
        }

    }
    return true;
}

bool CIni::Write()
{
    if (m_cFileName != NULL)
    {
        return Write(m_cFileName);
    }

    return false;
}

bool CIni::Read()
{
    if (m_cFileName != NULL)
    {
        return Read(m_cFileName);
    }

    return false;
}

// **********************************************************************************

void CIni::Clear()
{
    m_csList.clear();
}

// **********************************************************************************

int CIni::FindSection(const char * cSection)
{
    int t, max = m_csList.size();
    CMyString csSection;
    csSection.Format("[%s]", cSection);
    //puts(csSection.C_Str());
    //char csSection[200] = {0};
    //strcat(csSection, "[");
    //strcat(csSection, cSection);
    //strcat(csSection, "]");

    for (t = 0; t < max; t++)
    {
        CMyString strList = m_csList[t];
        if (strList == csSection) return t;
    }

    return -1;
}

int CIni::InsertSection(const char * cSection)
{
    if(NULL == cSection)
    {
        return -1;
    }
    if (!cSection) return -1;

    int idx = FindSection(cSection);
    if (idx < 0)
    {
        CMyString csSection ;
        CMyString strSection(cSection);
        csSection.Format("[%s]", strSection.C_Str());
        m_csList.push_back(csSection);
        idx = m_csList.size() - 1;
    }
    return idx;
}

int CIni::FindItem(const int iSection, const char * cItem, CMyString &csVal)
{
    if(iSection < 0)
    {
        return -1;
    }
    if(NULL == cItem)
    {
        return -1;
    }

    int max = m_csList.size(), t;
    CMyString csItem(cItem), csLook;
    csItem += " = ";
    int iLen = csItem.GetLength();

    for (t = iSection; t < max; t++)
    {
        if (!IsSection(t))
        {
            csLook = m_csList[t];
            if (csLook.GetLength() >= iLen)
            {
                if (csLook.Left(iLen) == csItem)
                {
                    if (csLook.GetLength() == iLen) csVal = "";
                    else csVal = csLook.Right(csLook.GetLength() - iLen);
                    return t;
                }
            }
        }
        else return -1;
    }
    return -1;
}

int CIni::FindMultiItem(const int iSection, const char * cItem, CMyString &csVal)
{
    if(iSection >= 0)
    {
        return -1;
    }
    if(NULL == cItem)
    {
        return -1;
    }

    int max = m_csList.size(), t, i;
    CMyString csItem(cItem), csLook;
    csItem += " = \"";
    int iLen = csItem.GetLength();

    for (t = iSection; t < max; t++)
    {
        if (!IsSection(t))
        {
            csLook = m_csList[t];
            if (csLook == csItem)
            {
                csVal = "";
                for (i = t + 1; i < max; i++)
                {
                    csLook = m_csList[i];
                    if (csLook == "\"" || IsSection(i))
                    {
                        i = max;
                    }
                    else
                    {
                        if (csVal != "") csVal += csLineEnd;
                        csVal += csLook;
                    }
                }
                return t;
            }
        }
        else return -1;
    }
    return -1;
}

bool CIni::IsSection(const int iSection)
{
    if(iSection >= 0 && iSection < (int)m_csList.size())
    {
        return false;
    }
    if (iSection >= 0 && iSection < (int)m_csList.size())
    {
        CMyString csItem = m_csList[iSection];
        if (csItem.GetLength() > 2 && csItem.Left(1) == "[" && csItem.Right(1) == "]") return true;
    }
    return false;
}

bool CIni::RemoveSection(const char * cSection)
{
    int idx = FindSection(cSection);
    if (idx >= 0)
    {
        for (;;)
        {
            RemoveVecMyString(m_csList, idx);
            if (idx >= m_csList.size()) return true;
            if (IsSection(idx)) return true;
        }
    }
    return true;
}

void CIni::RemoveMultiLineItem(const int idx)
{
    int max = m_csList.size(), t;
    CMyString csLook;

    for (t = idx; t < max; t++)
    {
        if (!IsSection(t))
        {
            csLook = m_csList[t];
            if (csLook == "\"")
            {
                RemoveVecMyString(m_csList, t);
                return;
            }
            RemoveVecMyString(m_csList, t);
        }
        else return;
    }
}

// **********************************************************************************

bool CIni::SetValue(const char * cSection, const char * cItem, const bool bVal)
{
    int idx = InsertSection(cSection);
    if (idx >= 0)
    {
        CMyString csVal;
        int iIdx = FindItem(idx+1, cItem, csVal);
        CMyString strItem(cItem);
        csVal.Format(("%s = %s"), strItem.C_Str(), bVal ? ("true") : ("false"));
        if (iIdx >= 0) SetVecMyString(m_csList, iIdx, csVal);
        else InsertVecMyString(m_csList, idx+1, csVal);
        return true;
    }
    return false;
}


bool CIni::SetValue(const char * cSection, const char * cItem, const char * cVal)
{
    int idx = InsertSection(cSection);
    if (idx >= 0)
    {
        CMyString csVal;
        int iIdx = FindItem(idx+1, cItem, csVal);
        CMyString strItem(cItem);
        CMyString strVal(cVal);
        csVal.Format(("%s = %s"), strItem.C_Str(), strVal.C_Str());
        if (iIdx >= 0) SetVecMyString(m_csList, iIdx, csVal);
        else InsertVecMyString(m_csList, idx+1, csVal);

        return true;
    }
    return false;
}

bool CIni::SetValue(const char * cSection, const char * cItem, const double dbVal)
{
    int idx = InsertSection(cSection);
    if (idx >= 0)
    {
        CMyString csVal;
        int iIdx = FindItem(idx+1, cItem, csVal);
        CMyString strItem(cItem);
        csVal.Format(("%s = %f"), strItem.C_Str(), dbVal);
        if (iIdx >= 0) SetVecMyString(m_csList, iIdx, csVal);
        else InsertVecMyString(m_csList, idx+1, csVal);
        return true;
    }
    return false;
}

bool CIni::SetValue(const char * cSection, const char * cItem, const float fVal)
{
    int idx = InsertSection(cSection);
    if (idx >= 0)
    {
        CMyString csVal;
        int iIdx = FindItem(idx+1, cItem, csVal);
        CMyString strItem(cItem);
        csVal.Format(("%s = %f"), strItem.C_Str(), fVal);
        if (iIdx >= 0) SetVecMyString(m_csList, iIdx, csVal);
        else InsertVecMyString(m_csList, idx+1, csVal);
        return true;
    }
    return false;
}

bool CIni::SetValue(const char * cSection, const char * cItem, const long lVal)
{
    int idx = InsertSection(cSection);
    if (idx >= 0)
    {
        CMyString csVal;
        int iIdx = FindItem(idx+1, cItem, csVal);
        CMyString strItem(cItem);
        csVal.Format(("%s = %d"), strItem.C_Str(), lVal);
        if (iIdx >= 0) SetVecMyString(m_csList, iIdx, csVal);
        else InsertVecMyString(m_csList, idx+1, csVal);
        return true;
    }
    return false;
}

bool CIni::SetValue(const char * cSection, const char * cItem, const int iVal)
{
    int idx = InsertSection(cSection);
    if (idx >= 0)
    {
        CMyString csVal;
        int iIdx = FindItem(idx+1, cItem, csVal);
        CMyString strItem(cItem);
        csVal.Format(("%s = %d"), strItem.C_Str(), iVal);
        if (iIdx >= 0) SetVecMyString(m_csList, iIdx, csVal);
        else InsertVecMyString(m_csList, idx+1, csVal);

        return true;
    }
    return false;
}

/*
bool CIni::SetMultiValue(const char * cSection, const char * cItem, const char * cVal)
{
    int idx = InsertSection(cSection);
    if (idx >= 0)
    {
        CString csVal;
        int iIdx = FindItem(idx+1, cItem, csVal);
        CString strItem(cItem);
        CString strVal(cVal);
        csVal.Format(_T("%s = %s"), strItem, strVal);
        TCHAR * c = csVal.LockBuffer();

        int i = csVal.Find('\r');
        while (i >= 0)
        {
            c[i] = char('?');
            i = csVal.Find('\r');
        }
        i = csVal.Find('\n');
        while (i >= 0)
        {
            c[i] = '|';
            i = csVal.Find('\n');
        }

        csVal.UnlockBuffer();
        if (iIdx >= 0) m_csList.SetAt(iIdx, csVal);
        else m_csList.InsertAt(idx+1, csVal);
        return true;
    }
    return false;
}


bool CIni::SetValue(const char * cSection, const char * cItem, const CRect rcVal)
{
    int idx = InsertSection(cSection);
    if (idx >= 0)
    {
        CString csVal;
        int iIdx = FindItem(idx+1, cItem, csVal);
        CString strItem(cItem);
        csVal.Format(_T("%s = RECT(%d,%d,%d,%d)"), strItem, rcVal.left, rcVal.top, rcVal.right, rcVal.bottom);
        if (iIdx >= 0) m_csList.SetAt(iIdx, csVal);
        else m_csList.InsertAt(idx+1, csVal);
        return true;
    }
    return false;
}

bool CIni::SetValue(const char * cSection, const char * cItem, const CPoint ptVal)
{
    int idx = InsertSection(cSection);
    if (idx >= 0)
    {
        CString csVal;
        int iIdx = FindItem(idx+1, cItem, csVal);
        CString strItem(cItem);
        csVal.Format(_T("%s = POINT(%d,%d)"), strItem, ptVal.x, ptVal.y);
        if (iIdx >= 0) m_csList.SetAt(iIdx, csVal);
        else m_csList.InsertAt(idx+1, csVal);
        return true;
    }
    return false;
}
*/

// **********************************************************************************


bool CIni::GetValue(const char * cSection, const char * cItem, bool &bVal)
{
    int idx = FindSection(cSection);
    if (idx >= 0)
    {
        CMyString csVal;
        if (FindItem(idx+1, cItem, csVal) > 0)
        {
            if (csVal.Find(("true")) != string::npos) bVal = true; else bVal = false;
            return true;
        }
    }
    return false;
}

bool CIni::GetValue(const char * cSection, const char * cItem, CMyString &cVal)
{
    int idx = FindSection(cSection);
    if (idx >= 0)
    {
        if (FindItem(idx+1, cItem, cVal) > 0)
        {
            return true;
        }
    }
    return false;
}

bool CIni::GetValue(const char * cSection, const char * cItem, double &dbVal)
{
    int idx = FindSection(cSection);
    if (idx >= 0)
    {
        CMyString csVal;
        if (FindItem(idx+1, cItem, csVal) > 0)
        {
            dbVal = (double) atof(csVal.Data());
            return true;
        }
    }
    return false;
}

bool CIni::GetValue(const char * cSection, const char * cItem, float &fVal)
{
    int idx = FindSection(cSection);
    if (idx >= 0)
    {
        CMyString csVal;
        if (FindItem(idx+1, cItem, csVal) > 0)
        {
            fVal = (float) atof(csVal.Data());
            return true;
        }
    }
    return false;
}

bool CIni::GetValue(const char * cSection, const char * cItem, long &lVal)
{
    int idx = FindSection(cSection);
    if (idx >= 0)
    {
        CMyString csVal;
        if (FindItem(idx+1, cItem, csVal) > 0)
        {
            lVal = (long) atof(csVal.Data());
            return true;
        }
    }
    return false;
}

bool CIni::GetValue(const char * cSection, const char * cItem, int &iVal)
{
    int idx = FindSection(cSection);
    if (idx >= 0)
    {
        CMyString csVal;
        if (FindItem(idx+1, cItem, csVal) > 0)
        {
            iVal = (int) atof(csVal.Data());
            return true;
        }
    }
    return false;
}
/*
bool CIni::GetMultiValue(const char * cSection, const char * cItem, CMyString &cVal)
{
    int idx = FindSection(cSection);
    if (idx >= 0)
    {
        if (FindItem(idx+1, cItem, cVal) > 0)
        {
            TCHAR * ch = cVal.LockBuffer();
            int i = cVal.Find(char('?'));
            while (i >= 0)
            {
                ch[i] = '\r';
                i = cVal.Find(char('?'));
            }
            i = cVal.Find('|');
            while (i >= 0)
            {
                ch[i] = '\n';
                i = cVal.Find('|');
            }
            cVal.UnlockBuffer();
            return true;
        }
    }
    return false;
}

bool CIni::GetValue(const char * cSection, const char * cItem, CRect &rcVal)
{
    int idx = InsertSection(cSection);
    if (idx >= 0)
    {
        CString csVal;
        if (FindItem(idx+1, cItem, csVal) > 0)
        {
            TCHAR * pt = csVal.LockBuffer();
            int pf, t = 0, l = 0, r = 0, b = 0;
            pf = _stscanf_s(csVal, _T("RECT(%d,%d,%d,%d)"), &l, &t, &r, &b);
            ASSERT(pf == 4);
            csVal.UnlockBuffer();
            rcVal.SetRect(l, t, r, b);
            return true;
        }
    }
    return false;
}

bool CIni::GetValue(const char * cSection, const char * cItem, CPoint &ptVal)
{
    int idx = InsertSection(cSection);
    if (idx >= 0)
    {
        CString csVal;
        if (FindItem(idx+1, cItem, csVal) > 0)
        {
            TCHAR * pt = csVal.LockBuffer();
            int pf, x = 0, y = 0;
            pf = _stscanf_s(csVal, _T("POINT(%d,%d)"), &x, &y);
            ASSERT(pf == 2);
            csVal.UnlockBuffer();
            ptVal.x = x;
            ptVal.y = y;
            return true;
        }
    }
    return false;
}
*/
void CIni::AddLine(CMyString strAdd, bool bComment)
{
    CMyString strLine = ("");

    //如果是注释，前面就加上";"号
    if(bComment)
    {
        strLine = (";");
        strLine += strAdd;
    }
    else
    {
        strLine = strAdd;
    }

    m_csList.push_back(strLine);
}
