#include "CMyString.h"


CMyString::CMyString(void)
{
    //m_szBuffer = std::make_shared<char[]>(SZ_BUFFER_LENGTH);
}

CMyString::CMyString(const char * str)
{
    m_string = str;
}

CMyString::CMyString(string& str)
{
    m_string = str;
}

CMyString::CMyString(const CMyString& str)
{
    m_string = str.m_string;
}

CMyString::~CMyString(void)
{

}

const char* CMyString::C_Str(void) const
{
    return m_string.c_str();
}

const char *CMyString::Data() const
{
    return m_string.data();
}

int CMyString::GetLength(void)
{
    return m_string.length();
}

void CMyString::Format(const char *fmt, ...)
{
    char m_szBuffer[SZ_BUFFER_LENGTH]={0};
    try
    {
        va_list args;
        va_start(args, fmt);
        //vsprintf(m_szBuffer, fmt, args);
        if ( vsnprintf ( m_szBuffer, sizeof(m_szBuffer)-1, fmt, args ) < 0 )
        {
            printf("vsnprinf overflow...\n");
            return;
        }
        va_end(args);
    }
    catch(...)
    {
        //m_szBuffer[0] = 0;
        memset(m_szBuffer, 0,sizeof(m_szBuffer));
    }

    m_string = m_szBuffer;
}

void CMyString::AppendFormat(const char *fmt, ...)
{
    char m_szBuffer[SZ_BUFFER_LENGTH]={0};
    try
    {
        va_list args;
        va_start(args, fmt);
        //vsprintf(m_szBuffer, fmt, args);
        if ( vsnprintf ( m_szBuffer, sizeof(m_szBuffer)-1, fmt, args ) < 0 )
        {
            printf("vsnprinf overflow...\n");
            return;
        }
        va_end(args);
    }
    catch(...)
    {
        //m_szBuffer[0] = 0;
        memset(m_szBuffer, 0,sizeof(m_szBuffer));
    }

    m_string += m_szBuffer;
}

#if 0
void CMyString::ConvectUtf8Format(const char *fmt, ...)
{
    try
    {
        va_list args;
        //char* buf = va_arg(args, fmt);

    }
    catch(...)
    {

    }

}
#endif

CMyString CMyString::Left(int count)
{
    string s = m_string.substr(0, count);
    return CMyString(s);
}


CMyString CMyString::Right(int count)
{
    int len = m_string.length();
    string s = m_string.substr(len-count, count);
    return CMyString(s);
}


CMyString CMyString::Mid(int first, int count)
{
    string s = m_string.substr(first, count);
    return CMyString(s);
}


CMyString CMyString::Mid(int first)
{
    string s = m_string.substr(first);
    return CMyString(s);
}


string::size_type CMyString::Find(const char *str)
{
    //return 	m_string.find_first_of(str);
    return m_string.find(str);
}

string::size_type CMyString::Find(char str)
{
    return 	m_string.find_first_of(str);
}


string::size_type CMyString::Find(CMyString& str)
{
    return 	m_string.find(str.m_string);
}


string::size_type CMyString::ReverseFind(char ch)
{
    return m_string.find_last_of(ch);
}


CMyString& CMyString::MakeUpper(void)
{
    transform(m_string.begin(), m_string.end(), m_string.begin(),::toupper);
    return *this;
}


CMyString& CMyString::MakeLower(void)
{
    transform(m_string.begin(), m_string.end(), m_string.begin(),::tolower);

    return *this;
}


void CMyString::Delete(int index, int count)
{
    m_string.erase(index, count);
}


void CMyString::Replace(const char* strOld, const char* strNew)
{
    size_t pos = 0;
    int oldLen = strlen(strOld);
    int newLen = strlen(strNew);

    while( (pos=m_string.find(strOld, pos)) != std::string::npos )
    {
        m_string.replace( pos, oldLen, strNew );
        pos += newLen;
    }
}

void CMyString::Replace(CMyString& strOld, CMyString& strNew)
{
    Replace(strOld.C_Str(), strNew.C_Str());
}

CMyString & CMyString::operator = (const CMyString & str)
{

    if (this == &str)
    {
        return *this;
    }

    m_string = str.m_string;

    return *this;
}

CMyString &CMyString::operator =(const char *str)
{
    if(this->m_string == str)
    {
        return *this;
    }

    m_string = str;

    return *this;
}

CMyString & CMyString::operator += (const CMyString & str)
{
    m_string += str.m_string;

    return *this;
}

CMyString & CMyString::operator += (const char * str)
{
    m_string += str;

    return *this;
}

CMyString CMyString::operator + (const CMyString & str)
{
    CMyString myStr;
    myStr.m_string = m_string + str.m_string;

    return myStr;
}

bool CMyString::operator > (const CMyString & str) const
{
    return m_string > str.m_string;
}

bool CMyString::operator >= (const CMyString & str) const
{
    return m_string >= str.m_string;
}

bool CMyString::operator < (const CMyString & str) const
{
    return (m_string < str.m_string);
}

bool CMyString::operator <= (const CMyString & str) const
{
    return m_string <= str.m_string;
}

bool CMyString::operator == (const char * str) const
{
    return (m_string == str);
}

bool CMyString::operator != (const char * str) const
{
    return m_string != str;
}

char& CMyString::operator[](int idx)
{
    return m_string[idx];
}


