#include <QtCore/qglobal.h>

#include "LxTimer.h"
#include <iostream>

#if defined(Q_OS_LINUX)
#include "Tools/timespec.h"
#endif

#if 0
CLxTimer::CLxTimer(int sec, int usec)
{
    m_timeval.tv_sec  = sec;
    m_timeval.tv_usec = usec;
}

void CLxTimer::GetCurrentTimeT()
{
    gettimeofday(&m_timeval,NULL);
}

double CLxTimer::GetTimer()
{
    return (double)(m_timeval.tv_sec+(double)m_timeval.tv_usec/1000000.0);
}

void CLxTimer::SetTimeval(timeval &time)
{
    m_timeval.tv_sec  = time.tv_sec;
    m_timeval.tv_usec = time.tv_usec;
}

void CLxTimer::AddTimer(int sec, int usec)
{
    this->m_timeval.tv_sec  += sec;
    this->m_timeval.tv_usec += usec;

    if(this->m_timeval.tv_usec >= 1000000)
    {
        this->m_timeval.tv_usec -= 1000000;
        this->m_timeval.tv_sec++;
    }

}

CLxTimer CLxTimer::operator+( CLxTimer &time)
{
    int ns = this->m_timeval.tv_sec + time.GetSec();
    int ms = this->m_timeval.tv_usec + time.GetUsec();
    if(ms >= 1000000)   //tv_usec范围为0~1000000 达到1000000时置0
    {
        ns++;
        ms -= 1000000;
    }

    return CLxTimer(ns, ms);
}

CLxTimer CLxTimer::operator +=(CLxTimer time)
{
    this->m_timeval.tv_sec  += time.GetSec();
    this->m_timeval.tv_usec += time.GetUsec();
    if(this->m_timeval.tv_usec >= 1000000)   //tv_usec范围为0~1000000 达到1000000时置0
    {
        this->m_timeval.tv_sec++;
        this->m_timeval.tv_usec -= 1000000;
    }

    return (*this);
}


CLxTimer CLxTimer::operator-(CLxTimer &time)
{
    CLxTimer temp;
    temp.m_timeval.tv_sec = this->m_timeval.tv_sec - time.GetSec();
    temp.m_timeval.tv_usec = this->m_timeval.tv_usec - time.GetUsec();
    if(temp.m_timeval.tv_usec < 0)
    {
        temp.m_timeval.tv_sec--;
        temp.m_timeval.tv_usec += 1000000;
    }

    return temp;
}


CLxTimer CLxTimer::operator=(CLxTimer time)
{
    this->m_timeval.tv_sec  = time.m_timeval.tv_sec;
    this->m_timeval.tv_usec = time.m_timeval.tv_usec;
}

#endif

#if 1

/*************************************************************/

CLxTimer::CLxTimer(clock_t t)
{
    //m_timeval = t;
#if defined(Q_OS_LINUX)
    sc_clk_tck = CLOCKS_PER_SEC;
#endif
}

void CLxTimer::GetCurrentTimeT()
{
#if defined(Q_OS_LINUX)
    //m_timeval = clock();
    //struct timespec now;
    clock_gettime(CLOCK_MONOTONIC,&m_timeval);
#endif
}
//单位:s
double CLxTimer::GetTimer()
{
    //printf("PlayTime=%f\n",((double)m_timeval.tv_nsec)/1000);
    return timespec_to_double(m_timeval);
}


#if 1
//CLxTimer CLxTimer::operator+( CLxTimer &time)
//{
//    int ns = this->m_timeval.tv_sec + time.GetSec();
//    int ms = this->m_timeval.tv_usec + time.GetUsec();
//    if(ms >= 1000000)   //tv_usec范围为0~1000000 达到1000000时置0
//    {
//        ns++;
//        ms -= 1000000;
//    }

//    return CLxTimer(ns, ms);
//}

CLxTimer CLxTimer::operator+=(CLxTimer time)
{
    m_timeval = timespec_add(m_timeval,time.m_timeval);
    return (*this);
}


CLxTimer CLxTimer::operator-(CLxTimer &time)
{
    #if 0
    m_timeval = timespec_sub(m_timeval,time.m_timeval);

    return (*this);
    #endif
    CLxTimer temp;
    temp.m_timeval = timespec_sub(this->m_timeval,time.m_timeval);

    return temp;
}


CLxTimer CLxTimer::operator=(CLxTimer time)
{
    m_timeval = time.m_timeval;

    return (*this);
}
#endif

#endif
