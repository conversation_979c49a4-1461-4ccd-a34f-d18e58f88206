#ifndef BASE64_H
#define BASE64_H


#include <string>
using namespace std;

string base64_encode(unsigned char const* , unsigned int len);
int base64_encode(const unsigned char *src, int srclength, char *target,
              int targsize);
string base64_encode(std::string const& encoded_string);
string base64_decode(string const& s);
int base64_decode(const char *src, unsigned char *target, int targsize);

int base64_declen(int orig_len);

#endif // BASE64_H
