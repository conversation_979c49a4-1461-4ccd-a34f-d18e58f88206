#include "stdafx.h"
#include <unistd.h>
#include <termios.h>
#include "usbGPS.h"
#include <ctime>

#define UART_USB_GPS_DEVICE "/dev/ttyACM0"

CusbGPS::CusbGPS()
{
	m_isWorking=false;
	m_bGpsFound=false;
	m_bGpsValid=false;

	m_nSerialPort=-1;
}

CusbGPS::~CusbGPS()
{

}

bool  CusbGPS::CheckUsbGpsExist()
{
    libusb_device** deviceList = NULL;
    libusb_device* device = NULL;
	QMutexLocker locker(&libUsbGetDeviceListQMutex);
    // 获取连接的USB设备列表
    ssize_t deviceCount = libusb_get_device_list(NULL, &deviceList);
    bool deviceFound = false;

    // 遍历设备列表
    for (ssize_t i = 0; i < deviceCount; i++) {
        device = deviceList[i];
        libusb_device_descriptor descriptor;
        
        // 获取设备描述符
        if (libusb_get_device_descriptor(device, &descriptor) != 0) {
            //printf("无法获取设备描述符\n");
            continue;
        }

        // 判断设备的VID和PID是否与指定的值相匹配
        if (descriptor.idVendor == USB_GPS_VID_1 && descriptor.idProduct == USB_GPS_PID_1) {
            deviceFound = true;
            break;
        }
    }

    // 释放设备列表
    libusb_free_device_list(deviceList, 1);

    return deviceFound;
}


bool	CusbGPS::Init()
{
	//加密狗没有启动，才初始化LIBUSB，因为如果加密狗已经启动，那么代表LIBUSB必然已经初始化成功
	bool result=true;
	if(!g_Global.m_softDog.m_isWorking)
	{
        printf("Gps Init libusb!\n");
		int rc = libusb_init (NULL);
		if (rc < 0)
		{
			printf("failed to initialise libusb: %s\n", libusb_error_name(rc));
			result=false;
		}
	}
	return result;
}

bool	CusbGPS::StartWorking()
{
	if (m_isWorking)
	{
		return FALSE;
	}
	
	m_isWorking = TRUE;

	pthread_t pid;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&pid, &attr, WorkThread, (void*)this);
    pthread_attr_destroy(&attr);

	return TRUE;
}

void *CusbGPS::WorkThread(void *lpParam)
{
	CusbGPS*  pUsbGps	= (CusbGPS *)lpParam;
	printf("usbGPS:WorkThread: Enter...\n");

    //第一次也要加入日志
    if(!pUsbGps->CheckUsbGpsExist())
    {
        CMyString strLogContents;
        strLogContents.Format("GPS module not found!");
        g_Global.m_logTable.InsertLog(	SUPER_USER_NAME,
                                        SUPER_USER_NAME,
                                        LT_ADVANCED_LOG,
                                        strLogContents);
    }

	while(1)
	{
		//检查是否存在
		if(pUsbGps->CheckUsbGpsExist())
		{
			if(!pUsbGps->GetGPSFound())
			{
				printf("USB GPS Found!\n");
                CMyString strLogContents;
                strLogContents.Format("GPS module inserted!");
                g_Global.m_logTable.InsertLog(	SUPER_USER_NAME,
                                                SUPER_USER_NAME,
                                                LT_ADVANCED_LOG,
                                                strLogContents);

                //先将无效加入日志
                strLogContents.Format("Gps is inValid!");
                g_Global.m_logTable.InsertLog(	SUPER_USER_NAME,
                                                SUPER_USER_NAME,
                                                LT_ADVANCED_LOG,
                                                strLogContents);

				pUsbGps->SetGPSFound(true);
                pUsbGps->SetGPSValid(false);
				usleep(200000);
				pUsbGps->initUart();
			}
		}
		else
		{
			if(pUsbGps->GetGPSFound())
			{
				printf("USB GPS Not Found!\n");
				pUsbGps->SetGPSFound(false);
				pUsbGps->deInitUart();
                pUsbGps->SetGPSValid(false);

                CMyString strLogContents;
                strLogContents.Format("GPS module removed!");
                g_Global.m_logTable.InsertLog(	SUPER_USER_NAME,
                                                SUPER_USER_NAME,
                                                LT_ADVANCED_LOG,
                                                strLogContents);
			}
		}
		sleep(1);
	}

	printf("usbGPS:WorkThread Exit...\n");
	return 0;
}



// 将GPS时间转换为std::tm结构体对象
std::tm CusbGPS::convertGpsTimeToTM(const std::string& date, const std::string& time) {
    std::tm dateTime = {};
    
    int year = stoi(date.substr(4, 2)) + 2000; // 假设年份字符串的格式是 "23" 表示 2023 年
    int month = stoi(date.substr(2, 2));
    int day = stoi(date.substr(0, 2));
    int hour = stoi(time.substr(0, 2));
    int minute = stoi(time.substr(2, 2));
    int second = stoi(time.substr(4, 2));
    
    dateTime.tm_year = year - 1900;
    dateTime.tm_mon = month - 1;
    dateTime.tm_mday = day;
    dateTime.tm_hour = hour;
    dateTime.tm_min = minute;
    dateTime.tm_sec = second;
    
    return dateTime;
}


string CusbGPS::receiveSerialData() {
    std::string data;

    // 读取串口数据
    char buffer[1024];
    ssize_t bytesRead = read(m_nSerialPort, buffer, sizeof(buffer));
    if (bytesRead > 0) {
        // 将字符数组转换为字符串
		//printf("bytesRead=%d\n",bytesRead);
        data = std::string(buffer, bytesRead);
    }

    return data;
}

void CusbGPS::parseNmeaProtocol(string& recvData) {
	if(recvData.size() == 0)
	{
		return;
	}
	// 解析NMEA协议，获取GPS的有效状态和当前时间
    //printf("Received data:%s\n",recvData.data());
	// 按逗号分割数据
    //下面是一个示例，记录日期为2023年7月28日，时间为18点28分00秒：
    //recvData = "$GPRMC,183000.000,A,0000.0000,N,00000.0000,E,0.00,0.00,280723,,,A*7B";
    std::istringstream iss(recvData);
    std::vector<std::string> tokens;
    std::string token;
    while (std::getline(iss, token, ',')) {
        tokens.push_back(token);
    }
	//printf("tokens.size()=%d,tokens[0]=%s\n",tokens.size(),tokens[0].data());
    // 根据NMEA协议格式，获取GPRMC的有效状态和当前时间
    if (tokens.size() >= 12 && tokens[0] == "$GPRMC") {
        std::string validStatus = tokens[2];
		if(validStatus == "A")
		{
            //printf("valid...\n");
            if(!m_bGpsValid)
            {
                m_bGpsValid=true;
                CMyString strLogContents;
                strLogContents.Format("Gps is Valid!");
                g_Global.m_logTable.InsertLog(	SUPER_USER_NAME,
                                                SUPER_USER_NAME,
                                                LT_ADVANCED_LOG,
                                                strLogContents);
            }
			std::string date = tokens[9];
    		std::string time = tokens[1];
			//std::string time = tokens[1].substr(0, 2) + ":" + tokens[1].substr(2, 2) + ":" + tokens[1].substr(4, 2);
			//std::cout << "Current time: " << time << std::endl;
			std::tm gpsTmTime=CusbGPS::convertGpsTimeToTM(date,time);   //此处是UTC时间的tm结构体
			//std::time_t gpsTimestamp = std::mktime(&gpsTmTime); //此处已经变成了本地时间
            std::time_t gpsTimestamp = timegm(&gpsTmTime);  //转换为格林威治时间

    		std::time_t tNow = CTime::GetCurrentTimeT().GetTime();  //这里得到的是UTC时间
            std::time_t timeDiff=tNow-gpsTimestamp;
            //printf("tNow=%lld,tGpsTime=%lld\n",tNow,gpsTimestamp);
            //printf("gps time diff=%d\n",abs(timeDiff));
            if(abs(tNow-gpsTimestamp) >=2)
            {
                //超过两秒才设置

                struct timeval tv;
                tv.tv_sec = gpsTimestamp;
                tv.tv_usec = 0;
                
                if (settimeofday(&tv, NULL) == 0) {
                    printf("set gpsTime ok!\n");
                    // 设置硬件时间
                    char* hw_param[] = { (char *)"hwclock", (char *)"-w", NULL};
                    ExecuteShell("hwclock", hw_param);
                } else {
                   printf("set gpsTime error!\n");
                }

#if 1
                struct tm *tblock = localtime(&gpsTimestamp);
                char timeStr[20] = {0};
                strftime(timeStr,20,"%F %T",tblock);

                printf("Set GPS time:%s\n",timeStr);

                CMyString strLogContents;
                strLogContents.Format("Set GPS time:%s",timeStr);
                g_Global.m_logTable.InsertLog(	SUPER_USER_NAME,
                                                SUPER_USER_NAME,
                                                LT_ADVANCED_LOG,
                                                strLogContents);

                //通知WEB更新时间
                string strDateTime = GetCurrentDateTime().C_Str();
                string strDataBootTime = Get_system_boot_time().C_Str();
                string strBuf = CWebProtocol::CmdResponseSetSystemDateTime(0, strDateTime,strDataBootTime);
                g_Global.m_WebNetwork.m_WebSend.ForwardDataToWeb(NULL, strBuf, false, false);
#endif
            }
		}
		else if(validStatus == "V")
		{
            //printf("invalid...\n");
            if(m_bGpsValid)
            {
                m_bGpsValid=false;
                CMyString strLogContents;
                strLogContents.Format("Gps is inValid!");
                g_Global.m_logTable.InsertLog(	SUPER_USER_NAME,
                                                SUPER_USER_NAME,
                                                LT_ADVANCED_LOG,
                                                strLogContents);
            }
		}

        //printf("gps_valid_status=%d\n",m_bGpsValid);
    }
}

void *CusbGPS::uartCommunicationThread(void *lpParam)
{
	CusbGPS*  pUsbGps	= (CusbGPS *)lpParam;
    while (pUsbGps->GetGPSFound()) {
        std::string serialData = pUsbGps->receiveSerialData();
        pUsbGps->parseNmeaProtocol(serialData);
    }
	return 0;
}

int CusbGPS::openSerialPort(const char *portName) {
    int serialPort = open(portName, O_RDWR | O_NOCTTY | O_NDELAY);
    if (serialPort == -1) {
        std::cerr << "Failed to open serial port" << std::endl;
        return -1;
    }

    // 配置串口
    struct termios tty;
    if (tcgetattr(serialPort, &tty) != 0) {
        std::cerr << "Failed to get serial port attributes" << std::endl;
        close(serialPort);
        return -1;
    }

    // 设置波特率为115200
    cfsetospeed(&tty, B115200);
    cfsetispeed(&tty, B115200);

    // 8个数据位，无校验位，1个停止位
    tty.c_cflag &= ~PARENB;
    tty.c_cflag &= ~CSTOPB;
    tty.c_cflag &= ~CSIZE;
    tty.c_cflag |= CS8;

    // 使能读取和忽略掉CR回车字符
    tty.c_iflag &= ~IGNBRK;
    tty.c_iflag &= ~(ICRNL | INLCR);
    tty.c_iflag &= ~(IXON | IXOFF | IXANY);

    // 使能原始模式输入
    tty.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);

    // 禁用软件流控和硬件流控
    tty.c_cflag &= ~CRTSCTS;
    tty.c_iflag &= ~(IXON | IXOFF | IXANY);

#if 0
2.VTIME和VMIN 决定了read()函数什么时候返回
1．当VTIME>0，VMIN>0时。read调用将保持阻塞直到读取到第一个字符，读到了第一个字符之后开始计时，此后若时间到了VTIME或者时间未到但已读够了VMIN个字符则会返回；若在时间未到之前又读到了一个字符(但此时读到的总数仍不够VMIN)则计时重新开始。
2. 当VTIME>0，VMIN=0时。read调用读到数据则立即返回，否则将为每个字符最多等待VTIME时间。
3. 当VTIME=0，VMIN>0时。read调用一直阻塞，直到读到VMIN个字符后立即返回。
4. 若在open或fcntl设置了O_NDELALY或O_NONBLOCK标志，read调用不会阻塞而是立即返回，那么VTIME和VMIN就没有意义，效果等同于与把VTIME和VMIN都设为了0。
VTIME 和  VMIN
VTIME  定义要求等待的零到几百毫秒的值(通常是一个8位的unsigned char变量)。
VMIN 定义了要求等待的最小字节数, 这个字节数可能是0。
只有设置为阻塞时这两个参数才有效，仅针对于读操作。
#endif
    // 设置超时时间为0.1秒
    tty.c_cc[VMIN] = 50;
    tty.c_cc[VTIME] = 1;	//单位100ms

    // 保存配置
    if (tcsetattr(serialPort, TCSANOW, &tty) != 0) {
        std::cerr << "Failed to set serial port attributes" << std::endl;
        close(serialPort);
        return -1;
    }

    return serialPort;
}

bool CusbGPS::initUart() {
    m_nSerialPort = openSerialPort(UART_USB_GPS_DEVICE);
    if (m_nSerialPort == -1) {
        return false;
    }
	printf("init gps uart succeed!\n");
	pthread_create(&m_pidSerialThread, NULL, uartCommunicationThread,(void*)this);
    return true;
}

bool CusbGPS::deInitUart() {	
	if(m_nSerialPort>=0)
	{
		pthread_join(m_pidSerialThread,NULL);
		close(m_nSerialPort);
		m_nSerialPort=-1;
		printf("deinit gps uart succeed!\n");
		return true;
	}
	else
	{
		return false;
	}
}