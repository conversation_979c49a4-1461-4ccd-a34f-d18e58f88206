#ifndef USBGPS_H
#define USBGPS_H

#include <QMutex>

#define USB_GPS_VID_1 0X1546
#define USB_GPS_PID_1 0X01a7

class CusbGPS
{
public:
    CusbGPS();
    ~CusbGPS();

    bool	Init();
    bool	StartWorking();
    static void *WorkThread(void *lpParam);

    bool  CheckUsbGpsExist();

    //bool SetValue(const char * cSection, const char * cItem, const COLORREF crVal);
    void SetGPSFound(bool isFound) {m_bGpsFound=isFound;}
    bool GetGPSFound(void) {return m_bGpsFound;}
    void SetGPSValid(bool isValid) {m_bGpsValid=isValid;}
    bool GetGPSValid(void) {return m_bGpsValid;}

    // 将GPS时间转换为std::tm结构体对象
    static std::tm convertGpsTimeToTM(const std::string& date, const std::string& time);
    string receiveSerialData();
    void parseNmeaProtocol(string& recvData);
    static void *uartCommunicationThread(void *lpParam);
    int openSerialPort(const char* portName);
    bool initUart();
    bool deInitUart();

public:
    QMutex  libUsbGetDeviceListQMutex;

    int m_nSerialPort;         //串口节点
    pthread_t m_pidSerialThread;    //串口通讯线程


private:
	bool	m_isWorking;
	HANDLE	m_hWorkThread;
    bool m_bGpsFound;       //GPS是否找到
    bool m_bGpsValid;       //GPS是否有效
};


#endif // USBGPS_H
