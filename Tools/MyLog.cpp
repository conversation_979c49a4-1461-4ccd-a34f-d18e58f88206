#include "stdafx.h"
#include <stdio.h>
#include "MyLog.h"
#include "Global/CType.h"
#include "tools.h"
#include "Global/GlobalMethod.h"
#include <algorithm>
#include <stdio.h>
#include <stdarg.h>

CMyLog::CMyLog()
{
    m_csWriteLog = PTHREAD_MUTEX_INITIALIZER;
    m_bWriteTime = TRUE;

    m_bPrint = TRUE;
    //m_CLIFd = -1;
}

CMyLog::~CMyLog()
{
    //20220427 静态初始化的互斥锁不需要,也不能用pthread_mutex_destroy销毁锁，否则将出错
    //::pthread_mutex_destroy(&m_csWriteLog);
}

void CMyLog::InitLogFile(string strLogFileName)
{
    m_strLogFileName = strLogFileName;
}

void CMyLog::SetWriteTime(bool bWriteTime)
{
    m_bWriteTime = bWriteTime;
}


void CMyLog::WriteLog(const char* fmt, ...)
{
    ::pthread_mutex_lock(&m_csWriteLog);


    va_list argptr;
    va_start(argptr, fmt);
    //_vsntprintf_s(m_szBuffer, BUFSIZE, fmt, argptr);	// 如果fmt含有%号会出错
    vsprintf(m_szBuffer, fmt, argptr);
    va_end(argptr);


    MyCFile fileLog;
    if(fileLog.Open(m_strLogFileName.data(), "a"))      // 附加方式写文件
    {
        // 一天的日志文件最大为10M，超过的话，应该是出现异常，不再写入，避免日志文件太大，占满内存
        if (fileLog.GetLength() < 10*1024*1024)
        {
            fileLog.SeekToEnd();

            CMyString strToWrite = ("");
            if(m_bWriteTime)
            {
                CTime ct = CTime::GetCurrentTimeT();
                strToWrite.Format(("【%02d:%02d:%02d】"), ct.GetHour(), ct.GetMinute(), ct.GetSecond());
            }

            strToWrite += m_szBuffer;
            //strToWrite += fmt;
            strToWrite += ("\r\n");

            // 注意：要转换成窄字符，否则出现乱码（可能是因为.txt文件默认是ansi编码）
            // 写入中文乱码，待解决

            //printf(strToWrite.C_Str());
            fileLog.Write((void*)strToWrite.C_Str(), strToWrite.GetLength());

        }

        fileLog.Close();
    }

    ::pthread_mutex_unlock(&m_csWriteLog);

}


void CMyLog::PrintLog(const char *szLog, LogLevel level)
{
    if( strlen(m_strLogDirPath.Data()) == 0 )
    {
       m_strLogDirPath = g_Global.m_strFolderPath+"/"+HTTP_FOLDER_ADATA+"/Log/log.out";
    }

    ::pthread_mutex_lock(&m_csWriteLog);
    // 根据日志等级获取颜色标识
    char* stype;
    switch (level) {
    case LV_INFO:
        stype = ST_NONE;
        break;
    case LV_WARNING:
        stype = ST_GREEN;
        break;
    case LV_ERROR:
        stype = ST_LIGHT_RED;
        break;
    case LV_DEADLY:
        stype = ST_LIGHT_RED;
        break;
    default:
        break;
    }

    if(m_bPrint)
    {
        if( strlen(szLog) <128 )
        {
            //printf("%s%s\n",stype,szLog);
            printf("%s\n",szLog);
        }
    }
#if defined(Q_OS_LINUX)
    CLIPrint(szLog, strlen(szLog));
#endif
    if( strstr(szLog,"----------send to web") !=NULL || strstr(szLog,"----------recv from web") !=NULL || strstr(szLog,"\"command\":") !=NULL )
    {
        ::pthread_mutex_unlock(&m_csWriteLog);
        return;
    }

    MyCFile fileLog;
    ulong fileSize = GetFileSize(m_strLogDirPath.Data());
    bool flag = FALSE;

    if(fileSize < 1024*1024*2)     // 日志文件最大为2M，超过的话，全部清除
    {
        flag = fileLog.Open(m_strLogDirPath.Data(), "a");
    }
    else
    {
        flag = fileLog.Open(m_strLogDirPath.Data(), "w");
    }

    if(flag)
    {
        fileLog.SeekToEnd();

        CMyString strToWrite = ("");
        if(m_bWriteTime)
        {
            //CTime ct = CTime::GetCurrentTimeT();
            //strToWrite.Format(("【%02d:%02d:%02d】"), ct.GetHour(), ct.GetMinute(), ct.GetSecond());
        }

        strToWrite += szLog;
        strToWrite += ("\r\n");

        // 注意：要转换成窄字符，否则出现乱码（可能是因为.txt文件默认是ansi编码）
        // 写入中文乱码，待解决

        //puts(strToWrite.C_Str());
        fileLog.Write((void*)strToWrite.C_Str(), strToWrite.GetLength());

        fileLog.Close();
    }

    ::pthread_mutex_unlock(&m_csWriteLog);
}

void CMyLog::NotifyLog(const char *fmt, ...)
{
    memset(m_szFormat, 0, sizeof(m_szFormat));

    try
    {
        va_list argptr;
        va_start(argptr, fmt);
        //_vsntprintf_s(m_szBuffer, BUFSIZE, fmt, argptr);	// 如果fmt含有%号会出错
        vsprintf(m_szFormat, fmt, argptr);
        va_end(argptr);
    }
    catch (...)
    {
        m_szFormat[0] = 0;
    }

    char szLog[BUFSIZE+20] = {0};
    sprintf(szLog, "[NOTIFY] %s", m_szFormat);
    PrintLog(szLog, LV_INFO);
}

void CMyLog::ErrorLog(const char *fmt, ...)
{
    memset(m_szFormat, 0, sizeof(m_szFormat));

    try
    {
        va_list argptr;
        va_start(argptr, fmt);
        //_vsntprintf_s(m_szBuffer, BUFSIZE, fmt, argptr);	// 如果fmt含有%号会出错
        vsprintf(m_szFormat, fmt, argptr);
        va_end(argptr);
    }
    catch (...)
    {
        m_szFormat[0] = 0;
    }

    char szLog[BUFSIZE+20] = {0};
    sprintf(szLog, "[ERROR] %s", m_szFormat);
    PrintLog(szLog, LV_ERROR);
}

const char *CMyLog::Format(const char *fmt, ...)
{
    memset(m_szFormat, 0, sizeof(m_szFormat));

    try
    {
        va_list argptr;
        va_start(argptr, fmt);
        //_vsntprintf_s(m_szBuffer, BUFSIZE, fmt, argptr);	// 如果fmt含有%号会出错
        vsprintf(m_szFormat, fmt, argptr);
        va_end(argptr);
    }
    catch (...)
    {
        m_szFormat[0] = 0;
    }

    return m_szFormat;
}

void CMyLog::SetPrint(bool bPrint)
{
    m_bPrint = bPrint;
}

void CMyLog::AddCLIFd(int fd)
{
    if(!IsExistFd(fd))
    {
        m_CLIFdArray.push_back(fd);
    }
}

void CMyLog::RemoveCLIFd(int fd)
{
    vector<int>::iterator iter = m_CLIFdArray.begin();

    for(; iter != m_CLIFdArray.end(); iter++)
    {
        if(*iter == fd)
        {
            m_CLIFdArray.erase(iter);
            break;
        }
    }
}

void CMyLog::CLIPrint(const char *szLog, int nLen)
{
    vector<int>::iterator iter = m_CLIFdArray.begin();
    for(; iter != m_CLIFdArray.end(); iter++)
    {
        write(*iter, szLog, nLen);
    }

}

bool CMyLog::IsExistFd(int fd)
{
    vector<int>::iterator iter = m_CLIFdArray.begin();

    for(; iter != m_CLIFdArray.end(); iter++)
    {
        if(*iter == fd)
        {
            return true;
        }
    }

    return false;
}









