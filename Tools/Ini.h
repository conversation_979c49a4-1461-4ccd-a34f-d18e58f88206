#ifndef INI_H
#define INI_H

#include "tools.h"

class CIni
{
public:
    CMyString csLineEnd;
    int FindItem(const int iSection, const char * cItem, CMyString &csVal);
    bool RemoveSection(const char * cSection);
    bool IsSection(const int iSection);
    int InsertSection(const char * cSection);
    int FindSection(const char * cSection);

    //bool SetValue(const char * cSection, const char * cItem, const COLORREF crVal);
    bool SetValue(const char * cSection, const char * cItem, const bool bVal);
    bool SetValue(const char * cSection, const char * cItem, const char * cVal);
    bool SetValue(const char * cSection, const char * cItem, const double dbVal);
    bool SetValue(const char * cSection, const char * cItem, const float fVal);
    bool SetValue(const char * cSection, const char * cItem, const long lVal);
    bool SetValue(const char * cSection, const char * cItem, const int iVal);

    //bool GetValue(const char * cSection, const char * cItem, COLORREF &crVal);
    bool GetValue(const char * cSection, const char * cItem, bool &bVal);
    bool GetValue(const char * cSection, const char * cItem, CMyString &cVal);
    //bool GetValue(const char * cSection, const char * cItem, char* &cVal);
    bool GetValue(const char * cSection, const char * cItem, double &dbVal);
    bool GetValue(const char * cSection, const char * cItem, float &fVal);
    bool GetValue(const char * cSection, const char * cItem, long &lVal);
    bool GetValue(const char * cSection, const char * cItem, int &iVal);

    //wjs///////////////bComment表示是不是添加注释
    void AddLine(CMyString strAdd, bool bComment = false);

    ////////////////////
    void Clear();
    bool Write(const char * cFileName);
    bool Read(const char * cFileName);
    bool Write();
    bool Read();

    CIni();
    virtual ~CIni();
    vector<CMyString>	m_csList;
    const char * m_cFileName;

    //CS添加
    int  FindMultiItem(const int iSection, const char * cItem, CMyString &csVal);
    void RemoveMultiLineItem(const int idx);
};


#endif // INI_H
