#include "stdafx.h"
#include "FileSync.h"


#if defined(Q_OS_LINUX)
CCurlDownload::CCurlDownload()
{
    m_Curl = NULL;
}

CCurlDownload::~CCurlDownload()
{
    FreeCurl();
}

bool CCurlDownload::InitCurl()
{
    CURLcode Code = curl_global_init(CURL_GLOBAL_WIN32);
    if(Code != CURLE_OK)
    {
       NOTIFY("init libcurl failed.");
       return FALSE;
    }

    m_Curl = curl_easy_init();
    if(m_Curl == NULL)
    {
        NOTIFY("curl init failed");
        return FALSE;
    }

    return TRUE;
}

void CCurlDownload::FreeCurl()
{
    if(m_Curl != NULL)
    {
        curl_easy_cleanup(m_Curl);
        curl_global_cleanup();
        m_Curl = NULL;
    }
}


char* String2Ascll( const char* szData, int nLen)
{
    char* szAscll = (char*)malloc(nLen*2);
    memset(szAscll, 0, nLen*2);
    int n = 0;
    for(int i=0; i<nLen; i++)
    {
        if((szData[i] >= 0 && szData[i] <= 47) ||
           (szData[i] >= 58 && szData[i] <= 64) ||
           (szData[i] >= 91 && szData[i] <= 96) ||
           (szData[i] >= 123 && szData[i] <= 126))
        {
            char szHex[4] = {0};
            sprintf(szHex, "%%%x", szData[i]);
            strcpy(&szAscll[n], szHex);
            n+=3;
            //printf("%s", szHex);
        }
        else
        {
            szAscll[n++] = szData[i];
        }
    }

    return szAscll;
}

/**
 * @brief CCurlDownload::StartDownloadFile [ CURL 下载文件 ]
 * @param szURL             [ URL ]
 * @param szFilePathName    [ 保存本地路径名称 ]
 * @return                  [ 是否成功 ]
 */
bool CCurlDownload::StartDownloadFile(const char* szURL,              // URL
                                      const char* szFilePathName,     // 保存文件路径名称
                                      void*	pUserDefined)             // 自定义数据
{
    NOTIFY("Start Download File : %s, download to : %s", szURL, szFilePathName);
    bool isOk = FALSE;
    CURLcode return_code = CURLE_OK;
    struct stat st;
    memset(&st, 0, sizeof(st));

    if(!InitCurl())
    {
        return FALSE;
    }

    return_code = curl_global_init(CURL_GLOBAL_WIN32);
    if(return_code != CURLE_OK)
    {
       NOTIFY("init libcurl failed.");
       return FALSE;
    }

    remove(szFilePathName);
    FILE* fp = NULL;//fopen(szFilePathName, "ab+");    //

    if(fp == NULL)
    {
        NOTIFY("open file failed : %s", szFilePathName);
        goto END;
    }

    // 设置easy handle属性
    curl_easy_setopt(m_Curl, CURLOPT_FAILONERROR, 1L);

    return_code = curl_easy_setopt(m_Curl, CURLOPT_URL, szURL);
    if(return_code != CURLE_OK)
    {
        printf("CURLOPT_URL code : %d\n", return_code);
        goto END;
    }

    curl_easy_setopt(m_Curl, CURLOPT_CONNECTTIMEOUT, 3);
    curl_easy_setopt(m_Curl, CURLOPT_RESUME_FROM_LARGE, st.st_size);

    return_code = curl_easy_setopt(m_Curl, CURLOPT_WRITEFUNCTION, process_data);
    if(return_code != CURLE_OK)
    {
        printf("CURLOPT_WRITEFUNCTION code : %d\n", return_code);
        goto END;
    }

    return_code = curl_easy_setopt(m_Curl, CURLOPT_WRITEDATA, fp);
    if(return_code != CURLE_OK)
    {
        printf("CURLOPT_WRITEDATA code : %d\n", return_code);
        goto END;
    }

    curl_easy_setopt(m_Curl, CURLOPT_VERBOSE, 1L);
    curl_easy_setopt(m_Curl, CURLOPT_NOPROGRESS, 0);
    curl_easy_setopt(m_Curl, CURLOPT_PROGRESSFUNCTION, my_progress_func);
    curl_easy_setopt(m_Curl, CURLOPT_PROGRESSDATA, pUserDefined);

    // 执行数据请求
    return_code = curl_easy_perform(m_Curl);
    if(return_code != CURLE_OK)
    {
        printf("curl_easy_perform code : %d\n", return_code);
        goto END;
    }
    isOk = TRUE;

    // 释放资源
END:
    FreeCurl();
    if(fp != NULL)  fclose(fp);
    return isOk;

}


/**
 *    @brief libcurl接收到数据时的回调函数
 *
 *    将接收到的数据保存到本地文件中，同时显示在控制台上。
 *
 *    @param [in] buffer 接收到的数据所在缓冲区
 *    @param [in] size 数据长度
 *    @param [in] nmemb 数据片数量
 *    @param [in/out] 用户自定义指针
 *    @return 获取的数据长度
 */

size_t CCurlDownload::process_data(void *buffer, size_t size, size_t nmemb, void *user_p)
{
    FILE *fp = (FILE *)user_p;
    size_t return_size = fwrite(buffer, size, nmemb, fp);
    //printf("return_size : %d\n", return_size);
    //cout << (char *)buffer << endl;
    return return_size;
}

// 下载进度回调函数
size_t CCurlDownload::my_progress_func(void *progress_data,   // 用户自定义参数
                                       double dltotal,    // 下载文件总大小
                                       double dlnow,      // 已下载大小
                                       double ultotal,    // 上传文件总大小
                                       double ulnow)      // 已上传大小
{
    static int i = 0;
    i++;
    if(i%500 == 0)
    {
        double dPercentage = dlnow*100.0 / dltotal;
        //printf("%s %g / %g (%g %%)\n", progress_data, d, t, d*100.0/t);
        //NOTIFY(" * speed : %0.2f %%\n", dPercentage);

        // 发送同步进度
        if(progress_data != NULL)
        {
            CHostFileSync* pUseDefined = (CHostFileSync*)progress_data;

            pUseDefined->UploadSongProgress(dPercentage);
        }
    }

    return 0;
}

/***************************************************/


CHostFileSync::CHostFileSync()
{
    Init();
}

CHostFileSync::~CHostFileSync()
{

}

void CHostFileSync::Init()
{
    m_bSync = FALSE;
    m_tSongSyncInfo.strCurSyncSongName = "";
    m_tSongSyncInfo.nAllSongCount = 0;
    m_tSongSyncInfo.nFinishedSongCount = 0;
    m_tSongSyncInfo.nPercentage = 0;
    m_CurSyncFile = FILE_PLAYLIST;
}

bool CHostFileSync::SyncFile(FileType ft, const char *szHttpURLAddr)
{
    if(IsSync())
    {
        return FALSE;
    }
    else
    {
        m_strURLAddr = szHttpURLAddr;
        m_CurSyncFile = ft;

        StartSyncXmlFilePthread();
    }

    return TRUE;
}

// 歌曲文件下载时，上传歌曲同步进度
void CHostFileSync::UploadSongProgress(double nPercentage)
{
    #if 0
    g_Global.m_Network.m_CmdSend.CmdSRCSendSyncProgress(0x01,
                                                        m_tSongSyncInfo.nAllSongCount,
                                                        m_tSongSyncInfo.nFinishedSongCount,
                                                        (int)nPercentage);
    #endif
}

void CHostFileSync::StartSyncXmlFilePthread()
{
    pthread_t pth;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);

    pthread_create(&pth, &attr, SyncXmlFilePthread, (void*)this);
    pthread_attr_destroy(&attr);
}


void *CHostFileSync::SyncXmlFilePthread(void *lparam)
{
    CHostFileSync* pThis = (CHostFileSync*)lparam;
    pThis->SetSync(TRUE);

    CCurlDownload curl;
    FileType ft = pThis->GetSyncFileType();
    switch (ft)
    {
    // 播放列表
    case FILE_PLAYLIST:
      {
        CMyString strFilePath;
        strFilePath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, HTTP_FILE_PLAYLIST_HOST);

        CMyString strURL;
        char* szAscll = String2Ascll(HTTP_FILE_PLAYLIST, strlen(HTTP_FILE_PLAYLIST));
        strURL.Format("%s/%s/%s/%s", pThis->GetURLAddr().C_Str(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, szAscll);
        free(szAscll);

        // 下载歌曲列表
        if(curl.StartDownloadFile(strURL.C_Str(), strFilePath.C_Str()))
        {
            // 重置上级主机播放列表数据
            g_Global.m_HigherHost.m_Playlist.ClearList();
            g_Global.m_HigherHost.m_Playlist.SetFileName(HTTP_FILE_PLAYLIST_HOST);
            g_Global.m_HigherHost.m_Playlist.ReadFile();
            #if 0
            g_Global.m_Network.m_CmdSend.CmdSRCReplyUpdateFile(ft, 0x03);  // 同步成功
            #endif
            CMyString strTip;
            strTip.Format(("curl download playlist file success : %s"), strURL.C_Str());
            g_Global.m_Network.AddLog(strTip);

            // 下载歌曲文件
            pThis->SyncSongFile(pThis->GetURLAddr().C_Str());
        }
        else
        {
            #if 0
            g_Global.m_Network.m_CmdSend.CmdSRCReplyUpdateFile(ft, 0x04);  // 同步成功
            #endif
            CMyString strTip;
            strTip.Format(("curl download playlist file failed : %s"), strURL.C_Str());
            g_Global.m_Network.AddLog(strTip);
        }
      }
        break;

    // 定时文件
    case FILE_TIMER:
      {
        CMyString strFilePath;
        strFilePath.Format("%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, HTTP_FILE_TIMER_HOST);

        CMyString strURL;
        char* szAscll = String2Ascll(HTTP_FILE_TIMER_HOST, strlen(HTTP_FILE_TIMER_HOST));
        strURL.Format("%s/%s/%s/%s", pThis->GetURLAddr().C_Str(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, szAscll);
        free(szAscll);

        // 下载定时文件
        if(curl.StartDownloadFile(strURL.C_Str(), strFilePath.C_Str()))
        {
            // 重置上级主机播放列表数据
            g_Global.m_HigherHost.m_TimerScheme.ClearSchemes();
            g_Global.m_HigherHost.m_TimerScheme.AddScheme(LANG_STR(LANG_SECTION_TIMING, "Timing Scheme", ("定时方案")));
            g_Global.m_HigherHost.m_TimerScheme.SetCurScheme(0);
            g_Global.m_HigherHost.m_TimerScheme.ReadTimerFile(HTTP_FILE_TIMER_HOST);
            #if 0
            g_Global.m_Network.m_CmdSend.CmdSRCReplyUpdateFile(ft, 0x03);  // 同步成功
            #endif
            CMyString strTip;
            strTip.Format(("curl download timer file success : %s"), strURL.C_Str());
            g_Global.m_Network.AddLog(strTip);

        }
        else
        {
            #if 0
            g_Global.m_Network.m_CmdSend.CmdSRCReplyUpdateFile(ft, 0x04);  // 同步成功
            #endif
            CMyString strTip;
            strTip.Format(("curl download timer file failed : %s"), strURL.C_Str());
            g_Global.m_Network.AddLog(strTip);
        }

      }
        break;
    default:
        break;
    }


    pThis->Init();
    return NULL;
}

void CHostFileSync::SyncSongFile(const char *szURLAddr)
{
    CCurlDownload curl;

    if(!curl.InitCurl())
    {
        NOTIFY("Curl init failed");
        return ;
    }

    m_tSongSyncInfo.nAllSongCount = g_Global.m_HigherHost.m_Playlist.GetAllSongCount();
    int nListCount = g_Global.m_HigherHost.m_Playlist.GetListCount();
    for(int i=0; i<nListCount; i++)
    {
        int nSongCount = g_Global.m_HigherHost.m_Playlist.GetListSongCount(i);

        for(int j=0; j<nSongCount; j++)
        {
            CSong song = g_Global.m_HigherHost.m_Playlist.GetListSong(i, j);

            // 判断歌曲文件是否存在
            CMyString strSongFileAbsolutePath;
            strSongFileAbsolutePath.Format("%s/%s", g_Global.m_strFolderPath.Data(), song.GetPathName().C_Str());

            if(IsExistFile(strSongFileAbsolutePath.C_Str())) // 歌曲文件存在，跳过
            {
                m_tSongSyncInfo.nFinishedSongCount++;
                continue;
            }
            else    // 歌曲文件不存在，执行下载
            {
                // URL
                CMyString strHTTPURL;
                char* szAscll = String2Ascll(song.GetName().C_Str(), song.GetName().GetLength());
                strHTTPURL.Format("%s/%s", szURLAddr, szAscll);
                free(szAscll);

                // 下载歌曲文件
                m_tSongSyncInfo.strCurSyncSongName = song.GetName();
                curl.StartDownloadFile(strHTTPURL.C_Str(), strSongFileAbsolutePath.C_Str(), (void*)this);

                m_tSongSyncInfo.nFinishedSongCount++;
            }
        }
    }

    UploadSongProgress(100.0);
}






#if 0
void CFileSync::StartSyncSong()
{
    pthread_t pth;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&pth, &attr, SyncSongFilePthread, (void*)this);
    pthread_attr_destroy(&attr);
}

void *CFileSync::SyncSongFilePthread(void *lparam)
{
    CFileSync* pThis = (CFileSync*)lparam;
    CCurlDownload curl;
    int nListCount = g_Global.m_LocalHost.m_HigherHostPlaylist.GetListCount();

    if(!curl.InitCurl())
    {
        return NULL;
    }

    for(int i=0; i<nListCount; i++)
    {
        int nSongCount = g_Global.m_LocalHost.m_HigherHostPlaylist.GetListSongCount(i);

        for(int j=0; j<nSongCount; j++)
        {
            CSong song = g_Global.m_LocalHost.m_HigherHostPlaylist.GetListSong(i, j);

            // 判断歌曲文件是否存在
            CMyString strSongFileAbsolutePath;
            strSongFileAbsolutePath.Format("%s/%s", g_Global.m_strFolderPath.Data(), song.GetPathName().C_Str());

            if(IsExistFile(strSongFileAbsolutePath.C_Str())) // 歌曲文件存在，跳过
            {
                continue;
            }
            else    // 歌曲文件不存在，执行下载
            {
                // URL
                CMyString strHTTPURL;
                char* szAscll = String2Ascll(song.GetName().C_Str(), song.GetName().GetLength());
                strHTTPURL.Format("%s/%s", pThis->GetURL().C_Str(), szAscll);
                free(szAscll);

                if(curl.StartDownloadFile(strHTTPURL.C_Str(), strSongFileAbsolutePath.C_Str()))
                {

                }
                else
                {
                    // 同步进度上传

                }
            }
        }
    }
}

#endif



/////////////////////////////////////////////////////////////

CFileSyncManager::CFileSyncManager()
{
    m_bSync = FALSE;
    m_bStopSync = FALSE;
}

CFileSyncManager::~CFileSyncManager()
{

}

bool CFileSyncManager::StartSyncFile(FileType ft)
{
    if(IsSync())
    {
        printf("sync is busy");
        return FALSE;
    }

    m_SyncFt = ft;
    m_bStopSync = FALSE;

    pthread_t pth;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);

    pthread_create(&pth, &attr, SyncFilePth, (void*)this);
    pthread_attr_destroy(&attr);
    return true;
}

void CFileSyncManager::StopSyncFile()
{
    m_bStopSync = TRUE;

    int nSecCount = g_Global.m_Sections.GetSecCount();
    for(int i=0; i<nSecCount; i++)
    {
        CSection& section = g_Global.m_Sections.GetSection(i);
        if ( section.IsSyncSong())   // 在同步歌曲
        {
            g_Global.m_Network.m_CmdSend.CmdStopSyncSongFile(section);
        }
    }

}

void *CFileSyncManager::SyncFilePth(void *lparam)
{
    CFileSyncManager* pThis = (CFileSyncManager*)lparam;
    pThis->SetSync(TRUE);

    FileType ft = pThis->GetSyncFt();
    int nMaxSyncCount = (ft == FILE_PLAYLIST) ? g_Global.m_nMaxDevicesSync : 5;

    NOTIFY("start sync file : %d", ft);
    while(!pThis->IsStop())
    {
        int nSecCount = g_Global.m_Sections.GetSecCount();
        for(int i=0; i<nSecCount; i++)
        {
            if(g_Global.m_Sections.GetSyncSongSectionCount() >= nMaxSyncCount)
            {
                usleep(10000);
                break;
            }

            CSection& section = g_Global.m_Sections.GetSection(i);
//            printf("section name : %s\n", section.GetUTFName().data());
//            printf("%d  -- %d\n", section.NeedUpdateFile((DATETIME_FILE)(ft-1)), section.IsUpdateFileDevice());
//            printf("datatime : %s\n", section.GetFileDateTime((DATETIME_FILE)(ft-1)).C_Str());
//            printf("isSync : %d\n", section.IsSyncSong());

            if (section.NeedUpdateFile((DATETIME_FILE)(ft-1))
                && !section.IsPagingIn()	 // 不是在寻呼
                && !section.IsSyncSong()     // 不是在同步歌曲
                && section.IsUpdateFileDevice())
            {
                if(ft == FILE_PLAYLIST)
                {
                    section.SetSyncSong(TRUE);
                }

                //printf("sync section : %s\n", section.GetName());
                g_Global.m_Network.m_CmdSend.CmdUpdateFile(ft, section);
            }
        }

        break;

    }

    NOTIFY("finished sync file pthread");
    pThis->SetSync(FALSE);
    return NULL;
}

#endif
