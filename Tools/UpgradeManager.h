#ifndef UPGRADEMANAGER_H
#define UPGRADEMANAGER_H

#include <iostream>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <string.h>
#include <pthread.h>
#include <list>
#include "Model/Device/Section.h"

using namespace std;

#define MAX_UPGRADE_SECTION_COUNT   50

class CUpgradeInfo
{
public:
    CUpgradeInfo(CMyString		strVersion,			// 版本号
                 CMyString		strAbsolutePath,	// 绝对路径
                 CMyString		strRelativePath,	// 相对路径
                 string     	lpServerIP,			// 服务器IP
                 unsigned short	uPort,				// 服务器端口
                 DeviceModel    mode,               // 设备类型
                 string         strSecMac);			// 设备Mac

public:
    CMyString		m_strVersion;               // 版本号
    CMyString		m_strAbsolutePath;          // 绝对路径
    CMyString		m_strRelativePath;          // 相对路径
    string          m_strHttpServerIP;          // 服务器IP
    unsigned short	m_uPort;                    // 服务器端口
    DeviceModel     m_SecModel;                 // 设备类型
    string          m_strSecMac;                // 设备Mac

};


class CUpgradeManager
{
public:
    CUpgradeManager();
    ~CUpgradeManager();

    void  AddUpdate(CUpgradeInfo& upgradeInfo);
    void  RemoveUpdate(CUpgradeInfo& upgradeInfo);

    void  HandleUpgrade(int nUpgradeCount=0);


private:
    list<CUpgradeInfo>   m_UpgradeInfoList;

    pthread_mutex_t      m_csUpdate;

};

#endif // UPGRADEMANAGER_H
