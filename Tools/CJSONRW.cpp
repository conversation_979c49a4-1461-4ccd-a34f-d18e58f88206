#include "CJSONRW.h"


// 检测cJSON内容(char*类型)
bool    IsStringJson(cJSON* json)
{
    if(json == NULL)
    {
        return false;
    }

    if(json->valuestring != NULL)
    {
        return true;
    }
    return false;
}

CJSONRead::CJSONRead(string strJson)
{
    m_root = cJSON_Parse(strJson.data());

    if(m_root != NULL)
    {
        m_bValid = true;
    }
    else
    {
        m_bValid = false;
    }
}

CJSONRead::~CJSONRead()
{
    if(m_root)
    {
        cJSON_Delete(m_root);
    }
}

string CJSONRead::GetValueString(string field)
{
    if(!IsValid())
    {
        return "";
    }

    cJSON* jsString = cJSON_GetObjectItem(m_root, field.data());
    if(IsStringJson(jsString))
    {
        return jsString->valuestring;
    }
    else
    {
        m_bValid = false;
        return "";
    }
}

int CJSONRead::GetValueInt(string field)
{
    if(!IsValid())
    {
        return 0;
    }

    cJSON* jsInt = cJSON_GetObjectItem(m_root, field.data());
    if(jsInt)
    {
        return jsInt->valueint;
    }
    else
    {
        m_bValid = false;
        return 0;
    }
}

double CJSONRead::GetValueDouble(string field)
{
    if(!IsValid())
    {
        return 0.0;
    }

    cJSON* jsDouble = cJSON_GetObjectItem(m_root, field.data());
    if(jsDouble)
    {
        return jsDouble->valuedouble;
    }
    else
    {
        m_bValid = false;
        return 0.0;
    }
}

bool CJSONRead::GetValueBool(string field)
{
    if(!IsValid())
    {
        return false;
    }

    cJSON* jsInt = cJSON_GetObjectItem(m_root, field.data());
    if(jsInt)
    {
        return jsInt->valueint;
    }
    else
    {
        m_bValid = false;
        return false;
    }
}


/*===================================================================*/

CJSONWrite::CJSONWrite()
{
    m_root = cJSON_CreateObject();
}

CJSONWrite::~CJSONWrite()
{
    cJSON_Delete(m_root);
}

void CJSONWrite::AddObjectString(string cmd, string item)
{
    cJSON_AddItemToObject(m_root, cmd.data(), cJSON_CreateString(item.data()));
}

void CJSONWrite::AddObjectInt(string cmd, int item)
{
    cJSON_AddItemToObject(m_root, cmd.data(), cJSON_CreateNumber(item));
}

void CJSONWrite::AddObjectDouble(string cmd, double item)
{
    cJSON_AddItemToObject(m_root, cmd.data(), cJSON_CreateNumber(item));
}














