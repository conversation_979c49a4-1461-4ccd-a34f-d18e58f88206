#ifndef LXTIMER_H
#define LXTIMER_H

#include <QtCore/qglobal.h>
#include <sys/time.h>
#if defined(Q_OS_LINUX)
#include <sys/times.h>
#endif
#include <stdio.h>
#include <unistd.h>

#if 0
class CLxTimer
{
public:
    CLxTimer(int sec=0,int usec=0);
    void    GetCurrentTimeT();

    timeval* GetTimeval() { return &m_timeval; }
    double   GetTimer();
    void     SetTimeval(timeval& time);

    int      GetSec()  { return m_timeval.tv_sec;  }
    int      GetUsec() { return m_timeval.tv_usec; }

    void        AddTimer(int sec = 0, int usec = 0);

    CLxTimer    operator +(CLxTimer& time) ;
    CLxTimer    operator +=(CLxTimer time);
    CLxTimer    operator -(CLxTimer& time) ;
    CLxTimer    operator =(CLxTimer time);

private:
    timeval m_timeval;

};

#endif

#if 1

class CLxTimer
{
public:
    CLxTimer(clock_t t = 0);
    void    GetCurrentTimeT();

    //timeval* GetTimeval() { return &m_timeval; }
    double   GetTimer();

    //int      GetSec()  { return m_timeval.tv_sec;  }
    //int      GetUsec() { return m_timeval.tv_usec; }

#if 1
    //CLxTimer    operator +(CLxTimer& time) ;
    CLxTimer    operator +=(CLxTimer time);
    CLxTimer    operator -(CLxTimer&time);
    CLxTimer    operator =(CLxTimer time);
#endif

public:
    //timeval m_timeval;
    #if defined(Q_OS_LINUX)
    struct  tms begin_tms;
    #endif
    //clock_t m_timeval;
    struct timespec m_timeval;

private:
    int sc_clk_tck;
};

#endif


#endif // LXTIMER_H
