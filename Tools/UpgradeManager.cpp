#include "stdafx.h"
#include "UpgradeManager.h"


CUpgradeInfo::CUpgradeInfo(CMyString		strVersion,			// 版本号
                           CMyString		strAbsolutePath,	// 绝对路径
                           CMyString		strRelativePath,	// 相对路径
                           string       	lpServerIP,			// 服务器IP
                           unsigned short	uPort,				// 服务器端口
                           DeviceModel      mode,               // 设备类型
                           string           strSecMac)  		// 设备Mac
{
    m_strVersion = strVersion;
    m_strAbsolutePath = strAbsolutePath;
    m_strRelativePath = strRelativePath;
    m_strHttpServerIP = lpServerIP;
    m_uPort = uPort;
    m_SecModel = mode;
    m_strSecMac = strSecMac;

}

////////////////////////////////////////////////////////////////


CUpgradeManager::CUpgradeManager()
{
    m_csUpdate = PTHREAD_MUTEX_INITIALIZER;
}

CUpgradeManager::~CUpgradeManager()
{
    m_UpgradeInfoList.clear();
}

void CUpgradeManager::AddUpdate(CUpgradeInfo &upgradeInfo)
{
    pthread_mutex_lock(&m_csUpdate);

    bool flag = TRUE;
    list<CUpgradeInfo>::iterator iter;
    for(iter = m_UpgradeInfoList.begin(); iter != m_UpgradeInfoList.end(); iter++)
    {
        if(iter->m_strSecMac == upgradeInfo.m_strSecMac &&
           iter->m_strAbsolutePath == upgradeInfo.m_strAbsolutePath)
        {
            flag = FALSE;
            break;
        }
    }

    if(flag)
    {
        m_UpgradeInfoList.push_back(upgradeInfo);
    }

    pthread_mutex_unlock(&m_csUpdate);
}

void CUpgradeManager::RemoveUpdate(CUpgradeInfo &upgradeInfo)
{
    pthread_mutex_lock(&m_csUpdate);

    list<CUpgradeInfo>::iterator iter;
    for(iter = m_UpgradeInfoList.begin(); iter != m_UpgradeInfoList.end(); iter++)
    {
        if(iter->m_strSecMac == upgradeInfo.m_strSecMac &&
           iter->m_strAbsolutePath == upgradeInfo.m_strAbsolutePath)
        {
            m_UpgradeInfoList.erase(iter);
            break;
        }
    }

    pthread_mutex_unlock(&m_csUpdate);
}


void CUpgradeManager::HandleUpgrade(int nUpgradeCount)
{
    pthread_mutex_lock(&m_csUpdate);
    int nCount = MAX_UPGRADE_SECTION_COUNT - nUpgradeCount;
    printf("HandleUpgrade:nCount=%d\n",nCount);

    list<CUpgradeInfo>::iterator iter = m_UpgradeInfoList.begin();

    string md5("");
    for( ; iter != m_UpgradeInfoList.end(); )
    {
        if(md5 == "")
        {
            md5=GetFileMd5(iter->m_strAbsolutePath.C_Str());
            printf("upgrade_md5=%s\n",md5.c_str());
        }

        bool flag = true;

        DeviceModel model = iter->m_SecModel;
        LPCSections sections;
        if(CSections::IsSectionDevice(model))
        {
            sections = &g_Global.m_Sections;
        }
        else if(CSections::IsNotSectionDevice(model))
        {
            sections = g_Global.m_ModelToDevices[model];
        }
        else
        {
            flag = false;
        }

        if(nCount-- <= 0)
        {
            break;
        }

        if(flag)
        {
            LPCSection pSection = sections->GetSectionByMac(iter->m_strSecMac.data());

            if (pSection != NULL && pSection->IsOnline() && !pSection->GetUpgrading())
            {
                pSection->SetUpgrading(TRUE);
                
                g_Global.m_Network.m_CmdSend.CmdUpgradeFirmware(pSection->GetDeviceModel(),
                                                                iter->m_strVersion,
                                                                iter->m_strAbsolutePath,
                                                                iter->m_strRelativePath,
                                                                iter->m_strHttpServerIP.data(),
                                                                iter->m_uPort,
                                                                *pSection,md5);
            }
        }

        iter=m_UpgradeInfoList.erase(iter);
    }
    pthread_mutex_unlock(&m_csUpdate);

}





