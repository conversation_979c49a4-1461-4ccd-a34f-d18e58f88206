#ifndef MYLOG_H
#define MYLOG_H

#include <Tools/CMyString.h>
#include <pthread.h>

#define ST_NONE         (char *)"\033[m"
#define ST_RED          (char *)"\033[0;32;31m"
#define ST_LIGHT_RED    (char *)"\033[1;31m"
#define ST_GREEN        (char *)"\033[0;32;32m"
#define ST_LIGHT_GREEN  (char *)"\033[1;32m"
#define ST_BLUE         (char *)"\033[0;32;34m"
#define ST_LIGHT_BLUE   (char *)"\033[1;34m"
#define ST_DARY_GRAY    (char *)"\033[1;30m"
#define ST_CYAN         (char *)"\033[0;36m"
#define ST_LIGHT_CYAN   (char *)"\033[1;36;43m"
#define ST_PURPLE       (char *)"\033[0;35m"
#define ST_LIGHT_PURPLE (char *)"\033[1;35m"
#define ST_BROWN        (char *)"\033[0;33m"
#define ST_YELLOW       (char *)"\033[1;33m"           // or \033[1;33m
#define ST_LIGHT_GRAY   (char *)"\033[0;37m"
#define ST_WHITE        (char *)"\033[1;37m"

typedef enum
{
    LV_DEBUG,            // 调试
    LV_INFO,             // 信息
    LV_WARNING,          // 警告
    LV_ERROR,            // 错误
    LV_DEADLY            // 致命错误
}LogLevel;


class CMyLog
{
public:
    CMyLog();
    ~CMyLog();

public:
    void			 InitLogFile(string strLogFileName);
    void			 SetWriteTime(bool bWriteTime);
    void			 WriteLog(const char* fmt, ...);
    void             PrintLog(const char* szLog, LogLevel level);
    void             NotifyLog(const char* fmt, ...);
    void             ErrorLog(const char* fmt, ...);
    const char*      Format(const char* fmt, ...);
    void             SetPrint(bool bPrint);
    void             SetReDirectFilepath();

// CLI
    //void             SetCLIFd(int fd)         { m_CLIFd = fd; }
    void             AddCLIFd(int fd);
    void             RemoveCLIFd(int fd);
    void             CLIPrint(const char* szLog, int nLen);

private:
    bool             IsExistFd(int fd);

protected:
    enum				{	BUFSIZE = 4096	};
    char				m_szBuffer[BUFSIZE];

    string				m_strLogFileName;
    pthread_mutex_t     m_csWriteLog;
    bool				m_bWriteTime;
    bool                m_bPrint;          // 是否打印日志
    //int                   m_CLIFd;           // CLI 接口 socket fd
    char                m_szRedirectFile;

    char                m_szFormat[BUFSIZE];
    vector<int>         m_CLIFdArray;   // CLI 接口 socket fd

    CMyString           m_strLogDirPath;    //log.out文件路径
};



#endif // MYLOG_H
