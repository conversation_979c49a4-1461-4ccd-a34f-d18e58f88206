#include "MyCFile.h"
#include <sys/stat.h>
#include <string.h>
#include "stdafx.h"


MyCFile::MyCFile()
{
    qfile      = new QFile();
}

MyCFile::~MyCFile()
{
    if(qfile)
    {
        Close();
        delete qfile;
        qfile = NULL;
    }
}

bool MyCFile::Open(const char *szPathName, const char *mode)
{
    if(szPathName == NULL || mode == NULL)
    {
        return false;
    }

    if(qfile->isOpen())
    {
        qfile->close();
    }
    qfile->setFileName(szPathName);
    QIODevice::OpenMode openMode=QIODevice::ReadOnly | QIODevice::ExistingOnly;     //只读且必须且文件必须存在
    if(strcmp(mode,"rb+") == 0)         //读写且文件必须存在
    {
        openMode=QIODevice::ReadWrite | QIODevice::ExistingOnly;
    }
    if(strcmp(mode,"rb") == 0)         //只读且文件必须存在
    {
        openMode=QIODevice::ReadOnly | QIODevice::ExistingOnly;
    }
    else if(strcmp(mode,"wb+") == 0)    //读写，如果文件不存在，自动创建
    {
        openMode=QIODevice::ReadWrite | QIODevice::Truncate;
    }
    else if(strcmp(mode,"wb") == 0)    //只写，如果文件不存在，自动创建
    {
        openMode=QIODevice::WriteOnly | QIODevice::Truncate;
    }
    else if(strcmp(mode,"a") == 0)    //附加写入方式打开，将文件指针指向文件末尾。如果文件不存在则尝试创建之。
    {
        openMode=QIODevice::WriteOnly | QIODevice::Append;
    }
    else if(strcmp(mode,"w") == 0)    //覆写方式打开，将文件指针指向文件末尾。如果文件不存在则尝试创建之。
    {
        openMode=QIODevice::WriteOnly | QIODevice::Truncate;
    }

    if(!qfile->open(openMode))
    {
        return false;
    }
    else
    {
        return true;
    }
}


void MyCFile::Close(void)
{
    if(qfile->isOpen())
    {
        qfile->close();
    }
}


int MyCFile::Read(void *lpBuf, unsigned int count)
{
    if(!qfile->isOpen())
    {
        return 0;
    }
    int nRet=qfile->read((char*)lpBuf,count);

    return nRet;

}

int MyCFile::Write(const void* lpBuf, unsigned int count)
{
    if(!qfile->isOpen())
    {
        return 0;
    }

    int nRet=qfile->write((char*)lpBuf,count);
    return nRet;
}

bool MyCFile::SeekToBegin()
{
    if(!qfile->isOpen())
    {
        return false;
    }

    return qfile->seek(0);
}

bool MyCFile::SeekToEnd()
{
    if(!qfile->isOpen())
    {
        return false;
    }
    return qfile->seek(qfile->size());
}

int MyCFile::Seek(int nOffset, int seekPos)
{
    if(!qfile->isOpen())
    {
        return -1;
    }
    qfile->seek(seekPos+nOffset);
    return seekPos+nOffset;
}


int MyCFile::GetFileSize(const char * fileName)
{
    return qfile->size();
}



