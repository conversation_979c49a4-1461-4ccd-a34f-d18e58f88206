#include "stdafx.h"
#include "ComputerInfo.h"
#if defined(Q_OS_LINUX)
#include <sys/ioctl.h>
#include <linux/hdreg.h>
#endif
#include <sys/fcntl.h>
#if defined(Q_OS_WIN32)
#include <QProcess>
#endif
//char *GetCPUID()
//{
//    static char PSN[30] = {0};
//    int varEAX, varEBX, varECX, varEDX;
//    char str[9];
//    //%eax=1 gives most significant 32 bits in eax
//    __asm__ __volatile__ ("cpuid"   : "=a" (varEAX), "=b" (varEBX), "=c" (varECX), "=d" (varEDX) : "a" (1));
//    sprintf(str, "%08X", varEAX); //i.e. XXXX-XXXX-xxxx-xxxx-xxxx-xxxx
//    sprintf(PSN, "%C%C%C%C-%C%C%C%C", str[0], str[1], str[2], str[3], str[4], str[5], str[6], str[7]);
//    //%eax=3 gives least significant 64 bits in edx and ecx [if PN is enabled]
//    __asm__ __volatile__ ("cpuid"   : "=a" (varEAX), "=b" (varEBX), "=c" (varECX), "=d" (varEDX) : "a" (3));
//    sprintf(str, "%08X", varEDX); //i.e. xxxx-xxxx-XXXX-XXXX-xxxx-xxxx
//    sprintf(PSN, "%s-%C%C%C%C-%C%C%C%C", PSN, str[0], str[1], str[2], str[3], str[4], str[5], str[6], str[7]);
//    sprintf(str, "%08X", varECX); //i.e. xxxx-xxxx-xxxx-xxxx-XXXX-XXXX
//    sprintf(PSN, "%s-%C%C%C%C-%C%C%C%C", PSN, str[0], str[1], str[2], str[3], str[4], str[5], str[6], str[7]);

//    return PSN;
//}



// Function to convert last bytes of MAC address to hex format
void macToHex(const char *mac, char *hexBuffer) {
    // Find the last bytes (8 characters) in MAC address
    int len = strlen(mac);
    const char *lastBytes = mac + (len - 8);

    // Convert the last bytes from string to hex number
    unsigned int hexValue = strtoul(lastBytes, NULL, 16);

    // Format the output as "0x" followed by the hex value
    sprintf(hexBuffer, "0x%08X", hexValue);
}

void stringTo_16string(const char *src,char *des)
{
    int index=0;
    for(int i=0;i<strlen(src);i++)
    {
        if( (src[i] >= '0' && src[i] <= '9') || (src[i] >= 'a' && src[i] <= 'f') || (src[i] >= 'A' && src[i] <= 'F') )
        {
            des[index++] = src[i];
        }
        #if 0
        else if(src[i] >= 'g' && src[i] <= 'z')
        {
            des[index++] = src[i]-6>'f'? 'f': src[i]-6;
        }
        else if(src[i] >= 'G' && src[i] <= 'Z')
        {
           des[index++] = src[i]-6>'F'? 'F': src[i]-6;
        }
        #endif
    }
     des[index++] = '\0';
}

int stringTo_16string_num(const char *src,char *des)
{
    int index=0;
    for(int i=0;i<strlen(src);i++)
    {
        if( (src[i] >= '0' && src[i] <= '9') )
        {
            des[index++] = src[i];
        }
        #if 0
        else if(src[i] >= 'g' && src[i] <= 'z')
        {
            des[index++] = src[i]-6>'f'? 'f': src[i]-6;
        }
        else if(src[i] >= 'G' && src[i] <= 'Z')
        {
           des[index++] = src[i]-6>'F'? 'F': src[i]-6;
        }
        #endif
    }
     des[index++] = '\0';
     return index;
}

// Function to remove a trailing newline character from a string
void removeTrailingNewline(char* str)
{
    size_t len = strlen(str);
    if (len > 0 && str[len-1] == '\n')
        str[len-1] = '\0';
}

#if defined(Q_OS_WIN32)
unsigned long getHardDriveComputerID_OLD_WIN32()
{
    QProcess p;
    QString cmd = "wmic diskdrive where index=0 get serialnumber";
    p.start(cmd);
    p.waitForFinished();
    QString result = QString::fromLocal8Bit(p.readAllStandardOutput());
    QStringList list = cmd.split(" ");
    result = result.remove(list.last(), Qt::CaseInsensitive);
    result = result.replace("\r", "");
    result = result.replace("\n", "");
    result = result.simplified();
    QStringList list2 = result.split(" ");
    QString strId = list2[0];


    char buf[64]={0};
    stringTo_16string(strId.toStdString().data(),buf);

    string strSn(buf);

    //20230921 错误SN
    if(strSn == (char *)"3035323042363337323832343738383120202020" || strSn == (char *)"3035323042363337323832343638434320202020")
    {
        return 0;
    }

    if (strSn.length() > 8)
     {
         strSn = strSn.substr(strSn.size() - 8);
     }

    if(strSn[0] == '0')
    {
        strSn[0] = 'F';
    }

    char * endptr;
    unsigned long sn =  strtoul(strSn.c_str(), &endptr,16);
    printf("serialNumber=%s,sn=%ld\n",strId.toStdString().data(),sn);
    return sn;
}
#endif


unsigned long getHardDriveComputerID_NEW()
{
    unsigned long sn=0;
#if defined(Q_OS_LINUX)
    //查找根目录挂载点对应的块设备
    string cmd = "lsblk | awk '{ if($7==\"/\") {print ($1)} }' "; 
    FILE * fp=NULL;
    char pRetMsg[1024]={0};
    string deviceName="",devicePath="";
    if ((fp = popen(cmd.c_str(), "r") ) == NULL)
    {
        LOG("popen lsblk falied", LV_INFO);
        goto ERROR_END;
    }
    else
    {
        fgets(pRetMsg,1024, fp);
        if(pRetMsg[strlen(pRetMsg) - 1] == '\n')    //去掉换行符
            pRetMsg[strlen(pRetMsg) - 1] = '\0' ;
        string::size_type pos=0;
        deviceName=pRetMsg;

        if(deviceName.length() == 0)
        {
            pclose(fp);
            cmd = "lsblk | awk '{ if($7==\"/boot/efi\") {print ($1)} }' "; 
            if ((fp = popen(cmd.c_str(), "r") ) == NULL)
            {
                LOG("popen lsblk falied2", LV_INFO);
                goto ERROR_END;
            }
            else
            {
                fgets(pRetMsg,1024, fp);
                if(pRetMsg[strlen(pRetMsg) - 1] == '\n')    //去掉换行符
                    pRetMsg[strlen(pRetMsg) - 1] = '\0' ;
                deviceName=pRetMsg;
            }
        }
        
        if( (pos=deviceName.find("sd") )!=string::npos  )
        {
            deviceName=deviceName.substr(pos,3);
            //printf("deviceName=%s...\n",deviceName.data());
            devicePath="/dev/"+deviceName;
            printf("devicePath=%s...\n",devicePath.data());
        }
        else if( (pos=deviceName.find("nvme0n1") )!=string::npos )
        {
            deviceName=deviceName.substr(pos,7);
            devicePath="/dev/"+deviceName;
            printf("devicePath=%s...\n",devicePath.data());
        }
        else if( (pos=deviceName.find("klas-root") )!=string::npos )
        {
            //centos
            char mapperName[] = "klas-root";
            FILE* lsblkOutput;
            char command[512];
            char buffer[512];
            char deviceNameTemp[512];
            char parentDeviceName[512];

            snprintf(command, sizeof(command), "lsblk -no NAME,PKNAME -p -s -r -o NAME,PKNAME");

            lsblkOutput = popen(command, "r");
            if (lsblkOutput == NULL) {
                perror("popen");
                goto ERROR_END;
            }

            parentDeviceName[0] = '\0';

            while (fgets(buffer, sizeof(buffer), lsblkOutput)) {
                removeTrailingNewline(buffer);
                char* token = strtok(buffer, " ");
                if (token != NULL) {
                    strncpy(deviceNameTemp, token, sizeof(deviceNameTemp) - 1);
                    token = strtok(NULL, " ");
                    if (token != NULL) {
                        strncpy(parentDeviceName, token, sizeof(parentDeviceName) - 1);
                    }
                }
                if (strcmp(deviceNameTemp, mapperName) == 0) {
                    break;
                }
            }

            pclose(lsblkOutput);

            if (parentDeviceName[0] != '\0') {
                //printf("Disk: %s\n", parentDeviceName);
                devicePath=parentDeviceName;
                if( (pos=devicePath.find("/dev/sd") )!=string::npos  )
                {
                    devicePath=devicePath.substr(pos,8);
                    printf("devicePath_centos=%s...\n",devicePath.data());
                }
                else if( (pos=devicePath.find("/dev/nvme0n1") )!=string::npos )
                {
                    devicePath="/dev/nvme0n1";
                }
            } else {
                printf("Disk not found\n");
            }
        }
        else if( (pos=deviceName.find("uos-root") )!=string::npos )
        {
            //centos
            char mapperName[] = "uos-root";
            FILE* lsblkOutput;
            char command[512];
            char buffer[512];
            char deviceNameTemp[512];
            char parentDeviceName[512];

            snprintf(command, sizeof(command), "lsblk -no NAME,PKNAME -p -s -r -o NAME,PKNAME");

            lsblkOutput = popen(command, "r");
            if (lsblkOutput == NULL) {
                perror("popen");
                goto ERROR_END;
            }

            parentDeviceName[0] = '\0';

            while (fgets(buffer, sizeof(buffer), lsblkOutput)) {
                removeTrailingNewline(buffer);
                char* token = strtok(buffer, " ");
                if (token != NULL) {
                    strncpy(deviceNameTemp, token, sizeof(deviceNameTemp) - 1);
                    token = strtok(NULL, " ");
                    if (token != NULL) {
                        strncpy(parentDeviceName, token, sizeof(parentDeviceName) - 1);
                    }
                }
                if (strcmp(deviceNameTemp, mapperName) == 0) {
                    break;
                }
            }

            pclose(lsblkOutput);

            if (parentDeviceName[0] != '\0') {
                //printf("Disk: %s\n", parentDeviceName);
                devicePath=parentDeviceName;
                if( (pos=devicePath.find("/dev/sd") )!=string::npos  )
                {
                    devicePath=devicePath.substr(pos,8);
                    printf("devicePath_centos=%s...\n",devicePath.data());
                }
                else if( (pos=devicePath.find("/dev/nvme0n1") )!=string::npos )
                {
                    devicePath="/dev/nvme0n1";
                }
            } else {
                printf("Disk not found\n");
            }
        }
    }
    pclose(fp);
    fp=NULL;

    if( !devicePath.length() )
    {
        LOG("getHardDriveName falied", LV_INFO);
        goto ERROR_END;
    }

    if( (devicePath.find("/dev/sd") )!=string::npos )
    {
        char snbuf[1024] = {0};
        int fd;
        struct hd_driveid hid;
        fd = open (devicePath.data(), O_RDONLY|O_NONBLOCK);
        if (fd < 0)
        {
            LOG("getHardDriveComputerID open falied", LV_INFO);
            goto ERROR_END;
        }
        if (ioctl (fd, HDIO_GET_IDENTITY, &hid) < 0)
        {
            LOG("getHardDriveComputerID ioctl falied", LV_INFO);
            goto ERROR_END;
        }
        close (fd);
        sprintf(snbuf, "%s", hid.serial_no);

        char buf[64]={0};
        stringTo_16string(snbuf,buf);

        string strSn(buf);
        if (strSn.length() > 8)
        {
            strSn = strSn.substr(strSn.size() - 8);
        }
        char * endptr;
        sn =  strtoul(strSn.c_str(), &endptr,16);
    }
    else if( (devicePath.find("/dev/nvme0n1") )!=string::npos )
    {
        char querySNCmd[128]={0};
        sprintf(querySNCmd,"udevadm info --query=all --name=%s |grep -w ID_SERIAL_SHORT",devicePath.data());
        //printf("querySNCmd=%s\n",querySNCmd);
        if ((fp = popen(querySNCmd, "r") ) != NULL)
        {
            fgets(pRetMsg,1024, fp);
            if(pRetMsg[strlen(pRetMsg) - 1] == '\n')    //去掉换行符
                pRetMsg[strlen(pRetMsg) - 1] = '\0' ;

            //printf("nvmeSN1=%s\n",pRetMsg);

            pclose(fp);
            fp=NULL;

            //查找文件
            char *serialNum = strstr(pRetMsg,"ID_SERIAL_SHORT=");
            if(serialNum!=NULL)
            {
                serialNum+=strlen("ID_SERIAL_SHORT=");
                //printf("nvmeSN2=%s\n",serialNum);

                #if 0
                char kkkkkk[64]={0};
                sprintf(kkkkkk,"CS0AN95001100AU3S");
                //sprintf(kkkkkk,"S780100G00110S");

                char buf[64]={0};
                int numCnt=stringTo_16string_num(kkkkkk,buf);
                #else
                char buf[64]={0};
                int numCnt=stringTo_16string_num(serialNum,buf);
                #endif
                //printf("nvmeSN3=%s\n",buf);
                if(numCnt>1)
                {
                    string strSn(buf);
                    string strSnNew(buf);
                    #if 0
                    if (strSn.length() > 8)
                    {
                        strSnNew = strSn.substr(strSn.size() - 8);
                        if(strSnNew[0] == '0')
                        {
                            for(int i=strSn.size()-8-1;i>=0;i--)
                            {
                                if(strSn[i]!='0')
                                {
                                    strSnNew[0] = strSn[i];
                                    break;
                                }
                            }
                        }
                    }
                    printf("nvmeSN4=%s\n",strSnNew.c_str());
                    #endif
                    char * endptr;
                    sn =  (unsigned int)strtoul(strSnNew.c_str(), &endptr,10);
                    //printf("sn=0x%x\n",sn);
                }
            }
        }
    }
    //LOG(FORMAT("------------strSn = %s,sn = %x\n", snbuf,sn), LV_INFO);
#else
    QProcess p;
    QString cmd = "wmic diskdrive where index=0 get serialnumber";
    p.start(cmd);
    p.waitForFinished();
    QString result = QString::fromLocal8Bit(p.readAllStandardOutput());
    QStringList list = cmd.split(" ");
    result = result.remove(list.last(), Qt::CaseInsensitive);
    result = result.replace("\r", "");
    result = result.replace("\n", "");
    result = result.simplified();
    QStringList list2 = result.split(" ");
    QString strId = list2[0];


    char buf[64]={0};
    stringTo_16string(strId.toStdString().data(),buf);

    string strSn(buf);
    if (strSn.length() > 8 && strSn.length() < 16)
    {
        strSn = strSn.substr(strSn.size() - 8);
    }
    else if(strSn.length() >= 16)
    {
        string firstFiveChars = strSn.substr(strSn.size()-16, 5);
        string lastThreeChars = strSn.substr(strSn.size()-3);
        strSn = firstFiveChars + lastThreeChars;
    }

    if(strSn[0] == '0')
    {
        strSn[0] = 'F';
    }

    char * endptr;
    sn =  strtoul(strSn.c_str(), &endptr,16);
    //printf("serialNumber=%s,sn=%ld\n",strId.toStdString().data(),sn);
    if(sn == 0)
    {
        goto ERROR_END;
    }
#endif
    return sn;
ERROR_END:
    //获取错误，应该使用网卡MAC作为序列号
    string hostMac=CNetwork::GetHostMac();
    unsigned char	Mac[10]		= {0};	// 大小不能直接定义为MAC_LEN，否则调用sscanf_s会出错
    sscanf(hostMac.data(), "%x:%x:%x:%x:%x:%x", &Mac[0], &Mac[1], &Mac[2], &Mac[3], &Mac[4], &Mac[5]);
    // 将Mac[2], Mac[3], Mac[4], Mac[5]组合成32位的16进制数
    sn = ((Mac[1] + Mac[2]) << 24) + (Mac[3] << 16) + (Mac[4] << 8) + (Mac[0]+Mac[5]);
    return sn&0xFFFFFFFF;   //只比较低 32 位
}

// 获取CPUID
string get_cpu_id_by_asm()
{
#if defined(Q_OS_LINUX)
    string cpu_id;
    cpu_id.clear();

    unsigned int s1 = 0;
    unsigned int s2 = 0;
    asm volatile
    (
        "movl $0x01, %%eax; \n\t"
        "xorl %%edx, %%edx; \n\t"
        "cpuid; \n\t"
        "movl %%edx, %0; \n\t"
        "movl %%eax, %1; \n\t"
        : "=m"(s1), "=m"(s2)
    );

    if (0 == s1 && 0 == s2)
    {
        return cpu_id;
    }

    char cpu[32] = { 0 };
    snprintf(cpu, sizeof(cpu), "%08X%08X", htonl(s2), htonl(s1));
    string(cpu).swap(cpu_id);

    return  cpu_id;
#else
   return 0;
#endif
}


unsigned long GetCPUID()
{
    string strID = get_cpu_id_by_asm();
    cout << "cpuid :" << strID << endl;

    long long id = 0;
    for(int i = 0; i < strID.length(); i++)
    {
        id *= 10;
        switch (strID.at(i)) {
        case '0': id += 0; break;
        case '1': id += 1; break;
        case '2': id += 2; break;
        case '3': id += 3; break;
        case '4': id += 4; break;
        case '5': id += 5; break;
        case '6': id += 6; break;
        case '7': id += 7; break;
        case '8': id += 8; break;
        case '9': id += 9; break;
        case 'a': case 'A': id += 10; break;
        case 'b': case 'B': id += 11; break;
        case 'c': case 'C': id += 12; break;
        case 'd': case 'D': id += 13; break;
        case 'e': case 'E': id += 14; break;
        case 'f': case 'F': id += 15; break;
        case 'g': case 'G': id += 16; break;
        case 'h': case 'H': id += 17; break;
        case 'i': case 'I': id += 18; break;
        case 'j': case 'J': id += 19; break;
        case 'k': case 'K': id += 20; break;
        case 'l': case 'L': id += 21; break;
        case 'm': case 'M': id += 22; break;
        case 'n': case 'N': id += 23; break;
        case 'o': case 'O': id += 24; break;
        case 'p': case 'P': id += 25; break;
        case 'q': case 'Q': id += 26; break;
        case 'r': case 'R': id += 27; break;
        case 's': case 'S': id += 28; break;
        case 't': case 'T': id += 29; break;
        case 'u': case 'U': id += 30; break;
        case 'v': case 'V': id += 31; break;
        case 'w': case 'W': id += 32; break;
        case 'x': case 'X': id += 33; break;
        case 'y': case 'Y': id += 34; break;
        case 'z': case 'Z': id += 35; break;
        default:
            break;
        }
    }

    id %= 100000000;

    return id;
}
