#include "stdafx.h"
#include "SBENetwork.h"
#include "Tools/ComputerInfo.h"
#if defined(Q_OS_LINUX)
#include <execinfo.h>
#endif
#include "Tools/Control/CRegister.h"

#if defined(Q_OS_LINUX)
// 函数：查找并修改文件
int modify_chrome_desktop(const char *filepath) {
    FILE *fp;
    char cmd[1024];
    
    // 创建临时文件
    char temp_file[] = "/tmp/chrome_desktop_XXXXXX";
    int temp_fd = mkstemp(temp_file);
    if (temp_fd == -1) {
        printf("创建临时文件失败\n");
        return 1;
    }
    close(temp_fd);
    
    // 打开原文件和临时文件
    FILE *input = fopen(filepath, "r");
    FILE *output = fopen(temp_file, "w");
    if (!input || !output) {
        printf("打开文件失败\n");
        return 1;
    }
    
    char line[1024];
    int modified = 0;
    
    // 逐行读取并修改
    while (fgets(line, sizeof(line), input)) {
        if (strstr(line, "http://localhost:9999") && !modified) {
                        // 检查该行是否已经包含了参数
            if (strstr(line, "--simulate-outdated-no-au") == NULL) {
                // 移除换行符
                line[strcspn(line, "\n")] = 0;
                fprintf(output, "%s --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT'\n", line);
                modified = 1;
                //printf("找到目标行并修改\n");
            } else {
                // 如果已经包含参数，直接写入原行
                fputs(line, output);
                //printf("目标行已包含参数，无需修改\n");
            }
        } else {
            fputs(line, output);
        }
    }
    
    fclose(input);
    fclose(output);
    
    // 如果文件被修改，替换原文件
    if (modified) {
        if (chmod(temp_file, 0777) == -1) {
            perror("设置文件权限失败");
            close(temp_fd);
            return 1;
        }
        snprintf(cmd, sizeof(cmd), "mv %s %s", temp_file, filepath);
        system(cmd);
        //printf("文件已成功修改：%s\n", filepath);
        return 0;
    } else {
        //printf("未找到匹配的行进行修改\n");
        remove(temp_file);
        return 1;
    }
}

// 函数：递归查找/home目录下的.desktop文件
void search_chrome_desktop_and_mod(const char *dir_path,const char *fileName) {
    struct dirent *entry;
    DIR *dir = opendir(dir_path);
    if (!dir) {
        perror("打开目录失败");
        return;
    }

    while ((entry = readdir(dir))) {
        if (entry->d_type == DT_DIR) {
            // 忽略 . 和 .. 目录
            if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
                continue;
            }
            // 递归查找子目录
            char subdir[1024];
            snprintf(subdir, sizeof(subdir), "%s/%s", dir_path, entry->d_name);
            search_chrome_desktop_and_mod(subdir,fileName);
        } else if (entry->d_type == DT_REG) {
            if (strstr(entry->d_name, fileName)) {
                char file_path[1024];
                snprintf(file_path, sizeof(file_path), "%s/%s", dir_path, entry->d_name);
                //printf("检查文件: %s\n", file_path);
                // 如果文件存在并且修改成功
                if (modify_chrome_desktop(file_path) == 0) {
                    printf("file changed: %s\n", file_path);
                }
            }
        }
    }

    closedir(dir);
}
#endif

bool SBE_StartWorking(SBE_CALLBACK *pCallBack, void *pDefined)
{
    LOG(FORMAT("\n==============  Version %s  ================\n", VERSION), LV_DEADLY);

    while( strcmp(CNetwork::GetHostIP(),"") == 0 )  //没有获取到IP地址，循环等待(适配DHCP没有插入网线的情况)
    {
        printf("Network failed,retry!\n");
        sleep(1);
    }

#if defined(Q_OS_LINUX)
    CNetworkTool::GetValidNetCardInfo(&g_Global.m_szNetCard);
    printf("NetCard:ifr=%s,ip:%s\n",g_Global.m_szNetCard.ifaName,g_Global.m_szNetCard.ifaAddr);

    //anydesk重置删除，避免clone后id重复
    if( IsExistFile("/etc/anydesk/service.conf") )
    {
         if( !IsExistFile("/etc/anydesk/hasChanged") )
         {
             system("rm /etc/anydesk/service.conf");
             system("touch /etc/anydesk/hasChanged");
             usleep(500000);
             system("reboot");
         }
    }

    //杀死ffmpeg進程
    if(IsProcessRun((char*)"ffmpeg"))
    {
        CMyString scriptPath;
        scriptPath.Format("%s/%s", (char *)SCRIPT_PATH, (char *)SCRIPT_RTSP);
        if(IsExistFile(scriptPath.C_Str()))
        {
            char *strdup_char1=strdup(std::to_string(2).data());
            char *strdup_char2=strdup("");
            char* param[] = { (char *)SCRIPT_RTSP, strdup_char1, strdup_char2, NULL};
            ExecuteShell(scriptPath.C_Str(), param);
            free(strdup_char1);
            free(strdup_char2);
        }
    }

    search_chrome_desktop_and_mod("/home","google-chrome.desktop");
    search_chrome_desktop_and_mod("/home","Chrome.desktop");
#endif

#if CANCEL_WEB_DEFAULT_PLACE_ADMIN
    Cancel_Web_Default_Admin_Login();
#endif

#if DISABLE_WEB_INDEXES_DIRECTORY_ACCESS
    Disable_Web_indexes_access();
#endif

#if SUPPORT_SERVER_SYNC
    g_Global.m_serverSync.StopLsyncdWorking();
#endif

    LOG(FORMAT("\n==============  HostIP: %s  ================\n", CNetwork::GetHostIP()), LV_DEADLY);

    // 初始化全局数据
    if (!g_Global.InitData(CRegister::HasRegister()))
    //if (!g_Global.InitData(true))
    {
        LOG("create dir error", LV_ERROR);
        return false;
    }

#if defined(Q_OS_LINUX)

    //如果是云端版本或者没有启用监控，那么关闭监控服务
    if( IS_SYSTEM_IN_CLOUD || !g_Global.m_bEnableMonitor)
    {
        StopProcess((char*)"MonInit",true);
        StopProcess((char*)"CamIPCSDK");
        StopProcess((char*)"node");
    }

    //判断命令是否正常
    if(IsCommandValid((char*)"nmcli"))
    {
        g_Global.m_bNmcliValid = true;    
    }

#endif


    #if APP_IS_AISP_TZY_ENCRYPTION
    g_Global.m_PasswordMod.initialize();
    #endif

    //读取本地文件信息
    g_Global.ReadLocalFileInfo();

    //20231007
    //启动zone xml更新线程
    g_Global.m_Sections.StartSectionXmlUpgradeThread();
    //初始化用户分区设备（一定要在用户初始化完成后调用）
    g_Global.m_Users.InitUserSection();

    //初始化播放列表
    g_Global.InitPlaylist(FALSE);

    g_Global.m_PlayList.StartBitRateChangeTHD();

    // 启动网络检测线程
    g_Global.StartInternetCheckThread();

#if 0
    // 写入所需默认的配置信息
    g_Global.InitDefaultConfig();
#endif
#if defined(Q_OS_LINUX)
    // 设置文件权限
    MyChmod();

    // 信号捕捉
    BeforeExit();
#endif
    // 网络开始工作
    return (g_Global.m_Network.StartWorking() ? true : false);
}


// 设置选中分区音量
bool SBE_SetVolume(byte nVolume, unsigned int *pZoneIndex, unsigned int uZoneCount)
{
    if (uZoneCount == 0)
    {
        return false;
    }

    g_Global.m_Network.m_CmdSend.CmdSetVolume(nVolume, pZoneIndex, uZoneCount);

    return true;
}


// 停止工作，释放资源
bool SBE_StopWorking()
{
    g_Global.m_Network.ExitWorking();
    return true;
}

#if 0
// 新建歌曲列表
int SBE_AddSongList(string strListName)
{
    int nListCount = g_Global.m_PlayList.GetListCount();
    int nMaxListCount = MAX_PLAY_LIST_COUNT;
    int uListIndex;
    CMyString cstrListName;
    cstrListName = StringToCString(strListName);

    if(nListCount >= nMaxListCount)                                  // 判断是否超过歌曲列表数量限制
    {
        return 1;
    }

    else if(g_Global.m_PlayList.FindListByName(cstrListName) >= 0 )  // 判断是否有重名
    {
        return 2;
    }
    else if(strListName.length() == 0)
    {
        return 3;
    }
    else if(strListName.length() > LIST_NAME_LEN)
    {
        return 4;
    }
    else
    {
        // 加入内存
        g_Global.m_PlayList.AddList(cstrListName);

        // 写入文件
        uListIndex = g_Global.m_PlayList.FindListByName(cstrListName);
        g_Global.m_PlayList.SetListDateTime(uListIndex, GetCurrentDateTime());
        g_Global.m_PlayList.WriteFile();


        return 0;
    }
}
#endif

// 往指定歌曲列表添加歌曲
int SBE_AddSongsToList(unsigned int uListIndex, vector<string> &vecSongPathName)
{
    int ret = g_Global.m_fileManager.CopySongFiles(&g_Global.m_PlayList, uListIndex, vecSongPathName);

    // 如果添加成功
    if (ret == 0)
    {
        // 要重新赋值一遍，因为有些歌曲是重复的，不能单纯地把所有歌曲加在末尾
        //InitPlayListData();
    }

    LOG(FORMAT("ret = %d\n", ret), LV_INFO);
    return ret;

}


// 选中分区播放节目源
bool SBE_PlaySource(unsigned int uListIndex, unsigned int uSongIndex, unsigned int *pZoneIndex, unsigned int uZoneCount)
{
    bool isValidSong = g_Global.m_PlayList.ConfirmListSong(uListIndex, uSongIndex);

    if (uZoneCount == 0 || !isValidSong)
    {
        return false;
    }

    // 集中模式
    if (WP_IS_CENTRALIZED)
    {
        CMyString strSongPathName = g_Global.m_strHttpRootDir + g_Global.m_PlayList.GetListSongPathName(uListIndex, uSongIndex);

        //puts(strSongPathName.C_Str());
        // 点播歌曲加入播放队列
        CPlayTask playTask(SOURCE_PLAY, {strSongPathName}, pZoneIndex, uZoneCount,SUPER_USER_NAME,0, uListIndex, uSongIndex);
        if(g_Global.m_PlayQueue.PushPlayTask(playTask))
        {
            LOG("play start", LV_INFO);
        }
    }
    // 分布模式
    else
    {
        g_Global.m_Network.m_CmdSend.CmdPlayLocalSong(uListIndex, uSongIndex, &g_Global.m_PlayList, pZoneIndex, uZoneCount);
    }

    return true;

}



// 新建分组
int	    SBE_AddGroup(CMyString strGroupName)
{
#if defined(Q_OS_LINUX)
    CMyString strGBGroupName = ConvertUTF8toGB2312((char*)strGroupName.C_Str(), strlen(strGroupName.C_Str()));

    int nGroupCount = g_Global.m_Groups.GetGroupCount();

    LOG(FORMAT("分组数量 = %d\n", nGroupCount), LV_INFO);
    if( nGroupCount >= MAX_GROUP_COUNT)    //判断分组数量是否超过最大限制
    {
        return 1;
    }
    else
    {
        LPCSTR  lpszName	=  strGBGroupName.C_Str();
        int		nNameLen	=  strlen(lpszName);
        BOOL	bExistName	=  FALSE;

        bExistName = g_Global.m_Groups.FindGroupByName(lpszName);

        if(nNameLen == 0)             // 判断分组名是否为空
        {
            LOG("长度为空", LV_INFO);
            return 2;
        }
        else if((nNameLen > GROUP_NAME_LEN))    // 判断分组名是否超过长度限制
        {
            LOG("超过长度限制", LV_INFO);
            return 3;
        }
        else if (bExistName)                    // 判断分组名是否已存在
        {
            LOG("名称已存在", LV_INFO);
            return 4;
        }
        else
        {
            CGroup group(GetGUID(), lpszName);
            g_Global.m_Groups.AddGroup(group);
            g_Global.m_Groups.WriteGroupFile(HTTP_FILE_GROUP);

            return 0;
        }
    }
#endif
}


// 设置歌曲播放模式（单曲播放0x01，单曲循环0x02，顺序播放0x03，列表循环0x04，随机播放0x05）（集中模式下）
bool	     SBE_SetPlayMode(int nPlayMode)
{
    if(nPlayMode <= 0 || nPlayMode > 5)
    {
        return false;
    }

    // 播放模式不一样
    if (g_Global.m_PlayList.GetPlayMode() != (PlayMode)nPlayMode)
    {
        g_Global.m_PlayList.SetPlayMode((PlayMode)nPlayMode);
        g_Global.m_IniConfig.SetValue(CONFIG_FILE_SECTION_SECTION, CONFIG_FILE_ITEM_PLAY_MODE, nPlayMode);
        g_Global.m_IniConfig.Write();

        // 集中模式
        if (WP_IS_CENTRALIZED)
        {
            g_Global.m_Network.DeviceChangePlayMode((PlayMode)nPlayMode);

            return true;
        }
        else
        {
            return false;
        }
        /*
        // 分布模式
        else
        {
            // 对选中分区设置播放模式
            g_Global.m_Network.m_CmdSend.CmdSetPlayMode(Mode);
        }
        */
    }

    return true;
}




// AUX
bool SBE_AUXPlay()
{
#if defined(Q_OS_LINUX)
    LPCSection pAudioSection = g_Global.m_AudioCollectors.GetSectionByMac(CNetwork::GetHostMac());
    if(pAudioSection == NULL)
    {
        printf("pAudioSection is NULL\n");
        return false;
    }

    // 获取到分区ID
    unsigned int uCount = 0;
    int nSecCount = g_Global.m_Sections.GetSecCount();
    unsigned int pSecIndexs[MAX_SECTION_COUNT_FORMAL] = {0};
    for (unsigned int i=0; i<nSecCount; ++i)
    {
        CSection& pSection = g_Global.m_Sections.GetSection(i);

        if (pSection.IsOnline())
        {
            pSecIndexs[uCount++] = pSection.GetID() - 1;
        }

        if(uCount >= MAX_SECTION_COUNT_FORMAL)
            break;
    }

    printf("uCount : %d\n", uCount);
    // 发送给选中分区
    //g_Global.m_Network.m_CmdSend.CmdSetAudioInfo(pAudioSection->m_pAudioCollector, pSecIndexs, uCount);

    g_Global.m_AuxPlay.StartAUXWorking();
#endif
    return true;
}


bool SBE_BW()
{
    // 获取到分区ID
    int nSecCount = g_Global.m_Sections.GetSecCount();
    for (unsigned int i=0; i<nSecCount; ++i)
    {
        CSection& device = g_Global.m_Sections.GetSection(i);

        if (device.IsOnline())
        {
            //pSecIndexs[uCount++] = pSection.GetID() - 1;
            NOTIFY("IP : %s", device.GetIP());
			g_Global.m_Network.m_CmdSend.CmdQueryNetQuality(device, 0x00, g_Global.m_szNetworkIP, 48880);
            g_Global.m_Network.m_CmdSend.CmdQueryNetQuality(device, 0x01, g_Global.m_szNetworkIP, 49880);
            g_Global.m_Network.m_CmdSend.CmdQueryNetQuality(device, 0x02, MULTICAST_SEND_IP, MULTICAST_SEND_PORT);
        }
    }
    return true;
}

char* GetTransferChara1(char* szData, int nLen)
{
    char* szTrans = (char*)malloc(nLen*2);
    memset(szTrans, 0, nLen*2);
    int n = 0;
    for(int i=0; i<nLen; i++)
    {
        if((szData[i] >= 0 && szData[i] <= 47) ||
           (szData[i] >= 58 && szData[i] <= 64) ||
           (szData[i] >= 91 && szData[i] <= 96) ||
           (szData[i] >= 123 && szData[i] <= 126))
        {
            if(szData[i] != ':' && szData[i] != '/')
            {
                char szHex[4] = {0};
                sprintf(szHex, "%%%x", szData[i]);
                strcpy(&szTrans[n], szHex);
                n+=3;
                printf("%s", szHex);
            }
            else
            {
                szTrans[n++] = szData[i];
            }
        }
        else
        {
            szTrans[n++] = szData[i];
        }
    }

    return szTrans;
}

// download file
void SBE_CURLDownload()
{
#if defined(Q_OS_LINUX)
    CCurlDownload curl;

    //char* szURL = "http://***************:65501/Data/Program/Other/0.mp3";
    char* szFileName = (char *)"Playlist.xml";
    char szURL[1024] = {0};
    char* szAscll = GetTransferChara1(szFileName, strlen(szFileName));
    sprintf(szURL, "http://***************:65501/Data/Xml/%s", szAscll);

    char szFilePath[1024] = {0};
    sprintf(szFilePath, "%s/%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_XML, HTTP_FILE_PLAYLIST_HOST);
    curl.StartDownloadFile(szURL, szFilePath);
    //g_Global.m_FileSync.SyncFile(FILE_PLAYLIST, szURL);

    //StartDownloadFile1(szURL, szFilePath);
    free(szAscll);
#endif
}



void SBEGetCPUID()
{

}


// register
void SBERegisterServer(string strRegisterNum)
{
    printf("\n******************************\n");
    //unsigned long nRegisterNum = std::stoi(strRegisterNum, 0, 16);
    //printf("num1 : %ld\n", nRegisterNum);

    printf("strCode : %s\n", strRegisterNum.data());
    CMyString strCode;
    if(strRegisterNum.length() == 16)
    {
        strCode.Format(("%s%s%s"), strRegisterNum.substr(0, 2).data(), strRegisterNum.substr(6, 4).data(), strRegisterNum.substr(14, 2).data());
        strRegisterNum = strCode.C_Str();
    }

    printf("strCode : %s\n", strRegisterNum.data());
    unsigned long uRegisterNum = strtoul(strRegisterNum.data(), NULL, 16);
    printf("num2 : %ld\n", uRegisterNum);

    uRegisterNum ^= SECRET_KEY2;
    uRegisterNum ^= SECRET_KEY1;

    printf("register num : %lu, %x\n", uRegisterNum, uRegisterNum);
}


void    myExit()
{
    CMyString strLog;
    strLog.Format("Networking exit");
    g_Global.m_Network.AddLog(strLog);
    exit(0);
}

void    signalHandler(int signal)
{
#if defined(Q_OS_LINUX)
    CMyString strLog;

    switch(signal)
    {
    case SIGSEGV: strLog.Format("Catch signal: SIGSEGV - %d", signal);  break;
    case SIGILL:  strLog.Format("Catch signal: SIGILL - %d", signal);   break;
    case SIGFPE:  strLog.Format("Catch signal: SIGFPE - %d", signal);   break;
    case SIGABRT: strLog.Format("Catch signal: SIGABRT - %d", signal);  break;
    case SIGTERM: strLog.Format("Catch signal: SIGTERM - %d", signal);  break;
    case SIGKILL: strLog.Format("Catch signal: SIGKILL - %d", signal);  break;
    case SIGXFSZ: strLog.Format("Catch signal: SIGXFSZ - %d", signal);  break;
    default:      strLog.Format("Catch signal: unkown %d", signal);     break;
    }

    // 获取当前时间
    CTime time = CTime::GetCurrentTimeT();

    // 文件名
    char fileName[256] = {0};
    sprintf(fileName, "core.%d-%d-%d_%d_%d_%d", time.GetYear(), time.GetMonth(), time.GetDay(),
            time.GetHour(), time.GetMinute(), time.GetDay());

    FILE* file = fopen(fileName, "a");
    if(file == NULL)
    {
        return;
    }

    //输出程序的绝对路径
    char buffer[4096];
    memset(buffer, 0, sizeof(buffer));
    int count = readlink("/proc/self/exe", buffer, sizeof(buffer));
    if(count > 0)
    {
        buffer[count] = '\n';
        buffer[count + 1] = 0;
        fwrite(buffer, 1, count+1, file);
    }

    //输出信息的时间
    memset(buffer, 0, sizeof(buffer));
    sprintf(buffer, "Dump Time: %d-%d-%d %d:%d:%d\n", time.GetYear(), time.GetMonth(), time.GetDay(),
            time.GetHour(), time.GetMinute(), time.GetDay());
    fwrite(buffer, 1, strlen(buffer), file);

    //线程和信号
    sprintf(buffer, "Curr thread: %u, Catch signal:%d\n", (int)pthread_self(), signal);
    fwrite(buffer, 1, strlen(buffer), file);


    void* DumpArray[256];
    int nSize = backtrace(DumpArray, 256);
    char** symbols = backtrace_symbols(DumpArray, nSize);
    if (symbols)
    {
        if (nSize > 256)
        {
            nSize = 256;
        }
        if (nSize > 0)
        {
            for (int i = 0; i < nSize; i++)
            {
                fwrite(symbols[i], 1, strlen(symbols[i]), file);
                //printf("symbols[%d] : %s", i, symbols[i]);
                fwrite("\n", 1, 1, file);
                //puts("");
            }
        }
        free(symbols);
    }

    g_Global.m_Network.AddLog(strLog);
    myExit();
#endif
}

// 退出程序前操作
void    BeforeExit()
{
//    signal(SIGSEGV, signalHandler);
//    signal(SIGILL,  signalHandler);
//    signal(SIGFPE,  signalHandler);
//    signal(SIGABRT, signalHandler);
//    signal(SIGTERM, signalHandler);
//    signal(SIGKILL, signalHandler);
//    signal(SIGXFSZ, signalHandler);

//    atexit(myExit);
}




