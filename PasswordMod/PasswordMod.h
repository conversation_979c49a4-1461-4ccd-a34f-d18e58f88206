#ifndef PASSWORD_MOD_H
#define PASSWORD_MOD_H

#include <string>
#include "Tools/cJSON.h"
#include <QNetworkAccessManager>
#include <QObject>
#include <QTimer>
using namespace std;


#define CONNECT_CIPHER_ERROR_HMAC "A8MY9rcFAK2FAoCyM91nfEbQsmhE0TY76eC1leDBr00="   //与密码机通讯失败时的HMAC值，用于匹对

class CPasswordMod : public QObject
{
    Q_OBJECT

    public:
        explicit CPasswordMod(QObject *parent = NULL);
        
        ~CPasswordMod();
        void initialize();

        void setAccessToken(string token) {m_strAccessToken = token;}
        string getAccessToken() {return m_strAccessToken;}

        string sendEncryptRequest(string inputOriData);
        string sendDecryptRequest(string inputBase64Data);
        string sendHmacRequest(string inputData);
        bool sendEccVerifySignByCert(string inputOriValue,string inputSignValue,string certificate);

        static string GetDerEncodedSignatureBase64(string inputSignvalue);

    private slots:
        void sendTokenRequest();
        #if 0
        void onFinished(QNetworkReply *reply);
        #endif


    private:
        //QNetworkAccessManager manager;
        QTimer timer;

        string m_strAccessToken;
};




#endif