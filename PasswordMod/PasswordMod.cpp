#include <QCoreApplication>
#include <QNetworkReply>
#include <QNetworkRequest>
#include <QEventLoop>
#include <QPointer>
#include <QTimer>
#include <QDebug>
#include "stdafx.h"
#include "Tools/base64.h"
#include "PasswordMod.h"

#define CIPHER_DEVICE_IP    "**************"
#define CIPHER_DEVICE_PORT  "8866"

#define CIPHER_API_TOKEN    "/ccsp/auth/app/v1/token"
#define CIPHER_API_ENCRYPT  "/pki/api/v6/encrypt/internal/symmetric"
#define CIPHER_API_DECRYPT  "/pki/api/v6/decrypt/internal/symmetric"
#define CIPHER_API_HMAC     "/pki/api/v6/hmac/internal/symmetric"
#define CIPHER_API_ECCVerifySignByCert  "/svs/api/v6/external/sm2/eccVerifySignByCert"

#define CIPHER_HEADER_Authorization_Token           "X-SW-Authorization-Token"
#define CIPHER_HEADER_Authorization_TenantCode      "X-SW-Authorization-TenantCode"
#define CIPHER_HEADER_Authorization_AppCode         "X-SW-Authorization-AppCode"

#define CIPHER_VALUE_Authorization_TenantCode       "ccsp_tenant"
#define CIPHER_VALUE_Authorization_AppCode          "app1"

CPasswordMod::CPasswordMod(QObject *parent) : QObject(parent)
{
 
}

CPasswordMod::~CPasswordMod()
{
    // Constructor is empty now
}

void CPasswordMod::initialize()
{
    //connect(&manager, &QNetworkAccessManager::finished, this, &CPasswordMod::onFinished);
    timer.setInterval(5 * 60 * 1000); // 5 minutes in milliseconds
    connect(&timer, &QTimer::timeout, this, &CPasswordMod::sendTokenRequest);
    timer.start(); // Start the timer for periodic requests
    sendTokenRequest();
}

void CPasswordMod::sendTokenRequest()
{
    printf("sendTokenRequest...\n");
    QString urlString = QString("https://%1:%2%3")
                    .arg(CIPHER_DEVICE_IP)
                    .arg(CIPHER_DEVICE_PORT)
                    .arg(CIPHER_API_TOKEN);
    QUrl url(urlString);
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QSslConfiguration config = QSslConfiguration::defaultConfiguration();
    config.setProtocol(QSsl::AnyProtocol);
    config.setPeerVerifyMode(QSslSocket::VerifyNone);
    request.setSslConfiguration(config);

    // Create JSON using cJSON
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "username", "app1");
    cJSON_AddStringToObject(root, "password", "swxa@1234");

    char *jsonData = cJSON_Print(root);
    QByteArray data(jsonData);
    cJSON_Delete(root);
    free(jsonData);

    QNetworkAccessManager manager;
    QNetworkReply *reply = manager.post(request, data);
    reply->setProperty("requestType", "token"); // Set custom property for request type

    QEventLoop localLoop;

    // 当网络请求完成时，退出事件循环
    connect(reply, &QNetworkReply::finished, &localLoop, &QEventLoop::quit);

    // Use QPointer to safely handle the QNetworkReply object
    QPointer<QNetworkReply> safeReply(reply);
    
    // 设置一个 2 秒的超时，如果超时则中止请求并退出事件循环
    QTimer *timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, [&]() {
        if (safeReply && safeReply->isRunning()) {
            qDebug() << "请求超时";
            safeReply->abort();
            localLoop.quit();
        }
        delete(timer);
        timer=NULL;
    });
    timer->setSingleShot(true);
    timer->start(1000);

    string strAccessToken;
    // 开始事件循环，阻塞直到请求完成或超时
    localLoop.exec();
    if(timer)
    {
        delete(timer);
    }
    if (reply->error() == QNetworkReply::NoError) {
        QByteArray response = reply->readAll();
        cJSON *jsonResponse = cJSON_Parse(response.data());

        if (jsonResponse) {

            char *jsonFormat = cJSON_Print(jsonResponse);
            //qDebug() << "token operation succeed,response:" << jsonFormat;
            free(jsonFormat);

            if(1)
            {
                cJSON *success = cJSON_GetObjectItem(jsonResponse, "success");
                if (cJSON_IsBool(success) && cJSON_IsTrue(success)) {

                    // 获取 "data" 字段的值
                    cJSON *data = cJSON_GetObjectItem(jsonResponse, "data");
                    if (data) {
                        // 获取 "accessToken" 字段的值
                        cJSON *accessToken = cJSON_GetObjectItem(data, "accessToken");
                        if (accessToken) {
                            // 输出 "accessToken" 字段的值
                            //printf("accessToken: %s\n", accessToken->valuestring);
                            strAccessToken = accessToken->valuestring;
                            //this->setAccessToken(accessToken->valuestring);
                            //g_Global.m_PasswordMod.sendEncryptRequest("admin");
                        } else {
                            printf("accessToken not found.\n");
                        }
                    } else {
                        printf("Data not found.\n");
                    }

                } else {
                    qDebug() << "token operation failed, response:" << response;
                }
            }
            cJSON_Delete(jsonResponse);
        } else {
            qDebug() << "Failed to parse JSON response";
        }
    } else {
        qDebug() << "Error:" << reply->errorString();
        //如果是abort主动停止，那么打印"Error: "Operation canceled"
    }

    setAccessToken(strAccessToken);

    reply->deleteLater();

    printf("sendTokenRequest exit!\n");
}


string CPasswordMod::sendEncryptRequest(string inputOriData)
{
    string resultEncrypt;
    if(getAccessToken().empty())
    {
        return resultEncrypt;
    }
    printf("sendEncryptRequest...\n");
    QString urlString = QString("https://%1:%2%3")
                .arg(CIPHER_DEVICE_IP)
                .arg(CIPHER_DEVICE_PORT)
                .arg(CIPHER_API_ENCRYPT);
    //printf("UrlString=%s\n",urlString.toStdString().data());
    QUrl url(urlString);
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    
    // 添加自定义头部
    request.setRawHeader(QByteArray::fromStdString(CIPHER_HEADER_Authorization_Token), QByteArray::fromStdString(getAccessToken()));
    request.setRawHeader(QByteArray::fromStdString(CIPHER_HEADER_Authorization_TenantCode), QByteArray::fromStdString(CIPHER_VALUE_Authorization_TenantCode));
    request.setRawHeader(QByteArray::fromStdString(CIPHER_HEADER_Authorization_AppCode), QByteArray::fromStdString(CIPHER_VALUE_Authorization_AppCode));

#if 0
        // 打印请求头部，检查是否正确
    qDebug() << "Request Headers:";
    foreach (QByteArray header, request.rawHeaderList()) {
        qDebug() << header << ":" << request.rawHeader(header);
    }
#endif

    QSslConfiguration config = QSslConfiguration::defaultConfiguration();
    config.setProtocol(QSsl::AnyProtocol);
    config.setPeerVerifyMode(QSslSocket::VerifyNone);
    request.setSslConfiguration(config);


    //将原文转换成base64编码
    string base64_inData = base64_encode((unsigned char*)inputOriData.data(),inputOriData.length());

    // Create JSON using cJSON
    cJSON *root = cJSON_CreateObject();

    cJSON_AddStringToObject(root, "keyName", "sm4");
    cJSON_AddStringToObject(root, "algType", "SGD_SM4_ECB");
    cJSON_AddStringToObject(root, "iv", "");
    cJSON_AddStringToObject(root, "inData", base64_inData.data());
    cJSON_AddStringToObject(root, "paddingType", "PKCS7PADDING");

    char *jsonData = cJSON_Print(root);
    QByteArray data(jsonData);
    cJSON_Delete(root);
    free(jsonData);

    QNetworkAccessManager manager;
    QNetworkReply *reply = manager.post(request, data);
    reply->setProperty("requestType", "encrypt"); // Set custom property for request type
    reply->setProperty("encrypt_ori_data", inputOriData.data()); // Set custom property for request type



    QEventLoop localLoop;

    // 当网络请求完成时，退出事件循环
    connect(reply, &QNetworkReply::finished, &localLoop, &QEventLoop::quit);

    // Use QPointer to safely handle the QNetworkReply object
    QPointer<QNetworkReply> safeReply(reply);
    
    // 设置一个 2 秒的超时，如果超时则中止请求并退出事件循环
    QTimer *timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, [&]() {
        if (safeReply && safeReply->isRunning()) {
            qDebug() << "请求超时";
            safeReply->abort();
            localLoop.quit();
        }
        delete(timer);
        timer=NULL;
    });
    timer->setSingleShot(true);
    timer->start(1000);

    // 开始事件循环，阻塞直到请求完成或超时
    localLoop.exec();
    if(timer)
    {
        delete(timer);
    }
    if (reply->error() == QNetworkReply::NoError) {
        QByteArray response = reply->readAll();
        cJSON *jsonResponse = cJSON_Parse(response.data());

        if (jsonResponse) {

            char *jsonFormat = cJSON_Print(jsonResponse);
            //qDebug() << "sendEncryptRequest operation succeed,response:" << jsonFormat;
            free(jsonFormat);

            if(1)
            {
                cJSON *message = cJSON_GetObjectItem(jsonResponse, "message");
                if(message && string(message->valuestring) == "success")
                {
                    // 获取 "result" 字段的值
                    cJSON *result = cJSON_GetObjectItem(jsonResponse, "result");
                    if (result) {
                        // 获取 "outData" 字段的值
                        cJSON *outData = cJSON_GetObjectItem(result, "outData");
                        if (outData) {
                            // 输出 "outData" 字段的值
                            //printf("outData=%s\n", outData->valuestring);
                            resultEncrypt = outData->valuestring;
                            #if 0
                            g_Global.m_PasswordMod.sendDecryptRequest(outData->valuestring);
                            #endif
                        } else {
                            printf("outData not found.\n");
                        }
                    } else {
                        printf("Data not found.\n");
                    }
                }
            }
            cJSON_Delete(jsonResponse);
        } else {
            qDebug() << "Failed to parse JSON response";
        }
    } else {
        qDebug() << "Error:" << reply->errorString();
    }

    reply->deleteLater();

    printf("sendEncryptRequest exit!\n");
    return resultEncrypt;
}


string CPasswordMod::sendDecryptRequest(string inputBase64Data)
{
    string resultDecrypt;
    if(getAccessToken().empty())
    {
        return resultDecrypt;
    }
    printf("sendDecryptRequest...\n");
    QString urlString = QString("https://%1:%2%3")
                .arg(CIPHER_DEVICE_IP)
                .arg(CIPHER_DEVICE_PORT)
                .arg(CIPHER_API_DECRYPT);
    //printf("UrlString=%s\n",urlString.toStdString().data());
    QUrl url(urlString);
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    // 添加自定义头部
    request.setRawHeader(QByteArray::fromStdString(CIPHER_HEADER_Authorization_Token), QByteArray::fromStdString(getAccessToken()));
    request.setRawHeader(QByteArray::fromStdString(CIPHER_HEADER_Authorization_TenantCode), QByteArray::fromStdString(CIPHER_VALUE_Authorization_TenantCode));
    request.setRawHeader(QByteArray::fromStdString(CIPHER_HEADER_Authorization_AppCode), QByteArray::fromStdString(CIPHER_VALUE_Authorization_AppCode));

    QSslConfiguration config = QSslConfiguration::defaultConfiguration();
    config.setProtocol(QSsl::AnyProtocol);
    config.setPeerVerifyMode(QSslSocket::VerifyNone);
    request.setSslConfiguration(config);

    // Create JSON using cJSON
    cJSON *root = cJSON_CreateObject();

    cJSON_AddStringToObject(root, "keyName", "sm4");
    cJSON_AddStringToObject(root, "algType", "SGD_SM4_ECB");
    cJSON_AddStringToObject(root, "iv", "");
    cJSON_AddStringToObject(root, "inData", inputBase64Data.data());
    cJSON_AddStringToObject(root, "paddingType", "PKCS7PADDING");

    char *jsonData = cJSON_Print(root);
    //printf("jsonData:%s\n",jsonData);
    QByteArray data(jsonData);
    cJSON_Delete(root);
    free(jsonData);

    QNetworkAccessManager manager;
    QNetworkReply *reply = manager.post(request, data);
    reply->setProperty("requestType", "decrypt"); // Set custom property for request type
    reply->setProperty("decode_base64_data", inputBase64Data.data()); // Set custom property for request type


    QEventLoop localLoop;

    // 当网络请求完成时，退出事件循环
    connect(reply, &QNetworkReply::finished, &localLoop, &QEventLoop::quit);

    // Use QPointer to safely handle the QNetworkReply object
    QPointer<QNetworkReply> safeReply(reply);
    
    // 设置一个 2 秒的超时，如果超时则中止请求并退出事件循环
    QTimer *timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, [&]() {
        if (safeReply && safeReply->isRunning()) {
            qDebug() << "请求超时";
            safeReply->abort();
            localLoop.quit();
        }
        delete(timer);
        timer=NULL;
    });
    timer->setSingleShot(true);
    timer->start(1000);

    // 开始事件循环，阻塞直到请求完成或超时
    localLoop.exec();
    if(timer)
    {
        delete(timer);
    }
    if (reply->error() == QNetworkReply::NoError) {
        QByteArray response = reply->readAll();
        cJSON *jsonResponse = cJSON_Parse(response.data());

        if (jsonResponse) {

            char *jsonFormat = cJSON_Print(jsonResponse);
            //qDebug() << "sendDecryptRequest operation succeed,response:" << jsonFormat;
            free(jsonFormat);

            if(1)
            {
                cJSON *message = cJSON_GetObjectItem(jsonResponse, "message");
                if(message && string(message->valuestring) == "success")
                {
                    // 获取 "result" 字段的值
                    cJSON *result = cJSON_GetObjectItem(jsonResponse, "result");
                    if (result) {
                        // 获取 "outData" 字段的值
                        cJSON *outData = cJSON_GetObjectItem(result, "outData");
                        if (outData) {
                            //将base64编码字符解码
                            string debase64_data = base64_decode(outData->valuestring);
                            resultDecrypt = debase64_data;
                            //printf("outData=%s,de_base64=%s\n", outData->valuestring,debase64_data.data());
                        } else {
                            printf("outData not found.\n");
                        }
                    } else {
                        printf("Data not found.\n");
                    }
                }
            }
            cJSON_Delete(jsonResponse);
        } else {
            qDebug() << "Failed to parse JSON response";
        }
    } else {
        qDebug() << "Error:" << reply->errorString();
    }

    reply->deleteLater();

    printf("sendDecryptRequest exit!\n");
    return resultDecrypt;
}



string CPasswordMod::sendHmacRequest(string inputData)
{
    string resultHMAC;
    if(getAccessToken().empty())
    {
        return resultHMAC;
    }
    //printf("sendHmacRequest...\n");
    QString urlString = QString("https://%1:%2%3")
                .arg(CIPHER_DEVICE_IP)
                .arg(CIPHER_DEVICE_PORT)
                .arg(CIPHER_API_HMAC);
    //printf("UrlString=%s\n",urlString.toStdString().data());
    QUrl url(urlString);
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    // 添加自定义头部
    request.setRawHeader(QByteArray::fromStdString(CIPHER_HEADER_Authorization_Token), QByteArray::fromStdString(getAccessToken()));
    request.setRawHeader(QByteArray::fromStdString(CIPHER_HEADER_Authorization_TenantCode), QByteArray::fromStdString(CIPHER_VALUE_Authorization_TenantCode));
    request.setRawHeader(QByteArray::fromStdString(CIPHER_HEADER_Authorization_AppCode), QByteArray::fromStdString(CIPHER_VALUE_Authorization_AppCode));

    QSslConfiguration config = QSslConfiguration::defaultConfiguration();
    config.setProtocol(QSsl::AnyProtocol);
    config.setPeerVerifyMode(QSslSocket::VerifyNone);
    request.setSslConfiguration(config);

    //将原文转换成base64编码
    string base64_inData = base64_encode((unsigned char*)inputData.data(),inputData.length());

    // Create JSON using cJSON
    cJSON *root = cJSON_CreateObject();

    cJSON_AddStringToObject(root, "keyName", "hmac");
    cJSON_AddStringToObject(root, "algType", "SGD_SM3");
    cJSON_AddStringToObject(root, "inData", base64_inData.data());

    char *jsonData = cJSON_Print(root);
    //printf("jsonData:%s\n",jsonData);
    QByteArray data(jsonData);
    cJSON_Delete(root);
    free(jsonData);

    QNetworkAccessManager manager;
    QNetworkReply *reply = manager.post(request, data);
    reply->setProperty("requestType", "hmac"); // Set custom property for request type


    QEventLoop localLoop;

    // 当网络请求完成时，退出事件循环
    connect(reply, &QNetworkReply::finished, &localLoop, &QEventLoop::quit);

    // Use QPointer to safely handle the QNetworkReply object
    QPointer<QNetworkReply> safeReply(reply);
    
    // 设置一个 2 秒的超时，如果超时则中止请求并退出事件循环
    QTimer *timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, [&]() {
        if (safeReply && safeReply->isRunning()) {
            qDebug() << "请求超时";
            safeReply->abort();
            localLoop.quit();
        }
        delete(timer);
        timer=NULL;
    });
    timer->setSingleShot(true);
    timer->start(1000);

    // 开始事件循环，阻塞直到请求完成或超时
    localLoop.exec();
    if(timer)
    {
        delete(timer);
    }
    if (reply->error() == QNetworkReply::NoError) {
        QByteArray response = reply->readAll();
        cJSON *jsonResponse = cJSON_Parse(response.data());

        if (jsonResponse) {

            char *jsonFormat = cJSON_Print(jsonResponse);
            //qDebug() << "sendDecryptRequest operation succeed,response:" << jsonFormat;
            free(jsonFormat);

            if(1)
            {
                cJSON *message = cJSON_GetObjectItem(jsonResponse, "message");
                if(message && string(message->valuestring) == "success")
                {
                    // 获取 "result" 字段的值
                    cJSON *result = cJSON_GetObjectItem(jsonResponse, "result");
                    if (result) {
                        // 获取 "outData" 字段的值
                        cJSON *outData = cJSON_GetObjectItem(result, "outData");
                        if (outData) {
                            //将base64编码字符解码
                            resultHMAC=outData->valuestring;
                            //printf("outData=%s\n", outData->valuestring);
                        } else {
                            printf("outData not found.\n");
                        }
                    } else {
                        printf("Data not found.\n");
                    }
                }
            }
            cJSON_Delete(jsonResponse);
        } else {
            qDebug() << "Failed to parse JSON response";
        }
    } else {
        qDebug() << "Error:" << reply->errorString();
    }

    reply->deleteLater();

    //printf("sendHmacRequest exit!\n");
    return resultHMAC;
}




bool CPasswordMod::sendEccVerifySignByCert(string inputOriValue,string inputSignValue,string certificate)
{
    bool eccVerifyResult=false;
    if(getAccessToken().empty())
    {
        return eccVerifyResult;
    }
    printf("sendEccVerifySignByCert...\n");
    QString urlString = QString("https://%1:%2%3")
                .arg(CIPHER_DEVICE_IP)
                .arg(CIPHER_DEVICE_PORT)
                .arg(CIPHER_API_ECCVerifySignByCert);
    //printf("UrlString=%s\n",urlString.toStdString().data());
    QUrl url(urlString);
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    // 添加自定义头部
    request.setRawHeader(QByteArray::fromStdString(CIPHER_HEADER_Authorization_Token), QByteArray::fromStdString(getAccessToken()));
    request.setRawHeader(QByteArray::fromStdString(CIPHER_HEADER_Authorization_TenantCode), QByteArray::fromStdString(CIPHER_VALUE_Authorization_TenantCode));
    request.setRawHeader(QByteArray::fromStdString(CIPHER_HEADER_Authorization_AppCode), QByteArray::fromStdString(CIPHER_VALUE_Authorization_AppCode));

    QSslConfiguration config = QSslConfiguration::defaultConfiguration();
    config.setProtocol(QSsl::AnyProtocol);
    config.setPeerVerifyMode(QSslSocket::VerifyNone);
    request.setSslConfiguration(config);



    //将原文转换成base64编码
    string base64_inData = base64_encode((unsigned char*)inputOriValue.data(),inputOriValue.length());
    string base64_signDerData = GetDerEncodedSignatureBase64(inputSignValue);
    if(base64_signDerData.length() == 0 || base64_inData.length() == 0)
    {
        return eccVerifyResult;
    }

    // Create JSON using cJSON
    cJSON *root = cJSON_CreateObject();

    cJSON_AddStringToObject(root, "hashAlg", "SM3");
    cJSON_AddStringToObject(root, "cert", certificate.data());
    cJSON_AddStringToObject(root, "inData", base64_inData.data());
    cJSON_AddStringToObject(root, "signData", base64_signDerData.data());

    char *jsonData = cJSON_Print(root);
    //printf("jsonData:%s\n",jsonData);
    QByteArray data(jsonData);
    cJSON_Delete(root);
    free(jsonData);

    QNetworkAccessManager manager;
    QNetworkReply *reply = manager.post(request, data);
    reply->setProperty("requestType", "eccVerify"); // Set custom property for request type

    QEventLoop localLoop;

    // 当网络请求完成时，退出事件循环
    connect(reply, &QNetworkReply::finished, &localLoop, &QEventLoop::quit);

    // Use QPointer to safely handle the QNetworkReply object
    QPointer<QNetworkReply> safeReply(reply);
    
    // 设置一个 2 秒的超时，如果超时则中止请求并退出事件循环
    QTimer *timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, [&]() {
        if (safeReply && safeReply->isRunning()) {
            qDebug() << "请求超时";
            safeReply->abort();
            localLoop.quit();
        }
        delete(timer);
        timer=NULL;
    });
    timer->setSingleShot(true);
    timer->start(1000);

    // 开始事件循环，阻塞直到请求完成或超时
    localLoop.exec();
    if(timer)
    {
        delete(timer);
    }
    if (reply->error() == QNetworkReply::NoError) {
        QByteArray response = reply->readAll();
        cJSON *jsonResponse = cJSON_Parse(response.data());

        if (jsonResponse) {

            char *jsonFormat = cJSON_Print(jsonResponse);
            //qDebug() << "sendDecryptRequest operation succeed,response:" << jsonFormat;
            free(jsonFormat);

            if(1)
            {
                cJSON *message = cJSON_GetObjectItem(jsonResponse, "message");
                if(message && string(message->valuestring) == "success")
                {
                    // 获取 "result" 字段的值
                    cJSON *result = cJSON_GetObjectItem(jsonResponse, "result");
                    if (result) {
                        // 获取 "outData" 字段的值
                        cJSON *verifyResult = cJSON_GetObjectItem(result, "verifyResult");
                        if (cJSON_IsBool(verifyResult) && cJSON_IsTrue(verifyResult)) 
                        {
                            eccVerifyResult=true;
                            printf("Ecc verify succeed!\n");
                        }
                        else {
                            printf("Ecc verify failed!\n");
                        }
                    }
                }
            }
            cJSON_Delete(jsonResponse);
        } else {
            qDebug() << "Failed to parse JSON response";
        }
    } else {
        qDebug() << "Error:" << reply->errorString();
    }

    reply->deleteLater();

    printf("sendEccVerifySignByCert exit!\n");
    return eccVerifyResult;
}


#if 0
void CPasswordMod::onFinished(QNetworkReply *reply)
{
    QString requestType = reply->property("requestType").toString();
    printf("PasswordMod onFinished:requestType=%s,result=%d\n",requestType.toStdString().data(),reply->error());
    
    QString encrypt_ori_data="";
    QString decrypt_base64_data="";
    if(requestType == "encrypt")
    {
        encrypt_ori_data = reply->property("encrypt_ori_data").toString();
        printf("encrypt_ori_data=%s\n",encrypt_ori_data.toStdString().data());
    }
    else if(requestType == "decrypt")
    {
        decrypt_base64_data = reply->property("decode_base64_data").toString();
        printf("decrypt_base64_data=%s\n",decrypt_base64_data.toStdString().data());
    }

    bool bGetToken = false;
    if (reply->error() == QNetworkReply::NoError) {
        QByteArray response = reply->readAll();
        cJSON *jsonResponse = cJSON_Parse(response.data());

        if (jsonResponse) {

            char *jsonFormat = cJSON_Print(jsonResponse);
            qDebug() << "token operation succeed,response:" << jsonFormat;
            free(jsonFormat);

            if(requestType == "token")
            {
                cJSON *success = cJSON_GetObjectItem(jsonResponse, "success");
                if (cJSON_IsBool(success) && cJSON_IsTrue(success)) {

                    // 获取 "data" 字段的值
                    cJSON *data = cJSON_GetObjectItem(jsonResponse, "data");
                    if (data) {
                        // 获取 "accessToken" 字段的值
                        cJSON *accessToken = cJSON_GetObjectItem(data, "accessToken");
                        if (accessToken) {
                            // 输出 "accessToken" 字段的值
                            bGetToken=true;
                            //printf("accessToken: %s\n", accessToken->valuestring);
                            this->setAccessToken(accessToken->valuestring);

                            g_Global.m_PasswordMod.sendEncryptRequest("admin");
                        } else {
                            printf("accessToken not found.\n");
                        }
                    } else {
                        printf("Data not found.\n");
                    }

                } else {
                    qDebug() << "token operation failed, response:" << response;
                }
            }
            else if(requestType == "encrypt" || requestType == "decrypt")
            {
                cJSON *message = cJSON_GetObjectItem(jsonResponse, "message");
                if(message && string(message->valuestring) == "success")
                {
                    // 获取 "result" 字段的值
                    cJSON *result = cJSON_GetObjectItem(jsonResponse, "result");
                    if (result) {
                        // 获取 "outData" 字段的值
                        cJSON *outData = cJSON_GetObjectItem(result, "outData");
                        if (outData) {
                            // 输出 "outData" 字段的值
                            printf("outData: %s\n", outData->valuestring);

                            if(requestType == "encrypt")
                            {
                                g_Global.m_PasswordMod.sendDecryptRequest(outData->valuestring);
                            }
                            else if(requestType == "decrypt")
                            {

                            }
                        } else {
                            printf("outData not found.\n");
                        }
                    } else {
                        printf("Data not found.\n");
                    }
                }
            }
            cJSON_Delete(jsonResponse);
        } else {
            qDebug() << "Failed to parse JSON response";
        }
    } else {
        qDebug() << "Error:" << reply->errorString();
    }

    reply->deleteLater();

    if(requestType == "token")
    {
        if(!bGetToken)
        {
            printf("getToken error,reTry!\n");
            sendTokenRequest();
        }
    }
}

#endif


//登录验签-将签名值转换成DER编码
string CPasswordMod::GetDerEncodedSignatureBase64(string inputSignvalue) {
    string base64_result;

    //先对签名值进行base64解码
    int base64_dec_target_size=base64_declen(inputSignvalue.length());
    //printf("base64_dec_target_size=%d\n",base64_dec_target_size);
    unsigned char *signValue = (unsigned char *)malloc(base64_dec_target_size+1);
    size_t signValueLen = base64_decode(inputSignvalue.data(),signValue,base64_dec_target_size);

    if (signValueLen != 128) {
        printf("Invalid signature length.\n");
        return base64_result;
    }

    int rpad = 0, spad = 0;
    if (signValue[32] & 0x80) rpad = 1; // 如果r的最高位是1，需要补0x00
    if (signValue[96] & 0x80) spad = 1; // 如果s的最高位是1，需要补0x00

    //der编码数据的长度为: R02+RL+Rpad+R+S02+SL+Spad+S的总长度
	int dataLen = (1+1+rpad+32+1+1+spad+32); 
   	//der编码后数据的总长度
	int derLenth = 1+1+(int)dataLen;
    unsigned char *derSignValue = (unsigned char *)malloc(derLenth);
    if (!derSignValue) {
        perror("Memory allocation failed");
        return base64_result;
    }

    int point = 0;
    derSignValue[point++] = 0x30; // SEQUENCE
    derSignValue[point++] = (unsigned char)(dataLen); // LENGTH       0x42=66,0x44=68
    //printf("dataLen=%d\n",dataLen);
    derSignValue[point++] = 0x02; // INTEGER
    derSignValue[point++] = (unsigned char)(32 + rpad);
    if (rpad) {
        derSignValue[point++] = 0x00;
    }
    memcpy(derSignValue + point, signValue + 32, 32);
    point += 32;

    derSignValue[point++] = 0x02; // INTEGER
    derSignValue[point++] = (unsigned char)(32 + spad);
    if (spad) {
        derSignValue[point++] = 0x00;
    }
    memcpy(derSignValue + point, signValue + 96, 32);
    point+=32;

    //将der编码的签名转换成base64编码
    char *encoded = (char *)malloc(320+1);
    base64_encode(derSignValue, point,encoded,320);
    //printf("%s\n", encoded);
    base64_result = encoded;
    free(encoded); // 释放编码后的字符串

    free(derSignValue); // 释放der编码的签名

    return base64_result;
}


