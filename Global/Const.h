#ifndef CONST_H
#define CONST_H

#include <QtCore/qglobal.h>

#define WRITE_LOG			TRUE
#define HIDE_NETWORK_MENU	TRUE	// 是否隐藏网络菜单项（TCP/UDP，集中/分布模式）
#define LOCAL_CAN_HTTP_PLAY	TRUE	// 本地歌曲是否支持HTTP播放
#define USE_USB_KEY			TRUE	// 是否使用加密狗

#define MAX_IP_LEN			16
#define MAX_BUF_LEN			1450
#define MAX_SOCKET_BUF_LEN  4096

typedef enum
{
    APP_TRIAL	= 1,	// 试用版
    APP_FORMAL,			// 正式版
    APP_LEBO,			// 北京乐播（一分区/分组对应一个播放列表和定时方案）
    APP_VIRTUAL,        // VMWARE版本
    APP_SOFTDOG_VALID,           // 加密狗版本（授权有效）
    APP_SOFTDOG_DATE_INVALID,    // 加密狗版本（授权到期）
    APP_REGISTER_INVALID         // 注册码授权到期
}AppType;

typedef enum
{
    LANGUAGE_AUTO	= 0,	// 自动读取config.ini
    LANGUAGE_CN_ZH  = 1,	// 简体中文
    LANGUAGE_CN_TW  = 2,	// 繁体中文
    LANGUAGE_CN_EN  = 3,    // 英文
}LanguageType;

// 定义编译的日期和时间
#define COMPILATION_DATE __DATE__
#define COMPILATION_TIME __TIME__

#define BETA_VERSION        0       //是否为测试版本，测试版本后面会有_beta
#define SHOW_BUILD_TIME     1       //版本号是否输出编译时间

/************龙之音电商版****************************************/
#define APP_IS_LZY_COMMERCE_VERSION     0               //龙之音电商版

#if APP_IS_LZY_COMMERCE_VERSION
    #define VERSION   ("V2.5")
#else
    #define VERSION   ("V2.5")
#endif

#define CUR_DATE_VERSION    0           //当前日期编译第几个版本（默认0）


#define DEFAULT_LANGUAGE    LANGUAGE_AUTO

#if defined(Q_OS_LINUX)
    #define SUPPORT_SERVER_SYNC    1         //支持服务器备份（启用主备服务器）
#else
    #define SUPPORT_SERVER_SYNC    0         //支持服务器备份（启用主备服务器）
#endif

#if SUPPORT_SERVER_SYNC
    #define IS_BACKUP_SERVER         0       //是否备用服务器,默认不是备用服务器
#else
    #define IS_BACKUP_SERVER         0       //是否备用服务器，如果不支持主备服务器，必定不是备用服务器
#endif

#define SUPPORT_TCP_DEFAULT      1       //默认打开TCP的支持
#define SUPPORT_WEB_PAGING       1       //支持WEB广播寻呼
#define SUPPORT_PAGER_CALL       1       //支持寻呼台对讲
#define SUPPORT_LISTEN_FUNCTION  0       //支持现场监听功能

#define SUPPORT_MANUALTASK_MANAGEMENT  1    //支持手动任务管理
#define SUPPORT_MANUALTASK_PLAYMODE    1    //支持手动任务播放模式
#define SUPPORT_MANUALTASK_PROGRESS    1    //支持手动任务控制播放进度

#define SUPPORT_SONG_MANAGER           1    //支持歌曲库管理

#define SUPPORT_TIMER_SCHEME_AUTO_SWITCH 0  //支持定时方案自动切换

#define SUPPORT_SOFTDOG                1    //支持加密狗

#define SUPPORT_USB_GPS                1    //支持USB GPS

#define SUPPORT_LOCAL_SONG_CHECK      1    //支持在线文件保存     

#define CANCEL_WEB_DEFAULT_PLACE_ADMIN 1    // 取消WEB登录页面默认显示admin账户

#define DISABLE_WEB_INDEXES_DIRECTORY_ACCESS   1    //修改.htaccess,禁止WEB进行目录访问

#define SUPPORT_WEBSOCKET_TIMEOUT_MANAGEMENT    0   //支持WEB超时管理

#define SUPPORT_REMOTE_CONTROLER         1    //支持遥控器

#define SUPPORT_AUDIO_MIXER              1    //支持音频混音器设备

#define SUPPORT_PHONE_GATEWAY            1    //支持电话网关

#define SUPPORT_AMP_CONTROLER            1    //支持功放控制器

#define SUPPORT_NOISE_DETECTOR            1    //支持噪声检测器

#define SUPPORT_INTERCOM_DEVICE_CONFIG    1     //支持对讲终端配置

#define SUPPORT_CALL_RECORD             1       //支持通话录音
#define SUPPORT_NET_RADIO               1       //支持网络电台

#define SUPPORT_SPEEX_DENOISE           1       //支持SPEEX音频降噪（用于APP喊话音频流降噪）

#define STREAM_ALLWAYS_USED_TCP          0       //始终使用TCP传输音频流(如梅州项目、佳比兰州东郊学校)

#if !STREAM_ALLWAYS_USED_TCP
#define STREAM_KCP_TCP_AUTO_SWITCH       1       //音频流传输自动切换KCP、TCP协议
#endif

#define SUPPORT_ALL_ACCOUNT_VIEW_ADMIN_DIR_AND_SONG  0    //所有子用户能看到管理员账户的歌曲列表、本地歌曲文件

#if !SUPPORT_ALL_ACCOUNT_VIEW_ADMIN_DIR_AND_SONG
    #undef SUPPORT_SUB_ACCOUNT_VIEW_SELF_LIST_ADMIN_SONG
    #define SUPPORT_SUB_ACCOUNT_VIEW_SELF_LIST_ADMIN_SONG    1    //子用户能看到自己账户所属列表的其他用户的歌曲
#endif

#define SUPPORT_USER_SECTION_XML        0         //启用用户分区XML，如果不启动，那么使用共同的分区XML（寻呼台用）

#define IMPLICIT_REGISTRATION           0       // 隐性注册(实际上未注册，但WEB显示注册)

#define ENABLE_SONG_AUDIT               0       // 启用歌曲审核（默认不启用，支持管理员及具备列表管理权限的用户进行歌曲审核，具体功能由WEB决定，目前仅限C2B6版本）

#define SUPPORT_ADVANTAGE_MINIAPP       1       // 是否支持高级版微信小程序（中转服务器转发数据）


#define IS_SYSTEM_IN_CLOUD      (APP_IS_VIRTUAL || g_Global.m_bIsRunInClound)

#define APP_IS_AISP_TZY_ENCRYPTION      0        //Aisp国密定制版（云南昆明铁道职业技术学院）

#if APP_IS_AISP_TZY_ENCRYPTION
    #undef  SUPPORT_USB_GPS
    #define SUPPORT_USB_GPS                         0      //不支持USB GPS
    
    #define TZY_ENCRYPTION_LOG          0       //是否写入调试文件
#endif

#if APP_IS_LZY_COMMERCE_VERSION

    #undef IS_SYSTEM_IN_CLOUD
    #define IS_SYSTEM_IN_CLOUD 1                    //强制云版本

    #undef SUPPORT_ADVANTAGE_MINIAPP
    #define SUPPORT_ADVANTAGE_MINIAPP 1             //强制启动高级版微信小程序
    
#endif

/**************************************************************/


/* 
* 龙之音云版本1:
* 1、歌曲比特率自动压缩为64kbps；
  2、限制子账户默认存储空间
*/


#define APP_IS_LZY_CLOUD_VERSION0       0       // 龙之音V0版本，码率128kbps，不限制存储空间，不支持寻呼台间对讲
#define APP_IS_LZY_CLOUD_VERSION1       0       // 龙之音V1版本，码率转换为64kbps,且限制存储空间，不支持寻呼台间对讲
#define APP_IS_LZY_CLOUD_VERSION2       0       // 龙之音V2版本，码率转换为64kbps,且限制存储空间，支持寻呼台间对讲
#define APP_IS_LZY_CLOUD_VERSION6       0       // 龙之音V6版本（商用新），码率转换为128kbps, 不限制存储空间, 不支持寻呼台间对讲

#define APP_IS_LZY_CLOUD_VERSION                 (APP_IS_LZY_CLOUD_VERSION0 || APP_IS_LZY_CLOUD_VERSION1 || APP_IS_LZY_CLOUD_VERSION2 || APP_IS_LZY_CLOUD_VERSION6)
#define APP_IS_SUPPORT_INTERCOM_BETWEEN_PAGER    (!APP_IS_LZY_CLOUD_VERSION0 && !APP_IS_LZY_CLOUD_VERSION1 && !APP_IS_LZY_CLOUD_VERSION6)
#define APP_IS_LZY_LIMIT_STORAGE                 (APP_IS_LZY_CLOUD_VERSION1 || APP_IS_LZY_CLOUD_VERSION2)

#if (APP_IS_LZY_CLOUD_VERSION0 == 0) && (APP_IS_LZY_CLOUD_VERSION1 == 0) && (APP_IS_LZY_CLOUD_VERSION2 == 0)
// 这里是当三者均为0时，允许编译的代码
#elif (APP_IS_LZY_CLOUD_VERSION0 == 1) && (APP_IS_LZY_CLOUD_VERSION1 == 0) && (APP_IS_LZY_CLOUD_VERSION2 == 0)
// 这里是当只有APP_IS_LZY_CLOUD_VERSION0为1时，允许编译的代码
#elif (APP_IS_LZY_CLOUD_VERSION0 == 0) && (APP_IS_LZY_CLOUD_VERSION1 == 1) && (APP_IS_LZY_CLOUD_VERSION2 == 0)
// 这里是当只有APP_IS_LZY_CLOUD_VERSION1为1时，允许编译的代码
#elif (APP_IS_LZY_CLOUD_VERSION0 == 0) && (APP_IS_LZY_CLOUD_VERSION1 == 0) && (APP_IS_LZY_CLOUD_VERSION2 == 1)
// 这里是当只有APP_IS_LZY_CLOUD_VERSION2为1时，允许编译的代码
#else
#error "最多只允许一个龙之音版本定义为1"
#endif

#if APP_IS_LZY_CLOUD_VERSION
    #undef IS_SYSTEM_IN_CLOUD
    #define  IS_SYSTEM_IN_CLOUD                     1      //强制云版本

    #undef SUPPORT_CALL_RECORD
    #define SUPPORT_CALL_RECORD                     0      //关闭通话录音

    #undef SUPPORT_NET_RADIO
    #define SUPPORT_NET_RADIO                       0      //关闭网络电台
#endif

#if APP_IS_LZY_CLOUD_VERSION1 || APP_IS_LZY_CLOUD_VERSION2 || APP_IS_LZY_CLOUD_VERSION6

    #undef SUPPORT_SERVER_SYNC
    #define SUPPORT_SERVER_SYNC                     0       //强制不支持主备服务器

    #undef SUPPORT_ADMIN_VIEW_ALL_USER_PLAYLIST      
    #if APP_IS_LZY_CLOUD_VERSION1
    #define SUPPORT_ADMIN_VIEW_ALL_USER_PLAYLIST     0      //管理员用户不显示其他子用户创建的歌曲列表(加快显示速度)
    #elif APP_IS_LZY_CLOUD_VERSION2
    #define SUPPORT_ADMIN_VIEW_ALL_USER_PLAYLIST     1      //管理员用户显示其他子用户创建的歌曲列表
    #elif APP_IS_LZY_CLOUD_VERSION6
    #define SUPPORT_ADMIN_VIEW_ALL_USER_PLAYLIST     0      //管理员用户不显示其他子用户创建的歌曲列表(加快显示速度)
    #endif

    #undef  SUPPORT_ALL_ACCOUNT_VIEW_ADMIN_DIR_AND_SONG
    #define SUPPORT_ALL_ACCOUNT_VIEW_ADMIN_DIR_AND_SONG     0      //子用户不允许查看管理员账户的歌曲列表、本地歌曲文件

    #undef  SUPPORT_SUB_ACCOUNT_VIEW_SELF_LIST_ADMIN_SONG
    #define SUPPORT_SUB_ACCOUNT_VIEW_SELF_LIST_ADMIN_SONG     0      //子用户不允许查看自己账户所属列表的其他用户歌曲

    #undef  SUPPORT_USER_SECTION_XML
    #define SUPPORT_USER_SECTION_XML     1                  //启用用户分区XML，如果不启动，那么使用共同的分区XML（寻呼台用）

    #undef  SUPPORT_AUDIO_MIXER
    #define SUPPORT_AUDIO_MIXER                     0      //关闭对音频协处理器的支持

    #undef  SUPPORT_USB_GPS
    #define SUPPORT_USB_GPS                         0      //不支持USB GPS
#endif


// 保留，待修改
#define APP_IS_LEBO             FALSE        //   (g_Global.m_AppType == APP_LEBO)
#define APP_IS_FORMAL			(g_Global.m_AppType == APP_FORMAL || g_Global.m_AppType == APP_VIRTUAL  || g_Global.m_AppType == APP_SOFTDOG_VALID)
#define APP_IS_TRIAL			(!APP_IS_FORMAL)
#define APP_IS_REGISTER         (g_Global.m_AppType == APP_FORMAL)
#define APP_IS_VIRTUAL			(g_Global.m_AppType == APP_VIRTUAL)
#define APP_IS_SOFTDOOG			(g_Global.m_AppType == APP_SOFTDOG_VALID)
#define WP_IS_CENTRALIZED		(g_Global.m_WorkPattern == WP_CENTRALIZED)
#define WP_IS_DISTRIBUTION		(g_Global.m_WorkPattern == WP_DISTRIBUTION)
#define NETWORK_IS_TCP			(g_Global.m_NetworkMode == NETWORK_TCP)
#define NETWORK_IS_UDP			(g_Global.m_NetworkMode == NETWORK_UDP)
#define PLAY_IS_HTTP            (g_Global.m_nPlayChannel == PLAY_CHANNEL_HTTP)


// 升级文件前缀
#define UPGRADE_PEFIX_PAGER_A			    ("S002")
#define UPGRADE_PEFIX_IP_SPEAKER_A			("S003")
#define UPGRADE_PEFIX_IP_SPEAKER_B			("S004")
#define UPGRADE_PEFIX_IP_SPEAKER_C			("S005")
#define UPGRADE_PEFIX_IP_SPEAKER_D			("S006")
#define UPGRADE_PEFIX_IP_SPEAKER_E			("S007")
#define UPGRADE_PEFIX_AUDIO_COLLECTOR_A		("S011")
#define UPGRADE_PEFIX_FIRE_COLLECTOR_A		("S012")
#define UPGRADE_PEFIX_SEQUENCE_POWER_A		("S013")
#define UPGRADE_PEFIX_AUDIO_COLLECTOR_B		("S014")
#define UPGRADE_PEFIX_FIRE_COLLECTOR_B		("S015")
#define UPGRADE_PEFIX_SEQUENCE_POWER_B		("S016")
#define UPGRADE_PEFIX_AUDIO_MIXER		    ("S017")
#define UPGRADE_PEFIX_REMOTE_CONTROLER		("S018")
#define UPGRADE_PEFIX_PAGER_B               ("S019")
#define UPGRADE_PEFIX_PAGER_C               ("S020")
#define UPGRADE_PEFIX_PHONE_GATEWAY         ("S021")

#define UPGRADE_PEFIX_FIRE_COLLECTOR_C      ("S022")
#define UPGRADE_PEFIX_AUDIO_COLLECTOR_C     ("S023")
#define UPGRADE_PEFIX_SEQUENCE_POWER_C      ("S024")
#define UPGRADE_PEFIX_REMOTE_CONTROLER_C    ("S025")
#define UPGRADE_PEFIX_AUDIO_MIXER_C         ("S026")

#define UPGRADE_PEFIX_IP_SPEAKER_F			("S030")
#define UPGRADE_PEFIX_FIRE_COLLECTOR_F      ("S031")
#define UPGRADE_PEFIX_AUDIO_COLLECTOR_F     ("S032")
#define UPGRADE_PEFIX_SEQUENCE_POWER_F      ("S033")
#define UPGRADE_PEFIX_REMOTE_CONTROLER_F    ("S034")

#define UPGRADE_PEFIX_IP_SPEAKER_G			("S035")
#define UPGRADE_PEFIX_AMP_CONTROLER         ("S036")
#define UPGRADE_PEFIX_NOISE_DETECTOR        ("S037")
#define UPGRADE_PEFIX_GPS_SYNCHRONIZER      ("S038")


#define UPGRADE_HOST                    ("S001")


// 保留，待修改 zhuyg
//#define FOLDER_ROOT							("SBENetworking\\")
// 本地文件夹
//#define FOLDER_LOG						("SBENetworking\\Log")
//#define FOLDER_CONFIG						("SBENetworking\\Config")
//#define FOLDER_LANGUAGE					("SBENetworking\\Language")
//#define FOLDER_DOWNLOAD					("SBENetworking\\Download")
//#define FOLDER_DOWNLOAD_LOGFILE			("\\LogFile")

// 本地文件夹
#define FOLDER_LOG							("Log")
#define FOLDER_CONFIG						("Config")
#define FOLDER_LANGUAGE						("Language")
#define FOLDER_DOWNLOAD						("Download")
#define FOLDER_DOWNLOAD_LOGFILE				("/LogFile")


// script path
#define SCRIPT_PATH                  ("/mnt/yaffs2/voip/Networking/ShellScript")   // 脚本根路径
#define SCRIPT_CHMOD                 ("chmod.sh")                                  // 更改文件夹权限
//#define SCRIPT_REMOVE_FILE         ("removeAllFile.sh")                          // 移除所有文件
#define SCRIPT_REBOOT                ("reboot.sh")                                 // 重启
#define SCRIPT_UPGRADE               ("upgrade.sh")                                // 升级系统
#define SCRIPT_BACKUP                ("backup.sh")                                 // 备份数据
#define SCRIPT_RESTORE               ("restore.sh")                                // 还原数据
#define SCRIPT_FACTORY               ("factory.sh")                                // 恢复出厂设置
#define SCRIPT_UNZIP                 ("unzip.sh")                                  // 解压缩
#define SCRIPT_UNZIP_BACKUP          ("unzip_backup.sh")                           // 备份文件解压缩，用于还原配置文件
#define SCRIPT_LOG                   ("exportLog.sh")                              // 导出日志
#define SCRIPT_RTSP                  ("rtmp.sh")                                   // rtsp转rtmp


// HTTP路径目录
#define HTTP_FOLDER_SEPARATOR				("/")
//#define HTTP_FOLDER_DATA					("SBENetworking\\Data")
#define HTTP_FOLDER_ADATA                   ("Data")
#define HTTP_FOLDER_BACKUP                  ("Backup")
#define HTTP_FOLDER_THEME_IMAGE             ("images")
#define HTTP_FOLDER_XML						("Xml")
#define HTTP_FOLDER_XML_OTHER               ("Xml/Other")
#define HTTP_FOLDER_PLAYLIST_OWN			("Xml/Playlist")
#define HTTP_FOLDER_SECTION_OWN			    ("Xml/Section")
#define HTTP_FOLDER_UPDATE					("Update")
#define HTTP_FOLDER_PROGRAM					("Program")
#define HTTP_FOLDER_PROGRAM_COMMON			("Program/Common")
#define HTTP_FOLDER_PROGRAM_MUSIC			("Program/Common/Music")
#define HTTP_FOLDER_PROGRAM_CHIME			("Program/Common/Chime")
#define HTTP_FOLDER_PROGRAM_OTHER			("Program/Other")
#define HTTP_FOLDER_UPDATE_FIRMWARE         ("Update/Firmware")
#define HTTP_FOLDER_UPDATE_UPGRADE          ("Update/Upgrade")
#define HTTP_FOLDER_UPDATE_OTHER            ("Update/Other")

// HTTP文件
#define HTTP_FILE_GROUP						("Group.xml")
#define HTTP_FILE_SECTION					("Zone.xml")
#define HTTP_FILE_SECTION_LIMIT				("Zone_limit.xml")
#define HTTP_FILE_PLAYLIST					("Playlist.xml")
#define HTTP_FILE_PLAYLIST_NETWORK			("Playlist(Network).xml")
#define HTTP_FILE_PLAYLIST_HOST             ("Playlist(Host).xml")
#define HTTP_FILE_TIMER						("Timer.xml")
#define HTTP_FILE_TIMER_HOST				("Timer(Host).xml")
#define HTTP_FILE_AUDIO_COLLECTOR			("AudioCollector.xml")
#define HTTP_FILE_FIRE_COLLECTOR			("FireCollector.xml")
#define HTTP_FILE_MONITOR                   ("Monitor.xml")
#define HTTP_FILE_SECMONITOR                ("SecMonitor.xml")
#define HTTP_FILE_INTERCOM_STATION          ("IntercomStation.xml")     //
#define HTTP_FILE_USER                      ("User.xml")
#define HTTP_FILE_SEQUENCE_POWER            ("SequencePower.xml")
#define HTTP_FILE_PAGER                     ("Pager.xml")       //寻呼台文件
#define HTTP_FILE_REMOTE_CONTROLER          ("RemoteControler.xml")       //远程遥控器文件
#define HTTP_FILE_AUDIO_MIXER               ("AudioMixer.xml")  //音频混音器文件
#define HTTP_FILE_PHONE_GATEWAY             ("PhoneGateway.xml")  //电话网关文件
#define HTTP_FILE_AMP_CONTROLER             ("AmpControler.xml")  //功放控制器文件
#define HTTP_FILE_NOISE_DETECTOR            ("NoiseDetector.xml")  //噪声检测器文件
#define HTTP_FILE_GPS                       ("Gps.xml")

#if SUPPORT_SONG_MANAGER
#define HTTP_FILE_SONGLIST					("Songlist.xml")
#endif

// 配置文件标签
#define CONFIG_FILE_NAME					("Config.ini")
#define CONFIG_FILE_SECTION_LAYOUT			("Layout")
#define CONFIG_FILE_ITEM_AUTO_SAVE			("AutoSave")
#define CONFIG_FILE_SECTION_SECTION			("Section")
#define CONFIG_FILE_ITEM_PLAY_MODE			("PlayMode")
#define CONFIG_FILE_ITEM_CLOUND_SERVER      ("CloudServer")
#define CONFIG_FILE_ITEM_MONITOR            ("EnableMonitor")
#define CONFIG_FILE_ITEM_EXAMINATION_MODE   ("ExaminationMode")
#define CONFIG_FILE_ITEM_CLOUD_CONTROL      ("CloudControl")
#define CONFIG_FILE_ITEM_ENABLE_CALL_RECORD ("EnableCallRecord")
#define CONFIG_FILE_ITEM_BELL				("Bell")
#define CONFIG_FILE_ITEM_WORK_PATTERN		("WorkPattern")
#define CONFIG_FILE_ITEM_NETWORK_MODE		("NetworkMode")
#define CONFIG_FILE_SECTION_SPLASH			("Splash")
#define CONFIG_FILE_ITEM_BMP_DEFAULT		("IsDefaultBmp")
#define CONFIG_FILE_ITEM_BMP_PATHNAME		("BmpPathName")
#define CONFIG_FILE_ITEM_BMP_DURATION		("Duration")
#define CONFIG_FILE_ITEM_BMP_ORIGINAL_SIZE	("IsDefaultSize")
#define CONFIG_FILE_ITEM_BMP_WIDTH			("Width")
#define CONFIG_FILE_ITEM_BMP_HEIGHT			("Height")
#define CONFIG_FILE_SECTION_LANGUAGE		("Language")
#define CONFIG_FILE_ITEM_CHECKED_LANG		("Checked")
#define CONFIG_FILE_SECTION_ADVANCE			("Advanced")
#define CONFIG_FILE_ITEM_AUTORUN			("Autorun")
#define CONFIG_FILE_ITEM_ZONE_ICON			("ZoneIcon")
#define CONFIG_FILE_ITEM_NETWORK_IP			("NetworkIP")
#define CONFIG_FILE_ITEM_DEVICES_SYNC		("DevicesSync")
#define CONFIG_FILE_ITEM_AUDIOCAST			("Audiocast")
#define CONFIG_FILE_ITEM_SONGS_FROM			("SongsFrom")
#define CONFIG_FILE_ITEM_PLAY_CHANNEL		("PlayChannel")
#define CONFIG_FILE_ITEM_SONGS_SERVER_IP	("SongsServerIP")
#define CONFIG_FILE_ITEM_SONGS_SERVER_PORT	("SongsServerPort")
#define CONFIG_FILE_SECTION_NETWORK_FILE	("NetworkFile")
#define CONFIG_FILE_ITEM_FILE_SERVER_IP		("ServerIP")
#define CONFIG_FILE_ITEM_FILE_SERVER_PORT	("Port")
#define CONFIG_FILE_ITEM_FILE_PATH			("FilePath")
#define CONFIG_FILE_ITEM_AUTOTIME           ("AutoTime")         // zhuyg
#define CONFIG_FILE_ITEM_LISTEN_DEVICE      ("ListenDevice")
#define CONFIG_FILE_SECTION_SERVER          ("Server")
#define CONFIG_FILE_ITEM_HOST_GRADE         ("Grade")            // 主机等级 1、2级
#define CONFIG_FILE_ITEM_HIGHER_HOST_IP     ("HigherHostIP")     // 上级主机IP，2级下级主机实时检测连接
#define CONFIG_FILE_ITEM_HOST_NAME          ("HostName")
#define CONFIG_FILE_ITEM_HOST_VOLUME        ("Volume")


//云广播通讯端口
#define CONFIG_FILE_SECTION_COMM_PORT       ("CommPort")
#define CONFIG_FILE_SECTION_COMM_HTTP_PORT  ("HttpPort")
#define CONFIG_FILE_SECTION_COMM_WEB_PORT   ("WebPort")
#define CONFIG_FILE_SECTION_COMM_TCP_PORT   ("TcpPort")
#define CONFIG_FILE_SECTION_COMM_KCP_PORT   ("KcpPort")

//备份同步相关配置
#define CONFIG_FILE_SECTION_SERVER_SYNC	        ("ServerSync")
#define CONFIG_FILE_SECTION_SERVER_SYNC_SWITCH  ("Switch")
#define CONFIG_FILE_SECTION_SERVER_SYNC_HOST    ("Host")
#define CONFIG_FILE_SECTION_SERVER_SYNC_USER    ("User")

// 注册文件标签
#define REGISTER_FILE_NAME                ("Register.ini")
#define REGISTER_FILE_CONFIG              ("Register")
#define REGISTER_FILE_ITER_CODE           ("Code")

#define REGISTER_TTS_BASIC_CODE           ("TTS_Basic_Code")
#define REGISTER_CLOUD_CONTROL_CODE       ("Cloud_Control_Code")


// 数据库文件
#if APP_IS_AISP_TZY_ENCRYPTION
#define DATABASE_SQLITE_LOG_NAME                  "log.db"
#define DATABASE_SQLITE_USER_NAME                 "account.db"
#define DATABASE_SQLITE_RECORD_NAME               "record.db"
#else
#define DATABASE_SQLITE_LOG_NAME                  "data.db"
#define DATABASE_SQLITE_USER_NAME                 "user.db"
#define DATABASE_SQLITE_RECORD_NAME               "record.db"
#endif

// TTS File
#define TTS_LIST_NAME                     ("TTS List")

//账户
#define MAX_USER_COUNT              1000            // 最大用户数量

// 分区分组
#define MAX_SECTION_COUNT_FORMAL	4000			// 最多分区数目（正式版）
#define MAX_SECTION_COUNT_TRIAL		2			    // 最多分区数目（测试版）
#define MAX_SECTION_COUNT			(APP_IS_TRIAL ? MAX_SECTION_COUNT_TRIAL : MAX_SECTION_COUNT_FORMAL)

#define MAX_GROUP_COUNT_FORMAL		1000			// 最多的分组数目（正式版）
#define MAX_GROUP_COUNT_TRIAL		1000			// 最多的分组数目（测试版）
#define MAX_GROUP_COUNT				(APP_IS_TRIAL ? MAX_GROUP_COUNT_TRIAL : MAX_GROUP_COUNT_FORMAL)

#define SEC_NAME_LEN                32          // 分区名称长度
#define GROUP_NAME_LEN				32			// 分组名称长度
#define SRC_NAME_LEN				128			// 节目源名称长度(显示的时候再截取)
#define SEC_MAC_LEN					18			// 分区MAC地址长度
#define SEC_VERSION_LEN				20			// 版本号长度
#define	BTN_BMP_COUNT				3			// 分区图片的数目
#define DEFAULT_SECTION_BMP_ID		2			// 默认分区图片ID
#define DEFAULT_GROUP_BMP_ID		1			// 默认分组图片ID
#define STR_MAX_PATH                    260         // 路径最大长度
#define MAX_PAGE_COUNT    20              // 包最大负载数量
#define MAX_MONITOR_COUNT  5              // 监控最大负载数量

// 播放列表
#define MAX_PLAY_LIST_COUNT_FORMAL			1000			// 最大的播放列表数目（正式版）
#define MAX_PLAY_LIST_COUNT_TRIAL			1000			// 最大的播放列表数目（测试版）
#define MAX_PLAY_LIST_COUNT					(APP_IS_TRIAL ? MAX_PLAY_LIST_COUNT_TRIAL : MAX_PLAY_LIST_COUNT_FORMAL)

#define MAX_SONGS_PER_LIST_COUNT_FORMAL		1000			// 每个列表最多的歌曲数目（正式版）
#define MAX_SONGS_PER_LIST_COUNT_TRIAL		1000	        // 每个列表最多的歌曲数目（测试版）
#define MAX_SONGS_PER_LIST_COUNT			(APP_IS_TRIAL ? MAX_SONGS_PER_LIST_COUNT_TRIAL : MAX_SONGS_PER_LIST_COUNT_FORMAL)

#define MAX_SONGS_TTS_LIST_COUNT            3000            // TTS list列表歌曲数量（独立）

#define LIST_NAME_LEN						48			// 列表名称长度
#define EXTRA_LIST				            1	        // 一个额外列表（音频采集器）

#if SUPPORT_SONG_MANAGER
#define MAX_TOTAL_SONGS_COUNT_FORMAL		10000		// 总共的歌曲数（正式版）
#define MAX_TOTAL_SONGS_COUNT_TRIAL		    10000	    // 总共的歌曲数（测试版）
#define MAX_TOTAL_SONGS_COUNT			(APP_IS_TRIAL ? MAX_TOTAL_SONGS_COUNT_TRIAL : MAX_TOTAL_SONGS_COUNT_FORMAL)                     
#endif

// 定时点
#define MAX_SCHEME_COUNT					100			// 最多定时方案数目
#define MAX_TIMER_COUNT_FORMAL				2000		// 最多定时点数目（正式版）
#define MAX_TIMER_COUNT_TRIAL				2			// 最多定时点数目（测试版）
#define MAX_TIMER_COUNT						(APP_IS_TRIAL ? MAX_TIMER_COUNT_TRIAL : MAX_TIMER_COUNT_FORMAL)

#define TIMER_NAME_LEN						24
#define SCHEME_NAME_LEN						24


// 网络
#define  WEB_PORT_DEFAULT                     8081                     // websocket端口
#define HTTP_PORT_DEFAULT					 9999                    // HTTP服务器访问端口
#define DEVICE_SYNC_MIN				 1						  // 最多同时同步的设备数最小值
#define DEVICE_SYNC_MAX				 (APP_IS_LEBO ? 30 : 10)  // 最多同时同步的设备数最大值
#define DEVICE_SYNC_DEFAULT			 5						  // 最多同时同步的设备数默认值


// SIP
#define VOIP_IP             ("**************")
#define VOIP_PORT           8999


// 其它
#define KEY_char_COUNT				12			// 密匙长度
#define LANGUAGE_COUNT				50			// 语言数目上限
#define SECRET_KEY1					0x96374185	// 密钥1
#define SECRET_KEY2					0x41859637	// 密钥2
#define MAX_BACKUP_COUNT     10            // 备份文件最大数量


#if APP_IS_LZY_COMMERCE_VERSION
#define SECRET_TTS_BASIC_KEY1       0x97265183  // 密钥1
#define SECRET_TTS_BASIC_KEY2       0x40967538  // 密钥2
#endif

#define SECRET_CLOUD_CONTROL_KEY1       0x95268163  // 密钥1
#define SECRET_CLOUD_CONTROL_KEY2       0x42689635  // 密钥2



#define LANG_STR(sec, item, def)	g_Global.m_Language.GetString(sec, item, def)
#define LOG(szLog, level)           g_Global.m_Log.PrintLog(szLog, level)
#define LOG_INFO(szLog)             g_Global.m_Log.PrintLog(szLog, LV_INFO)
#define NOTIFY(fmt, ...)            g_Global.m_Log.NotifyLog(fmt, ##__VA_ARGS__)
#define ERROR_LOG(fmt, ...)         g_Global.m_Log.ErrorLog(fmt, ##__VA_ARGS__)
#define FORMAT(fmt, ...)            g_Global.m_Log.Format(fmt, ##__VA_ARGS__)

#define DEFAULT_DATETIME         ("0000-00-00 00:00:00")
#define DEFAULT_DATE             ("0000-00-00")
#define DEFAULT_TIME             ("00:00:00")

// 常规错误码
typedef enum   ERROR_CODE
{
    EC_ERROR = -1,                  // 未知错误
    EC_SUCCESS  = 0,                // 操作正常
    EC_NO_LIMIT ,                   // 用户权限不足
    EC_USER_EXISTED,                // 用户已存在
    EC_USER_NOTEXIST,               // 用户不存在
    EC_PWD_ERROR,                   // 用户密码错误
    EC_USER_NOTLOGIN,               // 用户未登录
    EC_USER_LOGINED,                // 用户已登录
    EC_TARGET_NOTEXIST,             // 目标不存在
    EC_TARGET_EXISTED,              // 目标已存在
    EC_TARGET_NUM_ERROR,            // 目标数目不符
    EC_TARGET_FORMAT_ERROR,         // 目标格式不符
    EC_TARGET_PARM_ERROR,           // 目标参数不符
    EC_TARGET_USING,                // 目标繁忙
    EC_TARGET_OFFLINE,              // 目标离线
    EC_SELECTEDID_ERROR,            // 选中分区标识不符
    EC_MAX_NUM,                     // 已达最大数量
    EC_SIP_INUSE,                   // sip繁忙
    EC_SIP_OFFLINE,                 // sip号码离线
    EC_SIP_ERROR,                   // sip未知错误
    EC_SIP_NOTEXIST,                // sip账号不存在
    EC_SIP_RULE_NOTEXIST,           // sip拨号规则不存在
    EC_SIP_RULE_NOTUNIFY,           // sip拨号规则不统一
    EC_TC_SCHID_ID_ERROR,           // 定时方案ID错误
    EC_TC_TP_ID_ERROR,              // 定时点ID错误
    EC_TC_SCH_NAME_ERROR,           // 定时方案名称错误(为空或超出最大限制)
    EC_TC_TP_NAME_ERROR,            // 定时点名称错误(为空或超出最大限制)
    EC_TC_DATETIME_ERROR,           // 日期时间格式错误(为空,超出长度或数据格式错误)
    EC_TC_DATE_EARLY_TODAY,         // 指定日期早于今天
    EC_TC_SDATE_LATE_EDATE,         // 开始日期晚于结束日期
    EC_TC_SDATE_EARLY_NOW,          // 开始日期为今天且开始时间早于现在
    EC_TC_STIME_LATE_ETIME,         // 开始时间晚于结束时间
    EC_TC_NO_SELECT_DEVICE,         // 没有选择设备
    EC_TC_NO_SELECT_SONG,           // 没有选择歌曲
    EC_TC_TP_MAXNUM,                // 定时点超出数量限制
    EC_TC_TCH_MAXNUM,               // 定时方案超出数量限制
    EC_SONG_FILE_NOTEXIST,          // 歌曲文件不存在
    EC_UPGRADE_FILE_NOTEXIST,       // 升级文件不存在
    EC_TTS_PARAM_ERROR,             // 合成参数错误(TTS)
    EC_TTS_VOICE_NOTEXIST,          // 发音人不存在(TTS)
    EC_TTS_TEXT_MAX,                // 合成文字过长(TTS)
    EC_TTS_ERROR,                   // 合成失败(TTS)
    EC_TTS_AUDIO_SHORT,             // 音频过短(TTS)
    EC_WP_ERROR,                    // 工作模式不符
    EC_LISTEN_SOURCE_UNABLE,        // 当前节目源无法监听
    EC_SEC_NOT_EXIST,               // 设备不存在
    EC_SEC_OFFLINE,                 // 设备离线
    EC_DEVLOG_DOWNLOADING,          // 日志文件下载中
    EC_DEVLOG_NOT_EXIST,            // 日志文件不存在
    EC_REGISTER_FAILED,             // 服务器注册失败
    EC_TIMEOUT,                     // 操作超时
    EC_NETWORK_ERROR,               // 设备网络错误
    EC_TC_TP_CONFLICT,              // 定时点冲突
    EC_MANUAL_ALARM_SONG_NOT_EXIST, // 手动告警歌曲不存在
    EC_EXIST_SUB_USER_CAN_NOT_DELETED,    //该账户下存在子账户，无法删除
    EC_EXCEEDED_STORAGE_CAPACITY,       //超出存储容量
    
    EC_DEVICE_NOT_SUPPORT_FUNCTION,     //设备不支持该功能


    EC_USER_LOGIN_TZY_Cipher_CONNECT_ERROR=400,        // 密码机连接失败
    EC_USER_LOGIN_TZY_CERTIFICATE_ERROR=401,            // 用户证书错误
    EC_USER_LOGIN_TZY_CERTIFICATE_NOT_MATCH=402,        // 用户证书与USB-KEY证书不匹配

    EC_UPDATE_INIT_ERROR = 2000,    // 升级初始化失败
}ECODE;






#endif // CONST_H




