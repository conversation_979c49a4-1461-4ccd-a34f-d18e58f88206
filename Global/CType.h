#ifndef CTYPE_H
#define CTYPE_H

#include <QtCore/qglobal.h>

#if defined(Q_OS_LINUX)
#include "Network/HPSocket/GlobalDef.h"
#else
#include "windef.h"
#endif
//#define     CHAR        char
//#define     BYTE        unsigned char
#if defined(Q_OS_LINUX)
#define     byte        unsigned char
#endif
#define     BOOL        bool
#define     PBOOL      BOOL*
//#define     UINT        unsigned int
#define     UINT32      UINT
//#define     LPCTSTR     CMyString
//#define     SHORT       short
//#define     USHORT      unsigned short
//#define     ULONG       unsigned long
//#define     ULLONG      unsigned long long
//#define     LPSTR       char*
//#define     LPCSTR      const char*
#define     TRUE        1//true
#define     FALSE       0//false
//#define     VOID        void
//#define     LPVOID      void*
//#define     INT         int

#if defined(Q_OS_LINUX)
typedef     byte            *LPBYTE;
#endif
typedef     unsigned int    *PUINT;

#if defined(Q_OS_WIN32)
typedef     unsigned short  ushort;
typedef     unsigned long   ulong;
typedef     unsigned char   u_char;
#endif

// 返回数据库的数据结构
typedef struct BaseData
{
    int     nRow;
    int     nColumn;
    char**  szResult;

}BaseData;


struct if_data
{
    /*  generic interface information */
    unsigned long ifi_opackets;    /*  packets sent on interface */
    unsigned long ifi_ipackets;    /*  packets received on interface */
    unsigned long ifi_obytes;      /*  total number of octets sent */
    unsigned long ifi_ibytes;      /*  total number of octets received */
};

struct if_info
{
    char ifi_name[64];
    unsigned long ifi_ibytes;
    unsigned long ifi_obytes;
};

struct if_speed
{
    char ifs_name[64];
    unsigned long ifs_ispeed;
    unsigned long ifs_ospeed;
    unsigned long ifs_us;
};

struct io_speed
{
    float ispeed;
    float ospeed;
};



#endif // CTYPE_H
