#ifndef GLOBALDATA_H
#define GLOBALDATA_H

#include <QtCore/qglobal.h>

#include <map>
#include <vector>
#include <string>
#include <list>

using namespace std;

#include "Tools/CMyString.h"
#include "Network/Network.h"
#include "Model/Device/Section.h"
#include "Model/Other/Group.h"
#include "Model/Device/WebSection.h"
#include "Model/Other/TimerScheme.h"
#include "Model/Other/RadioManager.h"
#include "Tools/Language.h"
#include "Player/SongTool.h"
#include "Player/PlayQueue.h"
#include "Player/AUXPlay.h"
#include "Tools/Ini.h"
#include "Tools/MyLog.h"
#include "Tools/FileManager.h"
#include "Database/LogTable.h"
#include "Database/UserTable.h"
#include "Network/Web/WebNetwork.h"
#include "Network/Monitor/MonProc.h"
#include "Network/VOIP/Voip.h"
#include "Model/Other/VoipSipInfo.h"
#include "Model/User/UserManager.h"
#include "Tools/Control/TTS.h"
#include "Tools/FileSync.h"
#include "Model/LocalHost.h"
#include "Tools/UpgradeManager.h"
#include "Player/RecordManager.h"
#include "Tools/xxtea.h"

#include "Const.h"

#if SUPPORT_SOFTDOG
#include "Tools/Ukey/Softdog.h"
#endif

#if SUPPORT_USB_GPS
#include "Tools/Ugps/usbGPS.h"
#endif

#if defined(Q_OS_LINUX)
#include "Network/ServerSync.h"
#endif

#if APP_IS_AISP_TZY_ENCRYPTION
#include "PasswordMod/PasswordMod.h"
#endif

#define DEFAULT_SONG_SERVER_PORT	65505


enum
{
    ZONE_ICON_LARGE		= 0,	// 分区图标（大）
    ZONE_ICON_SMALL,			// 分区图标（小）
};

enum
{
    AUTORUN_FALSE		= 0,	// 开机不自动运行
    AUTORUN_TRUE,				// 开机自动运行
};

enum
{
    SONGS_FROM_LOCAL	= 0,	// 本机
    SONGS_FROM_NETWORK,			// 音频服务器
};

enum
{
    PLAY_CHANNEL_DEVICE	= 0,	// 从设备播放
    PLAY_CHANNEL_HTTP,			// 从HTTP播放
};

enum
{
    ZONE_DATA_SHARED	= 0,	// 数据共享
    ZONE_DATA_OWN,				// 数据独占（每个分区有独立的歌曲列表和定时方案）
};

/*
enum
{
    RESET_ZONE			= 0x00000001,	// 重置分区
    RESET_GROUP			= 0x00000010,	// 重置分组
    RESET_PLAY_LIST		= 0x00000100,	// 重置播放列表
    RESET_TIMER			= 0x00001000,	// 重置定时信息
    RESET_AUDIO			= 0x00010000,	// 重置音频采集器信息
    RESET_FIRE			= 0x00100000,	// 重置告警采集器信息
    RESET_ALL			= 0x11111111,	// 重置所有文件
};
*/

typedef enum
{
    HOST_GRADE_1,    // 主机等级1
    HOST_GRADE_2     // 主机等级2
}HOST_GRADE;

#if 0       //可能会导致分区定时等信息被误清除，等后面分区上线再处理
typedef struct
{
    string devMac;  // 设备Mac
    int devType;    // 设备类型
}st_devMacType;
#endif

#if defined(Q_OS_WIN32)
#if 0
typedef struct {
    char			m_szMAC[MAX_MAC_LEN];        // 本机MAC
    char			m_szIP[MAX_IP_LEN];		     // 本机网络地址1
    char			m_szSubAddress[MAX_IP_LEN];	 // 本机子网地址1
    char			m_szIP2[MAX_IP_LEN];		 // 本机网络地址2
    char			m_szSubAddress2[MAX_IP_LEN]; // 本机子网地址2
    char			m_szIP3[MAX_IP_LEN];		 // 本机网络地址3
    char			m_szSubAddress3[MAX_IP_LEN]; // 本机子网地址3
    char			m_szGateWay[MAX_IP_LEN];     // 本机网关
    char			m_szDNS[MAX_IP_LEN];         // 本机DNS
}st_netCard;
#endif

typedef struct {
    char			m_szIP[MAX_IP_LEN];
    char			m_szSubAddress[MAX_IP_LEN];
}st_netIpInfo;

typedef struct {
    char			m_szMAC[MAX_MAC_LEN];        // MAC
    char			m_szGateWay[MAX_IP_LEN];     // 网关
    char			m_szDNS[MAX_IP_LEN];         // DNS
    int             m_nValidIPId;                //有效的IP ID(从0开始)
    st_netIpInfo    m_szIPInfo[5];               // IP信息（每张网卡最大5个IP）
}st_netCard;
#endif

class CGlobalData
{
public:
    CGlobalData(void);
    ~CGlobalData(void);

public:
    bool	InitData(bool bRegister);				// 初始化数据
    void	SetFixedData(void);						// 设置一些固定配置
    void	ReadLocalFileInfo(void);				// 读取本地文件信息
    void	InitPlaylist(bool bReset);				// 初始化播放列表
    void	CreateLogFile(void);                    // 创建程序运行日志文件
    //void	SetSystemFont(void);					// 设置系统字体
    void	SetAutorun(bool bAuto);                 // 设置开机自启动
    void    SetAutoTime(bool bAutoTime);            // 设置是否自动设置时间(GPS)
    // 重置高级设置
    void    ResetAdvanceSettings(const char *szServerIP,        // 服务器IP
                                 WorkPattern wp,				// 工作模式
                                 NetworkMode nm,                // 网络模式
                                 Audiocast	ac,					// 音频传输
                                 int		nSyncCount,			// 同步设备数量
                                 int        nSongFrom,          // 歌曲来自
                                 LPCSTR	szSongServerIP,         // 歌曲服务器地址
                                 int        nSongServerPort,    // 歌曲服务器端口
                                 int     	nPlayChannel);      // 播放通道

    // 重置本地文件
    //void    ResetLocalFile(long long fileFlag);
    // 初始化默认配置信息
    void    InitDefaultConfig();

    // 获取文件中的所有固件名称
    void    UpdateFirmwareArray();
    // 删除服务器中所有的升级固件
    void    CleanFirmware();

    // 删除服务器中所有的日志文件
    void    CleanLog(int nDays = 0);
    
    // 清除数据库中超过指定天数的日志记录
    void    ClearLogDataByDays(int nDays);

    // 写入xml文件，通知所有大屏与web socket
    bool    WriteXmlFile(FileType fileType, bool bNotifyDeviceUpdate = true);
    
    // 网络检测相关方法
    void    StartInternetCheckThread();                // 启动网络检测线程
    void    StopInternetCheckThread();                 // 停止网络检测线程
    static void* InternetCheckThreadProc(void* lpParam); // 网络检测线程函数

private:
    void	InitAppType(bool bRegister);			// 初始化程序类型
    void	InitDeviceData(void);                   // 初始化设备数据
    void	CreateFileDirectory(void);				// 创建文件路径
    void	InitAdvanceData(void);					// 初始化高级设置的数据
    void	InitConfigData(void);					// 初始化配置数据
    void    InitLocalHostData(void);                // 初始化本地分控数据
    //bool	StartHttpServer(void);					// 启动HTTP服务器


public:
    AppType			m_AppType;						// 软件类型
    CTime           m_RegisterLimitDate;            // 注册码限制日期
    WorkPattern		m_WorkPattern;					// 工作模式（集中模式、分布模式）
    NetworkMode		m_NetworkMode;					// 网络模式（TCP、UDP）
    bool			m_bForceClose;					// 强制退出程序

    CSections		m_Sections;						// 分区设备
    CSections		m_Pagers;                       // 寻呼台设备
    CSections		m_GpsDevices;					// GPS设备
    CSections		m_AudioCollectors;				// 音频采集器
    CSections		m_FireCollectors;				// 消防采集器
    CSections       m_SequencePower;                // 电源时序器
    CSections       m_AudioMixers;                  // 混音器
    CSections       m_RemoteControlers;             // 远程遥控器
    CSections       m_PhoneGateways;                // 电话网关
    CSections       m_AmpControlers;                // 功放控制器
    CSections       m_NoiseDetectors;               // 噪声检测器
    CSections		m_ControlDevices;				// 分控设备（移动设备）
    CSections       m_Servers;                      // 下级分控服务器
    CSections       m_IntercomStations;             // DSP9312

    CMonitors       m_Monitors;                     // 监控设备

    CWebSections    m_WebSections;                  // web,大屏 设备
    CVoipSipInfo    m_VoipInfo;                     // sip信息保存
    CUserManager    m_Users;                        // 用户
    CLocalHost      m_LocalHost;                    // 本地状态信息保存
    CHigherHost     m_HigherHost;                   // 上级主机
#if defined(Q_OS_LINUX)
    CHostFileSync   m_HostFileSync;                 // 文件同步
#endif
    CSections*		m_pAllDevices[DEVICE_TYPE_COUNT];// 指向所有设备数据的指针

    CNetwork		m_Network;						// 网络与协议处理
    CGroups			m_Groups;						// 分组数据
    CPlayList       m_PlayList;						// 播放列表数据
    CRadioManager   m_RadioManager;					// 电台信息管理
    #if SUPPORT_SONG_MANAGER
    CSongManager    m_SongManager;                  // 歌曲管理
    #endif
    CTimerScheme	m_TimerScheme;					// 定时数据
    CLanguage		m_Language;						// 语言
    CTTSManager     m_TTSManager;                   // 文字转语音
#if defined(Q_OS_LINUX)
    CFileSyncManager m_FileSyncManager;             // 同步文件管理
#endif
    CUpgradeManager m_UpgradeManager;               // 固件升级管理

    // Linux服务器 zhuyg
    //CDbManager	m_DbManager;                    // 数据库
    CDataBase       m_dblog;                        // 日志数据库
    CLogTable		m_logTable;						// 日志数据表

    CDataBase       m_dbuser;                       // 用户数据库
    CUserTable      m_userTable;                    // 用户数据表
#if SUPPORT_CALL_RECORD
    CDataBase       m_dbrecord;                     // 录音数据库
    CRecordManager  m_RecordManager;                // 录音管理器
#endif
    CPlayQueue		m_PlayQueue;					// 歌曲播放队列
#if defined(Q_OS_LINUX)
    CAUXPlay        m_AuxPlay;                      // 主机AUX
#endif
    // 高级设置
    int				m_nZoneIcon;					// 分区图标
    int				m_nAutorun;						// 开机自动运行
    bool            m_bValidGps;                    // GPS是否有效
    char			m_szNetworkIP[MAX_IP_LEN];		// 本机网络地址
    NetCardInfo     m_szNetCard;        // 本机网卡信息

    int             m_bValidNetCardId;                // 有效网卡id(0或者1,目前仅WIN32平台有效，为了代码统一，没有使用宏定义区分)
    int             m_bValidNetCardCnt;               // 有效网卡的数量(目前仅WIN32平台有效，为了代码统一，没有使用宏定义区分)
#if defined(Q_OS_WIN32)
    int             m_bTotalNetCardCnt;               // 网卡总数

    st_netCard      m_netCardList[5];                // 网卡集合
#endif
    int				m_nMaxDevicesSync;				// 同时同步设备数目
    Audiocast		m_Audiocast;					// 音频传输模式
    char            m_ListenDevice[MAX_MAC_LEN];    // 监听设备MAC
    int				m_nSongsFrom;					// 歌曲来源
    int				m_nPlayChannel;					// 播放通道
    char            m_szSongServerIP[MAX_IP_LEN];	// 歌曲服务器IP
    int				m_nSongServerPort;				// 歌曲HTTP服务器端口
    int				m_nZoneData;					// 分区数据
    HOST_GRADE      m_HostGrade;                    // 主机等级
    char            m_szHigherHostIP[MAX_IP_LEN];   // 上级主机服务器IP
    bool            m_bNeedResumeLocalPlay;         // 是否需要掉线后恢复播放     //  CS 2019-5 （掉线后恢复播放）
    bool            m_bLinkInternet;                // 是否连接上外网
    pthread_t       m_internetCheckThread;         // 网络检测线程
    bool            m_bStopInternetCheck;          // 停止网络检测标志

    CFileManager	m_fileManager;                  // 文件管理类 CS
    CIni            m_IniConfig;					// 配置文件
    CSongTool		m_SongTool;                     // 歌曲工具类
    CMyLog			m_Log;							// 日志

    CMyString		m_strHttpRootDir;               // HTTP根目录
    string          m_strVoipServerIP;              // sip服务器地址

    CMyString       m_strRunDirPath;                // 程序运行目录
    CMyString       m_strFolderPath;                // 程序资源目录
    CMyString       m_strApachePath;                // Apache目录

    CMyString       m_strTempFolderPath;            // 临时文件夹路径

    map<DeviceModel, LPCSections> m_ModelToDevices; // 设备模型与设备数据的映射
    // Linux服务器
    //CMessageHandle	m_MsgHandle;				// 消息处理类

    vector<string>      m_Firmwares;               // 固件名称容器
    CWebNetwork         m_WebNetwork;              // webNetwork
    CMonitorProcess     m_MonProc;                 // 监控进程
    CVoip               m_Voip;                    // voip服务器


    bool                m_bIsRunInClound;          //是否运行于云服务器

    bool                m_bEnableMonitor;          //是否启用监控

    bool                m_bExamination_mode;       // 是否启用考试模式

    bool                m_bCloudControl;           // 是否启用云控制

    bool                m_bEnableCallRecord;       // 是否启用通话录音  

    bool                m_bNmcliValid;             //nmcli指令是否有效

    //云广播通讯端口
    int             m_HTTP_PORT;                      //http端口
    int             m_WEB_PORT;                       //WEB端口
    int             m_TCP_PORT;                       //TCP端口
    int             m_KCP_PORT;                       //TCP模式下UDP传输音频流(KCP)端口


    #if SUPPORT_SOFTDOG
    CSoftdog        m_softDog;                       //加密狗
    #endif
#if defined(Q_OS_LINUX)
    #if SUPPORT_USB_GPS
    CusbGPS         m_usbGps;                        //USB GPS
    #endif
#endif

    #if SUPPORT_SERVER_SYNC
    CServerSync     m_serverSync;                   // 主备同步服务器对象
    #endif

    unsigned int    m_nPlayUnitTimeout;                //播放单元超时检测，如果异常，退出程序让其重启 

    #if APP_IS_AISP_TZY_ENCRYPTION
    CPasswordMod  m_PasswordMod;
    #endif

    #if APP_IS_LZY_COMMERCE_VERSION
    unsigned char   b_tts_basic_Authorized;         //TTS功能授权状态：0=未授权且未试用，1=已授权，2=未授权但正在试用 3=未授权且试用期结束
    #endif
    bool            b_cloudControl_Authorized;      //云控制功能是否已经授权

    string          m_strBindIpAddress;             //绑定的IP地址
};

#endif // GLOBALDATA_H
