#include "stdafx.h"
#include "GlobalData.h"
#include "GlobalMethod.h"
#include <QDateTime>
#include <QDir>
#include <QFile>
#include <QFileInfo>
#include <pthread.h>
#include <unistd.h>

#define CUSTOM_HTTP_SERVER2

CGlobalData::CGlobalData()
{
    m_bLinkInternet = false;
    m_bStopInternetCheck = false;
    memset(&m_internetCheckThread, 0, sizeof(m_internetCheckThread));

    // 系统设置
    m_AppType		= APP_FORMAL;		// 如果修改成APP_LEBO，记得修改分区最大数MAX_SECTION_COUNT_FORMAL
    m_WorkPattern	= WP_CENTRALIZED;	// 默认工作模式（集中模式）
    m_NetworkMode	= NETWORK_UDP;		// 默认网络模式（UDP）

    // 分区歌曲列表共享/自定义（暂时不存在配置文件里）
    m_nZoneData		= ZONE_DATA_SHARED;

    // 高级设置
    m_nAutorun		= AUTORUN_TRUE;                 // 开机自动运行
    m_nMaxDevicesSync = DEVICE_SYNC_DEFAULT;        // 同时同步设备数目
    m_nZoneIcon		= ((m_AppType == APP_LEBO) ? ZONE_ICON_SMALL : ZONE_ICON_LARGE);  // 分区图标
    m_Audiocast		= AUDIOCAST_MULTICAST;          // 音频传输模式
    m_nSongsFrom	= SONGS_FROM_LOCAL;             // 歌曲来源
    m_nPlayChannel	= PLAY_CHANNEL_DEVICE;          // 播放通道
    m_bValidGps     = TRUE;                         // GPS是否有效
    m_HostGrade     = HOST_GRADE_1;                  // 主机等级
    m_bNeedResumeLocalPlay = TRUE;

    memset(m_szSongServerIP, 0,sizeof(m_szSongServerIP));  // 歌曲服务器IP
    memset(m_szHigherHostIP, 0,sizeof(m_szHigherHostIP));  // 上级服务器IP
    memset(m_ListenDevice, 0,sizeof(m_ListenDevice));  // 监听设备MAC

    // 指向所有设备的指针
    m_pAllDevices[DEVICE_SECTION]           = &m_Sections;
    m_pAllDevices[DEVICE_PAGER]             = &m_Pagers;
    m_pAllDevices[DEVICE_GPS]               = &m_GpsDevices;
    m_pAllDevices[DEVICE_AUDIO_COLLECTOR]   = &m_AudioCollectors;
    m_pAllDevices[DEVICE_FIRE_COLLECTOR]    = &m_FireCollectors;
    m_pAllDevices[DEVICE_SEQUENCE_POWER]    = &m_SequencePower;
    m_pAllDevices[DEVICE_AUDIO_MIXER]       = &m_AudioMixers;
    m_pAllDevices[DEVICE_REMOTE_CONTROLER]  = &m_RemoteControlers;
    m_pAllDevices[DEVICE_PHONE_DEVICE]      = &m_PhoneGateways;
    m_pAllDevices[DEVICE_AMP_CONTROLER]     = &m_AmpControlers;
    m_pAllDevices[DEVICE_NOISE_DETECTOR]    = &m_NoiseDetectors;
    m_pAllDevices[DEVICE_MOBILE]            = &m_ControlDevices;
    m_pAllDevices[DEVICE_SERVER]            = &m_Servers;
    m_pAllDevices[DEVICE_INTERCOM_STATION]  = &m_IntercomStations;

    //赋初值
    m_bValidNetCardId = 0;
    m_bValidNetCardCnt = 0;
    #if defined(Q_OS_WIN32)
    m_bTotalNetCardCnt = 0;
    memset(&m_netCardList,0,sizeof(m_netCardList));
    #endif

    memset(m_szNetworkIP,0,sizeof(m_szNetworkIP));
    //strcpy(m_szNetworkIP, CNetwork::GetHostIP());   // 本机网络地址


    m_bIsRunInClound = false;
    m_bEnableMonitor = false;
    m_bEnableCallRecord = false;
    m_bExamination_mode = false;
    m_bNmcliValid    = false;
    m_nPlayUnitTimeout  = 0;

    m_bCloudControl = false;

    ctime_t time0 = 0L;
    CTime timeT(time0);
    m_RegisterLimitDate = timeT;

    #if APP_IS_LZY_COMMERCE_VERSION
    b_tts_basic_Authorized = 0;  // 0=未授权且未试用
    #endif
    b_cloudControl_Authorized = false;
}

CGlobalData::~CGlobalData()
{
    // 停止网络检测线程
    StopInternetCheckThread();
}

// 初始化数据
bool CGlobalData::InitData(bool bRegister)
{
	 m_strHttpRootDir  = m_strFolderPath;

	m_strVoipServerIP = CNetwork::GetHostIP();                // VOIP_IP;

    #if SUPPORT_SOFTDOG
    //初始化加密狗
    if(!bRegister)
    {
        if(g_Global.m_softDog.Init())
        {
            bRegister = true;
        }
    }
    #endif

    // 设置应用程序的类型
    InitAppType(bRegister);
    printf("m_AppType=%d\n",m_AppType);
    InitDeviceData();
    CreateFileDirectory();
    InitAdvanceData();
    InitConfigData();
    UpdateFirmwareArray();

    SetFixedData();

    //清除90天前的系统日志（包含运行日志和日志数据库）
    g_Global.CleanLog(90);

#if defined(Q_OS_LINUX)
    #if SUPPORT_USB_GPS
    if(g_Global.m_usbGps.Init())
    {
        m_usbGps.StartWorking();
    }
    #endif
#endif

    return true;
}

// 清除数据库中超过指定天数的日志记录
void CGlobalData::ClearLogDataByDays(int nDays)
{
    if (nDays <= 0)
    {
        m_Log.PrintLog("ClearLogDataByDays: Invalid nDays parameter", LV_WARNING);
        return;
    }
    #if 0
    // 等待所有待处理的SQL命令执行完毕
    // 由于AddSQLCommand是异步执行的，我们需要等待队列清空
    int nWaitCount = 0;
    const int MAX_WAIT_COUNT = 20; // 最多等待5秒 (20 * 250ms)
    
    m_Log.PrintLog("ClearLogDataByDays: Waiting for pending SQL commands to complete...", LV_INFO);
    
    while (nWaitCount < MAX_WAIT_COUNT)
    {
        // 检查SQL命令队列是否为空（这里我们通过短暂等待来确保队列处理完毕）
        usleep(250000); // 等待250ms，与HandleSqlCommandThread的处理周期一致
        nWaitCount++;
        
        // 可以通过查询数据库来验证最新的插入是否已完成
        // 这里我们简单等待几个周期确保队列处理完毕
        if (nWaitCount >= 4) // 至少等待1秒
            break;
    }
    #endif
        
    // 计算截止日期
    CTime tNow = CTime::GetCurrentTimeT();
    CTime tToday(tNow.GetYear(), tNow.GetMonth(), tNow.GetDay(), 0, 0, 0);
    CTimeSpan span(nDays, 0, 0, 0);
    CTime tCutoff = tToday - span;
    
    // 格式化截止日期字符串
    CMyString strCutoffDate;
    strCutoffDate.Format("%04d-%02d-%02d", tCutoff.GetYear(), tCutoff.GetMonth(), tCutoff.GetDay());
    
    // 先查询要删除的记录数量
    CMyString strCountSQL;
    strCountSQL.Format("select count(*) from %s where Date < '%s'", "DeviceLog", strCutoffDate.C_Str());
    
    BaseData *countData = m_dblog.ExecuteSQL(strCountSQL.C_Str());
    int nDeleteCount = 0;
    if (countData != NULL && countData->nRow > 0)
    {
        nDeleteCount = atoi(countData->szResult[1]); // 第一行第一列是count结果
        sqlite3_free_table(countData->szResult);
        free(countData);
    }
    
    m_Log.PrintLog(m_Log.Format("ClearLogDataByDays: Found %d records to delete before %s", nDeleteCount, strCutoffDate.C_Str()), LV_INFO);
    
    if (nDeleteCount > 0)
    {
        // 删除超过指定天数的日志记录
        CMyString strDeleteSQL;
        strDeleteSQL.Format("delete from %s where Date < '%s'", "DeviceLog", strCutoffDate.C_Str());
        
        // 记录要执行的SQL语句用于调试
        m_Log.PrintLog(m_Log.Format("ClearLogDataByDays: Executing SQL: %s", strDeleteSQL.C_Str()), LV_INFO);
        
        if(m_dblog.DirectExecuteSQL(strDeleteSQL.C_Str()))
        {
            m_Log.PrintLog(m_Log.Format("ClearLogDataByDays: Successfully deleted %d log records before %s", nDeleteCount, strCutoffDate.C_Str()), LV_INFO);
            #if 0   //可能很耗时，不要回收数据库空间
            // 执行VACUUM命令回收数据库空间
            m_Log.PrintLog("ClearLogDataByDays: Executing VACUUM to reclaim database space...", LV_INFO);
            if(m_dblog.DirectExecuteSQL("VACUUM"))
            {
                m_Log.PrintLog("ClearLogDataByDays: VACUUM completed successfully, database space reclaimed", LV_INFO);
            }
            else
            {
                m_Log.PrintLog("ClearLogDataByDays: VACUUM failed, database space may not be reclaimed", LV_WARNING);
            }
            #endif
        }
        else
        {
            m_Log.PrintLog(m_Log.Format("ClearLogDataByDays: Failed to delete log data before %s", strCutoffDate.C_Str()), LV_ERROR);
        }
    }
    else
    {
        m_Log.PrintLog(m_Log.Format("ClearLogDataByDays: No records found to delete before %s", strCutoffDate.C_Str()), LV_INFO);
    }
}

// 初始化程序类型
void CGlobalData::InitAppType(bool bRegister)
{
    // 如果没有注册码
    if (!bRegister)
    {
        if(m_AppType != APP_SOFTDOG_DATE_INVALID && m_AppType != APP_REGISTER_INVALID )
        {
            m_AppType = APP_TRIAL;
        }
    }
}

// 设置一些固定配置
void CGlobalData::SetFixedData(void)
{
    // 北京乐播
    if (m_AppType == APP_LEBO)
    {
        m_nZoneData		= ZONE_DATA_OWN;		// 分区数据独享
        m_NetworkMode	= NETWORK_TCP;			// TCP
        m_WorkPattern	= WP_DISTRIBUTION;		// 分布模式
    }

    // 集中模式
    if (m_WorkPattern == WP_CENTRALIZED)
    {
        m_nSongsFrom	= SONGS_FROM_LOCAL;		// 只能是来自本地
        m_nZoneData		= ZONE_DATA_SHARED;		// 分区数据共有
        m_nPlayChannel	= PLAY_CHANNEL_DEVICE;	// 只能播放设备歌曲
    }

    // UDP，不需要网络地址
    if (m_NetworkMode != NETWORK_TCP)
    {
	 	strcpy(m_szNetworkIP, CNetwork::GetHostIP());
    }
}

// 初始化设备数据
void CGlobalData::InitDeviceData(void)
{
    // 设备类型
    m_Sections.SetDeviceType(DEVICE_SECTION);
    m_Pagers.SetDeviceType(DEVICE_PAGER);
    m_GpsDevices.SetDeviceType(DEVICE_GPS);
    m_AudioCollectors.SetDeviceType(DEVICE_AUDIO_COLLECTOR);
    m_FireCollectors.SetDeviceType(DEVICE_FIRE_COLLECTOR);
    m_ControlDevices.SetDeviceType(DEVICE_MOBILE);
    m_Servers.SetDeviceType(DEVICE_SERVER);
    m_IntercomStations.SetDeviceType(DEVICE_INTERCOM_STATION);
    m_SequencePower.SetDeviceType(DEVICE_SEQUENCE_POWER);
    m_AudioMixers.SetDeviceType(DEVICE_AUDIO_MIXER);
    m_RemoteControlers.SetDeviceType(DEVICE_REMOTE_CONTROLER);
    m_PhoneGateways.SetDeviceType(DEVICE_PHONE_DEVICE);
    m_AmpControlers.SetDeviceType(DEVICE_AMP_CONTROLER);
    m_NoiseDetectors.SetDeviceType(DEVICE_NOISE_DETECTOR);


    // 设备模型与设备数据的对应关系

    m_ModelToDevices[MODEL_IP_SPEAKER_A]             = &m_Sections;
    m_ModelToDevices[MODEL_IP_SPEAKER_B]             = &m_Sections;
    m_ModelToDevices[MODEL_IP_SPEAKER_C]             = &m_Sections;
    m_ModelToDevices[MODEL_IP_SPEAKER_D]             = &m_Sections;
    m_ModelToDevices[MODEL_IP_SPEAKER_E]             = &m_Sections;
    m_ModelToDevices[MODEL_IP_SPEAKER_F]             = &m_Sections;
    m_ModelToDevices[MODEL_IP_SPEAKER_G]             = &m_Sections;
    

    m_ModelToDevices[MODEL_PAGER_A]             = &m_Pagers;
    m_ModelToDevices[MODEL_PAGER_B]             = &m_Pagers;
    m_ModelToDevices[MODEL_PAGER_C]             = &m_Pagers;
    m_ModelToDevices[MODEL_GPS]                 = &m_GpsDevices;
    m_ModelToDevices[MODEL_AUDIO_COLLECTOR_A]   = &m_AudioCollectors;
    m_ModelToDevices[MODEL_FIRE_COLLECTOR_A]    = &m_FireCollectors;
    m_ModelToDevices[MODEL_MOBILE]              = &m_ControlDevices;
    m_ModelToDevices[MODEL_CONTROL_SERVER]      = &m_Servers;
    m_ModelToDevices[MODEL_INTERCOM_STATION]    = &m_IntercomStations;
    m_ModelToDevices[MODEL_SEQUENCE_POWER_A]    = &m_SequencePower;
    
    m_ModelToDevices[MODEL_AUDIO_COLLECTOR_B]   = &m_AudioCollectors;
    m_ModelToDevices[MODEL_FIRE_COLLECTOR_B]    = &m_FireCollectors;
    m_ModelToDevices[MODEL_SEQUENCE_POWER_B]    = &m_SequencePower;

    m_ModelToDevices[MODEL_REMOTE_CONTROLER]    = &m_RemoteControlers;

#if SUPPORT_AUDIO_MIXER
    m_ModelToDevices[MODEL_AUDIO_MIXER_DECODER] = &m_Sections;
    m_ModelToDevices[MODEL_AUDIO_MIXER_ENCODER] = &m_AudioMixers;

    m_ModelToDevices[MODEL_AUDIO_MIXER_DECODER_C] = &m_Sections;
    m_ModelToDevices[MODEL_AUDIO_MIXER_ENCODER_C] = &m_AudioMixers;
#endif

#if SUPPORT_PHONE_GATEWAY
    m_ModelToDevices[MODEL_PHONE_GATEWAY]    = &m_PhoneGateways;
#endif

#if SUPPORT_AMP_CONTROLER
    m_ModelToDevices[MODEL_AMP_CONTROLER]   = &m_AmpControlers;
#endif

#if SUPPORT_NOISE_DETECTOR
    m_ModelToDevices[MODEL_NOISE_DETECTOR]  = &m_NoiseDetectors;
#endif

    m_ModelToDevices[MODEL_AUDIO_COLLECTOR_C]   = &m_AudioCollectors;
    m_ModelToDevices[MODEL_FIRE_COLLECTOR_C]    = &m_FireCollectors;
    m_ModelToDevices[MODEL_SEQUENCE_POWER_C]    = &m_SequencePower;
    m_ModelToDevices[MODEL_REMOTE_CONTROLER_C]    = &m_RemoteControlers;

    m_ModelToDevices[MODEL_AUDIO_COLLECTOR_F]   = &m_AudioCollectors;
    m_ModelToDevices[MODEL_FIRE_COLLECTOR_F]    = &m_FireCollectors;
    m_ModelToDevices[MODEL_SEQUENCE_POWER_F]    = &m_SequencePower;
    m_ModelToDevices[MODEL_REMOTE_CONTROLER_F]    = &m_RemoteControlers;
}


// 创建文件路径 保留，待修改测试 zhuyg
void CGlobalData::CreateFileDirectory(void)
{
    CHAR	szPath[STR_MAX_PATH]			= {0};
    //CHAR	szRootPath[STR_MAX_PATH]		= {0};
    //CHAR	szHttpRootPath[STR_MAX_PATH]	= {0};
    CHAR	szLangPath[STR_MAX_PATH]		= {0};
    CHAR	szConfigPath[STR_MAX_PATH]		= {0};
    //CHAR	szLogPath[STR_MAX_PATH]			= {0};
    CHAR	szDownloadPath[STR_MAX_PATH]	= {0};

    // 得到根目录
    strcpy(szPath, m_strFolderPath.Data());

    // HTTP目录 添加Data目录
    CreateHttpDirectory(szPath, HTTP_FOLDER_ADATA);
    // Backup目录
    CreateHttpDirectory(szPath, HTTP_FOLDER_BACKUP);
    // images目录（用于龙之音商用版本切换主题）
    CreateHttpDirectory(szPath, HTTP_FOLDER_THEME_IMAGE);
    
    strcat(szPath, HTTP_FOLDER_SEPARATOR);
    strcat(szPath, HTTP_FOLDER_ADATA);

    CreateHttpDirectory(szPath, HTTP_FOLDER_XML);
    CreateHttpDirectory(szPath, HTTP_FOLDER_PLAYLIST_OWN);
    CreateHttpDirectory(szPath, HTTP_FOLDER_SECTION_OWN);
    CreateHttpDirectory(szPath, HTTP_FOLDER_UPDATE);
    CreateHttpDirectory(szPath, HTTP_FOLDER_PROGRAM);
    CreateHttpDirectory(szPath, HTTP_FOLDER_PROGRAM_COMMON);
    CreateHttpDirectory(szPath, HTTP_FOLDER_PROGRAM_MUSIC);
    CreateHttpDirectory(szPath, HTTP_FOLDER_PROGRAM_CHIME);
    CreateHttpDirectory(szPath, HTTP_FOLDER_PROGRAM_OTHER);
    CreateHttpDirectory(szPath, HTTP_FOLDER_UPDATE_FIRMWARE);
    CreateHttpDirectory(szPath, HTTP_FOLDER_UPDATE_UPGRADE);
    CreateHttpDirectory(szPath, HTTP_FOLDER_UPDATE_OTHER);
    CreateHttpDirectory(szPath, HTTP_FOLDER_XML_OTHER);
    CreateHttpDirectory(szPath, FOLDER_LOG);
    CreateHttpDirectory(szPath, FOLDER_DOWNLOAD);

    // 配置文件目录
    GetCurExePath(szConfigPath);
    strcat(szConfigPath, "/");
    strcat(szConfigPath, FOLDER_CONFIG);
	#if defined(Q_OS_LINUX)
    CreateDirectoryQ(szConfigPath);
	#endif
    strcat(szConfigPath, "/");
    strcat(szConfigPath, CONFIG_FILE_NAME);
    CMyString str = CMyString(szConfigPath);
    m_IniConfig.m_cFileName = CStringToChar(str);
    m_IniConfig.Read();

    // 语言文件目录
    GetCurExePath(szLangPath);
    strcat(szLangPath, "/");
    strcat(szLangPath, FOLDER_LANGUAGE);
	#if defined(Q_OS_LINUX)
    CreateDirectoryQ(szLangPath);
	#endif

    m_Language.InitLanguage(szLangPath);
    //强制设置默认语言
    //在C/C++中，我们不能直接比较两个宏定义，因为它们只是预处理期间的文本替换。
    printf("Set Default Language=%d\n",DEFAULT_LANGUAGE);
    if (DEFAULT_LANGUAGE == LANGUAGE_CN_ZH)
    {
        m_Language.SetCheckedLanguage("简体中文");
    }else if(DEFAULT_LANGUAGE == LANGUAGE_CN_TW)
    {
        m_Language.SetCheckedLanguage("繁体中文");
    }
    else if(DEFAULT_LANGUAGE == LANGUAGE_CN_EN)
    {
        m_Language.SetCheckedLanguage("English");
    }
#if defined(Q_OS_LINUX)
    m_Log.PrintLog(szLangPath, LV_INFO);
#endif
    // 创建日志文件目录
    //GetCurExePath(szLogPath);
    //strcat(szLogPath, "/");
    //strcat(szLogPath, FOLDER_LOG);
    //CreateDirectoryQ(szLogPath);
    CreateLogFile();

    // 创建下载文件夹
    //strcat(szDownloadPath, FOLDER_SOURCE);
    /*
    GetCurExePath(szDownloadPath);
    strcat(szDownloadPath, "/");
    strcat(szDownloadPath, FOLDER_DOWNLOAD);
    CreateDirectoryQ(szDownloadPath);
    strcat(szDownloadPath, "/");
    strcat(szDownloadPath, FOLDER_DOWNLOAD_LOGFILE);
    CreateDirectoryQ(szDownloadPath);
    */

    // 日志数据库数据创建
    CMyString strDataFile;
    strDataFile.Format((char*)("%s/%s/%s"), m_strFolderPath.Data(), HTTP_FOLDER_ADATA, DATABASE_SQLITE_LOG_NAME);

    if(!m_dblog.OpenData(strDataFile.Data()))
    {

    }
    else
    {
        m_logTable.SetDbManager(&m_dblog);
        m_logTable.Create();
        m_dblog.StartWorking();
    }

    // 用户数据库的创建
    CMyString strUserFile;
    strUserFile.Format((char*)("%s/%s/%s"), m_strFolderPath.Data(), HTTP_FOLDER_ADATA, DATABASE_SQLITE_USER_NAME);

    if(!m_dbuser.OpenData(strUserFile.Data()))
    {

    }
    else
    {
        m_userTable.SetDbManager(&m_dbuser);
        m_userTable.Create();
    }

    #if SUPPORT_CALL_RECORD
    // 录音数据库的创建
    CMyString strRecordFile;
    strRecordFile.Format((char*)("%s/%s/%s"), m_strFolderPath.Data(), HTTP_FOLDER_ADATA, DATABASE_SQLITE_RECORD_NAME);

    if(!m_dbrecord.OpenData(strRecordFile.Data()))
    {
        LOG("Failed to open record database", LV_ERROR);
    }
    else
    {
        m_RecordManager.SetDbManager(&m_dbrecord);
        if(!m_RecordManager.Initialize())
        {
            LOG("Failed to initialize record manager", LV_ERROR);
        }
    }
    #endif

    return;
}

// 初始化高级设置的数据 ini 获取 保留，待修改
void CGlobalData::InitAdvanceData(void)
{
    // 分区图片
    int nZoneIcon;
    if (m_IniConfig.GetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_ZONE_ICON, nZoneIcon))
    {
        m_nZoneIcon = nZoneIcon;
    }

    // 网络地址
    CMyString strNetworkIp;
    if (m_IniConfig.GetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_NETWORK_IP, strNetworkIp))
    {
        strcpy(m_szNetworkIP, strNetworkIp.Data());
    }

    // 设备同步数量
    int nDevicesSync;
    if (m_IniConfig.GetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_DEVICES_SYNC, nDevicesSync))
    {
        bool bWrite = FALSE;

        if (nDevicesSync < DEVICE_SYNC_MIN)
        {
            m_nMaxDevicesSync = DEVICE_SYNC_MIN;
            bWrite = TRUE;
        }
        else if (nDevicesSync > DEVICE_SYNC_MAX)
        {
            m_nMaxDevicesSync = DEVICE_SYNC_MAX;
            bWrite = TRUE;
        }
        else
        {
            m_nMaxDevicesSync = nDevicesSync;
        }

        if (bWrite)
        {
            m_IniConfig.SetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_DEVICES_SYNC, m_nMaxDevicesSync);
            m_IniConfig.Write();
        }
    }
#if 0
    // 音频传输
    int nAudiocast;
    if (m_IniConfig.GetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_AUDIOCAST, nAudiocast))
    {
        m_Audiocast = (Audiocast)nAudiocast;
    }
#endif
    // 歌曲来自
    int nSongFrom;
    if (m_IniConfig.GetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_SONGS_FROM, nSongFrom))
    {
        m_nSongsFrom = nSongFrom;
    }

    // 歌曲服务器IP
    CMyString strSongsServerIP;
    if (m_IniConfig.GetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_SONGS_SERVER_IP, strSongsServerIP))
    {
        strcpy(m_szSongServerIP, strSongsServerIP.Data());
    }

    // 端口
    int nSongSeverPort = 0;
    m_IniConfig.GetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_SONGS_SERVER_PORT, nSongSeverPort);
    m_nSongServerPort = (nSongSeverPort <= 0 ? DEFAULT_SONG_SERVER_PORT : nSongSeverPort);

    // 播放来自
    int nPlayChannel;
    if (m_IniConfig.GetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_PLAY_CHANNEL, nPlayChannel))
    {
        m_nPlayChannel = nPlayChannel;
    }

    // 是否自动设置时间
    bool bAutoTime;
    if(m_IniConfig.GetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_AUTOTIME, bAutoTime))
    {
        m_bValidGps = bAutoTime;
    }

    // 监听设备MAC
    CMyString strListenDevice;
    if(m_IniConfig.GetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_LISTEN_DEVICE, strListenDevice))
    {
        strcpy(m_ListenDevice, strListenDevice.Data());
    }


    // 服务器等级
    int nGrade;
    if(m_IniConfig.GetValue(CONFIG_FILE_SECTION_SERVER, CONFIG_FILE_ITEM_HOST_GRADE, nGrade))
    {
        m_HostGrade = (HOST_GRADE)nGrade;
    }

    // 上级主机IP
    CMyString strHigherHostIP;
    if (m_IniConfig.GetValue(CONFIG_FILE_SECTION_SERVER, CONFIG_FILE_ITEM_HIGHER_HOST_IP, strHigherHostIP))
    {
        strcpy(m_szHigherHostIP, strHigherHostIP.Data());
    }

    //KCP端口
    if (m_IniConfig.GetValue(CONFIG_FILE_SECTION_COMM_PORT, CONFIG_FILE_SECTION_COMM_KCP_PORT, m_KCP_PORT) == false)
    {
        //写入默认值
        m_KCP_PORT = UDP_STREAM_PORT_DEFAULT;
        m_IniConfig.SetValue(CONFIG_FILE_SECTION_COMM_PORT, CONFIG_FILE_SECTION_COMM_KCP_PORT, m_KCP_PORT);
    }

    //TCP端口
    if (m_IniConfig.GetValue(CONFIG_FILE_SECTION_COMM_PORT, CONFIG_FILE_SECTION_COMM_TCP_PORT, m_TCP_PORT) == false)
    {
        //写入默认值
        m_TCP_PORT = TCP_PORT_DEFAULT;
        m_IniConfig.SetValue(CONFIG_FILE_SECTION_COMM_PORT, CONFIG_FILE_SECTION_COMM_TCP_PORT, m_TCP_PORT);
    }

    //WEB端口
    if (m_IniConfig.GetValue(CONFIG_FILE_SECTION_COMM_PORT, CONFIG_FILE_SECTION_COMM_WEB_PORT, m_WEB_PORT) == false)
    {
        //写入默认值
        m_WEB_PORT = WEB_PORT_DEFAULT;
        m_IniConfig.SetValue(CONFIG_FILE_SECTION_COMM_PORT, CONFIG_FILE_SECTION_COMM_WEB_PORT, m_WEB_PORT);
    }
    
    //HTTP端口
    if (m_IniConfig.GetValue(CONFIG_FILE_SECTION_COMM_PORT, CONFIG_FILE_SECTION_COMM_HTTP_PORT, m_HTTP_PORT) == false)
    {
        //写入默认值
        m_HTTP_PORT = HTTP_PORT_DEFAULT;
        m_IniConfig.SetValue(CONFIG_FILE_SECTION_COMM_PORT, CONFIG_FILE_SECTION_COMM_HTTP_PORT, m_HTTP_PORT);
    }

}

// 创建程序运行日志文件
void CGlobalData::CreateLogFile(void)
{
    CHAR        szPathName[STR_MAX_PATH] = {0};
    CTime       ct = CTime::GetCurrentTimeT();
    CMyString   strName;

    // 得到日志文件夹路径
    /*GetCurExePath(szPathName);
    strcat(szPathName, "/");
    strcat(szPathName, FOLDER_LOG);
    strcat(szPathName, "/");*/

    strName.Format((char*)("Log_%04d%02d%02d.txt"), ct.GetYear(), ct.GetMonth(), ct.GetDay());
    // 通过目录与文件名合成路径
    //GetFilePathName(szPathName, FOLDER_LOG, strName.C_Str());
    //strcat(szPathName, strName.C_Str());
    CombinHttpURL(szPathName, FOLDER_LOG, strName.C_Str());

    //LOG(szPathName, LV_INFO);
    m_Log.PrintLog(szPathName, LV_ERROR);
    m_Log.InitLogFile(szPathName);
    m_Log.SetWriteTime(FALSE);
    m_Log.WriteLog("***APP: %d-%d-%d %d:%d:%d ***", ct.GetYear(), ct.GetMonth(), ct.GetDay(),ct.GetHour(), ct.GetMinute(), ct.GetSecond());
    #if(STREAM_ALLWAYS_USED_TCP)
    m_Log.WriteLog("Stream force tcp mode!");
    #endif
    m_Log.SetWriteTime(TRUE);

}

// 设置是否自动设置时间(GPS)
void CGlobalData::SetAutoTime(bool bAutoTime)
{
    if(m_bValidGps != bAutoTime)
    {
        if(m_IniConfig.SetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_AUTOTIME, bAutoTime))
        {
            m_bValidGps = bAutoTime;
        }
        m_IniConfig.Write();
    }
}

// 重置高级设置
void CGlobalData::ResetAdvanceSettings(LPCSTR  szServerIP,        // 服务器地址
                                       WorkPattern wp,			  // 工作模式
                                       NetworkMode nm,			  // 网络模式
                                       Audiocast   ac,			  // 音频传输
                                       int		nSyncCount,		  // 同步设备数量
                                       int      nSongFrom,        // 歌曲来自
                                       LPCSTR	szSongServerIP,   // 歌曲服务器地址
                                       int      nSongServerPort,  // 歌曲服务器端口
                                       int     	nPlayChannel)     // 播放通道
{
    // 本机网络地址
    if(strcmp(szServerIP, m_szNetworkIP) != 0)
    {
        strcpy(m_szNetworkIP, szServerIP);
    }

    bool IsChange = false;
    // 同步设备数目
    nSyncCount = (nSyncCount < DEVICE_SYNC_MIN ? DEVICE_SYNC_MIN : nSyncCount);
    nSyncCount = (nSyncCount > DEVICE_SYNC_MAX ? DEVICE_SYNC_MAX : nSyncCount);
    if (nSyncCount != m_nMaxDevicesSync)
    {
        m_nMaxDevicesSync = nSyncCount;
        m_IniConfig.SetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_DEVICES_SYNC, nSyncCount);
        IsChange = true;
    }

    // 工作模式
    if(m_WorkPattern != wp)
    {
        // 工作模式保存到本地
        m_WorkPattern = wp;
        m_IniConfig.SetValue(CONFIG_FILE_SECTION_SECTION, CONFIG_FILE_ITEM_WORK_PATTERN, m_WorkPattern);
        IsChange = true;

        // 播放队列要清空
        m_PlayQueue.Clear();

        // 所有设备都要置为空闲和改变工作模式
        m_Network.DeviceChangeWorkPattern();
        SetFixedData();
    }

    // 网络模式
    if (m_NetworkMode != nm)
    {
        // 网络模式保存在本地
        m_NetworkMode = nm;
        m_IniConfig.SetValue(CONFIG_FILE_SECTION_SECTION, CONFIG_FILE_ITEM_NETWORK_MODE, m_NetworkMode);
        IsChange = true;
    }

    // 音频传输
    if (m_Audiocast != ac)
    {
        // 音频传输方式保存在本地
        m_Audiocast = ac;
        m_IniConfig.SetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_AUDIOCAST, ac);
        IsChange = true;

        // 设备改变音频传输方式
        m_Network.DeviceChangeAudiocast();
    }

    // 歌曲来自
    if(m_nSongsFrom != nSongFrom)
    {
        m_nSongsFrom = nSongFrom;
        m_IniConfig.SetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_SONGS_FROM, nSongFrom);
        IsChange = true;
    }

    // 歌曲服务器IP
    if(strcmp(m_szSongServerIP, szSongServerIP) != 0)
    {
        strcpy(m_szSongServerIP, szSongServerIP);
        m_IniConfig.SetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_SONGS_SERVER_IP, m_szSongServerIP);
        IsChange = true;
    }

    // 歌曲服务器端口
    nSongServerPort = (nSongServerPort <= 0 ? DEFAULT_SONG_SERVER_PORT : nSongServerPort);
    if(m_nSongServerPort  != nSongServerPort)
    {
        m_nSongServerPort = nSongServerPort;
        m_IniConfig.SetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_SONGS_SERVER_PORT, m_nSongServerPort);
        IsChange = true;
    }

    // 播放来自
    if(m_nPlayChannel != nPlayChannel)
    {
        m_nPlayChannel = nPlayChannel;
        m_IniConfig.SetValue(CONFIG_FILE_SECTION_ADVANCE, CONFIG_FILE_ITEM_PLAY_CHANNEL, m_nPlayChannel);
        IsChange = true;
    }

    if(IsChange)
    {
        m_IniConfig.Write();
    }
}

// 初始化默认配置信息
void CGlobalData::InitDefaultConfig()
{
    // 添加本地AUX到音频采集器
    bool IsChange = false;
	const char* szIP = CNetwork::GetHostIP();
    const char* szMac = CNetwork::GetHostMac();
    if(strlen(szIP) == 0 || strlen(szMac) == 0)
    {
        return;
    }

    ctime_t		tNow		= CTime::GetCurrentTimeT().GetTime();
    CSection* pSection = m_AudioCollectors.GetSectionByMac(szMac);
    if(pSection == NULL)
    {
        CSection* pNewDevice = new CSection(m_AudioCollectors.GetSecCount() + 1,
                                            MODEL_AUDIO_COLLECTOR_A,
                                            szMac,
                                            szIP,
                                            0,
                                            PRO_OFFLINE,
                                            szIP);

        pNewDevice->SetVersion("");
        pNewDevice->SetLatestTime(tNow);

        m_AudioCollectors.AddSection(*pNewDevice);
        delete pNewDevice;
        IsChange = true;
    }
    else
    {
        // IP改变
        if (strcmp(szIP, pSection->GetIP()) != 0)
        {
            // IP与分区名称一样
            if (strcmp(pSection->GetName(), pSection->GetIP()) == 0)
            {
                pSection->SetName(szIP);
            }

            m_AudioCollectors.UpdateIp(*pSection,szIP);	// IP改变，更新与IP与ID对应关系
            IsChange = true;
        }
    }

    if(IsChange)
    {
        WriteXmlFile(FILE_AUDIO_COLLECTOR);
    }
}


// 初始化配置数据
void CGlobalData::InitConfigData(void)
{
    #if 0
    // 网络模式（默认是UDP）
    int nNetworkMode;
    if (m_IniConfig.GetValue(CONFIG_FILE_SECTION_SECTION, CONFIG_FILE_ITEM_NETWORK_MODE, nNetworkMode))
    {
        m_NetworkMode = (NetworkMode)nNetworkMode;
    }

    // 工作模式（默认是集中模式）
    int nWorkPattern;
    if (m_IniConfig.GetValue(CONFIG_FILE_SECTION_SECTION, CONFIG_FILE_ITEM_WORK_PATTERN, nWorkPattern))
    {
        m_WorkPattern = (WorkPattern)nWorkPattern;
    }
    #endif

    // 播放模式
    int nPlayMode = PM_ORDER;
    m_IniConfig.GetValue(CONFIG_FILE_SECTION_SECTION, CONFIG_FILE_ITEM_PLAY_MODE, nPlayMode);
    m_PlayList.SetPlayMode((PlayMode)nPlayMode);

    // 云服务器标志
    m_bIsRunInClound=false;
    m_IniConfig.GetValue(CONFIG_FILE_SECTION_SECTION, CONFIG_FILE_ITEM_CLOUND_SERVER, m_bIsRunInClound);

    // 启用监控标志(默认关闭)
    m_bEnableMonitor=false;
    m_IniConfig.GetValue(CONFIG_FILE_SECTION_SECTION, CONFIG_FILE_ITEM_MONITOR, m_bEnableMonitor);

    // 考试模式（默认关闭）
    m_bExamination_mode=false;
    m_IniConfig.GetValue(CONFIG_FILE_SECTION_SECTION, CONFIG_FILE_ITEM_EXAMINATION_MODE, m_bExamination_mode);

    // 云控制（默认关闭）
    m_bCloudControl = false;
    m_IniConfig.GetValue(CONFIG_FILE_SECTION_SECTION, CONFIG_FILE_ITEM_CLOUD_CONTROL, m_bCloudControl);

    // 通话录音（默认关闭）
    m_bEnableCallRecord = false;
    m_IniConfig.GetValue(CONFIG_FILE_SECTION_SECTION, CONFIG_FILE_ITEM_ENABLE_CALL_RECORD, m_bEnableCallRecord);
}

// 初始化本地分控数据
void CGlobalData::InitLocalHostData()
{
    // 服务器别名
    CMyString strHostName;;
    if(m_IniConfig.GetValue(CONFIG_FILE_SECTION_SERVER, CONFIG_FILE_ITEM_HOST_NAME, strHostName))
    {
        m_LocalHost.SetName(strHostName.C_Str());
    }

    // 服务器音量
    int nVolume;
    if (m_IniConfig.GetValue(CONFIG_FILE_SECTION_SERVER, CONFIG_FILE_ITEM_HOST_VOLUME, nVolume))
    {
        m_LocalHost.SetVolume(nVolume);
    }
    m_LocalHost.SetMac(CNetwork::GetHostMac());
}

// 获取文件中的所有固件名称
void CGlobalData::UpdateFirmwareArray()
{
    CMyString strFirmwarePath;
    strFirmwarePath.Format("%s/%s/%s", m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_UPDATE_FIRMWARE);

    m_Firmwares.clear();
    DIR * dir;
    struct dirent *ptr;
    dir = opendir((char*)strFirmwarePath.Data());       // 打开一个目录
    if(dir == NULL)
    {
        perror("opendir failed : ");
        return ;
    }
    while((ptr = readdir(dir)) != NULL)       // 循环读取目录数据
    {
        if(strcmp(ptr->d_name,".") == 0 || strcmp(ptr->d_name,"..") == 0)
            continue;

        #if defined(Q_OS_LINUX)
        m_Firmwares.push_back(ptr->d_name);
        #else
        string strUtf8Name = StringToUTF8(ptr->d_name);
        m_Firmwares.push_back(strUtf8Name.data());
        #endif
    }
    closedir(dir);
}


// 删除服务器中所有的升级固件
void CGlobalData::CleanFirmware()
{
    CMyString strFirmwarePath;
    strFirmwarePath.Format("%s/%s/%s", m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_UPDATE_FIRMWARE);

    RemoveDirectoryFile(strFirmwarePath.C_Str(),false);

    m_Firmwares.clear();
}

// 删除服务器中所有的日志文件
void CGlobalData::CleanLog(int nDays)
{
    if (nDays == 0)
    {
        // 清除所有日志文件，保持原有逻辑
        //清空日志目录文件
        CMyString strPathName;
        strPathName.Format("%s/%s/%s", m_strFolderPath.Data(), HTTP_FOLDER_ADATA, FOLDER_LOG);
        RemoveDirectoryFile(strPathName.C_Str(),false);

        CMyString strDataFile;
        strDataFile.Format((char*)("%s/%s/%s"), g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, DATABASE_SQLITE_LOG_NAME);
        g_Global.m_dblog.CloseData();

        //删除文件
        RemoveFile(strDataFile.Data());

        if(!m_dblog.OpenData(strDataFile.Data()))
        {

        }
        else
        {
            m_logTable.SetDbManager(&m_dblog);
            m_logTable.Create();
        }
    }
    else
    {
        // 清除指定天数之前的日志文件
        CMyString strLogPath;
        strLogPath.Format("%s/%s/%s", m_strFolderPath.Data(), HTTP_FOLDER_ADATA, FOLDER_LOG);
        
        QDir logDir(strLogPath.C_Str());
        if (logDir.exists())
        {
            QDateTime currentTime = QDateTime::currentDateTime();
            QDateTime cutoffTime = currentTime.addDays(-nDays);
            
            QFileInfoList fileList = logDir.entryInfoList(QDir::Files);
            for (int i = 0; i < fileList.size(); i++)
            {
                QFileInfo fileInfo = fileList.at(i);
                if (fileInfo.lastModified() < cutoffTime)
                {
                    QFile::remove(fileInfo.absoluteFilePath());
                }
            }
        }
        
        // 清除数据库中超过指定天数的日志记录
        ClearLogDataByDays(nDays);
    }
}

// 写入xml文件，通知所有大屏与web socket
bool CGlobalData::WriteXmlFile(FileType fileType, bool bNotifyDeviceUpdate)
{
    bool    IsSuccess = FALSE;
    switch (fileType) {
    case FILE_GROUP:
        IsSuccess = m_Groups.WriteGroupFile(HTTP_FILE_GROUP);
        break;
    case FILE_PLAYLIST:
        IsSuccess = m_PlayList.WriteFile();
        break;
    case FILE_TIMER:
        IsSuccess = m_TimerScheme.WriteTimerFile(HTTP_FILE_TIMER);
        break;
    case FILE_SECTION:
        IsSuccess = m_Sections.WriteFile(true);
        break;
    case FILE_AUDIO_COLLECTOR:
        IsSuccess = m_AudioCollectors.WriteFile(true);
        break;
    case FILE_FIRE_COLLECTOR:
        IsSuccess = m_FireCollectors.WriteFile(true);
        break;
    case FILE_MONITOR:
        IsSuccess = m_Monitors.WriteFile(HTTP_FILE_MONITOR);
        break;
    case FILE_SEQUENCE_POWER:
        IsSuccess = m_SequencePower.WriteFile(true);
        break;
    case FILE_PAGER:
        IsSuccess = m_Pagers.WriteFile(true);
        break;
    #if SUPPORT_REMOTE_CONTROLER
    case FILE_REMOTE_CONTROLER:
        IsSuccess = m_RemoteControlers.WriteFile(true);
        break;
    #endif
    #if SUPPORT_AUDIO_MIXER
    case FILE_AUDIO_MIXER:
        IsSuccess = m_AudioMixers.WriteFile(true);
    break;
    #endif
    #if SUPPORT_PHONE_GATEWAY
    case FILE_PHONE_GATEWAY:
        IsSuccess = m_PhoneGateways.WriteFile(true);
    break;
    #endif
    #if SUPPORT_AMP_CONTROLER
    case FILE_AMP_CONTROLER:
        IsSuccess = m_AmpControlers.WriteFile(true);
    break;
    #endif
    #if SUPPORT_NOISE_DETECTOR
    case FILE_NOISE_DETECTOR:
        IsSuccess = m_NoiseDetectors.WriteFile(true);
    break;
    #endif

    case FILE_GPS:
        IsSuccess = m_GpsDevices.WriteFile(true);
    break;

    default:
        break;
    }

    if(bNotifyDeviceUpdate && IsSuccess)        // 是否通知设备
    {
        if(fileType != FILE_SECTION)    //分区文件变化不需要通知WEB，因为每一个分区状态变化就会有状态指令下发到WEB
            m_WebNetwork.ForwardUpdateFileInfo(fileType);
    }

    if(IsSuccess)
    {
        if(fileType != FILE_SECTION)
        {
            CMyString strTip;
            strTip.Format("update file : %s", CProtocol::GetDescriptionFileType(fileType).C_Str());
            m_Network.AddLog(strTip.C_Str());
        }
    }
    else
    {
        m_Log.PrintLog(m_Log.Format("FileType : %d  write xml failed\n", fileType), LV_ERROR);
    }

    return IsSuccess;
}

void CGlobalData::ReadLocalFileInfo(void)
{
    // 用户数据库
    m_Users.InitUserData();

    // 分区设备文件
    if (!m_Sections.ReadFile())
    {
        m_Sections.WriteFile(true);
        //m_Sections.WriteSecMonitorFile(HTTP_FILE_SECMONITOR);
    }

    // 音频采集器文件
    if (!m_AudioCollectors.ReadFile())
    {
        m_AudioCollectors.WriteFile(true);
    }


    // 消防采集器文件
    if (!m_FireCollectors.ReadFile())
    {
        m_FireCollectors.WriteFile(true);
    }

    // 电源时序器文件
    if (!m_SequencePower.ReadFile())
    {
        m_SequencePower.WriteFile(true);
    }

    // 寻呼台文件
    if (!m_Pagers.ReadFile())
    {
        m_Pagers.WriteFile(true);
    }

    // 分组文件
    if (!m_Groups.ReadGroupFile(HTTP_FILE_GROUP))
    {
        m_Groups.WriteGroupFile(HTTP_FILE_GROUP);
    }

    // 定时文件
    if (!m_TimerScheme.ReadTimerFile(HTTP_FILE_TIMER))
    {
        CMyString strName = LANG_STR(LANG_SECTION_TIMING, "Timing Scheme", CMyString("定时方案"));
        m_TimerScheme.AddScheme(strName);
        m_TimerScheme.SetCurScheme(0);
        m_TimerScheme.WriteTimerFile(HTTP_FILE_TIMER);
    }

    // 监控文件
    if(!m_Monitors.ReadFile(HTTP_FILE_MONITOR))
    {
        m_Monitors.WriteFile(HTTP_FILE_MONITOR);
    }

    #if SUPPORT_REMOTE_CONTROLER
    // 远程遥控器文件
    if(!m_RemoteControlers.ReadFile())
    {
        m_RemoteControlers.WriteFile(true);
    }
    #endif

    #if SUPPORT_AUDIO_MIXER
    // 混音器文件
    if(!m_AudioMixers.ReadFile())
    {
        m_AudioMixers.WriteFile(true);
    }
    #endif

    #if SUPPORT_PHONE_GATEWAY
    // 电话网关文件
    if(!m_PhoneGateways.ReadFile())
    {
        m_PhoneGateways.WriteFile(true);
    }
    #endif

    #if SUPPORT_AMP_CONTROLER
    // 功放控制器文件
    if(!m_AmpControlers.ReadFile())
    {
        m_AmpControlers.WriteFile(true);
    }
    #endif

    #if SUPPORT_NOISE_DETECTOR
    // 噪声检测文件
    if(!m_NoiseDetectors.ReadFile())
    {
        m_NoiseDetectors.WriteFile(true);
    }
    #endif

    // GPS文件
    if(!m_GpsDevices.ReadFile())
    {
        m_GpsDevices.WriteFile(true);
    }

    //********校正用户分区-分组 //清理xml已删除但用户还存在的分区
    m_Users.CleanSection();

    //********校正用户分区-分组
    for(int i=0;i<m_Users.GetUserCount();i++)
    {
        g_Global.m_Groups.UpdateGroupSection(m_Users.GetUser(i)->GetAccount());
    }
    
   #if 0    //可能会导致分区定时等信息被误清除，等后面分区上线再处理
    //******** 剔除MAC重复的设备（优先保留非分区设备，因为其他类型设备可能含配置文件)
    vector<st_devMacType> vecDevMacType;
    for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
    {
        st_devMacType devMacType;
        int nDeviceCount = g_Global.m_pAllDevices[i]->GetSecCount();
        for (int j=0; j<nDeviceCount; ++j)
        {
            devMacType.devMac = g_Global.m_pAllDevices[i]->GetSection(j).GetMac();
            devMacType.devType = g_Global.m_pAllDevices[i]->GetSection(j).GetDevType();

            //遍历vector，判断之前的设备类型有没有mac重复,如果已经存在，需要删除前面的
            auto iterVec=vecDevMacType.begin();
            for(;iterVec!=vecDevMacType.end();)
            {
                if((*iterVec).devMac == devMacType.devMac)
                {
                    int devType = (*iterVec).devType;
                    if(devType<DEVICE_TYPE_COUNT)
                    {
                        printf("Repeat:mac=%s,devType=%d\n",(*iterVec).devMac.data(),devType);
                        CSections* pDevices = g_Global.m_pAllDevices[devType];
                        pDevices->RemoveSpecDevice(devMacType.devMac);
                    }
                    vecDevMacType.erase(iterVec);
                }
                else
                {
                    iterVec++;
                }
            }

            vecDevMacType.push_back(devMacType);
        }
    }
    #endif

    //放到这里再初始化读取user.xml文件
    g_Global.m_Users.ReadUserFile(HTTP_FILE_USER);
}

// 初始化播放列表
void CGlobalData::InitPlaylist(bool bReset)
{
    m_PlayList.ClearList();

    // 本地
    if (m_nSongsFrom == SONGS_FROM_LOCAL)
    {
        m_PlayList.SetFileName(HTTP_FILE_PLAYLIST);

        // 播放列表文件
        if(!m_PlayList.ReadFile() || bReset)
        {
            m_PlayList.ResetList();
        }
        else
        {
            char szPath[1024] = {0};
            sprintf(szPath, "%s/%s/%s", m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM);
            #if SUPPORT_SONG_MANAGER
            //20230610 在m_SongManager读取前先初始化（主要就是，如果说lbrc压缩率变更了，那么需要删除掉以前的压缩编码歌曲文件）
            m_SongManager.Init();
            m_SongManager.ReadFile();
            //为了节约资源，如果之前songlist.xml已经存储过歌曲，那么重新上电就不重新扫描了，主要是md5比较耗时
            m_PlayList.TabListSongFileExist(szPath,g_Global.m_SongManager.GetSongsCount() == 0 ? true:false);
            #else
            m_PlayList.TabListSongFileExist(szPath,false);
            #endif
        }
       
    }
    // 音乐服务器
    else
    {
        m_PlayList.SetFileName(HTTP_FILE_PLAYLIST_NETWORK);

        if(!m_PlayList.ReadFile())
        {

        }

        //m_Network.TcpClientStartWorking();
    }

    // 当本机等级为2级服务器时，初始化上级主机播放列表
    if(m_HostGrade == HOST_GRADE_2)
    {
        m_HigherHost.m_Playlist.ClearList();
        m_HigherHost.m_Playlist.SetFileName(HTTP_FILE_PLAYLIST_HOST);

        if(!m_HigherHost.m_Playlist.ReadFile())
        {

        }
    }

    //20230613
    //启动playlistXMl更新线程
    m_PlayList.StartPlaylistXmlUpgradeThread();
    //初始化用户播放列表（一定要在用户初始化完成后调用）
    m_Users.InitUserPlaylist();
}

// 网络检测线程函数
void* CGlobalData::InternetCheckThreadProc(void* lpParam)
{
    CGlobalData* pThis = (CGlobalData*)lpParam;
    
    while (!pThis->m_bStopInternetCheck)
    {
        // 检测网络连接状态
        bool bCurrentStatus = IsLinkNetwork();
        
        // 如果状态发生变化，更新变量并记录日志
        if (pThis->m_bLinkInternet != bCurrentStatus)
        {
            pThis->m_bLinkInternet = bCurrentStatus;
            
            CMyString strLog;
            strLog.Format("Internet connection status changed: %s", 
                         bCurrentStatus ? "Connected" : "Disconnected");
            pThis->m_Log.PrintLog(strLog.C_Str(), LV_INFO);
            g_Global.m_logTable.InsertLog(	SUPER_USER_NAME, SUPER_USER_NAME, LT_ADVANCED_LOG, strLog);
        }
        
        // 等待10秒
        for (int i = 0; i < 100 && !pThis->m_bStopInternetCheck; i++)
        {
            usleep(100000); // 100ms * 100 = 10秒，分段检查停止标志
        }
    }
    
    pthread_exit(NULL);
    return NULL;
}

// 启动网络检测线程
void CGlobalData::StartInternetCheckThread()
{
    m_bStopInternetCheck = false;
    
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    
    int ret = pthread_create(&m_internetCheckThread, &attr, InternetCheckThreadProc, this);
    if (ret != 0)
    {
        m_Log.PrintLog("Failed to create internet check thread", LV_ERROR);
    }
    else
    {
        m_Log.PrintLog("Internet check thread started successfully", LV_INFO);
    }
    
    pthread_attr_destroy(&attr);
}

// 停止网络检测线程
void CGlobalData::StopInternetCheckThread()
{
    m_bStopInternetCheck = true;
}











