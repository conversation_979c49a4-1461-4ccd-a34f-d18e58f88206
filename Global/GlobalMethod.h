#ifndef GLOBALMETHOD_H
#define GLOBALMETHOD_H

#include <QtCore/qglobal.h>

#include "Tools/CMyString.h"
#include "Tools/CTime.h"
#include <regex>
#include <dirent.h>
#include <sys/types.h>
#if defined(Q_OS_LINUX)
#include <sys/wait.h>
#include <openssl/md5.h>
#include "fcntl.h"
#endif
#include <fstream>
#include <string>
#include <string.h>
#include <qhostaddress.h>

#include "CType.h"

#define CODING_GB2312	936
#define BUF_MAX_LEN 1024

// utf8 -> gb2312
CMyString ConvertUTF8toGB2312(const char *pData, size_t size);

// gb2312 -> utf8
CMyString ConvertGB2312toUTF8(const char *pData, size_t size);

// 判断字符串是否为UTF8格式
bool IsTextUTF8(const char* str,ulong length);

// 字符串转成utf8格式
string  StringToUTF8(const char* pData);

// 字符串转成gb2312格式
string  StringToGB2312(const char* pData);

// CMyString 字符串转成char* 字符串，返回字符串内存得手动删除 保留，待修改 zhuyg
char*	CStringToChar(CMyString& str);


// 判断字符串为日期时间格式
bool    IsDateTimeFormat(const char* szDateTime);

// 检查日期格式
bool    IsDateFormat(const char* szDate);

// 检查时间格式
bool    IsTimeFormat(const char* szTime);

// 判断字符串是否为IP格式
bool    IsIPFormat(const char* szIP);

// 判断字符串是否为合法域名（格式+长度+TLD）
bool IsDomainFormat(const char* szDomain);

// 判断字符串是否为数字加逗号的格式
bool    IsTelNumberAndCommaFormat(const char* str);

// 判断字符是否为Mac格式
bool    IsMacFormat(const char* szMac);

// 判断字符串是否为GUID格式
bool    IsGUIDFormat(const char* szGuid);

// String  转成 CMyString
CMyString StringToCString(string& str);


// CMyString 转成 String
string  CStringToString(CMyString& cstr);


// char数组转成SHORT类型
unsigned short	CharsToShort(const char* pData);


// char数组转成int类型
int		CharsToInt(const char* pData);


//将字符串的IP转换为unsigned long
unsigned long   GetIpDword(const char* szIP);


// 得到当前的日期和时间，转换为字符串
CMyString GetCurrentDateTime(void);

// 设置系统时间
void    SetSystemTime_Linux(CTime &ctime);
// 设置系统时间
void    SetSystemTime_Linux(SYSTEMTIME_Q &st);

std::tm convertStrDateTimeToTM(std::string& dateTimeStr);
int SetSystemTime_hardware(time_t gpsTimestamp, bool bIndependent = false);

// 得到GUID
CMyString GetGUID(void);


// 判断本系统是否已经注册（目前使用这个）
bool    HasRegister();


// 截取节目名称
CMyString InterceptionProName(const char* szProName, bool& bIntercept);


/****************************************************/

// 得到运行程序路径
void	GetCurExePath(char* szPath);

// 得到文件绝对路径名
void	GetFilePathName(char* szPathName, string strFolder, string strName);

// 从绝对路径名得到文件名
CMyString GetNameByPathName(CMyString strPathName, bool hasExtension = false);

// 从升级文件路径名得到文件版本号
CMyString	GetVersionByPathName(CMyString strPathName);


/******************************************************/

// 创建文件夹
bool    CreateDirectoryQ(string Path);

// 创建HTTP目录
void    CreateHttpDirectory(char* szPath, string strFolder);

// 判断文件是否存在
bool    IsExistFile(const char* szPath);

// 判断目录是否存在
bool    IsExistDir(const char *szPath);

// 移动文件
bool    FileMove(string srcPath, string desPath);

// 获取文件大小
ulong  GetFileSize(const char* szPathName);

// 移除指定文件
bool    RemoveFile(const char* szPath);

// 移除文件夹内所有文件
void    RemoveDirectoryFile(const char* szPath,bool delSelf);

// 移除文件夹内所有文件(包括文件夹本身)
void    RemoveDirectoryALL(const char * path);

// 移除文件夹内所有后缀的所有文件（包含子目录）
void RemoveDirectoryAllFilesByExt(const char* dirPath, const char* extension, bool bSubDir);

// 组成可远程下载的文件URL 保留，待修改
void    CombinHttpURL(char* szPathName, string strFolder, string strName);

// 从文件路径得到URL 保留，待修改
CMyString GetHttpURLByPath(CMyString strPath, const char* szServerIP, int nPort);

// 从本地文件路径名得到URL路径部分 保留，待修改
CMyString GetHttpURLPathByPathName(CMyString strPathName);

// 从URL路径部分得到本地文件路径 保留，待修改
CMyString GetPathNamePathByHttpURL(CMyString strHttpURL);

// 从HTTP路径名得到文件名（去掉后缀名）
CMyString GetNameByHttpPathName(CMyString strPathName);

// 从HTTP路径名得到文件名（包括后缀名）
CMyString GetNameWithExtensionByHttpPathName(CMyString strPathName);

// 从HTTP路径名去掉文件名（包括后缀名）
CMyString GetPathByHttpPathName(CMyString strPathName);

// 获取指定目录下所有文件名称
void    GetDirAllFileName(CMyString strPath, vector<string>& vecFileNames);

/*****************************************************************/

CMyString ItemCannotBeEmpty(const char* szSection, const char* szItem, CMyString strDefault = NULL);
CMyString ItemCannotBeExceed(const char* szSection, const char* szItem, CMyString strDefault = NULL);

/************************************************************/

// 是否连接网络
bool    IsLinkNetwork();

// 设置文件权限
void    MyChmod();

#if defined(Q_OS_LINUX)
//设置文件目录权限(包含子目录)
void setPermissionsRecursive(const char* path,mode_t mode=S_IRWXU | S_IRWXG | S_IRWXO);

//检测操作系统用户密码是否匹配
bool checkSystemUserAndPassword(const std::string& username, const std::string& password);

//检测SSH是否可以免密码登录
bool canSSHLoginWithoutPassword(const std::string& host,const std::string& userName);

//读取root账户的sshKey公钥（如果没有公钥，则自动创建）
string read_rootUser_ssh_key();

//检查SSH authorizedKeysFile，并写入指定的publicKey
bool checkSSHKeyAndWriteNew(const std::string& username, const std::string& publicKey);
#endif

// 执行shell
bool    ExecuteShell(const char* szPathName, char* param[]);

// 获取网络速度
void    GetNetSpeed(io_speed *io);

// 获取文件MD5的值
string  GetFileMd5(const char* szPathName);

// 获取公网IP地址
string  GetPublicIP(void);

// 获取一定区间内的最大范围数
int     GetRandomNum(int minRange, int maxRange);

// 设置文件权限
void    SetFileMod(const char* szPathName, int mode);

// 得到字符串复制长度
int     GetCpLen(int dstLen, int srcLen);

//设置系统启动时间
CMyString Get_system_boot_time();

//获取硬盘总大小及剩余空间
void GetHardDiskSize(int *total,int *free);
//获取内存信息
void GetMemoryInfo(int *total,int *free);
//计算某目录所占空间大小（包含本身的4096Byte)
long long int GetDirectorySize(char *dir);
//获取网络信息
void Get_Server_Network_Config(string &ip_address,string &subnet_mask,string &gateway,string &dns_server);
//设置网络信息
void Set_Server_Network_Config(string &ip_address,string &subnet_mask,string &gateway,string &dns_server);
//获取多个IP地址
void Get_Multi_IPAddr(string &ipaddr1,string &ipaddr2);
//获取多个子网掩码
void Get_Multi_NetMask(string &subnet_mask1,string &subnet_mask2);

//IP转整型
int ip_to_int(const char * ip);

//IP是否属于局域网
bool isIPLAN(const char * szIP);

//取消WEB登录默认显示Admin
void Cancel_Web_Default_Admin_Login();

//设置系统主题配置文件
void SetSystemThemeConfig(string key,string value);

//取消网页目录访问
void Disable_Web_indexes_access();

//判断进程是否运行中
bool IsProcessRun(char *processName,bool IsShell=false);

//停止进程
void StopProcess(char *processName,bool IsShell=false);

//判断命令是否有效
bool IsCommandValid(char *cmdName);

//限制音频数据在16位范围内
int16_t limit_value_16bit(int32_t value);

//转换QT HostAddress类型到string类型
string QHostAddressToString(QHostAddress peerAddress);


string generateRandomMac(uint8_t firstByte);

#endif // GLOBALMETHOD_H






