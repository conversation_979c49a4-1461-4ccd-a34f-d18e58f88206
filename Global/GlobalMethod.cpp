#include <stdio.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>

#include <iconv.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>

#include <sys/time.h>
#include <time.h>

#include <assert.h>
#include <error.h>
#include <errno.h>

#include "GlobalMethod.h"
#include "Const.h"
#include "stdafx.h"
#include <string.h>

#include <QUuid>

#include <qdir.h>

#include <unistd.h>
#if defined(Q_OS_LINUX)
#include <linux/rtc.h>
#include <sys/ioctl.h>
#include <uuid/uuid.h>
#include <net/if.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <ifaddrs.h>
#include <sys/sysinfo.h>
#include <sys/statfs.h>
#include <shadow.h>
#include <libssh/libssh.h>
#else
#include <QCoreApplication>
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <objbase.h>
#include <QTextCodec>
#include <sysinfoapi.h>
#endif

#include <random>
#include <sstream> // 包含 stringstream


// 字符转码
int charset_convert(const char *from_charset,
                    const char *to_charset,
                    char *in_buf,
                    size_t in_left,
                    char *out_buf,
                    size_t out_left)
{
#if defined(Q_OS_LINUX)
    iconv_t icd;
    char *pin = in_buf;
    char *pout = out_buf;
    size_t out_len = out_left;
    if ((iconv_t)-1 == (icd = iconv_open(to_charset,from_charset))) {
        return -1;
    }
    if ((size_t)-1 == iconv(icd, &pin, &in_left, &pout, &out_left)) {
        iconv_close(icd);
        pout[0] = '\0';
        return -1;
    }
    out_buf[out_len - out_left] = 0;
    iconv_close(icd);
    return (int)out_len - out_left;
#else
    return 0;
#endif
}

// utf8 -> gb2312
CMyString ConvertUTF8toGB2312(const char *pData, size_t size)
{
#if defined(Q_OS_LINUX)
    char* newData = new char[size+1];
    strcpy(newData, pData);

    char* to_str_gbk = new char[size*3];

    int nRet = charset_convert("UTF-8","GB2312",
                               newData,size,to_str_gbk,size*3);

    if( nRet== -1)
    {
        perror("UTF8=>GBK error");
        return CMyString("unknow");
    }

    CMyString strGBK(to_str_gbk);
    delete[] to_str_gbk;
    delete[] newData;

    return strGBK;
#else
    QTextCodec::ConverterState state;
    QTextCodec* utf8Codec= QTextCodec::codecForName("utf-8");
    QTextCodec* gb2312Codec = QTextCodec::codecForName("gb2312");
    QString strUnicode= utf8Codec ->toUnicode(pData,size,&state);
    //CMyString realStr;
       if (state.invalidChars > 0) {    //异常,返回本身
           //realStr.Format("%s",pData);
           return CMyString(pData);
       } else {
           QByteArray ByteUtf8= gb2312Codec->fromUnicode(strUnicode);
           char *to_str_gbk = ByteUtf8.data();
           //realStr.Format("%s",to_str_gbk);
            return CMyString(to_str_gbk);
       }
#endif
}


// gb2312 -> utf8
CMyString ConvertGB2312toUTF8(const char *pData, size_t size)
{
#if defined(Q_OS_LINUX)
    char* newData = (char*)malloc(size+1);
    strcpy(newData, pData);

    char* to_str_utf8 = (char*)malloc(size*3);
    memset(to_str_utf8, 0, size*3);

    int nRet = charset_convert("GB2312","UTF-8",
                               newData, size, to_str_utf8, size*3);

    if( nRet== -1)
    {
        perror("GBK=>UTF8 error");
        return CMyString("unknow");
    }

    CMyString strUTF8(to_str_utf8);

    free(to_str_utf8);
    free(newData);

    return strUTF8;
#else
    QTextCodec::ConverterState state;
    QTextCodec* utf8Codec= QTextCodec::codecForName("utf-8");
    QTextCodec* gb2312Codec = QTextCodec::codecForName("gb2312");
    QString strUnicode= gb2312Codec->toUnicode(pData,size,&state);
    //CMyString realStr;
       if (state.invalidChars > 0) {    //异常,返回本身
           //realStr.Format("%s",pData);    //用.Format可能超出，如TTS文本
           return CMyString(pData);
       } else {
            QByteArray ByteUtf8= utf8Codec->fromUnicode(strUnicode);
           char *to_str_utf8 = ByteUtf8.data();
           //realStr.Format("%s",to_str_utf8);
           return CMyString(to_str_utf8);
       }
#endif

}


// 判断字符串是否为UTF8格式
bool IsTextUTF8(const char* str,ulong length)
{
    int i;
    int nBytes=0;//UFT8可用1-6个字节编码,ASCII用一个字节
    u_char chr;
    bool bAllAscii=true; //如果全部都是ASCII, 说明不是UTF-8
    for(i=0; i<(int)length; i++)
    {
        chr= *(str+i);
        if( (chr&0x80) != 0 ) // 判断是否ASCII编码,如果不是,说明有可能是UTF-8,ASCII用7位编码,但用一个字节存,最高位标记为0,o0xxxxxxx
            bAllAscii= false;
        if(nBytes==0) //如果不是ASCII码,应该是多字节符,计算字节数
        {
            if(chr>=0x80)
            {
                if(chr>=0xFC && chr<=0xFD)
                    nBytes = 6;
                else if(chr >= 0xF8)
                    nBytes=5;
                else if(chr >= 0xF0)
                    nBytes=4;
                else if(chr >= 0xE0)
                    nBytes=3;
                else if(chr >= 0xC0)
                    nBytes=2;
                else
                {
                    return false;
                }
                nBytes--;
            }
        }
        else //多字节符的非首字节,应为 10xxxxxx
        {
            if( (chr&0xC0) != 0x80 )
            {
                return false;
            }
            nBytes--;
        }
    }
    if( nBytes > 0 ) //违返规则
    {
        return false;
    }
    if( bAllAscii ) //如果全部都是ASCII, 说明不是UTF-8
    {
        return false;
    }
    return true;
}

// 字符串转成utf8格式
string  StringToUTF8(const char* pData)
{
#if defined(Q_OS_LINUX)
    return pData;
    #if 0
    int nLen = strlen(pData);
    if(nLen == 0)       return pData;

    if(!IsTextUTF8(pData, nLen))
    {
        CMyString msData = ConvertGB2312toUTF8(pData, nLen);
        return msData.C_Str();
    }

    return pData;
    #endif
#else
    return pData;
#endif

}


// 字符串转成gb2312格式
string  StringToGB2312(const char* pData)
{
    int nLen = strlen(pData);
    if(nLen == 0)       return pData;

    //if(IsTextUTF8(pData, nLen))       // 转换成GBK后还是判断为UTF8格式，待处理
    {
        CMyString msData = ConvertUTF8toGB2312(pData, nLen);
        return msData.C_Str();
    }

    return pData;
}


// CMyString 字符串转成char* 字符串，返回字符串内存得手动删除 保留，待修改 zhuyg
char*	CStringToChar(CMyString& str)
{
    int strLen  = str.GetLength();
    char* szStr = new char[strLen+1];

    strcpy(szStr, str.Data());

    return szStr;
}


// 判断字符串为日期时间格式
bool    IsDateTimeFormat(const char* szDateTime)
{
    try
    {
        string  pattenrn("(\\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d{1}|3[01])\\s(0\\d{1}|1\\d{1}|2[0-3]):[0-5]\\d{1}:([0-5]\\d{1})");
        regex  reg(pattenrn);
        bool    isFormat = regex_match(szDateTime, reg);
        return isFormat;
    }
    catch(std::regex_error& e)
    {
        return false;
    }
}

// 检查日期格式
bool    IsDateFormat(const char* szDate)
{
    try
    {
        // (\\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d{1}|3[01])-(0\\d{1}|1\\d{1}|2[0-3]):[0-5]\\d{1}:([0-5]\\d{1})
        string  pattenrn("(\\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d{1}|3[01])");
        regex  reg(pattenrn);
        bool    isFormat = regex_match(szDate, reg);
        return isFormat;
    }
    catch(std::regex_error& e)
    {
        return false;
    }
}

// 检查时间格式
bool    IsTimeFormat(const char* szTime)
{
    try
    {
        // (\\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d{1}|3[01])-(0\\d{1}|1\\d{1}|2[0-3]):[0-5]\\d{1}:([0-5]\\d{1})
        string  pattenrn("(0\\d{1}|1\\d{1}|2[0-3]):[0-5]\\d{1}:([0-5]\\d{1})");
        regex  reg(pattenrn);
        bool    isFormat = regex_match(szTime, reg);
        return isFormat;
    }
    catch(std::regex_error& e)
    {
        return false;
    }
}


// 判断字符串是否为IP格式
bool    IsIPFormat(const char* szIP)
{
    try
    {//"(2[0-4]\\d|25[0-5]|1\\d{2}|[1-9]\\d|[1-9])\.((2[0-4]\\d|25[0-5]|1\\d{2}|[1-9]\\d|\\d)\.){2}(2[0-4]\\d|25[0-5]|1\\d{2}|[1-9]\\d|\\d)";

        string pattenrn("((2[0-4]\\d|25[0-5]|1\\d{2}|[1-9]\\d|[0-9])\\.){3}(2[0-4]\\d|25[0-5]|1\\d{2}|[0-9]\\d|\\d)");
        regex  reg(pattenrn);
        bool   isFormat = regex_match(szIP, reg);
        return isFormat;
    }
    catch(std::regex_error& e)
    {
        return false;
    }
}

// 判断字符串是否为合法域名（格式+长度+TLD）
bool IsDomainFormat(const char* szDomain) {
    try {
        // 常见顶级域名列表（可扩展）
        const vector<string> commonTLDs = {
            "com", "org", "net", "io", "co", "ai", "gov", "edu", "mil", "int",
            "biz", "info", "mobi", "name", "tv", "cc", "me", "us", "uk", "ca",
            "au", "de", "jp", "cn", "in", "br", "fr", "ru", "es", "it", "nl",
            "se", "ch", "no", "mx", "ar", "pl", "id", "kr", "tr", "th", "vn",
            "gr", "be", "at", "dk", "fi", "nz", "pt", "sg", "my", "hk", "tw",
            "il", "ir", "pk", "ph", "ro", "ua", "za", "eg", "sa", "ae", "ng",
            "ke", "ma", "cl", "pe", "co.uk", "org.uk", "gov.uk", "ac.uk", "edu.au",
            "co.jp", "ne.jp", "or.jp", "go.jp", "ac.jp", "com.cn", "net.cn", "org.cn",
            "gov.cn", "edu.cn", "com.br", "net.br", "org.br", "gov.br", "edu.br"
        };

        string domain(szDomain);
        if (domain.empty() || domain.length() > 253) {
            return false;
        }

        // ------ 1. 检查标签长度（≤63）------
        size_t start = 0;
        size_t dotPos = domain.find('.');
        while (dotPos != string::npos) {
            string label = domain.substr(start, dotPos - start);
            if (label.empty() || label.length() > 63 || label.front() == '-' || label.back() == '-') {
                return false;
            }
            start = dotPos + 1;
            dotPos = domain.find('.', start);
        }
        string lastLabel = domain.substr(start);
        if (lastLabel.empty() || lastLabel.length() > 63 || lastLabel.front() == '-' || lastLabel.back() == '-') {
            return false;
        }

        // ------ 2. 检查是否以常见TLD结尾 ------
        for (const auto& tld : commonTLDs) {
            if (domain.length() >= tld.length() + 1) { // 确保TLD前有一个点（如 ".com"）
                string domainEnd = domain.substr(domain.length() - tld.length() - 1);
                if (domainEnd == "." + tld) {
                    return true;
                }
            }
        }

        return false;
    }
    catch (...) {
        return false;
    }
}

// 判断字符串是否为数字加逗号的格式
bool    IsTelNumberAndCommaFormat(const char* str)
{
    try
    {
        string pattenrn(R"(^\d{7,11}(,\d{7,11})*$)");
        regex  reg(pattenrn);
        bool   isFormat = regex_match(str, reg);
        return isFormat;
    }
    catch(std::regex_error& e)
    {
        return false;
    }
}

// 判断字符是否为Mac格式
bool IsMacFormat(const char *szMac)
{
    try
    {
        string  pattenrn("([A-Fa-f0-9]{2}:){5}[A-Fa-f0-9]{2}");
        regex  reg(pattenrn);
        bool    isFormat = regex_match(szMac, reg);
        return isFormat;
    }
    catch(std::regex_error& e)
    {
        return false;
    }
}

// 判断字符串是否为GUID格式
bool    IsGUIDFormat(const char* szGuid)
{
    try
    {//"(2[0-4]\\d|25[0-5]|1\\d{2}|[1-9]\\d|[1-9])\.((2[0-4]\\d|25[0-5]|1\\d{2}|[1-9]\\d|\\d)\.){2}(2[0-4]\\d|25[0-5]|1\\d{2}|[1-9]\\d|\\d)";

        string  pattenrn("[a-zA-Z0-9]{8}-([a-zA-Z0-9]{4}-){3}[a-zA-Z0-9]{12}");
        regex  reg(pattenrn);
        bool    isFormat = regex_match(szGuid, reg);
        return isFormat;
    }
    catch(std::regex_error& e)
    {
        return false;
    }
}

// String  转成 CMyString
CMyString StringToCString(string &str)
{
    CMyString cstr(str.c_str());

    return cstr;
}


// CMyString 转成 String
string CStringToString(CMyString &cstr)
{
    return cstr.C_Str();
}


// char数组转成SHORT类型
unsigned short CharsToShort(const char *pData)
{
    // 一定要先转成unsigned char，因为char装不下FF
    unsigned short	data = ((unsigned char)pData[0])*256 + (unsigned char)pData[1];

    return data;
}


// char数组转成int类型
int CharsToInt(const char *pData)
{
    // 一定要先转成unsigned char，因为char装不下FF
    int	data = ((unsigned char)pData[0])*256*256*256 +
               ((unsigned char)pData[1])*256*256 +
               ((unsigned char)pData[2])*256 +
                (unsigned char)pData[3];

    return data;
}

//将字符串的IP转换为unsigned long
unsigned long GetIpDword(const char *szIP)
{
    if (strlen(szIP) == 0)
    {
        return 0;
    }
    else
    {
        int ip1, ip2, ip3, ip4;
        sscanf(szIP, "%d.%d.%d.%d", &ip1, &ip2, &ip3, &ip4);
        return ((ip1<<24) + (ip2<<16) + (ip3<<8) + ip4);
    }
}


// 得到当前的日期和时间，转换为字符串 时间格式为 YY-MM-DD hh:mm:ss
CMyString GetCurrentDateTime()
{
#if defined(Q_OS_LINUX)
    time_t timer;
    struct tm *tblock;
    char timeStr[20] = {0};

    timer = time(NULL);
    tblock = localtime(&timer);
    strftime(timeStr,20,"%F %T",tblock);
#else
	CTime  ct = CTime::GetCurrentTimeT();
    char timeStr[20] = {0};
    sprintf(timeStr,"%04d-%02d-%02d %02d:%02d:%02d",ct.GetYear(),ct.GetMonth(),ct.GetDay(),ct.GetHour(),ct.GetMinute(),ct.GetSecond());

#endif
    return CMyString(timeStr);
}

// 设置系统时间
void    SetSystemTime_Linux(CTime &ctime)
{
    struct tm ptm;
    struct timeval tv;
    struct timezone tz;
    gettimeofday(&tv, &tz);
    ptm.tm_year = ctime.GetYear() -1900;
    ptm.tm_mon = ctime.GetMonth() - 1;     // 月份从0开始
    ptm.tm_mday = ctime.GetDay();
    ptm.tm_hour = ctime.GetHour();
    ptm.tm_min = ctime.GetMinute();
    ptm.tm_sec = ctime.GetSecond();
    time_t utc_t = mktime(&ptm);

    tv.tv_sec = utc_t;
    tv.tv_usec = 0;
	#if defined(Q_OS_LINUX)
    settimeofday(&tv, &tz);
	#endif
}

// 设置系统时间
void    SetSystemTime_Linux(SYSTEMTIME_Q& st)
{
    struct tm ptm; // = new struct tm();
    struct timeval tv;
    struct timezone tz;
    gettimeofday(&tv, &tz);
    ptm.tm_year = st.nYear - 1900;
    ptm.tm_mon = st.nMonth - 1;     // 月份从0开始
    ptm.tm_mday = st.nDay;
    ptm.tm_hour = st.nHour;
    ptm.tm_min = st.nMinute;
    ptm.tm_sec = st.nSecond;
    time_t utc_t = mktime(&ptm);

    tv.tv_sec = utc_t;
    tv.tv_usec = 0;
	#if defined(Q_OS_LINUX)
    settimeofday(&tv, &tz);
	#endif
}

std::tm convertStrDateTimeToTM(std::string& dateTimeStr)
{
    std::tm tm = {};
    
    // 假设输入格式为 "YYYY-MM-DD HH:MM:SS"
    // 提取日期部分 "2025-01-14"
    int year = stoi(dateTimeStr.substr(0, 4));
    int month = stoi(dateTimeStr.substr(5, 2));
    int day = stoi(dateTimeStr.substr(8, 2));
    
    // 提取时间部分 "14:10:10"
    int hour = stoi(dateTimeStr.substr(11, 2));
    int minute = stoi(dateTimeStr.substr(14, 2));
    int second = stoi(dateTimeStr.substr(17, 2));
    
    tm.tm_year = year - 1900;  // 年份从1900开始计算
    tm.tm_mon = month - 1;     // 月份是0-11
    tm.tm_mday = day;
    tm.tm_hour = hour;
    tm.tm_min = minute;
    tm.tm_sec = second;
    
    // 让系统自动计算星期和一年中的第几天
    tm.tm_isdst = -1;  // 不指定夏令时信息
    
    return tm;
}

// 辅助函数：构造 timedatectl 命令并执行
int SetSystemTime_hardware(time_t gpsTimestamp, bool bIndependent) {
    char time_str[64];
    struct tm *timeinfo;

    #if defined(Q_OS_LINUX)
    // 将时间戳转换为本地时间（或 UTC，取决于需求）
    timeinfo = localtime(&gpsTimestamp);
    if (!timeinfo) {
        perror("localtime");
        return -1;
    }

    // 格式化为 timedatectl 接受的格式：YYYY-MM-DD HH:MM:SS
    strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", timeinfo);

    // 构造命令（需 root 权限）
    char command[128];
    snprintf(command, sizeof(command), "timedatectl set-time '%s'", time_str);

    // 执行命令
    int ret = system(command);
    if (ret != 0) {
        fprintf(stderr, "Failed to set time via timedatectl (return: %d)\n", ret);
        return -1;
    }
    printf("SetSystemTime_hardware ok!\n");
    #else

    SYSTEMTIME st;
    timeinfo = gmtime(&gpsTimestamp);
    if (!timeinfo) return false;

    st.wYear = timeinfo->tm_year + 1900;
    st.wMonth = timeinfo->tm_mon + 1;
    st.wDay = timeinfo->tm_mday;
    st.wHour = timeinfo->tm_hour;
    st.wMinute = timeinfo->tm_min;
    st.wSecond = timeinfo->tm_sec;
    st.wMilliseconds = 0;

    static int win_setSysTimeErrorCnt=0;

    if (!SetSystemTime(&st)) {
        if(bIndependent)
        {
            return -1;
        }
        win_setSysTimeErrorCnt++;
        if(win_setSysTimeErrorCnt>10)
        {
            return -1;
        }
        DWORD err = GetLastError(); // err == 1314
        // 失败原因是权限不足 → 启动提权助手
        QString exe = QCoreApplication::applicationFilePath();
        QString arg = QStringLiteral("--internal-settime %1")
                        .arg(gpsTimestamp);

        // 用 runas 动词触发 UAC，仅这一次
        INT_PTR ret = (INT_PTR)ShellExecuteW(
            nullptr,
            L"runas",
            reinterpret_cast<const wchar_t*>(exe.utf16()),
            reinterpret_cast<const wchar_t*>(arg.utf16()),
            nullptr,
            SW_HIDE);  // 黑框隐藏

        if (ret <= 32) {
            printf("falied set time!\n");
        }
        return -1;
    }
    #endif

    return 0;
}

// 得到GUID
CMyString GetGUID()
{
#if defined(Q_OS_LINUX)
    uuid_t uu;
    char buf[255];

    uuid_generate(uu);
    uuid_unparse_upper(uu,buf);

    return CMyString(buf);
#else
    GUID	guid;
    CMyString	strGUID("");
    CHAR	szGUID[40]	= {0};

    if(S_OK == ::CoCreateGuid(&guid))
    {
        sprintf_s(szGUID, "%08X-%04X-%04X-%02X%02X-%02X%02X%02X%02X%02X%02X"
            ,guid.Data1
            ,guid.Data2
            ,guid.Data3
            ,guid.Data4[0], guid.Data4[1]
        ,guid.Data4[2], guid.Data4[3], guid.Data4[4], guid.Data4[5], guid.Data4[6], guid.Data4[7]);
        strGUID = CMyString(szGUID);
    }

return strGUID;
#endif

}

// 判断本系统是否已经注册（目前使用这个） 保留，待修改 zhuyg
bool HasRegister()
{
    return false;
}


// 截取节目名称
CMyString InterceptionProName(const char *szProName, bool &bIntercept)
{
    string strPro = szProName;

    int n = strPro.find(".");

    string ProName = strPro.substr(0, n);
    // 总字符能不能超过10个 保留，待修改 zhuyg
    bIntercept = true;

    return CMyString(ProName);

}


// 得到运行程序路径
void GetCurExePath(char *szPath)
{
    strcpy(szPath, g_Global.m_strRunDirPath.Data());
}


void GetFilePathName(char *szPathName, string strFolder, string strName)
{
    strcat(szPathName, g_Global.m_strFolderPath.Data());
    strcat(szPathName, "/");
    strcat(szPathName, strFolder.data());
    strcat(szPathName, "/");
    strcat(szPathName, strName.data());

}

// 从绝对路径名得到文件名
CMyString GetNameByPathName(CMyString strPathName, bool hasExtension)
{
    int		  nPos;
    CMyString strName;

    // 可能是"/"或者"\"
    if (strPathName.ReverseFind(('/')) !=string::npos)
    {
        nPos = strPathName.ReverseFind(('/'));
    }
    else
    {
        nPos = strPathName.ReverseFind(('\\'));
    }

    // 带扩展名
    if (hasExtension)
    {
        strName = strPathName.Right(strPathName.GetLength() - nPos - 1);
    }
    else
    {
        strName = strPathName.Mid(nPos + 1, strPathName.ReverseFind(('.')) - nPos - 1);
    }

    return strName;
}

// 从升级文件路径名得到文件版本号
CMyString	GetVersionByPathName(CMyString strPathName)
{
    strPathName.MakeUpper();
    CMyString strExtension	= ".TAR.GZ";

    int  posExtension = strPathName.Find(strExtension);
    int  posVersionStart = strPathName.ReverseFind('V');

    return strPathName.Mid(posVersionStart, posExtension - posVersionStart);
}

//////////////////////////////////////////////////////////////////////////////

// 创建文件夹
bool CreateDirectoryQ(string Path)
{
    //mkpath会创建所需的所有目录，如果此函数调用时，该路径已经存在，此函数将会返回true
    QDir dir;
    return dir.mkpath(QString::fromStdString(Path));
}

// 创建HTTP目录
void    CreateHttpDirectory(char* szPath, string strFolder)
{
    char path[STR_MAX_PATH] = {0};
    strcpy(path, szPath);

    strcat(path, HTTP_FOLDER_SEPARATOR);
    strcat(path, strFolder.c_str());

    //printf("CreateHttpDirectory:path=%s\n",path);
    CreateDirectoryQ(path);
}

// 判断文件是否存在
bool IsExistFile(const char *szPath)
{
    QFile file(szPath);
    if(file.exists())
    {
       return true;
    }
    #if 0
    qDebug()<<"fileName="<<szPath;

    CMyString strLog;
    strLog.Format("IsExistFile : szPath is not exist : %s", szPath);
    LOG(strLog.C_Str(), LV_INFO);
    g_Global.m_Network.AddLog(strLog);
    #endif
    return false;
}

// 判断目录是否存在
bool IsExistDir(const char *szPath)
{
    QDir dir(szPath);
    if(dir.exists())
    {
       return true;
    }
    return false;
}

// 移动文件
bool FileMove(string srcPath, string desPath)
{
    if(!IsExistFile(srcPath.data()))
        return false;
    QString QsrcPath = QString::fromStdString(srcPath);
    QString QdesPath = QString::fromStdString(desPath);
    return QFile::rename(QsrcPath, QdesPath);
}

// 获取文件大小
ulong  GetFileSize(const char* szPathName)
{
        unsigned long nFileSize = 0;
        struct stat statbuff;

        if (stat(szPathName, &statbuff) >= 0)
        {
            nFileSize = statbuff.st_size;
        }
        return nFileSize;
}

// 移除文件
bool RemoveFile(const char *szPath)
{
    QFile file(szPath);
    if(file.exists())
    {
        return file.remove();
    }
    return false;
}

// 移除文件夹内所有文件
void    RemoveDirectoryFile(const char* szPath,bool delSelf)
{
    QDir dir(szPath);
    if(!dir.exists())
    {
        perror("opendir failed : ");
        return ;
    }

    QFileInfoList list = dir.entryInfoList();//获取文件夹内部列表
    for (int i = 0; i < list.size(); i++) {//循环读取文件夹
        QFileInfo fileInfo = list.at(i);
        if(fileInfo.fileName()=="."|fileInfo.fileName()==".."){
            i=i+1;
            continue;
        }else{
            if(fileInfo.isDir()){//是否为文件夹
                QString dirPath= fileInfo.absoluteFilePath();
                RemoveDirectoryFile(dirPath.toStdString().data(),true);//循环读取文件夹内容
            }else{
                QString filePath= fileInfo.filePath();
                QFile file(filePath);
                file.remove();
            }
        }
    }
    if(delSelf)
        dir.rmdir(szPath);
}


// 移除文件夹内所有文件(包括文件夹本身)
void RemoveDirectoryALL(const char * path)
{
   RemoveDirectoryFile(path,true);
}

// 移除文件夹内所有后缀的所有文件（包含子目录）
void RemoveDirectoryAllFilesByExt(const char* dirPath, const char* extension, bool bSubDir) {
    DIR *dir = opendir(dirPath);  // 打开目录
    if (dir == NULL) {
        return;
    }

    dirent* dirp = NULL;
    while ((dirp = readdir(dir)) != NULL) {
        // 忽略特殊目录 . 和 ..
        if (strcmp(dirp->d_name, ".") == 0 || strcmp(dirp->d_name, "..") == 0) {
            continue;
        }

        // 拼接文件路径
        char filePath[1024];
        sprintf(filePath, "%s/%s", dirPath, dirp->d_name);

        #if defined(Q_OS_LINUX)
        // 删除目录下的子目录中符合要求的文件
        if (dirp->d_type == DT_DIR && bSubDir) {
            RemoveDirectoryAllFilesByExt(filePath, extension, true); // 递归调用
        }
        // 删除符合要求的文件
        if (dirp->d_type == DT_REG) {
            if (strstr(dirp->d_name, extension) != NULL) {
                int ret = unlink(filePath);  // 删除文件
                if (ret < 0) {
                    return;
                }
                printf("RemoveAllSongFilesByExt:%s\n",filePath);
            }
        }
        #else
        struct stat file_stat;
        stat(filePath, &file_stat);
        //printf("d_name=%s,mode=%d\n",dirp->d_name,file_stat.st_mode);
        if (S_ISDIR(file_stat.st_mode) && bSubDir) {
            RemoveDirectoryAllFilesByExt(filePath, extension, true); // 递归调用
        }else if (S_ISREG(file_stat.st_mode)) {
            if (strstr(dirp->d_name, extension) != NULL) {
                int ret = unlink(filePath);  // 删除文件
                if (ret < 0) {
                    return;
                }
                printf("RemoveAllSongFilesByExt:%s\n",filePath);
            }
        }

        #endif
    }
    closedir(dir);
}


// 组成可远程下载的文件URL(绝对路径) 保留，待修改 zhuyg
void    CombinHttpURL(char* szPathName, string strFolder, string strName)
{
    memset(szPathName,0,  strlen(szPathName));
    strcat(szPathName, g_Global.m_strFolderPath.Data());
    strcat(szPathName, "/");
    strcat(szPathName, HTTP_FOLDER_ADATA);
    strcat(szPathName, "/");
    strcat(szPathName, strFolder.data());
    strcat(szPathName, "/");
    strcat(szPathName, strName.data());
}

// 从文件路径得到URL 保留，待修改
CMyString GetHttpURLByPath(CMyString strPath, const char *szServerIP, int nPort)
{
    CMyString strPrev;
    strPrev.Format((char*)("http://%s:%d"), szServerIP, nPort);

    return (strPrev + strPath);
}


// 从本地文件路径名得到URL路径部分 保留，待修改
CMyString GetHttpURLPathByPathName(CMyString strPathName)
{
    CMyString strPrev = g_Global.m_strFolderPath;

    if (strPathName.Find(strPrev) == 0)
    {
        strPathName.Delete(0, strPrev.GetLength());
        return strPathName;
    }

    return NULL;
}

CMyString GetPathNamePathByHttpURL(CMyString strHttpURL)
{
    CMyString strPathName = g_Global.m_strFolderPath + strHttpURL;

    return strPathName;
}

CMyString GetNameByHttpPathName(CMyString strPathName)
{
    int		nPos	= strPathName.ReverseFind('/');
    CMyString strName	= strPathName.Mid(nPos+1, strPathName.ReverseFind('.')-nPos-1);

    return strName;
}

// 从HTTP路径名得到文件名（包括后缀名）
CMyString GetNameWithExtensionByHttpPathName(CMyString strPathName)
{
    return strPathName.Mid(strPathName.ReverseFind('/')+1);
}

CMyString GetPathByHttpPathName(CMyString strPathName)
{
    return strPathName.Left(strPathName.ReverseFind('/'));
}

// 获取指定目录下所有文件名称
void    GetDirAllFileName(CMyString strPath, vector<string> &vecFileNames)
{
    vecFileNames.clear();

    vecFileNames.clear();
    QDir dir(strPath.Data());
    if(!dir.exists())
    {
        perror("opendir failed : ");
        return ;
    }

    QFileInfoList list = dir.entryInfoList();//获取文件夹内部列表
    for (int i = 0; i < list.size(); i++) {//循环读取文件夹
        QFileInfo fileInfo = list.at(i);
        if(fileInfo.fileName()=="."|fileInfo.fileName()==".."){
            i=i+1;
            continue;
        }else{
            if(fileInfo.isDir()){//是否为文件夹
            }else{
                vecFileNames.push_back(fileInfo.fileName().toStdString());
            }
        }
    }

}




/************************************************************/

// 是否连接网络
bool IsLinkNetwork()
{
#if defined(Q_OS_LINUX)
    // 使用域名检测，同时验证DNS解析和网络连接
    const char* test_domains[] = {
        "www.baidu.com",     // 百度
        "www.qq.com",        // 腾讯
    };
    
    for (int i = 0; i < sizeof(test_domains)/sizeof(test_domains[0]); i++) {
        struct addrinfo hints, *result;
        memset(&hints, 0, sizeof(hints));
        hints.ai_family = AF_INET;      // IPv4
        hints.ai_socktype = SOCK_STREAM; // TCP
        
        // 尝试DNS解析
        int dns_result = getaddrinfo(test_domains[i], "80", &hints, &result);
        if (dns_result != 0) {
            continue; // DNS解析失败，尝试下一个域名
        }
        
        // DNS解析成功，尝试连接
        int sockfd = socket(AF_INET, SOCK_STREAM, 0);
        if (sockfd < 0) {
            freeaddrinfo(result);
            continue;
        }
        
        // 设置非阻塞模式
        int flags = fcntl(sockfd, F_GETFL, 0);
        fcntl(sockfd, F_SETFL, flags | O_NONBLOCK);
        
        // 尝试连接
        int connect_result = connect(sockfd, result->ai_addr, result->ai_addrlen);
        bool connected = false;
        
        if (connect_result == 0) {
            connected = true;
        } else if (errno == EINPROGRESS) {
            // 使用select等待连接完成，超时时间3秒
            fd_set write_fds;
            struct timeval timeout;
            FD_ZERO(&write_fds);
            FD_SET(sockfd, &write_fds);
            timeout.tv_sec = 3;
            timeout.tv_usec = 0;
            
            int select_result = select(sockfd + 1, NULL, &write_fds, NULL, &timeout);
            if (select_result > 0) {
                int error = 0;
                socklen_t len = sizeof(error);
                if (getsockopt(sockfd, SOL_SOCKET, SO_ERROR, &error, &len) == 0 && error == 0) {
                    connected = true;
                }
            }
        }
        
        close(sockfd);
        freeaddrinfo(result);
        
        if (connected) {
            return true; // 成功连接到任意一个域名
        }
    }
    
    return false;
    
#else
    // Windows平台实现
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        return false;
    }
    
    const char* test_domains[] = {
        "www.baidu.com",
        "www.qq.com",
    };
    
    for (int i = 0; i < sizeof(test_domains)/sizeof(test_domains[0]); i++) {
        struct addrinfo hints, *result;
        memset(&hints, 0, sizeof(hints));
        hints.ai_family = AF_INET;
        hints.ai_socktype = SOCK_STREAM;
        
        // 尝试DNS解析
        int dns_result = getaddrinfo(test_domains[i], "80", &hints, &result);
        if (dns_result != 0) {
            continue;
        }
        
        // DNS解析成功，尝试连接
        SOCKET sock = socket(AF_INET, SOCK_STREAM, 0);
        if (sock == INVALID_SOCKET) {
            freeaddrinfo(result);
            continue;
        }
        
        // 设置超时
        DWORD timeout = 3000; // 3秒
        setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, (char*)&timeout, sizeof(timeout));
        setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, (char*)&timeout, sizeof(timeout));
        
        bool connected = (connect(sock, result->ai_addr, (int)result->ai_addrlen) == 0);
        
        closesocket(sock);
        freeaddrinfo(result);
        
        if (connected) {
            WSACleanup();
            return true;
        }
    }
    
    WSACleanup();
    return false;
#endif
}

#if defined(Q_OS_LINUX)
// 设置文件权限
void MyChmod()
{
    #if 0
    // 设置文件夹权限
    CMyString scriptPath;
    scriptPath.Format("%s/%s", SCRIPT_PATH, SCRIPT_CHMOD);
    system(scriptPath.C_Str());
    #endif

    CMyString strPathData = g_Global.m_strFolderPath+HTTP_FOLDER_SEPARATOR+HTTP_FOLDER_ADATA;
    CMyString strPathApp = g_Global.m_strRunDirPath;

    setPermissionsRecursive(strPathData.Data());
    setPermissionsRecursive(strPathApp.Data());
}

//设置目录权限(包含子目录)
void setPermissionsRecursive(const char* path,mode_t mode) {
    struct stat st;
    if (stat(path, &st) != 0) {
        std::cerr << "Failed to get file status: " << strerror(errno) << std::endl;
        return;
    }

    if (S_ISDIR(st.st_mode)) {
        DIR* dir = opendir(path);
        if (dir == nullptr) {
            std::cerr << "Failed to open directory: " << strerror(errno) << std::endl;
            return;
        }

        struct dirent* entry;
        while ((entry = readdir(dir)) != nullptr) {
            if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
                continue;
            }

            char subPath[PATH_MAX];
            snprintf(subPath, sizeof(subPath), "%s/%s", path, entry->d_name);

            setPermissionsRecursive(subPath);
        }

        closedir(dir);
    }

    if (chmod(path, mode) != 0) {
        std::cerr << "Failed to set file permissions: " << strerror(errno) << std::endl;
    }
}

#endif


// 执行shell
bool ExecuteShell(const char *szPathName, char *param[])
{
#if defined(Q_OS_LINUX)
    // 需要创建一个进程调用操作文件(复制，移动等)的脚本，否则会造成udp端口阻塞
    pid_t id = fork();
    if(id < 0)
    {
        return false;
    }
    else if(id == 0)
    {
        execvp(szPathName, param);
        perror("execvp ");
        exit(0);
    }
    else
    {
        int stat_val;
        waitpid(id, &stat_val, 0);

        if(WIFEXITED(stat_val))
        {
            //LOG(FORMAT("Child exited with code %d\n", WEXITSTATUS(stat_val)), LV_INFO);
        }
        else if(WIFSIGNALED(stat_val))
        {
            //LOG(FORMAT("Child terminated abnormally, signal %d\n", WTERMSIG(stat_val)), LV_INFO);
        }
    }
#endif
    return true;
}


int get_if_dbytes(struct if_info* ndev)
{
#if defined(Q_OS_LINUX)
    //assert(ndev);

    struct ifaddrs *ifa_list = NULL;
    struct ifaddrs *ifa = NULL;
    struct if_data *ifd = NULL;
    int    ret = 0;

    ret = getifaddrs(&ifa_list);
    if(ret < 0) {
        perror("Get Interface Address Fail:");
        goto end;
    }

    for(ifa=ifa_list; ifa; ifa=ifa->ifa_next){
        if(!(ifa->ifa_flags & IFF_UP) && !(ifa->ifa_flags & IFF_RUNNING))
            continue;

        if(ifa->ifa_data == 0)
            continue;

        ret = strcmp(ifa->ifa_name,ndev->ifi_name);
        if(ret == 0){
          ifd = (struct if_data *)ifa->ifa_data;

          ndev->ifi_ibytes = ifd->ifi_ibytes;
          ndev->ifi_obytes = ifd->ifi_obytes;
          break;
        }
    }

    freeifaddrs(ifa_list);
end:
    return (ret ? -1 : 0);
#else
	return 0;
#endif
}

int get_if_speed(struct if_speed *ndev)
{
    //assert(ndev);

    struct if_info *p1=NULL,*p2=NULL;

    p1 = (struct if_info *)malloc(sizeof(struct if_info));
    p2 = (struct if_info *)malloc(sizeof(struct if_info));
    memset(p1,0,sizeof(struct if_info));
    memset(p2,0,sizeof(struct if_info));

    strncpy(p1->ifi_name,ndev->ifs_name,strlen(ndev->ifs_name)>=sizeof(p1->ifi_name)?sizeof(p1->ifi_name):strlen(ndev->ifs_name) );
    strncpy(p2->ifi_name,ndev->ifs_name,strlen(ndev->ifs_name)>=sizeof(p1->ifi_name)?sizeof(p1->ifi_name):strlen(ndev->ifs_name));

    int ret = 0;
    ret = get_if_dbytes(p1);
    if(ret < 0)    goto end;
    usleep(ndev->ifs_us);
    ret = get_if_dbytes(p2);
    if(ret < 0)    goto end;

    ndev->ifs_ispeed = p2->ifi_ibytes - p1->ifi_ibytes;
    ndev->ifs_ospeed = p2->ifi_obytes - p1->ifi_obytes;

end:
    free(p1);
    free(p2);

    return 0;
}

// 获取网络速度
void       GetNetSpeed(io_speed* io)
{
    struct if_speed ndev;

    int ret = 0;

    memset(&ndev,0,sizeof(ndev));
    sprintf(ndev.ifs_name,"eth0");

    ndev.ifs_us = 100000;

    ret = get_if_speed(&ndev);
    if(ret < 0)
    {
        //printf("\t\t\t[Fail]\n");
        return;
    }
    else
    {
        //printf("\t\t\t[OK]\n");
    }

    io->ispeed = (ndev.ifs_ispeed * 1.0/(ndev.ifs_us/1000 * 0.001))/1024.0;
    io->ospeed = (ndev.ifs_ospeed * 1.0/(ndev.ifs_us/1000 * 0.001))/1024.0;

    //printf("%s: Up Speed: %f MB/s || Down Speed: %f MB/s                  \r",
    //        ndev.ifs_name,ispeed/(1024.0*1024.0),ospeed/(1024.0*1024.0));
//    printf("%s: Up Speed: %5.2f KB/s || Down Speed: %5.2f KB/s                  \r",
//           ndev.ifs_name,ispeed/(1024),ospeed/(1024));


}

// 获取文件MD5的值
string GetFileMd5(const char *szPathName)
{
#if 0
    std::ifstream file(szPathName, std::ifstream::binary);
    if (!file)
    {
        return string("");
    }

    MD5_CTX md5Context;
    MD5_Init(&md5Context);

    char buf[1024 * 16];
    while (file.good())
    {
        file.read(buf, sizeof(buf));
        MD5_Update(&md5Context, buf, file.gcount());
    }

    unsigned char result[MD5_DIGEST_LENGTH];
    MD5_Final(result, &md5Context);

    char hex[35];
    memset(hex, 0, sizeof(hex));
    for (int i = 0; i < MD5_DIGEST_LENGTH; ++i)
    {
        sprintf(hex + i * 2, "%02x", result[i]);
    }
    hex[32] = '\0';
    return string(hex);
#else
    QFile theFile(szPathName);
    if(!theFile.exists())
    {
        return "";
    }
    theFile.open(QIODevice::ReadOnly);
    QByteArray ba = QCryptographicHash::hash(theFile.readAll(), QCryptographicHash::Md5);
    theFile.close();
    //qDebug() << ba.toHex().constData();
    string md5 = ba.toHex().toStdString();
    return md5;
#endif
}

// 获取公网IP地址
string GetPublicIP(void)
{
    string cmd = "curl -s the-x.cn";
    string strIP = "";

    FILE * fp;
    int res = -1;
    if ((fp = popen(cmd.c_str(), "r") ) == NULL)
    {
        printf("Popen Error!\n");
    }
    else
    {
        char pRetMsg[10240]={0};
        //get lastest result
        while(fgets(pRetMsg,10240, fp) != NULL)
        {
            strIP += pRetMsg;
        }

        if ((res = pclose(fp)) == -1)
        {
            printf("close popenerror!\n");
            strIP = "";
        }
    }

    return strIP;
}

// 获取一定区间内的最大范围数
int GetRandomNum(int minRange, int maxRange)
{
    srand((unsigned int)time(0));
    int num = ((double)rand()/RAND_MAX)*(maxRange - minRange) + minRange;

    return num;
}

// 设置文件权限
void SetFileMod(const char *szPathName, int mode)
{
    chmod(szPathName, mode);
}


// 得到字符串复制长度
int GetCpLen(int dstLen, int srcLen)
{
    return (dstLen <= srcLen) ? dstLen : srcLen;
}


//获取系统启动时间
CMyString Get_system_boot_time()
{
#if defined(Q_OS_LINUX)
    struct sysinfo info;
    time_t cur_time = 0;
    time_t boot_time = 0;
    struct tm *ptm = NULL;
    if (sysinfo(&info)) {
        printf("Failed to get sysinfo, errno:%u, reason:%s\n",errno, strerror(errno));
        return NULL;
    }
    time(&cur_time);
    if (cur_time > info.uptime) {
    boot_time = cur_time - info.uptime;
    }
    else {
    boot_time = info.uptime - cur_time;
    }

    char timeStr[20] = {0};
    ptm = localtime(&boot_time);
    strftime(timeStr,20,"%F %T",ptm);
    //printf("System boot time: %d-%-d-%d %d:%d:%d\n", ptm->tm_year + 1900,\
        ptm->tm_mon + 1, ptm->tm_mday, ptm->tm_hour, ptm->tm_min, ptm->tm_sec);
   return CMyString(timeStr);
#else
   /*20240923
    GetTickCount 函数返回的是一个32位值，表示系统启动后经过的毫秒数，因此它每49.7天就会溢出一次（2^32毫秒 ÷ 1000秒/天 ≈ 49.7天）。
    如果你需要处理长时间运行的应用程序或者需要考虑溢出的情况，需要使用GetTickCount64
    GetTickCount64需要包含头文件#include <sysinfoapi.h>，并且定义QMAKE_CXXFLAGS += -D_WIN32_WINNT=0x0601
   */ 
   //int iRunTime = GetTickCount();
   ULONGLONG iRunTime = GetTickCount64();
   time_t nowtime;
   time( &nowtime );

   time_t DateTime = nowtime - (iRunTime / 1000);

   struct tm *timeinfo;
   timeinfo = localtime( &DateTime );

   int year, month, day, hour, min, sec;
   year = timeinfo->tm_year + 1900;
   month = timeinfo->tm_mon + 1;
   day = timeinfo->tm_mday;
   hour = timeinfo->tm_hour;
   min = timeinfo->tm_min;
   sec = timeinfo->tm_sec;

  char timeStr[20] = {0};
  sprintf(timeStr,"%04d-%02d-%02d %02d:%02d:%02d",year,month,day,hour,min,sec);
  return CMyString(timeStr);
#endif
}



//获取磁盘总大小及剩余空间
void GetHardDiskSize(int *total,int *free)
{
#if defined(Q_OS_LINUX)
    struct statfs diskInfo;
    statfs("/",&diskInfo);
    unsigned long long blocksize  = diskInfo.f_bsize;
    unsigned long long totalDisk = diskInfo.f_blocks*blocksize;
    unsigned long long freeDisk = diskInfo.f_bfree*blocksize ;
 
    if(total!=NULL)
        *total = (int)(totalDisk/1024/1024);   //转换成MB
    if(free!=NULL)
        *free = (int)(freeDisk/1024/1024);       //转换成MB
#else
    string disk_part=g_Global.m_strRunDirPath.Left(2).Data();
    ULARGE_INTEGER lFreeBytesAvailable, lTotalBytesTemp, lTotalFreeBytes;
    if( !GetDiskFreeSpaceEx( disk_part.data(), &lFreeBytesAvailable, &lTotalBytesTemp, &lTotalFreeBytes ) )
    {
        *total = 0;
        *free = 0;
        return;
    }

    //unit : GB
    *total = lTotalBytesTemp.QuadPart / 1024.0 / 1024;  //MB
    *free = lTotalFreeBytes.QuadPart / 1024.0 / 1024;   //MB
#endif

}


//获取内存信息
void GetMemoryInfo(int *total,int *free)
{
#if defined(Q_OS_LINUX)
    struct sysinfo memInfo;
    sysinfo(&memInfo);
 
    if(total!=NULL)
        *total = (int)(memInfo.totalram/1024/1024);
    if(free!=NULL)
        *free =  (int)(memInfo.freeram/1024/1024);
#else
    MEMORYSTATUSEX memoryInfo;
    memoryInfo.dwLength = sizeof(memoryInfo);
    GlobalMemoryStatusEx(&memoryInfo);
    *total = memoryInfo.ullTotalPhys/1024/1024;
    *free = memoryInfo.ullAvailPhys/1024/1024;
#endif
}


 //计算某目录所占空间大小（包含本身的4096Byte）
long long  GetDirectorySize(char *dir)
{
#if defined(Q_OS_LINUX)
     DIR *dp;
     struct dirent *entry;
     struct stat statbuf;
     long long int totalSize=0;
 
     if ((dp = opendir(dir)) == NULL)
     {
         fprintf(stderr, "Cannot open dir: %s\n", dir);
         return -1; //可能是个文件，或者目录不存在
     }
    
     //先加上自身目录的大小
     lstat(dir, &statbuf);
     totalSize+=statbuf.st_size;
 
     while ((entry = readdir(dp)) != NULL)
     {
         char subdir[257];
         sprintf(subdir, "%s/%s", dir, entry->d_name);
         lstat(subdir, &statbuf);
         
         if (S_ISDIR(statbuf.st_mode))
         {
             if (strcmp(".", entry->d_name) == 0 ||
                 strcmp("..", entry->d_name) == 0)
             {
                 continue;
             }
 
             long long int subDirSize = GetDirectorySize(subdir);
             totalSize+=subDirSize;
         }
         else
         {
             totalSize+=statbuf.st_size;
         }
     }
 
     closedir(dp);    
     return totalSize;
#else

	return 0;
#endif
}




/**
 * 根据掩码位数获取子网掩码
 * 
 * @param maskBit
 * @return
 */
void GetSubMaskByBit(int maskBit ,string &maskIp) {
 
    if (maskBit > 32 || maskBit < 1)
      return;
 
    string maskBinary = "";
    for (int i = 0; i < 32; i++) {
      if (i < maskBit)
        maskBinary += "1";
      else {
        maskBinary += "0";
      }
    }
    for (int i = 0; i < 4; i++) {
      int intVal=strtol( maskBinary.substr(8 * i, 8).c_str() , NULL,2);
      char val[10]={0};
      sprintf(val,"%d",intVal);
      maskIp = maskIp + "." + val;
    }
    maskIp.replace(0,1,"");
}

int GetNumberof1(int n)
{
    int count = 0;
    unsigned int flag = 1;

    while (flag)
    {
        if (n & flag)
            count ++;
        flag = flag << 1;
    }

    return count;
}

//获取网络信息
void Get_Server_Network_Config(string &ip_address,string &subnet_mask,string &gateway,string &dns_server)
{
#if defined(Q_OS_LINUX)
    if(!g_Global.m_bNmcliValid)
        return;
    string cmd = " nmcli dev show | grep 'IP4.ADDRESS' | head -1 | awk '{print $2}' "; 
    FILE * fp=NULL;
    char pRetMsg[1024]={0};

    if ((fp = popen(cmd.c_str(), "r") ) == NULL)
    {
        printf("Popen IP4_ADDRESS Error!\n");
        return;
    }
    else
    {
        fgets(pRetMsg,1024, fp);
        if(pRetMsg[strlen(pRetMsg) - 1] == '\n')    //去掉换行符
            pRetMsg[strlen(pRetMsg) - 1] = '\0' ;
        string ip_addr(pRetMsg);
        string::size_type pos=0;
        if( (pos=ip_addr.find("/") )!=string::npos  )
        {
            ip_address=ip_addr.substr(0,pos);
            string subnet_mask_tmp=ip_addr.substr(pos+1,ip_addr.length()-pos);
            GetSubMaskByBit(atoi(subnet_mask_tmp.c_str()),subnet_mask);
        }
    }
    pclose(fp);
    fp=NULL;

    
    memset(pRetMsg,0,sizeof(pRetMsg));
    cmd = " nmcli dev show | grep 'IP4.GATEWAY' | head -1 | awk '{print $2}' ";
    if ((fp = popen(cmd.c_str(), "r") ) == NULL)
    {
        printf("Popen IP4_GATEWAY Error!\n");
        return;
    }
    else
    {
        fgets(pRetMsg,1024, fp);
        if(pRetMsg[strlen(pRetMsg) - 1] == '\n')    //去掉换行符
            pRetMsg[strlen(pRetMsg) - 1] = '\0' ;
        gateway += pRetMsg;
        
    }
    pclose(fp);
    fp=NULL;


    memset(pRetMsg,0,sizeof(pRetMsg));
    cmd = " nmcli dev show | grep 'IP4.DNS' | head -1 | awk '{print $2}'"; 
    if ((fp = popen(cmd.c_str(), "r") ) == NULL)
    {
        printf("Popen IP4_DNS Error!\n");
        return;
    }
    else
    {
        fgets(pRetMsg,1024, fp);
        if(pRetMsg[strlen(pRetMsg) - 1] == '\n')    //去掉换行符
            pRetMsg[strlen(pRetMsg) - 1] = '\0' ;
        dns_server += pRetMsg;
    }
    pclose(fp);
    fp=NULL;
#else
    int validIPid=g_Global.m_netCardList[g_Global.m_bValidNetCardId].m_nValidIPId;
    ip_address=string(g_Global.m_netCardList[g_Global.m_bValidNetCardId].m_szIPInfo[validIPid].m_szIP);
    subnet_mask=string(g_Global.m_netCardList[g_Global.m_bValidNetCardId].m_szIPInfo[validIPid].m_szSubAddress);
    gateway=string(g_Global.m_netCardList[g_Global.m_bValidNetCardId].m_szGateWay);
    dns_server=string(g_Global.m_netCardList[g_Global.m_bValidNetCardId].m_szDNS);
#endif



    printf("ip_address=%s,subnet_mask=%s,gateway=%s,dns_server=%s\n",ip_address.data(),subnet_mask.data(),gateway.data(),dns_server.data());

}



//获取多个IP地址
void Get_Multi_IPAddr(string &ipaddr1,string &ipaddr2)
{
#if defined(Q_OS_LINUX)
    if(!g_Global.m_bNmcliValid)
        return;
    string cmd = " nmcli dev show | grep 'IP4.ADDRESS' | head -2 | awk '{print $2}' | sed -n '1p' "; 
    FILE * fp=NULL;
    char pRetMsg[1024]={0};

    if ((fp = popen(cmd.c_str(), "r") ) == NULL)
    {
        printf("Popen Get_Multi_IPAddr1 Error!\n");
        return;
    }
    else
    {
        fgets(pRetMsg,1024, fp);
        if(pRetMsg[strlen(pRetMsg) - 1] == '\n')    //去掉换行符
            pRetMsg[strlen(pRetMsg) - 1] = '\0' ;
        string ip_addr(pRetMsg);
        string::size_type pos=0;
        if( (pos=ip_addr.find("/") )!=string::npos  )
        {
            ipaddr1=ip_addr.substr(0,pos);
        }
    }
    pclose(fp);
    fp=NULL;


    cmd = " nmcli dev show | grep 'IP4.ADDRESS' | head -2 | awk '{print $2}' | sed -n '2p' "; 
    if ((fp = popen(cmd.c_str(), "r") ) == NULL)
    {
        printf("Popen Get_Multi_IPAddr2 Error!\n");
        return;
    }
    else
    {
        fgets(pRetMsg,1024, fp);
        if(pRetMsg[strlen(pRetMsg) - 1] == '\n')    //去掉换行符
            pRetMsg[strlen(pRetMsg) - 1] = '\0' ;
        string ip_addr(pRetMsg);
        string::size_type pos=0;
        if( (pos=ip_addr.find("/") )!=string::npos  )
        {
            ipaddr2=ip_addr.substr(0,pos);
            if(ipaddr2 == "127.0.0.1")
            {
                ipaddr2="";
            }
        }
    }
    pclose(fp);
    fp=NULL;


    printf("ipaddr1=%s,ipaddr2=%s\n",ipaddr1.data(),ipaddr2.data());
#else
    int validIPid=g_Global.m_netCardList[g_Global.m_bValidNetCardId].m_nValidIPId;
    ipaddr1=g_Global.m_netCardList[g_Global.m_bValidNetCardId].m_szIPInfo[validIPid].m_szIP;
    ipaddr2=g_Global.m_netCardList[g_Global.m_bValidNetCardId].m_szIPInfo[1].m_szIP;
#endif

}


//获取多个子网掩码
void Get_Multi_NetMask(string &subnet_mask1,string &subnet_mask2)
{
#if defined(Q_OS_LINUX)
    if(!g_Global.m_bNmcliValid)
        return;
    string cmd = " nmcli dev show | grep 'IP4.ADDRESS' | head -2 | awk '{print $2}' | sed -n '1p' "; 
    FILE * fp=NULL;
    char pRetMsg[1024]={0};

    if ((fp = popen(cmd.c_str(), "r") ) == NULL)
    {
        printf("Popen Get_Multi_NetMask1 Error!\n");
        return;
    }
    else
    {
        fgets(pRetMsg,1024, fp);
        if(pRetMsg[strlen(pRetMsg) - 1] == '\n')    //去掉换行符
            pRetMsg[strlen(pRetMsg) - 1] = '\0' ;
        string ip_addr(pRetMsg);
        string::size_type pos=0;
        if( (pos=ip_addr.find("/") )!=string::npos  )
        {
            string ip_address=ip_addr.substr(0,pos);
            string subnet_mask_tmp=ip_addr.substr(pos+1,ip_addr.length()-pos);
            GetSubMaskByBit(atoi(subnet_mask_tmp.c_str()),subnet_mask1);
        }
    }
    pclose(fp);
    fp=NULL;


    cmd = " nmcli dev show | grep 'IP4.ADDRESS' | head -2 | awk '{print $2}' | sed -n '2p' "; 
    if ((fp = popen(cmd.c_str(), "r") ) == NULL)
    {
        printf("Popen Get_Multi_NetMask2 Error!\n");
        return;
    }
    else
    {
        fgets(pRetMsg,1024, fp);
        if(pRetMsg[strlen(pRetMsg) - 1] == '\n')    //去掉换行符
            pRetMsg[strlen(pRetMsg) - 1] = '\0' ;
        string ip_addr(pRetMsg);
        string::size_type pos=0;
        if( (pos=ip_addr.find("/") )!=string::npos  )
        {
            string ip_address=ip_addr.substr(0,pos);
            if(ip_address!="127.0.0.1")
            {
                string subnet_mask_tmp=ip_addr.substr(pos+1,ip_addr.length()-pos);
                GetSubMaskByBit(atoi(subnet_mask_tmp.c_str()),subnet_mask2);
            }
        }
    }
    pclose(fp);
    fp=NULL;


    printf("subnet_mask1=%s,subnet_mask2=%s\n",subnet_mask1.data(),subnet_mask2.data());
#else
    int validIPid=g_Global.m_netCardList[g_Global.m_bValidNetCardId].m_nValidIPId;
    subnet_mask1=g_Global.m_netCardList[g_Global.m_bValidNetCardId].m_szIPInfo[validIPid].m_szSubAddress;
    subnet_mask2=g_Global.m_netCardList[g_Global.m_bValidNetCardId].m_szIPInfo[1].m_szSubAddress;
#endif
}




//设置网络信息
void Set_Server_Network_Config(string &ip_address,string &subnet_mask,string &gateway,string &dns_server)
{
    //先查找出网卡节点名
    if(!g_Global.m_bNmcliValid)
        return;
    string cmd = " nmcli dev show | grep 'GENERAL.DEVICE' | head -1 | awk '{print $2}' "; 
    FILE * fp=NULL;
    char pRetMsg[1024]={0};
    string device_name;
    if ((fp = popen(cmd.c_str(), "r") ) == NULL)
    {
        printf("Popen device_name Error!\n");
        return;
    }
    else
    {
        fgets(pRetMsg,1024, fp);
        if(pRetMsg[strlen(pRetMsg) - 1] == '\n')    //去掉换行符
            pRetMsg[strlen(pRetMsg) - 1] = '\0' ;
        device_name += pRetMsg;
    }
    pclose(fp);
    fp=NULL;

    
    memset(pRetMsg,0,sizeof(pRetMsg));
    string connection_name;
    cmd = "nmcli conn show | grep " +device_name + " | head -1 | awk -F \"   *\" '{print $1}'";
    if ((fp = popen(cmd.c_str(), "r") ) == NULL)
    {
        printf("Popen conn_name Error!\n");
        return;
    }
    else
    {
        fgets(pRetMsg,1024, fp);
        if(pRetMsg[strlen(pRetMsg) - 1] == '\n')    //去掉换行符
            pRetMsg[strlen(pRetMsg) - 1] = '\0' ;
        connection_name += pRetMsg;
    }
    pclose(fp);
    fp=NULL;


    //设置连接信息,默认static

    //计算子网掩码位数
    int subMask1,subMask2,subMask3,subMask4;
    sscanf(subnet_mask.data(),"%d.%d.%d.%d",&subMask1,&subMask2,&subMask3,&subMask4);
    int subMaskLen=GetNumberof1(subMask1)+GetNumberof1(subMask2)+GetNumberof1(subMask3)+GetNumberof1(subMask4);
    
    char subMaskChar[10]={0};
    sprintf(subMaskChar,"/%d",subMaskLen);
    string IPaddress_subMask=ip_address+subMaskChar;


    CMyString scriptCMD;
    scriptCMD.Format("nmcli con mod \"%s\" ipv4.method manual ipv4.address %s ipv4.gateway %s ipv4.dns %s", connection_name.data(), IPaddress_subMask.data(),gateway.data(),dns_server.data());
    system(scriptCMD.C_Str());
    printf("cmd=%s\r\n",scriptCMD.Data());

    scriptCMD.Format("%s","nmcli connection reload");
    system(scriptCMD.C_Str());
    printf("cmd=%s\r\n",scriptCMD.Data());
    scriptCMD.Format("%s \"%s\"","nmcli connection up",connection_name.data());
    system(scriptCMD.C_Str());
    printf("cmd=%s\r\n",scriptCMD.Data());

    //printf("device_name=%s,connection_name=%s\n",device_name.c_str(),connection_name.c_str());
    //printf("IPaddress_subMask=%s\n",IPaddress_subMask.c_str());

}








static int _to_int(const char * str, int start_idx, int end_idx)
{
    int a = 0, i;
    for (i = start_idx; i <= end_idx; ++i)
    {
    a = a * 10 + (str[i] - '0');
    }
    
    return a;
}
 
/*
 * 将ip字符串转化为4字节的整形
 */
int ip_to_int(const char * ip)
{
    int start = 0, i = 0, ret = 0;
    int shift_factor = 3; // 一开始要向右移动3 * 8位
    char c;
    while (c = ip[i])
    {
    if (c == '.')
    {
    int a = _to_int(ip, start, i - 1);
    int temp = shift_factor * 8;
    ret = ret | (a << temp);

    shift_factor--;
    start = i + 1;
    }
    i++;
    }

    return ret;
}

//IP是否属于局域网
bool isIPLAN(const char * szIP)
{
    if(!IsIPFormat(szIP))
        return false;
	istringstream st(szIP);
	int ip[2];
	for(int i = 0; i < 2; i++)
	{
		string temp;
		getline(st,temp,'.');
		istringstream a(temp);
		a >> ip[i];
	}
	if((ip[0]==10) || (ip[0]==172 && ip[1]>=16 && ip[1]<=31) || (ip[0]==192 && ip[1]==168) || (ip[0]==169 && ip[1]==254))
		return true;
	return false;
}


//取消WEB登录默认显示Admin
void Cancel_Web_Default_Admin_Login()
{
    //首先判断文件是否存在,如果不存在，那么写入文件，"IS_PLACE_DEFAULT_USER_ADMIN": "false"
    //如果存在，判断"IS_PLACE_DEFAULT_USER_ADMIN": "false"，如果不是，写入。
    CMyString webConfigPath=g_Global.m_strFolderPath+"/config.json";
    FILE* file = fopen(webConfigPath.C_Str(), "a+");   //此处只能用a+,如果是w+,那么每次文件内容都会被清空
    if(file == NULL)
    {
        return;
    }
    
    fseek(file,0,SEEK_END);
    long FileLength=ftell(file);
    fseek(file,0,SEEK_SET);
    char *FileContent=(char*)malloc(FileLength+1);
    fread(FileContent,sizeof(char),FileLength,file);
    FileContent[FileLength] = '\0';

    int update_file=0;
    cJSON* root = cJSON_Parse(FileContent);
    free(FileContent);
    if(root == NULL)
    {
        update_file=1;
        root=cJSON_CreateObject();
    }
    cJSON* jsPlaceAdmin = cJSON_GetObjectItem(root, "IS_PLACE_DEFAULT_USER_ADMIN");
    if(jsPlaceAdmin!=NULL)
    {
        //如果现在的值不是false,那么将其修改为false
        if( strcasecmp(jsPlaceAdmin->valuestring,"false") !=0 )
        {
            //修改
            cJSON_SetValuestring(jsPlaceAdmin,"false");
            update_file=1;
        }
    }
    else
    {
        //写入
        cJSON_AddItemToObject(root,"IS_PLACE_DEFAULT_USER_ADMIN",cJSON_CreateString("false"));
        update_file=1;
    }

    if(update_file)
    {
        //由于之前是附加可读写方式打开，如果直接写的话会与文件原来的内容冲突，所以需要关闭文件再重新以覆写方式打开，清空原内容。
        fclose(file);
        file = fopen(webConfigPath.C_Str(), "w+");
        if(file != NULL)
        {
            FileContent = cJSON_Print(root);
            fwrite(FileContent, strlen(FileContent), 1, file);
            free(FileContent);
        }
    }

    if(file!=NULL)
        fclose(file);
    if(root != NULL)
        cJSON_Delete(root);
}



//设置系统主题配置文件
void SetSystemThemeConfig(string key,string value)
{
    CMyString webConfigPath=g_Global.m_strFolderPath+"/ui-config.json";
    FILE* file = fopen(webConfigPath.C_Str(), "a+");   //此处只能用a+,如果是w+,那么每次文件内容都会被清空
    if(file == NULL)
    {
        return;
    }
    
    fseek(file,0,SEEK_END);
    long FileLength=ftell(file);
    fseek(file,0,SEEK_SET);
    char *FileContent=(char*)malloc(FileLength+1);
    fread(FileContent,sizeof(char),FileLength,file);
    FileContent[FileLength] = '\0';

    int update_file=0;
    cJSON* root = cJSON_Parse(FileContent);
    free(FileContent);
    if(root == NULL)
    {
        update_file=1;
        root=cJSON_CreateObject();
    }
    cJSON* jsPlaceAdmin = cJSON_GetObjectItem(root, key.data());
    if(jsPlaceAdmin!=NULL)
    {
        cJSON_SetValuestring(jsPlaceAdmin,value.data());
        update_file=1;
    }
    else
    {
        //写入
        cJSON_AddItemToObject(root,key.data(),cJSON_CreateString(value.data()));
        update_file=1;
    }

    if(update_file)
    {
        //由于之前是附加可读写方式打开，如果直接写的话会与文件原来的内容冲突，所以需要关闭文件再重新以覆写方式打开，清空原内容。
        fclose(file);
        file = fopen(webConfigPath.C_Str(), "w+");
        if(file != NULL)
        {
            FileContent = cJSON_Print(root);
            fwrite(FileContent, strlen(FileContent), 1, file);
            free(FileContent);
        }
    }

    if(file!=NULL)
        fclose(file);
    if(root != NULL)
        cJSON_Delete(root);
}



//取消网页目录访问
void Disable_Web_indexes_access()
{
    //首先判断文件是否存在,如果不存在，那么写入文件，"IS_PLACE_DEFAULT_USER_ADMIN": "false"
    //如果存在，判断"IS_PLACE_DEFAULT_USER_ADMIN": "false"，如果不是，写入。
    CMyString web_htaccess_Path=g_Global.m_strFolderPath+"/.htaccess";
    std::ifstream file(web_htaccess_Path.Data());
    if (!file)
    {
        printf("Disable_Web_indexes_access:Not found!\n");
        return;
    }
    
    std::string line;
    bool found_options_indexes=false;
    while(std::getline(file, line))
    {
        if (line.find("Options -Indexes") != std::string::npos)
        {
            found_options_indexes=true;
            break;
        }
    }
    if(found_options_indexes)
    {
        printf("Disable_Web_indexes_access:has found!\n");
        file.close();
        return;
    }

    //文件已到达结尾: 如果在调用file.seekg()之前你已经读取完了整个文件，那么文件指针已经在文件末尾，此时file.seekg()不会起作用。
    //你可以使用file.clear()来清除文件状态标志，然后使用file.seekg(0, std::ios::beg)将文件指针移动到开头。
    file.clear();   //一定要先清除文件状态标志
    file.seekg(0,ios::beg);  //回到开头位置

    std::string fileContents((std::istreambuf_iterator<char>(file)),
                              std::istreambuf_iterator<char>());
    file.close();
    
    std::ofstream outputFile(web_htaccess_Path.Data());
    outputFile << "Options -Indexes\n" << fileContents;
    outputFile.close();

     printf("Disable_Web_indexes_access:Add OK!\n");
}



bool IsProcessRun(char *processName,bool IsShell)
{
    char cmd[256]={0};
    if(IsShell)
    {
        sprintf(cmd,"pidof -x %s",processName);
    }
    else
    {
        sprintf(cmd,"pidof %s",processName);
    }
    string strProcessPid = "";

    FILE * fp=NULL;
    if ((fp = popen(cmd, "r") ) == NULL)
    {
        printf("Popen Error!\n");
    }
    else
    {
        char pRetMsg[1024]={0};
        //get lastest result
        while(fgets(pRetMsg,1023, fp) != NULL)
        {
            strProcessPid += pRetMsg;
        }
        pclose(fp);
    }

    if(strProcessPid.length()>0)
    {
        //printf("IsProcessRun:%s Yes!\n",processName);
        return true;
    }
    else
    {
        //printf("IsProcessRun:%s No!\n",processName);
        return false;
    }
}

//停止进程
void StopProcess(char *processName,bool IsShell)
{
    if(IsProcessRun(processName,IsShell))
    {
        char cmd[256]={0};
        if(IsShell)
        {
            sprintf(cmd,"pidof -x %s | xargs sudo kill",processName);
        }
        else
        {
            sprintf(cmd,"pidof %s | xargs sudo kill",processName);
        }
        system(cmd);
        printf("StopProcess:%s succeed!\n",processName);
    }
}




bool IsCommandValid(char *cmdName)
{
    char cmd[256]={0};
    sprintf(cmd,"%s",cmdName);
    string strCommandResult = "";

    FILE * fp=NULL;
    if ((fp = popen(cmd, "r") ) == NULL)
    {
        printf("Popen Error!\n");
    }
    else
    {
        char pRetMsg[1024]={0};
        //get lastest result
        while(fgets(pRetMsg,1023, fp) != NULL)
        {
            strCommandResult += pRetMsg;
        }
        pclose(fp);
    }

    if(strCommandResult.length() == 0)
    {
        printf("IsCommandValid:%s No!\n",cmdName);
        return false;
    }
    else if(strCommandResult.find("not found")==string::npos)
    {
        printf("IsCommandValid:%s Yes!\n",cmdName);
        return true;
    }
    return false;
}



int16_t limit_value_16bit(int32_t value)
{
    int16_t result;
    if(value<-32767)
    {
        result = -32767;
    }
    else if(value>32766)
    {
        result=32766;
    }
    else
    {
        result=(int16_t)value;
    }
    return result;
}


string QHostAddressToString(QHostAddress peerAddress)
{
    QString ipString;
    //检查IP地址的协议类型
    if (peerAddress.protocol() == QAbstractSocket::IPv6Protocol) {
            // 检查是否为IPv4映射的IPv6地址
        if (peerAddress.toString().startsWith(QLatin1String("::ffff:"))) {
            // 移除IPv6前缀，获取IPv4地址
            ipString = peerAddress.toString().mid(7);
        } else {
            // 这是一个纯IPv6地址
            ipString = peerAddress.toString();
        }
    }
    else
    {
        // 直接获取IPv4地址的字符串表示
        ipString = peerAddress.toString();
    }
    return ipString.toLocal8Bit().data();
}


std::string toHexString(uint8_t zijie) {
    const char* hexDigits = "0123456789ABCDEF";
    std::string hexString = "";
    
    // 先转换高位的4个位
    hexString += hexDigits[zijie >> 4];
    // 再转换低位的4个位
    hexString += hexDigits[zijie & 0x0F];
    
    return hexString;
}

//生成随机MAC（第一个字节可以指定，生成后的MAC全部大写）
string generateRandomMac(uint8_t firstByte) {
    std::random_device rd;
    std::mt19937 generator(rd());
    std::uniform_int_distribution<> distribution(0, 255);

    // 确保第一个字节的高位是0，即0x00 - 0x7F
    std::string mac = toHexString(firstByte) + ":";

    for (int i = 0; i < 5; ++i) {
        // 生成随机的两个字节，格式为xx
        uint8_t randomByte = distribution(generator);
        // 转换为十六进制字符串
        mac += toHexString(randomByte) + ":";
    }

    // 去掉最后一个冒号
    mac.pop_back();

    return mac;
}
