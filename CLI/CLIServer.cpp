#include "stdafx.h"
#include "CLIServer.h"

#define  MAX_EVENTS_COUNT        256
#define  TIMEWAIT           5


enum
{
   CLI_CMD_LOG_START = 0x0030,
   CLI_CMD_LOG_CLOSE,
   CLI_CMD_VIEW_SECTION,
   CLI_CMD_VIEW_USER,
};


CLIServer::CLIServer()
{
    //m_nConnectCount = 0;
}

bool CLIServer::StartWorking()
{
    pthread_t pth;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&pth, &attr, CLIPthread, (void*)this);
    pthread_attr_destroy(&attr);
    return true;
}

bool CLIServer::InitSocket()
{
#if defined(Q_OS_LINUX)
    m_CLISocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if(m_CLISocket == -1)
    {
        perror("CLI create socket failed");
        return false;
    }

    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr =  htonl(INADDR_ANY);
    server_addr.sin_port = htons(CLI_PORT);

    /*设置地址重用,以解决第二次运行时端口占用的问题(SO_REUSEADDR允许完全相同的地址和端口的重复绑定，但这只用于UDP)*/
    int opt = 1;
    if((setsockopt(m_CLISocket,SOL_SOCKET,SO_REUSEADDR,&opt,sizeof(opt)))<0)//设置socket状态,允许在 bind（）过程中本地地址可重复使用
    {
        perror("setsockopt SO_REUSEADDR failed");
        close(m_CLISocket);
    }

    if(bind(m_CLISocket, (sockaddr*)&server_addr, sizeof(server_addr)) == -1)
    {
        perror("CLI bind socket failed!");
        close(m_CLISocket);
        return false;
    }

    if(listen(m_CLISocket, MAX_LISTEN_COUNT) == -1)
    {
        perror("web listen socket failed!");
        close(m_CLISocket);
        return false;
    }

    m_epollFd = epoll_create(MAX_EVENTS_COUNT);
    CtlEpollEvent(m_CLISocket, true);

    printf("cli server is run\n");
#endif
    return true;
}

void CLIServer::startEpoll()
{
#if defined(Q_OS_LINUX)
    struct sockaddr_in client_addr;
    memset(&client_addr,0, sizeof(client_addr));
    socklen_t cliLen = 0;
    int nfds = 0;
    int fd = 0;
    int bufLen = 0;
    struct epoll_event events[MAX_EVENTS_COUNT];

    char szBuf[MAX_BUF_LEN] = {0};

    while(true)
    {
        nfds = epoll_wait(m_epollFd, events, MAX_EVENTS_COUNT, TIMEWAIT);
        if(nfds == -1)
        {
            printf("epoll_wait failed %s\n",strerror(errno));
            if(errno!=EINTR)
            {
               break;
            }
            continue;
        }
        //printf("nfds = %d\n", nfds);
        for(int i=0; i<nfds; i++)
        {
            if(events[i].data.fd == m_CLISocket)
            {
                fd = accept(m_CLISocket, (sockaddr*)&client_addr, &cliLen);
                if(fd > 2)
                {
                    CtlEpollEvent(fd, true);    // 新的连接，加入epoll
                    printf("accept by : %d success\n", fd);
                }
                else
                {
                    printf("accept by : %d failed\n", fd);
                }
                continue;
            }

            if(events[i].events & EPOLLHUP)
            {
                if((fd = events[i].data.fd) < 0)
                    continue;
                CtlEpollEvent(fd, false);
            }

            else if(events[i].events & EPOLLERR)
            {
                if((fd = events[i].data.fd) < 0)
                    continue;
                CtlEpollEvent(fd, false);
            }

            if(events[i].events & EPOLLIN)
            {
                if((fd = events[i].data.fd) < 0)
                    continue;

                memset(szBuf, 0,sizeof(szBuf));
                if((bufLen=recv(fd, szBuf, MAX_BUF_LEN, MSG_DONTWAIT)) <= 0)
                {
                    ERROR_LOG("websocekt fd=%d read error,bufLen=%d\n", fd,bufLen);
                    
                    if( bufLen<0 &&(errno == EAGAIN||errno == EWOULDBLOCK||errno == EINTR) )
                    {
                        //正常情况，重新读取即可
                    }
                    else    //非上述情况表示接收出错，需要关闭socket
                    {
                        //关闭此socket
                        g_Global.m_Log.RemoveCLIFd(fd);        // 移除日志CLI socket
                        CtlEpollEvent(fd, false);
                    }
                    continue;
                }
                //printf("buf len: %d , szBuf len : %d\n", bufLen, strlen(szBuf));
                CLIServer::HandleCommand(fd, szBuf, bufLen);
             }
        }

    }
#endif
}

void CLIServer::HandleCommand(int fd,                            // socket fd
                                                          LPCSTR	pBuf,				// 数据区
                                                          USHORT	nLen)			// 数据长度
{
    int  command     = pBuf[0];      // 命令
    int nDataLen = pBuf[2];

    switch (command) {
    case CLI_CMD_LOG_START:
    {
        g_Global.m_Log.AddCLIFd(fd);        // 添加日志CLI socket
        break;
    }
    case CLI_CMD_LOG_CLOSE:
    {
        g_Global.m_Log.RemoveCLIFd(fd);        // 移除日志CLI socket
        break;
    }
    case CLI_CMD_VIEW_SECTION:
    {
        HandleGetSectionInfo(fd, &pBuf[3], nDataLen);
        break;
    }
    case CLI_CMD_VIEW_USER:
    {
        HandleGetUserInfo(fd, &pBuf[3], nDataLen);
        break;
    }
    default:
        break;
    }

}

// 获取分区信息
void CLIServer::HandleGetSectionInfo(int fd, const char *pData, int nDataLen)
{
    int nSectionCount = g_Global.m_Sections.GetSecCount();
    int nCount = 0;
    char buf[1024] = {0};

    write(fd, "\n", strlen("\n"));
    for(int i=0; i<nSectionCount; i++)
    {
        CSection &device = g_Global.m_Sections.GetSection(i);

        char data[1024] = {0};
        sprintf(data, "%s    %s\n", device.GetName(), device.IsOnline() ? "Online" : "Offline");
        strcat(buf, data);
        nCount++;

        if(nCount >=20 || nCount == nSectionCount)
        {
            write(fd, buf, strlen(buf));

            nCount = 0;
            memset(buf, 0,1024);
        }
    }
}

// 获取用户信息
void CLIServer::HandleGetUserInfo(int fd, const char *pData, int nDataLen)
{
    int nUserCount = g_Global.m_Users.GetUserCount();
    int nCount = 0;
    char buf[1024] = {0};

    write(fd, "\n", strlen("\n"));
    for(int i=0; i<nUserCount; i++)
    {
        LPCUserInfo pUser = g_Global.m_Users.GetUser(i);

        char data[1024] = {0};
        sprintf(data, "%s    secCount:%d\n", pUser->GetAccount(), pUser->GetSectionCount());
        strcat(buf, data);
        nCount++;

        if(nCount >=20 || nCount == nUserCount)
        {
            write(fd, buf, strlen(buf));

            nCount = 0;
            memset(buf,0, 1024);
        }
    }
}


void *CLIServer::CLIPthread(void *lparam)
{
    CLIServer* pCLI = (CLIServer*)lparam;

    if(pCLI->InitSocket())
    {
        pCLI->startEpoll();
    }
    return NULL;
}


// 设置socket 连接，关闭
void CLIServer::CtlEpollEvent(int fd, bool flag)
{
#if defined(Q_OS_LINUX)
    struct epoll_event ev;
    ev.data.fd = fd;
    ev.events  = flag ? (EPOLLIN|EPOLLHUP|EPOLLERR) : 0;

    int nRet = epoll_ctl(m_epollFd, flag ? EPOLL_CTL_ADD : EPOLL_CTL_DEL, fd, &ev);
    if(nRet == -1)
    {
            perror("epoll_ctl  failed : ");
            return;
    }

    if(flag)
    {
        SetNoblock(fd);
        if(fd != m_CLISocket) //&& m_nConnectCount == 0)       // 只允许一个连接
        {
            LOG(FORMAT("fd: %d join epoll loop\n", fd), LV_INFO);
            //m_nConnectCount++;
        }
    }
    else
    {
        close(fd);
        //m_nConnectCount --;
        //g_Global.m_Log.SetCLIFd(-1);        // 设置日志CLI socket
        //printf("fd: %d quit epoll loop\n", fd);
        LOG(FORMAT("fd: %d quit epoll loop\n", fd), LV_INFO);
    }
#endif
}

// 设置套接字非阻塞
int CLIServer::SetNoblock(int fd)
{
#if defined(Q_OS_LINUX)
    int flags;
    if ((flags = fcntl(fd, F_GETFL, 0)) == -1)
        flags = 0;
    return fcntl(fd, F_SETFL, flags | O_NONBLOCK);
#else
	return true;
#endif
}



