#ifndef CLISERVER_H
#define CLISERVER_H

#include "Global/CType.h"
#include "Lib/SBETcpClientSocket.h"
#include "Lib/SBETcpServerSocket.h"
#include "Lib/SBEUdpSocket.h"


#define CLI_PORT                            50120
#define MAX_LISTEN_COUNT                    50

class CLIServer
{
public:
    CLIServer();

    bool StartWorking();

    bool InitSocket();

    void startEpoll();

    static void HandleCommand(int fd,                   // socket fd
                              LPCSTR	pBuf,			// 数据区
                              USHORT	nLen);			// 数据长度

    static void HandleGetSectionInfo(int fd, const char* pData, int nDataLen);

    static void HandleGetUserInfo(int fd, const char* pData, int nDataLen);

private:
    static void* CLIPthread(void* lparam);

    void   CtlEpollEvent(int fd, bool flag);

    int    SetNoblock(int fd);

public:
    //int        m_nConnectCount;

private:
    //CSBETcpServerSocket	m_TcpServer;				// TCP服务器
    CUDPSocket*		m_pSocketCLI;						// CLI UDPSocket
    CSBEUdpSockets	m_udpSockets;
    int        m_CLISocket;
    int        m_epollFd;
};

#endif // CLISERVER_H
