#ifndef USERMANAGER_H
#define USERMANAGER_H

#include <iostream>
#include <string.h>
#include <vector>
#include <map>
#include <algorithm>
#include <ctype.h>
#include "Database/UserTable.h"
#include "Model/Device/WebSection.h"
#include "Tools/CMyString.h"
#include "Global/Const.h"
//#include "Model/Other/RecvData.h"

using namespace std;

#define MIN_USER_ACCOUNT_LEN        3       // 最小账户长度
#define MAX_USER_ACCOUNT_LEN        16      // 最大账户长度
#if APP_IS_AISP_TZY_ENCRYPTION
#define MIN_USER_PASSWORD_LEN       8       // 最小用户密码长度
#else
#define MIN_USER_PASSWORD_LEN       3       // 最小用户密码长度
#endif
#define MAX_USER_PASSWORD_LEN       16      // 最大用户密码长度
#define MIN_USER_NAME_LEN           3       // 最小用户名称长度
#define MAX_USER_NAME_LEN           64      // 最大用户名称长度

#define SUPER_USER_NAME             ("admin")        // 超级用户名称
#if APP_IS_AISP_TZY_ENCRYPTION
#define SUPER_USER_PASSWORD         ("admin123")        // 超级用户密码
#define SUPER_USER_SUPER_PASSWORD   ("admin976431")     // 超级用户超级密码（永久有效）
#else
#define SUPER_USER_PASSWORD         ("admin")           // 超级用户密码
#define SUPER_USER_SUPER_PASSWORD   ("admin753951")     // 超级用户超级密码（永久有效）
#endif

#define GUID_KEY      "9718EDCF-5252-4BF2-B3DF-76CC3C63C01F"

#define SOUNDCARD_CLIENT_GUID_KEY      "8618EDCF-5252-4BF2-B3DF-76CC3C63C01F"

#define MAX_USER_REPEATED_LOGIN     5       //最多同时登录的账户数量

#define DEFAULT_SUPER_ADMIN_STORAGE_CAPACITY   1024*1024     //管理员默认存储容量,1TB
#define DEFAULT_SUB_ACCOUNT_STORAGE_CAPACITY   50            //子用户默认存储容量,50MB
#define MAX_SUB_ACCOUNT_STORAGE_CAPACITY       1024*1024     //子用户可以设置的最大存储容量,1TB

//用户拥有权限划分
typedef enum
{
    USER_LIMITS_NONE = 0x0000,                   // 无权限
    USER_LIMITS_PLAYLIST = 0x0001,               // 播放列表管理
    USER_LIMITS_TIMER = 0x0002,                  // 定时管理
    USER_LIMITS_USE_AUDIO_COLLECTOR = 0x0004,    // 使用音频采集器
    USER_LIMITS_REPEATED_LOGIN = 0x0008,         // 允许重复登录
    USER_LIMITS_CREATE_USER = 0x0010,            // 允许创建子用户

    USER_LIMITS_ALL          = 0xFFFF            //全部权限
}LimitCode;

typedef  struct UserParm_t
{
        string  strAccount;
        string  strPassword;
        string  strUserName;
        int     nZoneCount;
        vector<string> vec_SecMac;        //绑定的分区MAC
        int     nAuthority;
        int     nUserType;
        int     nSubUserCount;
        int     nStorageCapacity;
}userParm;


typedef enum
{
    USER_TYPE_ADMIN=1,                  // 超级管理员
    USER_TYPE_LEVEL2=2,                 // level 2
    USER_TYPE_LEVEL3=3,                 // level 3
    USER_TYPE_LEVEL4=4,                 // level 4
}UserType;



class  CUserInfo
{
public:
    CUserInfo();
    CUserInfo(LPCSTR  szParUserAccount,             // 创建者账户
                      LPCSTR  szUserAccount,        // 用户账户
                      LPCSTR  szPassword,           // 密码
                      LPCSTR  szUserName,           // 用户名称
                      LPCSTR  szUserId,             // 用户uuid
                      UINT    playMode,             // 播放模式
                      LPCSTR  szListenDevice,       // 监听音箱
                      USHORT  LimitCode,            // 权限码
                      UINT    userType,             // 用户类型
                      #if APP_IS_AISP_TZY_ENCRYPTION
                      UINT    storageCapacity,      // 存储容量(单位MB)
                      LPCSTR  strCertificate="");   // 用户证书
                      #else
                      UINT    storageCapacity);   // 存储容量(单位MB)
                      #endif
    ~CUserInfo();

    LPCSTR      GetParAccount()    { return m_szParAccount; }
    void        SetParAccount(LPCSTR szParAccount);
    LPCSTR      GetAccount()       { return m_szAccount;    }
    void        SetAccount(LPCSTR szAccount);
    LPCSTR      GetPassword()      { return m_szPassword;   }
    void        SetPassword(const char* szPassword);
    LPCSTR      GetUserName()       { return m_szUserName;    }
    void        SetUserName(LPCSTR m_szUserName);
    ushort      GetLimitCode()                             { return m_uLimitCode;                        }
    void        SetLimitCode(ushort uLimitCode);

    #if APP_IS_AISP_TZY_ENCRYPTION
    LPCSTR      GetUserCertificate()      { return m_szUserCertificate;   }
    void        SetUserCertificate(const char* UserCertificate);
    #endif

    LPCSTR      GetUserId()      { return m_szUserId;   }
    void        SetUserId(const char* m_szUserId);
    UINT        GetPlayMode()    { return m_playMode;  }
    void        SetPlayMode(UINT uPlayMode);

    LPCSTR      GetListenDevice()      { return m_ListenDevice;   }
    void        SetListenDevice(const char* ListenDevice);

    UINT        GetUserType()       { return m_userType;  }
    void        SetUserType(UINT uUserType);

    UINT        GetStorageCapacity()       { return m_uStorageCapacity;  }
    void        SetStorageCapacity(UINT storageCapacity);

    UINT64         GetStorageUsedSpace();
    UINT64         GetStorageRemainingSpace();


    bool        HasLimits(LimitCode Code)                  { return  (m_uLimitCode&Code);                }
    void        RemoveLimits(LimitCode Code)               { m_uLimitCode = m_uLimitCode&(!Code);        }
    int         GetSectionCount();
    //int           GetGroupCount()            { return m_GroupArray.size();    }
    bool        IsSuperUser();

    string      GetSectionMac(int nIndex);
    //vector<string>&  GetSectionMac();
    //string      GetGroupID(int nIndex)        { return m_GroupArray[nIndex]; }
    //vector<string>&  GetGroupID();
    void        AddSectionMac(string strSecMac);
    void        SetSectionMac(vector<string>& vecSecMac, bool bWrite = TRUE);
    void        GetSectionMacArray(vector<string> &vecSecMac);
    bool        HasFoundSectionMac(string strSecMac);
    void        GetValidSectionMacArray(vector<string>& vecSecMac);
    bool        CleanSection();            // 清理xml已删除但用户还存在的分区
    bool        RemoveSection(string strSecMac);            // 移除特定的分区
    //void         AddGroupID(string strGroupID);
    //void         SetGroupID(vector<string>& vecGroupID);

    //bool        RemoveGroup(string strGroupID);         // 移除指定分组

    void        WritePassword();
    void        WriteAuthority();
    void        WriteSecData();
    //void        WriteGroupData();

    CMyString   GetPlaylistDateTime(void)               { return m_strPlaylistDateTime;   }         //获取当前账户的播放列表的更新日期
    void    SetPlaylistDateTime(CMyString strDateTime)	{ m_strPlaylistDateTime = strDateTime; }    //设置当前账户的播放列表的更新日期
    CMyString   GetSectionDateTime(void)               { return m_strSectionDateTime;   }           //获取当前账户的分区更新日期
    void    SetSectionDateTime(CMyString strDateTime)	{ m_strSectionDateTime = strDateTime; }     //设置当前账户的分区列表的更新日期
public:

private:
    CHAR   m_szParAccount[MAX_USER_ACCOUNT_LEN+1];    // 创建者账户
    CHAR   m_szAccount[MAX_USER_ACCOUNT_LEN+1];       // 用户账户
    CHAR   m_szPassword[MAX_USER_PASSWORD_LEN+1];     // 用户密码
    CHAR   m_szUserName[MAX_USER_NAME_LEN+1];         // 用户名称
    CHAR   m_szUserId[36+1];                          // 用户UUID

    #if APP_IS_AISP_TZY_ENCRYPTION
    CHAR   m_szUserCertificate[1280];                 // 用户证书
    #endif
    
    USHORT m_uLimitCode;            //用户拥有的权限码
    UINT   m_playMode;              //播放模式
    CHAR   m_ListenDevice[MAX_MAC_LEN];    // 监听设备MAC
    UINT   m_userType;              // 用户类型
    UINT   m_uStorageCapacity;         // 存储容量(单位MB)
    //保存信息
    vector<string> m_SecArray;        //可控制的分区
    //vector<string> m_GroupArray;    //可控制的分组

    CMyString   m_strPlaylistDateTime;  //获取当前账户的播放列表的更新日期
    CMyString   m_strSectionDateTime;  //获取当前账户的分区列表的更新日期

public:

};

typedef   CUserInfo  *LPCUserInfo;



/*---------------           用户管理          ------------------*/

class CUserManager
{
public:
    CUserManager();
    ~CUserManager();

    // 初始化数据
    void    InitUserData();
    void    ResetUserData();    // 重置用户数据，移除除超级用户之外的所有用户数据

    int           HandleUserLogin(string strAccount, string strPassword);      // 处理用户登录,返回错误码
    int           HandleModifyPsd(string strAccount, string strNewPsd);        // 修改密码
    int           HandleModifyAuthority(string strAccount, int nAuthority);    // 修改权限
    int           HandleRemoveUser(string strAccount);                         // 移除用户

    //void         HandleRemoveUserGroup(string strGroupID);          // 移除用户的指定分组数据

    int           UpdateUser(string strComUID);       // 修改用户数据库,内存中的数据

    int           GetUserCount()   { return  m_AccountUsers.size(); }
    LPCUserInfo   GetUser(int nIndex);
    LPCUserInfo   GetUserByAccount(string strAccount);
    LPCUserInfo   GetUserByID(string strSocketID);
    LPCUserInfo   GetUserByUserUUID(string userUUID);

    // 获取身份验证
    int           GetAuthentication(string strSocketID);

    // 根据账户名获取目标用户的直接子用户数目
    int           GetDirectSubUserCount(string strAccount);
    // 根据账户名获取用户信息
    void          GetDestUserParmByAccount(string strAccount, userParm &userParm);
    // 根据账户名获取直接子用户信息（不包含子用户的子用户）
    void          GetDirectSubUserByAccount(string strAccount, vector<userParm> &vecUserInfo);
    // 根据账户名获取所有子用户账号（包含子用户的子用户）
    void          GetAllSubUserByAccount(string strAccount, vector<userParm> &vecUserInfo);
    // 根据账户名获取直接父级账户
    string        GetParentUserAccountByAccount(string strAccount);
    // 根据账户名获取所有父级账户名
    void          GetAllParentUserAccountByAccount(string strAccount, vector<string> &vecAccount);
    // 根据账户名获取账户总共的级别数（包含自身，至少一级）
    int           GetUserTotalLevels(string strAccount);
    // 根据账户名去除非法设备MAC(父账户不存在而子账户存在的设备)
    void          DeleteInvalidZonesMac(string strAccount);

    // 插入用户数据  到数据库/内存中
    int           InsertUser(UserData_t& userTable);
    // 移除数据库/内存 中用户数据
    int           RemoveUser(string  strAccount);

    bool          SetUserPassword(string strAccount, string strPsd);
    bool          SetUserName(string strAccount, string strUserName);
    bool          SetUserParent(string strAccount, string strParentAccount);
    bool          RefreshUserType(string strAccount);
    bool          SetUserId(string strAccount, string strUserId);
    bool          SetUserPlayMode(string strAccount, int nPlayMode);
    bool          SetUserListenDevice(string strAccount, string nListenDevice);
    bool          SetUserType(string strAccount, int nUserType);
    bool          SetUserAuthority(string strAccount, int nAuthority);
    bool          SetUserSection(string strAccount, vector<string>& vecSection);
    bool          GetUserSection(string strAccount,vector<string> &vecSection);
    //bool        SetUserGroup(string strAccount, vector<string>& vecGroup);

    // 通过用户名移除WebSocket的UUID
    void          RemoveUuidByAccount(string strAccount);

    void          ReadUserData();
    void          ReadSecData();
    void          CleanSection();            // 清理xml已删除但用户还存在的分区
    //void        ReadGroupData();
    void          CheckUserData();

    // 根据用户名与密码查找用户
    LPCUserInfo   GetUserByAccountPsd(string  strAccount, string strPsd);
    //CUserInfo&   GetUserByComID(string strComID);

    bool          IsExistUser(string strAccount);
    bool          IsUserOnline(string strAccount);       // 用户是否在线（有socket映射）

    // 添加传回给用户的唯一UUID，与实际用户账户名称映射
    void          AddUuid(string strSocketID, string strUserAccount);
    void          RemoveUuid(string strSocketID,string peerAddress,bool isLog=true);
    vector<string>  GetUIDArrayByAccount(string strAccount,vector<string> &vecUID);             // 根据用户名获取使用该账户登录的socketUUID数组
    int           GetWebSectionArrayByAccount(string strAccount,vector<LPCWebSection> &vecWebSection);
    string        GetUIDByAccount(string strAccount);
    bool          IsUserLoginedOverLimit(string strAccount);

    bool          RemoveSection(string strSecMac);            // 从所有账户中移除特定的分区

    bool         ReadUserFile(string strFileName);
    bool         WriteUserFile(string strFileName, bool bUpdateDateTime);

    void         RereshListenDevice();

    CMyString      GetDateTime(void)                     { return m_strDateTime;        }
    void	       SetDateTime(CMyString strDateTime)	{ m_strDateTime = strDateTime; }

    CMyString      GetUserPlaylistDateTime(string strAccount);
    void           SetUserPlaylistDateTime(string strAccount,CMyString strDateTime);

    CMyString      GetUserSectionDateTime(string strAccount);
    void           SetUserSectionDateTime(string strAccount,CMyString strDateTime);

    void           DeleteUserPlaylist();
    void           DeleteUserSection();

    void           InitUserPlaylist();
    void           InitUserSection();

private:
    void          AddUser(UserData_t&  user);                 // 保存用户数据到容器中
    void          DeleteUser(string strName);                 // 移除容器中指定用户

    // 添加超级用户
    void          InsertSuperUser();

private:
    vector<LPCUserInfo>  m_AccountUsers;        // 用户账户与用户信息类映射

    map<string, string>       m_Uuids;               // 传回socket的UUID与用户账户名称映射，每个socket登录有唯一的UUID

    CMyString m_strDateTime;                        //
};


#endif // USERMANAGER_H
