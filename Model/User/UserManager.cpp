#include "stdafx.h"
#include "UserManager.h"
#include <algorithm>

CUserInfo::CUserInfo()
{

}

CUserInfo::CUserInfo(LPCSTR  szParUserAccount,  // 创建者账户
                     LPCSTR  szUserAccount,     // 用户账户
                     LPCSTR  szPassword,        // 密码
                     LPCSTR  szUserName,        // 密码
                     LPCSTR  szUserId,          // 用户uuid
                     UINT    playMode,          // 播放模式
                     LPCSTR  szListenDevice,    //监听音箱
                     USHORT  LimitCode,         // 权限码
                     UINT    userType,          // 用户类型
                     #if APP_IS_AISP_TZY_ENCRYPTION
                     UINT    storageCapacity,   // 存储容量(单位MB)
                     LPCSTR  strCertificate)    // 用户证书
                     #else
                     UINT    storageCapacity)   // 存储容量(单位MB)
                     #endif
{
    strcpy(m_szParAccount,szParUserAccount);
    strcpy(m_szAccount,szUserAccount);
    strcpy(m_szPassword,szPassword);
    strcpy(m_szUserName,szUserName);
    m_uLimitCode = LimitCode;
    strcpy(m_szUserId,szUserId);
    m_playMode = playMode;
    strcpy(m_ListenDevice,szListenDevice);
    m_userType = userType;
    m_uStorageCapacity = storageCapacity;
    m_strPlaylistDateTime="";
    m_strSectionDateTime="";

    #if APP_IS_AISP_TZY_ENCRYPTION
    strcpy(m_szUserCertificate,strCertificate);
    #endif
}


CUserInfo::~CUserInfo()
{

}

void CUserInfo::SetParAccount(const char *szParAccount)
{
    if(strcmp(m_szParAccount, szParAccount) != 0)
    {
        memset(m_szParAccount,0,sizeof(m_szParAccount));
        strcpy(m_szParAccount, szParAccount);
        g_Global.m_userTable.UpdateUserParAccount(m_szAccount, m_szParAccount);
    }
}

void CUserInfo::SetAccount(const char *szAccount)
{
    if(strcmp(m_szAccount, szAccount) != 0)
    {
        memset(m_szAccount,0,sizeof(m_szAccount));
        strcpy(m_szAccount, szAccount);
    }
}

void CUserInfo::SetUserName(const char *szName)
{
    if(strcmp(m_szUserName, szName) != 0)
    {
        memset(m_szUserName,0,sizeof(m_szUserName));
        strcpy(m_szUserName, szName);
        g_Global.m_userTable.UpdateUserName(m_szAccount, m_szUserName);
    }
}

void CUserInfo::SetPassword(const char *szPassword)
{
    if(strcmp(m_szPassword, szPassword) != 0)
    {
        memset(m_szPassword,0,sizeof(m_szPassword));
        strcpy(m_szPassword, szPassword);
        g_Global.m_userTable.UpdateUserPassword(m_szAccount, m_szPassword);
    }

}

void CUserInfo::SetLimitCode(ushort uLimitCode)
{
    if(m_uLimitCode != uLimitCode)
    {
        m_uLimitCode = uLimitCode;
        WriteAuthority();
    }

}


#if APP_IS_AISP_TZY_ENCRYPTION
void  CUserInfo::SetUserCertificate(const char* UserCertificate)
{
    if(strcmp(m_szUserCertificate, UserCertificate) != 0)
    {
        memset(m_szUserCertificate,0,sizeof(m_szUserCertificate));
        snprintf(m_szUserCertificate,sizeof(m_szUserCertificate),"%s",UserCertificate);
        g_Global.m_userTable.UpdateUserCertificate(m_szAccount, m_szUserCertificate);
    }
}
#endif


void   CUserInfo::SetUserId(const char* szUserId)
{
    if(strcmp(m_szUserId, szUserId) != 0)
    {
        sprintf(m_szUserId,"%s",szUserId);
        g_Global.m_userTable.UpdateUserId(m_szAccount, m_szUserId);
    }
}

void   CUserInfo::SetPlayMode(UINT uPlayMode)
{
    if( m_playMode != uPlayMode )
    {
        m_playMode = uPlayMode;
        g_Global.m_userTable.UpdateUserPlayMode(m_szAccount, m_playMode);
    }
}


void CUserInfo::SetListenDevice(const char* ListenDevice)
{
    sprintf(m_ListenDevice,"%s",ListenDevice);
    g_Global.m_userTable.UpdateUserListenDevice(m_szAccount, m_ListenDevice);
}

void CUserInfo::SetUserType(UINT uUserType)
{
    if( m_userType != uUserType )
    {
        m_userType = uUserType;
        g_Global.m_userTable.UpdateUserType(m_szAccount, uUserType);
    }
}
//设置用户存储空间（单位MB)
void CUserInfo::SetStorageCapacity(UINT storageCapacity)
{
    //限制管理员账户的存储容量固定在1TB
    if(IsSuperUser() && storageCapacity!=DEFAULT_SUPER_ADMIN_STORAGE_CAPACITY)
    {
        return;
    }
    if( m_uStorageCapacity != storageCapacity)
    {
        printf("m_szAccount=%s,SetStorageCapacity:%d\n",m_szAccount,storageCapacity);
        if(storageCapacity>=DEFAULT_SUB_ACCOUNT_STORAGE_CAPACITY && storageCapacity<=MAX_SUB_ACCOUNT_STORAGE_CAPACITY)    //子账户最大1TB
        {
            m_uStorageCapacity = storageCapacity;
            g_Global.m_userTable.UpdateUserStorageCapacity(m_szAccount, storageCapacity);
        }
    }
}

#if APP_IS_LZY_LIMIT_STORAGE
//获取该账户下媒体库的已用空间,单位字节（只统计该用户自身上传的）
UINT64 CUserInfo::GetStorageUsedSpace()
{
    return g_Global.m_SongManager.GetSongsUsedSpaceByUser(m_szAccount);
}
#endif

//获取该账户下媒体库的剩余空间,单位字节（只统计该用户自身上传的）
UINT64 CUserInfo::GetStorageRemainingSpace()
{
    //如果是非龙之音V1版本，那么返回的是系统剩余空间大小，预留512MB
    #if !APP_IS_LZY_LIMIT_STORAGE
    UINT64 hardDisk_total=0;       // 磁盘总大小
    UINT64 hardDisk_remain=0;      //剩余磁盘大小
    int hardDisk_total_int=0;       // 磁盘总大小
    int hardDisk_remain_int=0;      //剩余磁盘大小
    GetHardDiskSize(&hardDisk_total_int,&hardDisk_remain_int);
    hardDisk_remain=(UINT64)hardDisk_remain_int*1024*1024;      //转换成字节
    UINT64 remain_size_byte=512*1024*1024;             //512MB,转换成字节
    UINT64 remainSpace=hardDisk_remain-remain_size_byte>0?hardDisk_remain-remain_size_byte:0;
    return remainSpace;
    #endif
    UINT64 usedSpace=GetStorageUsedSpace();
    UINT64 remainingSpace=0;
    UINT64 m_uStorageCapacity_byte=((UINT64)m_uStorageCapacity)*1024*1024;   //将MB转换成字节
    //printf("usedSpace=%lld,m_uStorageCapacity_byte=%lld\n",usedSpace,m_uStorageCapacity_byte);
    if(m_uStorageCapacity_byte>usedSpace)
    {
        remainingSpace = m_uStorageCapacity_byte-usedSpace;
    }
    return remainingSpace;
}


int CUserInfo::GetSectionCount()
{
    if(IsSuperUser())
    {
        //return g_Global.m_Sections.GetSecCount();
        return 0;
    }
    else
    {
        return m_SecArray.size();
    }

}

bool CUserInfo::IsSuperUser()
{
    return (strcmp(m_szAccount, SUPER_USER_NAME) == 0);
}

string CUserInfo::GetSectionMac(int nIndex)
{
    if(IsSuperUser())
    {
        return g_Global.m_Sections.GetSection(nIndex).GetMac();
    }
    else
    {
        return m_SecArray[nIndex];
    }
}

#if 0
vector<string> &CUserInfo::GetSectionMac()
{
    vector<string>      vecSec = m_SecArray;
    return vecSec;
}
#endif

#if 0
vector<string> &CUserInfo::GetGroupID()
{
    vector<string>      vecSec = m_SecArray;
    return vecSec;
}
#endif

void CUserInfo::AddSectionMac(string strSecMac)
{
    vector<string>::iterator iter = find(m_SecArray.begin(), m_SecArray.end(), strSecMac);
    if(iter == m_SecArray.end())
    {
        m_SecArray.push_back(strSecMac);
    }
}

void CUserInfo::SetSectionMac(vector<string> &vecSecMac, bool bWrite)
{
    #if SUPPORT_AUDIO_MIXER
    //先判断新的集合中有没有音频混音器-解码器，如果存在，需要删除掉（见WEB原来添加了音频混音器-编码器，然后删除，就会只剩下一个解码器下发）
    for(int i=0;i<vecSecMac.size();i++)
    {
        LPCSection pSection=g_Global.m_Sections.GetSectionByMac(vecSecMac[i]);
        if(pSection!=NULL)
        {
            if(pSection->GetDeviceModel() == MODEL_AUDIO_MIXER_DECODER || pSection->GetDeviceModel() == MODEL_AUDIO_MIXER_DECODER_C)
            {
                vecSecMac.erase(std::remove(vecSecMac.begin(), vecSecMac.end(), vecSecMac[i]), vecSecMac.end());
            }
        }
    }

    //判断有没有存在音频混音器-编码器，如果存在，则将对应的解码器mac也加入；无需判断解码器，因为WEB已经限制了只能加入编码器
    for(int i=0;i<vecSecMac.size();i++)
    {
        if(g_Global.m_AudioMixers.GetSectionByMac(vecSecMac[i])!=NULL)
        {
            string mixerDecoderMac = vecSecMac[i];
            mixerDecoderMac.replace(0, 2, "2B");
            auto it = find(vecSecMac.begin(), vecSecMac.end(), mixerDecoderMac);
            if (it != vecSecMac.end()) {
                // 找到了解码器的mac,不处理
            } else {
                // 没有找到解码器的mac，加入
                vecSecMac.push_back(mixerDecoderMac);
            }
        }
    }
    #endif

    m_SecArray = vecSecMac;

    if(bWrite)
    {
        WriteSecData();
    }
}

void CUserInfo::GetSectionMacArray(vector<string> &vecSecMac)
{
    vecSecMac=m_SecArray;
}

bool CUserInfo::HasFoundSectionMac(string strSecMac)
{
    if(IsSuperUser())
        return true;
    if(std::find(m_SecArray.begin(),m_SecArray.end(),strSecMac) != m_SecArray.end())
    {
        return true;
    }
    return false;
}

void CUserInfo::GetValidSectionMacArray(vector<string>& vecSecMac)
{
    if(IsSuperUser())
        return;
    vector<string>::iterator iter = vecSecMac.begin();
    for(; iter != vecSecMac.end();)
    {
        string strMac = *iter;
        if(!HasFoundSectionMac(strMac))
        {
            iter=vecSecMac.erase(iter);
        }
        else
        {
            iter++;
        }
    }
}

// 清理xml已删除但用户还存在的分区
bool CUserInfo::CleanSection()
{
    if(IsSuperUser())       // 超级用户不处理
    {
        return false;
    }

    vector<string>::iterator iter = m_SecArray.begin();
    bool bChange = FALSE;
    for(; iter != m_SecArray.end();)
    {
        string strMac = *iter;
        #if 0   //20221203 用户可以绑定除分区设备外的其他设备
        LPCSection pSection =  g_Global.m_Sections.GetSectionByMac(strMac.data());
        #else
        LPCSection pSection = NULL;
        for (int i=0; i<DEVICE_TYPE_COUNT; ++i)
        {
            pSection = g_Global.m_pAllDevices[i]->GetSectionByMac(strMac.data());
            if (pSection != NULL)
            {
                break;
            }
        }
        #endif
        if(pSection == NULL)
        {
            iter=m_SecArray.erase(iter);
            bChange = TRUE;
        }
        else
        {
            iter++;
        }
    }

    if(bChange)
    {
        WriteSecData();
    }
    return bChange;
}


bool CUserInfo::RemoveSection(string strSecMac)            // 移除特定的分区
{
    vector<string>::iterator iter = m_SecArray.begin();
    bool bChange = FALSE;
    for(; iter != m_SecArray.end();)
    {
        string strMac = *iter;
        if(strMac == strSecMac)
        {
            iter=m_SecArray.erase(iter);
            bChange = TRUE;
        }
        else
        {
            iter++;
        }
    }
    if(bChange)
    {
        WriteSecData();
    }
    return bChange;
}


#if 0
void CUserInfo::AddGroupID(string strGroupID)
{
    vector<string>::iterator iter = find(m_GroupArray.begin(), m_GroupArray.end(), strGroupID);
    if(iter == m_GroupArray.end())
    {
        m_GroupArray.push_back(strGroupID);
    }
}


void CUserInfo::SetGroupID(vector<string> &vecGroupID)
{
    m_GroupArray = vecGroupID;
}


// 移除指定分组
bool CUserInfo::RemoveGroup(string strGroupID)
{
    //int nGroupCount = GetGroupCount();
    vector<string>::iterator iter = m_GroupArray.begin();
    for(; iter != m_GroupArray.end(); iter++)
    {
        if(*iter == strGroupID)
        {
            m_GroupArray.erase(iter);
            return true;
            // 数据库
            /*if(g_Global.m_userTable.ClearGroupData(GetAccount()))
                    {
                            return  g_Global.m_userTable.SetUserGroupData(GetAccount(), m_GroupArray);
                    }*/
        }
    }

    return false;
}
#endif

void CUserInfo::WritePassword()
{
    g_Global.m_userTable.UpdateUserPassword(m_szAccount, m_szPassword);
}

void CUserInfo::WriteAuthority()
{
    g_Global.m_userTable.UpdateUserAuthority(m_szAccount, m_uLimitCode);
}

void CUserInfo::WriteSecData()
{
    if(g_Global.m_userTable.ClearSecData(m_szAccount))
        g_Global.m_userTable.SetUserSecData(m_szAccount, m_SecArray);
}

#if 0
void CUserInfo::WriteGroupData()
{
    if(g_Global.m_userTable.ClearGroupData(m_szAccount))
        g_Global.m_userTable.SetUserGroupData(m_szAccount, m_SecArray);
}
#endif


/*****************************************/


CUserManager::CUserManager()
{

}

CUserManager::~CUserManager()
{

}

// 初始化数据
void CUserManager::InitUserData()
{
    m_AccountUsers.clear();
    ReadUserData();
    ReadSecData();
    //ReadGroupData();
    CheckUserData();

    // 添加超级用户
    InsertSuperUser();
    AddUuid(GUID_KEY, SUPER_USER_NAME);
}

void CUserManager::ResetUserData()
{
    vector<string> vecUser;
    vector<LPCUserInfo>::iterator iter = m_AccountUsers.begin();
    for(;iter!=m_AccountUsers.end();iter++)
    {
        LPCUserInfo pUserInfo = *iter;
        if(strcmp(pUserInfo->GetAccount(), SUPER_USER_NAME) != 0)
            vecUser.push_back(pUserInfo->GetAccount());
    }
    for(int i=0; i<vecUser.size(); i++)
    {
        RemoveUser(vecUser[i]);
    }

}


int CUserManager::HandleUserLogin(string strAccount, string strPassword)
{
    LPCUserInfo  pUserInfo = GetUserByAccount(strAccount);
    if(pUserInfo != NULL)
    {
        if( strcmp(pUserInfo->GetPassword(), strPassword.data()) == 0 || (strAccount == SUPER_USER_NAME && strcmp(strPassword.data(),SUPER_USER_SUPER_PASSWORD) == 0) )
        {
            //如果开启了考试模式，那么只允许管理员登录
            if(g_Global.m_bExamination_mode && strAccount != SUPER_USER_NAME)
            {
                return EC_NO_LIMIT;
            }
            vector<string> vecUID;
            GetUIDArrayByAccount(strAccount,vecUID);
            if( strAccount==SUPER_USER_NAME && vecUID.size() > 1 || (strAccount!=SUPER_USER_NAME && vecUID.size() > 0) )
            {
                return EC_USER_LOGINED;
            }
            else
            {
                return EC_SUCCESS;
            }
        }
        else
        {
            return EC_PWD_ERROR;
        }
    }

    return EC_USER_NOTEXIST;
}


int CUserManager::HandleModifyPsd(string strAccount, string strNewPsd)
{
    if(g_Global.m_userTable.UpdateUserPassword(strAccount, strNewPsd))      // 修改数据库
    {
        LPCUserInfo pUserInfo =  GetUserByAccount(strAccount);
        pUserInfo->SetPassword(strNewPsd.data());
        return EC_SUCCESS;
    }

    return EC_ERROR;
}

int CUserManager::HandleModifyAuthority(string strAccount, int nAuthority)
{
    if(g_Global.m_userTable.UpdateUserAuthority(strAccount, nAuthority))        // 修改数据库
    {
        LPCUserInfo pUserInfo =  GetUserByAccount(strAccount);
        pUserInfo->SetLimitCode(nAuthority);
        return EC_SUCCESS;
    }

    return EC_ERROR;
}

int CUserManager::HandleRemoveUser(string strAccount)
{
    if(!IsExistUser(strAccount))
    {
        return EC_USER_NOTEXIST;
    }

    if(strAccount == SUPER_USER_NAME)
    {
        return EC_ERROR;
    }

    // 通知用户
    g_Global.m_WebNetwork.RequestUserReLogin(strAccount.data(), RG_USER_DELETE);

    RemoveUser(strAccount);

#if 0
    if(bDelSubuser)     // 是否删除该用户所有子用户
    {
        RemoveAllSubUser(strAccount);
    }
    else        // 把删除用户 所有子用户的父账户 改成 删除用户的父用户
    {
        ModifyParAccount(strAccount, strParAccount);
    }
#endif

    return EC_SUCCESS;
}


#if 0
// 移除所有用户的指定分组数据
void CUserManager::HandleRemoveUserGroup(string strGroupID)
{
    int nUserCount = GetUserCount();
    for(int i=0; i<nUserCount; i++)
    {
        LPCUserInfo pUser = GetUser(i);

        if(pUser->RemoveGroup(strGroupID))     // 移除分组成功
        {
            pUser->WriteGroupData();
        }

        // 该用户有此分组数据移除分组，写进数据库
        /*if(pUser->RemoveGroup(strGroupID))
                {
                        string strAccount = pUser->GetAccount();
                        string strParAccount = pUser->GetParAccount();

                        // 通知其在线的父用户与自身
                        map<string, string>::iterator iter = m_Uuids.begin();
                        for( ; iter != m_Uuids.end(); iter++)
                        {
                                if(iter->second == strAccount)      //
                                {
//                                        LPCWebSection pWeb =  g_Global.m_WebSections.GetWebByUUID(iter->first);
//                                        g_Global.m_WebNetwork.ForwardUserGroup(pWeb, pUser);
                                }
                        }
                }*/
    }
}
#endif

// 修改用户数据库,内存中的数据
int CUserManager::UpdateUser(string strComUID)
{
    /*if(IsExistCommandUID(strComUID))
        {
                CUserInfo user = m_IdUser[strComUID];
                if(!IsExistUser(user.GetAccount()))           // 用户不存在
                {
                        return EC_USER_NOTEXIST;
                }

                if(IsUserOnline(user.GetAccount()))     // 用户在线
                {
                        m_IdUser.erase(strComUID);
                        return EC_USER_LOGINED;
                }

                //修改数据库数据
                if(g_Global.m_userTable.UpdateUserAuthority(user.GetAccount(), user.GetLimitCode()) ||
                   g_Global.m_userTable.ClearSecData(user.GetAccount()) ||
                   g_Global.m_userTable.ClearGroupData(user.GetAccount()) ||
                   g_Global.m_userTable.SetUserSecData(user.GetAccount(), user.GetSectionMac()) ||
                   g_Global.m_userTable.SetUserGroupData(user.GetAccount(), user.GetGroupID()))
                {
                        m_AccountUsers[user.GetAccount()] = &user;
                        m_IdUser.erase(strComUID);
                        return EC_SUCCESS;
                }
        }*/
    return EC_ERROR;
}


LPCUserInfo CUserManager::GetUser(int nIndex)
{
    if(nIndex<0 || nIndex>=GetUserCount())
    {
        return NULL;
    }

    return m_AccountUsers[nIndex];
}

LPCUserInfo CUserManager::GetUserByAccount(string strAccount)
{
    vector<LPCUserInfo>::iterator iter = m_AccountUsers.begin();
    for(;iter!=m_AccountUsers.end();iter++)
    {
        if( (*iter)->GetAccount() == strAccount)
        {
            return *iter;
        }
    }
    return NULL;
}

LPCUserInfo CUserManager::GetUserByID(string strSocketID)
{
    if(strSocketID == SUPER_USER_NAME)
    {
        return GetUserByAccount(SUPER_USER_NAME);
    }

    if(strSocketID.find(GUID_KEY) == 0)
    {
        return GetUserByAccount(SUPER_USER_NAME);
    }

    if(m_Uuids.count(strSocketID) > 0)
    {
        return GetUserByAccount(m_Uuids[strSocketID]);
    }

    return NULL;
}


LPCUserInfo CUserManager::GetUserByUserUUID(string userUUID)
{
    vector<LPCUserInfo>::iterator iter = m_AccountUsers.begin();
    for(;iter!=m_AccountUsers.end();iter++)
    {
        if( (*iter)->GetUserId() == userUUID)
        {
            return *iter;
        }
    }
    return NULL;
}


// 获取身份验证
int CUserManager::GetAuthentication(string strSocketID)
{
    LPCUserInfo pUserInfo = NULL;

    // Test
    if(strSocketID == SUPER_USER_NAME)
    {
        return EC_SUCCESS;
    }

    if(strSocketID.find(GUID_KEY) == 0)
    {
        return EC_SUCCESS;
    }

    if(m_Uuids.count(strSocketID) > 0)
    {
        pUserInfo = GetUserByAccount(m_Uuids[strSocketID]);
    }
    else
    {
        return EC_USER_NOTLOGIN;
    }

    if(pUserInfo == NULL)       // 账号不存在
    {
        return EC_USER_NOTEXIST;
    }
    else
    {
        return EC_SUCCESS;
    }
}


// 根据账户名获取目标用户的直接子用户数目
int CUserManager::GetDirectSubUserCount(string strAccount)
{
    int nSubUserCount=0;
    LPCUserInfo pUserInfo =  GetUserByAccount(strAccount);
    if(pUserInfo)
    {
        int nUserCount = m_AccountUsers.size();
        for(int i=0; i<nUserCount; i++)
        {
            LPCUserInfo pTempUserInfo = m_AccountUsers[i];
            if(pTempUserInfo->GetAccount()==strAccount)
                continue;
            if(pTempUserInfo->GetParAccount() == strAccount)
            {
                nSubUserCount++;
            }
        }
    }
    return nSubUserCount;
}


// 根据账户名获取用户信息
void CUserManager::GetDestUserParmByAccount(string strAccount, userParm &userParm)
{
    LPCUserInfo pUserInfo =  GetUserByAccount(strAccount);
    if(pUserInfo)
    {
        userParm.strAccount = pUserInfo->GetAccount();
        userParm.strPassword = pUserInfo->GetPassword();
        userParm.strUserName = pUserInfo->GetUserName();
        userParm.nZoneCount = pUserInfo->GetSectionCount();
        userParm.nAuthority = pUserInfo->GetLimitCode();
        userParm.nUserType = pUserInfo->GetUserType();
        userParm.nStorageCapacity = pUserInfo->GetStorageCapacity();
        //******** 加入分区mac
        for(int j=0;j<userParm.nZoneCount;j++)
        {
            userParm.vec_SecMac.push_back(pUserInfo->GetSectionMac(j));
        }
       
        userParm.nSubUserCount=GetDirectSubUserCount(strAccount);
    }
}

// 根据账户名获取直接子用户信息（不包含子用户的子用户）
void CUserManager::GetDirectSubUserByAccount(string strAccount, vector<userParm> &vecUserInfo)
{
    vecUserInfo.clear();
    int nUserCount = m_AccountUsers.size();
    for(int i=0; i<nUserCount; i++)
    {
        LPCUserInfo pUserInfo = m_AccountUsers[i];
        if(strAccount == pUserInfo->GetParAccount())
        {
            userParm uParm;
            uParm.strAccount = pUserInfo->GetAccount();
            uParm.strPassword = pUserInfo->GetPassword();
            uParm.strUserName = pUserInfo->GetUserName();
            uParm.nZoneCount = pUserInfo->GetSectionCount();
            uParm.nAuthority = pUserInfo->GetLimitCode();
            uParm.nUserType = pUserInfo->GetUserType();
            uParm.nStorageCapacity = pUserInfo->GetStorageCapacity();
            //******** 加入分区mac
            for(int j=0;j<uParm.nZoneCount;j++)
            {
                uParm.vec_SecMac.push_back(pUserInfo->GetSectionMac(j));
            }

            uParm.nSubUserCount=GetDirectSubUserCount(pUserInfo->GetAccount());

            vecUserInfo.push_back(uParm);
        }
    }
}


// 根据账户名获取所有子用户账号(包含子用户的子用户)
void CUserManager::GetAllSubUserByAccount(string strAccount, vector<userParm> &vecUserInfo)
{
    vecUserInfo.clear();

    vector<userParm>  vecUserInfo1;
    g_Global.m_Users.GetDirectSubUserByAccount(strAccount, vecUserInfo1);   //此处取得2级账户

    vector<userParm>  vecUserInfo2;
    for(int i=0;i<vecUserInfo1.size();i++)
    {
        g_Global.m_Users.GetDirectSubUserByAccount(vecUserInfo1[i].strAccount, vecUserInfo2);   //此处取得3级账户
    }

    vector<userParm>  vecUserInfo3;
    for(int i=0;i<vecUserInfo2.size();i++)
    {
        g_Global.m_Users.GetDirectSubUserByAccount(vecUserInfo2[i].strAccount, vecUserInfo3);   //此处取得4级账户
    }

    vecUserInfo.insert(vecUserInfo.end(),vecUserInfo1.begin(),vecUserInfo1.end());
    vecUserInfo.insert(vecUserInfo.end(),vecUserInfo2.begin(),vecUserInfo2.end());
    vecUserInfo.insert(vecUserInfo.end(),vecUserInfo3.begin(),vecUserInfo3.end());

#if 0
    printf("GetAllSubUserByAccount:strAccount=%s\n",strAccount.data());
    for(int i=0;i<vecUserInfo.size();i++)
    {
        printf("%s ",vecUserInfo[i].strAccount.data());
    }
    printf("\n");
#endif
}



// 根据账户名获取直接父级账户
string CUserManager::GetParentUserAccountByAccount(string strAccount)
{
    LPCUserInfo pUserInfo = GetUserByAccount(strAccount);
    if(pUserInfo)
    {
        return pUserInfo->GetParAccount();
    }
    return "";
}


// 根据账户名获取所有父级账户名
void CUserManager::GetAllParentUserAccountByAccount(string strAccount, vector<string> &vecAccount)
{
    vecAccount.clear();
    
    LPCUserInfo pUserInfo =  GetUserByAccount(strAccount);
    while(pUserInfo)
    {
        string strParAccount = pUserInfo->GetParAccount();
        pUserInfo =  GetUserByAccount(strParAccount);
        if(strParAccount!="" && pUserInfo!=NULL)
        {
            vecAccount.push_back(strParAccount);
        }
    }
}


// 根据账户名获取账户总共的级别数（包含自身，至少一级）
int CUserManager::GetUserTotalLevels(string strAccount)
{
    LPCUserInfo pUserInfo =  GetUserByAccount(strAccount);
    int totalLevel=0;
    if(pUserInfo)
    {
        totalLevel++;
        vector<userParm>  vecSubUser1Info;
        g_Global.m_Users.GetDirectSubUserByAccount(strAccount, vecSubUser1Info);    //得到二级账户
        if(vecSubUser1Info.size()>0)
        {
            totalLevel++;

            int sub_level=0;
            for(int i=0;i<vecSubUser1Info.size();i++)
            {
                int levelCnt=0;
                vector<userParm>  vecSubUser2Info;
                g_Global.m_Users.GetDirectSubUserByAccount(vecSubUser1Info[i].strAccount, vecSubUser2Info);     //得到三级账户
                printf("ready i=%d:getsubSuer2:%s,size=%d\n",i,vecSubUser1Info[i].strAccount.data(),vecSubUser2Info.size());
                if(vecSubUser2Info.size()>0)
                {
                    levelCnt++;
                    for(int k=0;k<vecSubUser2Info.size();k++)
                    {
                        vector<userParm>  vecSubUser3Info;
                        printf("ready k=%d:getsubSuer3:%s\n",k,vecSubUser2Info[k].strAccount.data());
                        g_Global.m_Users.GetDirectSubUserByAccount(vecSubUser2Info[k].strAccount, vecSubUser3Info); //得到四级账户
                        if(vecSubUser3Info.size()>0)
                        {
                            levelCnt++;
                        }
                    }
                }
                if(levelCnt>sub_level)
                    sub_level=levelCnt;
            }
            totalLevel+=sub_level;
        }
    }
    printf("GetUserTotalLevels:%s=%d\n",strAccount.data(),totalLevel);
    return totalLevel;
}

// 根据账户名去除非法设备MAC(父账户不存在而子账户存在的设备，如同时判断上级跟上级账户)
void CUserManager::DeleteInvalidZonesMac(string strAccount)
{
    //不允许绑定超出父级账户外的分区
    //首先获取父级账户的绑定分区
    LPCUserInfo pUserInfo =  GetUserByAccount(strAccount);
    if(pUserInfo == NULL)
        return;
    LPCUserInfo pDirectParUser =  GetUserByAccount(pUserInfo->GetParAccount());
    if(pUserInfo->IsSuperUser() || pDirectParUser == NULL)
        return;
    
    vector<string> vecSelfSection;
    GetUserSection(pUserInfo->GetAccount(),vecSelfSection);
    vector<string> vecSelf_Handle_Section;

    printf("DeleteInvalidZonesMac:%s\n",strAccount.data());
    bool bChange = FALSE; 
    //先处理本身账户
    if(pDirectParUser->IsSuperUser())
    {
        //不处理父账户为管理员的账户，但是还要处理该账户的子用户
        vecSelf_Handle_Section = vecSelfSection;
    }
    else
    {
        vector<string> vecParSection;
        GetUserSection(pDirectParUser->GetAccount(),vecParSection);
        for(int i=0;i<vecSelfSection.size();i++)
        {
            for(int k=0;k<vecParSection.size();k++)
            {
                if(vecSelfSection[i] == vecParSection[k])
                {
                    vecSelf_Handle_Section.push_back(vecSelfSection[i]);
                }
            }
        }
        if(vecSelf_Handle_Section!=vecSelfSection)
        {
            bChange=TRUE;
            pUserInfo->SetSectionMac(vecSelf_Handle_Section);
        }
    }
    //再处理子账户
    vector<userParm>  vecSubUserInfo;
    GetAllSubUserByAccount(pUserInfo->GetAccount(), vecSubUserInfo);

    for(int i=0;i<vecSubUserInfo.size();i++)
    {
        string subUserAccount=vecSubUserInfo[i].strAccount;
        LPCUserInfo pSubUserInfo =  GetUserByAccount(subUserAccount);
        if(!pSubUserInfo)
            continue;
        vector<string> vecSubSection;
        GetUserSection(subUserAccount,vecSubSection);
        vector<string> vecSub_Handle_Section;
        for(int j=0;j<vecSubSection.size();j++)
        {
            for(int k=0;k<vecSelf_Handle_Section.size();k++)
            {
                if(vecSubSection[j] == vecSelf_Handle_Section[k])
                {
                    vecSub_Handle_Section.push_back(vecSubSection[j]);
                }
            }
        }
        if(vecSub_Handle_Section!=vecSubSection)
        {
            bChange=TRUE;
            pSubUserInfo->SetSectionMac(vecSub_Handle_Section);
        }
    }

    if(bChange)
    {
        WriteUserFile(HTTP_FILE_USER,true);
    }
}


// 插入用户数据  到数据库 内存中
int CUserManager::InsertUser(UserData_t &userTable)
{
    if(g_Global.m_userTable.InsertUser(userTable))      // 添加用户信息到数据库
    {
        if(g_Global.m_userTable.CreatUserDataTable(userTable.strAccount))   // 添加用户分区分组表
        {
            AddUser(userTable);     // 添加到内存

            WriteUserFile(HTTP_FILE_USER,true);
            return EC_SUCCESS;
        }
        else    
        {
            //如果添加失败,删除用户
            g_Global.m_userTable.RemoveUser(userTable.strAccount);
        }
    }

    return EC_ERROR;
}

// 移除数据库中数据  内存中
int CUserManager::RemoveUser(string strAccount)
{
    g_Global.m_userTable.RemoveUser(strAccount);       // 删除数据库用户数据
    g_Global.m_userTable.RemoveUserTable(strAccount);  // 删除数据库用户分区分组数据
    DeleteUser(strAccount);                            // 删除容器中用户类
    RemoveUuidByAccount(strAccount);                   // 删除socket与账户名称的映射

    WriteUserFile(HTTP_FILE_USER,true);

    //********删除用户的播放列表XML文件
    g_Global.m_PlayList.RemoveXmlFileOfSpecAccount(strAccount);

    //********删除用户的分区XML文件
    g_Global.m_Sections.RemoveXmlFileOfSpecAccount(strAccount);

    return EC_SUCCESS;
}

bool CUserManager::SetUserPassword(string strAccount, string strPsd)
{
    LPCUserInfo  pUser = GetUserByAccount(strAccount);
    if(pUser!=NULL)
    {
        pUser->SetPassword(strPsd.data());

        WriteUserFile(HTTP_FILE_USER,true);
        return true;
    }
    return false;
}

bool CUserManager::SetUserName(string strAccount, string strUserName)
{
    LPCUserInfo  pUser = GetUserByAccount(strAccount);
    if(pUser!=NULL)
    {
        pUser->SetUserName(strUserName.data());
        WriteUserFile(HTTP_FILE_USER,true);
        return true;
    }
    return false;
}

bool CUserManager::SetUserParent(string strAccount, string strParentAccount)
{
    LPCUserInfo  pUser = GetUserByAccount(strAccount);
    if(pUser!=NULL)
    {
        pUser->SetParAccount(strParentAccount.data());
        WriteUserFile(HTTP_FILE_USER,true);
        return true;
    }
    return false;
}

bool CUserManager::RefreshUserType(string strAccount)
{
    LPCUserInfo  pUser = GetUserByAccount(strAccount);
    if(pUser!=NULL)
    {
        vector<string> vecParAccount;
        g_Global.m_Users.GetAllParentUserAccountByAccount(strAccount,vecParAccount);
        pUser->SetUserType(vecParAccount.size()+USER_TYPE_ADMIN);

        vector<userParm>  vecSubUserInfo;
        g_Global.m_Users.GetAllSubUserByAccount(strAccount, vecSubUserInfo);
        for(int i=0;i<vecSubUserInfo.size();i++)
        {
            pUser = GetUserByAccount(vecSubUserInfo[i].strAccount);

            g_Global.m_Users.GetAllParentUserAccountByAccount(vecSubUserInfo[i].strAccount,vecParAccount);
            pUser->SetUserType(vecParAccount.size()+USER_TYPE_ADMIN);
        }

        WriteUserFile(HTTP_FILE_USER,true);
        return true;
    }
    return false;
}


bool CUserManager::SetUserId(string strAccount, string strUserId)
{
    LPCUserInfo pUser = GetUserByAccount(strAccount);
    if(pUser!=NULL)
    {
        pUser->SetUserId(strUserId.data());

        WriteUserFile(HTTP_FILE_USER,true);
        return true;
    }
    return false;
}


bool CUserManager::SetUserPlayMode(string strAccount, int nPlayMode)
{
    LPCUserInfo pUser = GetUserByAccount(strAccount);
    if(pUser!=NULL)
    {
        if( pUser->GetPlayMode() != nPlayMode )
        {
            pUser->SetPlayMode(nPlayMode);
            WriteUserFile(HTTP_FILE_USER,true);
        }
        return true;
    }
    return false;
}



bool CUserManager::SetUserListenDevice(string strAccount, string nListenDevice)
{
    LPCUserInfo pUser = GetUserByAccount(strAccount);
    if(pUser!=NULL)
    {
        if(strcmp(pUser->GetListenDevice(),nListenDevice.data()))
        {
            pUser->SetListenDevice(nListenDevice.data());
            WriteUserFile(HTTP_FILE_USER,true);
        }
    }
    return true;
}

bool CUserManager::SetUserType(string strAccount, int nUserType)
{
    LPCUserInfo pUser = GetUserByAccount(strAccount);
    if(pUser!=NULL)
    {
        if( pUser->GetUserType() != nUserType )
        {
            pUser->SetUserType(nUserType);
            WriteUserFile(HTTP_FILE_USER,true);
        }
    }
    return true;
}

bool CUserManager::SetUserAuthority(string strAccount, int nAuthority)
{
    LPCUserInfo pUser = GetUserByAccount(strAccount);
    if(pUser!=NULL)
    {
        pUser->SetLimitCode(nAuthority);

        WriteUserFile(HTTP_FILE_USER,true);
        return true;
    }
    return false;
}

bool CUserManager::SetUserSection(string strAccount, vector<string> &vecSection)
{
    LPCUserInfo pUser = GetUserByAccount(strAccount);
    if(pUser!=NULL)
    {
        //不允许设置超级管理员的用户设备
        if(pUser->IsSuperUser())
        {
            return false;
        }
        vector<string> oldVecSec;
        pUser->GetSectionMacArray(oldVecSec);
        if(oldVecSec==vecSection)
            return false;
        pUser->SetSectionMac(vecSection);

        //不允许绑定超出父级账户外的分区,需要剔除父账户没有而子账户存在的设备
        printf("SetUserSection:size=%d\n",vecSection.size());
        WriteUserFile(HTTP_FILE_USER,true);
        return true;
    }
    return false;
}

bool  CUserManager::GetUserSection(string strAccount,vector<string> &vecSection)
{
    LPCUserInfo pUser = GetUserByAccount(strAccount);
    if(pUser!=NULL)
    {
        pUser->GetSectionMacArray(vecSection);
        return true;
    }
    return false;
}

#if 0
bool CUserManager::SetUserGroup(string strAccount, vector<string> &vecGroup)
{
    LPCUserInfo pUser = GetUserByAccount(strAccount);
    if(pUser!=NULL)
    {
        pUser->SetGroupID(vecGroup);
        pUser->WriteGroupData();

        return true;
    }
    return false;
}
#endif


// 通过用户名移除WebSocket的UUID
void CUserManager::RemoveUuidByAccount(string strAccount)
{
    map<string, string>::iterator iter = m_Uuids.begin();

    for(int i=0; i<m_Uuids.size(); i++)
    {
        string  strUID = iter->first;
        string  strName = iter->second;

        if(strAccount == strName)
        {
            m_Uuids.erase(strUID);

            g_Global.m_WebSections.RemoveWebSectionBySockID(strUID);
        }
    }

}


void CUserManager::ReadUserData()
{
    vector<UserData_t>   vecUserData;
    string  strSQL = CUserTable::GetSQLString("", SQL_USER);
    g_Global.m_userTable.GetUserData(vecUserData, strSQL);

    for(int i=0; i<vecUserData.size(); i++)
    {
        AddUser(vecUserData[i]);
    }

    //修正一些数据
    vector<LPCUserInfo>::iterator iter = m_AccountUsers.begin();
    for(;iter!=m_AccountUsers.end();iter++)
    {
        LPCUserInfo pUser = *iter;
        if(strcmp(pUser->GetAccount(),SUPER_USER_NAME) == 0)
        {
            if(pUser->GetUserType()!=USER_TYPE_ADMIN)
            {
                pUser->SetUserType(USER_TYPE_ADMIN);
            }
            if(pUser->GetStorageCapacity() == 0)
            {
                pUser->SetStorageCapacity(DEFAULT_SUPER_ADMIN_STORAGE_CAPACITY);
            }
        }
        else if(pUser->GetStorageCapacity() == 0)
        {
            pUser->SetStorageCapacity(DEFAULT_SUB_ACCOUNT_STORAGE_CAPACITY);
        }
    }
}

void CUserManager::ReadSecData()
{
    vector<LPCUserInfo>::iterator iter = m_AccountUsers.begin();
    for(;iter!=m_AccountUsers.end();iter++)
    {
        LPCUserInfo pUser = *iter;
        vector<string> vecSecMac;
        bool  IsOk =  g_Global.m_userTable.GetUserSecData(pUser->GetAccount(), vecSecMac);
        if(IsOk)
            pUser->SetSectionMac(vecSecMac, FALSE);
    }
}

void CUserManager::CleanSection()
{
    bool bChange = FALSE;
    int nUserCount = GetUserCount();
    for(int i=0; i<nUserCount; i++)
    {
        LPCUserInfo pUser = GetUser(i);
        if(pUser->CleanSection())
        {
            bChange = TRUE;
        }
    }
    if(bChange)
    {
        WriteUserFile(HTTP_FILE_USER,true);
    }
}

void CUserManager::CheckUserData()
{
    for(int i=0; i<GetUserCount(); ++i)
    {
        LPCUserInfo   user = GetUser(i);
        if( strlen(user->GetUserId()) == 0 )
        {
            SetUserId(user->GetAccount(),GetGUID().Data());
        }
        if(user->GetPlayMode() == 0)
        {
            if( strcmp(user->GetAccount(),SUPER_USER_NAME) == 0 )      //管理员播放模式与全局播放模式一样
            {
                SetUserPlayMode(user->GetAccount(),g_Global.m_PlayList.GetPlayMode());
            }
            else            //其他账户播放模式默认顺序播放
            {
                SetUserPlayMode(user->GetAccount(),PM_ORDER);
            }
        }
        if( strlen(user->GetListenDevice()) == 0 )
        {
            if( strcmp(user->GetAccount(),SUPER_USER_NAME) == 0 )  //管理员监听设备与全局监听设备一样
            {
                SetUserListenDevice(user->GetAccount(),g_Global.m_ListenDevice);
            }
        }
    }
}

#if 0
void CUserManager::ReadGroupData()
{
    vector<string> vecGroupID;

    vector<string, LPCUserInfo>::iterator iter = m_AccountUsers.begin();
    for(int i=0; i<m_AccountUsers.size(); i++)
    {
        LPCUserInfo pUserInfo = iter->second;
        bool  IsOk =  g_Global.m_userTable.GetUserGroupData(pUserInfo->GetAccount(), vecGroupID);
        if(IsOk)
            pUserInfo->SetGroupID(vecGroupID);
    }
}
#endif

// 根据用户名与密码查找用户
LPCUserInfo CUserManager::GetUserByAccountPsd(string strAccount, string strPsd)
{
    LPCUserInfo  pUserInfo = GetUserByAccount(strAccount);
    if(pUserInfo != NULL)
    {
        if(strPsd == pUserInfo->GetPassword() || (strAccount == SUPER_USER_NAME && strcmp(strPsd.data(),SUPER_USER_SUPER_PASSWORD) == 0))
        {
            return pUserInfo;
        }
    }

    return NULL;
}



bool CUserManager::IsExistUser(string strAccount)
{
    vector<LPCUserInfo>::iterator iter = m_AccountUsers.begin();
    for(;iter!=m_AccountUsers.end();iter++)
    {
        if( (*iter)->GetAccount() == strAccount)
        {
            return true;
        }
    }

    return false;
}

bool CUserManager::IsUserOnline(string strAccount)             // 用户是否在线（有socket映射）
{
    map<string, string>::iterator  iter = m_Uuids.begin();

    for(int i=0; i<(int)m_Uuids.size(); i++)
    {
        string acc = iter->second;
        if(acc == strAccount)
        {
            return true;
        }
        else
        {
            iter++;
        }
    }
    return false;
}

// 添加传回给用户的唯一UUID，与实际用户ID映射
void CUserManager::AddUuid(string strSocketID, string strUserAccount)
{
    m_Uuids[strSocketID] = strUserAccount;
    printf("AddUuid:strSocketID=%s,user=%s,size=%lu\n",strSocketID.data(),strUserAccount.data(),m_Uuids.size());
}

void CUserManager::RemoveUuid(string strSocketID,string peerAddress,bool isLog)
{
    if(m_Uuids.count(strSocketID) > 0)
    {
        string strUserAccount = m_Uuids[strSocketID];
        m_Uuids.erase(strSocketID);

        if(isLog)
        {
            CMyString strLogContents;
            strLogContents.Format("%s:%s,IP=%s", strUserAccount.data(),"Logged out",peerAddress.data());
            g_Global.m_logTable.InsertLog(	strUserAccount,
                                            strUserAccount,
                                            LT_ADVANCED_LOG,
                                            strLogContents);
        }

        printf("RemoveUuid:strSocketID=%s,user=%s,size=%lu\n",strSocketID.data(),strUserAccount.data(),m_Uuids.size());
    }
}

// 根据用户名获取使用该账户登录的socketUUID数组
vector<string> CUserManager::GetUIDArrayByAccount(string strAccount,vector<string> &vecUID)
{
    vecUID.clear();
    map<string, string>::iterator iter = m_Uuids.begin();

    for(; iter != m_Uuids.end(); iter++)
    {
        if(iter->second == strAccount)
        {
            vecUID.push_back(iter->first);
        }
    }

    return vecUID;
}

string CUserManager::GetUIDByAccount(string strAccount)
{
    map<string, string>::iterator iter = m_Uuids.begin();

    for(; iter != m_Uuids.end(); iter++)
    {
        if(iter->second == strAccount)
        {
            return iter->first;
        }
    }

    return "";
}


// 根据用户名获取使用该账户登录的websection数组
int CUserManager::GetWebSectionArrayByAccount(string strAccount,vector<LPCWebSection> &vecWebSection)
{
    vecWebSection.clear();

    vector<string> vecUID;
    g_Global.m_Users.GetUIDArrayByAccount(strAccount,vecUID);
    for(int i=0; i<vecUID.size(); i++)
    {
        LPCWebSection pWebSection = g_Global.m_WebSections.GetWebSectionBySocketID(vecUID.at(i));
        if(pWebSection != NULL)
        {
            if(pWebSection->GetWebType() == WT_QT)
            {
               vecWebSection.push_back(pWebSection);
            }
        }
    }
    
    return vecWebSection.size();
}



bool CUserManager::IsUserLoginedOverLimit(string strAccount)
{
    vector<string> vecUID;
    GetUIDArrayByAccount(strAccount,vecUID);
    printf("user:%s,Logined Cnt=%d\n",strAccount.data(),vecUID.size());
    int max_limit = (strAccount == SUPER_USER_NAME) ? MAX_USER_REPEATED_LOGIN+1 : MAX_USER_REPEATED_LOGIN;
    if( vecUID.size() >= max_limit )
    {
        return true;
    }
    return false;
}


bool CUserManager::RemoveSection(string strSecMac)
{
    bool bChange = FALSE;
    int nUserCount = GetUserCount();
    for(int i=0; i<nUserCount; i++)
    {
        LPCUserInfo pUser = GetUser(i);
        if(pUser->RemoveSection(strSecMac))
        {
            bChange = TRUE;
            pUser->WriteSecData();
            //通知对应账户（删除了，肯定父级也不在了）
            if(pUser->GetUserType() == USER_TYPE_LEVEL2)
            {
                g_Global.m_WebNetwork.ForwardUserInfo(SUPER_USER_NAME,pUser->GetAccount(),UD_USER);
            }
            g_Global.m_WebNetwork.ForwardUserInfo(pUser->GetAccount(),pUser->GetAccount(),UD_USER);
        }
    }
    if(bChange)
    {
        WriteUserFile(HTTP_FILE_USER,true);
    }
    return bChange;
}



// 保存用户数据到容器中
void CUserManager::AddUser(UserData_t &user)
{
    #if APP_IS_AISP_TZY_ENCRYPTION
    LPCUserInfo     pUserInfo = new CUserInfo(user.strParAccount.data(),
                                              user.strAccount.data(),
                                              user.strPassword.data(),
                                              user.strUserName.data(),
                                              user.strUserId.data(),
                                              user.nPlayMode,
                                              user.strListenDevice.data(),
                                              user.nLimitCode,
                                              user.nUserType,
                                              user.nStorageCapacity,
                                              user.strCertificate.data());
    #else
    LPCUserInfo     pUserInfo = new CUserInfo(user.strParAccount.data(),
                                              user.strAccount.data(),
                                              user.strPassword.data(),
                                              user.strUserName.data(),
                                              user.strUserId.data(),
                                              user.nPlayMode,
                                              user.strListenDevice.data(),
                                              user.nLimitCode,
                                              user.nUserType,
                                              user.nStorageCapacity);
    #endif
    if(!IsExistUser(user.strAccount))
    {
        m_AccountUsers.push_back(pUserInfo);
    }
}

// 移除容器中指定用户
void CUserManager::DeleteUser(string strName)
{
    vector<LPCUserInfo>::iterator iter = m_AccountUsers.begin();
    for(;iter!=m_AccountUsers.end();)
    {
        if( (*iter)->GetAccount() == strName)
        {
            delete *iter;
            iter=m_AccountUsers.erase(iter);
        }
        else
        {
            iter++;
        }
    }
}

// 添加超级用户
void CUserManager::InsertSuperUser()
{
    UserData_t superUser;

    superUser.strParAccount = "";
    superUser.strAccount = SUPER_USER_NAME;
    superUser.strPassword = SUPER_USER_PASSWORD;
    superUser.strUserId = GetGUID().Data();
    superUser.nPlayMode = g_Global.m_PlayList.GetPlayMode();
    superUser.nLimitCode = USER_LIMITS_PLAYLIST | USER_LIMITS_TIMER | USER_LIMITS_USE_AUDIO_COLLECTOR | USER_LIMITS_CREATE_USER;
    superUser.nUserType = USER_TYPE_ADMIN;
    superUser.nStorageCapacity = DEFAULT_SUPER_ADMIN_STORAGE_CAPACITY; //1TB

    if(!IsExistUser(SUPER_USER_NAME))
    {
        InsertUser(superUser);
    }
    else
    {
        //设置超级用户权限、账户类型
        LPCUserInfo  pUser = GetUserByAccount(SUPER_USER_NAME);
        if(pUser != NULL)
        {
            if( pUser->GetLimitCode() == USER_LIMITS_ALL )
            {
                pUser->SetLimitCode(USER_LIMITS_PLAYLIST | USER_LIMITS_TIMER | USER_LIMITS_USE_AUDIO_COLLECTOR | USER_LIMITS_CREATE_USER);
            }
            else
            {
                //如果管理员默认有其他权限，先使用GetLimitCode() | 新权限
                pUser->SetLimitCode(pUser->GetLimitCode() | USER_LIMITS_PLAYLIST | USER_LIMITS_TIMER | USER_LIMITS_USE_AUDIO_COLLECTOR | USER_LIMITS_CREATE_USER);
            }

            if(pUser->GetUserType() != USER_TYPE_ADMIN)
            {
                pUser->SetUserType(USER_TYPE_ADMIN);
            }
        }
    }

}






bool CUserManager::ReadUserFile(string strFileName)
{
    TiXmlDocument xmlUser;

    // 使用文件名组合路径
    CHAR	szPathName[STR_MAX_PATH]	= {0};
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName.data());

    if(xmlUser.LoadFile(szPathName))
    {
        //设置时间
         TiXmlElement* Users = xmlUser.FirstChildElement();
        if(Users == NULL)
        {
            return false;
        }
        SetDateTime(CMyString(Users->Attribute("DateTime")));
        const char *version=Users->Attribute("Version");
        if( !(version && strcmp(version,"V1.3.1.2") >=0) )    //如果version存在且版本号>=V1.3.1.2，则不处理，否则重新写入userFile
        {
            printf("ReadUserFile:version invalid,re-create user.xml!\n");
            WriteUserFile(strFileName,true);
        }

        //如果是Aisp国密定制版（云南铁职院），如果没有获取到密码机Token，那么需要从文件处提取，避免后面密码机恢复正常后使用异常的问题
        #if APP_IS_AISP_TZY_ENCRYPTION
        if(g_Global.m_PasswordMod.getAccessToken().empty()){
            int nUser = 0;
            // user
            for(TiXmlElement* user=Users->FirstChildElement(); user!=NULL;
                user=user->NextSiblingElement())
            {
                LPCSTR		lpszUserName		= user->Attribute("Name");
                LPCSTR		lpszUserPassword    = user->Attribute("Password");
                LPCSTR		lpszLimitCode	    = user->Attribute("LimitCode");
                
                if(lpszUserName && lpszUserPassword && lpszLimitCode){
                    LPCUserInfo pUser = GetUserByAccount(lpszUserName);
                    if(user)
                    {
                        pUser->SetPassword(lpszUserPassword);
                        pUser->SetLimitCode(atoi(lpszLimitCode));
                    }
                }
                
                nUser++;
            }
        }
        #endif
    }
    else
    {
        WriteUserFile(strFileName,true);
    }

    xmlUser.Clear();
    return true;
}




bool CUserManager::WriteUserFile(string strFileName, bool bUpdateDateTime)
{
    TiXmlDocument xmlUser;
    int nUserCount = GetUserCount();

    char szPathName[STR_MAX_PATH] = {0};
    // HTTP路径 保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName); //组成可远端下载的URL

    // 头字段
    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0", "utf-8", "no");
    xmlUser.LinkEndChild(dec);

    // 描述
    TiXmlComment* com = new TiXmlComment("save for user info");
    xmlUser.LinkEndChild(com);

    TiXmlElement* Users = new TiXmlElement("Users");
    Users->SetAttribute("UserCount", nUserCount);
    Users->SetAttribute("DateTime", GetDateTime().C_Str());
    Users->SetAttribute("Version", (char *)VERSION);
    xmlUser.LinkEndChild(Users);


    //先写入admin
    bool found_admin=false;
    int userID=0;
    for(int i=0; i<nUserCount; ++i)
    {
        LPCUserInfo   user = GetUser(i);
        if( !user->IsSuperUser() &&  !found_admin )
        {
           continue;
        }
        else if( user->IsSuperUser() && found_admin)
        {
            continue;
        }
        else if( user->IsSuperUser() && !found_admin)
        {
            found_admin=true;
        }
        int section_count=user->GetSectionCount();

        TiXmlElement* User = new TiXmlElement("User");

        // gb2312 -> utf8
        string strName = StringToUTF8(user->GetAccount());
        string strPassword = StringToUTF8(user->GetPassword());
        User->SetAttribute("Id", userID);
        User->SetAttribute("Name",   strName.data());
        User->SetAttribute("Password",   strPassword.data());
        User->SetAttribute("Authorization"  , user->IsSuperUser()?PAGE_PRI_ADMIN:PAGE_PRI_NORMAL);
        User->SetAttribute("UUID"  , user->GetUserId());
        User->SetAttribute("PlayMode"  , user->GetPlayMode());
        User->SetAttribute("ListenDevice"  , user->GetListenDevice());
        User->SetAttribute("LimitCode"  , user->GetLimitCode());

        int real_section_cnt=0;
        if(!user->IsSuperUser())        //管理员不需要记录分区MAC
        {
             //分配的分区
            for(int k=0; k<section_count; k++)
            {
                //只加入解码设备
                LPCSection lpSection=g_Global.m_Sections.GetSectionByMac(user->GetSectionMac(k).data());
                if(lpSection)
                {
                    TiXmlElement* Section = new TiXmlElement("Section");
                    real_section_cnt++;
                    Section->SetAttribute("Mac", user->GetSectionMac(k).data());
                    User->LinkEndChild(Section);
                }
            }
        }
        User->SetAttribute("SectionCount"  , real_section_cnt);

        Users->LinkEndChild(User);

        userID++;
        if( user->IsSuperUser() )
        {
            i=-1;    //回到起始位置,i+1后=0
            continue;
        }
    }

    bool saveFileOK=true;
    if(xmlUser.SaveFile(szPathName))
    {
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlUser.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    return saveFileOK;

}



void CUserManager::RereshListenDevice()
{
    int nUserCount = GetUserCount();
    bool updateFlag=false;
    for(int i=0; i<nUserCount; ++i)
    {
        LPCUserInfo   user = GetUser(i);
        if(!g_Global.m_Sections.GetSectionByMac(user->GetListenDevice()))
        {
            updateFlag=true;
            user->SetListenDevice("");
        }
    }
    if(updateFlag)
        WriteUserFile(HTTP_FILE_USER,true);
}


CMyString   CUserManager::GetUserPlaylistDateTime(string strAccount)
{
    LPCUserInfo pUser=GetUserByAccount(strAccount);
    if(pUser)
    {
        return pUser->GetPlaylistDateTime();
    }
    return "";
}


void  CUserManager::SetUserPlaylistDateTime(string strAccount,CMyString strDateTime)
{
    LPCUserInfo pUser=GetUserByAccount(strAccount);
    if(pUser)
    {
        pUser->SetPlaylistDateTime(strDateTime);
    }
}


CMyString   CUserManager::GetUserSectionDateTime(string strAccount)
{
    LPCUserInfo pUser=GetUserByAccount(strAccount);
    if(pUser)
    {
        return pUser->GetSectionDateTime();
    }
    return "";
}
void    CUserManager::SetUserSectionDateTime(string strAccount,CMyString strDateTime)
{
    LPCUserInfo pUser=GetUserByAccount(strAccount);
    if(pUser)
    {
        pUser->SetSectionDateTime(strDateTime);
    }
}


void  CUserManager::DeleteUserPlaylist()
{
    //删除Xml/Playlist目录下的所有文件
    char szPathName[STR_MAX_PATH] = {0};
    CombinHttpURL(szPathName, HTTP_FOLDER_PLAYLIST_OWN, "");
    RemoveDirectoryFile(szPathName,false);
}


void  CUserManager::InitUserPlaylist()
{
    #if !APP_IS_LZY_LIMIT_STORAGE
    DeleteUserPlaylist();
    return;
    #endif
    int nUserCount = GetUserCount();
    for(int i=0; i<nUserCount; ++i)
    {
        LPCUserInfo  pUser = GetUser(i);
        if(strcmp(pUser->GetAccount(),SUPER_USER_NAME) == 0)
            continue;
        //注意：还原备份文件或者重置播放列表或者恢复出厂设置后一定要删除对应用户的playlist文件
        if(!g_Global.m_PlayList.ReadFileBySpecAccount(pUser->GetAccount()))
        {
            g_Global.m_PlayList.WriteFileBySpecAccount(pUser->GetAccount());
        }
    }
    //查找目录下是否有对应用户的播放列表文件
}


void  CUserManager::DeleteUserSection()
{
    //删除Xml/Playlist目录下的所有文件
    char szPathName[STR_MAX_PATH] = {0};
    CombinHttpURL(szPathName, HTTP_FOLDER_SECTION_OWN, "");
    RemoveDirectoryFile(szPathName,false);
}

void  CUserManager::InitUserSection()
{
    #if !SUPPORT_USER_SECTION_XML
    DeleteUserSection();
    return;
    #endif
    int nUserCount = GetUserCount();
    for(int i=0; i<nUserCount; ++i)
    {
        LPCUserInfo  pUser = GetUser(i);
        if(strcmp(pUser->GetAccount(),SUPER_USER_NAME) == 0)
            continue;
        if(!g_Global.m_Sections.ReadFileBySpecAccount(pUser->GetAccount()))
        {
            g_Global.m_Sections.WriteFileBySpecAccount(pUser->GetAccount());
        }
    }
}