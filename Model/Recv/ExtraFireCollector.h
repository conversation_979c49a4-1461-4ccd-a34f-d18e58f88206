#ifndef EXTRAFIRECOLLECTOR_H
#define EXTRAFIRECOLLECTOR_H

#include <iostream>
#include <vector>
#include <map>

using namespace std;


class CExtraChannel
{
public:
    CExtraChannel();
    bool    IsDataFull();
    bool    AddSection(vector<string>& vecSection, int nPage);

    int     m_nID;
    int     m_nTriggerMode;         // 触发模式
    string  m_strName;              // 通道名称，自定义
    string  m_strSoundPathName;     // 报警声音完整路径名称
    int     m_nSectionCount;        //
    vector<string>  m_SectionMac;   // 通道对应的分区Mac

private:
    bool        AddSecPage(int nPage);          // 添加收到分区页数

private:
    vector<int>      m_SectionPage;   // 收到的分区页数容器
};

class CExtraFireCollector
{
public:
    CExtraFireCollector();
    bool    IsDataFull();
    bool    SetChannel(CExtraChannel& channel, int nPage);

private:
    bool    IsExistChannel(int nChannelID);

public:
    string  m_strMac;               // 设备mac
    string  m_strName;              // 设备名称
    int m_nChannelCount;            // 待设置消防通道总数量

    map<int, CExtraChannel>  m_Channels;        // 通道ID与类映射
};

#endif // EXTRAFIRECOLLECTOR_H
