#ifndef EXTRAGROUP_H
#define EXTRAGROUP_H

#include <iostream>
#include <vector>
using namespace std;

class CExtraGroup
{
public:
    CExtraGroup();
    bool        IsDataFull();

    bool        AddSection(vector<string>& vecSection, int nPage);
    vector<string>& GetSection()        { return m_vecSection; }

    string      m_strID;          // 分组ID
    string      m_strName;        // 分组名称
    int         m_nZoneCount;     // 分区数目

private:
    bool        AddSecPage(int nPage);          // 添加收到分区页数

private:
    vector<string>   m_vecSection;      // 分区容器
    vector<int>      m_SectionPage;     // 收到的分区页数容器
};

#endif // EXTRAGROUP_H
