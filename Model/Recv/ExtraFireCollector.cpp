#include "stdafx.h"
#include "ExtraFireCollector.h"
#include <algorithm>        //包含sort和find算法


CExtraChannel::CExtraChannel()
{
    m_nID = 0;
    m_nSectionCount = 0;
    m_nTriggerMode = 0;
    m_strName = "";
    m_strSoundPathName = "";

    m_SectionMac.clear();
    m_SectionPage.clear();
}

bool CExtraChannel::IsDataFull()
{
    //return (m_nSectionCount == m_SectionMac.size());
    return true;
}

bool CExtraChannel::AddSection(vector<string> &vecSection, int nPage)
{
    if(AddSecPage(nPage))
    {
        for(int i=0; i<(int)vecSection.size(); i++)
        {
            string str = vecSection[i];
            m_SectionMac.push_back(str);
        }

        return true;
    }

    return false;
}

bool CExtraChannel::AddSecPage(int nPage)
{
    vector<int>::iterator iter = find(m_SectionPage.begin(), m_SectionPage.end(), nPage);
    if(iter == m_SectionPage.end())     // 未查找到相同页数
    {
        m_SectionPage.push_back(nPage);
        return true;
    }

    return false;
}


////////////////////////////////////////////////////////////////////////////////

CExtraFireCollector::CExtraFireCollector()
{

}

bool CExtraFireCollector::IsDataFull()
{
        LOG(FORMAT("%d   %d\n", m_nChannelCount, m_Channels.size()), LV_INFO);
        if(m_nChannelCount == m_Channels.size())
        {
            for(int i=1; i<=m_Channels.size(); i++)
            {
                if(!m_Channels[i].IsDataFull())
                {
                    return false;
                }
            }

            return true;
        }

        return false;
}

bool CExtraFireCollector::SetChannel(CExtraChannel &channel, int nPage)
{
    //if(IsExistChannel(channel.m_nID))       // 避免通道不存在
    //{
        m_Channels[channel.m_nID] = channel;
        return true;
    //}
    //return false;
}

bool CExtraFireCollector::IsExistChannel(int nChannelID)
{
        if(m_Channels.count(nChannelID) > 0)
        {
            return true;
        }
        return false;
}


