#ifndef EXTIMER_H
#define EXTIMER_H

#include <iostream>
#include <vector>
#include "Model/Other/TimerScheme.h"
using namespace std;


class CExTimePoint
{
public:
    CExTimePoint();
    bool      IsDataFull();
    int       GetSchID()                      { return m_nSchID;       }
    void      SetSchID(int nSchID)            { m_nSchID = nSchID - 1; }

    bool        AddSection(vector<string>& vecSection, int nPage);
    bool        AddGroup(vector<string>& vecGroup, int nPage);
    bool        AddSong(vector<string>& vecSong, int nPage);
    bool        AddSequencePower(vector<string>& vecSequencePower,vector<unsigned short> channels,int nPage);
    bool        AddAudioCollector(ST_TIMER_AUDIO_COLLECTOR_INFO stAudioCollector);

    int       m_nID;                    // 定时点ID:0,1,2,3...
    string    m_strName;                // 定时点名称
    bool      m_bValid;                 // 是否有效，默认为true
    bool      m_bPlayToEnd;             // 是否播放至结束，默认为false
    bool      m_bResumePlay;            // 定时结束后是否恢复歌曲，目前只是分布模式有效，默认为false
    bool      m_bInterCut;              // 定时点是否可以强行插播其它定时点，默认为false
    int       m_nVolume;                // 定时点音量
    bool      m_bVolFollowDev;          // 定时点音量是是否跟随设备音量，默认为false
    int       m_nPlayMode;              // 0：顺序播放   1：循环播放   2：随机播放
    bool      m_bRandomSingle;          // 定时点是否随机播放单曲，默认为false
    int       m_nTimeMode;              // 定时方式，0：按周循环  1：按日期循环
    string    m_strWeekday;             // 从低位到高位，第1位为星期一，第2位为星期二...
    string    m_strStartDate;           // 开始日期，例如 2019-1-24
    string    m_strEndDate;             // 结束日期，例如 2019-1-25
    string    m_strStartTime;           // 开始时间，例如 20:10:30
    string    m_strEndTime;             // 结束时间，例如 20:20:30
    int       m_nSectionCount;          // 待添加的分区数
    int       m_nGroupCount;            // 待添加的分组数
    int       m_nSongCount;             // 待添加的歌曲数
    int       m_nSequencePowerCount;    // 待添加的时序器数
    int       m_nSourceType;            // 节目源类型，0：歌曲播放  1：音频采播
    int       m_nDeviceType;            // 设备类型，0：解码终端 1：电源时序器

    vector<string>      m_vecSection;
    vector<string>      m_vecGroup;
    vector<string>      m_vecSong;
    vector<string>      m_vecSequencePower;
    vector<unsigned short>      m_vecSequencePowerChannels;

    ST_TIMER_AUDIO_COLLECTOR_INFO m_stAudioCollector;

private:
    bool        AddSecPage(int nPage);      // 添加收到分区页数
    bool        AddGroupPage(int nPage);    // 添加收到分组页数
    bool        AddSongPage(int nPage);     // 添加收到歌曲页数
    bool        AddSequencePowerPage(int nPage);     // 添加收到歌曲页数

private:
    int         m_nSchID;                   // 要添加到的目标定时方案ID

    vector<int>      m_SectionPage;         // 收到的分区页数容器
    vector<int>      m_GroupPage;           //  收到的分组页数容器
    vector<int>      m_SongPage;            // 收到的歌曲页数容器
    vector<int>      m_SequencePowerPage;            // 收到的电源时序器页数容器
};





#endif // EXTIMER_H
