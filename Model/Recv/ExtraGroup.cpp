#include "ExtraGroup.h"
#include <algorithm>        //包含sort和find算法

CExtraGroup::CExtraGroup()
{
        m_strID = "";
        m_strName = "";
        m_nZoneCount = 0;
        m_vecSection.clear();
}

bool CExtraGroup::IsDataFull()
{
        return (m_vecSection.size() == m_nZoneCount);
}

bool CExtraGroup::AddSection(vector<string> &vecSection, int nPage)
{
        if(AddSecPage(nPage))
        {
            for(int i=0; i<(int)vecSection.size(); i++)
            {
                string str = vecSection[i];
                m_vecSection.push_back(str);
            }
            return true;
        }

        return false;
}

bool CExtraGroup::AddSecPage(int nPage)
{
        vector<int>::iterator iter = find(m_SectionPage.begin(), m_SectionPage.end(), nPage);
        if(iter == m_SectionPage.end())     // 未查找到相同页数
        {
            m_SectionPage.push_back(nPage);
            return true;
        }

        return false;
}

