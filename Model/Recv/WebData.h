#ifndef WEBDATA_H
#define WEBDATA_H

#include <map>
#include "ExtraUser.h"
#include "ExtraGroup.h"
#include "ExtraTimer.h"
#include "ExtraFireCollector.h"
#include "ExtraMonitorEvent.h"

// 数据类型
typedef enum
{
    DATA_ZONE,
    DATA_GROUP,
    DATA_SONG,
    DATA_SEQUENCE_POWER
}DataType;

// 请求类型
typedef enum
{
        RT_UNKNOW = -1,     // 未知

        RT_ADD_USER = 0,    // 添加用户
        RT_ADD_GROUP,       // 添加分组
        RT_EDIT_USER,       // 编辑用户
        RT_EDIT_GROUP,      // 编辑分组
        RT_ADD_TIMER,       // 添加定时点
        RT_EDIT_TIMER,      // 编辑定时点
        RT_EDIT_FIRE,       // 编辑消防采集器
        RT_EDIT_EVENT       // 编辑监控事件
}Request;




/*
 * Web端分包传输的数据保存，避免数据遗漏
 */
class CWebData
{
public:
    CWebData();
    bool    IsDataFull();
    bool    SetSection(vector<string>& vecSection, int nPage);
    bool    SetGroup(vector<string>& vecGroup, int nPage);
    bool    SetSong(vector<string>& vecSong, int nPage);

    bool    SetFireChannel(CExtraChannel &channel, int nPage);

    Request             m_Request;            // 请求类型

    CExtraUser          m_ExUser;           // 用户数据
    CExtraGroup         m_ExGroup;          // 分组
    CExTimePoint        m_ExTimePoint;      // 定时点
    CExtraFireCollector m_ExFireCollector;  // 消防采集器
    CExtraMonitorEvent  m_ExMonEvent;       // 监控事件
};



class CWebDatas
{
public:
    CWebDatas();
    void    HandleWebData(string strComID);         // 处理数据，是否接收完全

    void    AddWebData(string strComID, string strUserID, CWebData& webData);
    void    RemoveWebData(string strComID);

    int     SetSection(string strComID, string strUserID, vector<string>& vecSection, int nPage);         // 设置分区
    int     SetGroup(string strComID, string strUserID, vector<string>& vecGroup, int nPage);            // 设置分组
    int     SetSong(string strComID, string strUserID, vector<string>& vecSong, int nPage);                 // 设置消防通道

    int     SetSequencePower(string strComID, string strUserID, vector<string>& vecSeqPwr,vector<unsigned short> channels,int nPage);

    int     SetAudioCollector(string strComID, string strUserID,ST_TIMER_AUDIO_COLLECTOR_INFO &stAudioCollector);

    int     SetFireChannel(string strComID, CExtraChannel& channel, int nPage);

private:
    bool    IsDataFull(string strComID);
    bool    IsExistID(string strComID);

    void    ResponSetData(string strComID, string strUserID, DataType dt, int nPage, int nResult);                 // 设置分区/分组/歌曲数据 回复

    void    AddUser(string strComID);
    void    EditUser(string strComID);

    void    AddGroup(string strComID);
    void    EditGroup(string strComID);

    int     CheckTimePoint(string strComID);      // 检查定时点数据是否正确
    void    HandleTimePoint(string strComID);

    int     CheckFireCollector(string strComID);   // 检测消防采集器数据是否正确
    void    EditFireCollector(string strComID);

    int     CheckMonEvent(string strComID);    // 检测监控事件数据是否正确
    void    EditMonEvent(string strComID);

private:
    map<string, CWebData>  m_IDData;           // 命令ID与接收数据映射
    map<string, string>    m_ComUser;          // 命令ID与用户ID映射,用于命令回复
};


extern CWebDatas g_WebDatas;





#endif // WEBDATA_H
