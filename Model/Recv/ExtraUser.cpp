#include "ExtraUser.h"
#include <algorithm>   //包含sort和find算法

CExtraUser::CExtraUser()
{
    m_strParAccount = "";
    m_strAccount = "";
    m_strPassword = "";
    m_strUserName = "";
    m_nLimitCode = 0;
    m_nZoneCount = 0;
    //m_nGroupCount = 0;

    m_vecSection.clear();
    //m_vecGroup.clear();
}

bool CExtraUser::IsDataFull()
{
    return (m_vecSection.size() == m_nZoneCount);
    //&& m_vecGroup.size() == m_nGroupCount);
}


bool CExtraUser::AddSection(vector<string> &vecSection, int nPage)
{
    if(AddSecPage(nPage))
    {
        for(int i=0; i<(int)vecSection.size(); i++)
        {
            m_vecSection.push_back(vecSection[i]);
        }
        return true;
    }

    return false;
}

#if 0
bool CExtraUser::AddGroup(vector<string> &vecGroup, int nPage)
{
        if(AddGroupPage(nPage))
        {
            for(int i=0; i<(int)vecGroup.size(); i++)
            {
                m_vecGroup.push_back(vecGroup[i]);
            }
            return true;
        }

    return false;
}
#endif

bool CExtraUser::AddSecPage(int nPage)
{
    vector<int>::iterator iter = find(m_SectionPage.begin(), m_SectionPage.end(), nPage);
    if(iter == m_SectionPage.end())     // 未查找到相同页数
    {
        m_SectionPage.push_back(nPage);
        return true;
    }

    return false;
}

#if 0
bool CExtraUser::AddGroupPage(int nPage)
{
        vector<int>::iterator iter = find(m_GroupPage.begin(), m_GroupPage.end(), nPage);
        if(iter == m_GroupPage.end())     // 未查找到相同页数
        {
            m_GroupPage.push_back(nPage);
            return true;
        }

        return false;
}
#endif


