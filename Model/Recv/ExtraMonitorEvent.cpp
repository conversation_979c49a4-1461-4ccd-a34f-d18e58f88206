#include "ExtraMonitorEvent.h"

CExtraMonitorEvent::CExtraMonitorEvent()
{
    m_bValid = false;
    //m_eventType = EVENT_UNKNOWN;
    m_nVolume = 0;
    m_SoundPath = "";
    m_nTimeType= 0;
    m_strWeekday = "";            // 从低位到高位，第1位为星期一，第2位为星期二...
    m_strStartDate = "";          // 开始日期，例如 2019-1-24
    m_strEndDate = "";            // 结束日期，例如 2019-1-25
    m_strStartTime = "";          // 开始时间，例如 20:10:30
    m_strEndTime = "";            // 结束时间，例如 20:20:30
    m_nSectionCount = 0;          // 待添加的分区数

    m_vecSecMac.clear();
}

CExtraMonitorEvent::~CExtraMonitorEvent()
{

}

bool CExtraMonitorEvent::IsDataFull()
{
    return (m_vecSecMac.size() == m_nSectionCount);
}

bool CExtraMonitorEvent::AddSection(vector<string> &vecSection, int nPage)
{
    if(AddSecPage(nPage))
    {
        for(int i=0; i<(int)vecSection.size(); i++)
        {
            string strMac = vecSection[i];
            m_vecSecMac.push_back(strMac);
        }

        return true;
    }

    return false;
}

bool CExtraMonitorEvent::AddSecPage(int nPage)
{
    vector<int>::iterator iter = find(m_SectionPage.begin(), m_SectionPage.end(), nPage);
    if(iter == m_SectionPage.end())     // 未查找到相同页数
    {
        m_SectionPage.push_back(nPage);
        return true;
    }

    return false;
}

