#include "stdafx.h"
#include "ExtraTimer.h"
#include <algorithm>        //包含sort和find算法

CExTimePoint::CExTimePoint()
{
    m_nID = -1;                      // 定时点ID:1,2,3...
    m_strName = "";                  // 定时点名称
    m_bValid = true;                 // 是否有效，默认为true
    m_bPlayToEnd = false;            // 是否播放至结束，默认为false
    m_bResumePlay = false;           // 定时结束后是否恢复歌曲，目前只是分布模式有效，默认为false
    m_bInterCut = false;             // 定时点是否可以强行插播其它定时点，默认为false
    m_nVolume = 0;                   // 定时点音量
    m_bVolFollowDev = false;         // 定时点音量是是否跟随设备音量，默认为false
    m_nPlayMode = 0;                 // 0：顺序播放   1：循环播放   2：随机播放
    m_bRandomSingle = false;         // 定时点是否随机播放单曲，默认为false
    m_nTimeMode = 0;                 // 定时方式，0：按周循环  1：按日期循环
    m_strWeekday = "";               // 从低位到高位，第1位为星期一，第2位为星期二...
    m_strStartDate = "";             // 开始日期，例如 2019-1-24
    m_strEndDate = "";               // 结束日期，例如 2019-1-25
    m_strStartTime = "";             // 开始时间，例如 20:10:30
    m_strEndTime = "";               // 结束时间，例如 20:20:30
    m_nSectionCount = 0;             // 待添加的分区数
    m_nGroupCount = 0;               // 待添加的分组数
    m_nSongCount = 0;                // 待添加的歌曲数
    m_nSequencePowerCount = 0;       // 待添加的时序器数

    m_nSourceType = 0;
    m_nDeviceType = 0;

    m_nSchID = 0;                    // 要添加到的目标定时方案ID
    m_vecSection.clear();
    m_vecGroup.clear();
    m_vecSong.clear();
    memset(&m_stAudioCollector,0,sizeof(m_stAudioCollector));
}


bool CExTimePoint::IsDataFull()
{
    bool isOK=false;
    if(m_nDeviceType == TIMER_DEVICE_TYPE_DECODING_TERMINAL)
    {
        if(m_vecSection.size() == m_nSectionCount &&
           m_vecGroup.size() == m_nGroupCount
            && ( (m_nSourceType == TIMER_SOURCE_TYPE_LOCAL_SONG && m_vecSong.size() == m_nSongCount) ||
                (m_nSourceType == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR && (strlen(m_stAudioCollector.mac)>0) ) )
           )
        {
            isOK=true;
        }
    }
    else
    {
        if(m_vecSequencePower.size() == m_nSequencePowerCount)
        {
            isOK=true;
        }
    }
    return isOK;
}

bool CExTimePoint::AddSection(vector<string> &vecSection, int nPage)
{
    if(AddSecPage(nPage))
    {
        for(int i=0; i<(int)vecSection.size(); i++)
        {
            m_vecSection.push_back(vecSection[i]);
        }
        return true;
    }

    return false;
}

bool CExTimePoint::AddGroup(vector<string> &vecGroup, int nPage)
{
    if(AddGroupPage(nPage))
    {
        for(int i=0; i<(int)vecGroup.size(); i++)
        {
            m_vecGroup.push_back(vecGroup[i]);
        }
        return true;
    }

    return false;
}

bool CExTimePoint::AddSong(vector<string> &vecSong, int nPage)
{
    if(AddSongPage(nPage))
    {
        for(int i=0; i<(int)vecSong.size(); i++)
        {
            m_vecSong.push_back(vecSong[i]);
        }
        //printf("song size : %d\n", m_vecSong.size());
        LOG(FORMAT("song size : %d\n", m_vecSong.size()), LV_INFO);
        return true;
    }

    return false;
}

bool CExTimePoint::AddSequencePower(vector<string> &vecSequencePower,vector<unsigned short> channels, int nPage)
{
    if(AddSequencePowerPage(nPage))
    {
        for(int i=0; i<(int)vecSequencePower.size(); i++)
        {
            m_vecSequencePower.push_back(vecSequencePower[i]);
            m_vecSequencePowerChannels.push_back(channels[i]);
        }
        return true;
    }

    return false;
}

bool CExTimePoint::AddAudioCollector(ST_TIMER_AUDIO_COLLECTOR_INFO stAudioCollector)
{
    m_stAudioCollector = stAudioCollector;
    return true;
}

bool CExTimePoint::AddSecPage(int nPage)
{
    vector<int>::iterator iter = find(m_SectionPage.begin(), m_SectionPage.end(), nPage);
    if(iter == m_SectionPage.end())     // 未查找到相同页数
    {
        m_SectionPage.push_back(nPage);
        return true;
    }

    return false;
}

bool CExTimePoint::AddGroupPage(int nPage)
{
    vector<int>::iterator iter = find(m_GroupPage.begin(), m_GroupPage.end(), nPage);
    if(iter == m_GroupPage.end())     // 未查找到相同页数
    {
        m_GroupPage.push_back(nPage);
        return true;
    }

    return false;
}

bool CExTimePoint::AddSongPage(int nPage)
{
    vector<int>::iterator iter = find(m_SongPage.begin(), m_SongPage.end(), nPage);
    if(iter == m_SongPage.end())     // 未查找到相同页数
    {
        m_SongPage.push_back(nPage);
        return true;
    }

    return false;
}

bool CExTimePoint::AddSequencePowerPage(int nPage)
{
    vector<int>::iterator iter = find(m_SequencePowerPage.begin(), m_SequencePowerPage.end(), nPage);
    if(iter == m_SequencePowerPage.end())     // 未查找到相同页数
    {
        m_SequencePowerPage.push_back(nPage);
        return true;
    }

    return false;
}
