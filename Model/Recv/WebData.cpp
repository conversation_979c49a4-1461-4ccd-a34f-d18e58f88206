#include "stdafx.h"
#include "WebData.h"

CWebDatas g_WebDatas;

CWebData::CWebData()
{
    m_Request = RT_UNKNOW;

}


bool CWebData::IsDataFull()
{
    switch (m_Request)
    {
    case RT_ADD_USER:
    case RT_EDIT_USER:
        {
            return m_ExUser.IsDataFull();
        }
        break;
    case RT_ADD_GROUP:
    case RT_EDIT_GROUP:
        {
            return m_ExGroup.IsDataFull();
        }
        break;
    case RT_ADD_TIMER:
    case RT_EDIT_TIMER:
        {
            return m_ExTimePoint.IsDataFull();
        }
        break;
    case RT_EDIT_FIRE:
        {
            return m_ExFireCollector.IsDataFull();
        }
        break;
    case RT_EDIT_EVENT:
        {
            return m_ExMonEvent.IsDataFull();
        }
        break;
    default:
        break;
    }

    return false;
}

bool CWebData::SetSection(vector<string> &vecSection, int nPage)
{
    switch(m_Request)
    {
    case RT_ADD_USER:
    case RT_EDIT_USER:
        {
            return m_ExUser.AddSection(vecSection, nPage);
        }
        break;
    case RT_ADD_GROUP:
    case RT_EDIT_GROUP:
        {
            return m_ExGroup.AddSection(vecSection, nPage);
        }
        break;
    case RT_ADD_TIMER:
    case RT_EDIT_TIMER:
        {
            return m_ExTimePoint.AddSection(vecSection, nPage);
        }
        break;
    case RT_EDIT_EVENT:
        {
            return m_ExMonEvent.AddSection(vecSection, nPage);
        }
        break;
    default:
        break;
    }

    return false;
}

bool CWebData::SetGroup(vector<string> &vecGroup, int nPage)
{
    switch(m_Request)
    {
    case RT_ADD_USER:
    case RT_EDIT_USER:
        {
            //return m_ExUser.AddGroup(vecGroup, nPage);
        }
        break;
    case RT_ADD_TIMER:
    case RT_EDIT_TIMER:
        {
            return m_ExTimePoint.AddGroup(vecGroup, nPage);
        }
        break;
    default:
        break;
    }

    return false;
}

bool CWebData::SetSong(vector<string> &vecSong, int nPage)
{
    switch(m_Request)
    {
        case RT_ADD_TIMER:
        case RT_EDIT_TIMER:
        {
            return m_ExTimePoint.AddSong(vecSong, nPage);
        }
        break;
    default:
        break;
    }

    return false;
}

bool CWebData::SetFireChannel(CExtraChannel& channel, int nPage)
{
        return m_ExFireCollector.SetChannel(channel, nPage);
}


///////////////////////////////////////////////////////////////////////

CWebDatas::CWebDatas()
{
    m_IDData.clear();
    m_ComUser.clear();
}

void CWebDatas::HandleWebData(string strComID)
{
    if(IsDataFull(strComID))            // 数据是否接收完全
    {
        CWebData wd = m_IDData[strComID];
        switch (wd.m_Request)
        {
        case RT_ADD_USER:
        {
            AddUser(strComID);
        }
            break;
        case RT_EDIT_USER:
        {
            EditUser(strComID);
        }
            break;
        case RT_ADD_GROUP:
        {
            AddGroup(strComID);
        }
            break;
        case RT_EDIT_GROUP:
        {
            EditGroup(strComID);
        }
            break;
        case RT_ADD_TIMER:
        case RT_EDIT_TIMER:
        {
            HandleTimePoint(strComID);
        }
            break;
        case RT_EDIT_FIRE:
        {
            EditFireCollector(strComID);
        }
            break;
        case RT_EDIT_EVENT:
        {
            EditMonEvent(strComID);
        }
            break;
        default:
            break;
        }

        RemoveWebData(strComID);
    }
}



void CWebDatas::AddWebData(string strComID, string strUserID, CWebData &webData)
{
    NOTIFY("AddWebData : %s", strComID.data());
    m_IDData[strComID] = webData;
    m_ComUser[strComID] = strUserID;
}

void CWebDatas::RemoveWebData(string strComID)
{
    m_IDData.erase(strComID);
    m_ComUser.erase(strComID);
}

int CWebDatas::SetSection(string strComID, string strUserID, vector<string> &vecSection, int nPage)
{
    int nResult = EC_SUCCESS;
    if(IsExistID(strComID))
    {
        CWebData& wd = m_IDData[strComID];                  // 必须使用引用，需要改变容器内类的值
        bool IsSuccess = wd.SetSection(vecSection, nPage);
        LOG(FORMAT("SetSec Resquest : %d\n", wd.m_Request), LV_INFO);
        nResult = (IsSuccess) ? EC_SUCCESS : EC_ERROR;
    }
    else
    {
        nResult = EC_ERROR;
    }

    ResponSetData(strComID, strUserID, DATA_ZONE, nPage, nResult);
    return nResult;
}

int CWebDatas::SetGroup(string strComID, string strUserID, vector<string> &vecGroup, int nPage)
{
    int nResult = EC_SUCCESS;
    if(IsExistID(strComID))
    {
        CWebData& wd = m_IDData[strComID];
        bool IsSuccess = wd.SetGroup(vecGroup, nPage);
        nResult = (IsSuccess) ? EC_SUCCESS : EC_ERROR;
    }
    else
    {
        nResult = EC_ERROR;
    }

    ResponSetData(strComID, strUserID, DATA_GROUP, nPage, nResult);
    return nResult;
}

int CWebDatas::SetSong(string strComID, string strUserID, vector<string> &vecSong, int nPage)
{
    int nResult = EC_SUCCESS;
    if(IsExistID(strComID))
    {
        CWebData& wd = m_IDData[strComID];
        bool IsSuccess = wd.SetSong(vecSong, nPage);
        nResult = (IsSuccess) ? EC_SUCCESS : EC_ERROR;
    }
    else
    {
        nResult = EC_ERROR;
    }

    ResponSetData(strComID, strUserID, DATA_SONG, nPage, nResult);
    return nResult;
}


int CWebDatas::SetSequencePower(string strComID, string strUserID, vector<string>& vecSeqPwr,vector<unsigned short> channels,int nPage)
{
    int nResult = EC_SUCCESS;
    if(IsExistID(strComID))
    {
        CWebData& wd = m_IDData[strComID];
        bool IsSuccess = wd.m_ExTimePoint.AddSequencePower(vecSeqPwr,channels,nPage);
        nResult = (IsSuccess) ? EC_SUCCESS : EC_ERROR;
    }
    else
    {
        nResult = EC_ERROR;
    }
    return nResult;
}


int CWebDatas::SetAudioCollector(string strComID, string strUserID,ST_TIMER_AUDIO_COLLECTOR_INFO &stAudioCollector)
{
    int nResult = EC_SUCCESS;
    if(IsExistID(strComID))
    {
        CWebData& wd = m_IDData[strComID];
        wd.m_ExTimePoint.AddAudioCollector(stAudioCollector);
    }
    else
    {
        nResult = EC_ERROR;
    }
    return nResult;
}

int CWebDatas::SetFireChannel(string strComID, CExtraChannel &channel, int nPage)
{
    int nResult = EC_SUCCESS;
    if(IsExistID(strComID))
    {
        CWebData& wd = m_IDData[strComID];
        bool IsSuccess = wd.SetFireChannel(channel, nPage);
        nResult = (IsSuccess) ? EC_SUCCESS : EC_ERROR;
    }
    else
    {
        nResult = EC_ERROR;
    }

    return nResult;
}


bool CWebDatas::IsDataFull(string strComID)
{
        if(IsExistID(strComID))
        {
                return m_IDData[strComID].IsDataFull();
        }

        return false;
}

bool CWebDatas::IsExistID(string strComID)
{
    if(m_IDData.count(strComID) > 0 && m_ComUser.count(strComID) > 0)
    {
            return true;
    }

    return false;
}

void CWebDatas::ResponSetData(string strComID, string strUserID, DataType dt, int nPage, int nResult)
{
    string strBuf = "";
    CWebData wd = m_IDData[strComID];

    LOG(FORMAT("Request : %d   dt = %d\n", wd.m_Request, dt), LV_INFO);
    switch(wd.m_Request)
    {
    case RT_ADD_USER:
    case RT_EDIT_USER:
    {
        if(dt == DATA_ZONE)
        {
            strBuf = CWebProtocol::CmdResponseSetUserZone(strComID, wd.m_ExUser.m_strAccount, nPage, nResult);
        }
        if(dt == DATA_GROUP)
        {
            strBuf = CWebProtocol::CmdResponseSetUserGroup(strComID, wd.m_ExUser.m_strAccount, nPage, nResult);
        }
    }
        break;
    case RT_ADD_GROUP:
    case RT_EDIT_GROUP:
    {
        strBuf = CWebProtocol::CmdResponseSetGroupSec(strComID, wd.m_ExGroup.m_strID, nPage, nResult);
    }
        break;
    /*case RT_ADD_TIMER:
    case RT_EDIT_TIMER:
    {
        if(dt == DATA_ZONE)
        {
            strBuf = CWebProtocol::CmdResponseSetTimeZone(wd.m_ExTimePoint.GetSchID()+1, strComID, wd.m_ExTimePoint, nPage, nResult);
        }
        if(dt == DATA_GROUP)
        {
            strBuf = CWebProtocol::CmdResponseSetTimeGroup(wd.m_ExTimePoint.GetSchID()+1, strComID, wd.m_ExTimePoint, nPage, nResult);
        }
        if(dt == DATA_SONG)
        {
            strBuf = CWebProtocol::CmdResponseSetTimeSong(wd.m_ExTimePoint.GetSchID()+1, strComID, wd.m_ExTimePoint, nPage, nResult);
        }
    }
        break;*/
   case RT_EDIT_EVENT:
    {
        strBuf = CWebProtocol::CmdResponseSetEventZone(strComID, wd.m_ExMonEvent.m_strMonMac, wd.m_ExMonEvent.m_eventType,
                                                                                                  nPage, nResult);
    }
        break;
    default:
        break;
    }

    // 回复Web终端
    LPCWebSection pWeb = g_Global.m_WebSections.GetWebSectionBySocketID(strUserID);
    if(pWeb != NULL)
    {
        g_Global.m_WebNetwork.m_WebSend.CommandSend(strBuf.data(), *pWeb);
    }
    else
    {
        LOG("pWeb is NULL | WebData", LV_ERROR);
    }

    HandleWebData(strComID);
}

void CWebDatas::AddUser(string strComID)
{
    if(m_IDData.count(strComID) == 0 || m_ComUser.count(strComID) == 0)
    {
        return;
    }

    CWebData wd = m_IDData[strComID];
    CExtraUser user = wd.m_ExUser;
    int nResult = EC_SUCCESS;

    if(g_Global.m_Users.GetUserCount() >= MAX_USER_COUNT)
    {
        nResult = EC_MAX_NUM;
        goto END;
    }

    if(user.m_strAccount.length() < MIN_USER_ACCOUNT_LEN || user.m_strAccount.length() > MAX_USER_ACCOUNT_LEN ||
       user.m_strParAccount.length() <  MIN_USER_ACCOUNT_LEN || user.m_strParAccount.length() >  MAX_USER_ACCOUNT_LEN ||
       user.m_strPassword.length() < MIN_USER_PASSWORD_LEN || user.m_strPassword.length() > MAX_USER_PASSWORD_LEN ||
       user.m_strUserName.length() > MAX_USER_NAME_LEN)
    {
        nResult = EC_TARGET_FORMAT_ERROR;
        goto END;
    }

    //判断账户名是否合规，字母开头，接受由字母+数字+下划线组合。(3-16位)
    try
    {
        string pattenrn("^[a-zA-Z][a-zA-Z0-9_]{2,15}$");
        regex  reg(pattenrn);
        bool    isFormat = regex_match(user.m_strAccount, reg);
        if(!isFormat)
        {
            nResult = EC_TARGET_FORMAT_ERROR;
            goto END;
        }
    }
    catch(std::regex_error& e)
    {
        nResult = EC_TARGET_FORMAT_ERROR;
        goto END;
    }
    
    #if APP_IS_AISP_TZY_ENCRYPTION
    if(g_Global.m_PasswordMod.getAccessToken().empty())
    {
        nResult = EC_USER_LOGIN_TZY_Cipher_CONNECT_ERROR;
        goto END;
    }
    #endif

    if(!g_Global.m_Users.IsExistUser(user.m_strAccount))
    {
            UserData_t ud;
            ud.strAccount = user.m_strAccount;
            ud.strParAccount = user.m_strParAccount;
            ud.strPassword = user.m_strPassword;
            ud.strUserId = GetGUID().Data();
            ud.nPlayMode = PM_ORDER;   //默认顺序播放

            ud.nLimitCode = user.m_nLimitCode;

            ud.strUserName = user.m_strUserName;
            ud.nUserType = user.m_nUserType;
            ud.nStorageCapacity = user.m_nStorageCapacity;

            //******** 取消添加账户、编辑账户时的权限设置，改为独立指令
            #if 0
            //仅二级账户和管理员拥有高级权限，其他的需要剔除
            if(ud.nUserType>USER_TYPE_LEVEL2)
            {
                ud.nLimitCode=0;
                if(user.m_nLimitCode & USER_LIMITS_REPEATED_LOGIN)
                {
                    ud.nLimitCode |= USER_LIMITS_REPEATED_LOGIN;
                }
            }
            #else
            ud.nLimitCode = 0;
            #endif

            nResult = g_Global.m_Users.InsertUser(ud);       // 添加用户
            if(nResult == EC_SUCCESS)
            {
                // 设置用户分区分组
                g_Global.m_Users.SetUserSection(user.m_strAccount, user.GetSection());
                //g_Global.m_Users.SetUserGroup(user.m_strAccount, user.GetGroup());

                //********添加用户成功后添加一个歌曲列表，命名为Music List(账号名)
                CMyString strListID=GetGUID();
                CMyString cstrListName;
                cstrListName.Format("%s (%s)","Music List",user.m_strAccount.data());
                g_Global.m_PlayList.AddList(CMyString(strListID), cstrListName,user.m_strAccount);
                // 写入文件
                int nListIndex = g_Global.m_PlayList.FindListByIdAndName(CMyString(strListID),cstrListName);
                if(nListIndex>=0)
                {
                    g_Global.m_PlayList.SetListDateTime(nListIndex, GetCurrentDateTime());
                    g_Global.m_PlayList.PushWriteXmlTask(user.m_strAccount);

                    g_Global.m_Sections.PushWriteXmlAccountTask(user.m_strAccount);
                }
            }
            else
            {
                printf("nResult=%d\n",nResult);
            }
    }
    else
    {
            nResult = EC_USER_EXISTED;
    }

END:
    LPCWebSection pWeb = g_Global.m_WebSections.GetWebSectionBySocketID(m_ComUser[strComID]);
    g_Global.m_WebNetwork.m_WebSend.WebResponseAddUser(strComID, user.m_strAccount, pWeb, nResult);

    if(nResult == EC_SUCCESS)
    {
        //g_Global.m_WebNetwork.ForwardUserInfo(user.m_strAccount, UD_USER|UD_SECTION);
        // ******** 分发超级用户信息
        //g_Global.m_WebNetwork.ForwardUserInfo(SUPER_USER_NAME, UD_USER);
        //todo 不仅发给超级用户，也要发给它的所有上级用户的用户信息
        
        string strDirectParUser=g_Global.m_Users.GetParentUserAccountByAccount(user.m_strAccount);
        vector<string> vecParAccount;
        g_Global.m_Users.GetAllParentUserAccountByAccount(user.m_strAccount,vecParAccount);
        for(int i=0;i<vecParAccount.size();i++)
        {
            //发往所有上级用户，但是目标用户信息为直接上级即可
            g_Global.m_WebNetwork.ForwardUserInfo(vecParAccount[i],strDirectParUser,UD_USER);
        }
    }
}

void CWebDatas::EditUser(string strComID)
{
    if(m_IDData.count(strComID) == 0 || m_ComUser.count(strComID) == 0)
    {
        return;
    }

    CWebData wd = m_IDData[strComID];
    CExtraUser user = wd.m_ExUser;
    int nResult = EC_SUCCESS;

    if(user.m_strAccount.length() < MIN_USER_ACCOUNT_LEN || user.m_strAccount.length() > MAX_USER_ACCOUNT_LEN ||
       user.m_strPassword.length() < MIN_USER_PASSWORD_LEN || user.m_strPassword.length() > MAX_USER_PASSWORD_LEN ||
       user.m_strUserName.length() > MAX_USER_NAME_LEN)
    {
            nResult = EC_TARGET_FORMAT_ERROR;
    }

    bool bindDeviceChanged=false;
    bool parAccountChanged=false;
    LPCUserInfo pDestUser = g_Global.m_Users.GetUserByAccount(user.m_strAccount);
    LPCUserInfo pDestParUser = g_Global.m_Users.GetUserByAccount(user.m_strParAccount);
    if(user.m_strAccount!=SUPER_USER_NAME)
    {
        if(pDestUser == NULL || pDestParUser == NULL)
        {
            nResult = EC_USER_NOTEXIST;
        }
        else
        {
            //判断上级用户是否符合要求
            //判断上级用户属于几级
            //先判断目标用户有几级（包含本身那级）
            int destUserTotalLevel=g_Global.m_Users.GetUserTotalLevels(user.m_strAccount);
            int parUserType = pDestParUser->GetUserType();
            if(destUserTotalLevel+parUserType>USER_TYPE_LEVEL4)
            {
                nResult = EC_ERROR;
                printf("destUserTotalLevel=%d,parUserType=%d,over Limit!\n",destUserTotalLevel,parUserType);
            }
        }
    }
    else
    {
        user.m_strParAccount="";
    }

    //判断上级账户是否和目标账户相同
    if(user.m_strAccount == user.m_strParAccount)
    {
        nResult = EC_ERROR;
    }

    #if APP_IS_AISP_TZY_ENCRYPTION
    if(g_Global.m_PasswordMod.getAccessToken().empty())
    {
        nResult = EC_USER_LOGIN_TZY_Cipher_CONNECT_ERROR;
    }
    #endif
    

    vector<string> vecOldParAccount;
    if(nResult == EC_SUCCESS)
    {
            // 设置密码
            g_Global.m_Users.SetUserPassword(user.m_strAccount, user.m_strPassword);

            //设置用户名称
            g_Global.m_Users.SetUserName(user.m_strAccount, user.m_strUserName);

            //设置父账户（如果变更了的情况下)
            if(pDestUser->GetParAccount()!=user.m_strParAccount)
            {
                printf("user parent changed!!!\n");
                parAccountChanged=true;

                //先记录原来的上级，以便变更上级后通知原来的上级
                g_Global.m_Users.GetAllParentUserAccountByAccount(user.m_strAccount,vecOldParAccount);


                g_Global.m_Users.SetUserParent(user.m_strAccount,user.m_strParAccount);
                //如果改变了父账户，那么需要它和它下面的子账户的user_type
                g_Global.m_Users.RefreshUserType(user.m_strAccount);
            }

            // 设置用户分区(如果变化了，需要通知此账户下面的所有子账户，重新登录)
            
            bindDeviceChanged=g_Global.m_Users.SetUserSection(user.m_strAccount, user.GetSection());

            //设置用户权限

            //******** 取消添加账户、编辑账户时的权限设置，改为独立指令
            //******** 为了避免编辑账户时修改了账户的层级，此处仍需要修正
            #if 1
            int nLimitCode=pDestUser->GetLimitCode();
            int userLimitCodeCur=pDestUser->GetLimitCode();
            //二级账户和管理员可以拥有高级权限(USER_LIMITS_PLAYLIST、USER_LIMITS_TIMER)
            //三级账户还可以拥有USER_LIMITS_CREATE_USER权限
            //四级账户只剩下允许重复登录的权限
            if(pDestUser->GetUserType() >USER_TYPE_LEVEL2)
            {
                nLimitCode=0;
                if(userLimitCodeCur & USER_LIMITS_REPEATED_LOGIN)
                {
                    nLimitCode |= USER_LIMITS_REPEATED_LOGIN;
                }
                if(pDestUser->GetUserType() == USER_TYPE_LEVEL3)
                {
                    if(userLimitCodeCur & USER_LIMITS_CREATE_USER)
                    {
                        nLimitCode |= USER_LIMITS_CREATE_USER;
                    }
                }
            }
            if(nLimitCode!=pDestUser->GetLimitCode())
            {
                g_Global.m_Users.SetUserAuthority(user.m_strAccount,nLimitCode);
            }
            #endif
    }

    LPCWebSection pWeb = g_Global.m_WebSections.GetWebSectionBySocketID(m_ComUser[strComID]);
    g_Global.m_WebNetwork.m_WebSend.WebResponseModifyUser(strComID, user.m_strAccount, pWeb, nResult);

    // 通知用户重新登录
    if(nResult == EC_SUCCESS)
    {
        //通知该用户重新登录(绑定设备信息或者父账户变动了才需要告知下属用户)
        g_Global.m_WebNetwork.RequestUserReLogin(user.m_strAccount, RG_AUTH_CHANGER);

        if(bindDeviceChanged || parAccountChanged)
        {
            //不允许绑定超出父级账户外的分区(如果变化了，如要清理),此处要放在前面更新分组信息前面（需要先更新用户分区）
            g_Global.m_Users.DeleteInvalidZonesMac(user.m_strAccount);

            //更新分组信息
            g_Global.m_Groups.UpdateGroupSection(user.m_strAccount);

            g_Global.m_Sections.PushWriteXmlAccountTask(user.m_strAccount);

            vector<userParm>  vecSubUserInfo;
            g_Global.m_Users.GetAllSubUserByAccount(user.m_strAccount, vecSubUserInfo);
            for(int i=0;i<vecSubUserInfo.size();i++)
            {
                g_Global.m_WebNetwork.RequestUserReLogin(vecSubUserInfo[i].strAccount, RG_AUTH_CHANGER);

                g_Global.m_Sections.PushWriteXmlAccountTask(vecSubUserInfo[i].strAccount);
            }

            //******** 通知原来的上级账户
            if(parAccountChanged)
            {
                //通知原来的上级账户，让其自身变化
                //由于admin数据量庞大，需要区分对待，告知其最高子账户变化即可
                //找到除管理员外最高级别的父账户，以便后面通知管理员（专用），节省资源。
                #if 0
                for(int i=0;i<vecOldParAccount.size();i++)
                {
                    g_Global.m_WebNetwork.ForwardUserInfo(vecOldParAccount[i],vecOldParAccount[i],UD_USER);
                }
                #else
                if(vecOldParAccount.size() == USER_TYPE_ADMIN)  //如果size=1，代表直接父级账户是管理员，需要发送admin账户的信息
                    g_Global.m_WebNetwork.ForwardUserInfo(SUPER_USER_NAME,SUPER_USER_NAME,UD_USER);
                if(vecOldParAccount.size() >= USER_TYPE_LEVEL2)  //如果size>=2，那么发送除admin外最高父账户的信息
                    g_Global.m_WebNetwork.ForwardUserInfo(SUPER_USER_NAME,vecOldParAccount[vecOldParAccount.size()-2],UD_USER);
                for(int i=0;i<vecOldParAccount.size();i++)
                {
                    if(vecOldParAccount[i] != SUPER_USER_NAME)
                    {
                        g_Global.m_WebNetwork.ForwardUserInfo(vecOldParAccount[i],vecOldParAccount[i],UD_USER);

                        g_Global.m_Sections.PushWriteXmlAccountTask(vecOldParAccount[i]);
                    }
                }
                #endif
            }
        }

        // 分发超级用户信息
        //g_Global.m_WebNetwork.ForwardUserInfo(SUPER_USER_NAME, UD_USER);
        //todo 不仅发给超级用户，也要发给它的所有上级用户的用户信息(绑定设备信息变动了才需要告知下属用户)
        vector<string> vecParAccount;
        g_Global.m_Users.GetAllParentUserAccountByAccount(user.m_strAccount,vecParAccount);
        for(int i=0;i<vecParAccount.size();i++)
        {
            g_Global.m_WebNetwork.ForwardUserInfo(vecParAccount[i],user.m_strAccount,UD_USER);
        }
        
    }
}

void CWebDatas::AddGroup(string strComID)
{
    if(!IsExistID(strComID))
    {
        return;
    }

    CWebData wd = m_IDData[strComID];
    CExtraGroup exGroup = wd.m_ExGroup;
    int nResult = EC_SUCCESS;
    if(g_Global.m_Groups.GetGroupCount() >= MAX_GROUP_COUNT)
    {
        nResult = EC_MAX_NUM;
    }

    // 分组名是否存在
    bool    IsExistName = g_Global.m_Groups.FindGroupByName(exGroup.m_strName.data());
    if(!IsExistName && nResult == EC_SUCCESS)
    {
        // 分组名是否合法
        int nNameLen = exGroup.m_strName.length();
        if(nNameLen > 0 && nNameLen < GROUP_NAME_LEN)
        {
            CGroup group(CMyString(exGroup.m_strID), exGroup.m_strName.data());
            vector<string> vecSection = exGroup.GetSection();
            for(int i=0; i<vecSection.size(); i++)
            {
                group.AddSection(CMyString(vecSection[i]));
            }

            bool IsSuccess=false;
            LPCWebSection pWeb = g_Global.m_WebSections.GetWebSectionBySocketID(m_ComUser[strComID]);
            if(pWeb)
            {
                LPCUserInfo pUser =  g_Global.m_Users.GetUserByID(pWeb->GetSocketID());
                group.SetUserAccount(pUser?pUser->GetAccount():SUPER_USER_NAME);

                g_Global.m_Groups.AddGroup(group);
                IsSuccess = g_Global.WriteXmlFile(FILE_GROUP);
                if(!IsSuccess)
                {
                    nResult = EC_ERROR;
                    g_Global.m_Groups.RemoveGroup(g_Global.m_Groups.GetGroupCount() - 1);
                }
            }
            else
            {
                nResult = EC_ERROR;
            }

            
            #if 0
            // 用户添加分组权限
            LPCUserInfo pUser = g_Global.m_Users.GetUserByID(m_ComUser[strComID]);
            if(pUser != NULL)
            {
                pUser->AddGroupID(exGroup.m_strID); // 添加分组
                pUser->WriteGroupData();                        // 写入数据库
                //g_Global.m_WebNetwork.ForwardUserGroup(NULL, pUser);
                g_Global.m_WebNetwork.ForwardUserInfo(pUser->GetAccount(), UD_GROUP);
                if(!pUser->IsSuperUser())
                {
                    g_Global.m_WebNetwork.ForwardUserInfo(SUPER_USER_NAME, UD_GROUP);
                }
            }
            #endif
        }
        else
        {
            nResult = EC_TARGET_PARM_ERROR;
        }
    }
    else
    {
        nResult = EC_TARGET_EXISTED;
    }

    // 回复Web终端
    LPCWebSection pWeb = g_Global.m_WebSections.GetWebSectionBySocketID(m_ComUser[strComID]);
    g_Global.m_WebNetwork.m_WebSend.WebResponseAddGroup(strComID, exGroup.m_strName, exGroup.m_nZoneCount, pWeb, nResult);
}


void CWebDatas::EditGroup(string strComID)
{
    if(m_IDData.count(strComID) == 0 || m_ComUser.count(strComID) == 0)
    {
        return;
    }

    CWebData wd = m_IDData[strComID];
    CExtraGroup exGroup = wd.m_ExGroup;

    int nResult = EC_SUCCESS;
    int nIndex = 0;
    CGroup* group = g_Global.m_Groups.FindGroupByID(CMyString(exGroup.m_strID), nIndex);
    if(group == NULL)
    {
        nResult = EC_TARGET_NOTEXIST;
    }
    else
    {
        // 分组名是否存在
        string strGroupName = exGroup.m_strName;
        if(strGroupName != group->GetName())
        {
            bool    IsExistName = g_Global.m_Groups.FindGroupByName(strGroupName.data());
            if(IsExistName)
            {
               nResult = EC_TARGET_EXISTED;
            }
        }

        if(nResult == EC_SUCCESS)
        {
            // 分组名是否合法
            if(strGroupName.length() > 0 && strGroupName.length() < GROUP_NAME_LEN)
            {
                g_Global.m_Groups.SetGroupName(nIndex,strGroupName.data());
                vector<string> vecSection = exGroup.GetSection();
                vector<CMyString> vecSec;
                //如果编辑属于非管理员账户的分组，那么判断该分区MAC是否有效
                if(group->GetUserAccount() != SUPER_USER_NAME)
                {
                    LPCUserInfo groupUser=g_Global.m_Users.GetUserByAccount(group->GetUserAccount().Data());
                    for(int i=0; i<vecSection.size(); i++)
                    {
                        bool foundMAC=false;
                        for(int j=0;j<groupUser->GetSectionCount();j++)
                        {
                            if( groupUser->GetSectionMac(j) == vecSection[i] )
                            {
                                foundMAC=true;
                                break;
                            }
                        }
                        if(foundMAC)
                            vecSec.push_back(CMyString(vecSection[i]));
                    }
                }
                else
                {
                    for(int i=0; i<vecSection.size(); i++)
                    {
                        vecSec.push_back(CMyString(vecSection[i]));
                    }
                }

                //将vecSec排序
                std::sort(vecSec.begin(),vecSec.end(),CGroups::SortBySectionId);

                group->SetSecMacs(vecSec);

                bool IsSuccess = g_Global.WriteXmlFile(FILE_GROUP);

                if(!IsSuccess)
                {
                    nResult = EC_ERROR;
                    g_Global.m_Groups.RemoveGroup(g_Global.m_Groups.GetGroupCount() - 1);
                }
            }
            else
            {
                nResult = EC_TARGET_PARM_ERROR;
            }
        }
    }

    // 回复Web终端
    LPCWebSection pWeb = g_Global.m_WebSections.GetWebSectionBySocketID(m_ComUser[strComID]);
    g_Global.m_WebNetwork.m_WebSend.WebResponseModifyGroup(strComID, exGroup.m_strID, exGroup.m_strName,
                                                           exGroup.m_nZoneCount, pWeb, nResult);
}

// 检查定时点数据是否正确
int CWebDatas::CheckTimePoint(string strComID)
{
    CWebData cw = m_IDData[strComID];
    CExTimePoint tp = cw.m_ExTimePoint;
    // 检查定时方案索引
    int nSchemeCount = g_Global.m_TimerScheme.GetSchemeCount();
    if(tp.GetSchID() < 0 || tp.GetSchID() >= nSchemeCount)
    {
        return EC_TC_SCHID_ID_ERROR;
    }

    // 检查定时点索引
    if(cw.m_Request == RT_EDIT_TIMER)
    {
        int nTimerCount = g_Global.m_TimerScheme.GetTimerCount(tp.GetSchID());
        if(tp.m_nID <0 || tp.m_nID > nTimerCount)
        {
            return EC_TC_TP_ID_ERROR;
        }
    }

    // 检查定时点数量
    if(g_Global.m_TimerScheme.GetTimerCount(tp.GetSchID()) >= MAX_TIMER_COUNT)
    {
        return EC_TC_TP_MAXNUM;
    }

    // 检查定时点名称
    if(tp.m_strName.empty() || tp.m_strName.length() > TIMER_NAME_LEN)
    {
        return EC_TC_TP_NAME_ERROR;
    }

    // 检查时间格式
    if(tp.m_nTimeMode != 0 && tp.m_nTimeMode != 1)
    {
        NOTIFY("TimeMode : %d", tp.m_nTimeMode);
        return EC_TC_DATETIME_ERROR;
    }

    // 检查按周循环
    if(tp.m_nTimeMode == 0)
    {
        if(tp.m_strWeekday.empty() || tp.m_strWeekday.length() > 7 || tp.m_strWeekday == "")
        {
            NOTIFY("Weekday : %s", tp.m_strWeekday.data());
            return EC_TC_DATETIME_ERROR;
        }

        for(int i=0; i< tp.m_strWeekday.length(); i++)
        {
            // 检查周日期是否正确
            int nWeekDay = tp.m_strWeekday[i] - 48;
            if(nWeekDay < 1 || nWeekDay > 7)
            {
                return EC_TC_DATETIME_ERROR;
            }
        }
    }

    // 检查日期时间格式
    if(!IsTimeFormat(tp.m_strStartTime.data()) || !IsTimeFormat(tp.m_strEndTime.data()))
    {
        NOTIFY("startTime : %s, endTime : %s", tp.m_strStartTime.data(), tp.m_strEndTime.data());
        return EC_TC_DATETIME_ERROR;
    }

    SYSTEMTIME_Q sStartDate, sEndDate;
    LOG(tp.m_strStartTime.data(), LV_INFO);
    LOG(tp.m_strEndTime.data(), LV_INFO);
    sscanf(tp.m_strStartTime.data(), "%02d:%02d:%02d",&sStartDate.nHour, &sStartDate.nMinute, &sStartDate.nSecond);
    sscanf(tp.m_strEndTime.data(), "%02d:%02d:%02d",&sEndDate.nHour, &sEndDate.nMinute, &sEndDate.nSecond);

    CTime  tNow = CTime::GetCurrentTimeT();
    CTime   timeStart(tNow.GetYear(), tNow.GetMonth(), tNow.GetDay(), sStartDate.nHour, sStartDate.nMinute, sStartDate.nSecond);
    CTime   timeEnd(tNow.GetYear(), tNow.GetMonth(), tNow.GetDay(), sEndDate.nHour, sEndDate.nMinute, sEndDate.nSecond);

    // 检查按日期循环
    if(tp.m_nTimeMode == 1)
    {
        LOG(tp.m_strStartTime.data(), LV_INFO);
        LOG(tp.m_strEndTime.data(), LV_INFO);
        // 检查日期格式
        if(!IsDateFormat(tp.m_strStartDate.data()) || !IsDateFormat(tp.m_strEndDate.data()))
        {
            NOTIFY("startDate : %s, endDate : %s", tp.m_strStartDate.data(), tp.m_strEndDate.data());
            return EC_TC_DATETIME_ERROR;
        }

        sscanf(tp.m_strStartDate.data(), "%04d-%02d-%02d",&sStartDate.nYear, &sStartDate.nMonth, &sStartDate.nDay);
        sscanf(tp.m_strEndDate.data(), "%04d-%02d-%02d",&sEndDate.nYear, &sEndDate.nMonth, &sEndDate.nDay);

        CTime  dateToday(tNow.GetYear(), tNow.GetMonth(), tNow.GetDay(), 0, 0, 0);
        CTime  dateStart(sStartDate.nYear, sStartDate.nMonth, sStartDate.nDay, 0, 0, 0);
        CTime  dateEnd(sEndDate.nYear, sEndDate.nMonth, sEndDate.nDay, 0, 0, 0);

        // 判断开始日期与结束日期
        #if 0
        if(dateToday > dateStart || dateToday > dateEnd)
        {
            return EC_TC_DATE_EARLY_TODAY;
        }
        #endif

        if(dateStart > dateEnd)
        {
            return  EC_TC_SDATE_LATE_EDATE;
        }

        #if 0
        // 只有开始日期和今天一样，才判断开始时间是否早于现在时间（如果晚于今天）
        if(dateToday == dateStart && timeStart < tNow)
        {
            return     EC_TC_SDATE_EARLY_NOW;
        }
        #endif
    }

    // 开始时间不能晚于结束时间
    if(timeEnd <= timeStart && !tp.m_bPlayToEnd)
    {
        return EC_TC_STIME_LATE_ETIME;
    }

    if(tp.m_nDeviceType == TIMER_DEVICE_TYPE_DECODING_TERMINAL)
    {
        // 检查是否选择分区分组
        if(tp.m_vecSection.size() == 0 && tp.m_vecGroup.size() == 0)
        {
            return EC_TC_NO_SELECT_DEVICE;
        }
        // 检查歌曲
        if(tp.m_nSourceType == TIMER_SOURCE_TYPE_LOCAL_SONG && tp.m_vecSong.size() == 0)
        {
            return EC_TC_NO_SELECT_SONG;
        }
        else if(tp.m_nSourceType == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR && strlen(tp.m_stAudioCollector.mac) == 0)
        {
            return EC_TARGET_PARM_ERROR;
        }
    }
    else if(tp.m_nDeviceType == TIMER_DEVICE_TYPE_SEQUENCE_POWER)
    {
        // 检查是否选择电源时序器
        if(tp.m_vecSequencePower.size() == 0 || tp.m_vecSequencePowerChannels.size() == 0)
        {
            return EC_TC_NO_SELECT_DEVICE;
        }
    }

    return  EC_SUCCESS;
}

// 判断指定定时方案的定时点交叉
bool    ConfirmIntersect(uint uSchemeIndex, CTimePoint ct)
{
        bool    bValid = true;
        vector<IntersectScheme>  interSchemes;
        CTimePoint conflictFirstTimingPoint;
        uint count = g_Global.m_TimerScheme.GetIntersectSchemes(uSchemeIndex, ct, interSchemes, conflictFirstTimingPoint, TRUE);

        if (count > 0)
        {
            // 如果有冲突则把自己禁止
            bValid = FALSE;
        }

        return bValid;
}

#define MAX_TIMER_SONG_COUNT 100

void CWebDatas::HandleTimePoint(string strComID)
{
        if(!IsExistID(strComID))
        {
            return ;
        }
        LPCWebSection pWeb = g_Global.m_WebSections.GetWebSectionBySocketID(m_ComUser[strComID]);

        int nResult = CheckTimePoint(strComID);         // 检查定时点数据是否正确

        CWebData wd = m_IDData[strComID];
        CExTimePoint tp = wd.m_ExTimePoint;
        if(nResult == EC_SUCCESS)
        {
            CTimePoint  timerPoint;

            timerPoint.SetID(tp.m_nID + 1);  // 定时点类的ID是从1开始，但WEB端发过来的ID是从0开始，所以这里要+1
            timerPoint.SetName(CMyString(tp.m_strName));
            timerPoint.Enable(tp.m_bValid);
            timerPoint.SetTimeType((TIMER_TIME_TYPE)tp.m_nTimeMode);
            //timerPoint.SetIntercut(tp.m_bInterCut);
            //timerPoint.SetResumePlaying(tp.m_bResumePlay);
            //timerPoint.SetPlayToEnd(tp.m_bPlayToEnd);
            //timerPoint.SetSinglePlay(tp.m_bRandomSingle);
            timerPoint.SetFollowDevice(tp.m_bVolFollowDev);

            // 按星期循环模式，定时点选中的日期
            if(tp.m_nTimeMode == 0)
            {
                bool    selectDay[7] = {0};
                for(int i=0; i< tp.m_strWeekday.length(); i++)
                {
                        int nWeekDay = tp.m_strWeekday[i] - 48 - 1;
                        selectDay[nWeekDay] = true;
                }
                timerPoint.SetSelectedDays(selectDay);
            }
            // 按指定日期模式，开始日期和结束日期
            else                      //(tp.m_nTimeMode == 1)
            {
                TIMER_DATE     DateStart;
                TIMER_DATE     DateEnd;
                sscanf(tp.m_strStartDate.data(), "%04d-%02d-%02d",&DateStart.nYear, &DateStart.nMon, &DateStart.nDay);
                sscanf(tp.m_strEndDate.data(), "%04d-%02d-%02d",&DateEnd.nYear, &DateEnd.nMon, &DateEnd.nDay);

                timerPoint.SetDateStart(DateStart);
                timerPoint.SetDateEnd(DateEnd);
            }

            // 定时点开始时间和结束时间
            TIMER_TIME TimeStart;
            TIMER_TIME TimeEnd;
            sscanf(tp.m_strStartTime.data(), "%02d:%02d:%02d",&TimeStart.nHour, &TimeStart.nMin, &TimeStart.nSec);
            sscanf(tp.m_strEndTime.data(), "%02d:%02d:%02d",&TimeEnd.nHour, &TimeEnd.nMin, &TimeEnd.nSec);
            timerPoint.SetTimeStart(TimeStart);
            timerPoint.SetTimeEnd(TimeEnd);
            timerPoint.SetPlayMode(tp.m_nPlayMode);
            timerPoint.SetVolume(tp.m_nVolume);

            timerPoint.SetSourceType((TIMER_SOURCE_TYPE)tp.m_nSourceType);
            timerPoint.SetDeviceType((TIMER_DEVICE_TYPE)tp.m_nDeviceType);

            if(tp.m_nDeviceType == TIMER_DEVICE_TYPE_DECODING_TERMINAL)
            {
                // 设置分区
                timerPoint.ClearSections();
                for(int i=0; i<tp.m_vecSection.size(); i++)
                {
                    timerPoint.AddTimerSection(CMyString(tp.m_vecSection[i]));
                }
                // 设置分组
                timerPoint.ClearGroups();
                for(int i=0; i<tp.m_vecGroup.size(); i++)
                {
                    timerPoint.AddGroup(CMyString(tp.m_vecGroup[i]));
                }

                if(tp.m_nSourceType == TIMER_SOURCE_TYPE_LOCAL_SONG)
                {
                    //清空audioCollector
                    ST_TIMER_AUDIO_COLLECTOR_INFO st_audioCollctor;
                    memset(&st_audioCollctor,0,sizeof(st_audioCollctor));
                    timerPoint.SetAudioCollector(st_audioCollctor);

                    // 设置歌曲
                    timerPoint.RemoveAllSongs();
                    for(int i=0; i<tp.m_vecSong.size() && i<MAX_TIMER_SONG_COUNT; i++)
                    {
                        int nList = -1; int nSong = -1;
                        g_Global.m_PlayList.FindSongInPlayList(CMyString(tp.m_vecSong[i]), nList, nSong);
                        if(nList != -1 && nSong != -1)
                        {
                            CSong cs = g_Global.m_PlayList.GetListSong(nList, nSong);
                            timerPoint.AddSong(cs.GetPathName(), cs.GetDuration());
                        }
                    }
                }
                else if(tp.m_nSourceType == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
                {
                    // 设置定时点采集器
                    timerPoint.SetAudioCollector(tp.m_stAudioCollector);

                    //清空歌曲
                    timerPoint.RemoveAllSongs();
                }
            }
            else if(tp.m_nDeviceType == TIMER_DEVICE_TYPE_SEQUENCE_POWER)
            {
                // 设置时序器
                timerPoint.ClearSequencePowers();
                printf("tp.m_vecSequencePower.size=%d\n",tp.m_vecSequencePower.size());
                for(int i=0; i<tp.m_vecSequencePower.size(); i++)
                {
                    timerPoint.AddTimerSeqPwr(CMyString(tp.m_vecSequencePower[i]),tp.m_vecSequencePowerChannels[i]);
                }
            }

            // 判断定时点的交叉情况,暂时只考虑解码终端的情况，电源时序器不检测
            bool    bValid = timerPoint.IsValid();;
            if(tp.m_nDeviceType == TIMER_DEVICE_TYPE_DECODING_TERMINAL)
            {
                if(bValid)
                {
                    bValid = ConfirmIntersect(tp.GetSchID(), timerPoint);
                }
            }
            // 更新状态
            timerPoint.Enable(bValid);

            bool atTimeflag=FALSE;
            if(tp.m_nDeviceType == TIMER_DEVICE_TYPE_DECODING_TERMINAL)
            {
                /*************如果当前系统时间刚好处于定时时间内，且目前状态不是正在执行，那么将其设置为单次取消，即第二天才生效******/
                CTime tNow = CTime::GetCurrentTimeT();
                if(timerPoint.GetTimeType() == TT_WEEK_CYCLE)
                {
                    atTimeflag = CTimePoint::IsTimeInRange(timerPoint.GetTimeStart(), timerPoint.GetTimeEnd(), tNow);
                }
                else if(timerPoint.GetTimeType() == TT_SPECIFY_DATE)
                {
                    atTimeflag = (CTimePoint::IsDateInRange(timerPoint.GetDateStart(), timerPoint.GetDateEnd(), tNow) && CTimePoint::IsTimeInRange(timerPoint.GetTimeStart(), timerPoint.GetTimeEnd(), tNow));
                }
            }
            /**************************************************************************************/

            if(wd.m_Request == RT_ADD_TIMER)              // 添加定时点
            {
                //如果是添加定时点（设置定时点所属用户）
                LPCUserInfo pUser =  g_Global.m_Users.GetUserByID(pWeb->GetSocketID());
                timerPoint.SetAccount(pUser?pUser->GetAccount():SUPER_USER_NAME);
                
                /********************************/
                if(tp.m_nDeviceType == TIMER_DEVICE_TYPE_DECODING_TERMINAL)
                {
                    if(atTimeflag)
                    {
                        timerPoint.SetSingleCancel(true);
                        timerPoint.SetHasPlayed(true);  //不要让其自动开始
                    }
                }
                else
                {
                    //如果定时点的设备类型是电源时序器，那么让其继续执行，不设置单次取消
                }
                /********************************/

                // 存入内存
                g_Global.m_TimerScheme.AddTimer(tp.GetSchID(), timerPoint);
            }
            else if(wd.m_Request == RT_EDIT_TIMER)      // 编辑定时点
            {
                // 存入内存
                // 利用引用更新内存
                g_Global.m_TimerScheme.MutexLock();
                CTimePoint& Timer = g_Global.m_TimerScheme.GetTimer(tp.GetSchID(), tp.m_nID);
                timerPoint.SetAccount(Timer.GetAccount());  //记得要恢复原来的账户名，否则编辑后账户就会变成admin
                
                /*********************************/
                if(tp.m_nDeviceType == TIMER_DEVICE_TYPE_DECODING_TERMINAL)
                {
                    if(atTimeflag && timerPoint.GetPlayID() <= 0)    //如果处于定时时间内且处于非执行状态，那么就将其设置为单次取消，否则不处理
                    {
                        timerPoint.SetSingleCancel(true);
                        timerPoint.SetHasPlayed(true);  //不要让其自动开始
                    }
                }
                else
                {
                    //如果定时点的设备类型是电源时序器，那么让其继续执行，不设置单次取消
                }
                /********************************/

                Timer = timerPoint;
                g_Global.m_TimerScheme.MutexUnlock();
            }

            // 写入文件
            g_Global.WriteXmlFile(FILE_TIMER);

        }

        // 回复终端
        if(wd.m_Request == RT_ADD_TIMER)              // 添加定时点
        {
            g_Global.m_WebNetwork.m_WebSend.WebResponseAddTimePoint(tp.GetSchID()+1, strComID, tp, pWeb, nResult);
        }
        else if(wd.m_Request == RT_EDIT_TIMER)      // 编辑定时点
        {
            g_Global.m_WebNetwork.m_WebSend.WebResponseModifyTimePoint(tp.GetSchID()+1, strComID, tp, pWeb, nResult);
        }

}

// 检测消防采集器数据是否正确
int CWebDatas::CheckFireCollector(string strComID)
{
        CExtraFireCollector ExFireCollector = m_IDData[strComID].m_ExFireCollector;
        CSection* pSection = g_Global.m_FireCollectors.GetSectionByMac(ExFireCollector.m_strMac.data());
        if(pSection == NULL)
        {
            return EC_TARGET_NOTEXIST;
        }

        return EC_SUCCESS;
}


void CWebDatas::EditFireCollector(string strComID)
{
    if(!IsExistID(strComID))
    {
        return;
    }

    int nResult = CheckFireCollector(strComID);     // 检查数据

    CWebData wd = m_IDData[strComID];
    CExtraFireCollector ExFireCollector = wd.m_ExFireCollector;
    if(nResult == EC_SUCCESS)           // 处理编辑消防采集器，待处理
    {
        CSection* pFireCollectors = g_Global.m_FireCollectors.GetSectionByMac(ExFireCollector.m_strMac.data());

        std::shared_ptr<CFireCollector> pFireCollector = pFireCollectors->m_pFireCollector;
#if 0
        // 检查修改设备名称
        string strName = ExFireCollector.m_strName;
        if(strName.length() == 0 || strName.length() > SEC_NAME_LEN)             // 名称为空或过长
        {
            nResult = EC_TARGET_FORMAT_ERROR;
            goto END;
        }
        else
        {
            #if 0
                string gbkName = StringToGB2312(strName.data());
            #else
                string gbkName = strName;
            #endif
            if(gbkName != pFireCollectors->GetName())
            {
                g_Global.m_Network.m_CmdSend.CmdSetName(CMyString(gbkName), *pFireCollectors);
                pFireCollectors->SetName(gbkName.data());      // 内存中保存为GB2312数据
            }
        }
#endif
 printf("ExFireCollector.m_Channels_size=%lu\n",ExFireCollector.m_Channels.size());  //此处size异常，待分析解决2020.12.15
        map<int, CExtraChannel>::iterator iter = ExFireCollector.m_Channels.begin();
        for(; iter!=ExFireCollector.m_Channels.end(); iter++)
        {
            //2020.12.15 临时解决ExFireCollector.m_Channels.size()引起的问题，size应该恒为1才对。
            if(iter->second.m_strName.length() == 0)
                continue;
            int nChannelID = iter->first;
            CExtraChannel ExChannel = iter->second;

            if(nChannelID < 1 || nChannelID > 32)
            {
                return;
            }

            printf("nChannelID=%d,channelName=%s,TriggerMode=%d\n",nChannelID,ExChannel.m_strName.data(),ExChannel.m_nTriggerMode);

            // 根据消防通道ID修改数据
            CFireChannel& channel = pFireCollector->GetChannel(nChannelID-1);

            // 修改通道名称
            channel.SetName(ExChannel.m_strName.data());

            // 修改触发模式
            channel.SetTriggerMode(ExChannel.m_nTriggerMode);

            // 修改音效路径
            if(ExChannel.m_strSoundPathName == "/Data/Program/Other/")      // 过滤Web界面空路径信息
            {
                ExChannel.m_strSoundPathName = "";
            }
            channel.SetSoundPathName(CMyString(ExChannel.m_strSoundPathName.data()));

            // 修改触发分区
            channel.ClearSectoins();
            for(int j=0; j<ExChannel.m_SectionMac.size(); j++)
            {
                string strMac = ExChannel.m_SectionMac[j];
                LPCSection pSec = g_Global.m_Sections.GetSectionByMac(strMac.data());
                if(pSec != NULL)
                {
                    channel.AddSection(CMyString(pSec->GetMac()), CMyString(pSec->GetIP()));
                }
            }
        }

 

        // 写入xml文件
        bool IsSuccess = g_Global.WriteXmlFile(FILE_FIRE_COLLECTOR);
        pFireCollector->UpdateTriggerMode();
        if(IsSuccess)
        {
            #if 0
            for (int i=0; i<pFireCollector->GetChannelCount(); ++i)
            {
                CFireChannel& channel = pFireCollector->GetChannel(i);

                // 报警声设置
                if (channel.GetSoundPathName() != (""))
                {
                    int nList, nSong;
                    g_Global.m_PlayList.FindSongInPlayList(channel.GetSoundPathName(), nList, nSong);

                    // 从列表中查找得到
                    if (nList >= 0 && nSong >= 0)
                    {
                        for (int j=0; j<channel.GetSectionCount(); ++j)
                        {
                            CSection* pSection = g_Global.m_Sections.GetSectionByMac(channel.GetSectionMac(j));

                            if (pSection != NULL && pSection->IsOnline())
                            {
                                g_Global.m_Network.m_CmdSend.CmdSetAlarmSound(channel.GetID(), nList, nSong, *pSection);
                            }
                        }
                    }
                }
            }
            #endif
            g_Global.m_Network.m_CmdSend.CmdAlarmMode(*pFireCollectors, pFireCollector->GetChannelCount(), pFireCollector->GetTriggerMode());
        }
        else
        {
            nResult = EC_ERROR;
        }

        //清除通道map
        wd.m_ExFireCollector.m_Channels.clear();
    }

END:
    // 回复终端
    LPCWebSection pWeb = g_Global.m_WebSections.GetWebSectionBySocketID(m_ComUser[strComID]);
    g_Global.m_WebNetwork.m_WebSend.WebResponseSetFireCollector(pWeb, strComID, ExFireCollector.m_strMac, ExFireCollector.m_strName,
                                                                ExFireCollector.m_nChannelCount, nResult);
}

// 检测监控事件数据是否正确
int CWebDatas::CheckMonEvent(string strComID)
{
        CWebData cw = m_IDData[strComID];
        CExtraMonitorEvent exEvent = cw.m_ExMonEvent;

        // 检查事件类型
        if(exEvent.m_eventType < EVENT_MOVE_DETECT || exEvent.m_eventType > EVENT_GOODS_FLIT)
        {
            return EC_TARGET_NOTEXIST;
        }

        // 检查歌曲路径
        int nList = -1; int nSong = -1;
        g_Global.m_PlayList.FindSongInPlayList(CMyString(exEvent.m_SoundPath), nList, nSong);
        if(nList == -1 && nSong == -1)
        {
            return EC_SONG_FILE_NOTEXIST;
        }

        // 检查时间格式
        if(exEvent.m_nTimeType != 0 && exEvent.m_nTimeType != 1)
        {
            return EC_TARGET_PARM_ERROR;
        }

        // 检查按周循环
        if(exEvent.m_nTimeType == 0)
        {
            if(exEvent.m_strWeekday.empty() || exEvent.m_strWeekday.length() > 7)
            {
                return EC_TC_DATETIME_ERROR;
            }

            for(int i=0; i< exEvent.m_strWeekday.length(); i++)
            {
                // 检查周日期是否正确
                int nWeekDay = exEvent.m_strWeekday[i] - 48;
                if(nWeekDay < 1 || nWeekDay > 7)
                {
                    return EC_TC_DATETIME_ERROR;
                }
            }
        }

        // 检查日期时间格式
        if(!IsTimeFormat(exEvent.m_strStartTime.data()) || !IsTimeFormat(exEvent.m_strEndTime.data()))
        {
            return EC_TC_DATETIME_ERROR;
        }

        SYSTEMTIME_Q sStartDate, sEndDate;
        LOG(exEvent.m_strStartTime.data(), LV_INFO);
        LOG(exEvent.m_strEndTime.data(), LV_INFO);
        sscanf(exEvent.m_strStartTime.data(), "%02d:%02d:%02d",&sStartDate.nHour, &sStartDate.nMinute, &sStartDate.nSecond);
        sscanf(exEvent.m_strEndTime.data(), "%02d:%02d:%02d",&sEndDate.nHour, &sEndDate.nMinute, &sEndDate.nSecond);

        CTime   tNow = CTime::GetCurrentTimeT();
        CTime   timeStart(tNow.GetYear(), tNow.GetMonth(), tNow.GetDay(), sStartDate.nHour, sStartDate.nMinute, sStartDate.nSecond);
        CTime   timeEnd(tNow.GetYear(), tNow.GetMonth(), tNow.GetDay(), sEndDate.nHour, sEndDate.nMinute, sEndDate.nSecond);

        // 检查按日期循环
        if(exEvent.m_nTimeType == 1)
        {
            LOG(exEvent.m_strStartTime.data(), LV_INFO);
            LOG(exEvent.m_strEndTime.data(), LV_INFO);
            // 检查日期格式
            if(!IsDateFormat(exEvent.m_strStartDate.data()) || !IsDateFormat(exEvent.m_strEndDate.data()))
            {
                return EC_TC_DATETIME_ERROR;
            }

            sscanf(exEvent.m_strStartDate.data(), "%04d-%02d-%02d",&sStartDate.nYear, &sStartDate.nMonth, &sStartDate.nDay);
            sscanf(exEvent.m_strEndDate.data(), "%04d-%02d-%02d",&sEndDate.nYear, &sEndDate.nMonth, &sEndDate.nDay);

            //CTime  dateToday(tNow.GetYear(), tNow.GetMonth(), tNow.GetDay(), 0, 0, 0);
            CTime  dateStart(sStartDate.nYear, sStartDate.nMonth, sStartDate.nDay, 0, 0, 0);
            CTime  dateEnd(sEndDate.nYear, sEndDate.nMonth, sEndDate.nDay, 0, 0, 0);

            // 判断开始日期与结束日期
            /*if(dateToday > dateStart || dateToday > dateEnd)
            {
                return EC_TC_DATE_EARLY_TODAY;
            }*/
            if(dateStart > dateEnd)
            {
                return  EC_TC_SDATE_LATE_EDATE;
            }

            // 只有开始日期和今天一样，才判断开始时间是否早于现在时间（如果晚于今天）
            /*if(dateToday == dateStart && timeStart < tNow)
            {
                return     EC_TC_SDATE_EARLY_NOW;
            }*/
        }

        // 开始时间不能晚于结束时间
        //printf("PlayToEnd %d\n", tp.m_bPlayToEnd);
        if(timeEnd <= timeStart)
        {
            /*
            printf("timeStart : %d-%d-%d %d:%d:%d\n", timeStart.GetYear(), timeStart.GetMonth(), timeStart.GetDay(),
                                                                                        timeStart.GetHour(), timeStart.GetMinute(), timeStart.GetSecond());

            printf("timeEnd : %d-%d-%d %d:%d:%d\n", timeEnd.GetYear(), timeEnd.GetMonth(), timeEnd.GetDay(),
                                                                                        timeEnd.GetHour(), timeEnd.GetMinute(), timeEnd.GetSecond());

            printf("playToEnd : %d\n", tp.m_bPlayToEnd);
            */
            return EC_TC_STIME_LATE_ETIME;
        }

        return EC_SUCCESS;
}

void CWebDatas::EditMonEvent(string strComID)
{
        if(!IsExistID(strComID))
        {
            return;
        }

        int nResult = CheckMonEvent(strComID);     // 检查数据
        CWebData wd = m_IDData[strComID];
        CExtraMonitorEvent exEvent = wd.m_ExMonEvent;
        if(nResult == EC_SUCCESS)
        {
            LPCMonitorInfo pMonitor  = g_Global.m_Monitors.GetMonitorByMac(exEvent.m_strMonMac.data());
            if(pMonitor != NULL)
            {
                  CMonitorEvent& event = pMonitor->GetEvent(exEvent.m_eventType - 1);
                  event.Enable(exEvent.m_bValid);

                  event.SetTimeType((TIMER_TIME_TYPE)exEvent.m_nTimeType);
                  // 按星期循环模式，定时点选中的日期
                  if(exEvent.m_nTimeType == 0)
                  {
                      bool    selectDay[7] = {0};
                      for(int i=0; i< exEvent.m_strWeekday.length(); i++)
                      {
                              int nWeekDay = exEvent.m_strWeekday[i] - 48 - 1;
                              selectDay[nWeekDay] = true;
                      }
                      event.SetSelectedDays(selectDay);
                  }
                  else          // (tp.m_nTimeMode == 1)
                  {
                      // 按指定日期模式，开始日期和结束日期
                      TIMER_DATE     DateStart;
                      TIMER_DATE     DateEnd;
                      sscanf(exEvent.m_strStartDate.data(), "%04d-%02d-%02d",&DateStart.nYear, &DateStart.nMon, &DateStart.nDay);
                      sscanf(exEvent.m_strEndDate.data(), "%04d-%02d-%02d",&DateEnd.nYear, &DateEnd.nMon, &DateEnd.nDay);

                      event.SetDateStart(DateStart);
                      event.SetDateEnd(DateEnd);
                  }

                  // 开始时间和结束时间
                  TIMER_TIME TimeStart;
                  TIMER_TIME TimeEnd;
                  sscanf(exEvent.m_strStartTime.data(), "%02d:%02d:%02d",&TimeStart.nHour, &TimeStart.nMin, &TimeStart.nSec);
                  sscanf(exEvent.m_strEndTime.data(), "%02d:%02d:%02d",&TimeEnd.nHour, &TimeEnd.nMin, &TimeEnd.nSec);
                  event.SetTimeStart(TimeStart);
                  event.SetTimeEnd(TimeEnd);

                  // 设置音效路径
                  event.SetSoundPath(exEvent.m_SoundPath);
                  event.SetVolume(exEvent.m_nVolume);
                  NOTIFY("Volume : %d", exEvent.m_nVolume);

                  // 修改触发分区
                  event.ClearSections();
                  for(int i=0; i<exEvent.m_vecSecMac.size(); i++)
                  {
                          string strMac = exEvent.m_vecSecMac[i];
                          LPCSection pSec = g_Global.m_Sections.GetSectionByMac(strMac.data());
                          if(pSec != NULL)
                          {
                                  //LOG(pSec->GetMac(), LV_INFO);
                                  event.AddSecMac(pSec->GetMac());
                          }
                  }

                  // 写入xml文件
                  bool IsSuccess = g_Global.WriteXmlFile(FILE_MONITOR);
                  if(!IsSuccess)      nResult = EC_ERROR;
            }
            else
            {
                nResult = EC_TARGET_NOTEXIST;
            }
        }
        printf("nResult : %d\n", nResult);

        // 回复终端
        LPCWebSection pWeb = g_Global.m_WebSections.GetWebSectionBySocketID(m_ComUser[strComID]);
        if(pWeb != NULL)
        {
            string strBuf = CWebProtocol::CmdResponseSetMonitorEvent(strComID, exEvent.m_strMonMac, nResult);
            g_Global.m_WebNetwork.m_WebSend.ForwardDataToWeb(pWeb, strBuf, false, false);
        }
}











