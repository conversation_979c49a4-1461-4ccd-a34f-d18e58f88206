#ifndef EXTRAUSER_H
#define EXTRAUSER_H

#include <iostream>
#include <vector>
using namespace std;

class CExtraUser
{
public:
    CExtraUser();
    bool        IsDataFull();       // 数据是否接收完全

    bool        AddSection(vector<string>& vecSection, int nPage);
    //bool        AddGroup(vector<string>& vecGroup, int nPage);

    vector<string>&  GetSection()       { return m_vecSection; }
    //vector<string>&  GetGroup()         { return m_vecGroup;  }

    string      m_strParAccount;        // 父用户名称
    string      m_strAccount;           // 用户账户
    string      m_strPassword;          // 用户密码
    string      m_strUserName;          // 用户名称
    int         m_nLimitCode;           // 权限码
    int         m_nZoneCount;           // 分区数量
    int         m_nUserType;            // 账户类型
    int         m_nStorageCapacity;     // 存储容量
    //int           m_nGroupCount;      // 分组数量

private:
    bool        AddSecPage(int nPage);          // 添加收到分区页数
    //bool        AddGroupPage(int nPage);     // 添加收到分组页数

private:
    vector<string> m_vecSection;     // 分区容器
    //vector<string> m_vecGroup;       // 分组容器

    vector<int>      m_SectionPage;   // 收到的分区页数容器
    //vector<int>      m_GroupPage;    //  收到的分组页数容器
};

#endif // EXTRAUSER_H
