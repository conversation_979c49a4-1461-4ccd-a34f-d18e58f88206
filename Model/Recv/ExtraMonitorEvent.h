#ifndef EXTRAMONITOREVENT_H
#define EXTRAMONITOREVENT_H

#include "Network/Monitor/MonProc.h"

class CExtraMonitorEvent
{
public:
    CExtraMonitorEvent();
    ~CExtraMonitorEvent();
    bool        IsDataFull();

    bool        AddSection(vector<string>& vecSection, int nPage);

    bool                m_bValid;
    EventType           m_eventType;
    //char             m_Direction;
    int                   m_nVolume;
    string              m_SoundPath;
    int                   m_nTimeType;
    string              m_strWeekday;           // 从低位到高位，第1位为星期一，第2位为星期二...
    string              m_strStartDate;          // 开始日期，例如 2019-1-24
    string              m_strEndDate;            // 结束日期，例如 2019-1-25
    string              m_strStartTime;          // 开始时间，例如 20:10:30
    string              m_strEndTime;            // 结束时间，例如 20:20:30
    int                   m_nSectionCount;      // 待添加的分区数

    vector<string>    m_vecSecMac;

    string              m_strMonMac;            // 监控设备Mac

private:
    bool        AddSecPage(int nPage);          // 添加收到分区页数

    vector<int>      m_SectionPage;   // 收到的分区页数容器
};

#endif // EXTRAMONITOREVENT_H
