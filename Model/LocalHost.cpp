#include "LocalHost.h"



CHigherHost::CHigherHost()
{

}

CHigherHost::~CHigherHost()
{

}


/*******************************************************/


CLocalHost::CLocalHost()
{
    m_nID               = 0;
    m_nVolume			= 50;
    m_nProSource		= PRO_IDLE;
    m_nPreSource		= PRO_IDLE;
    m_nPlayStatus		= PS_STOP;
    m_isUpgrading		= FALSE;
    m_isSyncSong		= FALSE;
    m_nFinishedSong		= 0;
    m_nSyncSongCount	= 0;
    m_nPlayID			= -1;

    memset(m_szProName, 0,sizeof(m_szProName));

    m_strCurDateTime	= "";
    m_bConnectedGPS		= FALSE;
}


CLocalHost::~CLocalHost()
{

}


void CLocalHost::ResetData()
{
    m_nPlayID		= -1;
    m_nVolume		= 0;
    m_nProSource	= PRO_IDLE;
    m_nPreSource	= PRO_IDLE;
    m_nPlayStatus	= PS_STOP;
    m_isSyncSong	= FALSE;

    memset(m_szProName, 0,sizeof(m_szProName));
    ResetFileDateTime();
}


const char *CLocalHost::GetName(bool bToUTF8)
{
    if(bToUTF8)
    {
        return StringToUTF8(m_szSecName).data();
    }

    return m_szSecName;
}

void CLocalHost::SetMac(const char *lpszMac)
{
    if (lpszMac != NULL)
    {
        strcpy(m_netInfo.m_szMac, lpszMac);
    }
}

void CLocalHost::SetIP(const char *lpszIP)
{
    if (lpszIP != NULL)
    {
        strcpy(m_netInfo.m_szIP, lpszIP);
    }
}

void CLocalHost::SetProSource(ProgramSource src)
{
    m_nPreSource = m_nProSource;
    m_nProSource = src;

    if (src == PRO_OFFLINE)
    {
        m_nPreSource = PRO_OFFLINE;
    }
}

CMyString CLocalHost::GetProSourceName()
{
    return CProtocol::GetDescriptionProSource(m_nProSource);
}

void CLocalHost::SetProName(const char *lpszProName)
{
    if (strlen(lpszProName) > SRC_NAME_LEN)
    {
        strncpy(m_szProName, lpszProName, SRC_NAME_LEN);
        m_szProName[SRC_NAME_LEN] = '\0';
    }
    else
    {
        strcpy(m_szProName, lpszProName);
    }
}

void CLocalHost::SetName(const char *szName)
{
    if (strlen(szName) > SEC_NAME_LEN)
    {
        strncpy(m_szSecName, szName, SEC_NAME_LEN);
        m_szSecName[SEC_NAME_LEN] = '\0';
    }
    else
    {
        strcpy(m_szSecName, szName);
    }
}

bool CLocalHost::IsPagingIn()
{
    return (m_nProSource == PRO_PAGING);
}

bool CLocalHost::IsIdle()
{
    return (m_nProSource == PRO_IDLE || m_nProSource == PRO_ANALOG_INPUT);
}

CMyString CLocalHost::GetFileDateTime(DATETIME_FILE dt)
{
    return m_strFileDateTimes[dt];
}

void CLocalHost::SetFileDateTime(DATETIME_FILE dt, CMyString strDateTime)
{
    m_strFileDateTimes[dt]	= strDateTime;
    // ? 是否需要改为1
    m_uGetFileInfoCount[dt] = 0;
}

void CLocalHost::ResetFileDateTime()
{
    for (int i=0; i<DT_COUNT; ++i)
    {
        m_strFileDateTimes[i]	= ("");
        m_uGetFileInfoCount[i]	= 0;
    }
}

void CLocalHost::SetSyncSong(bool isSyncSong)
{
    m_isSyncSong = isSyncSong;

    // zhuyg
    if (isSyncSong == FALSE)
    {
        m_nFinishedSong		= 0;
        m_nSyncSongCount	= 0;
    }
}






















