#ifndef SELECTEDSECTIONS_H
#define SELECTEDSECTIONS_H

#include <iostream>
#include <vector>
#include "Tools/CMyString.h"

#define MAX_CONTROL_SECTION_COUNT	MAX_SECTION_COUNT_FORMAL

class CSelectedSections
{
public:
    CSelectedSections(void);
    ~CSelectedSections(void);

public:
    unsigned  char	GetID(void)					{	return m_nID;						}
    void	  SetID(unsigned char nID)			{	m_nID = nID;						}
    unsigned  char	GetPackCount(void)			{	return m_nPackCount;				}
    void	  SetPackCount(unsigned char count)	{	m_nPackCount = count;				}
    unsigned  char	GetPackID(void)				{	return m_nPackID;					}
    void	  SetPackID(unsigned char packID)	{	m_nPackID = packID;					}
    int		  GetCount(void)                    {	return m_vecMacs.size();			}
    void	  Clear(void)                       {	m_vecMacs.clear();					}
    bool	  HasRecvAll(void)                  {	return m_nPackID == m_nPackCount;	}
    vector<CMyString>& GetMacs(void)            {	return m_vecMacs;					}
    bool	  CanHandleCmd(unsigned char nID);
    CMyString GetAt(int index);
    bool	  Add(CMyString strMac);


private:
    unsigned char			m_nID;
    unsigned char			m_nPackCount;
    unsigned char			m_nPackID;
    vector<CMyString>       m_vecMacs;
};


#endif // SELECTEDSECTIONS_H
