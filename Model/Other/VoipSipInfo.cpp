#include "stdafx.h"
#include "VoipSipInfo.h"

//  把string状态信息转成int
int GetStatusNum(string strStatus)
{
    if(strStatus == "Idle")
    {
        return  VOIP_SIP_IDLE;          // 空闲
    }
    else if(strStatus == "InUse"  )
    {
        return VOIP_SIP_BUSY;           // 繁忙
    }

    else if(strStatus == "Hold Ringing" || strStatus == "Ringing")
    {
        return VOIP_SIP_RING;           // 响铃
    }

    else if(strStatus == "DAHDI")
    {
        return VOIP_SIP_DAHDI;
    }

    else if(strStatus == "Unavailable")
    {
        return VOIP_SIP_OFFLINE;           // 离线
    }

    else
    {
        return VOIP_SIP_ERROR;           // 未知错误
    }
}


SipInfo::SipInfo()
{

}

SipInfo::SipInfo(string exten, string status)
{
    m_strExten = exten;
    m_nExten = atoi(exten.data());
    m_strStatus = status;
    m_nStatus = GetStatusNum(status);
}



/*********************************/


CVoipSipInfo::CVoipSipInfo()
{

}


//  添加Sip账号与状态，如果是第一个包，则清空所有Sip信息
void CVoipSipInfo::AddSipInfo(int nCount,             // sip数量
                              SipInfo* sipInfos ,     // sip数组
                              bool isCrear)           // 是否清空
{
    if(isCrear)
        m_SipInfos.clear();

    for(int i=0; i<nCount; i++)
    {
        m_SipInfos[sipInfos[i].m_strExten] = sipInfos[i];
    }
}


SipInfo& CVoipSipInfo::GetSipInfo(int index)
{
    map<string, SipInfo>::iterator iter = m_SipInfos.begin();
    for(int i=0; i<index; i++)
    {
        iter++;
    }
    return iter->second;
}


LPSipInfo CVoipSipInfo::GetSipInfo(string strExten)
{
    if(m_SipInfos.count(strExten) > 0)
    {
            return &m_SipInfos[strExten];
    }

    return NULL;
}

int CVoipSipInfo::GetSipStatus(vector<string> &vecExten)
{
    int nSipStatus = EC_SUCCESS;
    for(int i=0; i<(int)vecExten.size(); i++)
    {
        string strExten = vecExten[i];
        if(m_SipInfos.count(strExten) > 0)
        {
            int nStatus = m_SipInfos[strExten].m_nStatus;             // Sip状态(int);
            switch(nStatus)
            {
            case 0:     nSipStatus = EC_SUCCESS;         break;
            case 1:
            case 2:     nSipStatus = EC_SIP_INUSE;       break;
            case 4:     nSipStatus = EC_SIP_OFFLINE;     break;
            default:    nSipStatus = EC_SIP_ERROR;       break;
            }
        }
        else
        {
            nSipStatus = EC_SIP_NOTEXIST;
        }

        if(nSipStatus != EC_SUCCESS)
        {
            break;
        }
    }

    return nSipStatus;
}

int CVoipSipInfo::GetSipStatus(string strExten)
{
    int nSipStatus = EC_SIP_ERROR;
    if(m_SipInfos.count(strExten) > 0)
    {
        int nStatus = m_SipInfos[strExten].m_nStatus;             // Sip状态(int);

        // voip协议的sip状态 转换成 web协议的sip状态
        switch(nStatus)
        {
            case VOIP_SIP_IDLE:
                nSipStatus = EC_SUCCESS;
            break;

            case VOIP_SIP_BUSY:
            case VOIP_SIP_RING:
                nSipStatus = EC_SIP_INUSE;

            break;
            //case 3:     nSipStatus = EC_SIP_ERROR;                           break;       DAHDI

            case VOIP_SIP_OFFLINE:
                nSipStatus = EC_SIP_OFFLINE;
            break;

            default:
                nSipStatus = EC_SIP_ERROR;
            break;
        }
    }

    return nSipStatus;
}


bool CVoipSipInfo::IsExistSip(string strExten)
{
    if(m_SipInfos.count(strExten) > 0)
    {
        return true;
    }

    return false;
}




