#include "stdafx.h"
#include "TimerScheme.h"
#include <string.h>
#include "math.h"

CTimePoint::CTimePoint(void)
{
    m_bPlayToEnd	= FALSE;
    m_bResumePlaying= FALSE;
    m_bSinglePlay	= FALSE;
    m_bIntercut     = FALSE;
    
    m_bValid		= TRUE;
    m_strName		= "";
    m_strUniqueID	= "";
    m_TimeType		= TT_WEEK_CYCLE;
    m_SourceType	= TIMER_SOURCE_TYPE_LOCAL_SONG;
    m_DeviceType	= TIMER_DEVICE_TYPE_DECODING_TERMINAL;
    m_nPlayMode		= PM_ORDER;
    m_nVolume		= 50;
    m_nPlayID		= -1;
    m_HasPlayed     = FALSE;
    m_bSingleCancel  = FALSE;

    memset(&m_DateStart	, 0,sizeof(m_DateStart));
    memset(&m_DateEnd	, 0,sizeof(m_DateEnd));
    memset(&m_TimeStart	,0, sizeof(m_TimeStart));
    memset(&m_TimeEnd	, 0,sizeof(m_TimeEnd));
    memset(m_SelectedDays,0, sizeof(bool)*DAYS_PER_WEEK);

    memset(m_szAccount,0,sizeof(m_szAccount));
}


CTimePoint::~CTimePoint(void)
{
    if(m_vecSecMac.size() > 0)
    {
        ClearSections();
    }

    if(m_vecSong.size() > 0)
    {
        RemoveAllSongs();
    }

    if(m_vecGroupID.size() > 0)
    {
        ClearGroups();
    }

    if(m_vecSeqPwrMac.size() > 0)
    {
        ClearSequencePowers();
    }

}


void CTimePoint::SetSelectedDays(bool* bSelDays)
{
    memcpy(m_SelectedDays, bSelDays, sizeof(bool)*DAYS_PER_WEEK);
}


void CTimePoint::ClearSections(void)
{
    m_vecSecMac.clear();

    if (m_vecSplitterStatus.size() > 0)
    {
        m_vecSplitterStatus.clear();
    }
}


void CTimePoint::ClearSequencePowers(void)
{
    m_vecSeqPwrMac.clear();
    m_vecSeqPwrChannels.clear();
}


void CTimePoint::ClearGroups(void)
{
    m_vecGroupID.clear();
}


void CTimePoint::AddSong(CMyString strPathName, int nDuration)
{
    CSong song(strPathName, nDuration);
    m_vecSong.push_back(song);
}


void CTimePoint::RemoveAllSongs(void)
{
    m_vecSong.clear();
}

void CTimePoint::AddTimerSection(CMyString strMac, int nSplitterStatus)
{
    m_vecSecMac.push_back(strMac);
    m_vecSplitterStatus.push_back(nSplitterStatus);
}


bool CTimePoint::RemoveTimerSection(CMyString strMac)
{
    bool flag = FALSE;

    vector<CMyString>::iterator iter1;
    vector<int>::iterator iter2;
    int i = 0;
    for(i=0, iter1=m_vecSecMac.begin(), iter2 = m_vecSplitterStatus.begin();
        (iter1!=m_vecSecMac.end() && iter2!=m_vecSplitterStatus.end());
        ++i, ++iter1, ++iter2)
    {
        if(strMac == m_vecSecMac[i])
        {
            m_vecSecMac.erase(iter1);
            m_vecSplitterStatus.erase(iter2);

            flag = TRUE;
            break;	// 务必加上break
        }
    }

    return flag;
}



bool CTimePoint::RemoveTimerSequencePower(CMyString strMac)
{
    bool flag = FALSE;

    vector<CMyString>::iterator iter1;
    vector<unsigned short>::iterator iter2;
    int i = 0;
    for(i=0, iter1=m_vecSeqPwrMac.begin(), iter2 = m_vecSeqPwrChannels.begin();
        (iter1!=m_vecSeqPwrMac.end() && iter2!=m_vecSeqPwrChannels.end());
        ++i, ++iter1, ++iter2)
    {
        if(strMac == m_vecSeqPwrMac[i])
        {
            m_vecSeqPwrMac.erase(iter1);
            m_vecSeqPwrChannels.erase(iter2);

            flag = TRUE;
            break;	// 务必加上break
        }
    }

    return flag;
}


void CTimePoint::AddTimerSeqPwr(CMyString strMac,unsigned short nChannels)
{
    m_vecSeqPwrMac.push_back(strMac);
    m_vecSeqPwrChannels.push_back(nChannels);
}


bool CTimePoint::RemoveTimerSeqPwr(CMyString strMac)
{
    bool flag = FALSE;

    vector<CMyString>::iterator iter1;
    vector<unsigned short>::iterator iter2;
    int i = 0;

    for(i=0, iter1=m_vecSeqPwrMac.begin(), iter2 = m_vecSeqPwrChannels.begin();
        (iter1!=m_vecSeqPwrMac.end() && iter2!=m_vecSeqPwrChannels.end());
        ++i, ++iter1, ++iter2)
    {
        if(strMac == m_vecSecMac[i])
        {
            m_vecSeqPwrMac.erase(iter1);
            m_vecSeqPwrChannels.erase(iter2);

            flag = TRUE;
            break;	// 务必加上break
        }
    }

    return flag;
}

bool CTimePoint::RemoveGroup(CMyString strGroupID)
{
    vector<CMyString>::iterator iter;
    int i = 0;

    for(i=0, iter=m_vecGroupID.begin(); iter!=m_vecGroupID.end(); ++i, ++iter)
    {
        if(strGroupID == m_vecGroupID[i])
        {
            m_vecGroupID.erase(iter);
            return TRUE;
        }
    }

    return FALSE;
}


// 是否选择该分区
bool CTimePoint::ContainZone(CMyString strMac)
{
    for (unsigned int i=0; i<m_vecSecMac.size(); ++i)
    {
        if (m_vecSecMac[i] == strMac)
        {
            return TRUE;
        }
    }

    // 定时点中的所有的分组
    for (unsigned int i=0; i<m_vecGroupID.size(); ++i)
    {
        int index = 0;
        CGroup* pGroup = g_Global.m_Groups.FindGroupByID(m_vecGroupID[i], index);

        if (pGroup != NULL)
        {
            // 轮询分组中的分区
            for (int j=0; j<pGroup->GetSecCount(); ++j)
            {
                if (pGroup->GetSecMac(j) == strMac)
                {
                    return TRUE;
                }
            }
        }
    }

    return FALSE;
}


// 是否有交叉
bool CTimePoint::HasIntersect(CTimePoint& timePoint)
{
    // 只有同级（同样是普通或者强插）的定时点，才去判断是否有交叉
    if (timePoint.GetIntercut() == GetIntercut())
    {
        return (HasIntersectZones(timePoint) && HasIntersectTime(timePoint));
    }
    // 否则直接返回FALSE
    else
    {
        return FALSE;
    }
}


// 分区是否有交叉
bool CTimePoint::HasIntersectZones(CTimePoint& timePoint)
{
    vector<CMyString> vecMac;
    int nSelSecCount = timePoint.GetSelectedSections(vecMac);

    for (int i=0; i<nSelSecCount; ++i)
    {
        if (ContainZone(vecMac[i]))
        {
            return TRUE;
        }
    }

    return FALSE;
}


// 时间是否有交叉
bool CTimePoint::HasIntersectTime(CTimePoint& timePoint)
{
    CTime time1Start(2010, 1, 1, m_TimeStart.nHour, m_TimeStart.nMin, m_TimeStart.nSec);
    CTime time1End(2010, 1, 1, m_TimeEnd.nHour, m_TimeEnd.nMin, m_TimeEnd.nSec);
    CTime time2Start(2010, 1, 1, timePoint.GetTimeStart().nHour, timePoint.GetTimeStart().nMin, timePoint.GetTimeStart().nSec);
    CTime time2End(2010, 1, 1, timePoint.GetTimeEnd().nHour, timePoint.GetTimeEnd().nMin, timePoint.GetTimeEnd().nSec);

    // 如果时间有交叉
    if ((time2Start <= time1Start && time1Start <= time2End)	// 定时点1的开始时间在定时点2的时间段内
        || (time1Start <= time2Start && time2Start <= time1End)) // 或者定时点2的开始时间在定时点1的时间段内
    {
        // 按周与按周
        if (m_TimeType == TT_WEEK_CYCLE && timePoint.GetTimeType() == TT_WEEK_CYCLE)
        {
            return CTimePoint::CompareWeekdays(GetSelectedDays(), timePoint.GetSelectedDays());
        }
        // 按周与按日期
        else if (m_TimeType == TT_WEEK_CYCLE && timePoint.GetTimeType() == TT_SPECIFY_DATE)
        {
            bool	weekDays[DAYS_PER_WEEK] = {0};

            // 日期转换成周
            timePoint.DateToWeekdays(weekDays);

            return CTimePoint::CompareWeekdays(GetSelectedDays(), weekDays);
        }
        // 按日期与按日期
        else if (m_TimeType == TT_SPECIFY_DATE && timePoint.GetTimeType() == TT_SPECIFY_DATE)
        {
            CTime date1Start(m_DateStart.nYear, m_DateStart.nMon, m_DateStart.nDay, 0, 0, 0);
            CTime date1End(m_DateEnd.nYear, m_DateEnd.nMon, m_DateEnd.nDay, 0, 0, 0);
            CTime date2Start(timePoint.GetDateStart().nYear, timePoint.GetDateStart().nMon, timePoint.GetDateStart().nDay, 0, 0, 0);
            CTime date2End(timePoint.GetDateEnd().nYear, timePoint.GetDateEnd().nMon, timePoint.GetDateEnd().nDay, 0, 0, 0);

            return ((date2Start <= date1Start && date1Start <= date2End)
                || (date2Start <= date1End && date1End <= date2End));
        }
        // 按日期与按周
        else if (m_TimeType == TT_SPECIFY_DATE && timePoint.GetTimeType() == TT_WEEK_CYCLE)
        {
            bool	weekDays[DAYS_PER_WEEK] = {0};

            // 日期转换成周
            DateToWeekdays(weekDays);

            return CTimePoint::CompareWeekdays(weekDays, timePoint.GetSelectedDays());
        }

        return TRUE;
    }

    return FALSE;
}


// 日期转换成周
void CTimePoint::DateToWeekdays(bool* pWeekdays)
{
    if (m_TimeType == TT_SPECIFY_DATE)
    {
        CTime dateStart(m_DateStart.nYear, m_DateStart.nMon, m_DateStart.nDay, 0, 0, 0);
        CTime dateEnd(m_DateEnd.nYear, m_DateEnd.nMon, m_DateEnd.nDay, 0, 0, 0);

        CTimeSpan dateSpan = dateEnd - dateStart;

        for (int i=0; i<=dateSpan.GetDays(); ++i)
        {
            CTimeSpan span(i, 0, 0, 0);
            CTime date =  dateStart + span;
            int	  weekday = date.GetDayOfWeek();

            // 周日(1)对应下标为6
            if (weekday == 1)
            {
                pWeekdays[6] = TRUE;
            }
            // 周一到周六(2-7)，对应下标为0-5
            else
            {
                pWeekdays[weekday-2] = TRUE;
            }
        }
    }
    else
    {
        memcpy(pWeekdays, m_SelectedDays, DAYS_PER_WEEK*sizeof(bool));
    }
}


// 对比周几有没有重叠
bool CTimePoint::CompareWeekdays(bool* pWeekdays1, bool* pWeekdays2)
{
    for (int i=0; i<DAYS_PER_WEEK; ++i)
    {
        // 如果有周几同时被选中
        if (pWeekdays1[i] && pWeekdays2[i])
        {
            return TRUE;
        }
    }

    return FALSE;
}


int CTimePoint::GetSelectedSections(vector<CMyString>& vecMac)
{
    vecMac.clear();

    for (unsigned int i=0; i<m_vecSecMac.size(); ++i)
    {
        vecMac.push_back(m_vecSecMac[i]);
    }

    // 定时点中的所有的分组
    for (unsigned int i=0; i<m_vecGroupID.size(); ++i)
    {
        int index = 0;
        CGroup* pGroup = g_Global.m_Groups.FindGroupByID(m_vecGroupID[i], index);

        if (pGroup != NULL)
        {
            // 轮询分组中的分区
            for (int j=0; j<pGroup->GetSecCount(); ++j)
            {
                bool bFind = FALSE;
                for (unsigned int k=0; k<vecMac.size(); k++)
                {
                    if (pGroup->GetSecMac(j) == vecMac[k])
                    {
                        bFind = TRUE;
                        break;
                    }
                }

                // 如果之前添加的分区中没有该分区，才会加入
                if (!bFind)
                {
                    vecMac.push_back(pGroup->GetSecMac(j));
                }
            }
        }
    }

    return vecMac.size();
}


int  CTimePoint::GetSectionIndexByMac(CMyString strMac)		// 得到指定mac地址的索引
{
    for (unsigned int i=0; i<m_vecSecMac.size(); ++i)
    {
        if (strMac == m_vecSecMac[i])
        {
            return i;
        }
    }

    return -1;
}

void  CTimePoint::SetAccount(LPCSTR szAccount)
{
    if( g_Global.m_Users.IsExistUser(szAccount) )
    {
        sprintf(m_szAccount,"%s",szAccount);
    }
    else
    {
        sprintf(m_szAccount,"%s",SUPER_USER_NAME);
    }
}



/*********************************************************/


// 判断日期是否在开始与结束日期之间
bool CTimePoint::IsDateInRange(TIMER_DATE& dateStart, TIMER_DATE& dateEnd, CTime& t)
{
    CTime tStart(dateStart.nYear, dateStart.nMon, dateStart.nDay, 0, 0, 0);
    CTime tEnd(dateEnd.nYear, dateEnd.nMon, dateEnd.nDay, 0, 0, 0);
    CTime tMid(t.GetYear(), t.GetMonth(), t.GetDay(), 0, 0, 0);

    return (tStart <= tMid && tMid <= tEnd);
}

// 判断时间是否在开始时间与结束时间之间
BOOL CTimePoint::IsTimeInRange(TIMER_TIME& timeStart, TIMER_TIME& timeEnd, CTime& t)
{
    CTime tStart(2020, 1, 1, timeStart.nHour, timeStart.nMin, timeStart.nSec);
    CTime tEnd(2020, 1, 1, timeEnd.nHour, timeEnd.nMin, timeEnd.nSec);
    CTime tMid(2020, 1, 1, t.GetHour(), t.GetMinute(), t.GetSecond());

    return (tStart <= tMid && tMid <= tEnd);
}


// 判断当前时间是否是启动定时点的有效时间（0~+1秒，极个别时候后一秒才有反应）
BOOL CTimePoint::IsCurValidStartTimer(TIMER_TIME& timeStart, TIMER_TIME& timeEnd, CTime& t)
{
    CTime tStart(2020, 1, 1, timeStart.nHour, timeStart.nMin, timeStart.nSec);
    CTime tEnd(2020, 1, 1, timeEnd.nHour, timeEnd.nMin, timeEnd.nSec);
    CTime tCur(2020, 1, 1, t.GetHour(), t.GetMinute(), t.GetSecond());

    return (tStart <= tCur && tCur <= tEnd && tCur-tStart <= 1);
}

// 判断时间是否等于准备时间
BOOL CTimePoint::IsTimeEqualReady(TIMER_TIME& timeStart,CTime& t,int secTimeout)
{
    CTime tStart(2020, 1, 1, timeStart.nHour, timeStart.nMin, timeStart.nSec);
    CTime tCur(2020, 1, 1, t.GetHour(), t.GetMinute(), t.GetSecond());

    return ( tStart - tCur == secTimeout );
}

// 判断时间是否处于准备时间内
BOOL CTimePoint::IsTimeInReadyRange(TIMER_TIME& timeStart,CTime& t,int secTimeout)
{
    CTime tStart(2020, 1, 1, timeStart.nHour, timeStart.nMin, timeStart.nSec);
    CTime tCur(2020, 1, 1, t.GetHour(), t.GetMinute(), t.GetSecond());

    return ( (tStart - tCur >=0) && (tStart - tCur<=secTimeout) );
}

// 是否开始执行
bool  CTimePoint::IsToStartWorking(CTime& t)
{
    // 如果无效或者正在执行，则返回FALSE
    if (!IsValid() || m_nPlayID > 0)
    {
        return FALSE;
    }

    bool flag = FALSE;

    /*
    if (m_TimeType == TT_WEEK_CYCLE)
    {
        // 星期日
        if (t.GetDayOfWeek() == 1)
        {
            if (!m_SelectedDays[6]) // 未选中，则返回FALSE
            {
                return FALSE;
            }
        }
        // 星期一到星期六
        else
        {
            if (!m_SelectedDays[t.GetDayOfWeek()-2])// 未选中，则返回FALSE
            {
                return FALSE;
            }
        }

        flag = (t.GetHour() == m_TimeStart.nHour
             && t.GetMinute() == m_TimeStart.nMin
             && t.GetSecond() == m_TimeStart.nSec);
    }
    else if (m_TimeType == TT_SPECIFY_DATE)
    {
        flag = (IsDateInRange(m_DateStart, m_DateEnd, t)
             && t.GetHour() == m_TimeStart.nHour
             && t.GetMinute() == m_TimeStart.nMin
             && t.GetSecond() == m_TimeStart.nSec);
    }

    return flag;
    */

    if (m_TimeType == TT_WEEK_CYCLE)
    {
        // 星期日
        if (t.GetDayOfWeek() == 1)
        {
            if (!m_SelectedDays[6]) // 未选中，则返回FALSE
            {
                return FALSE;
            }
        }
        // 星期一到星期六
        else
        {
            if (!m_SelectedDays[t.GetDayOfWeek()-2])// 未选中，则返回FALSE
            {
                return FALSE;
            }
        }

        // 播放过
        if (m_HasPlayed)
        {
            #if 0
            flag = (t.GetHour() == m_TimeStart.nHour
                && t.GetMinute() == m_TimeStart.nMin
                && t.GetSecond() == m_TimeStart.nSec);
            #endif
            flag = IsCurValidStartTimer(m_TimeStart, m_TimeEnd, t);
        }
        // 没播放过（重启后，判断范围即可）
        else
        {
            flag = IsTimeInRange(m_TimeStart, m_TimeEnd, t);
        }
    }
    else if (m_TimeType == TT_SPECIFY_DATE)
    {
        // 播放过
        if (m_HasPlayed)
        {
            #if 0
            flag = (IsDateInRange(m_DateStart, m_DateEnd, t)
                    && t.GetHour() == m_TimeStart.nHour
                    && t.GetMinute() == m_TimeStart.nMin
                    && t.GetSecond() == m_TimeStart.nSec);
            #endif
            flag = (IsDateInRange(m_DateStart, m_DateEnd, t) && IsCurValidStartTimer(m_TimeStart, m_TimeEnd, t));
        }
        // 没播放过（重启后，判断范围即可）
        else
        {
            flag = (IsDateInRange(m_DateStart, m_DateEnd, t) && IsTimeInRange(m_TimeStart, m_TimeEnd, t));
        }
    }

    if (flag)
    {
        m_HasPlayed = TRUE;

        CMyString strLog;
        strLog.Format("has section online %s", g_Global.m_Sections.HasSectionOnline() ?  "true" : "false" );
        g_Global.m_Network.AddLog(strLog);
    }

    return flag;
}



// 是否准备开始执行(解码终端)
bool  CTimePoint::IsSectionToReadyWorking(CTime& t)
{
    // 如果无效或者正在执行，则返回FALSE
    if (!IsValid() || m_nPlayID > 0)
    {
        return FALSE;
    }

    bool flag = FALSE;

    if (m_TimeType == TT_WEEK_CYCLE)
    {
        // 星期日
        if (t.GetDayOfWeek() == 1)
        {
            if (!m_SelectedDays[6]) // 未选中，则返回FALSE
            {
                return FALSE;
            }
        }
        // 星期一到星期六
        else
        {
            if (!m_SelectedDays[t.GetDayOfWeek()-2])// 未选中，则返回FALSE
            {
                return FALSE;
            }
        }

        flag = IsTimeEqualReady(m_TimeStart, t,10);
    }
    else if (m_TimeType == TT_SPECIFY_DATE)
    {

            flag = ( IsDateInRange(m_DateStart, m_DateEnd, t) && IsTimeEqualReady(m_TimeStart, t,10) );
    }

    return flag;
}



// 是否准备开始执行（电源时序器）
bool  CTimePoint::IsSequencePowerWorking(CTime& t)
{
    // 如果无效，则返回FALSE
    if (!IsValid())
    {
        return FALSE;
    }

    bool flag = FALSE;

    if (m_TimeType == TT_WEEK_CYCLE)
    {
        // 星期日
        if (t.GetDayOfWeek() == 1)
        {
            if (!m_SelectedDays[6]) // 未选中，则返回FALSE
            {
                return FALSE;
            }
        }
        // 星期一到星期六
        else
        {
            if (!m_SelectedDays[t.GetDayOfWeek()-2])// 未选中，则返回FALSE
            {
                return FALSE;
            }
        }

        flag = (IsTimeInReadyRange(m_TimeStart, t,15) || IsTimeInRange(m_TimeStart, m_TimeEnd, t));
    }
    else if (m_TimeType == TT_SPECIFY_DATE)
    {
        flag = ( IsDateInRange(m_DateStart, m_DateEnd, t) && ( IsTimeInReadyRange(m_TimeStart, t,15) || IsTimeInRange(m_TimeStart, m_TimeEnd, t)) );
    }

    return flag;
}


// 是否停止执行
bool   CTimePoint::IsToStopWorking(CTime& t)
{
    // 如果没有执行，或者播放到歌曲结束
    if ( GetSourceType() == TIMER_SOURCE_TYPE_LOCAL_SONG && ( m_nPlayID <= 0 || m_bPlayToEnd) )
    {
        return FALSE;
    }

    bool flag = FALSE;

    /*
    if (m_TimeType == TT_WEEK_CYCLE)
    {
        // 星期日
        if (t.GetDayOfWeek() == 1)
        {
            if (!m_SelectedDays[6]) // 未选中，则返回FALSE
            {
                return FALSE;
            }
        }
        // 星期一到星期六
        else
        {
            if (!m_SelectedDays[t.GetDayOfWeek()-2])// 未选中，则返回FALSE
            {
                return FALSE;
            }
        }

        flag = (t.GetHour() == m_TimeEnd.nHour
             && t.GetMinute() == m_TimeEnd.nMin
             && t.GetSecond() == m_TimeEnd.nSec);
    }
    else if (m_TimeType == TT_SPECIFY_DATE)
    {
        flag = (IsDateInRange(m_DateStart, m_DateEnd, t)
             && t.GetHour() == m_TimeEnd.nHour
             && t.GetMinute() == m_TimeEnd.nMin
             && t.GetSecond() == m_TimeEnd.nSec);
    }

    return flag;
    */

    if (m_TimeType == TT_WEEK_CYCLE)
    {
        // 星期日
        if (t.GetDayOfWeek() == 1)
        {
            if (!m_SelectedDays[6]) // 未选中，则返回FALSE
            {
                return FALSE;
            }
        }
        // 星期一到星期六
        else
        {
            if (!m_SelectedDays[t.GetDayOfWeek()-2])// 未选中，则返回FALSE
            {
                return FALSE;
            }
        }

        flag = ((t.GetHour() == m_TimeEnd.nHour
            && t.GetMinute() == m_TimeEnd.nMin
            && t.GetSecond() == m_TimeEnd.nSec)
            ||  ( GetSourceType() == TIMER_SOURCE_TYPE_LOCAL_SONG && !IsTimeInRange(m_TimeStart, m_TimeEnd, t)) ); // 加上IsTimeInRange判断是预防校正时间不在范围
    }
    else if (m_TimeType == TT_SPECIFY_DATE)
    {
        flag = (IsDateInRange(m_DateStart, m_DateEnd, t)
            && ((t.GetHour() == m_TimeEnd.nHour
            && t.GetMinute() == m_TimeEnd.nMin
            && t.GetSecond() == m_TimeEnd.nSec)
            || ( GetSourceType() == TIMER_SOURCE_TYPE_LOCAL_SONG && !IsTimeInRange(m_TimeStart, m_TimeEnd, t)) ) );// 加上IsTimeInRange判断是预防校正时间不在范围
    }

    return flag;
}

// 定时点里在线的分区个数
int CTimePoint::GetOnlineSectionCount(void)
{
    int nSecCount = m_vecSecMac.size();
    int nOnlineCount = 0;

    for (int i=0; i<nSecCount; ++i)
    {
        LPCSection pSection = g_Global.m_Sections.GetSectionByMac(m_vecSecMac[i]);

        if (pSection != NULL && pSection->IsOnline())
        {
            nOnlineCount++;
        }
    }

    return nOnlineCount;
}



int   CTimePoint::GetStartSong()
{
    int	nSongCount	= GetSongCount();

    if (nSongCount <= 0)
    {
        return -1;
    }

    // 随机模式
    if (m_nPlayMode == PM_RANDOM)
    {
        //srand((unsigned int)time(NULL));
        struct timespec spec;
        clock_gettime(CLOCK_MONOTONIC, &spec);
        unsigned long long timeTv = (unsigned long long)spec.tv_sec * 1000000 + (unsigned long long)spec.tv_nsec / 1000;
        srand((unsigned int)(timeTv & 0xFFFFFFFF));  // 使用低32位作为种子

        return  (rand()%nSongCount);
    }
    // 其它模式从第一首歌曲开始
    else
    {
        return 0;
    }
}

int  CTimePoint::GetNextSong(int nSong, bool bGetStartSong)
{
    if (nSong < 0 || nSong >= GetSongCount())
    {
        return -1;
    }

    int nNextSong	= nSong;
    int	nSongCount	= GetSongCount();

    // 顺序和列表循环
    if (m_nPlayMode == PM_ORDER || m_nPlayMode == PM_LIST_CYCLE)
    {
        ++nNextSong;

        if (nNextSong >= nSongCount)
        {
            nNextSong = (m_nPlayMode == PM_ORDER ? -1 : 0);
        }
    }
    // 随机播放
    else if (m_nPlayMode == PM_RANDOM)
    {
        //  如果是获取起始歌曲，或者不是随机单曲播放
        if (bGetStartSong || !m_bSinglePlay)
        {   
            //time(NULL)是秒为单位，但时间内不会变
            //srand((unsigned int)time(NULL));
            struct timespec spec;
            clock_gettime(CLOCK_MONOTONIC, &spec);
            unsigned long long timeTv = (unsigned long long)spec.tv_sec * 1000000 + (unsigned long long)spec.tv_nsec / 1000;
            srand((unsigned int)(timeTv & 0xFFFFFFFF));  // 使用低32位作为种子

            int nReadyNextSong = nNextSong;
            int nMaxTryCount=10;
            int nTryCount=0;
            while(nMaxTryCount-- && nTryCount++<nSongCount) {
                nReadyNextSong = rand() % nSongCount;
                if(nNextSong!=nReadyNextSong)
                {
                    break;
                }
            }
            nNextSong = nReadyNextSong;
        }
        // 否则结束
        else
        {
            nNextSong = -1;
        }
    }
    // 单曲循环
    else if (m_nPlayMode == PM_SINGLE_CYCLE)
    {
        nNextSong = nSong;
    }
    // 单曲播放
    else if (m_nPlayMode == PM_SINGLE)
    {
        nNextSong = -1;
    }

    // 如果找到下一首歌曲
    if (nNextSong >= 0)
    {
        // 则需要检查

    }


    return nNextSong;
}

int CTimePoint::GetNextSong(const char *szSongUrl)
{
    int nSongCount = GetSongCount();
    int nNextSong = -1;

    for(int i=0; i<nSongCount; i++)
    {
        CSong song = GetSong(i);
        if(song.GetPathName() == szSongUrl)
        {
            // 顺序和列表循环
            if (m_nPlayMode == PM_ORDER || m_nPlayMode == PM_LIST_CYCLE)
            {
                ++nNextSong;

                if (nNextSong >= nSongCount)
                {
                    nNextSong = (m_nPlayMode == PM_ORDER ? -1 : 0);
                }
            }
            // 随机播放
            else if (m_nPlayMode == PM_RANDOM)
            {
                //  如果是获取起始歌曲，或者不是随机单曲播放
                if (!m_bSinglePlay)
                {
                    //srand((unsigned int)time(NULL));
                    struct timespec spec;
                    clock_gettime(CLOCK_MONOTONIC, &spec);
                    unsigned long long timeTv = (unsigned long long)spec.tv_sec * 1000000 + (unsigned long long)spec.tv_nsec / 1000;
                    srand((unsigned int)(timeTv & 0xFFFFFFFF));  // 使用低32位作为种子
                    
                    nNextSong = rand()%nSongCount;
                }
                // 否则结束
                else
                {
                    nNextSong = -1;
                }
            }
            // 单曲循环
            else if (m_nPlayMode == PM_SINGLE_CYCLE)
            {
                nNextSong = i;
            }
            // 单曲播放
            else if (m_nPlayMode == PM_SINGLE)
            {
                nNextSong = -1;
            }

            break;
        }
    }

    return nNextSong;
}


/**************************************************************/


CScheme::CScheme(void)
{
    m_strSchemeName	= ("");
    m_strUniqueID	= ("");
    #if SUPPORT_TIMER_SCHEME_AUTO_SWITCH
    m_nAutoSwitchType = SCH_AUTO_SWITCH_NOT_SET;
    memset(&m_DateStart,0,sizeof(m_DateStart));
    memset(&m_DateEnd,0,sizeof(m_DateEnd));
    #endif
}

CScheme::~CScheme(void)
{
    if(m_TimePoints.size() > 0)
    {
        ClearTimers();
    }
}

bool CScheme::AddTimer(CTimePoint& TimePoint)
{
    //20240220,将此处最大定时点数目固定为正式版的数目，只在WEB添加定时点时才判断；
    //因为之前版本不限制试用版的定时点数目，现在需要限制，为了不覆盖用户原有的定时点，此处不进行判断。
    int nMaxTimerCount = MAX_TIMER_COUNT_FORMAL;

    if (GetTimerCount() >= nMaxTimerCount)
    {
        return FALSE;
    }

    TimePoint.SetID(m_TimePoints.size() + 1);
    m_TimePoints.push_back(TimePoint);

    return TRUE;
}


void CScheme::RemoveTimer(int nTimer)
{
    vector<CTimePoint>::iterator iter;
    int i = 0;
    for(i=0, iter=m_TimePoints.begin(); iter!=m_TimePoints.end(); ++i, ++iter)
    {
        if(nTimer == i)
        {
            m_TimePoints.erase(iter);
            break;	// 务必加上break
        }
    }

    DeassignTimersID();
}


void CScheme::ClearTimers(void)
{
    m_TimePoints.clear();
}


int CScheme::GetValidTimerCount(void)
{
    int nTimerCount = GetTimerCount();
    int nValidCount = 0;

    for (int i=0; i<nTimerCount; ++i)
    {
        if (GetTimer(i).IsValid())
        {
            nValidCount++;
        }
    }

    return nValidCount;
}


CMyString CScheme::GetValidName(void)
{
    CMyString strName;
    strName.Format((char*)("%s(%d/%d)"), GetName().Data(), GetValidTimerCount(), GetTimerCount());

    return strName;
}


unsigned int CScheme::GetIntersectTimers(CTimePoint& timePoint, vector<CTimePoint*>& pTimePoints, bool bAllTimers)
{
    pTimePoints.clear();

    for (int i=0; i<GetTimerCount(); ++i)
    {
        CTimePoint& tp = GetTimer(i);

        // 如果是定时点本身
        //if (&tp == &timePoint)
        if (tp.GetID() == timePoint.GetID())
        {
            // 如果只是对比该定时点前面的定时点，则直接返回
            if (!bAllTimers)
            {
                break;
            }
        }
        else if (tp.IsValid() && timePoint.HasIntersect(tp))
        {
            pTimePoints.push_back(&tp);
        }
    }

    return pTimePoints.size();
}


void CScheme::SortByName(bool bAscend)
{
    int nTimerCount = GetTimerCount();

    for (int i=0; i<nTimerCount; ++i)
    {
        for (int j=i; j<nTimerCount; ++j)
        {
            bool bExchange = (bAscend ? (m_TimePoints[i].GetName() > m_TimePoints[j].GetName()) :
                                        (m_TimePoints[i].GetName() < m_TimePoints[j].GetName()));

            if (bExchange)
            {
                CTimePoint tp	= m_TimePoints[j];
                m_TimePoints[j] = m_TimePoints[i];
                m_TimePoints[i] = tp;
            }
        }
    }

    DeassignTimersID();
}


// 按定时点状态排序
void CScheme::SortByStatus(bool bAscend)
{
    int nTimerCount = GetTimerCount();

    list<CTimePoint> listTimer;
    list<CTimePoint>::iterator iter;

    for (int i=0; i<nTimerCount; ++i)
    {
        for(iter=listTimer.begin();iter!=listTimer.end();++iter)
        {
            bool bEqual = (m_TimePoints[i].IsValid() == iter->IsValid());

            if (bEqual)
            {
                listTimer.insert(iter, m_TimePoints[i]);
                break;
            }
        }

        // 如果一直找不到相同的
        if (iter == listTimer.end())
        {
            // 如果是升序，则无效的时间点放后面
            bool bPushBack = (bAscend && !m_TimePoints[i].IsValid());

            if (bPushBack)
            {
                listTimer.push_back(m_TimePoints[i]);
            }
            else
            {
                listTimer.push_front(m_TimePoints[i]);
            }
        }
    }

    // 清除掉原来的，然后加载排序好的
    m_TimePoints.clear();
    for(iter=listTimer.begin();iter!=listTimer.end();++iter)
    {
        m_TimePoints.push_back(*iter);
    }

    DeassignTimersID();
}


// 按定时点周期排序
void CScheme::SortByCycle(bool bAscend)
{
    int nTimerCount = GetTimerCount();

    list<CTimePoint> listTimer;
    list<CTimePoint>::iterator iter;

    for (int i=0; i<nTimerCount; ++i)
    {
        for(iter=listTimer.begin();iter!=listTimer.end();++iter)
        {
            bool bEqual = (m_TimePoints[i].GetTimeType() == iter->GetTimeType());

            if (bEqual)
            {
                listTimer.insert(iter, m_TimePoints[i]);
                break;
            }
        }

        // 如果一直找不到相同的
        if (iter == listTimer.end())
        {
            // 如果是升序，则有效的时间点放后面
            bool bPushBack = (bAscend && m_TimePoints[i].GetTimeType() == TT_WEEK_CYCLE);

            if (bPushBack)
            {
                listTimer.push_back(m_TimePoints[i]);
            }
            else
            {
                listTimer.push_front(m_TimePoints[i]);
            }
        }
    }

    // 清除掉原来的，然后加载排好按周与按日期的
    m_TimePoints.clear();
    for(iter=listTimer.begin();iter!=listTimer.end();++iter)
    {
        m_TimePoints.push_back(*iter);
    }

    // 找到指定日期的开始与结束位置
    int nStart = -1, nEnd = -1;
    for (int i=0; i<nTimerCount; ++i)
    {
        if (m_TimePoints[i].GetTimeType() == TT_SPECIFY_DATE)
        {
            if (nStart < 0)
            {
                nStart = i;
                nEnd = nStart;
            }
            else
            {
                nEnd++;
            }
        }
    }

    if (nStart >= 0)
    {
        // 然后进行排序
        for (int i=nStart; i<=nEnd; ++i)
        {
            CTime dateStart1(	m_TimePoints[i].GetDateStart().nYear,
                m_TimePoints[i].GetDateStart().nMon,
                m_TimePoints[i].GetDateStart().nDay,
                0, 0, 0);

            for (int j=i; j<=nEnd; ++j)
            {
                CTime dateStart2(	m_TimePoints[j].GetDateStart().nYear,
                    m_TimePoints[j].GetDateStart().nMon,
                    m_TimePoints[j].GetDateStart().nDay,
                    0, 0, 0);

                if (dateStart1 > dateStart2)
                {
                    CTimePoint tp	= m_TimePoints[j];
                    m_TimePoints[j] = m_TimePoints[i];
                    m_TimePoints[i] = tp;
                }
            }
        }
    }

    // 找到周几的开始与结束位置
    nStart = -1, nEnd = -1;
    for (int i=0; i<nTimerCount; ++i)
    {
        if (m_TimePoints[i].GetTimeType() == TT_WEEK_CYCLE)
        {
            if (nStart < 0)
            {
                nStart = i;
                nEnd = nStart;
            }
            else
            {
                nEnd++;
            }
        }
    }

    if (nStart >= 0)
    {
        // 然后进行排序
        for (int i=nStart; i<=nEnd; ++i)
        {
            for (int j=i; j<=nEnd; ++j)
            {
                if (CompareWeekdays(m_TimePoints[i], m_TimePoints[j]) > 0)
                {
                    CTimePoint tp	= m_TimePoints[j];
                    m_TimePoints[j] = m_TimePoints[i];
                    m_TimePoints[i] = tp;
                }
            }
        }
    }

    DeassignTimersID();
}


// 按定时点开始时间排序
void CScheme::SortByStartTime(bool bAscend)
{
    int nTimerCount = GetTimerCount();

    for (int i=0; i<nTimerCount-1; ++i)
    {
        for (int j=0; j<nTimerCount-i-1; ++j)
        {
            CTime tStart1(2000, 1, 1,
                            m_TimePoints[j].GetTimeStart().nHour,
                            m_TimePoints[j].GetTimeStart().nMin,
                            m_TimePoints[j].GetTimeStart().nSec);

            CTime tStart2(2000, 1, 1,
                            m_TimePoints[j+1].GetTimeStart().nHour,
                            m_TimePoints[j+1].GetTimeStart().nMin,
                            m_TimePoints[j+1].GetTimeStart().nSec);

            BOOL bExchange = (bAscend ? (tStart1 > tStart2) : (tStart1 < tStart2));

            if (bExchange)
            {
                CTimePoint tp	= m_TimePoints[j];
                m_TimePoints[j] = m_TimePoints[j+1];
                m_TimePoints[j+1] = tp;
            }
        }
    }

    DeassignTimersID();
}


// 比较周几
int CScheme::CompareWeekdays(CTimePoint& tp1, CTimePoint& tp2)
{
    bool* pWeekdays1 = tp1.GetSelectedDays();
    bool* pWeekdays2 = tp2.GetSelectedDays();
    int	  result = 0;

    for (int i=0; i<DAYS_PER_WEEK; ++i)
    {
        if (pWeekdays1[i] != pWeekdays2[i])
        {
            return (pWeekdays1[i] ? -1 : 1);
        }
    }

    return result;
}


// 重新赋值定时点的ID
void CScheme::DeassignTimersID(void)
{
    int nTimerCount = GetTimerCount();

    for (int i=0; i<nTimerCount; ++i)
    {
        GetTimer(i).SetID(i+1);
    }
}


// 禁止所有的定时点
void CScheme::DisableAllTimers(void)
{
    int nTimerCount = GetTimerCount();

    for (int i=0; i<nTimerCount; ++i)
    {
        GetTimer(i).Enable(FALSE);
    }
}

void CScheme::AbleAllTimers()
{
    int nTimerCount = GetTimerCount();

    for (int i=0; i<nTimerCount; ++i)
    {
        bool    bValid = TRUE;
        CTimePoint& ct = GetTimer(i);
        ct.Enable(bValid);

        //IntersectScheme interScheme;
        vector<CTimePoint*> pTimePoints;
        //vector<IntersectScheme> interSchemes;

        if (int nCount = GetIntersectTimers(ct, pTimePoints, true) > 0)
        {
            //interScheme.nScheme = i;
            // 如果有冲突则把自己禁止
            bValid = FALSE;
        }

        ct.Enable(bValid);
    }
}


/***********************************************************/

CTimerScheme::CTimerScheme(void)
{
    m_nCurScheme		= 0;
    m_strDateTime		= ("");
    m_bAllSchemesValid	= FALSE;
    m_bTimePointsResumePlaying	= TRUE;

    m_TimerMutex = PTHREAD_MUTEX_INITIALIZER;
}

CTimerScheme::~CTimerScheme(void)
{
    if(m_Schemes.size() > 0)
    {
        ClearSchemes();
    }
    //20220427 静态初始化的互斥锁不需要,也不能用pthread_mutex_destroy销毁锁，否则将出错
    //pthread_mutex_destroy(&m_TimerMutex);
}


void CTimerScheme::ClearSchemes(void)
{
    MutexLock();
    m_Schemes.clear();
    MutexUnlock();
}

void CTimerScheme::AddScheme(CMyString strName)
{
    MutexLock();
    CScheme   	Scheme;
    Scheme.SetName(strName);
    m_Schemes.push_back(Scheme);
    MutexUnlock();
}

void CTimerScheme::AddScheme(string strName, int &nIndex)
{
    MutexLock();
    CScheme   	Scheme;
    Scheme.SetName(strName);
    m_Schemes.push_back(Scheme);
    nIndex = GetSchemeCount();
    MutexUnlock();
}

void CTimerScheme::RemoveScheme(int nScheme)
{
    MutexLock();
    vector<CScheme>::iterator iter;
    int i =0;
    for(i=0, iter=m_Schemes.begin(); iter!=m_Schemes.end(); ++i, ++iter)
    {
        if(nScheme == i)
        {
            m_Schemes.erase(iter);
            break;	// 务必加上break
        }
    }
    MutexUnlock();
}

void CTimerScheme::RemoveTimer(int nSch, int nTimer)
{
    MutexLock();
    m_Schemes[nSch].RemoveTimer(nTimer);
    MutexUnlock();
}


bool CTimerScheme::ReadTimerFile(string strFileName)
{
    TiXmlDocument* xmlTimer = new TiXmlDocument;
    TIMER_DATE    date;
    TIMER_TIME    time;
    int           nScheme					= 0;
    int			  nTimeType                 = 0;
    int           nSourceType               = 0;
    int           nDeviceType               = 0;
    int			  nCurScheme				= 0;
    string		  strAllSchemesValid		= "";
    string		  strData					= "";
    string		  strDateTime				= "";
    #if SUPPORT_TIMER_SCHEME_AUTO_SWITCH
    int           nAutoSwitch               = 0;
    #endif
    CHAR		  szPathName[STR_MAX_PATH]      = {0};
    BOOL		  bSelDays[DAYS_PER_WEEK]   = {0};
    //20240220,将此处最大定时点数目固定为正式版的数目，只在WEB添加定时点时才判断；
    //因为之前版本不限制试用版的定时点数目，现在需要限制，为了不覆盖用户原有的定时点，此处不进行判断。
    int			  nMaxTimerCount			= MAX_TIMER_COUNT_FORMAL;
    BOOL		  bUpdateFile				= FALSE;

    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName.data());

    if(!xmlTimer->LoadFile(szPathName))
    {
        return FALSE;
    }

    ClearSchemes();       // 先清除所有的定时方案信息
//LOG("定时方案统计", LV_INFO);
    // 定时方案统计
    TiXmlElement* timerScheme = xmlTimer->FirstChildElement();
    nCurScheme = atoi(timerScheme->Attribute("CurScheme")) - 1;
    strDateTime = timerScheme->Attribute("DateTime");
    strAllSchemesValid = timerScheme->Attribute("AllSchemesValid");

    #if SUPPORT_TIMER_SCHEME_AUTO_SWITCH
    if( timerScheme->Attribute("AutoSwitch") != NULL )
    {
        nAutoSwitch=(strcmp(timerScheme->Attribute("AutoSwitch"),"TRUE") == 0);
    }
    #endif

//LOG("定时方案数量",LV_INFO);
    // 定时方案数量
    for(TiXmlElement* elem=timerScheme->FirstChildElement(); elem!=NULL;
        elem=elem->NextSiblingElement())
    {
        CScheme SchemeFind;
        SchemeFind.SetName(elem->Attribute("Name"));
        if(elem->Attribute("UniqueID") != NULL)
        {
            SchemeFind.SetUniqueID(elem->Attribute("UniqueID"));
        }
        else
        {
            SchemeFind.SetUniqueID(GetGUID());
            bUpdateFile = TRUE;
        }

        #if SUPPORT_TIMER_SCHEME_AUTO_SWITCH
        //自动切换开关
        const char *szAutoSwitchType = elem->Attribute("AutoSwitchType");
        if(szAutoSwitchType == NULL)
        {
            SchemeFind.SetAutoSwitchType(SCH_AUTO_SWITCH_NOT_SET);
        }
        else
        {
            SchemeFind.SetAutoSwitchType(atoi(szAutoSwitchType));
        }
        
        //自动切换开始时间
        string AsStartDate = "";
        if(elem->Attribute("StartDate")!=NULL)
            AsStartDate = elem->Attribute("StartDate");
        if(AsStartDate.length()>0)
        {
            date.nYear = std::stoi(AsStartDate.substr(0,4));
            date.nMon  = std::stoi(AsStartDate.substr(5,2));
            date.nDay  = std::stoi(AsStartDate.substr(8,2));
        }
        else
        {
            memset(&date,0,sizeof(date));
        }
        SchemeFind.SetDateStart(date);
        //自动切换结束时间
        string AsEndDate = "";
        if(elem->Attribute("EndDate")!=NULL)
            AsEndDate = elem->Attribute("EndDate");
        if(AsEndDate.length()>0)
        {
            date.nYear = std::stoi(AsEndDate.substr(0,4));
            date.nMon  = std::stoi(AsEndDate.substr(5,2));
            date.nDay  = std::stoi(AsEndDate.substr(8,2));
        }
        else
        {
            memset(&date,0,sizeof(date));
        }
        SchemeFind.SetDateEnd(date);
        #endif
//LOG("定时点", LV_INFO);
        // 定时点 Timer
        for(TiXmlElement* timer=elem->FirstChildElement(); timer!=NULL;
            timer=timer->NextSiblingElement())
        {
            //printf("MaxTimerCount : %d\n", nMaxTimerCount);
            //printf("SchemeFind.GetTimerCount() : %d\n", SchemeFind.GetTimerCount());
            if(SchemeFind.GetTimerCount() >= nMaxTimerCount)
            {
                break;
            }

            CTimePoint TPFind;
            TPFind.SetName(timer->Attribute("Name"));
            if(timer->Attribute("UniqueID") != NULL)
            {
                TPFind.SetUniqueID(timer->Attribute("UniqueID"));
            }
            else
            {
                TPFind.SetUniqueID(GetGUID());
                bUpdateFile = TRUE;
            }
            TPFind.Enable(strcmp(timer->Attribute("Valid"), "TRUE") == 0);
            nTimeType = atoi(timer->Attribute("TimerType"));
            if(timer->Attribute("SourceType")!=NULL)
            {
                nSourceType = atoi(timer->Attribute("SourceType"));
            }
            if(timer->Attribute("DeviceType")!=NULL)
            {
                nDeviceType = atoi(timer->Attribute("DeviceType"));
            }
            TPFind.SetSourceType((TIMER_SOURCE_TYPE)nSourceType);
            TPFind.SetDeviceType((TIMER_DEVICE_TYPE)nDeviceType);
            TPFind.SetTimeType(TIMER_TIME_TYPE(nTimeType));
            #if 0
            TPFind.SetPlayToEnd(strcmp(timer->Attribute("PlayToEnd"), "TRUE") == 0);
            TPFind.SetResumePlaying(strcmp(timer->Attribute("ResumePlaying"), "TRUE") == 0);
            TPFind.SetIntercut(strcmp(timer->Attribute("Inter-cut"), "TRUE") == 0);
            TPFind.SetSinglePlay(strcmp(timer->Attribute("SinglePlay"), "TRUE") == 0);
            #endif
            TPFind.SetFollowDevice(strcmp(timer->Attribute("FollowDevice"), "TRUE") == 0);
//LOG("如果播放至结尾，则只能顺序播放有效", LV_INFO);
            // 如果播放至结尾，则只能顺序播放有效
            int nPlayMode = atoi(timer->Attribute("PlayMode"));

            if(TPFind.IsPlayToEnd() && nPlayMode!=PM_ORDER)
            {
                nPlayMode = PM_ORDER;
                bUpdateFile = TRUE;
            }

            TPFind.SetPlayMode(nPlayMode);
            TPFind.SetVolume(atoi(timer->Attribute("Volume")));

            //如果有Account字段，则设置定时点所属用户为指定名称，否则设置为admin
            const char *timer_account = timer->Attribute("Account");
            if(timer_account != NULL)
            {
               if(!g_Global.m_Users.IsExistUser(timer_account))
                {
                    continue;
                }
                TPFind.SetAccount(timer_account);
            }
            else
            {
                TPFind.SetAccount(SUPER_USER_NAME);
            }
            //如果有CancelDate参数，则设置今日定时点取消日期
            const char *timer_cancel_date = timer->Attribute("CancelDate");
            if(timer_cancel_date != NULL)
            {
               //判断是否是今日日期
               //CMyString tday=GetCurrentDateTime();
               //取得日期
                CTime	t = CTime::GetCurrentTimeT();
                char	szDate[11]  = {0};
                sprintf(szDate, "%04d-%02d-%02d", t.GetYear(), t.GetMonth(), t.GetDay());
                if(strcmp(szDate,timer_cancel_date) == 0)
                {
                    TPFind.SetSingleCancel(true);
                }
                else
                {
                    TPFind.SetSingleCancel(false);
                }
            }
            else
            {
                TPFind.SetSingleCancel(false);
            }

            // 定时点元素
            //TiXmlElement* timerElem = timer->FirstChildElement();
//LOG("选中分区", LV_INFO);
            // 选中分区
            TiXmlElement* SelectedSections = timer->FirstChildElement("SelectedSections");
            for(TiXmlElement* Section=SelectedSections->FirstChildElement(); Section!=NULL;
                Section=Section->NextSiblingElement())
            {
                BYTE nSplitterStatus = -1;

                // 如果有status属性
                if(Section->Attribute("Status")!=NULL && Section)
                {
                    nSplitterStatus = atoi(Section->Attribute("Status"));
                }

                TPFind.AddTimerSection(CMyString(Section->Attribute("Mac")), nSplitterStatus);
            }
//LOG("选中分组", LV_INFO);
            // 选中分组
            TiXmlElement* SelectedGroups = timer->FirstChildElement("SelectedGroups");

            if (SelectedGroups != NULL)
            {
                for(TiXmlElement* Group=SelectedGroups->FirstChildElement(); Group!=NULL;
                    Group=Group->NextSiblingElement())
                {
                    //LOG(Group->Attribute("ID"), LV_INFO);
                    //20230830 为避免旧版本删除用户后没有将用户创建的分组删除，造成定时点内该用户的分组仍存在，故在启动时检测，分组不存在时不加入。
                    const char *groupId=Group->Attribute("ID");
                    int groupIndex=-1;
                    if(g_Global.m_Groups.FindGroupByID(groupId,groupIndex))
                    {
                        TPFind.AddGroup(groupId);
                    }
                }
            }

//LOG("时间", LV_INFO);
            /*  时间  */
            //TiXmlElement* endElem;   // 保存结束元素
            // 时间按周循环
            if(nTimeType == TT_WEEK_CYCLE)
            {
                TiXmlElement* SelectedDays = timer->FirstChildElement("SelectedDays");
                TiXmlNode* text = SelectedDays->FirstChild();
                strData = string(text->ToText()->Value());
                // 每次取定时点都需要初始化一遍
                memset(bSelDays, 0 , sizeof(BOOL)*DAYS_PER_WEEK);
                for(int i=0; i<(int)strData.length(); i++)
                {
                    char cIndex = strData.at(i);
                    int Index = cIndex - 48 - 1;
                    bSelDays[Index] = TRUE;
                }
                TPFind.SetSelectedDays(bSelDays);
            }
            // 时间按日期循环
            else if(nTimeType == TT_SPECIFY_DATE)
            {
                // 开始日期
                //TiXmlElement* StartDate = SelectedGroups->NextSiblingElement();
                TiXmlElement* StartDate = timer->FirstChildElement("StartDate");
                TiXmlNode* startText = StartDate->FirstChild();
                strData = string(startText->ToText()->Value());
                date.nYear = std::stoi(strData.substr(0,4));
                date.nMon  = std::stoi(strData.substr(5,2));
                date.nDay  = std::stoi(strData.substr(8,2));
                TPFind.SetDateStart(date);//LOG(strData.data(), LV_INFO);

                // 结束日期
                //TiXmlElement* EndDate = StartDate->NextSiblingElement();
                TiXmlElement* EndDate = timer->FirstChildElement("EndDate");
                TiXmlNode* endText = EndDate->FirstChild();
                strData = string(endText->ToText()->Value());
                date.nYear = std::stoi(strData.substr(0,4));
                date.nMon  = std::stoi(strData.substr(5,2));
                date.nDay  = std::stoi(strData.substr(8,2));
                TPFind.SetDateEnd(date);
            }
//LOG("开始时间", LV_INFO);
            // 开始时间
            TiXmlElement* StartTime = timer->FirstChildElement("StartTime");
            TiXmlNode* startTimeText = StartTime->FirstChild();
            strData = string(startTimeText->ToText()->Value());
            time.nHour = std::stoi(strData.substr(0,2));
            time.nMin  = std::stoi(strData.substr(3,2));
            time.nSec  = std::stoi(strData.substr(6,2));
            TPFind.SetTimeStart(time);

            // 结束时间
            //TiXmlElement* EndTime = StartTime->NextSiblingElement();

            TiXmlElement* EndTime = timer->FirstChildElement("EndTime");

            if (EndTime != NULL)
            {
                TiXmlNode* endTimeText = EndTime->FirstChild();
                strData = string(endTimeText->ToText()->Value());
                time.nHour = std::stoi(strData.substr(0,2));
                time.nMin  = std::stoi(strData.substr(3,2));
                time.nSec  = std::stoi(strData.substr(6,2));
                TPFind.SetTimeEnd(time);
            }


            TiXmlElement* SelectedSequencePowers = timer->FirstChildElement("SelectedSeqPwrs");
            if(SelectedSequencePowers!=NULL)
            {
                for(TiXmlElement* SeqPwr=SelectedSequencePowers->FirstChildElement(); SeqPwr!=NULL;
                    SeqPwr=SeqPwr->NextSiblingElement())
                {
                    unsigned short channels = 0;
                    // 如果有status属性
                    if(SeqPwr->Attribute("Channels")!=NULL)
                    {
                        channels = atoi(SeqPwr->Attribute("Channels"));
                    }
                    if(SeqPwr->Attribute("Mac")!=NULL)
                    {
                        TPFind.AddTimerSeqPwr(CMyString(SeqPwr->Attribute("Mac")),channels);
                    }
                }
            }

//LOG("选择歌曲", LV_INFO);
            // 选择歌曲
            if(nSourceType == TIMER_SOURCE_TYPE_LOCAL_SONG)
            {
                TiXmlElement* SelectedSongs = timer->FirstChildElement("SelectedSongs");
                if(SelectedSongs!=NULL)
                {
                    for(TiXmlElement* songElem=SelectedSongs->FirstChildElement(); songElem!=NULL;
                        songElem=songElem->NextSiblingElement())
                    {
                        //LOG(songElem->Attribute("PathName"), LV_INFO);
                        //LOG(songElem->Attribute("Duration"), LV_INFO);
                        //printf("%d\n", atoi(songElem->Attribute("Duration")));
                        //LOG(FORMAT("%d\n", atoi(songElem->Attribute("Duration"))), LV_INFO);
                        TPFind.AddSong(CMyString(songElem->Attribute("PathName")), atoi(songElem->Attribute("Duration")));
                    }
                }
            }
            else if(nSourceType == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
            {
                // 选中音频采集器，由于一个定时点只能选中一个音频采集器的其中一个通道，所以没有子节点
                TiXmlElement* SelectedAudioCollector = timer->FirstChildElement("SelectedAudioCollector");
                if(SelectedAudioCollector!=NULL)
                {
                    ST_TIMER_AUDIO_COLLECTOR_INFO st_audioCollector;
                    sprintf(st_audioCollector.mac,"%s",SelectedAudioCollector->Attribute("Mac")?SelectedAudioCollector->Attribute("Mac"):"");
                    //判断此采集器是否存在
                    if(g_Global.m_AudioCollectors.GetSectionByMac(st_audioCollector.mac)!=NULL)
                    {
                        st_audioCollector.channelId = atoi(SelectedAudioCollector->Attribute("ChannelId")?SelectedAudioCollector->Attribute("ChannelId"):"");
                    }
                    else
                    {
                        memset(&st_audioCollector,0,sizeof(st_audioCollector));
                    }

                    TPFind.SetAudioCollector(st_audioCollector);
                }
            }

            SchemeFind.AddTimer(TPFind);
        }

        AddScheme(SchemeFind);
        nScheme++;
    }

    m_nCurScheme = (nCurScheme < nScheme ? nCurScheme : 0);
    SetDateTime(strDateTime);
#if SUPPORT_TIMER_SCHEME_AUTO_SWITCH
    SetAutoSwitch(nAutoSwitch);
#endif
    BOOL bAllSchemeValid = (strAllSchemesValid=="TRUE");
    m_bAllSchemesValid = (g_Global.m_AppType == APP_LEBO);      //APP_IS_LEBO;

    if(bAllSchemeValid != m_bAllSchemesValid)
    {
        bUpdateFile = TRUE;
    }

    if(RemoveInvalidGroup())
    {
        bUpdateFile=FALSE;
    }

    if(bUpdateFile)
    {
        WriteTimerFile(strFileName,TRUE);
    }

    xmlTimer->Clear();
    delete xmlTimer;
    return TRUE;
}


bool CTimerScheme::WriteTimerFile(string strFileName, bool bUpdateDateTime)
{
    MutexLock();
    TiXmlDocument   xmlTimer;
    TIMER_DATE      date;
    TIMER_TIME      time;
    int             nTimerCount				= 0;
    int             nTimeType				= 0;
    int             nSourceType             = 0;
    int             nDeviceType             = 0;
    BOOL            bPlayToEnd				= FALSE;
    string          str						= "";
    string          strData					= "";
    BOOL*           bSelDays				= NULL;
    int             nSchemeCount			= GetSchemeCount();
    CHAR            szPathName[STR_MAX_PATH]	= {0};

    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName.data());

    //m_bAllSchemesValid = APP_IS_LEBO;	// 如果是乐播版本，则设置为TRUE，其它设置为FALSE

    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0","utf-8","no");
    xmlTimer.LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment("save for schemeTime");
    xmlTimer.LinkEndChild(com);

    TiXmlElement* timerScheme = new TiXmlElement("TimerScheme");
    timerScheme->SetAttribute("SchemeCount", nSchemeCount);
    timerScheme->SetAttribute("CurScheme", GetCurScheme()+1);
    timerScheme->SetAttribute("DateTime", GetDateTime().C_Str());
    timerScheme->SetAttribute("AllSchemesValid",m_bAllSchemesValid?("TRUE"):("FALSE"));
    #if SUPPORT_TIMER_SCHEME_AUTO_SWITCH
    timerScheme->SetAttribute("AutoSwitch", m_bAutoSwitch?("TRUE"):("FALSE"));
    #endif
    xmlTimer.LinkEndChild(timerScheme);

    // 定时方案
    for(int sch=0; sch<nSchemeCount; sch++)
    {
        nTimerCount = GetTimerCount(sch);
        TiXmlElement* Scheme = new TiXmlElement("Scheme");
        Scheme->SetAttribute("SchemeID", sch+1);
        Scheme->SetAttribute("UniqueID", GetScheme(sch).GetUniqueID().C_Str());
        
        //Scheme->SetAttribute("Name", GetSchemeName(sch).C_Str());
        //string strName = StringToUTF8(GetSchemeName(sch).C_Str());
        Scheme->SetAttribute("Name", GetSchemeName(sch).C_Str());
        #if SUPPORT_TIMER_SCHEME_AUTO_SWITCH
        //自动切换类型
        Scheme->SetAttribute("AutoSwitchType",GetScheme(sch).GetAutoSwitchType());
        //开始日期
        date = GetScheme(sch).GetDateStart();
        char startDate[25] = {0};
        if(date.nYear == 0)
            memset(startDate,0,sizeof(startDate));
        else
            sprintf(startDate, "%04d-%02d-%02d", date.nYear, date.nMon, date.nDay);
        //结束日期
        date = GetScheme(sch).GetDateEnd();
        char endDate[25] = {0};
        if(date.nYear == 0)
            memset(endDate,0,sizeof(endDate));
        else
            sprintf(endDate, "%04d-%02d-%02d", date.nYear, date.nMon, date.nDay);

        Scheme->SetAttribute("StartDate", startDate);
        Scheme->SetAttribute("EndDate", endDate);
        #endif
        Scheme->SetAttribute("TimerCount", nTimerCount);
        timerScheme->LinkEndChild(Scheme);

        // 定时点
        for(int tm=0; tm<nTimerCount; tm++)
        {
            CTimePoint& tp = GetTimer(sch,tm);
            nTimeType      = tp.GetTimeType();
            nSourceType    = tp.GetSourceType();
            nDeviceType    = tp.GetDeviceType();
            bPlayToEnd     = tp.IsPlayToEnd();

            TiXmlElement* Timer = new TiXmlElement("Timer");

            Timer->SetAttribute("UniqueID", tp.GetUniqueID().C_Str());

            Timer->SetAttribute("Name", tp.GetName().C_Str());
            //string strName = StringToUTF8(tp.GetName().C_Str());
            //Timer->SetAttribute("Name", strName.data());

            Timer->SetAttribute("Valid", (tp.IsValid()?("TRUE"):("FALSE")));
            Timer->SetAttribute("TimerType", nTimeType);
            Timer->SetAttribute("SourceType", nSourceType);
            Timer->SetAttribute("DeviceType", nDeviceType);
            Timer->SetAttribute("PlayToEnd", tp.IsPlayToEnd()?("TRUE"):("FALSE"));
            Timer->SetAttribute("ResumePlaying", tp.GetResumePlaying() ? ("TRUE"):("FALSE"));
            Timer->SetAttribute("Inter-cut", tp.GetIntercut()?("TRUE"):("FALSE"));
            Timer->SetAttribute("FollowDevice", tp.GetFollowDevice()?("TRUE"):("FALSE"));
            Timer->SetAttribute("SinglePlay", tp.GetSinglePlay()?("TRUE"):("FALSE"));
            Timer->SetAttribute("PlayMode", tp.GetPlayMode());
            Timer->SetAttribute("Volume", tp.GetVolume());
            if(tp.GetSingleCancel() == true)
            {
                CTime	t = CTime::GetCurrentTimeT();
                char	szDate[11]  = {0};
                sprintf(szDate, "%04d-%02d-%02d", t.GetYear(), t.GetMonth(), t.GetDay());
                Timer->SetAttribute("CancelDate", szDate);
            }
            else
            {
                Timer->SetAttribute("CancelDate", "");
            }

            //设置定时点所属用户
            Timer->SetAttribute("Account",tp.GetAccount());

            Scheme->LinkEndChild(Timer);

            // 选中分区
            TiXmlElement* SelectedSections = new TiXmlElement("SelectedSections");
            SelectedSections->SetAttribute("SectionCount", tp.GetZoneCount());
            for(int i=0; i<tp.GetZoneCount(); i++)
            {
                TiXmlElement* Section = new TiXmlElement("Section");
                Section->SetAttribute("Mac", tp.GetZoneMac(i).C_Str());

                if (tp.GetSplitterStatus(i) >= 0)
                {
                    Section->SetAttribute("Status", tp.GetSplitterStatus(i));
                }
                SelectedSections->LinkEndChild(Section);        // ****
            }
            Timer->LinkEndChild(SelectedSections);

            // 选中分组
            TiXmlElement* SelectedGroups = new TiXmlElement("SelectedGroups");
            SelectedGroups->SetAttribute("GroupCount", tp.GetGroupCount());
            for(int i=0; i<tp.GetGroupCount(); ++i)
            {
                TiXmlElement* Group = new TiXmlElement("Group");
                Group->SetAttribute("ID", tp.GetGroupID(i).C_Str());
                SelectedGroups->LinkEndChild(Group);
            }
            Timer->LinkEndChild(SelectedGroups);

            // 按周循环
            if(nTimeType == TT_WEEK_CYCLE)
            {
                strData = "";
                bSelDays = tp.GetSelectedDays();
                for(int i=0; i<DAYS_PER_WEEK; i++)
                {
                    if(bSelDays[i])
                    {
                        str = std::to_string(i+1);
                        strData += str;
                    }
                }
                TiXmlElement* SelectedDays = new TiXmlElement("SelectedDays");
                TiXmlText* days = new TiXmlText(strData.c_str());
                SelectedDays->LinkEndChild(days);
                Timer->LinkEndChild(SelectedDays);
            }
            // 指定日期
            else if(nTimeType == TT_SPECIFY_DATE)
            {
                // 开始日期
                date = tp.GetDateStart();
                char startDate[25] = {0};
                sprintf(startDate, "%04d-%02d-%02d", date.nYear, date.nMon, date.nDay);
                TiXmlElement* StartDate = new TiXmlElement("StartDate");
                TiXmlText* sDate = new TiXmlText(startDate);
                StartDate->LinkEndChild(sDate);
                Timer->LinkEndChild(StartDate);

                // 截止日期
                date = tp.GetDateEnd();
                char endDate[25] = {0};
                sprintf(endDate,"%04d-%02d-%02d",date.nYear, date.nMon, date.nDay);
                TiXmlElement* EndDate = new TiXmlElement("EndDate");
                TiXmlText* eDate = new TiXmlText(endDate);
                EndDate->LinkEndChild(eDate);
                Timer->LinkEndChild(EndDate);
            }

            // 开始时间
            time = tp.GetTimeStart();
            char startTime[20] = {0};
            sprintf(startTime,"%02d:%02d:%02d",time.nHour, time.nMin, time.nSec);
            TiXmlElement* StartTime = new TiXmlElement("StartTime");
            TiXmlText* tStart = new TiXmlText(startTime);
            StartTime->LinkEndChild(tStart);
            Timer->LinkEndChild(StartTime);

            // 结束时间(如果是播放至结束，也要把结束时间写进去，为了判断定时点的时间交叉)
            time = tp.GetTimeEnd();
            char endTime[20] = {0};
            sprintf(endTime,"%02d:%02d:%02d",time.nHour, time.nMin, time.nSec);
            TiXmlElement* EndTime = new TiXmlElement("EndTime");
            TiXmlText* tEnd = new TiXmlText(endTime);
            EndTime->LinkEndChild(tEnd);
            Timer->LinkEndChild(EndTime);


            //选择时序器
            TiXmlElement* SelectedSeqPwrs = new TiXmlElement("SelectedSeqPwrs");
            SelectedSeqPwrs->SetAttribute("SeqPwrCount", tp.GetSeqPwrCount());
            for(int i=0; i<tp.GetSeqPwrCount(); i++)
            {
                TiXmlElement* SeqPwr = new TiXmlElement("SequencePwr");
                SeqPwr->SetAttribute("Mac", tp.GetSeqPwrMac(i).C_Str());

                if (tp.GetSeqPwrChannels(i) >= 0)
                {
                    SeqPwr->SetAttribute("Channels", tp.GetSeqPwrChannels(i));
                }
                SelectedSeqPwrs->LinkEndChild(SeqPwr);
            }
            Timer->LinkEndChild(SelectedSeqPwrs);

            if(nSourceType == TIMER_SOURCE_TYPE_LOCAL_SONG)
            {
                // 选定歌曲
                TiXmlElement* SelectedSongs = new TiXmlElement("SelectedSongs");
                SelectedSongs->SetAttribute("SongCount",tp.GetSongCount());
                Timer->LinkEndChild(SelectedSongs);
                for(int i=0; i<tp.GetSongCount(); i++)
                {
                    CSong& song = tp.GetSong(i);
                    TiXmlElement* Song = new TiXmlElement("Song");
                    Song->SetAttribute("Duration",song.GetDuration());
                    Song->SetAttribute("PathName",song.GetPathName().C_Str());
                    SelectedSongs->LinkEndChild(Song);
                }
            }
            else if(nSourceType == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
            {
                //为了避免升级回旧版本后导致读取selectdSongs报错，无论是否是播放本地歌曲，都出现歌曲字段
                TiXmlElement* SelectedSongs = new TiXmlElement("SelectedSongs");
                SelectedSongs->SetAttribute("SongCount",0);
                Timer->LinkEndChild(SelectedSongs);
                
                if(nSourceType == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
                {
                    // 选中音频采集器，由于一个定时点只能选中一个音频采集器的其中一个通道，所以不需要子节点
                    TiXmlElement* SelectedAudioCollector = new TiXmlElement("SelectedAudioCollector");
                    if(strlen(tp.GetAudioCollector().mac)>0)
                    {
                        SelectedAudioCollector->SetAttribute("Mac",tp.GetAudioCollector().mac);
                        SelectedAudioCollector->SetAttribute("ChannelId",tp.GetAudioCollector().channelId);
                    }
                    Timer->LinkEndChild(SelectedAudioCollector);
                }
            }
        }
    }
    MutexUnlock();

    bool saveFileOK=true;
    if(xmlTimer.SaveFile(szPathName))
    {
        SetNeedNotify(true);
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlTimer.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    return saveFileOK;
}

bool CTimerScheme::ImportSchemeFile(CScheme &scheme, string strPathName)
{
    MutexLock();
    TiXmlDocument*  xmlTimer = new TiXmlDocument;
    TIMER_DATE      date;
    TIMER_TIME      time;
    int			nScheme					= 0;
    int			nTimeType				= 0;
    int			nCurScheme				= 0;
    string		strData					= ("");
    string		strDateTime				= ("");
    BOOL		bSelDays[DAYS_PER_WEEK] = {0};

    if(!xmlTimer->LoadFile(strPathName.data()))
    {
        MutexUnlock();
        return FALSE;
    }

    TiXmlElement* timerScheme = xmlTimer->FirstChildElement();
    scheme.SetName(timerScheme->Attribute("Name"));

    for(TiXmlElement* timer=timerScheme->FirstChildElement(); timer!=NULL;
                timer=timer->NextSiblingElement())
    {
        CTimePoint TPFind;
        TPFind.SetName(timer->Attribute("Name"));
        TPFind.Enable(strcmp(timer->Attribute("Valid"), "TRUE") == 0);
        nTimeType = atoi(timer->Attribute("TimerType"));
        TPFind.SetTimeType(TIMER_TIME_TYPE(nTimeType));
        TPFind.SetPlayToEnd(strcmp(timer->Attribute("PlayToEnd"), "TRUE") == 0);
        TPFind.SetResumePlaying(strcmp(timer->Attribute("ResumePlaying"), "TRUE") == 0);
        TPFind.SetIntercut(strcmp(timer->Attribute("Inter-cut"), "TRUE") == 0);
        TPFind.SetFollowDevice(strcmp(timer->Attribute("FollowDevice"), "TRUE") == 0);
        TPFind.SetSinglePlay(strcmp(timer->Attribute("SinglePlay"), "TRUE") == 0);
        //LOG("如果播放至结尾，则只能顺序播放有效", LV_INFO);
        // 如果播放至结尾，则只能顺序播放有效
        int nPlayMode = atoi(timer->Attribute("PlayMode"));

        if(TPFind.IsPlayToEnd() && nPlayMode!=PM_ORDER)
        {
            nPlayMode = PM_ORDER;
        }

        TPFind.SetPlayMode(nPlayMode);
        TPFind.SetVolume(atoi(timer->Attribute("Volume")));

        // 定时点元素
        //TiXmlElement* timerElem = timer->FirstChildElement();
        //LOG("选中分区", LV_INFO);
        // 选中分区
        TiXmlElement* SelectedSections = timer->FirstChildElement("SelectedSections");
        for(TiXmlElement* Section=SelectedSections->FirstChildElement(); Section!=NULL;
            Section=Section->NextSiblingElement())
        {
            BYTE nSplitterStatus = -1;

            // 如果有status属性
            if(Section->Attribute("Status")!=NULL && Section)
            {
                nSplitterStatus = atoi(Section->Attribute("Status"));
            }

            TPFind.AddTimerSection(CMyString(Section->Attribute("Mac")), nSplitterStatus);
        }
        //LOG("选中分组", LV_INFO);
        // 选中分组
        TiXmlElement* SelectedGroups = timer->FirstChildElement("SelectedGroups");

        if (SelectedGroups != NULL)
        {
            for(TiXmlElement* Group=SelectedGroups->FirstChildElement(); Group!=NULL;
                Group=Group->NextSiblingElement())
            {
                //LOG(Group->Attribute("ID"), LV_INFO);
                TPFind.AddGroup(CMyString(Group->Attribute("ID")));
            }
        }

       //LOG("时间", LV_INFO);
        /*  时间  */
        //TiXmlElement* endElem;   // 保存结束元素
        // 时间按周循环
        if(nTimeType == TT_WEEK_CYCLE)
        {
            TiXmlElement* SelectedDays = timer->FirstChildElement("SelectedDays");
            TiXmlNode* text = SelectedDays->FirstChild();
            strData = string(text->ToText()->Value());
            // 每次取定时点都需要初始化一遍
            memset(bSelDays, 0 , sizeof(BOOL)*DAYS_PER_WEEK);
            for(int i=0; i<(int)strData.length(); i++)
            {
                char cIndex = strData.at(i);
                int Index = cIndex - 48 - 1;
                bSelDays[Index] = TRUE;
            }
            TPFind.SetSelectedDays(bSelDays);
        }
        // 时间按日期循环
        else if(nTimeType == TT_SPECIFY_DATE)
        {
            // 开始日期
            TiXmlElement* StartDate = SelectedGroups->NextSiblingElement();
            TiXmlNode* startText = StartDate->FirstChild();
            strData = string(startText->ToText()->Value());
            date.nYear = std::stoi(strData.substr(0,4));
            date.nMon  = std::stoi(strData.substr(5,2));
            date.nDay  = std::stoi(strData.substr(8,2));
            TPFind.SetDateStart(date);

            // 结束日期
            TiXmlElement* EndDate = StartDate->NextSiblingElement();
            TiXmlNode* endText = EndDate->FirstChild();
            strData = string(endText->ToText()->Value());
            date.nYear = std::stoi(strData.substr(0,4));
            date.nMon  = std::stoi(strData.substr(5,2));
            date.nDay  = std::stoi(strData.substr(8,2));
            TPFind.SetDateEnd(date);
        }
        //LOG("开始时间", LV_INFO);
        // 开始时间
        TiXmlElement* StartTime = timer->FirstChildElement("StartTime");
        TiXmlNode* startTimeText = StartTime->FirstChild();
        strData = string(startTimeText->ToText()->Value());
        time.nHour = std::stoi(strData.substr(0,2));
        time.nMin  = std::stoi(strData.substr(3,2));
        time.nSec  = std::stoi(strData.substr(6,2));
        TPFind.SetTimeStart(time);

        // 结束时间
        //TiXmlElement* EndTime = StartTime->NextSiblingElement();
        TiXmlElement* EndTime = timer->FirstChildElement("EndTime");

        if (EndTime != NULL)
        {
            TiXmlNode* endTimeText = EndTime->FirstChild();
            strData = string(endTimeText->ToText()->Value());
            time.nHour = std::stoi(strData.substr(0,2));
            time.nMin  = std::stoi(strData.substr(3,2));
            time.nSec  = std::stoi(strData.substr(6,2));
            TPFind.SetTimeEnd(time);
        }

        //LOG("选择歌曲", LV_INFO);
        // 选择歌曲
        TiXmlElement* SelectedSongs = timer->FirstChildElement("SelectedSongs");
        for(TiXmlElement* songElem=SelectedSongs->FirstChildElement(); songElem!=NULL;
            songElem=songElem->NextSiblingElement())
        {
            //LOG(songElem->Attribute("PathName"), LV_INFO);
            //LOG(songElem->Attribute("Duration"), LV_INFO);
            //printf("%d\n", atoi(songElem->Attribute("Duration")));
            TPFind.AddSong(CMyString(songElem->Attribute("PathName")), atoi(songElem->Attribute("Duration")));
        }

        scheme.AddTimer(TPFind);
    }

    xmlTimer->Clear();
    delete xmlTimer;
    MutexUnlock();
    return TRUE;
}

bool CTimerScheme::ExportSchemeFile(int nSch, string strPathName)
{
    MutexLock();
    TiXmlDocument xmlTimer;
    TIMER_DATE      date;
    TIMER_TIME      time;
    int             nTimerCount				= 0;
    int             nTimeType				= 0;
    BOOL            bPlayToEnd				= FALSE;
    string          str						= "";
    string          strData					= "";
    BOOL*           bSelDays				= NULL;
    int                  nSchemeCount			= GetSchemeCount();
    //CHAR             szPathName[STR_MAX_PATH]	= {0};

    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0","utf-8","no");
    xmlTimer.LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment("save for schemeTime");
    xmlTimer.LinkEndChild(com);

    nTimerCount = GetTimerCount(nSch);
    TiXmlElement* Scheme = new TiXmlElement("Scheme");
    Scheme->SetAttribute("Name", GetSchemeName(nSch).C_Str());
    Scheme->SetAttribute("TimerCount", nTimerCount);
    xmlTimer.LinkEndChild(Scheme);

    // 定时点
    for(int tm=0; tm<nTimerCount; tm++)
    {
        CTimePoint& tp = GetTimer(nSch,tm);
        nTimeType      = tp.GetTimeType();
        bPlayToEnd     = tp.IsPlayToEnd();

        TiXmlElement* Timer = new TiXmlElement("Timer");

        Timer->SetAttribute("Name", tp.GetName().C_Str());
        //string strName = StringToUTF8(tp.GetName().C_Str());
        //Timer->SetAttribute("Name", strName.data());

        Timer->SetAttribute("Valid", (tp.IsValid()?("TRUE"):("FALSE")));
        Timer->SetAttribute("TimerType", nTimeType);
        Timer->SetAttribute("PlayToEnd", tp.IsPlayToEnd()?("TRUE"):("FALSE"));
        Timer->SetAttribute("ResumePlaying", tp.GetResumePlaying() ? ("TRUE"):("FALSE"));
        Timer->SetAttribute("Inter-cut", tp.GetIntercut()?("TRUE"):("FALSE"));
        Timer->SetAttribute("FollowDevice", tp.GetFollowDevice()?("TRUE"):("FALSE"));
        Timer->SetAttribute("SinglePlay", tp.GetSinglePlay()?("TRUE"):("FALSE"));
        Timer->SetAttribute("PlayMode", tp.GetPlayMode());
        Timer->SetAttribute("Volume", tp.GetVolume());

        Scheme->LinkEndChild(Timer);

        // 选中分区
        TiXmlElement* SelectedSections = new TiXmlElement("SelectedSections");
        SelectedSections->SetAttribute("SectionCount", tp.GetZoneCount());
        for(int i=0; i<tp.GetZoneCount(); i++)
        {
            TiXmlElement* Section = new TiXmlElement("Section");
            Section->SetAttribute("Mac", tp.GetZoneMac(i).C_Str());

            if (tp.GetSplitterStatus(i) >= 0)
            {
                Section->SetAttribute("Status", tp.GetSplitterStatus(i));
            }
            SelectedSections->LinkEndChild(Section);        // ****
        }
        Timer->LinkEndChild(SelectedSections);

        // 选中分组
        TiXmlElement* SelectedGroups = new TiXmlElement("SelectedGroups");
        SelectedGroups->SetAttribute("GroupCount", tp.GetGroupCount());
        for(int i=0; i<tp.GetGroupCount(); ++i)
        {
            TiXmlElement* Group = new TiXmlElement("Group");
            Group->SetAttribute("ID", tp.GetGroupID(i).C_Str());
            SelectedGroups->LinkEndChild(Group);
        }
        Timer->LinkEndChild(SelectedGroups);

        // 按周循环
        if(nTimeType == TT_WEEK_CYCLE)
        {
            strData = "";
            bSelDays = tp.GetSelectedDays();
            for(int i=0; i<DAYS_PER_WEEK; i++)
            {
                if(bSelDays[i])
                {
                    str = std::to_string(i+1);
                    strData += str;
                }
            }
            TiXmlElement* SelectedDays = new TiXmlElement("SelectedDays");
            TiXmlText* days = new TiXmlText(strData.c_str());
            SelectedDays->LinkEndChild(days);
            Timer->LinkEndChild(SelectedDays);
        }
        // 指定日期
        else if(nTimeType == TT_SPECIFY_DATE)
        {
            // 指定日期
            date = tp.GetDateStart();
            char startDate[25] = {0};
            sprintf(startDate, "%04d-%02d-%02d", date.nYear, date.nMon, date.nDay);
            TiXmlElement* StartDate = new TiXmlElement("StartDate");
            TiXmlText* sDate = new TiXmlText(startDate);
            StartDate->LinkEndChild(sDate);
            Timer->LinkEndChild(StartDate);

            // 截止日期
            date = tp.GetDateEnd();
            char endDate[25] = {0};
            sprintf(endDate,"%04d-%02d-%02d",date.nYear, date.nMon, date.nDay);
            TiXmlElement* EndDate = new TiXmlElement("EndDate");
            TiXmlText* eDate = new TiXmlText(endDate);
            EndDate->LinkEndChild(eDate);
            Timer->LinkEndChild(EndDate);
        }

        // 开始时间
        time = tp.GetTimeStart();
        char startTime[20] = {0};
        sprintf(startTime,"%02d:%02d:%02d",time.nHour, time.nMin, time.nSec);
        TiXmlElement* StartTime = new TiXmlElement("StartTime");
        TiXmlText* tStart = new TiXmlText(startTime);
        StartTime->LinkEndChild(tStart);
        Timer->LinkEndChild(StartTime);

        // 结束时间(如果是播放至结束，也要把结束时间写进去，为了判断定时点的时间交叉)
        time = tp.GetTimeEnd();
        char endTime[20] = {0};
        sprintf(endTime,"%02d:%02d:%02d",time.nHour, time.nMin, time.nSec);
        TiXmlElement* EndTime = new TiXmlElement("EndTime");
        TiXmlText* tEnd = new TiXmlText(endTime);
        EndTime->LinkEndChild(tEnd);
        Timer->LinkEndChild(EndTime);

        // 选定歌曲
        TiXmlElement* SelectedSongs = new TiXmlElement("SelectedSongs");
        SelectedSongs->SetAttribute("SongCount",tp.GetSongCount());
        Timer->LinkEndChild(SelectedSongs);
        for(int i=0; i<tp.GetSongCount(); i++)
        {
            CSong& song = tp.GetSong(i);
            TiXmlElement* Song = new TiXmlElement("Song");
            Song->SetAttribute("Duration",song.GetDuration());
            Song->SetAttribute("PathName",song.GetPathName().C_Str());
            SelectedSongs->LinkEndChild(Song);
        }
    }

    MutexUnlock();
    if(xmlTimer.SaveFile(strPathName.data()))
    {
        xmlTimer.Clear();
        return TRUE;
    }
    else
    {
        xmlTimer.Clear();
        return FALSE;
    }
}


bool CTimerScheme::UpdateSchemeName(CMyString strOldDefaultSchemeName, CMyString strOldDefaultTimerName)
{
    bool	bUpdated	= FALSE;
    CMyString strDateTime	= GetCurrentDateTime();
    MutexLock();
    for (int i=0; i<GetSchemeCount(); ++i)
    {
        CScheme& scheme = GetScheme(i);
        CMyString strSchemeName = scheme.GetName();

        if (strSchemeName.Find(strOldDefaultSchemeName) == 0)
        {
            strSchemeName.Replace(strOldDefaultSchemeName, LANG_STR(LANG_SECTION_TIMING, "Timing Scheme", ("定时方案")));
            SetSchemeName(i, strSchemeName);
            //SetDateTime(strDateTime);

            bUpdated = TRUE;
        }

        for (int j=0; j<scheme.GetTimerCount(); ++j)
        {
            CMyString strTimerName = scheme.GetTimerName(j);

            if (strTimerName.Find(strOldDefaultTimerName) == 0)
            {
                strTimerName.Replace(strOldDefaultTimerName, LANG_STR(LANG_SECTION_TIMING, "Timing Point", ("定时点")));
                scheme.SetTimerName(j, strTimerName);
                //SetDateTime(strDateTime);
                bUpdated = TRUE;
            }
        }
    }

    MutexUnlock();
    if (bUpdated)
    {
        //WriteTimerFile(HTTP_FILE_TIMER);
        g_Global.WriteXmlFile(FILE_TIMER);       //zhuyg
    }

    return bUpdated;
}

// 通过playID找到对应的定时点
CTimePoint*	 CTimerScheme::GetTimePointByPlayID(int playID)
{
    MutexLock();
    int nTimePointCount = m_Schemes[m_nCurScheme].GetTimerCount();

    for (int i=0; i<nTimePointCount; ++i)
    {
        CTimePoint& timePoint = m_Schemes[m_nCurScheme].GetTimer(i);

        if (timePoint.GetPlayID() == playID)
        {
            MutexUnlock();
            return &timePoint;
        }
    }
    MutexUnlock();
    return NULL;
}

// 通过MAC查找此时在执行的定时点
CTimePoint* CTimerScheme::GetPlayingTimePointByDeviceMac(LPCSTR szMac)
{
    MutexLock();
    int nTimePointCount = m_Schemes[m_nCurScheme].GetTimerCount();
    CMyString strMac(szMac);

    for (int i=0; i<nTimePointCount; ++i)
    {
        CTimePoint& timePoint = m_Schemes[m_nCurScheme].GetTimer(i);

        if (timePoint.GetPlayID() > 0 && timePoint.ContainZone(strMac))
        {
            MutexUnlock();
            return &timePoint;
        }
    }
    MutexUnlock();
    return NULL;
}


bool	 CTimerScheme::RemoveGroupFromAllSchemes(CMyString strGroupID)
{
    MutexLock();
    int nSchemeCount = GetSchemeCount();
    bool flag = FALSE;
    for (int i=0; i<nSchemeCount; ++i)
    {
        CScheme& scheme = GetScheme(i);

        int nTimerCount = scheme.GetTimerCount();

        for (int j=0; j<nTimerCount; j++)
        {
            CTimePoint& timePoint = scheme.GetTimer(j);

            if (timePoint.RemoveGroup(strGroupID))
            {
                flag = TRUE;
            }
        }
    }

    MutexUnlock();
    if (flag)
    {
        //WriteTimerFile(HTTP_FILE_TIMER, TRUE);
        g_Global.WriteXmlFile(FILE_TIMER);       //zhuyg
    }
    return flag;
}


bool	 CTimerScheme::RemoveAllGroupsFromAllSchemes(void)
{
    MutexLock();
    int nSchemeCount = GetSchemeCount();
    BOOL flag = FALSE;

    for (int i=0; i<nSchemeCount; ++i)
    {
        CScheme& scheme = GetScheme(i);

        int nTimerCount = scheme.GetTimerCount();

        for (int j=0; j<nTimerCount; j++)
        {
            CTimePoint& timePoint = scheme.GetTimer(j);

            if (timePoint.GetGroupCount() > 0)
            {
                flag = TRUE;
                timePoint.ClearGroups();
            }
        }
    }
    MutexUnlock();
    return flag;
}



bool CTimerScheme::RemoveInvalidGroup()
{
    MutexLock();
    int nSchemeCount = GetSchemeCount();
    bool flag = FALSE;
    for (int i=0; i<nSchemeCount; ++i)
    {
        CScheme& scheme = GetScheme(i);

        int nTimerCount = scheme.GetTimerCount();

        for (int j=0; j<nTimerCount; j++)
        {
            CTimePoint& timePoint = scheme.GetTimer(j);

            for(int k=0;k<timePoint.GetGroupCount();k++)
            {
                int groupIndex=-1;
                CMyString groupID=timePoint.GetGroupID(k);
                if( g_Global.m_Groups.FindGroupByID(groupID,groupIndex) == NULL )
                {
                    if (timePoint.RemoveGroup(groupID))
                    {
                        flag = TRUE;
                    }
                }
            }
        }
    }

    MutexUnlock();
    if (flag)
    {
        g_Global.WriteXmlFile(FILE_TIMER);
    }
    return flag;
}



// CS新增
UINT CTimerScheme::GetIntersectSchemes(unsigned int uSchemeIndex, CTimePoint& timePoint, vector<IntersectScheme>& interSchemes,CTimePoint &conflictFirstTimingPoint,BOOL bAllTimers)
{
    MutexLock();
    interSchemes.clear();

    UINT uSchemeCount = GetSchemeCount();

    bool foundConflictFirstTimingPoint=false;

    for (UINT i=0; i<uSchemeCount; ++i)
    {
        // 如果不是所有方案有效，则只判断当前的方案，其它的跳过
        if (!m_bAllSchemesValid && i != uSchemeIndex)
        {
            continue;
        }

        CScheme& scheme = GetScheme(i);

        IntersectScheme interScheme;

        if (scheme.GetIntersectTimers(timePoint, interScheme.pTimePoints, bAllTimers) > 0)
        {
            interScheme.nScheme = i;
            interSchemes.push_back(interScheme);
            
            if(!foundConflictFirstTimingPoint)
            {
                foundConflictFirstTimingPoint = true;
                conflictFirstTimingPoint = *interScheme.pTimePoints[0];
            }
        }
    }
    MutexUnlock();
    return interSchemes.size();
}

unsigned int CTimerScheme::GetIntersectSchemes(CTimePoint& timePoint, vector<IntersectScheme>& interSchemes, bool bAllTimers)
{
    MutexLock();
    interSchemes.clear();

    unsigned int uSchemeCount = GetSchemeCount();

    for (unsigned int i=0; i<uSchemeCount; ++i)
    {
        // 如果不是所有方案有效，则只判断当前的方案，其它的跳过
        if (!m_bAllSchemesValid && i != (uint)m_nCurScheme)
        {
            continue;
        }

        CScheme& scheme = GetScheme(i);

        IntersectScheme interScheme;

        if (scheme.GetIntersectTimers(timePoint, interScheme.pTimePoints, bAllTimers) > 0)
        {
            interScheme.nScheme = i;
            interSchemes.push_back(interScheme);
        }
    }
    MutexUnlock();
    return interSchemes.size();
}


void CTimerScheme::DisableTimers(vector<IntersectScheme>& interSchemes)
{
    MutexLock();
    unsigned int uSchemeCount = interSchemes.size();

    for (unsigned int i=0; i<uSchemeCount; ++i)
    {
        unsigned int uTimerCount = interSchemes[i].pTimePoints.size();

        for (unsigned int j=0; j<uTimerCount; ++j)
        {
            CTimePoint* pTimePoint = interSchemes[i].pTimePoints[j];
            pTimePoint->Enable(FALSE);
        }
    }
    MutexUnlock();
}


CMyString CTimerScheme::GetTimersName(vector<IntersectScheme>& interSchemes)
{
    MutexLock();
    CMyString strName, strNames = ("");

    unsigned int uSchemeCount = interSchemes.size();

    for (unsigned int i=0; i<uSchemeCount; ++i)
    {
        unsigned int uTimerCount = interSchemes[i].pTimePoints.size();

        for (unsigned int j=0; j<uTimerCount; ++j)
        {
            CTimePoint* pTimePoint = interSchemes[i].pTimePoints[j];

            // 最后一个定时点，名称后面不用加上逗号
            if (i == uSchemeCount-1 && j == uTimerCount- 1)
            {
                strName = pTimePoint->GetName();
            }
            else
            {
                strName.Format((char*)("%s,"), pTimePoint->GetName().C_Str());
            }

            strNames += strName;
        }
    }
    MutexUnlock();
    return strNames;
}


// 移除定时点中的分区
bool CTimerScheme::RemoveSectionFromAllSchemes(const char* szMac)
{
    MutexLock();
    int nSchemeCount = GetSchemeCount();
    bool flag = FALSE;
    
    for (int i=0; i<nSchemeCount; ++i)
    {
        CScheme& scheme = GetScheme(i);

        int nTimerCount = scheme.GetTimerCount();

        for (int j=0; j<nTimerCount; j++)
        {
            CTimePoint& timePoint = scheme.GetTimer(j);

            if (timePoint.RemoveTimerSection(CMyString(szMac)))
            {
                flag = TRUE;
            }
        }
    }

    MutexUnlock();
    if (flag)
    {
        //WriteTimerFile(HTTP_FILE_TIMER, TRUE);
        g_Global.WriteXmlFile(FILE_TIMER);       //zhuyg
    }

    return flag;
}


// 移除定时点中的音频采集器设备
bool CTimerScheme::RemoveAudioCollectorFromAllSchemes(const char* szMac)
{
    MutexLock();
    int nSchemeCount = GetSchemeCount();
    bool flag = FALSE;
    
    for (int i=0; i<nSchemeCount; ++i)
    {
        CScheme& scheme = GetScheme(i);

        int nTimerCount = scheme.GetTimerCount();

        for (int j=0; j<nTimerCount; j++)
        {
            CTimePoint& timePoint = scheme.GetTimer(j);

            if(strcmp(timePoint.GetAudioCollector().mac,szMac) == 0)
            {
                ST_TIMER_AUDIO_COLLECTOR_INFO st_audioCollctor;
                memset(&st_audioCollctor,0,sizeof(st_audioCollctor));
                timePoint.SetAudioCollector(st_audioCollctor);
            }
        }
    }

    MutexUnlock();
    if (flag)
    {
        //WriteTimerFile(HTTP_FILE_TIMER, TRUE);
        g_Global.WriteXmlFile(FILE_TIMER);       //zhuyg
    }

    return flag;
}



// 移除定时点中的电源时序器
bool CTimerScheme::RemoveSequencePowerFromAllSchemes(const char* szMac)
{
    MutexLock();
    int nSchemeCount = GetSchemeCount();
    bool flag = FALSE;
    
    for (int i=0; i<nSchemeCount; ++i)
    {
        CScheme& scheme = GetScheme(i);

        int nTimerCount = scheme.GetTimerCount();

        for (int j=0; j<nTimerCount; j++)
        {
            CTimePoint& timePoint = scheme.GetTimer(j);

            if (timePoint.RemoveTimerSequencePower(CMyString(szMac)))
            {
                flag = TRUE;
            }
        }
    }

    MutexUnlock();
    if (flag)
    {
        g_Global.WriteXmlFile(FILE_TIMER);       //zhuyg
    }

    return flag;
}


//获取今日定时点
void CTimerScheme::GetTodayTimer(CTime& t,vector<StTodayTimerP>& timerVec)
{
    int nSchemeCount = GetSchemeCount();
    if(nSchemeCount == 0)
        return;
    MutexLock();
    nSchemeCount = GetSchemeCount();
    if(nSchemeCount == 0)
    {
        MutexUnlock();
        return;
    }
    CScheme& scheme = GetScheme(m_nCurScheme);

    for (int j=0; j<scheme.GetTimerCount(); j++)
    {
        CTimePoint& timePoint = scheme.GetTimer(j);
        StTodayTimerP todayTimerp;
        // 如果定时点无效，继续检测下一个定时点
        if (!timePoint.IsValid())
        {
            continue;
        }

        if(timePoint.GetSingleCancel() == true)
        {
            todayTimerp.status = TODAY_TIMER_STATUS_SINGLE_CANCEL;
        }
        else if (timePoint.GetTimeType() == TT_WEEK_CYCLE)
        {
            bool *sel=timePoint.GetSelectedDays();
            // 星期日
            if (t.GetDayOfWeek() == 1)
            {
                if (!sel[6]) // 未选中，则返回FALSE
                {
                    continue;
                }
            }
            // 星期一到星期六
            else
            {
                if (!sel[t.GetDayOfWeek()-2])// 未选中，则返回FALSE
                {
                    continue;
                }
            }

            CTime tStart1(2000, 1, 1,
                            t.GetHour(), t.GetMinute(), t.GetSecond() );

            CTime tStart2(2000, 1, 1,
                            timePoint.GetTimeStart().nHour,
                            timePoint.GetTimeStart().nMin,
                            timePoint.GetTimeStart().nSec);

            if( CTimePoint::IsTimeInRange(timePoint.GetTimeStart(), timePoint.GetTimeEnd(), t) )
            {
                if(timePoint.GetDeviceType() == TIMER_DEVICE_TYPE_DECODING_TERMINAL)
                {
                    if(timePoint.GetSourceType() == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
                    {
                        todayTimerp.status=TODAY_TIMER_STATUS_RUNNING;
                    }
                    else if(timePoint.GetSourceType() == TIMER_SOURCE_TYPE_LOCAL_SONG)
                    {
                        if( timePoint.GetPlayID()>0 || tStart1-tStart2<=1 ) //刚好到点，这时候playID还未更新，此时也认为是正在执行
                            todayTimerp.status=TODAY_TIMER_STATUS_RUNNING;
                        else
                            todayTimerp.status=TODAY_TIMER_STATUS_ALREADY_RUN;
                    }
                }
                else if(timePoint.GetDeviceType() == TIMER_DEVICE_TYPE_SEQUENCE_POWER)
                {
                    if(timePoint.GetSeqPwrCount()>0) //如果存在电源时序器，才正在运行
                    {
                        todayTimerp.status=TODAY_TIMER_STATUS_RUNNING;
                    }
                    else
                    {
                        todayTimerp.status=TODAY_TIMER_STATUS_ALREADY_RUN;
                    }
                }
            }
            else if(tStart1>tStart2)
            {
                todayTimerp.status=TODAY_TIMER_STATUS_ALREADY_RUN;
            }
            else
            {
                todayTimerp.status=TODAY_TIMER_STATUS_NO_RUN;
            }
        }
        else if (timePoint.GetTimeType() == TT_SPECIFY_DATE)
        {
            if(!CTimePoint::IsDateInRange(timePoint.GetDateStart(), timePoint.GetDateEnd(), t))
            {
                continue;
            }

            CTime tStart1(2000, 1, 1,
                            t.GetHour(), t.GetMinute(), t.GetSecond() );

            CTime tStart2(2000, 1, 1,
                            timePoint.GetTimeStart().nHour,
                            timePoint.GetTimeStart().nMin,
                            timePoint.GetTimeStart().nSec);

            if( CTimePoint::IsTimeInRange(timePoint.GetTimeStart(), timePoint.GetTimeEnd(), t) )
            {
                if(timePoint.GetDeviceType() == TIMER_DEVICE_TYPE_DECODING_TERMINAL)
                {
                    if(timePoint.GetSourceType() == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
                    {
                        todayTimerp.status=TODAY_TIMER_STATUS_RUNNING;
                    }
                    else if(timePoint.GetSourceType() == TIMER_SOURCE_TYPE_LOCAL_SONG)
                    {
                        if( timePoint.GetPlayID()>0 || tStart1-tStart2<=1 ) //刚好到点，这时候playID还未更新，此时也认为是正在执行
                            todayTimerp.status=TODAY_TIMER_STATUS_RUNNING;
                        else
                            todayTimerp.status=TODAY_TIMER_STATUS_ALREADY_RUN;
                    }
                }
                else if(timePoint.GetDeviceType() == TIMER_DEVICE_TYPE_SEQUENCE_POWER)
                {
                    todayTimerp.status=TODAY_TIMER_STATUS_RUNNING;
                }
            }
            else if(tStart1>tStart2)
            {
                todayTimerp.status=TODAY_TIMER_STATUS_ALREADY_RUN;
            }
            else
            {
                todayTimerp.status=TODAY_TIMER_STATUS_NO_RUN;
            }
        }
        todayTimerp.TimerPoint=timePoint;
        timerVec.push_back(todayTimerp);
    }

    //定时点排序

    int nTimerCount = timerVec.size();

    for (int i=0; i<nTimerCount-1; ++i)
    {
        for (int j=0; j<nTimerCount-i-1; ++j)
        {
            CTimePoint &p1=timerVec[j].TimerPoint;
            CTimePoint &p2=timerVec[j+1].TimerPoint;
            int &s1=timerVec[j].status;
            int &s2=timerVec[j+1].status;
            CTime tStart1(2000, 1, 1,
                            p1.GetTimeStart().nHour,
                            p1.GetTimeStart().nMin,
                            p1.GetTimeStart().nSec);

            CTime tStart2(2000, 1, 1,
                            p2.GetTimeStart().nHour,
                            p2.GetTimeStart().nMin,
                            p2.GetTimeStart().nSec);

            BOOL bExchange = (tStart1 > tStart2);   //如果tStart1>tStart2,交换位置，保持最小的在前面

            if (bExchange)
            {
                CTimePoint tp = p1;     //此处不能用引用，否则P1变化后tp也会跟着变
                p1=p2;
                p2=tp;

                int ts=s1;
                s1=s2;
                s2=ts;
            }
        }
    }
    
#if 0
    printf("TodayTimeCount=%d\n",nTimerCount);
    for(int i=0;i<nTimerCount;i++)
    {
        printf("i=%d,status=%d,TimerName=%s\n",i,timerVec[i].status,timerVec[i].TimerPoint.GetName().C_Str());
    }
#endif

    MutexUnlock();
}




//对比两个今日定时点向量是否相等
bool CTimerScheme::IsTimeVecEqual(vector<StTodayTimerP>& timerVec1,vector<StTodayTimerP>& timerVec2)
{
    if(timerVec1.size()!=timerVec2.size())
        return false;
    int nTimerCount = timerVec1.size();
    for(int i=0;i<nTimerCount;i++)
    {
        if(timerVec1[i].status!=timerVec2[i].status)
            return false;
        if(timerVec1[i].TimerPoint.GetID()!=timerVec2[i].TimerPoint.GetID() || timerVec1[i].TimerPoint.GetName()!=timerVec2[i].TimerPoint.GetName())
            return false;
    }
    return true;
}




//移除对应用户的定时点
bool CTimerScheme::RemoveTimerSpecUser(const char* szAccount)
{
    bool bRemove=false;
    MutexLock();
   
    for (int i=0; i<GetSchemeCount(); ++i)
    {
        CScheme& scheme = GetScheme(i);
        for (int j=0; j<scheme.GetTimerCount(); ++j)
        {
            CTimePoint& timePoint = scheme.GetTimer(j);
            if( strcmp(timePoint.GetAccount(),szAccount) == 0 )
            {
                scheme.RemoveTimer(j);
                bRemove=true;
            }
        }
    }
    
    MutexUnlock();
    if(bRemove)
    {
        g_Global.WriteXmlFile(FILE_TIMER);
    }
    return bRemove;
}




void CTimerScheme::MutexLock()
{
    pthread_mutex_lock(&m_TimerMutex);
}


void CTimerScheme::MutexUnlock()
{
    pthread_mutex_unlock(&m_TimerMutex);
}




//恢复定时点的单次取消状态（新一天主动恢复）
void CTimerScheme::ResetTimerSingleCancel()
{
    BOOL bUpdateFile = FALSE;
    MutexLock();
    for (int i=0; i<GetSchemeCount(); ++i)
    {
        CScheme& scheme = GetScheme(i);
        for (int j=0; j<scheme.GetTimerCount(); ++j)
        {
            CTimePoint& timePoint = scheme.GetTimer(j);
            if(timePoint.GetSingleCancel())
            {
                bUpdateFile=true;
                timePoint.SetSingleCancel(false);
            }
        }
    }
    MutexUnlock();
    if(bUpdateFile)
    {
        g_Global.WriteXmlFile(FILE_TIMER);
    }
}



// 检测定时点内的电源时序器状态
void   CTimerScheme::CheckTimersSequenceStatus(CTime& t)
{
    //如果没有电源时序器，那么退出
    if(g_Global.m_SequencePower.GetSecCount() == 0)
        return;

    CScheme&	curScheme	= g_Global.m_TimerScheme.GetScheme(g_Global.m_TimerScheme.GetCurScheme());
    int			nTimerCount = curScheme.GetTimerCount();

    //现将所有的电源时序器通道定时信息清空
    bool existOnlinePwr=false;
    for(int i=0;i<g_Global.m_SequencePower.GetSecCount();i++)
    {
        CSection &sectionPwr=g_Global.m_SequencePower.GetSection(i);
        std::shared_ptr<CSequencePower> seqPwr = sectionPwr.m_pSequencePower;
        if(seqPwr)
        {
            for(int k=0;k<seqPwr->GetChannelCount();k++)
            {
                seqPwr->GetChannel(k).SetIsExistTimer(false);
                seqPwr->GetChannel(k).SetIsInTiming(false);
            }
        }
        if(sectionPwr.IsOnline())
        {
            existOnlinePwr=true;
        }
    }
    if(!existOnlinePwr)
    {
        return;
    }

    for (int i=0; i<nTimerCount; ++i)
    {
        CTimePoint& timePoint = curScheme.GetTimer(i);
        //如果定时点的设备类型不是电源时序器，则跳过
        if(timePoint.GetDeviceType() != TIMER_DEVICE_TYPE_SEQUENCE_POWER)
            continue;
        //如果定时点已经处于单次取消状态，那么也跳过
        #if 1
        if(timePoint.GetSingleCancel())
            continue;
        #endif
        int seqPwrCount=timePoint.GetSeqPwrCount();
        if(seqPwrCount>0)
        {
            bool isSeqPwrWorking=false;
            if(timePoint.IsSequencePowerWorking(t))
            {
                //准备执行或者在执行中
                isSeqPwrWorking=true;
            }
            
            for(int k=0;k<seqPwrCount;k++)
            {
                //找到每一个电源时序器
                CMyString seqPwrMac=timePoint.GetSeqPwrMac(k);
                vector<int> vecChannel;
                LPCSection seqPwrSec=g_Global.m_SequencePower.GetSectionByMac(seqPwrMac);
                if(seqPwrSec)
                {
                    unsigned short channelVal = timePoint.GetSeqPwrChannels(k);
                    //channelId是集合，
                    vector<int> vecChannel;
                    for(int t=0;t<seqPwrSec->m_pSequencePower->GetRealChannelCnt();t++)
                    {
                        int bitVal=pow(2,t);
                        if(channelVal & bitVal)
                        {
                            vecChannel.push_back(t+1);
                            seqPwrSec->m_pSequencePower->GetChannel(t).SetIsExistTimer(true);
                            if(isSeqPwrWorking)
                                seqPwrSec->m_pSequencePower->GetChannel(t).SetIsInTiming(isSeqPwrWorking);

                            //printf("channel:%d,isExistTimer=%d,isInTimer=%d\n",seqPwrSec->m_pSequencePower->GetChannel(t).GetID(),\
                                    seqPwrSec->m_pSequencePower->GetChannel(t).GetIsExistTimer(),seqPwrSec->m_pSequencePower->GetChannel(t).GetIsInTiming());
                        }
                    }
                }
            }
        }
    }


    //查找系统中是否存在寻呼任务、消防任务、手动任务。
    bool IsExistZonePaging=false;
    bool IsExistZoneAlarm=false;
    bool IsExistZoneManualPlay=false;

#if 0
    bool IsQuickResponse=g_Global.m_Sections.IsExistZoneNeedQuickResponse_seqPwr();
#else
    bool IsQuickResponse=false; //20230814 取消之前存在任意分区处于网络音源时的快速响应，如果此标志位true，终端需要打开所有通道
#endif

    //发送到终端中
    for(int i=0;i<g_Global.m_SequencePower.GetSecCount();i++)
    {
        CSection &SecSeqPwr = g_Global.m_SequencePower.GetSection(i);
        if(SecSeqPwr.IsOnline())
            g_Global.m_Network.m_CmdSend.CmdSetSequencePowerTiming(SecSeqPwr, SecSeqPwr.m_pSequencePower,IsQuickResponse);
    }
}



bool  CTimerScheme::GetRunningTimePointAcChannel(vector<CTimePoint>& timerVec,const char *AudioCollectorMac,unsigned char channel)
{
    #if 0
    CScheme&	curScheme	= g_Global.m_TimerScheme.GetScheme(g_Global.m_TimerScheme.GetCurScheme());
    int	nTimerCount = curScheme.GetTimerCount();
    CTime tNow = CTime::GetCurrentTimeT();
    for (int i=0; i<nTimerCount; ++i)
    {
        CTimePoint& timePoint = curScheme.GetTimer(i);
        if(timePoint.GetSourceType() == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
        {
            ST_TIMER_AUDIO_COLLECTOR_INFO stAudioCollector=timePoint.GetAudioCollector();
            if( strcmp(AudioCollectorMac,stAudioCollector.mac) == 0 && stAudioCollector.channelId == channel)
            {
                if( IsTimeInRange(timePoint.GetTimeStart(), timePoint.GetTimeEnd(), tNow) )
                {
                    timerVec.push_back(timePoint);
                }
            }
        }
    }
    #endif

    vector<StTodayTimerP> TimerVec;
    CTime tNow = CTime::GetCurrentTimeT();
    GetTodayTimer(tNow,TimerVec);
    for(int i=0;i<TimerVec.size();i++)
    {
        if(TimerVec[i].status != TODAY_TIMER_STATUS_RUNNING)
            continue;
        CTimePoint& timePoint = TimerVec[i].TimerPoint;
        if(timePoint.GetSourceType() == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
        {
            ST_TIMER_AUDIO_COLLECTOR_INFO stAudioCollector=timePoint.GetAudioCollector();
            if( strcmp(AudioCollectorMac,stAudioCollector.mac) == 0 && stAudioCollector.channelId == channel)
            {
                if( CTimePoint::IsTimeInRange(timePoint.GetTimeStart(), timePoint.GetTimeEnd(), tNow) )
                {
                    timerVec.push_back(timePoint);
                }
            }
        }
    }

    if(timerVec.size()>0)
        return true;
    return false;
}