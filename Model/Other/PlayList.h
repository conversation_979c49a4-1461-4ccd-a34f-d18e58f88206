#ifndef PLAYLIST_H
#define PLAYLIST_H

#include <iostream>
#include <vector>
#include "Global/Const.h"
#include "Network/Protocol/Protocol.h"
#include "Tools/CMyString.h"
#include "Global/CType.h"
#include <QProcess>
#include <shared_mutex>
#include <memory>

using std::vector;

#define SONG_LOW_RATE_MAX_THREAD            4                   //码率转换最大并发数
#define SONG_LOW_RATE_STATUS_NOT_RUNNING    0
#define SONG_LOW_RATE_STATUS_RUNNING        1
#define SONG_LOW_RATE_STATUS_FINISH         2
#define SONG_LOW_RATE_STATUS_CRASH          3

#if APP_IS_LZY_LIMIT_STORAGE
    #define SONG_LOW_BITRATE                    64                 //歌曲低码率64kbps
#else
    #define SONG_LOW_BITRATE                    128                //歌曲低码率128kbps
#endif

#define SONG_LOW_RATE_128_EXT_NAME          "_lbrc.mp3"
#define SONG_LOW_RATE_128_EXT_TEMP_NAME     "_lbrc_temp.mp3"
#define SONG_LOW_RATE_64_EXT_NAME           "_lbrc64.mp3"
#define SONG_LOW_RATE_64_EXT_TEMP_NAME      "_lbrc64_temp.mp3"

#if (SONG_LOW_BITRATE == 128)
    #define SONG_LOW_RATE_EXT_NAME              SONG_LOW_RATE_128_EXT_NAME
    #define SONG_LOW_RATE_EXT_TEMP_NAME         SONG_LOW_RATE_128_EXT_TEMP_NAME
#elif (SONG_LOW_BITRATE == 64)
    #define SONG_LOW_RATE_EXT_NAME              SONG_LOW_RATE_64_EXT_NAME
    #define SONG_LOW_RATE_EXT_TEMP_NAME         SONG_LOW_RATE_64_EXT_TEMP_NAME
#endif


/************************************************************************/
/* 歌曲信息类                                                           */
/************************************************************************/


typedef struct
{
    CMyString songNamePath;
    int status;    //0-代表没由运行，1-代表正在运行   2-代表正常结束   3-代表异常退出
    bool needExit;  //需要退出
    bool alreadyExit;   //已经退出
    pthread_t pth;  //线程
    QProcess *qPro; 
}stBitRateInfo;

class CSong
{
public:
    //这行会有警告: 将在m_nSize后初始化
    //CSong(void):m_strPathName(""), m_nDuration(0), m_nSize(0), m_strName(""){		}
    CSong(CMyString strPathName="", int nDuration = 0, int nSize = 0);

public:
    CMyString		GetPathName(void)				{	return m_strPathName;			}
    void			SetDuration(int nDuration)		{	m_nDuration = nDuration;		}
    int				GetDuration(void)				{	return m_nDuration;				}
    void			SetSize(int nSize)				{	m_nSize = nSize;				}
    int				GetSize(void)					{	return m_nSize;					}
    void			SetLowRateSize(int nSize)	{	m_nLowRateSize = nSize;		    }
    int				GetLowRateSize(void)	    {	return m_nLowRateSize;			}
    CMyString		GetName(void)                   {	return m_strName;				}
    void			SetBitRate(int nBitRate)		{	m_bitrate = nBitRate;				}
    int				GetBitRate(void)				{	return m_bitrate;					}
    string			GetMd5(void)					{	return m_md5;					}
    void		    SetMd5(string strmd5)           {	m_md5 = strmd5;				}

    string          GetLbrcMd5(void)                {   return m_md5_lbrc;          }
    void            SetLbrcMd5(string strlbrcMd5)   {   m_md5_lbrc = strlbrcMd5;    }

    bool            SetLowRateFile(CMyString strPathName);
    CMyString       GetLowRateFile();
    bool			IsStreamFormat(void);	// 是否为可推送的格式(MP3和WAV)
    void			SetPathName(CMyString strPathName);
    bool			IsExist(void);
    void            SetAlive(bool IsAlive);
    bool            GetAlive(void);
    CMyString       GetUserAccount();
    void            SetUserAccount(CMyString strUserAccount);
#if SUPPORT_SONG_MANAGER
    void            SetAudit(bool hasAudit)          {   m_bHasAudit = hasAudit;         }
    bool            GetAudit(void)                   {   return m_bHasAudit;                }
#endif
private:
    CMyString		m_strPathName;		// 歌曲完整路径名称
    CMyString		m_strName;			// 歌曲名称(不含扩展名)
    int				m_nDuration;		// 歌曲时间长度
    int				m_nSize;			// 歌曲大小
    int				m_nLowRateSize;	    // lbrc歌曲大小
    int             m_bitrate;          // 歌曲比特率
    string          m_md5;              // 歌曲MD5值
    string          m_md5_lbrc;         // lbrc歌曲MD5值
    int             m_alive;            // 歌曲本地文件是否存在
    CMyString       m_strUserAccount;   // 文件归属账户，用于分区TTS文件，因为所有用户创建的TTS文件都在一个目录内
    CMyString       m_strLowRateFile;   // 是否存在低码率歌曲(MP3<=128kbps属于低码率,WAV文件统一转成MP3)
#if SUPPORT_SONG_MANAGER
    bool            m_bHasAudit;           // 是否有效（审核通过）
#endif
};


/************************************************************************/
/*  歌曲列表类                                                          */
/************************************************************************/

class CSongList
{
public:
    CSongList(CMyString strListName): m_strListName(strListName){ }
    CSongList(void)	: m_strListName("")	{ }
    ~CSongList(void);

public:
    void		SetID(CMyString strID)				{ m_strID = strID;							}
    CMyString	GetID(void)							{ return m_strID;							}
    void		SetDateTime(CMyString strDateTime)	{ m_strDateTime = strDateTime;				}
    CMyString	GetDateTime(void)					{ return m_strDateTime;						}
    void		SetListName(CMyString strListName)	{ m_strListName = strListName;				}
    CMyString	GetListName(void)					{ return m_strListName;						}
    CMyString   GetUserAccount()                    {  return m_strUserAccount;                 }
    void        SetUserAccount(CMyString strUserAccount);
    int			GetSongDuration(int nSong)			{ return m_Song[nSong].GetDuration();		}
    int			GetSongSize(int nSong)				{ return m_Song[nSong].GetSize();			}
    int			GetLowRateSongSize(int nSong)		{ return m_Song[nSong].GetLowRateSize();			}
    int         GetSongBitRate(int nSong)           { return m_Song[nSong].GetBitRate();        }
    string      GetSongMd5(int nSong)               { return m_Song[nSong].GetMd5();            }
    CMyString	GetLowRateFile(int nSong)			{ return m_Song[nSong].GetLowRateFile();		}
    int			GetSongCount(void)					{ return m_Song.size();						}
    CMyString	GetSongPathName(int nSong)			{ return m_Song[nSong].GetPathName();		}
    CMyString	GetSongName(int nSong)				{ return m_Song[nSong].GetName();			}
    CSong&		GetSong(int nSong)					{ return m_Song[nSong];						}
    bool		GetSongAlive(int nSong)             { return m_Song[nSong].GetAlive();			}
    CMyString	GetSongAccount(int nSong)           { return m_Song[nSong].GetUserAccount();			}
    bool		AddSong(CSong& song);
    void		RemoveSong(int nSong);
    void        RemoveSongByName(CMyString strPathName);
    void		RemoveAllSongs(void);

private:
    CMyString				m_strID;		// 列表ID（新建列表的唯一标识）
    CMyString				m_strListName;	// 列表名称
    CMyString				m_strDateTime;	// 最新更新日期时间
    CMyString               m_strUserAccount; //列表归属账户
    vector<CSong>           m_Song;			// 列表里歌曲信息
};


/************************************************************************/
/* 歌曲列表管理类                                                       */
/************************************************************************/

class CSongSync;


class CPlayList
{
public:
    CPlayList(void);
    ~CPlayList(void);

public:
    // 对成员变量操作
    void		SetSelList(int nSelList)			{ m_nSelList	= nSelList;		}
    int			GetSelList(void)					{ return m_nSelList;			}
    CSongList&	GetSongList(int nList)				{ return m_SongLists[nList];	}
    void		SetSelSong(int nSelSong)			{ m_nSelSong	= nSelSong;		}
    int			GetSelSong(void)					{ return m_nSelSong;			}
    void		SetDateTime(CMyString strDateTime)	{ m_strDateTime = strDateTime;	}
    CMyString	GetDateTime(void)					{ return m_strDateTime;			}
    PlayMode	GetPlayMode(void)					{ return m_PlayMode;			}
    void		SetPlayMode(PlayMode playMode)		{ m_PlayMode = playMode;		}
    CMyString   GetSongDirPath()                    { return m_strSongDirPath;      }
    void        SetSongDirPath(CMyString strSongDirPath)  { m_strSongDirPath = strSongDirPath; }
    bool        GetNeedNotify()                     { return m_bNeedNotify;     }
    void        SetNeedNotify(bool isNotify)        { m_bNeedNotify = isNotify; }

    // 对列表操作
    int			GetListCount(void);
    bool		AddList(CMyString strListName,CMyString strUserAccount);
    bool        AddList(CMyString strID, CMyString strListName,CMyString strUserAccount);
    bool		AddList(CSongList& songList);
    void		RemoveList(int nList);
    void		RemoveList(CMyString strUserAccount);
    void		ClearList(void);
    void        RemoveAllSongFile();
    void		ResetList(void);
    CMyString	GetListID(int nList);
    void		SetListID(int nList, CMyString strID);
    CMyString	GetListName(int nList);
    void		SetListName(int nList, CMyString strListName);
    CMyString	GetListDateTime(int nList);
    void		SetListDateTime(int nList, CMyString strDateTime);
    void        SetListUserAccount(int nList, CMyString strUserAccount);
    CMyString   GetListUserAccount(int nList);

    int        FindFirstIndexOfAccountList(CMyString strUserAccount);    //查找当前账户的第一个歌曲列表？(除TTS LIST外，如果没有列表，则返回-1）

    // 对音乐操作
    int         GetAllSongCount();
    CSong&		GetListSong(int nList, int nSong);
    int			GetListSongDuration(int nList, int nSong);
    int			GetListSongSize(int nList, int nSong);
    int			GetListSongLowRateSize(int nList, int nSong);
    int         GetListSongBitRate(int nList, int nSong);
    string      GetListSongMd5(int nList, int nSong);
    CMyString   GetListSongLowRateFile(int nList, int nSong);
    int			GetListSongCount(int nList);
    int         GetTTSListSongCountByAccount(int nList,CMyString strUserAccount);
    CMyString	GetListSongPathName(int nList, int nSong);
    CMyString	GetListSongName(int nList, int nSong);
    CMyString   GetSongUserAccount(int nList, int nSong);
    void		AddListSong(int nList, CSong& song);
    void		RemoveListSong(int nList, int nSong);
    void        RemoveListSong(int nList, CMyString strPathName);
    void		ClearListSong(int nList);
    bool        UpdateSongInfo(CSong& song);

    // 对音乐列表文件操作
    bool		ExportListFile(int nList, string strPathName);
    bool		ReadFile();
    bool		WriteFile(BOOL bUpdateDateTime = TRUE);

    bool		ReadFileBySpecAccount(CMyString strUserAccount);
    bool		WriteFileBySpecAccount(CMyString strUserAccount,BOOL bUpdateDateTime = TRUE);
    void        RemoveXmlFileOfSpecAccount(CMyString strUserAccount);

    // 从列表中查找歌曲
    bool        FindSongByList(CMyString strPathName, int nList);
    int         FindSongIdByList(CMyString strPathName, int nList);
    int			FindListByName(CMyString strListName);
    int			FindListByIdAndName(CMyString strListID, CMyString strListName);
    int			FindListByName(CMyString strListName, int nList);

    // 语言改变，更新播放列表
    bool		UpdateListName(CMyString strOldDefaultListName, bool bChimeList = FALSE);

    // 歌曲是否存在于列表中
    bool		IsSongInPlayList(CMyString strPathName);
    bool        IsSongInPlayList(int nListID, CMyString strPathName);
    void		FindSongInPlayList(CMyString strPathName, int& nList, int& nSong);
    int         GetNextSong(int nList, int nSong,int nPlayMode);

    bool        IsSongInPlayListBySpecAccount(CMyString strPathName,CMyString strUserAccount);  //指定歌曲是否存在于指定账户的歌曲列表中

    //int         GetNextSong(const char* szListID, const char* szSongUrl, int &nList, int &nSong,int nPlayMode);

    int         GetPreSong(int nList, int nSong,int nPlayMode);

    // 歌曲文件是否存在
    bool		IsSongFileExist(int nList, int nSong);

    // 歌曲是否为可推送格式
    bool		IsSongStreamFormat(int nList, int nSong);

    // 查找歌曲(List ID和带扩展名的歌曲名称)
    CSong*		FindSongByListAndName(CMyString strListID, CMyString strName, int& nListIndex, int& nSongIndex);

    // 是否为自己的列表
    bool		ExistOwnFile(void);
    void		SetFileName(CMyString strFileName);
    CMyString	GetFileName(void);
    CMyString	GetFileFolder(void);
    void		SetFileFolder(CMyString strFolder);
    CMyString	GetFileHttpPath(void);
    int			GetListByID(CMyString strID);

    // 确认歌曲是否有效
    bool		ConfirmListSong(int nList, int nSong);

    // 歌曲是否与列表中
    bool        IsExistInList(CMyString strFilePath);
    // 清除不在播放列表的歌曲文件
    void        ClearBesidesListSongFile();
    void        RemoveBesidesListSongFile(const char* szPath);
    void        TabListSongFileExist(const char *szPath,bool NeedGetSongInfo);
    void        UpdateUserPlaylist(CMyString strUserAccount);

    // 判断这个用户有几个列表
    int         GetSpecUserPlaylistCount(CMyString strUserAccount);


    static void* BitRateChangeTHD(void* lpParam);
    static void* BitchangeSingleTHD(void* lpParam);
    static void startBitchangeSingleTHD(CMyString strPathName);

    void        StartBitRateChangeTHD();

    void        AddLowRateSong(CMyString strPathName);
    void        DelLowRateSong(CMyString strPathName);
    stBitRateInfo* GetBitRateInfo(CMyString strPathName);
    bool		UpdateSongLowRateFile(CMyString strPathName);

    void        PushWriteXmlTask(CMyString strUserAccount);
    static void*       PlaylistXmlUpgradeThread(void* lpParam);
    void        StartPlaylistXmlUpgradeThread();

private:
    PlayMode			m_PlayMode;		// 播放模式
    int                 m_nSelList;     // 选中的列表
    int                 m_nSelSong;		// 选中的歌曲
    CMyString			m_strDateTime;	// 最新更新日期时间
    vector<CSongList>	m_SongLists;	// 列表信息
    CMyString			m_strFileName;	// 播放列表名称
    CMyString			m_strFolder;    // 文件夹

    bool                m_bNeedNotify;    // 是否需要通知Web终端
    CMyString           m_strSongDirPath; // 歌曲文件夹路径

public:
    vector<stBitRateInfo>   bitRateChangeSongs;
    int                   availLBrcThreadCnt; //可用线程数

    vector<CMyString>   m_vecPlaylistAccountChange;

public:
    pthread_mutex_t     PlaylistUpgradeMutex;
    //pthread_mutex_t     LowRateSongsModQmutex;
    //shared_timed_mutex  LowRateSongsModQSharedMutex;
    std::shared_ptr<std::shared_timed_mutex> LowRateSongsModQSharedMutex = std::make_shared<std::shared_timed_mutex>();
    pthread_mutex_t     PlaylistXmlUpgradeMutex;
};


#if SUPPORT_SONG_MANAGER
class CSongManager
{
public:
    //CPlayList(void);
    //~CPlayList(void);

    
public:
    int     GetSongsCount(void);
    CSong* GetSongByMd5(string md5);
    string GetMd5ByPathName(string pathName);
    bool    AddSong(CSong& song);
    bool    DeleteSong(CMyString strPathName,bool removeFile);
    bool    DeleteAllSongOfAccount(CMyString strUserAccount);
    bool    UpdateSong(CMyString strPathName,CSong& song);
    bool    GetSongByPathName(CMyString strPathName,CSong& song);
    CSong* GetSongByPathName(CMyString strPathName);
    void    ScanLocalSong();
    void	SetDateTime(CMyString strDateTime)	{ m_strDateTime = strDateTime;	}
    CMyString	GetDateTime(void)	{ return m_strDateTime;	}
    bool    ReadFile();
    void    Reset();
    void    Init();
public:
    bool    WriteFile(bool bUpdateDateTime = TRUE);
    UINT64   GetSongsUsedSpaceByUser(CMyString strUserAccount);

public:
    vector<CSong>       m_Songs;        // 歌曲集合
    
private:
    CMyString			m_strDateTime;	// 最新更新日期时间
};

#endif

#endif // PLAYLIST_H
