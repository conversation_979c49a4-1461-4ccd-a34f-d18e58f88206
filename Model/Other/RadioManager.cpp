#include "stdafx.h"
#include "RadioManager.h"
#include "Global/GlobalData.h"
#include "Tools/CJSONRW.h"
#include "Tools/MyCFile.h"
#include "Tools/base64.h"
#include "Tools/sha1.h"
#include "Tools/fmt/format.h"
#include <fstream>
#include <sstream>
#include <functional>

// 静态成员定义
const string CRadioManager::ENCRYPT_PREFIX = "ENCRYPTED_RADIO_";
static const std::string XXTEA_KEY = "MhitechRadioKey0";   // 16 字节

extern string GetPresetRadioDataJson();
extern string GetPresetProvinceDataJson();

CRadioManager::CRadioManager()
{
    m_nextRadioId = 10000; // 自定义电台ID从10000开始
}

CRadioManager::~CRadioManager()
{
    Clear();
}

bool CRadioManager::InitRadioData()
{
    m_customRadioFilePath = string(g_Global.m_strFolderPath.Data()) + "/" + HTTP_FOLDER_ADATA + "/radio_custom.json";
    // 加载预置电台数据
    if (!LoadPresetRadioData())
    {
        LOG("加载预置电台数据失败", LV_ERROR);
        return false;
    }
    
    // 加载自定义电台数据
    if (!LoadCustomRadioData())
    {
        LOG("加载自定义电台数据失败", LV_WARNING);
        // 自定义电台数据加载失败不影响系统启动
    }
    
    #if 0
    LOG(FORMAT("电台数据初始化完成，预置电台: %d个，自定义电台: %d个", 
        m_presetRadioList.size(), m_customRadioList.size()), LV_INFO);
    #endif
#if 0    
    //解码测试
    string test_decrypt_url = DecryptRadioUrl("ENCRYPTED_RADIO_gZ9Q2fvvtxFw4v3ykbFMUdwDF9/9pMGqEXrAmZR4NtJH06LAJ+qrw4mIy/lIkdXr7lGkLQ==");
    printf("test_decrypt_url=%s\n",test_decrypt_url.data());
#endif

    return true;
}

bool CRadioManager::LoadPresetRadioData()
{
    // 使用硬编码的预置电台数据
    string radioContent = ::GetPresetRadioDataJson();
    if (!ParseRadioDataJson(radioContent, m_presetRadioList))
    {
        LOG("解析预置电台数据失败", LV_ERROR);
        return false;
    }
    
    // 使用硬编码的预置分组数据
    string provinceContent = ::GetPresetProvinceDataJson();
    if (!ParseProvinceDataJson(provinceContent, m_radioGroupList))
    {
        LOG("解析预置分组数据失败", LV_ERROR);
        return false;
    }
    
    return true;
}

bool CRadioManager::LoadCustomRadioData()
{
    MyCFile customFile;
    if (!customFile.Open(m_customRadioFilePath.c_str(), "r"))
    {
        // 文件不存在是正常的，创建空文件
        LOG("自定义电台文件不存在，将创建新文件", LV_INFO);
        return SaveCustomRadioData();
    }
    
    int fileSize = customFile.GetLength();
    char* buffer = new char[fileSize + 1];
    customFile.Read(buffer, fileSize);
    buffer[fileSize] = '\0';
    string customContent(buffer);
    delete[] buffer;
    customFile.Close();
    
    if (customContent.empty())
    {
        return true; // 空文件是正常的
    }
    
    return ParseRadioDataJson(customContent, m_customRadioList);
}

bool CRadioManager::SaveCustomRadioData()
{
    string jsonContent = GenerateCustomRadioJson();
    
    MyCFile customFile;
    if (!customFile.Open(m_customRadioFilePath.c_str(), "w"))
    {
        LOG(FORMAT("无法创建自定义电台文件: %s", m_customRadioFilePath.c_str()), LV_ERROR);
        return false;
    }
    
    customFile.Write(jsonContent.c_str(), jsonContent.length());
    customFile.Close();
    
    LOG("自定义电台数据保存成功", LV_INFO);
    return true;
}

bool CRadioManager::AddRadioInfo(const RadioInfo& radioInfo)
{
    // 验证是否为自定义分组
    if (radioInfo.groupId != CUSTOM_GROUP_ID)
    {
        LOG("只能在自定义分组中添加电台", LV_ERROR);
        return false;
    }
    
    // 验证电台信息
    if (!ValidateRadioInfo(radioInfo))
    {
        LOG("电台信息验证失败", LV_ERROR);
        return false;
    }
    
    // 检查电台名称是否重复
    for (const auto& radio : m_customRadioList)
    {
        if (radio.name == radioInfo.name)
        {
            LOG(FORMAT("电台名称已存在: %s", radioInfo.name.c_str()), LV_ERROR);
            return false;
        }
    }
    
    // 创建新电台信息
    RadioInfo newRadio = radioInfo;
    newRadio.id = GetNextRadioId();
    newRadio.groupId = CUSTOM_GROUP_ID;
    newRadio.groupName = "自定义";
    
    // 添加到自定义电台列表
    m_customRadioList.push_back(newRadio);
    
    // 保存到文件
    if (!SaveCustomRadioData())
    {
        // 保存失败，回滚
        m_customRadioList.pop_back();
        return false;
    }
    
    LOG(FORMAT("成功添加自定义电台: %s", newRadio.name.c_str()), LV_INFO);
    return true;
}

bool CRadioManager::EditRadioInfo(int radioId, const RadioInfo& radioInfo)
{
    // 查找要编辑的电台
    auto it = std::find_if(m_customRadioList.begin(), m_customRadioList.end(),
        [radioId](const RadioInfo& radio) { return radio.id == radioId; });
    
    if (it == m_customRadioList.end())
    {
        LOG(FORMAT("未找到要编辑的电台ID: %d", radioId), LV_ERROR);
        return false;
    }
    
    // 验证是否为自定义分组
    if (it->groupId != CUSTOM_GROUP_ID)
    {
        LOG("只能编辑自定义分组中的电台", LV_ERROR);
        return false;
    }
    
    // 验证电台信息
    if (!ValidateRadioInfo(radioInfo))
    {
        LOG("电台信息验证失败", LV_ERROR);
        return false;
    }
    
    // 检查电台名称是否与其他电台重复
    for (const auto& radio : m_customRadioList)
    {
        if (radio.id != radioId && radio.name == radioInfo.name)
        {
            LOG(FORMAT("电台名称已存在: %s", radioInfo.name.c_str()), LV_ERROR);
            return false;
        }
    }
    
    // 保存原始信息用于回滚
    RadioInfo originalRadio = *it;
    
    // 更新电台信息
    it->name = radioInfo.name;
    it->url = radioInfo.url;
    it->forbidden = radioInfo.forbidden;
    it->createTime = radioInfo.createTime;
    #if 1 //编辑电台不改变创建用户，暂时不处理
    it->creatorUserName = radioInfo.creatorUserName;
    #endif
    
    // 保存到文件
    if (!SaveCustomRadioData())
    {
        // 保存失败，回滚
        *it = originalRadio;
        return false;
    }
    
    LOG(FORMAT("成功编辑自定义电台: %s", it->name.c_str()), LV_INFO);
    return true;
}

bool CRadioManager::DeleteRadioInfo(int radioId)
{
    // 查找要删除的电台
    auto it = std::find_if(m_customRadioList.begin(), m_customRadioList.end(),
        [radioId](const RadioInfo& radio) { return radio.id == radioId; });
    
    if (it == m_customRadioList.end())
    {
        LOG(FORMAT("未找到要删除的电台ID: %d", radioId), LV_ERROR);
        return false;
    }
    
    // 验证是否为自定义分组
    if (it->groupId != CUSTOM_GROUP_ID)
    {
        LOG("只能删除自定义分组中的电台", LV_ERROR);
        return false;
    }
    
    // 保存电台信息用于回滚
    RadioInfo deletedRadio = *it;
    
    // 从列表中删除
    m_customRadioList.erase(it);
    
    // 保存到文件
    if (!SaveCustomRadioData())
    {
        // 保存失败，回滚
        m_customRadioList.push_back(deletedRadio);
        return false;
    }
    
    LOG(FORMAT("成功删除自定义电台: %s", deletedRadio.name.c_str()), LV_INFO);
    return true;
}

vector<RadioGroupInfo> CRadioManager::GetRadioGroupList()
{
    return m_radioGroupList;
}

vector<RadioInfo> CRadioManager::GetRadioListByGroupId(int groupId, bool needEncrypt)
{
    vector<RadioInfo> result;
    #if 0
    LOG(fmt::format("GetRadioListByGroupId: groupId={}, 总分组数={}, 总电台数={}", 
        groupId, m_radioGroupList.size(), m_presetRadioList.size()).c_str(), LV_INFO);
    #endif
    if (groupId == CUSTOM_GROUP_ID)
    {
        // 自定义分组，返回自定义电台列表，无需加密
        result = m_customRadioList;
        //LOG(fmt::format("返回自定义电台数量: {}", result.size()).c_str(), LV_INFO);
    }
    else
    {
        // 预置分组，从预置电台列表中筛选当前分组的电台
        int currentGroupRadioCount = 0;
        for (const auto& radio : m_presetRadioList)
        {
            if (radio.groupId == groupId)
            {
                RadioInfo radioInfo = radio;
                // 如果需要加密且不是自定义分组，则加密URL
                if (needEncrypt)
                {
                    radioInfo.url = EncryptRadioUrl(radio.url);
                }
                result.push_back(radioInfo);
                currentGroupRadioCount++;
            }
        }
        //LOG(fmt::format("当前分组{}的电台数量: {}", groupId, currentGroupRadioCount).c_str(), LV_INFO);
        
        // 递归获取所有子分组的电台
        function<void(const vector<RadioGroupInfo>&)> getChildGroupRadios = [&](const vector<RadioGroupInfo>& groups) {
            for (const auto& group : groups)
            {
                #if 0
                LOG(fmt::format("处理子分组: groupId={}, name={}, 子分组数={}",
                    group.groupId, group.name, group.children.size()).c_str(), LV_INFO);
                #endif
                // 获取当前分组的电台
                int childGroupRadioCount = 0;
                for (const auto& radio : m_presetRadioList)
                {
                    if (radio.groupId == group.groupId)
                    {
                        RadioInfo radioInfo = radio;
                        if (needEncrypt)
                        {
                            radioInfo.url = EncryptRadioUrl(radio.url);
                        }
                        result.push_back(radioInfo);
                        childGroupRadioCount++;
                    }
                }
                //LOG(fmt::format("子分组{}的电台数量: {}", group.groupId, childGroupRadioCount).c_str(), LV_INFO);
                
                // 递归处理子分组
                if (!group.children.empty())
                {
                    getChildGroupRadios(group.children);
                }
            }
        };
        
        // 查找指定分组并获取其子分组电台
        function<void(const vector<RadioGroupInfo>&, int)> findAndProcessGroup = [&](const vector<RadioGroupInfo>& groups, int targetGroupId) {
            //LOG(fmt::format("在{}个分组中查找目标分组{}", groups.size(), targetGroupId).c_str(), LV_INFO);
            for (const auto& group : groups)
            {
                //LOG(fmt::format("检查分组: groupId={}, name={}", group.groupId, group.name).c_str(), LV_INFO);
                if (group.groupId == targetGroupId)
                {
                    //LOG(fmt::format("找到目标分组{}, 子分组数={}", targetGroupId, group.children.size()).c_str(), LV_INFO);
                    // 找到目标分组，处理其子分组
                    if (!group.children.empty())
                    {
                        getChildGroupRadios(group.children);
                    }
                    else
                    {
                        //LOG(fmt::format("目标分组{}没有子分组", targetGroupId).c_str(), LV_INFO);
                    }
                    return;
                }
                
                // 在子分组中继续查找
                if (!group.children.empty())
                {
                    findAndProcessGroup(group.children, targetGroupId);
                }
            }
        };
        
        // 开始查找并处理指定分组的子分组电台
        findAndProcessGroup(m_radioGroupList, groupId);
    }
    
    //LOG(fmt::format("GetRadioListByGroupId最终返回电台数量: {}", result.size()).c_str(), LV_INFO);
    return result;
}

std::string CRadioManager::EncryptRadioUrl(const std::string& url)
{
    if (url.empty()) return url;

    // 1. XXTEA 加密
    std::string cipher = xxtea_encrypt(url, XXTEA_KEY);

    // 2. Base64 编码
    std::string encoded = base64_encode(
        reinterpret_cast<const unsigned char*>(cipher.c_str()), cipher.size());

    return ENCRYPT_PREFIX + encoded;
}

std::string CRadioManager::DecryptRadioUrl(const std::string& encryptedUrl)
{
    if (!IsEncryptedUrl(encryptedUrl))
        return encryptedUrl;

    // 去掉前缀
    std::string encoded = encryptedUrl.substr(ENCRYPT_PREFIX.length());

    // 1. Base64 解码
    std::string cipher = base64_decode(encoded);
    if (cipher.empty()) return "";

    // 2. XXTEA 解密
    std::string plain = xxtea_decrypt(cipher, XXTEA_KEY);

    // 去掉可能的零填充
    size_t n = plain.find_last_not_of('\0');
    if (n != std::string::npos)
        plain.resize(n + 1);
    return plain;
}

bool CRadioManager::IsEncryptedUrl(const string& url)
{
    return url.find(ENCRYPT_PREFIX) == 0;
}

int CRadioManager::GetNextRadioId()
{
    // 确保ID不与现有电台冲突
    while (true)
    {
        bool idExists = false;
        
        // 检查预置电台
        for (const auto& radio : m_presetRadioList)
        {
            if (radio.id == m_nextRadioId)
            {
                idExists = true;
                break;
            }
        }
        
        // 检查自定义电台
        if (!idExists)
        {
            for (const auto& radio : m_customRadioList)
            {
                if (radio.id == m_nextRadioId)
                {
                    idExists = true;
                    break;
                }
            }
        }
        
        if (!idExists)
        {
            return m_nextRadioId++;
        }
        
        m_nextRadioId++;
    }
}

void CRadioManager::Clear()
{
    m_presetRadioList.clear();
    m_customRadioList.clear();
    m_radioGroupList.clear();
}

bool CRadioManager::ParseRadioDataJson(const string& jsonContent, vector<RadioInfo>& radioList)
{
    cJSON* root = cJSON_Parse(jsonContent.c_str());
    if (!root)
    {
        LOG("解析电台数据JSON失败", LV_ERROR);
        return false;
    }
    
    cJSON* listArray = cJSON_GetObjectItem(root, "list");
    if (!listArray || !cJSON_IsArray(listArray))
    {
        LOG("电台数据JSON格式错误：缺少list数组", LV_ERROR);
        cJSON_Delete(root);
        return false;
    }
    
    int arraySize = cJSON_GetArraySize(listArray);
    for (int i = 0; i < arraySize; i++)
    {
        cJSON* radioItem = cJSON_GetArrayItem(listArray, i);
        if (!radioItem)
            continue;
            
        RadioInfo radioInfo;
        
        cJSON* idItem = cJSON_GetObjectItem(radioItem, "id");
        if (idItem && cJSON_IsNumber(idItem))
            radioInfo.id = idItem->valueint;
            
        cJSON* groupIdItem = cJSON_GetObjectItem(radioItem, "groupId");
        if (groupIdItem && cJSON_IsNumber(groupIdItem))
            radioInfo.groupId = groupIdItem->valueint;
            
        cJSON* nameItem = cJSON_GetObjectItem(radioItem, "name");
        if (nameItem && cJSON_IsString(nameItem))
            radioInfo.name = nameItem->valuestring;
            
        cJSON* urlItem = cJSON_GetObjectItem(radioItem, "url");
        if (urlItem && cJSON_IsString(urlItem))
            radioInfo.url = urlItem->valuestring;
            
        cJSON* forbiddenItem = cJSON_GetObjectItem(radioItem, "forbidden");
        if (forbiddenItem && cJSON_IsBool(forbiddenItem))
            radioInfo.forbidden = cJSON_IsTrue(forbiddenItem);
            
        cJSON* createTimeItem = cJSON_GetObjectItem(radioItem, "createTime");
        if (createTimeItem && cJSON_IsString(createTimeItem))
            radioInfo.createTime = createTimeItem->valuestring;
            
        cJSON* groupNameItem = cJSON_GetObjectItem(radioItem, "groupName");
        if (groupNameItem && cJSON_IsString(groupNameItem))
            radioInfo.groupName = groupNameItem->valuestring;
            
        cJSON* creatorUserItem = cJSON_GetObjectItem(radioItem, "creatorUser");
        if (creatorUserItem)
        {
            cJSON* userNameItem = cJSON_GetObjectItem(creatorUserItem, "userName");
            if (userNameItem && cJSON_IsString(userNameItem))
                radioInfo.creatorUserName = userNameItem->valuestring;
        }
        
        radioList.push_back(radioInfo);
        
        // 更新最大ID
        if (radioInfo.id >= m_nextRadioId)
        {
            m_nextRadioId = radioInfo.id + 1;
        }
    }
    
    cJSON_Delete(root);
    return true;
}

bool CRadioManager::ParseProvinceDataJson(const string& jsonContent, vector<RadioGroupInfo>& groupList)
{
    cJSON* root = cJSON_Parse(jsonContent.c_str());
    if (!root)
    {
        LOG("解析省份数据JSON失败", LV_ERROR);
        return false;
    }
    
    // 递归解析分组函数，构建层级结构
    function<RadioGroupInfo(cJSON*)> parseGroupRecursive = [&](cJSON* groupItem) -> RadioGroupInfo {
        RadioGroupInfo groupInfo;
        
        if (!groupItem) return groupInfo;
        
        cJSON* keyItem = cJSON_GetObjectItem(groupItem, "key");
        if (keyItem && cJSON_IsString(keyItem))
            groupInfo.key = keyItem->valuestring;
            
        cJSON* groupIdItem = cJSON_GetObjectItem(groupItem, "groupId");
        if (groupIdItem && cJSON_IsNumber(groupIdItem))
            groupInfo.groupId = groupIdItem->valueint;
            
        cJSON* pidItem = cJSON_GetObjectItem(groupItem, "pid");
        if (pidItem && cJSON_IsNumber(pidItem))
            groupInfo.pid = pidItem->valueint;
            
        cJSON* nameItem = cJSON_GetObjectItem(groupItem, "name");
        if (nameItem && cJSON_IsString(nameItem))
            groupInfo.name = nameItem->valuestring;
        
        // 递归处理子分组
        cJSON* childrenArray = cJSON_GetObjectItem(groupItem, "children");
        if (childrenArray && cJSON_IsArray(childrenArray))
        {
            int childArraySize = cJSON_GetArraySize(childrenArray);
            for (int j = 0; j < childArraySize; j++)
            {
                cJSON* childGroupItem = cJSON_GetArrayItem(childrenArray, j);
                RadioGroupInfo childGroup = parseGroupRecursive(childGroupItem);
                if (!childGroup.name.empty()) // 确保子分组有效
                {
                    childGroup.pid = groupInfo.groupId; // 设置父分组ID
                    groupInfo.children.push_back(childGroup);
                }
            }
        }
        
        return groupInfo;
    };
    
    cJSON* childrenArray = cJSON_GetObjectItem(root, "children");
    if (!childrenArray || !cJSON_IsArray(childrenArray))
    {
        LOG("省份数据JSON格式错误：缺少children数组", LV_ERROR);
        cJSON_Delete(root);
        return false;
    }
    
    int arraySize = cJSON_GetArraySize(childrenArray);
    for (int i = 0; i < arraySize; i++)
    {
        cJSON* groupItem = cJSON_GetArrayItem(childrenArray, i);
        RadioGroupInfo group = parseGroupRecursive(groupItem);
        if (!group.name.empty()) // 确保分组有效
        {
            group.pid = 0; // 顶级分组的父ID为0
            groupList.push_back(group);
        }
    }
    
    cJSON_Delete(root);
    return true;
}

string CRadioManager::GenerateCustomRadioJson()
{
    cJSON* root = cJSON_CreateObject();
    cJSON* listArray = cJSON_CreateArray();
    
    for (const auto& radio : m_customRadioList)
    {
        cJSON* radioItem = cJSON_CreateObject();
        
        cJSON_AddNumberToObject(radioItem, "id", radio.id);
        cJSON_AddNumberToObject(radioItem, "groupId", radio.groupId);
        cJSON_AddStringToObject(radioItem, "name", radio.name.c_str());
        cJSON_AddStringToObject(radioItem, "url", radio.url.c_str());
        cJSON_AddBoolToObject(radioItem, "forbidden", radio.forbidden);
        cJSON_AddStringToObject(radioItem, "createTime", radio.createTime.c_str());
        cJSON_AddStringToObject(radioItem, "groupName", radio.groupName.c_str());
        
        cJSON* creatorUser = cJSON_CreateObject();
        cJSON_AddStringToObject(creatorUser, "userName", radio.creatorUserName.c_str());
        cJSON_AddItemToObject(radioItem, "creatorUser", creatorUser);
        
        cJSON_AddItemToArray(listArray, radioItem);
    }
    
    cJSON_AddItemToObject(root, "list", listArray);
    
    char* jsonString = cJSON_Print(root);
    string result = jsonString;
    
    free(jsonString);
    cJSON_Delete(root);
    
    return result;
}

bool CRadioManager::ValidateRadioInfo(const RadioInfo& radioInfo)
{
    if (radioInfo.name.empty())
    {
        LOG("电台名称不能为空", LV_ERROR);
        return false;
    }
    
    if (radioInfo.name.length() > 100)
    {
        LOG("电台名称过长", LV_ERROR);
        return false;
    }
    
    if (radioInfo.url.empty())
    {
        LOG("电台URL不能为空", LV_ERROR);
        return false;
    }
    
    if (radioInfo.url.length() > 500)
    {
        LOG("电台URL过长", LV_ERROR);
        return false;
    }
    
    // 简单的URL格式验证
    if (radioInfo.url.find("http://") != 0 && radioInfo.url.find("https://") != 0)
    {
        LOG("电台URL格式错误，必须以http://或https://开头", LV_ERROR);
        return false;
    }
    
    return true;
}