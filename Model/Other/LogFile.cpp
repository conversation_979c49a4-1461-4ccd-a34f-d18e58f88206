#include "stdafx.h"
#include "LogFile.h"

CLogFile::CLogFile()
{
    memset(m_szName, 0,sizeof(m_szName));

    m_nSize			= 0;
    m_nRecvSize		= 0;
    m_nPackCount	= 0;
    m_nRecvPackID	= 0;

    m_pData = NULL;
}

CLogFile::~CLogFile()
{
    if (m_pData != NULL)
    {
        delete[] m_pData;
        m_pData = NULL;
    }
}


bool	CLogFile::HasFinished(void)
{
    return (m_nRecvPackID == m_nPackCount);
}

unsigned short	CLogFile::NextPackID(void)
{
    if (m_nRecvPackID >= m_nPackCount)
    {
        return 0;
    }

    return (m_nRecvPackID + 1);
}


void	CLogFiles::AddLogFile(CLogFile& logFile)
{
    m_LogFiles.push_back(logFile);
}


void	CLogFiles::Clear(void)
{
    m_LogFiles.clear();
}


CLogFile*	CLogFiles::FindLogFile(const char* szFileName)
{
    int nFileCount = GetLogFileCount();

    for (int i=0; i<nFileCount; ++i)
    {
        if (strcmp(m_LogFiles[i].m_szName, szFileName) == 0)
        {
            return &m_LogFiles[i];
        }
    }

    return NULL;
}


void	CLogFiles::CreateLogFileDirectory(const char* szMac)   //,
                                           //const char* szName)
{
    // 创建日志文件夹  ../Download/LogFile/NameLog_MAC zhuyg
    CMyString strDir = GetLogFileDirectory(szMac);   //, szName);

    CreateDirectoryQ(strDir.C_Str());

}

// zhuyg
CMyString	CLogFiles::GetLogFileDirectory(const char* szMac)  // ,
                                           // const char* szName)
{
    CMyString strDir;

    CMyString strMac(szMac);
    strMac.Replace(":", "-");

    strDir.Format("%s/%s/%s/Log_%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, FOLDER_DOWNLOAD, strMac.C_Str());

    return strDir;
}

bool CLogFiles::IsNeedDownload(const char *szMac, const char *szName)
{
    CMyString strFilePath;
    strFilePath.Format("%s/%s", GetLogFileDirectory(szMac).C_Str(), szName);

    bool bNeedDowd = TRUE;
    if(IsExistFile(strFilePath.C_Str()))
    {
        MyCFile file;
        if(file.Open(strFilePath.C_Str(), "rb+"))
        {
            CLogFile* pLogFile = FindLogFile(szName);
            if(pLogFile != NULL)
            {
                if(pLogFile->m_nSize != file.GetLength())
                {
                    bNeedDowd = FALSE;
                }
            }
        }
    }

    return bNeedDowd;
}



