#include <string>
using namespace std;

// 获取预置电台数据JSON（硬编码）
string GetPresetRadioDataJson()
{
    return R"({
	"list": [
		{
		  "id": 1,
		  "groupId": 1,
		  "name": "北京新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1994/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "北京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 2,
		  "groupId": 1,
		  "name": "北京交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/93/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "北京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 3,
		  "groupId": 1,
		  "name": "北京文艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/94/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "北京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 4,
		  "groupId": 1,
		  "name": "北京体育广播",
		  "url": "https://lhttp.qtfm.cn/live/335/64k.mp3?app_id=web",
		  "forbidden": true,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "北京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 5,
		  "groupId": 1,
		  "name": "北京音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/95/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "北京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 6,
		  "groupId": 1,
		  "name": "北京城市广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/97/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "北京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 7,
		  "groupId": 1,
		  "name": "京津冀之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/91/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "北京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 8,
		  "groupId": 1,
		  "name": "北京大兴人民广播电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2632/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "北京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 9,
		  "groupId": 1,
		  "name": "北京新城1077",
		  "url": "https://lhttp.qtfm.cn/live/20500023/64k.mp3?app_id=web",
		  "forbidden": true,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "北京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 10,
		  "groupId": 1,
		  "name": "经典调频北京FM969",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/963/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "北京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 11,
		  "groupId": 2,
		  "name": "天津TIKI FM100.5",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/121/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "天津",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 12,
		  "groupId": 2,
		  "name": "经典FM1008",
		  "url": "https://lhttp.qtfm.cn/live/20212227/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "天津",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 13,
		  "groupId": 2,
		  "name": "962汽车音乐台",
		  "url": "https://lhttp.qtfm.cn/live/20500044/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "天津",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 14,
		  "groupId": 3,
		  "name": "上海新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/58/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "上海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 15,
		  "groupId": 3,
		  "name": "第一财经广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/56/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "上海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 16,
		  "groupId": 3,
		  "name": "上海Love Radio",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/55/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "上海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 17,
		  "groupId": 3,
		  "name": "上海动感101",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/53/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "上海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 18,
		  "groupId": 3,
		  "name": "上海五星体育",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/57/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "上海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 19,
		  "groupId": 3,
		  "name": "长三角之声",
		  "url": "https://lhttp.qtfm.cn/live/275/64k.mp3?app_id=web",
		  "forbidden": true,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "上海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 20,
		  "groupId": 3,
		  "name": "上海交通广播电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/59/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "上海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 21,
		  "groupId": 3,
		  "name": "上海经典947",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/54/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "上海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 22,
		  "groupId": 3,
		  "name": "上海戏曲广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/60/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "上海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 23,
		  "groupId": 3,
		  "name": "上海沸点100音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1236/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "上海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 24,
		  "groupId": 3,
		  "name": "上海KFM981",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1661/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "上海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 25,
		  "groupId": 3,
		  "name": "上海故事广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/61/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "上海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 26,
		  "groupId": 3,
		  "name": "闵行人民广播电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1731/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "上海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 27,
		  "groupId": 3,
		  "name": "东上海之声FM106.5",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1239/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "上海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 28,
		  "groupId": 3,
		  "name": "金山区广播电视台综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2044/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "上海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 29,
		  "groupId": 3,
		  "name": "崇明区广播电视台综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2774/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "上海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 30,
		  "groupId": 4,
		  "name": "重庆之声",
		  "url": "https://lhttp.qtfm.cn/live/1498/64k.mp3?app_id=web",
		  "forbidden": true,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "重庆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 31,
		  "groupId": 4,
		  "name": "重庆交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/130/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "重庆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 32,
		  "groupId": 4,
		  "name": "重庆音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/131/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "重庆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 33,
		  "groupId": 4,
		  "name": "938重庆私家车广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/132/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "重庆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 34,
		  "groupId": 4,
		  "name": "重庆嘉陵之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2411/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "重庆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 35,
		  "groupId": 4,
		  "name": "重庆生活1015",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/129/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "重庆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 36,
		  "groupId": 4,
		  "name": "重庆巴渝之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2795/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "重庆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 37,
		  "groupId": 4,
		  "name": "重庆永川之声FM00.7",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2749/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "重庆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 38,
		  "groupId": 4,
		  "name": "大足人民广播电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2770/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "重庆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 39,
		  "groupId": 4,
		  "name": "重庆江津广播电台FM98.7",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2519/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "重庆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 40,
		  "groupId": 4,
		  "name": "万州新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1678/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "重庆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 41,
		  "groupId": 4,
		  "name": "万州交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1679/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "重庆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 42,
		  "groupId": 4,
		  "name": "梁平之声",
		  "url": "https://lhttp.qtfm.cn/live/20211646/64k.mp3?app_id=web",
		  "forbidden": true,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "重庆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 43,
		  "groupId": 4,
		  "name": "荣昌综合广播",
		  "url": "https://lhttp.qtfm.cn/live/20500107/64k.mp3?app_id=web",
		  "forbidden": true,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "重庆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 44,
		  "groupId": 5,
		  "name": "河北新闻广播",
		  "url": "https://lhttp.qtfm.cn/live/1644/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "河北",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 45,
		  "groupId": 5,
		  "name": "河北音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/494/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "河北",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 46,
		  "groupId": 5,
		  "name": "河北交通广播",
		  "url": "https://lhttp.qtfm.cn/live/1646/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "河北",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 47,
		  "groupId": 5,
		  "name": "河北综合广播",
		  "url": "https://lhttp.qtfm.cn/live/20500111/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "河北",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 48,
		  "groupId": 34,
		  "name": "石家庄新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/502/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "石家庄",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 49,
		  "groupId": 35,
		  "name": "邯郸新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/514/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "邯郸",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 50,
		  "groupId": 34,
		  "name": "石家庄交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/505/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "石家庄",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 51,
		  "groupId": 35,
		  "name": "邯郸都市生活广播",
		  "url": "https://lhttp.qtfm.cn/live/3951/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "邯郸",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 52,
		  "groupId": 35,
		  "name": "邯郸交通广播",
		  "url": "https://lhttp.qtfm.cn/live/3950/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "邯郸",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 53,
		  "groupId": 5,
		  "name": "河北文艺广播",
		  "url": "https://lhttp.qtfm.cn/live/4868/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "河北",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 54,
		  "groupId": 34,
		  "name": "石家庄音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/504/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "石家庄",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 55,
		  "groupId": 5,
		  "name": "河北农民广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/500/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "河北",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 56,
		  "groupId": 5,
		  "name": "河北生活广播",
		  "url": "https://lhttp.qtfm.cn/live/4867/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "河北",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 57,
		  "groupId": 35,
		  "name": "邯郸音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1125/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "邯郸",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 58,
		  "groupId": 36,
		  "name": "张家口交通广播",
		  "url": "https://lhttp.qtfm.cn/live/5021910/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "张家口",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 59,
		  "groupId": 36,
		  "name": "张家口986城市生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/526/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "张家口",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 60,
		  "groupId": 36,
		  "name": "张家口旅游广播",
		  "url": "https://lhttp.qtfm.cn/live/5021507/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "张家口",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 61,
		  "groupId": 36,
		  "name": "张家口1074综合广播",
		  "url": "https://lhttp.qtfm.cn/live/15318285/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "张家口",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 62,
		  "groupId": 6,
		  "name": "河南新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/461/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "河南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 63,
		  "groupId": 6,
		  "name": "河南交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/463/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "河南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 64,
		  "groupId": 6,
		  "name": "河南音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/465/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "河南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 65,
		  "groupId": 6,
		  "name": "河南经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/462/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "河南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 66,
		  "groupId": 6,
		  "name": "河南教育广播",
		  "url": "https://lhttp.qtfm.cn/live/1207/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "河南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 67,
		  "groupId": 37,
		  "name": "郑州新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/470/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "郑州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 68,
		  "groupId": 37,
		  "name": "郑州经济生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/471/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "郑州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 69,
		  "groupId": 37,
		  "name": "郑州交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/474/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "郑州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 70,
		  "groupId": 37,
		  "name": "郑州私家车广播FM91.8",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/472/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "郑州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 71,
		  "groupId": 38,
		  "name": "开封交通旅游广播",
		  "url": "https://lhttp.qtfm.cn/live/1214/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "开封",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 72,
		  "groupId": 37,
		  "name": "郑州音乐广播FM94.4",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/475/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "郑州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 73,
		  "groupId": 38,
		  "name": "开封音乐广播",
		  "url": "https://lhttp.qtfm.cn/live/4569/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "开封",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 74,
		  "groupId": 38,
		  "name": "开封综合广播",
		  "url": "https://lhttp.qtfm.cn/live/5022653/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "开封",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 75,
		  "groupId": 38,
		  "name": "开封祥符广播919",
		  "url": "https://lhttp.qtfm.cn/live/20500156/64k.mp3?app_id=web",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "开封",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 76,
		  "groupId": 34,
		  "name": "石家庄广播电视台经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/503/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "石家庄",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 77,
		  "groupId": 42,
		  "name": "邢台经济生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2791/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "邢台",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 78,
		  "groupId": 43,
		  "name": "保定1058飞扬调频汽车音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/521/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "保定",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 79,
		  "groupId": 43,
		  "name": "保定经典964汽车音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2265/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "保定",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 80,
		  "groupId": 43,
		  "name": "保定交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/517/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "保定",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 81,
		  "groupId": 44,
		  "name": "沧州1058汽车音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2050/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "沧州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 82,
		  "groupId": 45,
		  "name": "秦皇岛交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/523/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "秦皇岛",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 83,
		  "groupId": 45,
		  "name": "秦皇岛体育音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/522/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "秦皇岛",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 84,
		  "groupId": 45,
		  "name": "秦皇岛新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1114/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "秦皇岛",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 85,
		  "groupId": 46,
		  "name": "承德交通文艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2709/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "承德",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 86,
		  "groupId": 45,
		  "name": "秦皇岛924欢乐调频",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1129/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "秦皇岛",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 87,
		  "groupId": 45,
		  "name": "秦皇岛1038私家车广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1122/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "秦皇岛",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 88,
		  "groupId": 42,
		  "name": "邢台爱在104",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2052/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "邢台",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 89,
		  "groupId": 36,
		  "name": "张家口私家车广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1858/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "张家口",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 90,
		  "groupId": 47,
		  "name": "廊坊交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/511/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "廊坊",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 91,
		  "groupId": 6,
		  "name": "河南信息广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1650/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "河南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 92,
		  "groupId": 6,
		  "name": "河南娱乐976",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1941/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "河南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 93,
		  "groupId": 48,
		  "name": "洛阳交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/492/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "洛阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 94,
		  "groupId": 40,
		  "name": "驻马店交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2757/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "驻马店",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 95,
		  "groupId": 49,
		  "name": "商丘交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1261/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "商丘",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 96,
		  "groupId": 49,
		  "name": "商丘新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1271/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "商丘",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 97,
		  "groupId": 7,
		  "name": "山东人民广播电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1440/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "山东",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 98,
		  "groupId": 7,
		  "name": "山东经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/797/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "山东",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 99,
		  "groupId": 7,
		  "name": "山东体育休闲广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/805/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "山东",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 100,
		  "groupId": 7,
		  "name": "山东经典音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/802/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "山东",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 101,
		  "groupId": 7,
		  "name": "山东音乐频道",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/794/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "山东",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 102,
		  "groupId": 50,
		  "name": "济南新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/795/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "济南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 103,
		  "groupId": 50,
		  "name": "济南经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/800/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "济南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 104,
		  "groupId": 50,
		  "name": "济南音乐广播Music88.7",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/799/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "济南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 105,
		  "groupId": 51,
		  "name": "FM96.4青岛文艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/813/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "青岛",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 106,
		  "groupId": 52,
		  "name": "烟台综合广播FM101",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2717/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "烟台",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 107,
		  "groupId": 52,
		  "name": "烟台交通广播FM103",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2715/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "烟台",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 108,
		  "groupId": 52,
		  "name": "烟台音乐广播FM105.9",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2716/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "烟台",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 109,
		  "groupId": 52,
		  "name": "烟台汽车音乐广播FM898",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2829/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "烟台",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 110,
		  "groupId": 53,
		  "name": "淄博综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/828/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "淄博",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 111,
		  "groupId": 53,
		  "name": "淄博交通音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/817/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "淄博",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 112,
		  "groupId": 54,
		  "name": "德州新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/862/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "德州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 113,
		  "groupId": 54,
		  "name": "德州交通音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/856/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "德州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 114,
		  "groupId": 54,
		  "name": "德州文艺广播FM92.9",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2555/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "德州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 115,
		  "groupId": 53,
		  "name": "淄博私家车广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/819/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "淄博",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 116,
		  "groupId": 55,
		  "name": "潍坊新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/849/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "潍坊",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 117,
		  "groupId": 55,
		  "name": "潍坊交通广播FM107",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2417/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "潍坊",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 118,
		  "groupId": 55,
		  "name": "潍坊933经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/841/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "潍坊",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 119,
		  "groupId": 55,
		  "name": "潍坊城市之声FM100.8",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2813/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "潍坊",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 120,
		  "groupId": 55,
		  "name": "潍坊资讯音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2418/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "潍坊",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 121,
		  "groupId": 56,
		  "name": "临沂综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1291/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "临沂",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 122,
		  "groupId": 56,
		  "name": "临沂交通旅游广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1275/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "临沂",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 123,
		  "groupId": 57,
		  "name": "聊城新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2113/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "聊城",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 124,
		  "groupId": 57,
		  "name": "聊城交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2114/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "聊城",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 125,
		  "groupId": 57,
		  "name": "聊城音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2115/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "聊城",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 126,
		  "groupId": 58,
		  "name": "菏泽新闻广播FM87.5",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2397/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "菏泽",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 127,
		  "groupId": 58,
		  "name": "菏泽交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/845/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "菏泽",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 128,
		  "groupId": 58,
		  "name": "菏泽音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/839/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "菏泽",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 129,
		  "groupId": 59,
		  "name": "威海音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2810/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "威海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 130,
		  "groupId": 8,
		  "name": "山西综合广播FM904",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/893/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "山西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 131,
		  "groupId": 8,
		  "name": "山西交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/885/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "山西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 132,
		  "groupId": 8,
		  "name": "山西音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/886/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "山西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 133,
		  "groupId": 8,
		  "name": "山西文艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/887/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "山西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 134,
		  "groupId": 8,
		  "name": "山西故事广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1669/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "山西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 135,
		  "groupId": 8,
		  "name": "山西经济广播958电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/891/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "山西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 136,
		  "groupId": 8,
		  "name": "山西健康之声广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2736/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "山西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 137,
		  "groupId": 8,
		  "name": "山西农村广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/894/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "山西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 138,
		  "groupId": 60,
		  "name": "太原新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/888/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "太原",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 139,
		  "groupId": 60,
		  "name": "太原交通台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/883/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "太原",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 140,
		  "groupId": 60,
		  "name": "太原经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/884/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "太原",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 141,
		  "groupId": 61,
		  "name": "运城综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1252/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:22:26",
		  "groupName": "运城",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 142,
		  "groupId": 61,
		  "name": "运城文艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1251/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:22:54",
		  "groupName": "运城",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 143,
		  "groupId": 62,
		  "name": "临汾广播电视台综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2514/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:23:43",
		  "groupName": "临汾",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 144,
		  "groupId": 62,
		  "name": "临汾广播电视台交通文艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2515/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:24:15",
		  "groupName": "临汾",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 145,
		  "groupId": 62,
		  "name": "临汾广播电视台音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2516/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:24:40",
		  "groupName": "临汾",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 146,
		  "groupId": 63,
		  "name": "吕梁综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2573/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:25:41",
		  "groupName": "吕梁",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 147,
		  "groupId": 63,
		  "name": "吕梁交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/898/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:26:25",
		  "groupName": "吕梁",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 148,
		  "groupId": 64,
		  "name": "长治交通广播FM104.1",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1445/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:27:17",
		  "groupName": "长治",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 149,
		  "groupId": 64,
		  "name": "长治都市音乐之声FM98.0",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2546/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:27:45",
		  "groupName": "长治",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 150,
		  "groupId": 9,
		  "name": "内蒙古新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1070/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:28:50",
		  "groupName": "内蒙古",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 151,
		  "groupId": 9,
		  "name": "内蒙古新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/971/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:29:22",
		  "groupName": "内蒙古",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 152,
		  "groupId": 9,
		  "name": "内蒙古交通之声广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/972/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:29:50",
		  "groupName": "内蒙古",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 153,
		  "groupId": 9,
		  "name": "内蒙古评书曲艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/975/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:30:19",
		  "groupName": "内蒙古",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 154,
		  "groupId": 9,
		  "name": "内蒙古绿野之声广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/976/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:30:46",
		  "groupName": "内蒙古",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 155,
		  "groupId": 9,
		  "name": "内蒙古经济生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/974/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:31:18",
		  "groupName": "内蒙古",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 156,
		  "groupId": 9,
		  "name": "内蒙古蒙古语广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/970/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:31:44",
		  "groupName": "内蒙古",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 157,
		  "groupId": 65,
		  "name": "包头新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1104/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:32:41",
		  "groupName": "包头",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 158,
		  "groupId": 65,
		  "name": "包头交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1111/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:33:06",
		  "groupName": "包头",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 159,
		  "groupId": 65,
		  "name": "包头文艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1085/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:33:34",
		  "groupName": "包头",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 160,
		  "groupId": 65,
		  "name": "包头生活娱乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1094/64.m3u8",
		  "forbidden": false,
		  "createTime": "2023-03-20 13:33:59",
		  "groupName": "包头",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 161,
		  "groupId": 65,
		  "name": "包头汽车音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1103/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "包头",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 162,
		  "groupId": 65,
		  "name": "包头FM90.6",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2130/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "包头",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 163,
		  "groupId": 66,
		  "name": "鄂尔多斯之声FM896",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2627/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "鄂尔多斯",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 164,
		  "groupId": 66,
		  "name": "鄂尔多斯文体交通广播FM100.8",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1089/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "鄂尔多斯",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 165,
		  "groupId": 66,
		  "name": "鄂尔多斯曲艺评书广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2643/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "鄂尔多斯",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 166,
		  "groupId": 67,
		  "name": "阿拉善汉语综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2131/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "阿拉善",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 167,
		  "groupId": 68,
		  "name": "巴彦淖尔新闻综合频率FM107",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2598/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "巴彦淖尔",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 168,
		  "groupId": 68,
		  "name": "巴彦淖尔文艺生活",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2599/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "巴彦淖尔",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 169,
		  "groupId": 68,
		  "name": "巴彦淖尔交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1096/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "巴彦淖尔",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 170,
		  "groupId": 10,
		  "name": "辽宁交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1649/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "辽宁",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 171,
		  "groupId": 10,
		  "name": "辽宁生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2815/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "辽宁",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 172,
		  "groupId": 69,
		  "name": "沈阳新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/328/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "沈阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 173,
		  "groupId": 69,
		  "name": "沈阳交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/329/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "沈阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 174,
		  "groupId": 69,
		  "name": "沈阳都市广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/331/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "沈阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 175,
		  "groupId": 70,
		  "name": "大连综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2732/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "大连",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 176,
		  "groupId": 70,
		  "name": "大连交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2817/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "大连",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 177,
		  "groupId": 70,
		  "name": "大连都市之声广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2819/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "大连",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 178,
		  "groupId": 70,
		  "name": "大连电台财经广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/335/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "大连",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 179,
		  "groupId": 70,
		  "name": "大连1043",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2707/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "大连",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 180,
		  "groupId": 71,
		  "name": "抚顺音乐台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/341/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "抚顺",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 181,
		  "groupId": 72,
		  "name": "盘锦新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/348/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "盘锦",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 182,
		  "groupId": 72,
		  "name": "盘锦交通文艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/350/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "盘锦",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 183,
		  "groupId": 72,
		  "name": "盘锦经济生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2696/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "盘锦",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 184,
		  "groupId": 73,
		  "name": "朝阳新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/353/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "朝阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 185,
		  "groupId": 73,
		  "name": "朝阳交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/354/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "朝阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 186,
		  "groupId": 73,
		  "name": "朝阳经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/355/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "朝阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 187,
		  "groupId": 11,
		  "name": "吉林新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/429/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "吉林",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 188,
		  "groupId": 11,
		  "name": "吉林交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/431/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "吉林",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 189,
		  "groupId": 11,
		  "name": "吉林资讯广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/432/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "吉林",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 190,
		  "groupId": 11,
		  "name": "吉林音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/434/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "吉林",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 191,
		  "groupId": 11,
		  "name": "吉林旅游广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/436/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "吉林",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 192,
		  "groupId": 11,
		  "name": "吉林乡村广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/430/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "吉林",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 193,
		  "groupId": 11,
		  "name": "吉林经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/433/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "吉林",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 194,
		  "groupId": 11,
		  "name": "吉林教育广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/437/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "吉林",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 195,
		  "groupId": 11,
		  "name": "吉林健康娱乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/435/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "吉林",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 196,
		  "groupId": 11,
		  "name": "吉林市新闻频率",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/443/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "吉林",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 197,
		  "groupId": 12,
		  "name": "黑龙江生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/377/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "黑龙江",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 198,
		  "groupId": 12,
		  "name": "黑龙江音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/379/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "黑龙江",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 199,
		  "groupId": 74,
		  "name": "哈尔滨广播电视台综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/389/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "哈尔滨",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 200,
		  "groupId": 74,
		  "name": "哈尔滨交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/385/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "哈尔滨",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 201,
		  "groupId": 74,
		  "name": "哈尔滨文艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/390/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "哈尔滨",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 202,
		  "groupId": 74,
		  "name": "哈尔滨音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/386/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "哈尔滨",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 203,
		  "groupId": 74,
		  "name": "哈尔滨经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/387/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "哈尔滨",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 204,
		  "groupId": 74,
		  "name": "哈尔滨古典音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1664/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "哈尔滨",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 205,
		  "groupId": 75,
		  "name": "牡丹江综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1558/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "牡丹江",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 206,
		  "groupId": 75,
		  "name": "牡丹江交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1559/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "牡丹江",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 207,
		  "groupId": 76,
		  "name": "龙广新闻台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/375/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "龙广",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 208,
		  "groupId": 76,
		  "name": "龙广都市女性台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/378/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "龙广",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 209,
		  "groupId": 76,
		  "name": "龙广爱家频道",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/380/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "龙广",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 210,
		  "groupId": 76,
		  "name": "龙广青苹果之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/384/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "龙广",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 211,
		  "groupId": 13,
		  "name": "江苏新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/534/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "江苏",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 212,
		  "groupId": 13,
		  "name": "江苏交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/536/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "江苏",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 213,
		  "groupId": 13,
		  "name": "江苏经典流行音乐",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/538/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "江苏",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 214,
		  "groupId": 13,
		  "name": "江苏音乐广播PlayFM89.7",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/537/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "江苏",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 215,
		  "groupId": 13,
		  "name": "江苏财经广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/539/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "江苏",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 216,
		  "groupId": 13,
		  "name": "江苏新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/533/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "江苏",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 217,
		  "groupId": 13,
		  "name": "江苏故事广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/540/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "江苏",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 218,
		  "groupId": 13,
		  "name": "江苏文艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/541/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "江苏",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 219,
		  "groupId": 13,
		  "name": "江苏广播FM99.7金陵之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1394/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "江苏",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 220,
		  "groupId": 13,
		  "name": "江苏健康广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/542/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "江苏",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 221,
		  "groupId": 77,
		  "name": "南京新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/68/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "南京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 222,
		  "groupId": 77,
		  "name": "南京交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/70/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "南京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 223,
		  "groupId": 77,
		  "name": "南京音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/71/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "南京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 224,
		  "groupId": 77,
		  "name": "南京体育广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/88/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "南京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 225,
		  "groupId": 77,
		  "name": "南京活力广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2697/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "南京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 226,
		  "groupId": 77,
		  "name": "南京城市调频",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/90/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "南京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 227,
		  "groupId": 77,
		  "name": "南京经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/69/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "南京",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 228,
		  "groupId": 78,
		  "name": "苏州新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/63/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "苏州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 229,
		  "groupId": 78,
		  "name": "苏州交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/64/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "苏州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 230,
		  "groupId": 78,
		  "name": "苏州音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/65/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "苏州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 231,
		  "groupId": 78,
		  "name": "苏州儿童广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/66/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "苏州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 232,
		  "groupId": 78,
		  "name": "苏州生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/573/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "苏州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 233,
		  "groupId": 78,
		  "name": "苏州戏曲广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/576/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "苏州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 234,
		  "groupId": 79,
		  "name": "常州新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/79/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "常州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 235,
		  "groupId": 79,
		  "name": "常州交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/82/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "常州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 236,
		  "groupId": 79,
		  "name": "常州音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/80/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "常州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 237,
		  "groupId": 79,
		  "name": "常州经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/81/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "常州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 238,
		  "groupId": 80,
		  "name": "无锡交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/560/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "无锡",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 239,
		  "groupId": 80,
		  "name": "无锡梁溪之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/558/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "无锡",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 240,
		  "groupId": 80,
		  "name": "无锡经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/563/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "无锡",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 241,
		  "groupId": 80,
		  "name": "无锡都市生活",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/557/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "无锡",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 242,
		  "groupId": 80,
		  "name": "无锡音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/561/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "无锡",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 243,
		  "groupId": 81,
		  "name": "扬州新闻广播FM98.5",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2543/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "扬州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 244,
		  "groupId": 81,
		  "name": "扬州经济音乐广播FM94.9",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2542/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "扬州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 245,
		  "groupId": 81,
		  "name": "扬州交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2541/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "扬州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 246,
		  "groupId": 81,
		  "name": "扬州FM96.7",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2539/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "扬州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 247,
		  "groupId": 81,
		  "name": "扬州江都广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2540/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "扬州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 248,
		  "groupId": 82,
		  "name": "徐州交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/603/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "徐州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 249,
		  "groupId": 82,
		  "name": "徐州音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2743/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "徐州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 250,
		  "groupId": 82,
		  "name": "徐州广播FM105",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2478/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "徐州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 251,
		  "groupId": 83,
		  "name": "淮安新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/744/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "淮安",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 252,
		  "groupId": 83,
		  "name": "淮安交通文艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/746/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "淮安",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 253,
		  "groupId": 83,
		  "name": "淮安经济生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/745/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "淮安",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 254,
		  "groupId": 83,
		  "name": "淮安经典992",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2821/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "淮安",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 255,
		  "groupId": 83,
		  "name": "淮安车生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2776/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "淮安",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 256,
		  "groupId": 83,
		  "name": "淮安汽车音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1341/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "淮安",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 257,
		  "groupId": 14,
		  "name": "浙江之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/633/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "浙江",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 258,
		  "groupId": 14,
		  "name": "浙江城市之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/638/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "浙江",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 259,
		  "groupId": 14,
		  "name": "浙江交通之声FM93",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/634/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "浙江",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 260,
		  "groupId": 14,
		  "name": "浙江音乐调频",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/637/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "浙江",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 261,
		  "groupId": 14,
		  "name": "浙江民生996",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/639/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "浙江",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 262,
		  "groupId": 84,
		  "name": "杭州之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/646/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "杭州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 263,
		  "groupId": 84,
		  "name": "杭州西湖之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/635/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "杭州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 264,
		  "groupId": 84,
		  "name": "杭州交通经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/632/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "杭州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 265,
		  "groupId": 84,
		  "name": "杭州FM90.7",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1845/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "杭州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 266,
		  "groupId": 84,
		  "name": "杭州老朋友广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2832/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "杭州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 267,
		  "groupId": 85,
		  "name": "温州新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/655/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "温州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 268,
		  "groupId": 85,
		  "name": "温州交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/641/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "温州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 269,
		  "groupId": 85,
		  "name": "温州经济生活",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/659/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "温州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 270,
		  "groupId": 85,
		  "name": "温州绿色之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/663/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "温州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 271,
		  "groupId": 85,
		  "name": "温州私家车音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/642/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "温州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 272,
		  "groupId": 86,
		  "name": "湖州新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/675/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "湖州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 273,
		  "groupId": 86,
		  "name": "湖州交通文艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/656/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "湖州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 274,
		  "groupId": 86,
		  "name": "湖州经济广播FM103.5",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/647/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "湖州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 275,
		  "groupId": 87,
		  "name": "嘉兴新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/650/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "嘉兴",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 276,
		  "groupId": 87,
		  "name": "嘉兴交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/640/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "嘉兴",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 277,
		  "groupId": 87,
		  "name": "嘉兴音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/651/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "嘉兴",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 278,
		  "groupId": 88,
		  "name": "宁波新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/653/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "宁波",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 279,
		  "groupId": 88,
		  "name": "宁波交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/649/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "宁波",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 280,
		  "groupId": 88,
		  "name": "宁波经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/661/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "宁波",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 281,
		  "groupId": 88,
		  "name": "宁波音乐广播私家车986",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/660/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "宁波",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 282,
		  "groupId": 88,
		  "name": "宁波老少广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/665/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "宁波",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 283,
		  "groupId": 89,
		  "name": "台州新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/682/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "台州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 284,
		  "groupId": 89,
		  "name": "台州交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/676/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "台州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 285,
		  "groupId": 89,
		  "name": "台州音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/668/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "台州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 286,
		  "groupId": 15,
		  "name": "安徽综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2833/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "安徽",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 287,
		  "groupId": 15,
		  "name": "安徽交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/138/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "安徽",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 288,
		  "groupId": 15,
		  "name": "安徽经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/135/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "安徽",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 289,
		  "groupId": 15,
		  "name": "安徽生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/137/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "安徽",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 290,
		  "groupId": 15,
		  "name": "安徽音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/136/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "安徽",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 291,
		  "groupId": 15,
		  "name": "安徽潮流音乐电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/143/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "安徽",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 292,
		  "groupId": 15,
		  "name": "安徽农村广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/139/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "安徽",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 293,
		  "groupId": 15,
		  "name": "安徽老年广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/140/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "安徽",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 294,
		  "groupId": 15,
		  "name": "安徽戏曲广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/141/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "安徽",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 295,
		  "groupId": 90,
		  "name": "合肥故事广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/146/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "合肥",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 296,
		  "groupId": 91,
		  "name": "芜湖新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/165/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "芜湖",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 297,
		  "groupId": 92,
		  "name": "蚌埠交通文艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/151/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "蚌埠",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 298,
		  "groupId": 93,
		  "name": "淮北综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2576/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "淮北",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 299,
		  "groupId": 93,
		  "name": "淮北交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2577/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "淮北",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 300,
		  "groupId": 16,
		  "name": "福建交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/790/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "福建",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 301,
		  "groupId": 94,
		  "name": "泉州广播电视台889新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2110/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "泉州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 302,
		  "groupId": 94,
		  "name": "泉州经济生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1166/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "泉州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 303,
		  "groupId": 95,
		  "name": "石狮广播电视台综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2730/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "石狮",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 304,
		  "groupId": 96,
		  "name": "龙岩旅游广播fm94.6",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2805/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "龙岩",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 305,
		  "groupId": 97,
		  "name": "漳浦广播电视台综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1168/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "漳浦",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 306,
		  "groupId": 98,
		  "name": "闽侯广播电视台综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2772/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "闽侯",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 307,
		  "groupId": 99,
		  "name": "永安广播电视台综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2729/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "永安",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 308,
		  "groupId": 17,
		  "name": "江西交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/609/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "江西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 309,
		  "groupId": 17,
		  "name": "江西音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/608/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "江西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 310,
		  "groupId": 100,
		  "name": "南昌交通音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/618/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "南昌",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 311,
		  "groupId": 101,
		  "name": "赣州综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/620/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "赣州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 312,
		  "groupId": 102,
		  "name": "九江柴桑之声FM91.0",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/630/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "九江",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 313,
		  "groupId": 102,
		  "name": "九江私家车广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2078/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "九江",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 314,
		  "groupId": 103,
		  "name": "南康交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/624/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "南康",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 315,
		  "groupId": 18,
		  "name": "湖南金鹰955电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/405/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "湖南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 316,
		  "groupId": 104,
		  "name": "长沙新闻广播FM105.0",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/406/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "长沙",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 317,
		  "groupId": 104,
		  "name": "长沙品味音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/401/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "长沙",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 318,
		  "groupId": 105,
		  "name": "湘潭新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2673/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "湘潭",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 319,
		  "groupId": 105,
		  "name": "湘潭交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1174/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "湘潭",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 320,
		  "groupId": 106,
		  "name": "株洲新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/411/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "株洲",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 321,
		  "groupId": 107,
		  "name": "常德综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2650/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "常德",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 322,
		  "groupId": 107,
		  "name": "常德交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2648/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "常德",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 323,
		  "groupId": 107,
		  "name": "常德音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2649/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "常德",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 324,
		  "groupId": 107,
		  "name": "常德活力调频鼎广电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1184/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "常德",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 325,
		  "groupId": 108,
		  "name": "衡阳综合广播FM98.9",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1185/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "衡阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 326,
		  "groupId": 108,
		  "name": "衡阳交通频道",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1181/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "衡阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 327,
		  "groupId": 108,
		  "name": "衡阳FM105.6音乐电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1182/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "衡阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 328,
		  "groupId": 109,
		  "name": "娄底综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/416/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "娄底",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 329,
		  "groupId": 110,
		  "name": "郴州电台综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2712/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "郴州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 330,
		  "groupId": 110,
		  "name": "郴州交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2713/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "郴州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 331,
		  "groupId": 111,
		  "name": "永州新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2634/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "永州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 332,
		  "groupId": 112,
		  "name": "畅行954邵阳交通频道",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2121/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "邵阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 333,
		  "groupId": 113,
		  "name": "吉首综合广播FM99.1",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2827/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "吉首",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 334,
		  "groupId": 114,
		  "name": "宁乡电台FM945",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2676/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "宁乡",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 335,
		  "groupId": 115,
		  "name": "桃江人民广播电台FM105.2",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1874/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "桃江",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 336,
		  "groupId": 116,
		  "name": "浏阳99.5交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1978/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "浏阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 337,
		  "groupId": 19,
		  "name": "湖北之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/703/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "湖北",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 338,
		  "groupId": 117,
		  "name": "武汉音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2605/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "武汉",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 339,
		  "groupId": 117,
		  "name": "武汉青少广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2607/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "武汉",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 340,
		  "groupId": 118,
		  "name": "十堰综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/730/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "十堰",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 341,
		  "groupId": 118,
		  "name": "十堰交通音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/731/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "十堰",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 342,
		  "groupId": 119,
		  "name": "荆门综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/732/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "荆门",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 343,
		  "groupId": 119,
		  "name": "荆门交通音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/733/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "荆门",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 344,
		  "groupId": 20,
		  "name": "广东新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/245/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广东",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 345,
		  "groupId": 20,
		  "name": "广东城市之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/247/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广东",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 346,
		  "groupId": 20,
		  "name": "广东羊城交通台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/248/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广东",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 347,
		  "groupId": 20,
		  "name": "广东广播电视台文体广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/967/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广东",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 348,
		  "groupId": 20,
		  "name": "广东广播电视台珠江之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/255/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广东",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 349,
		  "groupId": 20,
		  "name": "广东珠江经济电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/252/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广东",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 350,
		  "groupId": 20,
		  "name": "广东音乐之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/74/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广东",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 351,
		  "groupId": 20,
		  "name": "广东南方生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/249/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广东",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 352,
		  "groupId": 120,
		  "name": "广州新闻电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/256/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 353,
		  "groupId": 120,
		  "name": "广州交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/258/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 354,
		  "groupId": 120,
		  "name": "广州MYFM88.0",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/259/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 355,
		  "groupId": 120,
		  "name": "广州汽车音乐电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/257/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 356,
		  "groupId": 121,
		  "name": "深圳电台湾区之声1043",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/267/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "深圳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 357,
		  "groupId": 121,
		  "name": "深圳生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/265/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "深圳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 358,
		  "groupId": 121,
		  "name": "深圳飞扬971",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/263/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "深圳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 359,
		  "groupId": 121,
		  "name": "宝安905",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2644/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "深圳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 360,
		  "groupId": 122,
		  "name": "珠海电台活力915",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1227/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "珠海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 361,
		  "groupId": 122,
		  "name": "FM92.8斗门电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2740/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "珠海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 362,
		  "groupId": 123,
		  "name": "中山电台快乐888",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1191/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "中山",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 363,
		  "groupId": 123,
		  "name": "中山电台新锐967",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1220/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "中山",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 364,
		  "groupId": 124,
		  "name": "东莞综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1228/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "东莞",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 365,
		  "groupId": 124,
		  "name": "东莞交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1210/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "东莞",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 366,
		  "groupId": 124,
		  "name": "东莞音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1215/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "东莞",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 367,
		  "groupId": 120,
		  "name": "增城电台FM89.0",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2564/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 368,
		  "groupId": 21,
		  "name": "广西电台新闻910",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/299/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 369,
		  "groupId": 21,
		  "name": "广西交通台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/304/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 370,
		  "groupId": 21,
		  "name": "FM950广西音乐台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/301/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 371,
		  "groupId": 21,
		  "name": "广西北部湾之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/303/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 372,
		  "groupId": 21,
		  "name": "广西女主播电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/300/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 373,
		  "groupId": 21,
		  "name": "广西私家车930",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/302/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "广西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 374,
		  "groupId": 125,
		  "name": "南宁990新闻台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/305/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "南宁",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 375,
		  "groupId": 125,
		  "name": "南宁经典1049",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1316/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "南宁",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 376,
		  "groupId": 125,
		  "name": "南宁1074交通台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/306/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "南宁",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 377,
		  "groupId": 125,
		  "name": "南宁快乐895",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1321/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "南宁",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 378,
		  "groupId": 126,
		  "name": "北海新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1315/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "北海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 379,
		  "groupId": 127,
		  "name": "防城港人民广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1318/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "防城港",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 380,
		  "groupId": 22,
		  "name": "海南交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/529/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "海南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 381,
		  "groupId": 128,
		  "name": "海口生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2654/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "海口",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 382,
		  "groupId": 128,
		  "name": "海口旅游交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2837/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "海口",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 383,
		  "groupId": 129,
		  "name": "三亚之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2825/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "三亚",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 384,
		  "groupId": 130,
		  "name": "琼海人民广播电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2083/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "琼海",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 385,
		  "groupId": 23,
		  "name": "四川广播电视台民族频率",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2801/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "四川",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 386,
		  "groupId": 23,
		  "name": "四川新闻广播FM106.1",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2799/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "四川",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 387,
		  "groupId": 23,
		  "name": "四川综合广播FM98.1",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2800/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "四川",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 388,
		  "groupId": 23,
		  "name": "四川交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/752/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "四川",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 389,
		  "groupId": 23,
		  "name": "四川财富广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1645/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "四川",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 390,
		  "groupId": 23,
		  "name": "四川城市之音",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/755/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "四川",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 391,
		  "groupId": 23,
		  "name": "四川岷江音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2802/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "四川",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 392,
		  "groupId": 23,
		  "name": "四川文艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2804/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "四川",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 393,
		  "groupId": 131,
		  "name": "成都新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/750/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "成都",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 394,
		  "groupId": 131,
		  "name": "成都交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/757/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "成都",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 395,
		  "groupId": 131,
		  "name": "成都潮台FM88.7",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2781/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "成都",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 396,
		  "groupId": 131,
		  "name": "成都经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/758/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "成都",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 397,
		  "groupId": 131,
		  "name": "成都经典946",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/5/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "成都",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 398,
		  "groupId": 131,
		  "name": "亚洲音乐成都FM96.5",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1003/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "成都",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 399,
		  "groupId": 131,
		  "name": "成都故事广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1247/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "成都",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 400,
		  "groupId": 131,
		  "name": "成都年代音乐88.9&103.2",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2724/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "成都",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 401,
		  "groupId": 132,
		  "name": "绵阳新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/765/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "绵阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 402,
		  "groupId": 132,
		  "name": "绵阳交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/766/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "绵阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 403,
		  "groupId": 132,
		  "name": "绵阳音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1243/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "绵阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 404,
		  "groupId": 133,
		  "name": "攀枝花综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1064/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "攀枝花",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 405,
		  "groupId": 134,
		  "name": "南充交通音乐广播FM91.5",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/772/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "南充",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 406,
		  "groupId": 135,
		  "name": "乐山广播电视台音乐交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2703/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "乐山",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 407,
		  "groupId": 136,
		  "name": "德阳综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2502/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "德阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 408,
		  "groupId": 134,
		  "name": "南充综合广播FM100.4",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1242/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "南充",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 409,
		  "groupId": 137,
		  "name": "泸州新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/785/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "泸州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 410,
		  "groupId": 137,
		  "name": "泸州交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/786/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "泸州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 411,
		  "groupId": 136,
		  "name": "德阳经济生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1250/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "德阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 412,
		  "groupId": 136,
		  "name": "德阳市旌阳区人民广播电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2604/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "德阳",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 413,
		  "groupId": 24,
		  "name": "贵阳新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/365/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "贵州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 414,
		  "groupId": 24,
		  "name": "贵州交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/362/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "贵州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 415,
		  "groupId": 138,
		  "name": "遵义旅游生活广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/374/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "遵义",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 416,
		  "groupId": 139,
		  "name": "六盘水广播电视台交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1820/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "六盘水",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 417,
		  "groupId": 140,
		  "name": "FM106兴仁人民广播电台",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2789/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "兴仁",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 418,
		  "groupId": 25,
		  "name": "云南新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/905/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "云南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 419,
		  "groupId": 25,
		  "name": "云南交通之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/903/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "云南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 420,
		  "groupId": 25,
		  "name": "云南音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/904/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "云南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 421,
		  "groupId": 25,
		  "name": "云南国际广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/923/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "云南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 422,
		  "groupId": 25,
		  "name": "云南民族广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/911/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "云南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 423,
		  "groupId": 25,
		  "name": "云南私家车广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/909/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "云南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 424,
		  "groupId": 141,
		  "name": "昆明都市调频",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/907/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "昆明",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 425,
		  "groupId": 141,
		  "name": "昆明阳光频率",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/910/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "昆明",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 426,
		  "groupId": 141,
		  "name": "昆明汽车广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/906/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "昆明",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 427,
		  "groupId": 142,
		  "name": "红河交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/916/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "红河",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 428,
		  "groupId": 142,
		  "name": "红河综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/920/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "红河",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 429,
		  "groupId": 143,
		  "name": "普洱电台新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/922/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "普洱",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 430,
		  "groupId": 144,
		  "name": "怒江综合频率fm1056",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1710/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "怒江",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 431,
		  "groupId": 145,
		  "name": "曲靖交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2015/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "曲靖",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 432,
		  "groupId": 145,
		  "name": "曲靖综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2016/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "曲靖",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 433,
		  "groupId": 26,
		  "name": "陕西都市广播 陕广新闻",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/872/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "陕西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 434,
		  "groupId": 26,
		  "name": "陕西音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/867/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "陕西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 435,
		  "groupId": 26,
		  "name": "陕西交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/869/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "陕西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 436,
		  "groupId": 26,
		  "name": "陕西秦腔广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/871/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "陕西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 437,
		  "groupId": 26,
		  "name": "陕西896汽车调频",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/868/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "陕西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 438,
		  "groupId": 26,
		  "name": "陕西戏曲广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/876/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "陕西",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 439,
		  "groupId": 146,
		  "name": "西安新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1370/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "西安",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 440,
		  "groupId": 146,
		  "name": "西安综艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1373/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "西安",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 441,
		  "groupId": 146,
		  "name": "西安音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1442/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "西安",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 442,
		  "groupId": 146,
		  "name": "西安交通旅游广播FM104.3",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1371/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "西安",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 443,
		  "groupId": 146,
		  "name": "西安资讯广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1374/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "西安",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 444,
		  "groupId": 147,
		  "name": "宝鸡新闻广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1701/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "宝鸡",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 445,
		  "groupId": 147,
		  "name": "宝鸡交通旅游广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1704/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "宝鸡",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 446,
		  "groupId": 147,
		  "name": "宝鸡音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1703/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "宝鸡",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 447,
		  "groupId": 147,
		  "name": "宝鸡经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1702/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "宝鸡",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 448,
		  "groupId": 148,
		  "name": "延安新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2699/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "延安",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 449,
		  "groupId": 148,
		  "name": "延安交通音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2698/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "延安",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 450,
		  "groupId": 149,
		  "name": "安康综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/880/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "安康",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 451,
		  "groupId": 149,
		  "name": "安康交通旅游音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/881/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "安康",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 452,
		  "groupId": 150,
		  "name": "渭南交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2082/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "渭南",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 453,
		  "groupId": 27,
		  "name": "甘肃新闻综合",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/287/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "甘肃",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 454,
		  "groupId": 27,
		  "name": "甘肃交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/289/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "甘肃",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 455,
		  "groupId": 27,
		  "name": "甘肃都市调频快乐1066",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/288/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "甘肃",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 456,
		  "groupId": 27,
		  "name": "甘肃经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/292/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "甘肃",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 457,
		  "groupId": 27,
		  "name": "甘肃青春调频",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/290/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "甘肃",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 458,
		  "groupId": 27,
		  "name": "甘肃农村广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/291/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "甘肃",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 459,
		  "groupId": 151,
		  "name": "兰州交通音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/296/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "兰州",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 460,
		  "groupId": 152,
		  "name": "天水新闻综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2048/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "天水",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 461,
		  "groupId": 152,
		  "name": "天水音乐文艺广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2049/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "天水",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 462,
		  "groupId": 152,
		  "name": "天水旅游咨询频道",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2047/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "天水",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 463,
		  "groupId": 152,
		  "name": "FM91.9 天水交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2720/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "天水",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 464,
		  "groupId": 28,
		  "name": "宁夏音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1878/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "宁夏",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 465,
		  "groupId": 28,
		  "name": "宁夏都市广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1071/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "宁夏",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 466,
		  "groupId": 28,
		  "name": "宁夏经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/985/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "宁夏",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 467,
		  "groupId": 153,
		  "name": "银川经典音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2820/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "银川",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 468,
		  "groupId": 29,
		  "name": "新疆兵团之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1838/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "新疆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 469,
		  "groupId": 29,
		  "name": "新疆交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/987/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "新疆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 470,
		  "groupId": 154,
		  "name": "喀什广播电视台文旅交通广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2782/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "喀什",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 471,
		  "groupId": 154,
		  "name": "喀什广播电视台综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2773/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "喀什",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 472,
		  "groupId": 155,
		  "name": "阿克苏人民广播电台维吾尔语综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2682/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "阿克苏",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 473,
		  "groupId": 155,
		  "name": "阿克苏人民广播电台汉语综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2681/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "阿克苏",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 474,
		  "groupId": 156,
		  "name": "库车综合广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2659/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "库车",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 475,
		  "groupId": 29,
		  "name": "990新闻音乐广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2816/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "新疆",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 476,
		  "groupId": 157,
		  "name": "库尔勒市FM1053梨城之声",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/2671/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "库尔勒",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 477,
		  "groupId": 158,
		  "name": "伊犁维语新闻综合",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1356/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "伊犁",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 478,
		  "groupId": 158,
		  "name": "伊犁经济广播",
		  "url": "https://live.ximalaya.com/radio-first-page-app/live/1353/64.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "伊犁",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 479,
		  "groupId": 32,
		  "name": "央视新闻",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv13_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 480,
		  "groupId": 32,
		  "name": "凤凰资讯",
		  "url": "http://playtv-live.ifeng.com/live/06OLEEWQKN4_audio.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 481,
		  "groupId": 32,
		  "name": "凤凰中文",
		  "url": "http://playtv-live.ifeng.com/live/06OLEGEGM4G_audio.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 482,
		  "groupId": 32,
		  "name": "CCTV-1",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv1_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 483,
		  "groupId": 32,
		  "name": "CCTV-2",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv2_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 484,
		  "groupId": 32,
		  "name": "CCTV-3",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv3_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 485,
		  "groupId": 32,
		  "name": "CCTV-4",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv4_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 486,
		  "groupId": 32,
		  "name": "CCTV-5",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv5_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 487,
		  "groupId": 32,
		  "name": "CCTV-5+",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv5plus_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 488,
		  "groupId": 32,
		  "name": "CCTV-6",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv6_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 489,
		  "groupId": 32,
		  "name": "CCTV-7",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv7_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 490,
		  "groupId": 32,
		  "name": "CCTV-8",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv8_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 491,
		  "groupId": 32,
		  "name": "CCTV-9",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv9_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 492,
		  "groupId": 32,
		  "name": "CCTV-10",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv10_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 493,
		  "groupId": 32,
		  "name": "CCTV-11",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv11_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 494,
		  "groupId": 32,
		  "name": "CCTV-12",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv12_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 495,
		  "groupId": 32,
		  "name": "CCTV-13",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv13_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 496,
		  "groupId": 32,
		  "name": "CCTV-14",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv14_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 497,
		  "groupId": 32,
		  "name": "CCTV-15",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv15_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 498,
		  "groupId": 32,
		  "name": "CCTV-16",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv16_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		},
		{
		  "id": 499,
		  "groupId": 32,
		  "name": "CCTV-17",
		  "url": "http://piccpndali.v.myalicdn.com/audio/cctv17_2.m3u8",
		  "forbidden": false,
		  "createTime": "2025-08-05 08:00:00",
		  "groupName": "央视",
		  "creatorUser": {
			"userName": "admin"
		  }
		}
	 ]
    })";
}