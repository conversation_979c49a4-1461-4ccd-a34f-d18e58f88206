#ifndef TIMERSCHEME_H
#define TIMERSCHEME_H


#include "PlayList.h"
#include "Tools/CTime.h"
#include "Global/Const.h"

#define MAX_USER_ACCOUNT_LEN 16     //最大用户名称长度

#define DAYS_PER_WEEK			7

typedef	enum
{
    TT_DEFAULT = -1,              //默认，为此模式时不添加时间(监控事件使用)    zhuyg

    TT_WEEK_CYCLE	= 0	,		// 按周循环
    TT_SPECIFY_DATE		,		// 指定日期
}TIMER_TIME_TYPE;	// 定时模式

typedef struct tagDATE
{
    int		nYear;				// 年
    int		nMon;				// 月
    int		nDay;				// 日

}TIMER_DATE;	// 日期

typedef struct tagTIME
{
    int		nHour;				// 时
    int		nMin;				// 分
    int		nSec;				// 秒

}TIMER_TIME;	// 时间

typedef enum
{
    TIMER_SOURCE_TYPE_LOCAL_SONG = 0,        //定时音源-本地音乐
    TIMER_SOURCE_TYPE_AUDIO_COLLECTOR = 1,        //定时音源-音频采集器
}TIMER_SOURCE_TYPE;     //定时音源类型

typedef enum
{
    TIMER_DEVICE_TYPE_DECODING_TERMINAL = 0,     //解码终端
    TIMER_DEVICE_TYPE_SEQUENCE_POWER = 1,        //电源时序器
}TIMER_DEVICE_TYPE;     //定时设备类型

typedef struct
{
    char mac[SEC_MAC_LEN];
    unsigned char channelId;
}ST_TIMER_AUDIO_COLLECTOR_INFO;

/************************************************************************/
/* 定时点类                                                             */
/************************************************************************/

class CTimePoint
{
public:
    CTimePoint(void);
    ~CTimePoint(void);

public:
    int				GetID(void)							{	return m_nID;								}
    void			SetID(int nID)						{	m_nID = nID;								}
    void			SetUniqueID(CMyString strUniqueID)	{	m_strUniqueID = strUniqueID;					    }
    CMyString		GetUniqueID(void)					{	return m_strUniqueID;						}
    void			Enable(bool bValid)					{	m_bValid = bValid;							}
    bool			IsValid(void)						{	return m_bValid;							}
    void			SetPlayToEnd(bool bToEnd)			{	m_bPlayToEnd = bToEnd;						}
    bool			IsPlayToEnd(void)					{	return m_bPlayToEnd;						}
    void			SetResumePlaying(bool bPlaying)		{	m_bResumePlaying = bPlaying;				}
    bool			GetResumePlaying(void)				{	return m_bResumePlaying;					}
    void			SetIntercut(bool bIntercut)			{	m_bIntercut = bIntercut;					}
    bool			GetIntercut(void)					{	return m_bIntercut;							}
    void			SetFollowDevice(bool bFollow)		{	m_bFollowDevice = bFollow;					}
    bool			GetFollowDevice(void)				{	return m_bFollowDevice;						}
    void			SetSinglePlay(bool bSingle)			{	m_bSinglePlay = bSingle;					}
    bool			GetSinglePlay(void)					{	return m_bSinglePlay;						}
    void			SetName(CMyString strName)			{	m_strName = strName;						}
    CMyString		GetName(void)						{	return m_strName;							}
    CMyString		GetZoneMac(int nSec)				{	return m_vecSecMac[nSec];					}
    int				GetSplitterStatus(int nSec)			{	return m_vecSplitterStatus[nSec];			}
    void			SetSplitterStatus(int nSec, int status)	{	m_vecSplitterStatus[nSec] = status;		}
    int				GetZoneCount(void)					{	return m_vecSecMac.size();					}
    CMyString		GetSeqPwrMac(int nSec)				{	return m_vecSeqPwrMac[nSec];				}
    int				GetSeqPwrCount(void)				{	return m_vecSeqPwrMac.size();				}
    int				GetSeqPwrChannelsCount(void)		{	return m_vecSeqPwrChannels.size();			}
    unsigned short	GetSeqPwrChannels(int nSec)			{	return m_vecSeqPwrChannels[nSec];			}
    void			SetSeqPwrChannels(int nSec, unsigned short channels)	{	m_vecSeqPwrChannels[nSec] = channels;		}
    CMyString		GetGroupID(int nGroup)				{	return m_vecGroupID[nGroup];				}
    int				GetGroupCount(void)					{	return m_vecGroupID.size();					}
    CSong&			GetSong(int nSong)					{	return m_vecSong[nSong];					}
    int				GetSongCount(void)					{	return m_vecSong.size();					}
    void			SetDateStart(TIMER_DATE& dtStart)	{	m_DateStart = dtStart;						}
    TIMER_DATE&		GetDateStart(void)					{	return	m_DateStart;						}
    void			SetDateEnd(TIMER_DATE& dtEnd)		{	m_DateEnd = dtEnd;							}
    TIMER_DATE&		GetDateEnd(void)					{	return	m_DateEnd;							}
    void			SetTimeStart(TIMER_TIME& Time)		{	m_TimeStart = Time;							}
    TIMER_TIME&		GetTimeStart(void)					{	return m_TimeStart;							}
    void			SetTimeEnd(TIMER_TIME& Time)		{	m_TimeEnd = Time;							}
    TIMER_TIME&		GetTimeEnd(void)					{	return m_TimeEnd;							}
    LPCSTR          GetAccount()                        {   return m_szAccount;                         }
    void            SetAccount(LPCSTR szAccount);
    void			AddGroup(CMyString strGroupID)		{	m_vecGroupID.push_back(strGroupID);			}
    void			SetTimeType(TIMER_TIME_TYPE Type)		{	m_TimeType	= Type;							}
    TIMER_TIME_TYPE	GetTimeType(void)					{	return m_TimeType;							}
    void			SetSourceType(TIMER_SOURCE_TYPE Type)	{	m_SourceType	= Type;							}
    TIMER_SOURCE_TYPE	GetSourceType(void)				{	return m_SourceType;							}
    void			SetDeviceType(TIMER_DEVICE_TYPE Type)	{	m_DeviceType	= Type;							}
    TIMER_DEVICE_TYPE	GetDeviceType(void)				{	return m_DeviceType;							}
    void			SetPlayMode(int Mode)				{	m_nPlayMode	= Mode;							}
    int				GetPlayMode(void)					{	return m_nPlayMode;							}
    bool*			GetSelectedDays(void)				{	return m_SelectedDays;						}
    int				GetVolume(void)						{	return m_nVolume;							}
    void			SetVolume(int nVolume)				{	m_nVolume = nVolume;						}

    bool			GetSingleCancel(void)			    {	return m_bSingleCancel;						}
    void			SetSingleCancel(bool IsCancel)		{	m_bSingleCancel = IsCancel;					}

    bool			GetHasPlayed(void)			        {	return m_HasPlayed;						    }
    void            SetHasPlayed(bool hasPlayed)        {   m_HasPlayed = hasPlayed;                    }

    void            SetAudioCollector(ST_TIMER_AUDIO_COLLECTOR_INFO st_audioCollctor) { m_stAudioCollector = st_audioCollctor;  }
    ST_TIMER_AUDIO_COLLECTOR_INFO GetAudioCollector(void)  { return m_stAudioCollector;            }

    void			AddTimerSection(CMyString strMac, int nSplitterStatus = -1);
    bool			RemoveTimerSection(CMyString	strMac);
    bool            RemoveTimerSequencePower(CMyString strMac);
    void			AddTimerSeqPwr(CMyString strMac,unsigned short nChannels = 0xFFFF);
    bool			RemoveTimerSeqPwr(CMyString	strMac);
    bool			RemoveGroup(CMyString strGroupID);
    void			SetSelectedDays(bool* bSelDays);
    void			ClearSections(void);
    void			ClearGroups(void);
    void			ClearSequencePowers(void);
    void			AddSong(CMyString strPathName, int nDuration);
    void			RemoveAllSongs(void);

    bool			ContainZone(CMyString strMac);				// 是否选择该分区
    bool			HasIntersect(CTimePoint& timePoint);		// 是否有交叉
    bool			HasIntersectZones(CTimePoint& timePoint);	// 分区是否有交叉
    bool			HasIntersectTime(CTimePoint& timePoint);	// 时间是否有交叉
    void			DateToWeekdays(bool* pWeekdays);			// 日期转换成周
    static bool		CompareWeekdays(bool* pWeekdays1, bool* pWeekdays2);	// 对比周几有没有重叠

    int				GetSelectedSections(vector<CMyString>& vecMac);

    int				GetSectionIndexByMac(CMyString strMac);		// 得到指定mac地址的索引


    // 判断日期是否在开始与结束日期之间
    static BOOL IsDateInRange(TIMER_DATE& dateStart, TIMER_DATE& dateEnd, CTime& t);
    // 判断时间是否在开始时间与结束时间之间
    static BOOL IsTimeInRange(TIMER_TIME& timeStart, TIMER_TIME& timeEnd, CTime& t);
    // 判断当前时间是否是启动定时点的有效时间（0~+1秒，极个别时候后一秒才有反应）
    static BOOL IsCurValidStartTimer(TIMER_TIME& timeStart, TIMER_TIME& timeEnd, CTime& t);
    // 判断时间是否等于准备时间
    static BOOL IsTimeEqualReady(TIMER_TIME& timeStart,CTime& t,int secTimeout);
    // 判断时间是否处于准备时间内
    static BOOL IsTimeInReadyRange(TIMER_TIME& timeStart,CTime& t,int secTimeout);

    // 集中模式的定时
    bool			IsToStartWorking(CTime& t);			// 是否开始执行
    bool            IsSectionToReadyWorking(CTime& t);         // 是否处于准备时间内(默认提前10秒、5秒)
    bool            IsSequencePowerWorking(CTime& t);
    bool			IsToStopWorking(CTime& t);			// 是否停止执行
    int				GetOnlineSectionCount(void);		// 定时点里在线的分区个数
    int				GetStartSong();
    int				GetNextSong(int nSong, bool bGetStartSong = FALSE);	//
    int             GetNextSong(const char* szSongUrl);
    int				GetPlayID(void)						{	return m_nPlayID;							}
    void			SetPlayID(int playID)				{	m_nPlayID = playID;							}
    int             GetHTTPID(void)                     {   return m_nHTTPID;                           }
    void            SetHTTPID(int httpID)               {   m_nHTTPID = httpID;                         }

private:
    int					m_nID;				// 从1开始
    CMyString		    m_strUniqueID;						// ID
    bool				m_bValid;
    CMyString			m_strName;
    vector<CMyString>	m_vecSecMac;
    vector<int>			m_vecSplitterStatus;
    vector<CMyString>	m_vecSeqPwrMac;
    vector<unsigned short>	m_vecSeqPwrChannels;
    vector<CMyString>	m_vecGroupID;
    vector<CSong>		m_vecSong;
    bool				m_bPlayToEnd;
    bool				m_bResumePlaying;	// 定时结束后恢复播放歌曲
    bool				m_bIntercut;		// 插播
    bool				m_bFollowDevice;	// 音量跟随设备
    bool				m_bSinglePlay;		// 随机播放单曲
    int					m_nVolume;
    TIMER_DATE			m_DateStart;
    TIMER_DATE			m_DateEnd;
    TIMER_TIME			m_TimeStart;
    TIMER_TIME			m_TimeEnd;
    TIMER_TIME_TYPE		m_TimeType;

    TIMER_SOURCE_TYPE	m_SourceType;

    TIMER_DEVICE_TYPE   m_DeviceType;

    ST_TIMER_AUDIO_COLLECTOR_INFO m_stAudioCollector;

    int					m_nPlayMode;
    bool				m_SelectedDays[DAYS_PER_WEEK];	// 按周一，周二..周日来存储，但GetDayOfWeek是周日(1)，周一(2)...周六(7)

    int					m_nPlayID;	// 播放ID：1-200
    int                 m_nHTTPID;  // HTTP播放ID
    BOOL				m_HasPlayed;	// 定时点是否执行过
    bool                m_bSingleCancel; //是否单次取消(第二天自动恢复)

    CHAR   m_szAccount[MAX_USER_ACCOUNT_LEN+1];       // 创建该定时点的用户名称
};

/************************************************************************/
/* 定时点方案类                                                           */
/************************************************************************/
#if SUPPORT_TIMER_SCHEME_AUTO_SWITCH
enum
{
    SCH_AUTO_SWITCH_NOT_SET=0,  //未设置
    SCH_AUTO_SWITCH_REGULAR,    //常规
    SCH_AUTO_SWITCH_OTHER       //其他
};
#endif
class CScheme
{
public:
    CScheme(void);
    ~CScheme(void);

public:
    void			SetUniqueID(CMyString strUniqueID)		{	m_strUniqueID = strUniqueID;					    }
    CMyString		GetUniqueID(void)					{	return m_strUniqueID;						}
    void		EnableTimer(int nTimer, bool bEnable)	{	m_TimePoints[nTimer].Enable(bEnable);		}
    bool		IsTimerValid(int nTimer)				{	return m_TimePoints[nTimer].IsValid();		}
    void		SetName(CMyString strName)				{	m_strSchemeName = strName;					}
    CMyString	GetName(void)							{	return m_strSchemeName;						}
    CTimePoint&	GetTimer(int nTimer)					{	return m_TimePoints[nTimer];				}
    int			GetTimerCount(void)						{	return m_TimePoints.size();					}
    CMyString	GetTimerName(int nTimer)				{	return m_TimePoints[nTimer].GetName();		}
    void		SetTimerName(int nTimer, CMyString strName){	m_TimePoints[nTimer].SetName(strName);		}
    bool		AddTimer(CTimePoint& TimePoint);
    void		RemoveTimer(int nTimer);
    void		ClearTimers(void);
    int			GetValidTimerCount(void);
    CMyString	GetValidName(void);
#if SUPPORT_TIMER_SCHEME_AUTO_SWITCH
    int	            GetAutoSwitchType()			            {	return m_nAutoSwitchType;}
    void			SetAutoSwitchType(int type)				{   m_nAutoSwitchType = type;					}
    void			SetDateStart(TIMER_DATE& dtStart)	{	m_DateStart = dtStart;						}
    TIMER_DATE&		GetDateStart(void)					{	return	m_DateStart;						}
    void			SetDateEnd(TIMER_DATE& dtEnd)		{	m_DateEnd = dtEnd;							}
    TIMER_DATE&		GetDateEnd(void)					{	return	m_DateEnd;							}
#endif
    // 判断定时点交叉
    unsigned int		GetIntersectTimers(CTimePoint& timePoint, vector<CTimePoint*>&	pTimePoints, bool bAllTimers = TRUE);

    void		SortByName(bool bAscend = TRUE);		// 按定时点名称排序
    void		SortByStatus(bool bAscend = TRUE);		// 按定时点状态排序
    void		SortByCycle(bool bAscend = TRUE);		// 按定时点周期排序
    void		SortByStartTime(bool bAscend = TRUE);	// 按定时点开始时间排序
    int         CompareWeekdays(CTimePoint& tp1, CTimePoint& tp2);	// 比较定时点周几

    void		DeassignTimersID(void);                 // 重新赋值定时点的ID
    void		DisableAllTimers(void);					// 禁止所有的定时点
    void        AbleAllTimers();                        // 恢复所有定时点

private:
    vector<CTimePoint>	m_TimePoints;
    CMyString		    m_strUniqueID;						// ID
    CMyString			m_strSchemeName;
    #if SUPPORT_TIMER_SCHEME_AUTO_SWITCH
    int                 m_nAutoSwitchType;              // 自动切换类型
    TIMER_DATE			m_DateStart;          // 自动触发开始日期
    TIMER_DATE			m_DateEnd;            // 自动触发结束日期
    #endif
};

/************************************************************************/
/* 定时方案管理类                                                       */
/************************************************************************/

// 定时点交叉数据
typedef struct
{
    int					nScheme;
    vector<CTimePoint*> pTimePoints;

}IntersectScheme;



#define TODAY_TIMER_STATUS_NO_RUN       0
#define TODAY_TIMER_STATUS_ALREADY_RUN  1
#define TODAY_TIMER_STATUS_RUNNING      2
#define TODAY_TIMER_STATUS_SINGLE_CANCEL  3
//今日定时点数据
typedef struct
{
    int				status;         //0-未执行，1已执行，2正在执行
    CTimePoint      TimerPoint;    //TimerPoint
}StTodayTimerP;

class CTimerScheme
{
public:
    CTimerScheme(void);
    ~CTimerScheme(void);

public:
    CMyString	GetDateTime(void)						{	return m_strDateTime;						}
    void		SetDateTime(CMyString strDateTime)		{	m_strDateTime = strDateTime;				}
    int			GetSchemeCount(void)					{	return m_Schemes.size();					}
    void		SetCurScheme(int nScheme)				{	m_nCurScheme = nScheme;						}
    int			GetCurScheme(void)						{	return m_nCurScheme;						}
    CScheme&	GetScheme(int nScheme)					{	return m_Schemes[nScheme];					}
    bool        GetNeedNotify()                                       { return m_bNeedNotify;      }
    void        SetNeedNotify(bool isNotify)                  { m_bNeedNotify = isNotify; }

    void		AddScheme(CScheme&	Scheme)				{	m_Schemes.push_back(Scheme);				}
    void		SetSchemeName(int nSch, CMyString strName){	m_Schemes[nSch].SetName(strName);			}
    CMyString	GetSchemeName(int nSch)					{	return m_Schemes[nSch].GetName();			}
    void		AddTimer(int nSch, CTimePoint& tp)		{	m_Schemes[nSch].AddTimer(tp);				}
    void		EnableTimer(int nSch, int nTm, bool bEn){	m_Schemes[nSch].EnableTimer(nTm, bEn);		}
    bool		IsTimerValid(int nSch, int nTimer)		{	return m_Schemes[nSch].IsTimerValid(nTimer);}
    int			GetTimerCount(int nSch)					{	return m_Schemes[nSch].GetTimerCount();		}
    CTimePoint& GetTimer(int nSch, int nTimer)			{	return m_Schemes[nSch].GetTimer(nTimer);	}
    CMyString	GetTimerName(int nSch, int nTimer)		{	return m_Schemes[nSch].GetTimerName(nTimer);}
    bool		IsAllSchemesValid(void)					{	return m_bAllSchemesValid;					}
    void		SetTimePointsResumePlaying(BOOL bPlaying){	m_bTimePointsResumePlaying = bPlaying;	}
    BOOL		GetTimePointsResumePlaying(void)		{	return m_bTimePointsResumePlaying;		}
    void		AddScheme(CMyString strName);
    void        AddScheme(string strName, int &nIndex);
    void		RemoveScheme(int nScheme);
    void		RemoveTimer(int nSch, int nTimer);
    void		ClearSchemes(void);
    bool		ReadTimerFile(string strFileName);
    bool		WriteTimerFile(string strFileName, bool bUpdateDateTime = TRUE);
    bool		ImportSchemeFile(CScheme& scheme, string strPathName);
    bool		ExportSchemeFile(int nSch,  string strPathName);
    bool		UpdateSchemeName(CMyString strOldDefaultSchemeName, CMyString strOldDefaultTimerName);
    #if SUPPORT_TIMER_SCHEME_AUTO_SWITCH
    int	        GetAutoSwitch(void)			            {	return m_bAutoSwitch;}
    void	    SetAutoSwitch(int IsOn)			    {   m_bAutoSwitch = IsOn;					}
    #endif
    // 通过playID找到对应的定时点
    CTimePoint*	GetTimePointByPlayID(int playID);
    // 通过MAC查找此时在执行的定时点
    CTimePoint* GetPlayingTimePointByDeviceMac(LPCSTR szMac);

    bool		RemoveGroupFromAllSchemes(CMyString strGroupID);
    bool		RemoveAllGroupsFromAllSchemes(void);
    bool	    RemoveInvalidGroup();

    // 判断定时点交叉
    UINT        GetIntersectSchemes(unsigned int uSchemeIndex, CTimePoint& timePoint, vector<IntersectScheme>& interSchemes, 
                                    CTimePoint &conflictFirstTimingPoint, BOOL bAllTimers = FALSE);
    unsigned int GetIntersectSchemes(CTimePoint& timePoint, vector<IntersectScheme>& interSchemes, bool bAllTimers = TRUE);
    void		DisableTimers(vector<IntersectScheme>&	interSchemes);
    CMyString	GetTimersName(vector<IntersectScheme>&	interSchemes);

    // 移除定时点中的分区
    bool		RemoveSectionFromAllSchemes(const char* szMac);

    // 移除定时点中的音频采集器设备
    bool        RemoveAudioCollectorFromAllSchemes(const char* szMac);

    // 移除定时点中的电源时序器
    bool        RemoveSequencePowerFromAllSchemes(const char* szMac);

    // 获取今日定时点
    void        GetTodayTimer(CTime& t,vector<StTodayTimerP>& timerVec);

    // 对比两个timervec是否相等
    static bool IsTimeVecEqual(vector<StTodayTimerP>& timerVec1,vector<StTodayTimerP>& timerVec2);

    //移除对应用户的定时点
    bool		RemoveTimerSpecUser(const char* szAccount);

    void        MutexLock();
    void        MutexUnlock();

    //恢复定时点的单次取消状态（新一天主动恢复）
    void    ResetTimerSingleCancel();

    // 检测定时点内的电源时序器状态
    void   CheckTimersSequenceStatus(CTime& t);

    // 检测正在执行定时音频采集的定时点
    bool  GetRunningTimePointAcChannel(vector<CTimePoint>& timerVec,const char *AudioCollectorMac,unsigned char channel);

private:
    vector<CScheme>		m_Schemes;
    int					m_nCurScheme;
    CMyString			m_strDateTime;
    bool				m_bAllSchemesValid;	// 所有方案有效，定制的项目所用
    bool                m_bNeedNotify;      // 文件是否需要通知Web终端
    BOOL				m_bTimePointsResumePlaying;	// 定时结束后恢复播放歌曲(集中模式)
    #if SUPPORT_TIMER_SCHEME_AUTO_SWITCH
    BOOL                m_bAutoSwitch;      // 定时方案是否开启自动切换功能
    #endif
private:
   pthread_mutex_t    m_TimerMutex;
};




#endif // TIMERSCHEME_H
