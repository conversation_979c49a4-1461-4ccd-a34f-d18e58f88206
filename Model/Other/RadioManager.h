#ifndef RADIOMANAGER_H
#define RADIOMANAGER_H

#include <string>
#include <vector>
#include <map>
#include "Tools/cJSON.h"

using namespace std;

// 电台信息结构体
typedef struct {
    int id;
    int groupId;
    string name;
    string url;
    bool forbidden;
    string createTime;
    string groupName;
    string creatorUserName;
} RadioInfo;

// 前向声明
struct RadioGroupInfo;

// 电台分组信息结构体
typedef struct RadioGroupInfo {
    string key;
    int groupId;
    int pid;
    string name;
    vector<RadioGroupInfo> children;
} RadioGroupInfo;

class CRadioManager
{
public:
    CRadioManager();
    ~CRadioManager();

    // 初始化电台数据
    bool InitRadioData();
    
    // 加载预置电台数据
    bool LoadPresetRadioData();
    
    // 加载自定义电台数据
    bool LoadCustomRadioData();
    
    // 保存自定义电台数据
    bool SaveCustomRadioData();
    
    // 添加电台信息（仅限自定义分组）
    bool AddRadioInfo(const RadioInfo& radioInfo);
    
    // 编辑电台信息（仅限自定义分组）
    bool EditRadioInfo(int radioId, const RadioInfo& radioInfo);
    
    // 删除电台信息（仅限自定义分组）
    bool DeleteRadioInfo(int radioId);
    
    // 获取电台分组列表
    vector<RadioGroupInfo> GetRadioGroupList();
    
    // 通过分组ID获取详细电台列表
    vector<RadioInfo> GetRadioListByGroupId(int groupId, bool needEncrypt = true);
    
    // 加密电台URL
    string EncryptRadioUrl(const string& url);
    
    // 解密电台URL
    string DecryptRadioUrl(const string& encryptedUrl);
    
    // 检查是否为加密URL
    bool IsEncryptedUrl(const string& url);
    
    // 获取下一个可用的电台ID
    int GetNextRadioId();
    
    // 清空所有数据
    void Clear();

private:
    // 解析电台数据JSON
    bool ParseRadioDataJson(const string& jsonContent, vector<RadioInfo>& radioList);
    
    // 解析省份数据JSON
    bool ParseProvinceDataJson(const string& jsonContent, vector<RadioGroupInfo>& groupList);
    
    // 生成自定义电台JSON
    string GenerateCustomRadioJson();
    
    // 验证电台信息
    bool ValidateRadioInfo(const RadioInfo& radioInfo);
    
    // 获取预置电台数据JSON（硬编码）
    static string GetPresetRadioDataJson();
    
    // 获取预置分组数据JSON（硬编码）
    static string GetPresetProvinceDataJson();

private:
    vector<RadioInfo> m_presetRadioList;        // 预置电台列表
    vector<RadioInfo> m_customRadioList;        // 自定义电台列表
    vector<RadioGroupInfo> m_radioGroupList;    // 电台分组列表
    
    string m_customRadioFilePath;               // 自定义电台文件路径
    int m_nextRadioId;                          // 下一个可用的电台ID
    
    static const string ENCRYPT_PREFIX;         // 加密URL前缀
    static const int CUSTOM_GROUP_ID = 33;      // 自定义分组ID
};

#endif // RADIOMANAGER_H