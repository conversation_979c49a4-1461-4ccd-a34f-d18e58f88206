#ifndef LOGFILE_H
#define LOGFILE_H

#include "Network/Protocol/Protocol.h"
#include "Tools/CMyString.h"
#include <iostream>
#include <vector>

/************************************************************************/
/*  日志文件信息                                                        */
/************************************************************************/

class CLogFile
{
public:
    CLogFile();
    virtual ~CLogFile();
    unsigned short	NextPackID(void);
    bool	HasFinished(void);

public:
    char	m_szName[LOG_FILE_NAME_MAX_LEN];	// 文件名称
    int		m_nSize;		// 文件大小（字节）
    int		m_nRecvSize;	// 收到的文件数据大小
    unsigned short	m_nPackCount;	// 包总数
    unsigned short	m_nRecvPackID;	// 上一次收到的包ID
    char*	m_pData;		// 日志文件内容指针，动态分配内存
};

//日志文件管理类
class CLogFiles
{
public:
    CLogFile&	  GetLogFile(int index)			{	return m_LogFiles[index];	}
    unsigned char GetLogFileCount(void)			{	return m_LogFiles.size();	}
    void		  AddLogFile(CLogFile& logFile);
    void		  Clear(void);
    CLogFile*	  FindLogFile(const char* szFileName);
    void		  CreateLogFileDirectory(const char* szMac);   //, const char* szName);
    CMyString	  GetLogFileDirectory(const char* szMac); //, const char* szName);
    bool      IsNeedDownload(const char* szMac, const char* szName);

private:
    vector<CLogFile> m_LogFiles;	// 日志文件信息

};


#endif // LOGFILE_H
