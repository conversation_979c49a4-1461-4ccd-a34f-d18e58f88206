#include "stdafx.h"
#include "Group.h"

CGroup::CGroup(CMyString strID, const char* lpszName)
{
    m_strID = strID;
    SetName(lpszName);
    m_isChecked = FALSE;
    m_nBmpID = -1;
    memset(m_szBmpPath, 0,sizeof(m_szBmpPath));

    // 播放列表相关
    InitPlaylistGroupFileName();
    m_PlaylistGroup.ReadFile();
}

CGroup::~CGroup()
{
    if(m_vecSecMac.size() > 0)
    {
        ClearSections();
    }
}


void CGroup::SetUserAccount(CMyString strUserAccount)
{
    if( g_Global.m_Users.IsExistUser(strUserAccount.Data()) )
    {
        m_strUserAccount=strUserAccount;
    }
    else
    {
        m_strUserAccount=SUPER_USER_NAME;
    }
}


void CGroup::ClearSections(void)
{
    m_vecSecMac.clear();
}

void CGroup::AddSection(CMyString strMac)
{
    int							i			= 0;
    bool						hasExisted	= FALSE;
    vector<CMyString>::iterator	iter;

    for(i=0, iter=m_vecSecMac.begin(); iter!=m_vecSecMac.end(); ++i, ++iter)
    {
        if(strMac == m_vecSecMac[i])	// 不能重复添加
        {
            hasExisted = TRUE;
            break;
        }
    }

    if(!hasExisted)
    {
        m_vecSecMac.push_back(strMac);
    }
}

bool CGroup::RemoveSection(CMyString strMac)
{
    bool flag = FALSE;

    vector<CMyString>::iterator iter;
    int i = 0;
    for(i=0, iter=m_vecSecMac.begin(); iter!=m_vecSecMac.end(); ++i, ++iter)
    {
        if(strMac == m_vecSecMac[i])
        {
            m_vecSecMac.erase(iter);
            flag = TRUE;
            break;	// 务必加上break
        }
    }

    return flag;
}

bool CGroup::IsSameSecMacs(vector<CMyString>& secMacs)
{
    // 保留，待修改 zhuyg
    //return (m_vecSecMac == secMacs);
    if(m_vecSecMac.size() != secMacs.size())
    {
        return false;
    }

    for(int i=0; i<(int)m_vecSecMac.size(); i++)
    {
        if(m_vecSecMac[i] != secMacs[i])
        {
            return false;
        }
    }

    return true;
}

void CGroup::SetSecMacs(vector<CMyString>& secMacs)
{
    m_vecSecMac = secMacs;
}

void CGroup::SetSecMacs(vector<string> &secMacs)
{
    m_vecSecMac.clear();
    for(int i=0; i<(int)secMacs.size(); i++)
    {
        m_vecSecMac.push_back(CMyString(secMacs[i]));
    }
}

// 检测无效的分区
bool CGroup::RemoveInvalidSections(void)
{
    bool bRemoved = FALSE;

    // 检测是不是有分区不存在
    for(int i=0; i<GetSecCount(); ++i)
    {
        CMyString strMac = GetSecMac(i);

        // 分区不存在
        if (g_Global.m_Sections.GetSectionByMac(strMac) == NULL)
        {
            // 则从该分组中删除该分区
            RemoveSection(strMac);
            i--;
            bRemoved = TRUE;
        }
    }

    return bRemoved;
}

int CGroup::GetSectionIndex(CMyString strMac)
{
    int nSecCount = m_vecSecMac.size();

    for (int i=0; i<nSecCount; ++i)
    {
        if (strMac == m_vecSecMac[i])
        {
            return i;
        }
    }

    return -1;
}

unsigned int CGroup::GetCheckedSections(unsigned int* pCheckedIndexs)
{
    unsigned int uCheckedCount	= 0;
    int  nSecCount		= GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        CMyString		strMac		= GetSecMac(i);
        CSection*	pSection	= g_Global.m_Sections.GetSectionByMac(strMac);

        if (pSection != NULL && pSection->GetCheck())
        {
            pCheckedIndexs[uCheckedCount++] = pSection->GetID() - 1;
        }
    }

    return uCheckedCount;
}

void CGroup::InitPlaylistGroupFileName(void)
{
    m_PlaylistGroup.SetFileFolder(HTTP_FOLDER_PLAYLIST_OWN);

    CMyString strFileName;
    strFileName.Format((char*)("Playlist_%s.xml"), m_strID.C_Str());

    m_PlaylistGroup.SetFileName(strFileName);
}

CPlayList* CGroup::GetPlaylist(void)
{
    if (m_PlaylistGroup.ExistOwnFile())
    {
        return &m_PlaylistGroup;
    }
    else
    {
        return &g_Global.m_PlayList;
    }
}

void CGroup::ClearPlaylistGroup(void)
{
    if (m_PlaylistGroup.ExistOwnFile())
    {
        CHAR	szPathName[MAX_PATH] = {0};
        CombinHttpURL(szPathName, m_PlaylistGroup.GetFileFolder().C_Str(), m_PlaylistGroup.GetFileName().C_Str());

        // 删除掉已存在的文件 保留，待修改
        //DeleteFile(szPathName);
    }

    m_PlaylistGroup.ClearList();
}

bool CGroup::GroupSectionSort(void)
{
    vector<CMyString> m_vecSecMac_old=m_vecSecMac;
    std::sort(m_vecSecMac.begin(),m_vecSecMac.end(),CGroups::SortBySectionId);
    if(m_vecSecMac_old == m_vecSecMac)
    {
        return false;
    }
    else
    {
        return true;
    }
}

/*bool CGroup::AddPage(int nPage)
{
        vector<int>::iterator iter = m_vecPage.begin();
        // 相同，则不添加
        for(; iter != m_vecPage.end(); iter++)
        {
            if(*iter == nPage)
            {
                return false;
            }
        }

        m_vecPage.push_back(nPage);
        return true;
}

int CGroup::GetPageCount()
{
        return m_vecPage.size();
}

// 设置临时保存的分区Mac地址
void CGroup::SecTempSecMac(vector<string> &vecSecMac, int nPage, int nPageCount)
{
        if(AddPage(nPage))
        {
            for(int i=0; i<vecSecMac.size(); i++)
            {
                    m_vecTempSecMac.push_back(CMyString(vecSecMac.at(i)));
            }
        }

        if(GetPageCount() == nPageCount)        // 接收完分区数据
        {
                // 保存进内存与xml
                SetSecMacs(m_vecTempSecMac);
                g_Global.WriteXmlFile(FILE_GROUP);

                // 清空数据
                m_vecPage.clear();
                m_vecTempSecMac.clear();
        }
}*/



/********************************************************************/

CGroups::CGroups()
{
    m_bNeedNotify = false;
}

CGroups::~CGroups()
{
    if(m_Groups.size())
    {
        m_Groups.clear();
    }
}


void CGroups::SetGroupUserAccount(int nGroup, CMyString strUserAccount)
{
    m_Groups[nGroup].SetUserAccount(strUserAccount);
}
CMyString CGroups::GetGroupUserAccount(int nGroup)
{
    return m_Groups[nGroup].GetUserAccount();
}


CMyString CGroups::GetGroupSecMac(int nGroup, int nSec)
{
    return m_Groups[nGroup].GetSecMac(nSec);
}

bool CGroups::AddGroup(CGroup& group)
{
    int nMaxGroupCount = MAX_GROUP_COUNT;

    if (GetGroupCount() >= nMaxGroupCount)
    {
        return FALSE;
    }

    m_Groups.push_back(group);

    return TRUE;
}

void CGroups::RemoveGroup(int nGroup)
{
    // 删除
    vector<CGroup>::iterator iter;
    int i = 0;
    for(i=0, iter=m_Groups.begin(); iter!=m_Groups.end(); ++i, ++iter)
    {
        if(nGroup == i)
        {
            m_Groups.erase(iter);
            break;	// 务必加上break
        }
    }
}

void CGroups::RemoveGroup(CMyString strUserAccount)
{
    bool IsUpdate=false;
    // 删除
    vector<CGroup>::iterator iter;
    for(iter=m_Groups.begin(); iter!=m_Groups.end();)
    {
        if((*iter).GetUserAccount() == strUserAccount)
        {
            m_Groups.erase(iter);
            IsUpdate=true;
            continue;
        }
        else
        {
            iter++;
        }
    }

    if(IsUpdate)
        g_Global.WriteXmlFile(FILE_GROUP);
}

bool CGroups::RemoveSectionFromAllGroups(const char* szMac)
{
    BOOL flag = FALSE;

    for (int i=0; i<GetGroupCount(); ++i)
    {
        if (m_Groups[i].RemoveSection(CMyString(szMac)))
        {
            flag = TRUE;
        }
    }

    return flag;
}


unsigned int CGroups::GetCheckedGroups(unsigned int* pCheckedIndexs)
{
    int  nGroupCount	= GetGroupCount();
    unsigned int uCheckedCount	= 0;

    for (int i=0; i<nGroupCount; ++i)
    {
        if (m_Groups[i].GetCheck())
        {
            pCheckedIndexs[uCheckedCount++] = i;
        }
    }

    return uCheckedCount;
}


unsigned int CGroups::GetCheckedGroupsCount()
{
    int  nGroupCount	= GetGroupCount();
    unsigned int uCheckedCount	= 0;

    for (int i=0; i<nGroupCount; ++i)
    {
        if (m_Groups[i].GetCheck())
        {
            uCheckedCount++;
        }
    }

    return uCheckedCount;
}


unsigned int CGroups::GetCheckedSections(unsigned int* pCheckedIndexs)
{
    map<string,int> macID;
    int uCheckedCount	= 0;

    for (int i=0; i<GetGroupCount(); ++i)
    {
        CGroup& group = GetGroup(i);

        if (group.GetCheck()) //分组选中
        {
            // 轮循选中分组的所有分区
            for (int j=0; j<group.GetSecCount(); j++)
            {
                CMyString strMac = group.GetSecMac(j);
                CSection* pSection = g_Global.m_Sections.GetSectionByMac(strMac);

                if (pSection != NULL)
                {
                    macID[strMac.C_Str()] = (pSection->GetID()-1); // 加入到map中，重复的覆盖掉（不同分组可能有相同的分区）
                }
            }
        }
    }

    map<string, int>::iterator iter;
    for(iter=macID.begin(); iter!=macID.end(); ++iter)
    {
        pCheckedIndexs[uCheckedCount++] = iter->second; // 选中的分区索引
    }

    return uCheckedCount;
}


void CGroups::AddGroupSection(int nGroup, CMyString strMac)
{
    m_Groups[nGroup].AddSection(strMac);
}

void CGroups::AddGroupSection(int nGroup, const char* szMac)
{
    AddGroupSection(nGroup, CMyString(szMac));
}

void CGroups::RemoveGroupSection(int nGroup, CMyString strMac)
{
    m_Groups[nGroup].RemoveSection(strMac);
}

void CGroups::RemoveGroupSection(int nGroup, const char* szMac)
{
    RemoveGroupSection(nGroup, CMyString(szMac));
}

void CGroups::ClearGroups(void)
{
    m_Groups.clear();
}


//读取XML文件中的分组信息
bool CGroups::ReadGroupFile(string strFileName)
{
    TiXmlDocument* xmlGroup = new TiXmlDocument;
    string  strGroupID				= "";
    int		nGroup					= 0;
    string strGroupName             = "";
    string strGroupUserName         = "";
    CHAR	szPathName[MAX_PATH]	= {0};
    //int		nSecIndex				= 0;
    BOOL	bWriteToFile			= FALSE;

    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName.data());

    int nMaxGroupCount = MAX_GROUP_COUNT;

    if(xmlGroup->LoadFile(szPathName))
    {
        // SectionGroup
        TiXmlElement* SectionGroup = xmlGroup->FirstChildElement();
        SetDateTime(CMyString(SectionGroup->Attribute("DateTime")));

        // Group
        for(TiXmlElement* Group=SectionGroup->FirstChildElement(); Group!=NULL;
            Group=Group->NextSiblingElement())
        {
            if(GetGroupCount() >= nMaxGroupCount)
            {
                break;
            }

            strGroupID   = Group->Attribute("ID");
            strGroupName = Group->Attribute("Name");
            string strBmpID = Group->Attribute("BmpID");
            LPCSTR lpszBmpPath = Group->Attribute("BmpPath");
            if( Group->Attribute("UserName") == NULL )
            {
                strGroupUserName =SUPER_USER_NAME;
                bWriteToFile = TRUE;
            }
            else
            {
                strGroupUserName = Group->Attribute("UserName");
            }

            // 没有读到ID，兼容以前的文件
            if (strGroupID == "")
            {
                strGroupID = GetGUID().C_Str();
                bWriteToFile = TRUE;
            }

            // 判断分组名是否为utf8，如果是则转成gb2312
            /*if(IsTextUTF8(strGroupName.data(), strGroupName.length()))
            {
                CMyString msGroupName = ConvertUTF8toGB2312(strGroupName.data(), strGroupName.length());
                CGroup group(CMyString(strGroupID), msGroupName.C_Str());
                group.SetBmpID((strBmpID == "") ? -1 : std::stoi(strBmpID));
                group.SetBmpPath(lpszBmpPath);
                AddGroup(group);
            }
            else
            {
                CGroup group(CMyString(strGroupID), strGroupName.c_str());
                group.SetBmpID((strBmpID == "") ? -1 : std::stoi(strBmpID));
                group.SetBmpPath(lpszBmpPath);
                AddGroup(group);
            }*/

            CGroup group(CMyString(strGroupID), strGroupName.c_str());
            group.SetBmpID((strBmpID == "") ? -1 : std::stoi(strBmpID));
            group.SetBmpPath(lpszBmpPath);
            group.SetUserAccount(strGroupUserName);
            AddGroup(group);

            for(TiXmlElement* Section=Group->FirstChildElement(); Section!=NULL;
                Section=Section->NextSiblingElement())
            {
                const char* szMac=Section->Attribute("Mac");
                //AddGroupSection(nGroup,szMac);
                //如果分区不存在，则删除 ********
                if( g_Global.m_Sections.GetSectionByMac(szMac) != NULL )
                    AddGroupSection(nGroup,szMac);
                else   
                {
                     bWriteToFile = TRUE;
                }
            }

            nGroup++;
        }
    }
    else
    {
        return FALSE;
    }

    if (bWriteToFile)
    {
        WriteGroupFile(strFileName, TRUE);
        //g_Global.WriteXmlFile(FILE_GROUP);      // zhuyg
    }

    xmlGroup->Clear();
    delete xmlGroup;
    return TRUE;

}


//往XML文件中写入分组信息
bool CGroups::WriteGroupFile(string strFileName, bool bUpdateDateTime)
{
    TiXmlDocument* xmlGroup = new TiXmlDocument;
    int		nGroupCount = GetGroupCount();
    int		nSecCount	= 0;

    CHAR	szPathName[MAX_PATH] = {0};
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName.data());

    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0", "utf-8", "no");
    xmlGroup->LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment("Save for Group info");
    xmlGroup->LinkEndChild(com);

    TiXmlElement* SectionGroup = new TiXmlElement("SectoinGroup");
    SectionGroup->SetAttribute("GroupCount", nGroupCount);
    SectionGroup->SetAttribute("DateTime", GetDateTime().C_Str());
    xmlGroup->LinkEndChild(SectionGroup);

    // Group
    LOG(FORMAT("nGroupCount : %d\n", nGroupCount, __FILE__, __FUNCTION__), LV_INFO);
    for(int i=0; i<nGroupCount; i++)
    {
        TiXmlElement* Group = new TiXmlElement("Group");
        SectionGroup->LinkEndChild(Group);
        Group->SetAttribute("ID", GetGroupID(i).C_Str());
        Group->SetAttribute("SectionCount", GetGroupSecCount(i));
        Group->SetAttribute("Name", GetGroupName(i));
        Group->SetAttribute("BmpID", GetGroupBmpID(i));
        Group->SetAttribute("BmpPath", GetGroupBmpPath(i));
        Group->SetAttribute("UserName", GetGroupUserAccount(i).Data());
        nSecCount = GetGroupSecCount(i);

        for(int j=0; j<nSecCount; j++)
        {
            TiXmlElement* Section = new TiXmlElement("Section");
            Section->SetAttribute("Mac", GetGroupSecMac(i, j).C_Str());
            Group->LinkEndChild(Section);
        }
    }

    bool saveFileOK=true;
    if(xmlGroup->SaveFile(szPathName))
    {
        SetNeedNotify(true);
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlGroup->Clear();
    delete xmlGroup;

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    return saveFileOK;

}


bool CGroups::UpdateGroupName(CMyString strOldDefaultGroupName)
{
    bool	bUpdated		= FALSE;
    CMyString strDateTime	= GetCurrentDateTime();

    for (int i=0; i<GetGroupCount(); ++i)
    {
        CMyString strGroupName = GetGroupName(i);

        if (strGroupName.Find(strOldDefaultGroupName) == 0)
        {
            strGroupName.Replace(strOldDefaultGroupName, LANG_STR(LANG_SECTION_ZONE_GROUP, "Group", ("分组")));
            SetGroupName(i, strGroupName.C_Str());

            bUpdated = TRUE;
        }
    }

    if (bUpdated)
    {
        SetDateTime(strDateTime);
        //WriteGroupFile(HTTP_FILE_GROUP);
        g_Global.WriteXmlFile(FILE_GROUP);      // zhuyg
    }

    return bUpdated;
}


bool CGroups::FindGroupByName(const char* szName)
{
    int nGroupCount = GetGroupCount();

    for (int i=0; i<nGroupCount; ++i)
    {
        // zhuyg   strcpy 改成 strcmp
        if (strcmp(szName, GetGroupName(i)) == 0)
        {
            return TRUE;
        }
    }

    return FALSE;
}


bool CGroups::FindGroupByName(const char* szName, CGroup& group)
{
    int nGroupCount = GetGroupCount();

    for (int i=0; i<nGroupCount; ++i)
    {
        // zhuyg   strcpy 改成 strcmp
        if (group.GetID() != GetGroupID(i) && strcmp(szName, GetGroupName(i)) == 0)
        {
            return TRUE;
        }
    }

    return FALSE;
}


void CGroups::GetAllGroupsBySpecUser(CMyString strUserAccount,vector<CGroup> &vecGroup)
{
    int nGroupCount = GetGroupCount();

    for (int i=0; i<nGroupCount; ++i)
    {
        CGroup &group = GetGroup(i);
        if (group.GetUserAccount() == strUserAccount )
        {
            vecGroup.push_back(group);
        }
    }
}


CGroup*	 CGroups::FindGroupByID(CMyString strID, int& nIndex)
{
    int nGroupCount = GetGroupCount();

    for (int i=0; i<nGroupCount; ++i)
    {
        CGroup& group = GetGroup(i);
        if (strID == group.GetID())
        {
            nIndex = i;
            return &group;
        }
    }

    return NULL;
}

void	 CGroups::ClearAllPlaylistGroup(void)
{
    int nGroupCount = GetGroupCount();

    for (int i=0; i<nGroupCount; ++i)
    {
        GetGroup(i).ClearPlaylistGroup();
    }
}


 //判断分组内的分区是否仍属于分组，否则删除掉（在账户更新绑定分区时进入）
void    CGroups::UpdateGroupSection(CMyString strUserAccount)
{
    bool IsUpdate=false;
    if(strUserAccount == SUPER_USER_NAME)
    {
        return;
    }

    LPCUserInfo userDest=g_Global.m_Users.GetUserByAccount(strUserAccount.Data());
    vector<userParm>  vecSubUserInfo;
    g_Global.m_Users.GetAllSubUserByAccount(strUserAccount.Data(), vecSubUserInfo);

    int nGroupCount = GetGroupCount();
    for (int i=0; i<nGroupCount; ++i)
    {
        LPCUserInfo user=NULL;
        if( m_Groups[i].GetUserAccount() == strUserAccount )
        {
            user=userDest;
        }
        else
        {
            for(int k=0;k<vecSubUserInfo.size();k++)
            {
                if(m_Groups[i].GetUserAccount() == vecSubUserInfo[k].strAccount)
                {
                    user = g_Global.m_Users.GetUserByAccount(m_Groups[i].GetUserAccount().Data());
                    break;
                }
            }
        }
        //printf("用户:%s的分组%s更新\n",strUserAccount.Data(),m_Groups[i].GetName());
        if(user)
        {
            vector<CMyString> vecRemoveSec;
            for(int j=0;j<m_Groups[i].GetSecCount();j++)
            {
                bool found_section=false;
                CMyString secMac=m_Groups[i].GetSecMac(j);
                for(int k=0;k<user->GetSectionCount();k++ )
                {
                    if(secMac == user->GetSectionMac(k).data())
                    {
                        found_section=true;
                        break;
                    }
                }
                if(!found_section)
                {
                    vecRemoveSec.push_back(secMac);
                    //此处有问题，删除后分组内分区总数变更,需要先加入到向量表，后面再统一删除
                    //m_Groups[i].RemoveSection(secMac);
                }
            }

            if(vecRemoveSec.size()>0)
            {
                IsUpdate=true;
                for(int j=0;j<vecRemoveSec.size();j++)
                {
                    m_Groups[i].RemoveSection(vecRemoveSec[j]);
                }
            }
        }
    }

    if(IsUpdate)
        g_Global.WriteXmlFile(FILE_GROUP);
}


//所有分组内分区排序(升序)
void   CGroups::GroupsSectionSort(void)
{
    bool IsUpdate=false;
    int nGroupCount = GetGroupCount();
    for (int i=0; i<nGroupCount; ++i)
    {
        if( m_Groups[i].GroupSectionSort() )
            IsUpdate=true;
    }
    if(IsUpdate)
        g_Global.WriteXmlFile(FILE_GROUP);
}

 //根据分区ID排序(升序)
bool   CGroups::SortBySectionId(const CMyString &secMac1, const CMyString &secMac2)
{
    CSection* sec1 = g_Global.m_Sections.GetSectionByMac(secMac1);
    CSection* sec2 = g_Global.m_Sections.GetSectionByMac(secMac2);
    if(sec1!=NULL && sec2!=NULL)
    {
        return sec1->GetID() < sec2->GetID();
    }
    return true;
}