#include "stdafx.h"
#include "PlayList.h"
#include "Global/GlobalMethod.h"
#include <qdir.h>
#include <QTextCodec>
#include <QProcess>

CSong::CSong(CMyString strPathName, int nDuration, int nSize)
{
    m_strPathName	= strPathName;
    m_nDuration		= nDuration;
    m_nSize			= nSize;
    m_nLowRateSize  = 0;
    m_alive         = FALSE;
    m_bitrate       = 0;
    m_md5           = "";
    m_md5_lbrc      = "";
    m_strLowRateFile = "";
    m_strUserAccount = "";
    #if SUPPORT_SONG_MANAGER
    m_bHasAudit      = FALSE;
    #endif
    m_strName = GetNameByHttpPathName(m_strPathName);
}

void CSong::SetPathName(CMyString strPathName)
{

    m_strPathName = strPathName;

    m_strName = GetNameByHttpPathName(m_strPathName);
}

bool  CSong::SetLowRateFile(CMyString strPathName)
{
    CMyString strLowRateFilePath=g_Global.m_strFolderPath+strPathName+SONG_LOW_RATE_EXT_NAME;
    if(IsExistFile(strLowRateFilePath.Data()))
    {
        m_strLowRateFile=strLowRateFilePath;
        return true;
    }
    else
    {
        m_strLowRateFile="";
        return false;
    }
}

CMyString CSong::GetLowRateFile()
{
    return m_strLowRateFile;
}

bool CSong::IsStreamFormat(void)
{
    return g_Global.m_SongTool.IsStreamFormat(m_strPathName);
}

bool CSong::IsExist(void)
{
    if (g_Global.m_nSongsFrom == SONGS_FROM_NETWORK)
    {
        return TRUE;
    }
    CMyString SongPath=m_strPathName;
    CMyString strPath1toFour = m_strPathName.Mid(1,4);
    if(strPath1toFour==HTTP_FOLDER_ADATA)
    {
        SongPath=g_Global.m_strHttpRootDir + m_strPathName;
    }
    return g_Global.m_SongTool.IsPathExist(SongPath.Data());
}


void CSong::SetAlive(bool IsAlive)
{
    m_alive=IsAlive;
}

bool CSong::GetAlive(void)
{
    return  m_alive;
}


CMyString CSong::GetUserAccount(void)
{
    return m_strUserAccount;
}

void CSong::SetUserAccount(CMyString strUserAccount)
{
    m_strUserAccount=strUserAccount;
}


/*-----------------      CSongList       -------------------------*/

CSongList::~CSongList(void)
{
    if(!m_Song.empty())
    {
        RemoveAllSongs();
    }
}

void CSongList::SetUserAccount(CMyString strUserAccount)
{
    if( g_Global.m_Users.IsExistUser(strUserAccount.Data()) )
    {
        m_strUserAccount=strUserAccount;
    }
    else
    {
        m_strUserAccount=SUPER_USER_NAME;
    }

}


bool CSongList::AddSong(CSong& song)
{
    int nMaxSongsCount = MAX_SONGS_PER_LIST_COUNT;
    //如果是TTS列表，单独处理
    if(GetID() == g_Global.m_PlayList.GetListID(0))
    {
        nMaxSongsCount = MAX_SONGS_TTS_LIST_COUNT;
    }
    if (GetSongCount() >= nMaxSongsCount)
    {
        return FALSE;
    }

    m_Song.push_back(song);

    return TRUE;
}


void CSongList::RemoveSong(int nSong)
{
    vector<CSong>::iterator iter;
    int i = 0;
    for(i=0, iter=m_Song.begin(); iter!=m_Song.end(); ++i, ++iter)
    {
        if(nSong == i)
        {
            #if !SUPPORT_SONG_MANAGER
            g_Global.m_PlayList.DelLowRateSong(iter->GetPathName());
            #endif
            m_Song.erase(iter);
            break;	// 务必加上break
        }
    }
}

void CSongList::RemoveSongByName(CMyString strPathName)
{
    vector<CSong>::iterator iter;
    int i = 0;
    for(i=0, iter=m_Song.begin(); iter!=m_Song.end(); ++i, ++iter)
    {

        if(iter->GetPathName() == strPathName)
        {
            #if !SUPPORT_SONG_MANAGER
            g_Global.m_PlayList.DelLowRateSong(iter->GetPathName());
            #endif
            m_Song.erase(iter);
            break;	// 务必加上break
        }
    }
}

void CSongList::RemoveAllSongs(void)
{
    m_Song.clear();
}


/*-------------       CPlayList            ------------*/

CPlayList::CPlayList(void)
{
    m_PlayMode		= PM_ORDER;
    m_strFolder		= HTTP_FOLDER_XML;
    m_strFileName	= ("");

    m_nSelList		= -1;
    m_nSelSong		= -1;
    m_strDateTime	= ("");
    m_SongLists.clear();

    // zhuyg
    m_bNeedNotify = false;
    PlaylistUpgradeMutex = PTHREAD_MUTEX_INITIALIZER;
    //LowRateSongsModQmutex = PTHREAD_MUTEX_INITIALIZER;
    PlaylistXmlUpgradeMutex = PTHREAD_MUTEX_INITIALIZER;

    availLBrcThreadCnt=SONG_LOW_RATE_MAX_THREAD;
}


CPlayList:: ~CPlayList(void)
{
    ClearList();
    //******** 静态初始化的互斥锁不需要,也不能用pthread_mutex_destroy销毁锁，否则将出错
}


int CPlayList::GetListCount(void)
{
    return m_SongLists.size();
}


bool CPlayList::AddList(CMyString strListName, CMyString strUserAccount)
{
    int	nMaxListCount = MAX_PLAY_LIST_COUNT;
    pthread_mutex_lock(&PlaylistUpgradeMutex);
    if (GetListCount() >= nMaxListCount)
    {
        pthread_mutex_unlock(&PlaylistUpgradeMutex);
        return FALSE;
    }

    CSongList	songList(strListName);
    songList.SetID(GetGUID());
    songList.SetUserAccount(strUserAccount);
    m_SongLists.push_back(songList);
    pthread_mutex_unlock(&PlaylistUpgradeMutex);
    return TRUE;
}

bool CPlayList::AddList(CMyString strID, CMyString strListName, CMyString strUserAccount)
{
    int	nMaxListCount = MAX_PLAY_LIST_COUNT;
    pthread_mutex_lock(&PlaylistUpgradeMutex);
    if (GetListCount() >= nMaxListCount)
    {
        pthread_mutex_unlock(&PlaylistUpgradeMutex);
        return FALSE;
    }

    CSongList	songList(strListName);
    songList.SetID(strID);
    songList.SetUserAccount(strUserAccount);
    m_SongLists.push_back(songList);
    pthread_mutex_unlock(&PlaylistUpgradeMutex);
    return TRUE;
}


bool CPlayList::AddList(CSongList& songList)
{
    int	nMaxListCount = MAX_PLAY_LIST_COUNT;
    pthread_mutex_lock(&PlaylistUpgradeMutex);
    if (GetListCount() >= nMaxListCount)
    {
        pthread_mutex_unlock(&PlaylistUpgradeMutex);
        return FALSE;
    }

    m_SongLists.push_back(songList);
    pthread_mutex_unlock(&PlaylistUpgradeMutex);
    return TRUE;
}


void CPlayList::RemoveList(int nList)
{
    pthread_mutex_lock(&PlaylistUpgradeMutex);
    vector<CSongList>::iterator iter;
    int i = 0;
    for(i=0, iter=m_SongLists.begin(); iter!=m_SongLists.end(); ++i, ++iter)
    {
        if(nList == i)
        {
            m_SongLists.erase(iter);
            break;	// 务必加上break
        }
    }
    pthread_mutex_unlock(&PlaylistUpgradeMutex);
}

void CPlayList::RemoveList(CMyString strUserAccount)
{
    pthread_mutex_lock(&PlaylistUpgradeMutex);

    bool IsUpdate=false;
    int nListCount = GetListCount();

    vector<int> vecListId;
    for(int i=0; i<nListCount; i++)
    {
        if(m_SongLists[i].GetUserAccount() == strUserAccount)
        {
            IsUpdate=true;
            vecListId.push_back(i);
        }
    }
    pthread_mutex_unlock(&PlaylistUpgradeMutex);

    //再删除播放列表
    for(int i=0;i<vecListId.size();i++)
    {
        RemoveList(vecListId[i]);
    }

    if(IsUpdate)
    {
        g_Global.m_PlayList.PushWriteXmlTask(strUserAccount);
    }
}


void CPlayList::ClearList(void)
{
    pthread_mutex_lock(&PlaylistUpgradeMutex);
    m_SongLists.clear();

    m_nSelList		= -1;
    m_nSelSong		= -1;

    m_strDateTime	= ("");
    pthread_mutex_unlock(&PlaylistUpgradeMutex);
}

void CPlayList::RemoveAllSongFile()
{
    // 移除所有歌曲文件
    CMyString songPath1, songPath2;
    songPath1.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_OTHER);
    songPath2.Format("%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM_MUSIC);

    RemoveDirectoryFile(songPath1.C_Str(),false);
    RemoveDirectoryFile(songPath2.C_Str(),false);
}


//重置歌曲列表
void CPlayList::ResetList(void)
{
    ClearList();

    // 重置歌曲列表与原实现不同 保留，待修改
    // 预置我的列表与钟声
    AddList(CMyString(TTS_LIST_NAME),SUPER_USER_NAME);
    AddList(LANG_STR(LANG_SECTION_PLAY_LIST, "My List", ("我的列表")),SUPER_USER_NAME);

    CMyString strNow = GetCurrentDateTime();

    CSongList& myList0 = GetSongList(0);
    myList0.SetDateTime(strNow);
    CSongList& myList1 = GetSongList(1);
    myList1.SetDateTime(strNow);

    SetDateTime(strNow);
    WriteFile();
}


CMyString CPlayList::GetListID(int nList)
{
    return m_SongLists[nList].GetID();
}

void CPlayList::SetListID(int nList, CMyString strID)
{
    m_SongLists[nList].SetID(strID);
}

CMyString CPlayList::GetListName(int nList)
{
    if(nList >= m_SongLists.size())
    {
        return "";
    }

    return m_SongLists[nList].GetListName();
}

void CPlayList::SetListName(int nList, CMyString strListName)
{
    m_SongLists[nList].SetListName(strListName);
}


CMyString CPlayList::GetListDateTime(int nList)
{
    return m_SongLists[nList].GetDateTime();
}

void CPlayList::SetListDateTime(int nList, CMyString strDateTime)
{
    if (nList < 0 || nList >= GetListCount())
    {
        return;
    }

    m_SongLists[nList].SetDateTime(strDateTime);

    /*if (strDateTime != GetDateTime())       // zhuyg
    {
        SetDateTime(strDateTime);
    }*/
}

void CPlayList::SetListUserAccount(int nList, CMyString strUserAccount)
{
    m_SongLists[nList].SetUserAccount(strUserAccount);
}


CMyString CPlayList::GetListUserAccount(int nList)
{
    return m_SongLists[nList].GetUserAccount();
}

int  CPlayList::FindFirstIndexOfAccountList(CMyString strUserAccount)    //查找当前账户的第一个歌曲列表？(除TTS LIST外，如果没有列表，则返回-1）
{
    int nListCount = GetListCount();
    int listIndex=-1;
    for(int i=0; i<nListCount; i++)
    {
        CSongList list = GetSongList(i);
        if(i!=0 && list.GetUserAccount() == strUserAccount)
        {
            listIndex=i;
            break;
        }
    }
    return listIndex;
}

int CPlayList::GetAllSongCount()
{
    int nAllSongCount = 0;
    int nListCount = GetListCount();

    for(int i=0; i<nListCount; i++)
    {
        int nSongCount = g_Global.m_HigherHost.m_Playlist.GetListSongCount(i);
        nAllSongCount += nSongCount;
    }

    return nAllSongCount;
}


CSong& CPlayList::GetListSong(int nList, int nSong)
{
    return m_SongLists[nList].GetSong(nSong);
}

int CPlayList::GetListSongDuration(int nList, int nSong)
{
    return m_SongLists[nList].GetSongDuration(nSong);
}

int CPlayList::GetListSongSize(int nList, int nSong)
{
    return m_SongLists[nList].GetSongSize(nSong);
}

int CPlayList::GetListSongLowRateSize(int nList, int nSong)
{
    return m_SongLists[nList].GetLowRateSongSize(nSong);
}

int CPlayList::GetListSongBitRate(int nList, int nSong)
{
    return m_SongLists[nList].GetSongBitRate(nSong);
}

string CPlayList::GetListSongMd5(int nList, int nSong)
{
    return m_SongLists[nList].GetSongMd5(nSong);
}

CMyString CPlayList::GetListSongLowRateFile(int nList, int nSong)
{
    return m_SongLists[nList].GetLowRateFile(nSong);
}

int CPlayList::GetListSongCount(int nList)
{
    return m_SongLists[nList].GetSongCount();
}

int CPlayList::GetTTSListSongCountByAccount(int nList,CMyString strUserAccount)
{
    //适用于龙之音V1版本
    int songCnt=0;
    for(int i=0;i<GetListSongCount(nList);i++)
    {
        CSong &song=GetListSong(nList,i);
        if(song.GetUserAccount() == strUserAccount)
        {
            songCnt++;
        }
    }
    return songCnt;
}


CMyString CPlayList::GetListSongPathName(int nList, int nSong)
{
    return m_SongLists[nList].GetSongPathName(nSong);
}

CMyString CPlayList::GetListSongName(int nList, int nSong)
{
    return m_SongLists[nList].GetSongName(nSong);
}

CMyString CPlayList::GetSongUserAccount(int nList, int nSong)
{
    return m_SongLists[nList].GetSongAccount(nSong);
}

void CPlayList::AddListSong(int nList, CSong& song)
{
    pthread_mutex_lock(&PlaylistUpgradeMutex);
    m_SongLists[nList].AddSong(song);
    pthread_mutex_unlock(&PlaylistUpgradeMutex);
}

void CPlayList::RemoveListSong(int nList, int nSong)
{
    pthread_mutex_lock(&PlaylistUpgradeMutex);
    m_SongLists[nList].RemoveSong(nSong);
    pthread_mutex_unlock(&PlaylistUpgradeMutex);
}

void CPlayList::RemoveListSong(int nList, CMyString strPathName)
{
    pthread_mutex_lock(&PlaylistUpgradeMutex);
    m_SongLists[nList].RemoveSongByName(strPathName);
    pthread_mutex_unlock(&PlaylistUpgradeMutex);
}

void CPlayList::ClearListSong(int nList)
{
    pthread_mutex_lock(&PlaylistUpgradeMutex);
    m_SongLists[nList].RemoveAllSongs();
    pthread_mutex_unlock(&PlaylistUpgradeMutex);
}

bool CPlayList::UpdateSongInfo(CSong& song)
{
    pthread_mutex_lock(&PlaylistUpgradeMutex);
    bool IsUpdate=false;
    int nListCount = GetListCount();
    for(int i=0; i<nListCount; i++)
    {
        int list_update=false;
        int songId=FindSongIdByList(song.GetPathName(),i);
        if(songId!=-1)
        {
            CSong& mSong = GetListSong(i,songId);
            mSong.SetAlive(song.GetAlive());
            if(mSong.GetBitRate()!=song.GetBitRate())
            {
                IsUpdate=true;
                list_update=true;
                mSong.SetBitRate(song.GetBitRate());
            }
            if(mSong.GetDuration()!=song.GetDuration())
            {
                IsUpdate=true;
                list_update=true;
                mSong.SetDuration(song.GetDuration());
            }
            if(mSong.GetSize()!=song.GetSize())
            {
                IsUpdate=true;
                list_update=true;
                mSong.SetSize(song.GetSize());
            }
            if(mSong.GetMd5()!=song.GetMd5())
            {
                IsUpdate=true;
                list_update=true;
                mSong.SetMd5(song.GetMd5());
            }
            #if 0
            if( mSong.GetLowRateFile() != song.GetLowRateFile() )
            {
                IsUpdate=true;
                list_update=true;
                mSong.SetLowRateFile(song.GetLowRateFile());
            }
            #endif
        }
        if(list_update)
        {
            SetListDateTime(i, GetCurrentDateTime());
        }
    }
    pthread_mutex_unlock(&PlaylistUpgradeMutex);
    return IsUpdate;
}


// 保留，待修改
//bool CPlayList::ExportListFile(int nList, string strPathName)
//{
//    return true;
//}


bool CPlayList::ReadFile()
{
    if(m_strFileName == "")
    {
        return FALSE;
    }

    TiXmlDocument* xmlPlayList = new TiXmlDocument;
    int		nList					= 0;
    CHAR	szPathName[STR_MAX_PATH]	= {0};

    CombinHttpURL(szPathName, m_strFolder.C_Str(), m_strFileName.Data());

    int	nMaxListCount	= MAX_PLAY_LIST_COUNT;
    int	nMaxSongsCount	= MAX_SONGS_PER_LIST_COUNT;

    bool update_file=false;
    if(xmlPlayList->LoadFile(szPathName))
    {
        m_SongLists.clear();

        TiXmlElement* playList = xmlPlayList->FirstChildElement();
        SetDateTime(CMyString(playList->Attribute("DateTime")));

        // List
        int listIndex=0;
        for(TiXmlElement* List=playList->FirstChildElement(); List!=NULL;
            List=List->NextSiblingElement(),listIndex++)
        {
            if(GetListCount() >= nMaxListCount)
            {
                break;
            }

            AddList(List->Attribute("Name"),SUPER_USER_NAME);
            SetListID(nList, List->Attribute("ID"));
            if( List->Attribute("UserName") == NULL )
            {
                SetListUserAccount(nList,SUPER_USER_NAME);
            }
            else
            {
                SetListUserAccount(nList,List->Attribute("UserName"));
            }
            SetListDateTime(nList, List->Attribute("DateTime"));

            // Song
            for(TiXmlElement* Song=List->FirstChildElement(); Song!=NULL;
                Song=Song->NextSiblingElement())
            {
                if(listIndex == 0)  //TTS单独处理
                {
                    nMaxSongsCount = MAX_SONGS_TTS_LIST_COUNT;
                }
                else
                {
                    nMaxSongsCount = MAX_SONGS_PER_LIST_COUNT;
                }
                if(GetListSongCount(nList) >= nMaxSongsCount)
                {
                    break;
                }

                CSong csong;
                csong.SetDuration(atoi(Song->Attribute("Duration")));
                csong.SetSize(atoi(Song->Attribute("Size")));
                csong.SetPathName(Song->Attribute("PathName"));
                if(Song->Attribute("BitRate") == NULL)
                {
                    update_file=true;
                    CMyString SongPath=g_Global.m_strHttpRootDir + csong.GetPathName();
                    song_header_t  songHeader=g_Global.m_SongTool.GetSongInfo(SongPath);
                    csong.SetBitRate(songHeader.bitrate);
                }
                else
                {
                    csong.SetBitRate(atoi(Song->Attribute("BitRate")));
                }
                if(Song->Attribute("md5") == NULL)
                {
                    update_file=true;
                    CMyString SongPath=g_Global.m_strHttpRootDir + csong.GetPathName();
                    csong.SetMd5(GetFileMd5(SongPath.Data()));
                }
                else
                {
                    csong.SetMd5(Song->Attribute("md5"));
                }
                if(Song->Attribute("lbrc") == NULL)
                {
                    csong.SetLowRateFile("");
                }
                else
                {
                    const char *lbrc=Song->Attribute("lbrc");
                    if( strcmp(lbrc,"TRUE") == 0 )
                    {
                        csong.SetLowRateFile(csong.GetPathName());
                    }
                    else
                    {
                        csong.SetLowRateFile("");
                    }
                }
                if(Song->Attribute("UserName") == NULL)
                {
                    csong.SetUserAccount(GetListUserAccount(nList));
                }
                else 
                {
                    csong.SetUserAccount(Song->Attribute("UserName"));
                }


                AddListSong(nList, csong);
            }

            nList++;
        }
    }
    else
    {
        delete xmlPlayList;
        return FALSE;
    }

    xmlPlayList->Clear();
    delete xmlPlayList;

    if(update_file)
    {
        //g_Global.m_PlayList.PushWriteXmlTask("");
        WriteFile();
    }
    return TRUE;
}


bool CPlayList::WriteFile(bool bUpdateDateTime)
{
    if(m_strFileName == "")
    {
        return FALSE;
    }
    pthread_mutex_lock(&PlaylistUpgradeMutex);
    TiXmlDocument xmlPlayList;
    int		nListCount	= GetListCount();
    int		nSongCount = 0;

    CHAR	szPathName[STR_MAX_PATH] = {0};
    CombinHttpURL(szPathName, m_strFolder.Data(), m_strFileName.Data());

    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0","utf-8","no");
    xmlPlayList.LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment("Save for Playlist");
    xmlPlayList.LinkEndChild(com);

    TiXmlElement* playList = new TiXmlElement("PlayList");
    playList->SetAttribute("ListCount", nListCount);
    playList->SetAttribute("DateTime",  m_strDateTime.Data());
    xmlPlayList.LinkEndChild(playList);

    for(int i=0; i<nListCount; i++)
    {
        nSongCount = GetListSongCount(i);
        TiXmlElement* List = new TiXmlElement("List");
        List->SetAttribute("ID", GetListID(i).C_Str());

        //string strListName = StringToUTF8(GetListName(i).C_Str());
        List->SetAttribute("Name", GetListName(i).C_Str());
        List->SetAttribute("SongCount", nSongCount);
        List->SetAttribute("UserName", GetListUserAccount(i).C_Str());
        List->SetAttribute("DateTime", GetListDateTime(i).C_Str());

        for(int j=0; j<nSongCount; j++)
        {
            TiXmlElement* Song = new TiXmlElement("Song");
            Song->SetAttribute("Duration", GetListSongDuration(i, j));
            Song->SetAttribute("Size", GetListSongSize(i, j));
            Song->SetAttribute("PathName",GetListSongPathName(i,j).C_Str());
            Song->SetAttribute("BitRate",GetListSongBitRate(i,j));
            Song->SetAttribute("md5",GetListSongMd5(i,j).data());
            Song->SetAttribute("lbrc",(GetListSongLowRateFile(i,j) == "")?("FALSE"):("TRUE") );
            Song->SetAttribute("UserName", GetSongUserAccount(i,j).Data());
            List->LinkEndChild(Song);
        }

        playList->LinkEndChild(List);
    }

    bool saveFileOK=true;
    if(xmlPlayList.SaveFile(szPathName))
    {
        SetNeedNotify(true);
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlPlayList.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }

    pthread_mutex_unlock(&PlaylistUpgradeMutex);
    return saveFileOK;
}



bool	CPlayList::ReadFileBySpecAccount(CMyString strUserAccount)
{
    #if !APP_IS_LZY_LIMIT_STORAGE
    return false;
    #endif

    CMyString m_strPlaylist_account_fileName;
    m_strPlaylist_account_fileName.Format("%s_%s.xml","Playlist",strUserAccount.Data());

    TiXmlDocument* xmlPlayList = new TiXmlDocument;
    int		nList					= 0;
    CHAR	szPathName[STR_MAX_PATH]	= {0};

    CombinHttpURL(szPathName, HTTP_FOLDER_PLAYLIST_OWN, m_strPlaylist_account_fileName.Data());

    LPCUserInfo pUser=g_Global.m_Users.GetUserByAccount(strUserAccount.Data());
    if(!pUser)
    {
        delete xmlPlayList;
        return false;
    }
    if(xmlPlayList->LoadFile(szPathName))
    {
        TiXmlElement* playList = xmlPlayList->FirstChildElement();
        //设置DateTime
        pUser->SetPlaylistDateTime(CMyString(playList->Attribute("DateTime")));
    }
    else
    {
        delete xmlPlayList;
        return FALSE;
    }

    xmlPlayList->Clear();
    delete xmlPlayList;

    return TRUE;
}

bool  CPlayList::WriteFileBySpecAccount(CMyString strUserAccount,BOOL bUpdateDateTime)
{
    #if !APP_IS_LZY_LIMIT_STORAGE
    return false;
    #endif

    CMyString m_strPlaylist_account_fileName;
    m_strPlaylist_account_fileName.Format("%s_%s.xml","Playlist",strUserAccount.Data());
    pthread_mutex_lock(&PlaylistUpgradeMutex);
    TiXmlDocument xmlPlayList;
    int		nListCount	= GetListCount();
    int		nSongCount = 0;

    LPCUserInfo pUser=g_Global.m_Users.GetUserByAccount(strUserAccount.Data());
    if(!pUser)
    {
        pthread_mutex_unlock(&PlaylistUpgradeMutex);
        return false;
    }
    CMyString currentDataTime=GetCurrentDateTime();

    //找到当前账户下总共有多少个列表？(账户名属于本身的)
    int playListAccountCnt=0;
    for(int i=0; i<nListCount; i++)
    {
        if(GetListUserAccount(i) == strUserAccount || i == 0)   //此处不用GetListName(i) == TTS_LIST_NAME，因为列表名可以重复
        {
            playListAccountCnt++;
        }
    }

    CHAR	szPathName[STR_MAX_PATH] = {0};
    CombinHttpURL(szPathName, HTTP_FOLDER_PLAYLIST_OWN, m_strPlaylist_account_fileName.Data());

    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0","utf-8","no");
    xmlPlayList.LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment("Save for Playlist");
    xmlPlayList.LinkEndChild(com);

    TiXmlElement* playList = new TiXmlElement("PlayList");
    playList->SetAttribute("ListCount", playListAccountCnt);
    playList->SetAttribute("DateTime",  currentDataTime.Data());
    xmlPlayList.LinkEndChild(playList);

    for(int i=0; i<nListCount; i++)
    {
        //只写入属于当前账户的列表(tts list除外),龙之音V1版本不考虑所有列表管理权限了，因为不会用到
        if(i>0)
        {
            //if(GetListUserAccount(i) != strUserAccount && !pUser->HasLimits(USER_LIMITS_PLAYLIST))
            if(GetListUserAccount(i) != strUserAccount)
            {
                continue;
            }
        }
        nSongCount = GetListSongCount(i);
        int playListSongAccountCnt= GetTTSListSongCountByAccount(i,strUserAccount);
        TiXmlElement* List = new TiXmlElement("List");
        List->SetAttribute("ID", GetListID(i).C_Str());

        //string strListName = StringToUTF8(GetListName(i).C_Str());
        List->SetAttribute("Name", GetListName(i).C_Str());
        List->SetAttribute("SongCount", playListSongAccountCnt);
        List->SetAttribute("UserName", GetListUserAccount(i).C_Str());
        List->SetAttribute("DateTime", GetListDateTime(i).C_Str());

        for(int j=0; j<nSongCount; j++)
        {
            //if(GetSongUserAccount(i,j) != strUserAccount || pUser->HasLimits(USER_LIMITS_PLAYLIST))
            if(GetSongUserAccount(i,j) != strUserAccount)
            {
                continue;
            }
            TiXmlElement* Song = new TiXmlElement("Song");
            Song->SetAttribute("Duration", GetListSongDuration(i, j));
            Song->SetAttribute("Size", GetListSongSize(i, j));
            Song->SetAttribute("PathName",GetListSongPathName(i,j).C_Str());
            Song->SetAttribute("BitRate",GetListSongBitRate(i,j));
            Song->SetAttribute("md5",GetListSongMd5(i,j).data());
            Song->SetAttribute("lbrc",(GetListSongLowRateFile(i,j) == "")?("FALSE"):("TRUE") );
            Song->SetAttribute("UserName", GetSongUserAccount(i,j).Data());
            List->LinkEndChild(Song);
        }

        playList->LinkEndChild(List);
    }

    bool saveFileOK=true;
    if(xmlPlayList.SaveFile(szPathName))
    {
        SetNeedNotify(true);
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlPlayList.Clear();

    if (bUpdateDateTime)
    {
        pUser->SetPlaylistDateTime(currentDataTime);
    }

    pthread_mutex_unlock(&PlaylistUpgradeMutex);
    return saveFileOK;
}


void  CPlayList::RemoveXmlFileOfSpecAccount(CMyString strUserAccount)
{
    #if !APP_IS_LZY_LIMIT_STORAGE
    return;
    #endif

    pthread_mutex_lock(&PlaylistUpgradeMutex);
    CMyString m_strPlaylist_account_fileName;
    m_strPlaylist_account_fileName.Format("%s_%s.xml","Playlist",strUserAccount.Data());
    CHAR	szPathName[STR_MAX_PATH] = {0};
    CombinHttpURL(szPathName, HTTP_FOLDER_PLAYLIST_OWN, m_strPlaylist_account_fileName.Data());
    RemoveFile(szPathName);
    pthread_mutex_unlock(&PlaylistUpgradeMutex);
}



// 从列表中查找歌曲是否存在
bool CPlayList::FindSongByList(CMyString strPathName, int nList)
{
    int nSongCount = GetListSongCount(nList);

    for (int i=0; i<nSongCount; ++i)
    {
        if (GetListSongPathName(nList, i) == strPathName)
        {
            return TRUE;
        }
    }

    return FALSE;
}


// 从列表中查找歌曲id
int CPlayList::FindSongIdByList(CMyString strPathName, int nList)
{
    int nSongCount = GetListSongCount(nList);

    for (int i=0; i<nSongCount; ++i)
    {
        if (GetListSongPathName(nList, i) == strPathName)
        {
            return i;
        }
    }
    return -1;
}


int CPlayList::FindListByName(CMyString strListName)
{
    int nListCount = GetListCount();

    for (int i=0; i<nListCount; ++i)
    {
        if (GetListName(i) == strListName)
        {
            return i;
        }
    }

    return -1;
}


int CPlayList::FindListByIdAndName(CMyString strListID, CMyString strListName)
{
    int nListCount = GetListCount();

    for (int i=0; i<nListCount; ++i)
    {
        if (GetListID(i) == strListID && GetListName(i) == strListName)
        {
            return i;
        }
    }

    return -1;
}


int CPlayList::FindListByName(CMyString strListName, int nList)
{
    int nListCount = GetListCount();

    for (int i=0; i<nListCount; ++i)
    {
        if (i != nList && GetListName(i) == strListName)
        {
            return i;
        }
    }

    return -1;
}


bool CPlayList::UpdateListName(CMyString strOldDefaultListName, bool bChimeList)
{
    bool	bUpdated	= FALSE;
    CMyString strDateTime	= GetCurrentDateTime();

    for (int i=0; i<GetListCount(); ++i)
    {
        CMyString strListName = GetListName(i);

        if (strListName.Find(strOldDefaultListName) == 0)
        {
            // 歌曲列表
            if (!bChimeList)
            {
                strListName.Replace(strOldDefaultListName, LANG_STR(LANG_SECTION_PLAY_LIST, "My List", ("我的列表")));
            }
            // 钟声列表
            else
            {
                strListName.Replace(strOldDefaultListName, LANG_STR(LANG_SECTION_PLAY_LIST, "Chime List", ("钟声")));
            }
            SetListName(i, strListName);
            SetListDateTime(i, strDateTime);

            bUpdated = TRUE;
        }
    }

    if (bUpdated)
    {
        //g_Global.WriteXmlFile(FILE_PLAYLIST);       //zhuyg
        g_Global.m_PlayList.PushWriteXmlTask("");
    }

    return bUpdated;
}


// 歌曲是否存在于列表中
bool CPlayList::IsSongInPlayList(CMyString strPathName)
{
    int nListCount = GetListCount();

    for (int i=0; i<nListCount; ++i)
    {
        int nSongCount = m_SongLists[i].GetSongCount();

        for (int j=0; j<nSongCount; ++j)
        {
            if (strPathName == m_SongLists[i].GetSongPathName(j))
            {
                return TRUE;
            }
        }
    }

    return FALSE;
}


bool CPlayList::IsSongInPlayList(int nListID, CMyString strPathName)
{
    int nSongCount = m_SongLists[nListID].GetSongCount();

    for (int i=0; i<nSongCount; ++i)
    {
        if (strPathName == m_SongLists[nListID].GetSongPathName(i))
        {
            return TRUE;
        }
    }
    return FALSE;
}


void CPlayList::FindSongInPlayList(CMyString strPathName, int& nList, int& nSong)
{
    int nListCount = GetListCount();

    for (int i=0; i<nListCount; ++i)
    {
        int nSongCount = m_SongLists[i].GetSongCount();

        for (int j=0; j<nSongCount; ++j)
        {
            if (strPathName == m_SongLists[i].GetSongPathName(j))
            {
                nList = i, nSong = j;
                return;
            }
        }
    }

    nList = -1, nSong = -1;
}


bool  CPlayList::IsSongInPlayListBySpecAccount(CMyString strPathName,CMyString strUserAccount)  //指定歌曲是否存在于指定账户的歌曲列表中
{
    int nListCount = GetListCount();
    for (int i=0; i<nListCount; ++i)
    {
        if(m_SongLists[i].GetUserAccount()!=strUserAccount)
        {
            continue;
        }
        int nSongCount = m_SongLists[i].GetSongCount();
        for (int j=0; j<nSongCount; ++j)
        {
            if (strPathName == m_SongLists[i].GetSongPathName(j))
            {
                return true;
            }
        }
    }
    return false;
}


int CPlayList::GetNextSong(int nList, int nSong,int nPlayMode)
{
    if (nList < 0 || nList >= GetListCount() || nSong < 0 || nSong >= GetListSongCount(nList))
    {
        return -1;
    }

    int	nSongCount	= GetListSongCount(nList);
    int nNextSong   = nSong;

    // 顺序和列表循环
    if (nPlayMode == PM_ORDER || nPlayMode == PM_LIST_CYCLE)
    {
        ++nNextSong;

        if (nNextSong >= nSongCount)
        {
            nNextSong = (nPlayMode == PM_ORDER ? -1 : 0);
        }
    }
    // 随机播放
    else if (nPlayMode == PM_RANDOM)
    {
        //srand((unsigned int)time(NULL));
        struct timespec spec;
        clock_gettime(CLOCK_MONOTONIC, &spec);
        unsigned long long timeTv = (unsigned long long)spec.tv_sec * 1000000 + (unsigned long long)spec.tv_nsec / 1000;
        srand((unsigned int)(timeTv & 0xFFFFFFFF));  // 使用低32位作为种子

        nNextSong = rand()%nSongCount;
    }
    // 单曲循环
    else if (nPlayMode == PM_SINGLE_CYCLE)
    {
        nNextSong = nSong;
    }
    // 单曲播放
    else if (nPlayMode == PM_SINGLE)
    {
        nNextSong = -1;
    }

    return nNextSong;
}
#if 0
int CPlayList::GetNextSong(const char *szListID, const char *szSongUrl, int &nList, int &nSong,int nPlayMode)
{
    //int	nSongCount	= GetListSongCount(nList);
    int nNextSong   = -1;

    int nListCount = GetListCount();
    for(int i=0; i < nListCount; i++)
    {
        CSongList& songList = GetSongList(i);
        if(songList.GetID() == szListID)
        {
            int nSongCount = songList.GetSongCount();
            for(int j=0; j<nSongCount; j++)
            {
                CSong song = songList.GetSong(j);
                if(song.GetPathName() == szSongUrl)
                {
                    // 顺序和列表循环
                    if (nPlayMode == PM_ORDER || nPlayMode == PM_LIST_CYCLE)
                    {
                        ++nNextSong;

                        if (nNextSong >= nSongCount)
                        {
                            nNextSong = (nPlayMode == PM_ORDER ? -1 : 0);
                        }
                    }
                    // 随机播放
                    else if (nPlayMode == PM_RANDOM)
                    {
                        //srand((unsigned int)time(NULL));
                        struct timespec spec;
                        clock_gettime(CLOCK_MONOTONIC, &spec);
                        unsigned long long timeTv = (unsigned long long)spec.tv_sec * 1000000 + (unsigned long long)spec.tv_nsec / 1000;
                        srand((unsigned int)(timeTv & 0xFFFFFFFF));  // 使用低32位作为种子

                        nNextSong = rand()%nSongCount;
                    }
                    // 单曲循环
                    else if (nPlayMode == PM_SINGLE_CYCLE)
                    {
                        nNextSong = j;
                    }
                    // 单曲播放
                    else if (nPlayMode == PM_SINGLE)
                    {
                        nNextSong = -1;
                    }
                }

                nList = i;
                nSong = j;
                break;
            }

            break;
        }
    }

    return nNextSong;
}
#endif


int CPlayList::GetPreSong(int nList, int nSong,int nPlayMode)
{
    if (nList < 0 || nList >= GetListCount() || nSong < 0 || nSong >= GetListSongCount(nList))
    {
        return -1;
    }

    int	nSongCount	= GetListSongCount(nList);
    int nNextSong   = nSong;

    if(nSongCount == 0)
    {
        nNextSong=-1;
    }
    else
    {
        // 随机播放
        if (nPlayMode == PM_RANDOM)
        {
            //srand((unsigned int)time(NULL));
            struct timespec spec;
            clock_gettime(CLOCK_MONOTONIC, &spec);
            unsigned long long timeTv = (unsigned long long)spec.tv_sec * 1000000 + (unsigned long long)spec.tv_nsec / 1000;
            srand((unsigned int)(timeTv & 0xFFFFFFFF));  // 使用低32位作为种子

            nNextSong = rand()%nSongCount;
        }
        else
        {
            nNextSong--;
            if(nNextSong == -1)
            {
                nNextSong = nSongCount-1;
            }
        }
    }

    return nNextSong;
}


// 歌曲文件是否存在
bool CPlayList::IsSongFileExist(int nList, int nSong)
{
    CSong& song = m_SongLists[nList].GetSong(nSong);

    return song.IsExist();
}


// 歌曲是否为可推送格式
bool CPlayList::IsSongStreamFormat(int nList, int nSong)
{
    CSong& song = m_SongLists[nList].GetSong(nSong);

    return song.IsStreamFormat();
}

CSong* CPlayList::FindSongByListAndName(CMyString strListID, CMyString strName, int& nListIndex, int& nSongIndex)
{
    int nListCount = GetListCount();

    for (int i=0; i<nListCount; ++i)
    {
        if (m_SongLists[i].GetID() == strListID)
        {
            int nSongCount = m_SongLists[i].GetSongCount();

            for (int j=0; j<nSongCount; ++j)
            {
                //if (m_SongLists[i].GetSongPathName(j).Find(strName) >= 0)
                if ( m_SongLists[i].GetSongName(j) == strName)
                {
                    nListIndex = i, nSongIndex = j;
                    return &m_SongLists[i].GetSong(j);
                }
            }
        }
    }

    //如果没有找到，尝试去掉扩展名后再次判断（寻呼台下发的歌名带有扩展名）
    string::size_type	nPos	= strName.ReverseFind('.');
    CMyString strNameMod;
    if(nPos!=string::npos)
    {
        strNameMod = strName.Left(nPos);
    }
    for (int i=0; i<nListCount; ++i)
    {
        if (m_SongLists[i].GetID() == strListID)
        {
            int nSongCount = m_SongLists[i].GetSongCount();

            for (int j=0; j<nSongCount; ++j)
            {
                if ( m_SongLists[i].GetSongName(j) == strNameMod)
                {
                    nListIndex = i, nSongIndex = j;
                    return &m_SongLists[i].GetSong(j);
                }
            }
        }
    }
    

    nListIndex = -1, nSongIndex = -1;
    return NULL;
}


bool CPlayList::ExistOwnFile(void)
{
    return (g_Global.m_nZoneData == ZONE_DATA_OWN && m_strDateTime != ("") &&
                IsExistFile(m_strFileName.Data()));    // zhuyg
}

void CPlayList::SetFileName(CMyString strFileName)
{
    m_strFileName = strFileName;
}

CMyString CPlayList::GetFileName(void)
{
    return m_strFileName;
}

CMyString CPlayList::GetFileFolder(void)
{
    return m_strFolder;
}


void CPlayList::SetFileFolder(CMyString strFolder)
{
    m_strFolder = strFolder;
}

// HTTP路径，保留，待修改
CMyString CPlayList::GetFileHttpPath(void)
{
    CMyString strHttpPath = HTTP_FOLDER_SEPARATOR;

    strHttpPath += HTTP_FOLDER_ADATA;
    strHttpPath += HTTP_FOLDER_SEPARATOR;
    strHttpPath += m_strFolder;             // "Xml/PlayList/"
    strHttpPath += HTTP_FOLDER_SEPARATOR;
    strHttpPath += m_strFileName;

    return strHttpPath;
}

int CPlayList::GetListByID(CMyString strID)
{
        for(int i=0; i<GetListCount(); i++)
        {
                if(m_SongLists[i].GetID() == strID)
                {
                        return i;
                }
        }
        return -1;
}

bool CPlayList::ConfirmListSong(int nList, int nSong)
{
    CSong&	song	= GetListSong(nList, nSong);
    bool	bResult = false;

    if (!song.IsExist())
    {
        bResult = false;
    }
    else if (WP_IS_CENTRALIZED && !song.IsStreamFormat())
    {
        bResult = false;
    }
    else
    {
        bResult = true;
    }

    return bResult;
}

// 歌曲是否与列表中
bool CPlayList::IsExistInList(CMyString strFilePath)
{
    int nList = -1;
    int nSong = -1;

    FindSongInPlayList(strFilePath, nList, nSong);
    if(nList > -1 && nSong > -1)
    {
        return TRUE;
    }

    return FALSE;
}

//判断这个用户有几个列表
int  CPlayList::GetSpecUserPlaylistCount(CMyString strUserAccount)
{
    int nCount = 0;
    for(int i=0; i<GetListCount(); i++)
    {
        if(m_SongLists[i].GetUserAccount() == strUserAccount)
        {
            nCount++;
        }
    }
    return nCount;
}

// 清除不在播放列表的歌曲文件
void CPlayList::ClearBesidesListSongFile()
{
    char szPath[1024] = {0};
    sprintf(szPath, "%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM);

    RemoveBesidesListSongFile(szPath);
}

void CPlayList::RemoveBesidesListSongFile(const char *szPath)
{
#if defined(Q_OS_LINUX)
    char szFilePathName[1024] = {0};
    strcpy(szFilePathName, szPath);

    DIR * dir;
    struct dirent *ptr;

    dir = opendir(szPath);       // 打开一个目录
    if(dir == NULL)
    {
        perror("opendir failed : ");
        return ;
    }
    while((ptr = readdir(dir)) != NULL)       // 循环读取目录数据
    {
        if(strcmp(ptr->d_name,".") == 0 || strcmp(ptr->d_name,"..") == 0)
            continue;
        else if(ptr->d_type == DT_REG)          //  file
        {
            sprintf(szFilePathName, "%s/%s", szPath, ptr->d_name);
            CMyString strHttpPath = GetHttpURLPathByPathName(CMyString(szFilePathName));

            if(!IsExistInList(strHttpPath))
            {
                //printf("filename : %s\n", szFilePathName);
                remove(szFilePathName);
            }
        }
        else if(ptr->d_type == DT_DIR)          // dir
        {
            sprintf(szFilePathName, "%s/%s", szPath, ptr->d_name);
            RemoveBesidesListSongFile(szFilePathName);
        }
    }

    closedir(dir);
#endif
}







void CPlayList::TabListSongFileExist(const char *szPath,bool NeedGetSongInfo)
{
    //printf("TabListSongFileExist:%s\n",szPath);
    char szFilePathName[1024] = {0};
    strcpy(szFilePathName, szPath);

    QDir dir(szFilePathName);
    if(!dir.exists())
    {
        perror("opendir failed : ");
        return ;
    }
    static bool updateSong=false;
    static vector<CMyString> vecUpdateSongAccount;

    char szLevel1Path[1024] = {0};
    sprintf(szLevel1Path, "%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM);
    if(strcmp(szLevel1Path,szPath) == 0)
    {
        updateSong=false;
        vecUpdateSongAccount.clear();
    }

    QFileInfoList list = dir.entryInfoList();//获取文件夹内部列表
    for (int i = 0; i < list.size(); i++) {//循环读取文件夹
        QFileInfo fileInfo = list.at(i);
        if(fileInfo.fileName()=="."|fileInfo.fileName()==".."){
            i=i+1;
            continue;
        }else{
            if(fileInfo.isDir()){//是否为文件夹
                QString dirPath= fileInfo.filePath();
                TabListSongFileExist(dirPath.toStdString().data(),NeedGetSongInfo);//循环读取文件夹内容
            }else{
                string fileName=fileInfo.fileName().toStdString();
                sprintf(szFilePathName, "%s/%s", szPath, fileName.data());
                CMyString strHttpPath = GetHttpURLPathByPathName(CMyString(szFilePathName));

                //printf("szFilePathName=%s\n",szFilePathName);
                //printf("strHttpPath=%s\n",strHttpPath.Data());

                if( fileName.find(SONG_LOW_RATE_EXT_NAME) != string::npos || fileName.find(SONG_LOW_RATE_EXT_TEMP_NAME) != string::npos )
                {
                    continue;
                }

#if SUPPORT_SONG_MANAGER
                CSong Lsong;
                Lsong.SetPathName(strHttpPath);
#endif

                if(IsExistInList(strHttpPath))
                {
                    int list_count = g_Global.m_PlayList.GetListCount();
                    bool updateAccount_flag=false;
                    for(int j=0;j<list_count;j++)
                    {
                        int songId=g_Global.m_PlayList.FindSongIdByList(strHttpPath,j);
                        if(songId>=0)
                        {
                            CSong &song = g_Global.m_PlayList.GetListSong(j,songId);
                            song.SetAlive(TRUE);

                            //判断时长和大小是否匹配，不匹配则重新获取
                            if( song.GetDuration()*song.GetBitRate()*1000/8 < (song.GetSize()/2)  )
                            {
                                printf("Song:%s Info error,get new!\n",song.GetPathName().Data());
                                CMyString strSongPath = g_Global.m_strFolderPath+song.GetPathName();
                                song_header_t  songHeader=g_Global.m_SongTool.GetSongInfo(strSongPath);
                                song.SetDuration(songHeader.nDuration);
                                song.SetBitRate(songHeader.bitrate);
                                
                                updateAccount_flag=true;
                                g_Global.m_PlayList.SetListDateTime(j, GetCurrentDateTime());
                            }

                            //判断是否属于低码率歌曲，如果不是，需要进行转换
                            if(song.GetBitRate() > SONG_LOW_BITRATE)
                            {
                                CMyString t_LowRateFile=song.GetLowRateFile();
                                //printf("Song1:%s ,t_LowRateFile=%s\n",song.GetPathName().Data(),song.GetLowRateFile().Data());
                                if( !song.SetLowRateFile(song.GetPathName()) )
                                {
                                    AddLowRateSong(song.GetPathName());
                                }
                                if( t_LowRateFile!=song.GetLowRateFile() )
                                {
                                    //printf("Song2:%s ,t_LowRateFile=%s\n",song.GetPathName().Data(),song.GetLowRateFile().Data());
                                    updateAccount_flag=true;
                                    g_Global.m_PlayList.SetListDateTime(j, GetCurrentDateTime());
                                }
                            }

                            if(updateAccount_flag)
                            {
                                updateSong=true;
                                if(std::find(vecUpdateSongAccount.begin(),vecUpdateSongAccount.end(),song.GetUserAccount())==vecUpdateSongAccount.end())
                                {
                                    vecUpdateSongAccount.push_back(song.GetUserAccount());
                                }
                            }

                            #if SUPPORT_SONG_MANAGER
                            if(NeedGetSongInfo && Lsong.GetSize() == 0)
                            {
                                Lsong.SetDuration(song.GetDuration());
                                Lsong.SetSize(song.GetSize());
                                Lsong.SetBitRate(song.GetBitRate());
                                Lsong.SetMd5(song.GetMd5());
                                Lsong.SetLowRateFile(song.GetPathName());
                                Lsong.SetUserAccount(song.GetUserAccount());
                            }
                            #endif
                        }
                    }
                }

                #if SUPPORT_SONG_MANAGER
                if(NeedGetSongInfo)
                {
                    //不记录TTS下的歌曲，不允许编辑
                    //if(strHttpPath.Find(HTTP_FOLDER_PROGRAM_COMMON) == string::npos)
                    {
                        if(Lsong.GetSize() == 0)
                        {
                            CMyString strSongPath = g_Global.m_strFolderPath+strHttpPath;
                            song_header_t  songHeader=g_Global.m_SongTool.GetSongInfo(strSongPath);
                            Lsong.SetDuration(songHeader.nDuration);
                            Lsong.SetBitRate(songHeader.bitrate);
                            Lsong.SetSize(fileInfo.size());
                            Lsong.SetMd5(GetFileMd5(strSongPath.Data()));
                            if(Lsong.GetBitRate() > SONG_LOW_BITRATE)
                            {
                                Lsong.SetLowRateFile(Lsong.GetPathName());
                            }
                            else
                            {
                                Lsong.SetLowRateFile("");
                            }
                            Lsong.SetUserAccount(SUPER_USER_NAME);
                        }
                        if(Lsong.GetDuration()>0)
                        {
                            g_Global.m_SongManager.AddSong(Lsong);
                        }
                    }
                }
                #endif

            }
        }
    }

    //递归第一层次才更新文件
    if(strcmp(szLevel1Path,szPath) == 0)
    {
        if(updateSong)
        {
            //更新XML文件
            for(int i=0;i<vecUpdateSongAccount.size();i++)
            {
                g_Global.m_PlayList.PushWriteXmlTask(vecUpdateSongAccount[i]);
            }
        }
        #if SUPPORT_SONG_MANAGER
            if(NeedGetSongInfo && g_Global.m_SongManager.GetSongsCount()>0)
            {
                g_Global.m_SongManager.WriteFile();
            }
            else if(g_Global.m_SongManager.GetSongsCount()>0)
            {
                g_Global.m_SongManager.ScanLocalSong();
            }
        #endif
    }
}



void CPlayList::UpdateUserPlaylist(CMyString strUserAccount)
{
    pthread_mutex_lock(&PlaylistUpgradeMutex);

    bool IsUpdate=false;
    int nListCount = GetListCount();
    for(int i=0; i<nListCount; i++)
    {
        if(m_SongLists[i].GetUserAccount() == strUserAccount)
        {
            IsUpdate=true;
            m_SongLists[i].SetUserAccount(SUPER_USER_NAME);
        }

        int nSongCount = GetListSongCount(i);
        for(int k=0;k<nSongCount;k++)
        {
            if(m_SongLists[i].GetSong(k).GetUserAccount() == strUserAccount)
            {
                IsUpdate=true;
                m_SongLists[i].GetSong(k).SetUserAccount(SUPER_USER_NAME);

                #if SUPPORT_SONG_MANAGER
                g_Global.m_SongManager.UpdateSong(m_SongLists[i].GetSong(k).GetPathName(),m_SongLists[i].GetSong(k));
                #endif
            }
        }
    }

    pthread_mutex_unlock(&PlaylistUpgradeMutex);

    if(IsUpdate)
        g_Global.m_PlayList.PushWriteXmlTask(strUserAccount);
}




void CPlayList::AddLowRateSong(CMyString strPathName)
{
    if(!IS_SYSTEM_IN_CLOUD)
        return;
    unique_lock<shared_timed_mutex> ulk_lowRateSong(*LowRateSongsModQSharedMutex);              //写锁加锁
    printf("AddLowRateSong:%s\n",strPathName.Data());
    int found=0;
    vector<stBitRateInfo>::iterator iter;
    for(iter=bitRateChangeSongs.begin(); iter!=bitRateChangeSongs.end();++iter)
    {
        CMyString songNamePath=iter->songNamePath;
        if(songNamePath == strPathName)
        {
            found=1;
            break;
        }
    }
    bool canAdd=false;
    if(found)
    {
        int status = iter->status;
        if(  status != SONG_LOW_RATE_STATUS_NOT_RUNNING && status != SONG_LOW_RATE_STATUS_RUNNING )
        {
            canAdd=true;
        }
    }
    else
    {
        canAdd=true;
    }
    if(canAdd)
    {
        stBitRateInfo bRchange;
        bRchange.songNamePath = strPathName;
        bRchange.status = SONG_LOW_RATE_STATUS_NOT_RUNNING;
        bRchange.needExit = false;
        bRchange.alreadyExit = false;
        bitRateChangeSongs.push_back(bRchange);
    }
}


void CPlayList::DelLowRateSong(CMyString strPathName)
{
    if(!IS_SYSTEM_IN_CLOUD)
        return;
    unique_lock<shared_timed_mutex> ulk_lowRateSong(*LowRateSongsModQSharedMutex);              //写锁加锁
    printf("DelLowRateSong:%s\n",strPathName.Data());
    int found=0;
    vector<stBitRateInfo>::iterator iter;
    for(iter=bitRateChangeSongs.begin(); iter!=bitRateChangeSongs.end();++iter)
    {
        CMyString songNamePath=iter->songNamePath;
        if(songNamePath == strPathName)
        {
            found=1;
            break;
        }
    }
    if(found)
    {
        iter->needExit=true;
        iter->alreadyExit=true;
    }
}


stBitRateInfo* CPlayList::GetBitRateInfo(CMyString strPathName)
{
    int found=0;
    vector<stBitRateInfo>::iterator iter;
    for(iter=bitRateChangeSongs.begin(); iter!=bitRateChangeSongs.end();++iter)
    {
        CMyString songNamePath=iter->songNamePath;
        if(songNamePath == strPathName)
        {
            return &(*iter);
        }
    }
    return NULL;
}

void* CPlayList::BitchangeSingleTHD(void* lpParam)
{
    CMyString* songNamePath = static_cast<CMyString*>(lpParam);
    CMyString strSongNamePath=*songNamePath;
    shared_lock<shared_timed_mutex> slk_lowRateSong(*g_Global.m_PlayList.LowRateSongsModQSharedMutex);              //读锁加锁
    stBitRateInfo *brcInfo=g_Global.m_PlayList.GetBitRateInfo(*songNamePath);
    if(!brcInfo)
    {
        delete(songNamePath);
        return NULL;
    }

    printf("ready ffmpeg :%s\n",brcInfo->songNamePath.Data());
    CMyString songPath=g_Global.m_strFolderPath+brcInfo->songNamePath;
    CMyString brcSongPath_temp=g_Global.m_strFolderPath+brcInfo->songNamePath+SONG_LOW_RATE_EXT_TEMP_NAME;
    CMyString brcSongPath=g_Global.m_strFolderPath+brcInfo->songNamePath+SONG_LOW_RATE_EXT_NAME;
    
    #if defined(Q_OS_LINUX)
    QString program = "ffmpeg";
    #else
    CMyString ffmpegPath= g_Global.m_strRunDirPath+"/Tools/ffmpeg.exe";
    QString program = ffmpegPath.Data();
    #endif
    QStringList params;
    if(SONG_LOW_BITRATE == 64)
    {
        params<<"-y"<<"-i"<<songPath.Data()<<"-ab"<<"64k"<<"-acodec"<<"libmp3lame"<<brcSongPath_temp.Data();
    }
    else if(SONG_LOW_BITRATE == 128)
    {
        params<<"-y"<<"-i"<<songPath.Data()<<"-ab"<<"128k"<<"-acodec"<<"libmp3lame"<<brcSongPath_temp.Data();
    }

    //创建ffmpeg转码
    QProcess *TqPro = new QProcess;
    brcInfo->qPro = TqPro;

    brcInfo->status = SONG_LOW_RATE_STATUS_RUNNING;
    g_Global.m_PlayList.availLBrcThreadCnt--;

    bool canStart=!brcInfo->needExit;

    slk_lowRateSong.unlock();           //解锁

    QProcess::ExitStatus exitStatus = QProcess::CrashExit;
    if(canStart)
    {
        TqPro->start(program,params);
        TqPro->waitForFinished(-1);
        exitStatus = TqPro->exitStatus();
    }
    else
    {
        printf("needExit\n");
    }

    unique_lock<shared_timed_mutex> ulk_lowRateSong(*g_Global.m_PlayList.LowRateSongsModQSharedMutex);              //写锁加锁
    printf("TTstrSongNamePath=%s\n",strSongNamePath.Data());
    brcInfo=g_Global.m_PlayList.GetBitRateInfo(strSongNamePath);    //此处一定要重新获取，否则异常，应该和fork有关系
    if(!brcInfo)
    {
        delete(songNamePath);
        delete(TqPro);
        printf("ERROR:ERROR:ERROR!brcInfo!\n");
        return NULL;
    }

    printf("%s,brcInfo->qPro exitstatus=%d\n",brcInfo->songNamePath.Data(),exitStatus);

    if(!exitStatus && !brcInfo->alreadyExit)
    {
        brcInfo->status=SONG_LOW_RATE_STATUS_FINISH;
        printf("finish ffmpeg :%s\n",brcInfo->songNamePath.Data());
        QFile::rename(brcSongPath_temp.Data(),brcSongPath.Data());

        g_Global.m_PlayList.UpdateSongLowRateFile(brcInfo->songNamePath);
    }
    else
    {
        brcInfo->status=SONG_LOW_RATE_STATUS_CRASH;
        printf("crash ffmpeg :%s\n",brcInfo->songNamePath.Data());
        QFile::remove(brcSongPath_temp.Data());
    }

    vector<stBitRateInfo>::iterator iter;
    printf("ready erase %s\n",brcInfo->songNamePath.Data());
    for(iter=g_Global.m_PlayList.bitRateChangeSongs.begin(); iter!=g_Global.m_PlayList.bitRateChangeSongs.end();++iter)
    {
        CMyString songNamePath=iter->songNamePath;
        if(songNamePath == brcInfo->songNamePath)
        {
            printf("already erase %s\n",brcInfo->songNamePath.Data());
            g_Global.m_PlayList.bitRateChangeSongs.erase(iter);
            break;
        }
    }
    
    g_Global.m_PlayList.availLBrcThreadCnt++;
    delete(songNamePath);
    delete(TqPro);
    return NULL;
}

//转换线程
void CPlayList::startBitchangeSingleTHD(CMyString strPathName)
{
    pthread_t thr;
    pthread_attr_t  attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    printf("startBitchangeSingleTHD:%s\n",strPathName.Data());
    CMyString *tempStrPathName= new CMyString(strPathName);
    int ret = pthread_create(&thr, &attr, BitchangeSingleTHD, static_cast<void*>(tempStrPathName));
    if(ret != 0)
    {
        LOG(FORMAT("Create thread error!  %s:%s",  __FILE__, __FUNCTION__), LV_ERROR);
    }
    pthread_attr_destroy(&attr);
}




void* CPlayList::BitRateChangeTHD(void* lpParam)
{
    while(1)
    {
        //每次最大创建4个线程
        while(1)
        {
            shared_lock<shared_timed_mutex> slk_lowRateSong(*g_Global.m_PlayList.LowRateSongsModQSharedMutex);              //读锁加锁
            int songCount=g_Global.m_PlayList.bitRateChangeSongs.size();
            stBitRateInfo *brcInfo=NULL;
            CMyString songNamePath="";
            int status=SONG_LOW_RATE_STATUS_NOT_RUNNING;
            int threadCnt=g_Global.m_PlayList.availLBrcThreadCnt;

            for(int i=0;i<songCount;i++)
            {
                brcInfo=&g_Global.m_PlayList.bitRateChangeSongs[i];
                if( brcInfo->status == SONG_LOW_RATE_STATUS_RUNNING && brcInfo->needExit )  //已经处于转换状态且需要退出（发生在转换过程中删除歌曲）
                {
                    printf("bcInfo needExit...\n");
                    brcInfo->needExit=false;
                    if(brcInfo->qPro && brcInfo->qPro->state() == QProcess::Running)
                    {
                        printf("cancel brcQPro!\n");
                        brcInfo->qPro->close();
                    }
                }
                else
                {
                    if(g_Global.m_PlayList.availLBrcThreadCnt>0 && brcInfo->status == SONG_LOW_RATE_STATUS_NOT_RUNNING)
                    {
                        startBitchangeSingleTHD(brcInfo->songNamePath);
                    }
                }
            }
            slk_lowRateSong.unlock();   //解锁

            usleep(100000);
        }
        
    }
    return NULL;
}

void CPlayList::StartBitRateChangeTHD()
{
    if(!IS_SYSTEM_IN_CLOUD)
        return;
    pthread_t thr;
    pthread_attr_t  attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    int ret = pthread_create(&thr, &attr, BitRateChangeTHD, (void*)this);
    if(ret != 0)
    {
        LOG(FORMAT("Create thread error!  %s:%s",  __FILE__, __FUNCTION__), LV_ERROR);
    }
    pthread_attr_destroy(&attr);
}









bool CPlayList::UpdateSongLowRateFile(CMyString strPathName)
{
    pthread_mutex_lock(&PlaylistUpgradeMutex);
    bool IsUpdate=false;

    #if SUPPORT_SONG_MANAGER
    CSong tsong;
    if(g_Global.m_SongManager.GetSongByPathName(strPathName,tsong))
    {
        if(tsong.GetBitRate() > SONG_LOW_BITRATE)
        {
            tsong.SetLowRateFile(strPathName);
        }
        g_Global.m_SongManager.UpdateSong(strPathName,tsong);
    }
    #endif

    int nListCount = GetListCount();
    for(int i=0; i<nListCount; i++)
    {
        int list_update=false;
        int songId=FindSongIdByList(strPathName,i);
        if(songId!=-1)
        {
            IsUpdate=true;
            CSong& song = GetListSong(i,songId);
            //判断是否属于低码率歌曲，如果不是，需要进行转换
            if(song.GetBitRate() > SONG_LOW_BITRATE)
            {
                song.SetLowRateFile(strPathName);
            }
             //更新歌曲列表日期
            g_Global.m_PlayList.SetListDateTime(i, GetCurrentDateTime());
        }
    }
    pthread_mutex_unlock(&PlaylistUpgradeMutex);
    if(IsUpdate)
    {
        //等到全部歌曲更新完，再统一更新XML文件
        if(g_Global.m_PlayList.bitRateChangeSongs.size() <= 1)
            g_Global.m_PlayList.PushWriteXmlTask("");
    }
    return IsUpdate;
}



void CPlayList::PushWriteXmlTask(CMyString strUserAccount)
{
    printf("PushWriteXmlTask:Account=%s\n",strUserAccount.Data());
    pthread_mutex_lock(&PlaylistXmlUpgradeMutex);
    //未找到超级用户，那么才加入
    if(std::find(m_vecPlaylistAccountChange.begin(),m_vecPlaylistAccountChange.end(),SUPER_USER_NAME) == m_vecPlaylistAccountChange.end())
    {
        m_vecPlaylistAccountChange.push_back(SUPER_USER_NAME);
    }
    #if APP_IS_LZY_LIMIT_STORAGE
    if(std::find(m_vecPlaylistAccountChange.begin(),m_vecPlaylistAccountChange.end(),strUserAccount) == m_vecPlaylistAccountChange.end())
    {
        m_vecPlaylistAccountChange.push_back(strUserAccount);
    }
    #endif
    pthread_mutex_unlock(&PlaylistXmlUpgradeMutex);
}


void* CPlayList::PlaylistXmlUpgradeThread(void* lpParam)
{
    while(1)
    {
        pthread_mutex_lock(&g_Global.m_PlayList.PlaylistXmlUpgradeMutex);
        int changeCnt=g_Global.m_PlayList.m_vecPlaylistAccountChange.size();
        if(changeCnt)
        {
            for(int i=0;i<changeCnt;i++)
            {
                CMyString strAccount = g_Global.m_PlayList.m_vecPlaylistAccountChange[i];
                if(strAccount==SUPER_USER_NAME)
                {
                    g_Global.m_PlayList.WriteFile();
                }
                else
                {
                    g_Global.m_PlayList.WriteFileBySpecAccount(strAccount);
                    
                    //存储信息变化后，通知WEB管理员及对应目标用户
                    g_Global.m_WebNetwork.ForwardAccountStorageCapacity(SUPER_USER_NAME,strAccount.Data());
                    g_Global.m_WebNetwork.ForwardAccountStorageCapacity(strAccount.Data(),strAccount.Data());
                }
            }
            //遍历完毕后清空vector
            g_Global.m_PlayList.m_vecPlaylistAccountChange.clear();
            //播放列表变化后通知WEB
            //todo 只通知当前账户以及它的父账户
            g_Global.m_WebNetwork.ForwardUpdateFileInfo(FILE_PLAYLIST);
        }
        pthread_mutex_unlock(&g_Global.m_PlayList.PlaylistXmlUpgradeMutex);

        usleep(500000);
    }
    return NULL;
}


void CPlayList::StartPlaylistXmlUpgradeThread()
{
    pthread_t thr;
    pthread_attr_t  attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    int ret = pthread_create(&thr, &attr, PlaylistXmlUpgradeThread, (void*)this);
    if(ret != 0)
    {
        LOG(FORMAT("Create thread error!  %s:%s",  __FILE__, __FUNCTION__), LV_ERROR);
    }
    pthread_attr_destroy(&attr);
}




#if SUPPORT_SONG_MANAGER
/***********************************************/


bool CSongManager::AddSong(CSong& song)
{
    if(GetSongsCount() >= MAX_TOTAL_SONGS_COUNT)
    {
        return FALSE;
    }
    //printf("AddSong:strPathName=%s\n",song.GetPathName().Data());
    bool find = FALSE;
    bool result = FALSE;
    vector<CSong>::iterator iter;
    for(iter=m_Songs.begin(); iter!=m_Songs.end(); ++iter)
    {
        if(song.GetPathName() == iter->GetPathName())
        {
            find = TRUE;
            break;
        }
    }

    if(!find)
    {
        result=TRUE;
        //默认放开权限
        //******** 如果是TTS歌曲，或者管理员，或者普通用户具有所有歌曲管理权限，才默认已审核
        #if ENABLE_SONG_AUDIT
        LPCUserInfo  pSongUser = g_Global.m_Users.GetUserByAccount(song.GetUserAccount().Data());
        if(pSongUser)
        {
            if(song.GetPathName().Find("/Data/Program/Common/Music/") != std::string::npos || ( song.GetUserAccount() == SUPER_USER_NAME || (pSongUser->GetLimitCode() & USER_LIMITS_PLAYLIST)) )
            {
                song.SetAudit(true);
            }
        }
        #else
        song.SetAudit(true);
        #endif
        song.SetAlive(true);
        m_Songs.push_back(song);
        WriteFile();
    }
    else
    {
        result=UpdateSong(song.GetPathName(),song);
    }

    #if APP_IS_LZY_LIMIT_STORAGE
    //如果是龙之音V1版本，如果变化了，需要告知寻呼台最新的存储信息
    if(g_Global.m_Network.m_bNetworkIsReady && result)
    {
        string strDestAccount=song.GetUserAccount().Data();
        //改变存储大小后，主动告知该登录账户的寻呼台设备
        //计算该账户的总存储空间
        LPCUserInfo  pDestUser = g_Global.m_Users.GetUserByAccount(strDestAccount);
        if(pDestUser && !pDestUser->IsSuperUser())  //管理员用户不需要通知变化
        {
            UINT64 storage_capacity=0;
            UINT64 storage_used=0;
            UINT64 storage_remaining=0;
            int compress_bitrate=0;
            storage_capacity = ((UINT64)pDestUser->GetStorageCapacity())*1024*1024;
            //计算该账户的已用存储空间（字节）
            storage_used = pDestUser->GetStorageUsedSpace();
            //计算该账户的剩余存储空间（字节）
            storage_remaining = pDestUser->GetStorageRemainingSpace();
            //压缩比特率
            compress_bitrate = SONG_LOW_BITRATE;
            for(int i=0;i<g_Global.m_Pagers.GetSecCount();i++)
            {
                CSection &pagerDevice=g_Global.m_Pagers.GetSection(i);
                if(pagerDevice.IsOnline() && pagerDevice.GetUserAccount() == strDestAccount)
                {
                    g_Global.m_Network.m_CmdSend.CmdAccountStorageCapacity(pagerDevice,strDestAccount.data(),storage_capacity,storage_used,storage_remaining,compress_bitrate);
                }
            }

            //存储信息变化后，通知WEB管理员及对应目标用户
            g_Global.m_WebNetwork.ForwardAccountStorageCapacity(SUPER_USER_NAME,strDestAccount);
            g_Global.m_WebNetwork.ForwardAccountStorageCapacity(strDestAccount,strDestAccount);
        }
        
    }
    #endif

    return result;
}


bool CSongManager::DeleteSong(CMyString strPathName,bool removeFile)
{
    //printf("DeleteSong:strPathName=%s\n",strPathName.Data());
    bool result = FALSE;
    vector<CSong>::iterator iter;
    for(iter=m_Songs.begin(); iter!=m_Songs.end(); ++iter)
    {
        if(strPathName == iter->GetPathName())
        {
            CMyString songAccount=iter->GetUserAccount();
            m_Songs.erase(iter);

            //删除相关歌曲文件
            if(removeFile)
            {
                #if SUPPORT_SONG_MANAGER
                g_Global.m_PlayList.DelLowRateSong(strPathName);
                #endif

                CMyString strSongPath = g_Global.m_strFolderPath + strPathName;
                RemoveFile(strSongPath.Data());
                CMyString brcSongPath=strSongPath+SONG_LOW_RATE_EXT_NAME;
                RemoveFile(brcSongPath.Data());

                for(int i=0;i<g_Global.m_PlayList.GetListCount();i++)
                {
                    g_Global.m_PlayList.RemoveListSong(i,strPathName);
                }
                g_Global.m_PlayList.PushWriteXmlTask(songAccount);
            }

            result = TRUE;
            break;
        }
    }
    if(result)
    {
        WriteFile();
        //本地歌曲发生变化，发送给所有web
        g_Global.m_WebNetwork.ForwardLocalSongInfoToWeb(NULL);
    }
    return result;
}


bool CSongManager::DeleteAllSongOfAccount(CMyString strUserAccount)
{
    bool result = FALSE;
    vector<CSong>::iterator iter;
    for(iter=m_Songs.begin(); iter!=m_Songs.end();)
    {
        if(iter->GetUserAccount() == strUserAccount)
        {
            CMyString strPathName=iter->GetPathName();
            iter=m_Songs.erase(iter);

            //删除相关歌曲文件
            #if SUPPORT_SONG_MANAGER
            g_Global.m_PlayList.DelLowRateSong(strPathName);
            #endif

            CMyString strSongPath = g_Global.m_strFolderPath + strPathName;
            RemoveFile(strSongPath.Data());
            CMyString brcSongPath=strSongPath+SONG_LOW_RATE_EXT_NAME;
            RemoveFile(brcSongPath.Data());

            for(int i=0;i<g_Global.m_PlayList.GetListCount();i++)
            {
                g_Global.m_PlayList.RemoveListSong(i,strPathName);
            }
            g_Global.m_PlayList.PushWriteXmlTask(strUserAccount);
            

            result = TRUE;
        }
        else
        {
            ++iter;
        }
    }
    if(result)
    {
        WriteFile();
        //本地歌曲发生变化，发送给所有web
        g_Global.m_WebNetwork.ForwardLocalSongInfoToWeb(NULL);
    }
    return result;
}


bool CSongManager::GetSongByPathName(CMyString strPathName,CSong& song)
{
    vector<CSong>::iterator iter;
    for(iter=m_Songs.begin(); iter!=m_Songs.end(); ++iter)
    {
        if(strPathName == iter->GetPathName())
        {
            song = *iter;
            return true;
        }
    }
    return false;
}

CSong* CSongManager::GetSongByPathName(CMyString strPathName)
{
    vector<CSong>::iterator iter;
    for(iter=m_Songs.begin(); iter!=m_Songs.end(); ++iter)
    {
        if(strPathName == iter->GetPathName())
        {
            return &(*iter);
        }
    }
    return NULL;
}


bool CSongManager::UpdateSong(CMyString strPathName,CSong& song)
{
    bool update_flag = FALSE;
    vector<CSong>::iterator iter;
    for(iter=m_Songs.begin(); iter!=m_Songs.end(); ++iter)
    {
        if(song.GetPathName() == iter->GetPathName())
        {
            CSong& song_ori = *iter;

            if(song_ori.GetDuration()!=song.GetDuration())
            {
                update_flag = TRUE;
                song_ori.SetDuration(song.GetDuration());
            }
            if(song_ori.GetSize()!=song.GetSize())
            {
                update_flag = TRUE;
                song_ori.SetSize(song.GetSize());
            }
            if(song_ori.GetBitRate()!=song.GetBitRate())
            {
                update_flag = TRUE;
                song_ori.SetBitRate(song.GetBitRate());
            }
            if(song_ori.GetMd5()!=song.GetMd5())
            {
                update_flag = TRUE;
                song_ori.SetMd5(song.GetMd5());
            }
            if(song_ori.GetLowRateFile()!=song.GetLowRateFile())
            {
                update_flag = TRUE;
                song_ori.SetLowRateFile(song.GetPathName());
            }
            if(song_ori.GetUserAccount()!=song.GetUserAccount())
            {
                update_flag = TRUE;
                song_ori.SetUserAccount(song.GetUserAccount());
            }
            
            if(update_flag)
            {
                if(song_ori.GetLowRateFile()!="")
                {
                    song_ori.SetLbrcMd5(GetFileMd5(song_ori.GetLowRateFile().C_Str()));
                    song_ori.SetLowRateSize(GetFileSize(song_ori.GetLowRateFile().C_Str()));
                }

                //默认放开权限
                song_ori.SetAlive(true);
                song_ori.SetAudit(true);

                WriteFile();
            }
            break;
        }
    }

    return update_flag;
}


void CSongManager::ScanLocalSong()
{
    vector<CSong>::iterator iter;
    bool needUpdate=false;
    for(iter=m_Songs.begin(); iter!=m_Songs.end();)
    {
        CMyString strSongPath = g_Global.m_strFolderPath + iter->GetPathName();
        if(  !IsExistFile(strSongPath.Data()) )
        {
            iter=m_Songs.erase(iter);
            needUpdate=true;
            continue;
        }
        else
        {
            //判断是不是存在lbrc文件，如果是，也获取它的MD5值
            if(iter->GetLowRateFile() != "")
            {
                //当lbrc歌曲文件的md5不存在时才去读取并设置
                if(iter->GetLbrcMd5() == "")
                {
                    //获取
                    iter->SetLbrcMd5(GetFileMd5(iter->GetLowRateFile().C_Str()));
                    needUpdate=true;
                    printf("ScanLocalSong1:Name=%s,md5=%s\n",iter->GetLowRateFile().C_Str(),iter->GetLbrcMd5().data());
                }
                //20230609 获取lbrc歌曲文件大小,这个就不保存到songlist.xml文件内了，读文件大小不耗时，而md5读取比较耗时，所以保存下来。
                ulong lbrcfileSize=GetFileSize(iter->GetLowRateFile().C_Str());
                if(lbrcfileSize)
                {
                    iter->SetLowRateSize(lbrcfileSize);
                }
            }
            ++iter;
        }
    }
    if(needUpdate)
    {
        WriteFile();
    }
}


bool CSongManager::ReadFile()
{
    TiXmlDocument* xmlSongList = new TiXmlDocument;
    CHAR	szPathName[STR_MAX_PATH]	= {0};

    CombinHttpURL(szPathName, HTTP_FOLDER_XML, HTTP_FILE_SONGLIST);

    int	nMaxSongsCount	= MAX_TOTAL_SONGS_COUNT;

    bool update_file=false;
    if(xmlSongList->LoadFile(szPathName))
    {
        m_Songs.clear();

        TiXmlElement* songList = xmlSongList->FirstChildElement();
        SetDateTime(CMyString(songList->Attribute("DateTime")));

        // Song
        for(TiXmlElement* Song=songList->FirstChildElement(); Song!=NULL;
            Song=Song->NextSiblingElement())
        {
            if(GetSongsCount() >= nMaxSongsCount)
            {
                break;
            }

            CSong csong;
            csong.SetDuration(atoi(Song->Attribute("Duration")));
            csong.SetSize(atoi(Song->Attribute("Size")));
            csong.SetPathName(Song->Attribute("PathName"));
            csong.SetBitRate(atoi(Song->Attribute("BitRate")));
            csong.SetMd5(Song->Attribute("md5"));
    
            const char *lbrc=Song->Attribute("lbrc");
            if( strcmp(lbrc,"TRUE") == 0 )
            {
                csong.SetLowRateFile(csong.GetPathName());
                
                if(Song->Attribute("lbrcMd5")!=NULL)
                {
                    csong.SetLbrcMd5(Song->Attribute("lbrcMd5"));
                }
            }
            else
            {
                csong.SetLowRateFile("");
            }
            
            csong.SetUserAccount(Song->Attribute("UserName"));

            const char *audit=Song->Attribute("Audit");
            if( strcmp(audit,"TRUE") == 0 )
            {
                csong.SetAudit(true);
            }
            else
            {
                csong.SetAudit(false);
            }


            //如果是龙之音V1版本，预先设置lbrc文件大小，避免切换非V1版本后程序启动一段时间内大小不对的问题
            #if APP_IS_LZY_LIMIT_STORAGE
            if( csong.GetBitRate() > SONG_LOW_BITRATE )
            {
                double lbrcDiv=csong.GetBitRate()/SONG_LOW_BITRATE;
                double lbrcSize=csong.GetSize()/lbrcDiv;
                csong.SetLowRateSize(lbrcSize);
                //printf("TLsong:%s,lowSize=%f\n",csong.GetName().Data(),lbrcSize);
            }
            #endif
            
            AddSong(csong);
        }
    }
    else
    {
        delete xmlSongList;
        return FALSE;
    }

    xmlSongList->Clear();
    delete xmlSongList;

    if(update_file)
    {
        WriteFile();
    }
    return TRUE;
}



bool CSongManager::WriteFile(bool bUpdateDateTime)
{
    TiXmlDocument xmlSongList;
    int		nSongCount	= GetSongsCount();

    CHAR	szPathName[STR_MAX_PATH] = {0};
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, HTTP_FILE_SONGLIST);

    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0","utf-8","no");
    xmlSongList.LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment("Save for Songlist");
    xmlSongList.LinkEndChild(com);

    TiXmlElement* SongList = new TiXmlElement("SongList");
    SongList->SetAttribute("SongCount", nSongCount);
    SongList->SetAttribute("DateTime",  m_strDateTime.Data());
    xmlSongList.LinkEndChild(SongList);

    for(int i=0; i<nSongCount; i++)
    {
        TiXmlElement* Song = new TiXmlElement("Song");

        Song->SetAttribute("Duration", m_Songs[i].GetDuration());
        Song->SetAttribute("Size", m_Songs[i].GetSize());
        Song->SetAttribute("PathName",m_Songs[i].GetPathName().Data());
        Song->SetAttribute("BitRate",m_Songs[i].GetBitRate());
        Song->SetAttribute("md5",m_Songs[i].GetMd5().data());
        Song->SetAttribute("lbrcMd5",m_Songs[i].GetLbrcMd5().data());
        Song->SetAttribute("lbrc",(m_Songs[i].GetLowRateFile() == "")?("FALSE"):("TRUE") );
        Song->SetAttribute("UserName", m_Songs[i].GetUserAccount().Data());
        Song->SetAttribute("Audit",m_Songs[i].GetAudit()?("TRUE"):("FALSE") );

        SongList->LinkEndChild(Song);
    }

    bool saveFileOK=true;
    if(xmlSongList.SaveFile(szPathName))
    {
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlSongList.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    return saveFileOK;
}



int CSongManager::GetSongsCount(void)
{
    return m_Songs.size();
}

CSong* CSongManager::GetSongByMd5(string md5)
{
    vector<CSong>::iterator iter;
    for(iter=m_Songs.begin(); iter!=m_Songs.end(); ++iter)
    {
        if( iter->GetMd5() == md5 )
        {
            return &(*iter);
        }
    }
    return NULL;
}

string CSongManager::GetMd5ByPathName(string pathName)
{
    vector<CSong>::iterator iter;
    string t_pathName=pathName;
    CMyString brcSongPath=pathName;
    const char zeroChar[2]={0};
    brcSongPath.Replace(SONG_LOW_RATE_EXT_NAME,zeroChar);
    t_pathName = brcSongPath.Data();

    for(iter=m_Songs.begin(); iter!=m_Songs.end(); ++iter)
    {
        if(iter->GetPathName() == t_pathName.data())
        {
            if(pathName.find(SONG_LOW_RATE_EXT_NAME) != string::npos)
            {
                return iter->GetLbrcMd5();
            }
            else
            {
                return iter->GetMd5();
            }
        }
    }
    return "";
}



void CSongManager::Reset()
{
    m_Songs.clear();
    WriteFile();
}


void CSongManager::Init()
{
    //检查目录文件下是否存在以前的低码率歌曲，如有，则删除掉
    //如果当前非云广播版本，那么暂不删除云广播自动压缩的文件（后续可以考虑删除，避免切换版本号占用空间）
    string delSongLowRateExt,delSongLowRateTempExt;
    if(SONG_LOW_BITRATE == 64)
    {
        delSongLowRateExt = SONG_LOW_RATE_128_EXT_NAME;
        delSongLowRateTempExt = SONG_LOW_RATE_128_EXT_TEMP_NAME;
    }
    else if(SONG_LOW_BITRATE == 128)
    {
        delSongLowRateExt = SONG_LOW_RATE_64_EXT_NAME;
        delSongLowRateTempExt = SONG_LOW_RATE_64_EXT_TEMP_NAME;
    }
    if(delSongLowRateExt.length()>0)
    {
        char szPath[1024] = {0};
        sprintf(szPath, "%s/%s/%s", g_Global.m_strFolderPath.Data(), HTTP_FOLDER_ADATA, HTTP_FOLDER_PROGRAM);
        RemoveDirectoryAllFilesByExt(szPath,delSongLowRateExt.data(),true);
        RemoveDirectoryAllFilesByExt(szPath,delSongLowRateTempExt.data(),true);
    }
}

#if APP_IS_LZY_LIMIT_STORAGE
UINT64 CSongManager::GetSongsUsedSpaceByUser(CMyString strUserAccount)
{
    UINT64 usedSpace=0;
    vector<CSong>::iterator iter;
    for(iter=m_Songs.begin(); iter!=m_Songs.end(); ++iter)
    {
        if( iter->GetUserAccount() == strUserAccount )
        {
            usedSpace+=iter->GetLowRateSize()?iter->GetLowRateSize():iter->GetSize();
        }
    }
    printf("GetSongsUsedSpaceByUser:strUserAccount=%s,usedSpace=%lld\n",strUserAccount.Data(),usedSpace);
    return usedSpace;
}
#endif

/*********************************************/

#endif