#include "stdafx.h"
#include "SelectedSections.h"

CSelectedSections::CSelectedSections(void)
{
    m_nPackCount = 0;
    m_nPackID	 = 0;
    m_nID		 = 0;
}


CSelectedSections::~CSelectedSections(void)
{

}


bool CSelectedSections::CanHandleCmd(unsigned char nID)
{
    return (nID > 0 && m_nID == nID && HasRecvAll());
}

CMyString	CSelectedSections::GetAt(int index)
{
    if (index >= GetCount())
    {
        return ("");
    }

    return m_vecMacs[index];
}


bool CSelectedSections::Add(CMyString strMac)
{
    if (GetCount() >= MAX_CONTROL_SECTION_COUNT)
    {
        return FALSE;
    }

    m_vecMacs.push_back(strMac);
    return TRUE;
}





