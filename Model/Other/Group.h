#ifndef GROUP_H
#define G<PERSON>UP_H

#include <iostream>
#include <vector>
#include <map>
#include "Global/Const.h"
#include "Tools/CMyString.h"
#include "PlayList.h"

using std::map;

/************************************************************************/
/* 分组信息类                                                           */
/************************************************************************/

class CGroup
{
public:
    CGroup(void)  {}
    CGroup(CMyString strID, const char* lpszName);
    virtual ~CGroup(void);

public:
    void			SetID(CMyString strID)		{	m_strID = strID;					    }
    CMyString		GetID(void)					{	return m_strID;						}
    void			SetName(const char* lpszName)	{	strcpy(m_szGroupName, lpszName);	}
    const char*		GetName(void)				{	return m_szGroupName;				}
    CMyString       GetUserAccount()            {  return m_strUserAccount;                 }
    void            SetUserAccount(CMyString strUserAccount);
    int				GetSecCount(void)			{	return m_vecSecMac.size();			}
    CMyString		GetSecMac(int nSec)			{	return m_vecSecMac[nSec];			}
    int				GetBmpID(void)				{	return m_nBmpID;					}
    void			SetBmpID(int nID)			{	m_nBmpID = nID;						}
    const char*		GetBmpPath(void)			{	return m_szBmpPath;					}
    void			SetBmpPath(const char* szPath)	{	strcpy(m_szBmpPath, szPath);		}
    void			SetCheck(bool isChecked)	{	m_isChecked = isChecked;			}
    bool			GetCheck(void)				{	return m_isChecked;					}
    void			ClearSections(void);
    void			AddSection(CMyString strMac);
    bool			RemoveSection(CMyString strMac);
    bool			IsSameSecMacs(vector<CMyString>& secMacs);
    void			SetSecMacs(vector<CMyString>& secMacs);
    void         SetSecMacs(vector<string>& secMacs);
    bool			RemoveInvalidSections(void);
    int				GetSectionIndex(CMyString strMac);
    unsigned int	GetCheckedSections(unsigned int* pCheckedIndexs);

    void			InitPlaylistGroupFileName(void);
    CPlayList*		GetPlaylist(void);
    void			ClearPlaylistGroup(void);

    bool            GroupSectionSort(void);

    //bool        AddPage(int nPage);
    //int           GetPageCount();
    // 设置临时保存的分区Mac地址
    //void        SecTempSecMac(vector<string> &vecSecMac, int nPage, int nPageCount);

private:
    CMyString		m_strID;						// 分组ID
    char			m_szGroupName[SEC_NAME_LEN+1];	// 分组名称
    int				m_nBmpID;						// 分组图片ID,如果是自定义图片，为0, 初始化为-1
    char			m_szBmpPath[STR_MAX_PATH];			// 分组图片路径
    bool			m_isChecked;
    vector<CMyString>	m_vecSecMac;
    CMyString       m_strUserAccount;       //分组归属账户

    //vector<int>                 m_vecPage;                 // 页数容器，保存每次收到数据的页数，用于判断数据是否获取完全
    //vector<CMyString>    m_vecTempSecMac;   // 保存临时分区Mac地址

public:
    CPlayList		m_PlaylistGroup;				// 分组的播放列表
};


/************************************************************************/
/* 分组管理类                                                           */
/************************************************************************/

class CGroups
{
public:
    CGroups(void);
    virtual ~CGroups(void);

public:
    CGroup&			GetGroup(int nIndex)					{	return m_Groups[nIndex];					}
    CMyString		GetGroupID(int nIndex)					{	return m_Groups[nIndex].GetID();		}
    bool			GetGroupChecked(int nIndex)				{	return m_Groups[nIndex].GetCheck();			}
    void			SetGroupChecked(int nIndex, bool bChk)	{	m_Groups[nIndex].SetCheck(bChk);			}
    CMyString		GetDateTime(void)						{	return m_strDateTime;					}
    void			SetDateTime(CMyString strDateTime)		{	m_strDateTime = strDateTime;				}
    int				GetGroupCount(void)						{	return m_Groups.size();						}
    const char*		GetGroupName(int nGroup)				{	return m_Groups[nGroup].GetName();			}
    void			SetGroupName(int nGroup, const char* szName){	m_Groups[nGroup].SetName(szName);		}
    int				GetGroupSecCount(int nGroup)			{	return m_Groups[nGroup].GetSecCount();		}
    int				GetGroupBmpID(int nIndex)				{	return m_Groups[nIndex].GetBmpID();			}
    void			SetGroupBmpID(int nIndex, int	nBmpID)	{	m_Groups[nIndex].SetBmpID(nBmpID);			}
    const char*		GetGroupBmpPath(int nIndex)				{	return m_Groups[nIndex].GetBmpPath();	}
    void			SetGroupBmpPath(int nInx, const char* szPath){	m_Groups[nInx].SetBmpPath(szPath);		}
    bool        GetNeedNotify()                                       { return m_bNeedNotify;      }
    void        SetNeedNotify(bool isNotify)                  { m_bNeedNotify = isNotify; }

    CMyString		GetGroupSecMac(int nGroup, int nSec);
    bool			AddGroup(CGroup& group);
    void			RemoveGroup(int nGroup);
    void            RemoveGroup(CMyString strUserAccount);
    bool			RemoveSectionFromAllGroups(const char* szMac);
    void			AddGroupSection(int nGroup, CMyString strMac);
    void			AddGroupSection(int nGroup, const char* szMac);
    void			RemoveGroupSection(int nGroup, CMyString strMac);
    void			RemoveGroupSection(int nGroup, const char* szMac);

    void            SetGroupUserAccount(int nGroup, CMyString strUserAccount);
    CMyString       GetGroupUserAccount(int nGroup);

    void            UpdateGroupSection(CMyString strUserAccount);

    void			ClearGroups(void);
    bool			ReadGroupFile(string strFileName);
    bool			WriteGroupFile(string strFileName, bool bUpdateDateTime = TRUE);

    unsigned int	GetCheckedGroups(unsigned int* pCheckedIndexs);
    unsigned int	GetCheckedGroupsCount();

    unsigned int	GetCheckedSections(unsigned int* pCheckedIndexs);

    bool			UpdateGroupName(CMyString strOldDefaultGroupName);

    bool			FindGroupByName(const char* szName);
    bool			FindGroupByName(const char* szName, CGroup& group);
    CGroup*	FindGroupByID(CMyString strID, int& nIndex);
    void			ClearAllPlaylistGroup(void);

    //所有分组内分区排序(升序)
    void            GroupsSectionSort(void);
     //根据分区ID排序(升序)
    static  bool    SortBySectionId(const CMyString &secMac1, const CMyString &secMac2);

    void            GetAllGroupsBySpecUser(CMyString strUserAccount,vector<CGroup> &vecGroup);

private:
    bool                        m_bNeedNotify;     // 文件是否需要通知Web终端

    vector<CGroup>	m_Groups;       // 分组信息
    CMyString		m_strDateTime;

    //map<string, CGroup>     m_UIDGroups;        // 命令UUID与临时保存的组信息类映射
};




#endif // GROUP_H
