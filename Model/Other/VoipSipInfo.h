#ifndef VOIPSIPINFO_H
#define VOIPSIPINFO_H

#include <iostream>
#include <map>
#include <vector>
#include "Model/Device/Section.h"

using namespace std;


enum
{
    VOIP_SIP_IDLE = 0,        // 空闲
    VOIP_SIP_BUSY = 1,        // 繁忙
    VOIP_SIP_RING = 2,        // 响铃
    VOIP_SIP_DAHDI = 3,       // DAHDI
    VOIP_SIP_OFFLINE = 4,     // 离线
    VOIP_SIP_ERROR = 5        // 未知错误
};


class SipInfo
{
public:
        SipInfo();
        SipInfo(string exten, string status);

        string   m_strExten;            // Sip账号(string)
        int      m_nExten;              // Sip账号(int)
        string   m_strStatus;           // Sip状态(string)
        int      m_nStatus;             // Sip状态(int)
};

typedef SipInfo* LPSipInfo;


//#define  VOIP_SERVER_ADDR     ("***************")
//#define  VOIP_SERVER_PORT      5065
//#define  VOIP_PASSWORD        ("1234")

class CVoipSipInfo
{
public:
        CVoipSipInfo();

        int   GetSipInfoCount()          { return m_SipInfos.size();     }

        //  添加Sip账号与状态，如果是第一个包，则清空所有Sip信息
        void  AddSipInfo(int nCount,                  // sip数量
                         SipInfo* sipInfos,           // sip数组
                         bool isCrear = false);       // 是否清空

        SipInfo   &GetSipInfo(int index);
        LPSipInfo GetSipInfo(string  strExten);

        int   GetSipStatus(vector<string>  &vecExten);
        int   GetSipStatus(string strExten);

private:
        bool  IsExistSip(string strExten);

private:
        map<string, SipInfo> m_SipInfos;                // Sip账号与信息映射映射

};

#endif // VOIPSIPINFO_H
