#include "SequencePower.h"
#include <strings.h>
#include "Global/CType.h"

CSequencePowerChannel::CSequencePowerChannel(int nID)
{
    m_nID				= nID;
    memset(m_szName,0,sizeof(m_szName));
    m_nSwitchState      = SEQUENCE_POWER_CHANNEL_STATE_OFF;
    m_bExistTimer       = false;
    m_bInTiming         = false;
}


CSequencePowerChannel::~CSequencePowerChannel(void)
{
   
}

void CSequencePowerChannel::SetName(const char *szName)
{
    memset(m_szName,0,sizeof(m_szName));
    if (strlen(szName) > SEC_NAME_LEN)
    {
        strncpy(m_szName, szName, SEC_NAME_LEN);
    }
    else
    {
        strcpy(m_szName, szName);
    }
}

/******************************************************/

CSequencePower::CSequencePower()
{
    m_bInitOK = false;
    m_nSwitchSate = 0;
    m_nChannelCnt = SEQUENCE_POWER_MAX_CHANNEL_COUNT;
    m_Channels.clear();
    m_nControlMode = SEQUENCE_POWER_MODE_MANUAL;    //默认手动模式
    
    for(int i=0;i<m_nChannelCnt;i++)
    {
        CSequencePowerChannel seqPwrChannel(i + 1);
        CHAR name[SEC_NAME_LEN] = {0};
        sprintf(name, "Channel %d", i + 1);
        seqPwrChannel.SetName(name);

        AddChannel(seqPwrChannel);
    }
}


CSequencePower::~CSequencePower(void)
{
    
}

bool CSequencePower::operator!=( const CSequencePower &deviceSequencePower)
{
    //printf("new_controlMode=%d,newChannelCnt=%d,newSwitchSate=%d\n",deviceSequencePower.m_nControlMode,deviceSequencePower.m_nChannelCnt,deviceSequencePower.m_nSwitchSate);
    return ( deviceSequencePower.m_nControlMode != m_nControlMode || deviceSequencePower.m_nChannelCnt != m_nChannelCnt || deviceSequencePower.m_nSwitchSate != m_nSwitchSate );
}

void	CSequencePower::SetControlMode(int nControlMode)
{
    m_nControlMode = nControlMode;
}


void    CSequencePower::SetAllSwitchState(unsigned short state)
{
    if(state!=m_nSwitchSate)
    {
        int nChannelCount = GetRealChannelCnt();
        for (int i=0; i<nChannelCount; ++i)
        {
            m_Channels[i].SetSwitchState((state>>i)&0x01);
        }
        m_nSwitchSate = state;
    }
}


int	CSequencePower::GetChannelCount(void)
{
    return m_Channels.size();
}

bool CSequencePower::AddChannel(CSequencePowerChannel& channel)
{
    if (GetChannelCount() < GetRealChannelCnt())
    {
        m_Channels.push_back(channel);

        return TRUE;
    }

    return FALSE;
}

