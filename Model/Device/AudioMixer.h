#ifndef AUDIOMIXER_H
#define AUDIOMIXER_H

#include <iostream>
#include <vector>
#include "Global/Const.h"
#include "Tools/CMyString.h"
#include "Tools/G722/g722_encoder.h"

#if SUPPORT_AUDIO_MIXER

#define AUDIO_MIXER_BROADCAST_ADDR  "230.230.231.14"
#define AUDIO_MIXER_BROADCAST_PORT  50401

#define AUDIO_MIXER_FMT 16

enum
{
    AUDIO_MIXER_RATE_16K		= 16000,
    AUDIO_MIXER_RATE_2205K	    = 22050,
    AUDIO_MIXER_RATE_32K		= 32000,
    AUDIO_MIXER_RATE_441K		= 44100,
    AUDIO_MIXER_RATE_48K		= 48000,

    AUDIO_MIXER_RATE_COUNT	    = 5,

};

enum
{
    AUDIO_MIXER_MONO_CHANNEL		= 0x01,	// 单声道
    AUDIO_MIXER_STERO_CHANNEL		= 0x02,	// 双声道
};

enum
{
    AUDIO_MIXER_EVENT_STOP	    = 0,
    AUDIO_MIXER_EVENT_START		= 1,
    AUDIO_MIXER_EVENT_RETRY	    = 2,
};

class CAudioMixer
{
public:
    CAudioMixer(void);
    ~CAudioMixer(void);

public:
    unsigned int	GetSampleRate(void)			{	return m_uSampleRate;	}
    void	SetSampleRate(unsigned int sampleRate)			{	m_uSampleRate=sampleRate;}
    unsigned char	GetAlgorithm(void)			{	return m_uAlgorithm;	}
    void	SetAlgorithm(unsigned char algorithm)			{	m_uAlgorithm = algorithm;	}
    
    unsigned char GetMasterSwitch(void)         {   return m_nMasterSwitch;}
    void SetMasterSwitch(unsigned char masterSwitch)   { m_nMasterSwitch = masterSwitch;}
    
    unsigned char GetPriority(void)              {   return m_nPriority;}
    void SetPriority(unsigned char priority)     { m_nPriority = priority;}
    unsigned char GetVolume(void)         {   return m_nVolume;}
    void SetVolume(unsigned char volume)   { m_nVolume = volume;}
    unsigned char GetTriggerSensitivity(void)              {   return m_nTriggerSensitivity;}
    void SetTriggerSensitivity(unsigned char triggerSensitivity)     { m_nTriggerSensitivity = triggerSensitivity;}
    unsigned char GetTriggerType(void)              {   return m_nTriggerType;}
    void SetTriggerType(unsigned char triggerType)     { m_nTriggerType = triggerType;}
    unsigned char GetVolumeFadeLevel(void)              {   return m_nVolumeFadeLevel;}
    void SetVolumeFadeLevel(unsigned char volumeFadeVolume)     { m_nVolumeFadeLevel = volumeFadeVolume;}
    unsigned char GetDelayMode(void)              {   return m_nDelayMode;}
    void SetDelayMode(unsigned char delayMode)     { m_nDelayMode = delayMode;}


    CMyString		GetSectionMac(int nSec)				{	return m_vecSecMac[nSec];					}
    int				GetSectionCount(void)					{	return m_vecSecMac.size();				}
    void            AddSection(CMyString strMac)        {   m_vecSecMac.push_back(strMac);              }
    bool            ClearSection(bool bAll,CMyString strMac);
    void            SetSection(vector<CMyString>& vecMac)    {   m_vecSecMac=vecMac;                    }
    int             GetSelectedSections(vector<CMyString>& vecMac);
    bool            HasSectionMac(CMyString strMac)     {   return std::count(m_vecSecMac.begin(), m_vecSecMac.end(), strMac)>0; }


    bool		IsAudioMixerSingalValid()                {	return b_AudioMixerSingalValid;	}
    void		SetAudioMixerSingalValid(bool valid)     {	b_AudioMixerSingalValid = valid;	}

    bool   operator==(const CAudioMixer &audioMixer);

    G722_ENC_CTX *Get_G722_enc_ctx(){
        if(g722_enc_ctx == NULL)
        {
            int srate=G722_SAMPLE_RATE_8000;
	        srate &= ~ G722_SAMPLE_RATE_8000;
            g722_enc_ctx = g722_encoder_new(64000, srate);
        }
        return g722_enc_ctx;
    }

private:
    unsigned int        m_uSampleRate;		// 采样率
    unsigned char		m_uAlgorithm;		// 音频编解码算法，参见3.12 音频编解码算法(目前固定为PCM）
    G722_ENC_CTX        *g722_enc_ctx;      // G722压缩编码指针
    unsigned char m_nMasterSwitch;          //主开关（0/1，默认为0关闭）
    unsigned char m_nPriority;              //优先级(1~9)，高优先级设备可以打断低优先级设备，与寻呼音源类似,默认为1
    unsigned char m_nTriggerType;           //触发类型(1~3) 混合触发 2：MIC 3：AUX，默认为1：混合触发
    unsigned char m_nTriggerSensitivity;    //信号触发灵敏度（1~9，数值越大，灵敏度越高）
    unsigned char m_nVolumeFadeLevel;       //有MIC信号时，网络信号、AUX信号的淡化级别（0~9） 默认为5:-15dB，0不淡化，1:-3dB，2：-6dB，3：-9dB，4：-12dB，5:-16dB，6：-19dB，7：-21dB，8：-24dB，9：-27dB
    unsigned char m_nDelayMode;             //延时模式（0：低延时模式，1：标准模式  2：高延时模式） 默认为1：标准模式(暂时固定)
    unsigned char m_nVolume;                //混音音量
    vector<CMyString>	m_vecSecMac;        //分区MAC集合
    bool                b_AudioMixerSingalValid;   //混音器信号是否有效
};

#endif

#endif // AUDIOMIXER_H
