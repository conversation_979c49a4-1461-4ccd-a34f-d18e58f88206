#ifndef DEVICETRIGGER_H
#define DEVICETRIGGER_H

#include <iostream>
#include <vector>
#include "Tools/CMyString.h"
#include "Global/Const.h"

using std::vector;

#define TRIG_MODE_LEVEL		0x00	// 电平触发
#define TRIG_MODE_SHORT		0x01	// 短路触发

// 消防通道
class CDeviceTrigger
{
public:
    CDeviceTrigger(void);
    ~CDeviceTrigger(void);

public:
    unsigned char	GetTriggerMode(void)					{	return m_nTriggerMode;					}
    void            SetTriggerMode(unsigned char mode)		{	m_nTriggerMode	= mode;					}
    unsigned char	GetTriggerState(void)					{	return m_nTriggerState;					}
    void            SetTriggerState(unsigned char state)	{	m_nTriggerState	= state;				}
    CMyString		GetSongPathName(void)					{	return m_strSongPathName;				}
    void            SetSongPathName(CMyString strPathName)	{	m_strSongPathName	= strPathName;		}
    CMyString		GetSongPathName(void)					{	return m_strSongPathName;				}
    void            SetSongMd5(CMyString strMd5)	        {	m_strSongMd5	= strMd5;		        }
    CMyString		GetSongMd5(void)					    {	return m_strSongMd5;				    }
    void            SetTriggerVolume(unsigned int volume)   {   m_nTriggerVolume = volume;              }
    unsigned int    GetTriggerVolume()                      {   return m_nTriggerVolume;                }

private:
    unsigned char		m_nTriggerMode;		    // 触发模式
    unsigned char		m_nTriggerState;	    // 触发状态
    CMyString		    m_strSongPathName;	    // 触发音乐完整路径名称
    CMyString		    m_strSongMd5;		    // 触发音乐Md5
    unsigned int        m_nTriggerPlayTimes;    // 触发播放次数
    unsigned int        m_nTriggerVolume;       // 触发播放音量
};



#endif // DEVICETRIGGER_H
