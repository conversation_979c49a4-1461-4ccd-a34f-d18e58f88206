#ifndef NETWORKINFO_H
#define NETWORKINFO_H

#include "Global/Const.h"

#define IP_ACCESS_MODE_DHCP		0x00	// 动态获取IP
#define IP_ACCESS_MODE_STATIC	0x01	// 静态IP

#define BANDWIDTH_UDP   0x00
#define BANDWIDTH_MUL   0x01
#define BANDWIDTH_TCP   0x02



class CNetworkInfo
{
public:
    CNetworkInfo(void);
    ~CNetworkInfo(void);

public:
    // 重置属性
    void	ResetPropterty(void);
    void    ResetBWPropterty(void);


public:
    unsigned char	m_IpAccessMode;			// IP获取方式
    char	m_szIP[MAX_IP_LEN];				// IP地址
    char	m_szSubnetMask[MAX_IP_LEN];		// 子网掩码
    char	m_szGateway[MAX_IP_LEN];		// 网关
    char	m_szDNS1[MAX_IP_LEN];			// DNS1
    char	m_szDNS2[MAX_IP_LEN];			// DNS2
    char	m_szMac[SEC_MAC_LEN];			// MAC

};

#endif // NETWORKINFO_H
