#ifndef AUDIOCOLLECTOR_H
#define AUDIOCOLLECTOR_H

#include <stddef.h>
#include <iostream>
#include <vector>
#include "Tools/G722/g722_encoder.h"
#include "Tools/CMyString.h"

#define AUDIO_MULTICAST_PORT_MIN        50301
#define STREAMING_MULTICAST_PORT_MIN    50340

#define AUDIO_COLLECTOR_CHANNELS        4       //每台设备4个channel

#define AUDIO_COLLECTOR_MAX             10

enum
{
    AUDIO_RATE_16K		= 16000,
    AUDIO_RATE_2205K	= 22050,
    AUDIO_RATE_32K		= 32000,
    AUDIO_RATE_441K		= 44100,
    AUDIO_RATE_48K		= 48000,

    AUDIO_RATE_COUNT	= 5,

};

enum
{
    CHANNEL_SINGLE		= 0x01,	// 单声道
    CHANNEL_DUAL		= 0x02,	// 双声道
};

class CAudioCollector
{
public:
    CAudioCollector(int nID, unsigned char DevModel);
    ~CAudioCollector(void);

public:
    unsigned char   GetId(void)                 {   return m_uId;           }
    unsigned short	GetPort(void)				{	return m_uPort;			}
    unsigned char	GetSourceID(void)			{	return m_uSrcID;		}
    unsigned int	GetSampleRate(void)			{	return m_uSampleRate;	}
    unsigned char	GetSampleFMT(void)			{	return m_uSampleFMT;	}
    unsigned char	GetChannel(void)			{	return m_uChannel;		}
    void	SetChannel(unsigned char channel)	{	m_uChannel = channel;	}
    unsigned char	GetAlgorithm(void)			{	return m_uAlgorithm;	}
    unsigned int	GetSampleRateIndex(void);
    void	SetSampleRateIndex(unsigned int index);
    void	SetSampleRate(unsigned int rate);

    void   SetChannelNameById(int channelId,string channelName)
    {
        m_strChannelName[channelId-1] = channelName;
    }
    string   GetChannelNameById(int channelId)
    {
        return m_strChannelName[channelId-1];
    }
    vector<string>  GetChannelNameArray()
    {
        return m_strChannelName;
    }

    void  SetChannelNameArray(vector<string> channelNameArray)
    {
       m_strChannelName=channelNameArray;
    }

    
    unsigned char   GetTriggerSwitch(void)      {   return m_nTriggerSwitch;}
    void    SetTriggerSwitch(unsigned char nSwitch)              {m_nTriggerSwitch = nSwitch;}
    unsigned char   GetTriggerChannelId(void)   {   return m_nTriggrChannelId;}
    void    SetTriggerChannelId(unsigned char nChannelId)           {m_nTriggrChannelId = nChannelId;}
    unsigned char   GetTriggerVolume(void)      {   return m_nTriggerVolume;}
    void    SetTriggerVolume(unsigned char nVolume)              {m_nTriggerVolume = nVolume;}

    CMyString		GetSectionMac(int nSec)				{	return m_vecTriggerMac[nSec];					}
    int				GetSectionCount(void)					{	return m_vecTriggerMac.size();				}
    void            AddSection(CMyString strMac)        {   m_vecTriggerMac.push_back(strMac);              }
    bool            ClearSection(bool bAll,CMyString strMac);
    void            SetSection(vector<CMyString>& vecMac)    {   m_vecTriggerMac=vecMac;                    }
    int             GetSelectedSections(vector<CMyString>& vecMac);
    bool            HasSectionMac(CMyString strMac)     {   return std::count(m_vecTriggerMac.begin(), m_vecTriggerMac.end(), strMac)>0; }
    
    bool		    IsAudioCollectorSingalValid()                {	return b_AudioCollectorSingalValid;	}
    void		    SetAudioCollectorSingalValid(bool valid)     {	b_AudioCollectorSingalValid = valid;	}

    unsigned char GetPriority(void);
    void SetPriority(unsigned char priority);

    bool   operator==(const CAudioCollector &audioCollector);



    int   GetSpan();

    G722_ENC_CTX *Get_G722_enc_ctx(unsigned char channel){
        if(g722_enc_ctx[channel-1] == NULL)
        {
            int srate=G722_SAMPLE_RATE_8000;
	        srate &= ~ G722_SAMPLE_RATE_8000;
            g722_enc_ctx[channel-1] = g722_encoder_new(64000, srate);
        }
        return g722_enc_ctx[channel-1];
    }

private:
    unsigned char       m_uId;              // 采集器ID
    unsigned short		m_uPort;			// 组播端口
    unsigned char		m_uSrcID;			// 采集器音频ID
    unsigned int        m_uSampleRate;		// 采样率
    unsigned char		m_uSampleFMT;		// 采样数度，目前固定16bit
    unsigned char		m_uChannel;			// 声道数，目前固定为单声道，0x01:单声道，0x02:立体声
    unsigned char		m_uAlgorithm;		// 音频编解码算法，参见3.12 音频编解码算法(目前固定为PCM）
    G722_ENC_CTX        *g722_enc_ctx[AUDIO_COLLECTOR_CHANNELS];
    unsigned char       m_nTriggerSwitch;         //触发开关（0/1，默认为0关闭）
    unsigned char       m_nTriggrChannelId;       //触发通道ID(1~4)
    unsigned char       m_nTriggerVolume;         //触发音量
    vector<CMyString>	m_vecTriggerMac;          //触发分区MAC集合 
    bool                b_AudioCollectorSingalValid;   //音频采集器信号是否有效
    unsigned char       m_nPriority;              //触发优先级(两档，1:小于定时，2：大于定时)
    vector<string>      m_strChannelName; //通道名称
};

#endif // AUDIOCOLLECTOR_H
