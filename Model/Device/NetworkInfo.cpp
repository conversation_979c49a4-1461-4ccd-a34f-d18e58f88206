#include "NetworkInfo.h"
#include <string.h>
#include "Tools/tools.h"

CNetworkInfo::CNetworkInfo(void)
{
    memset(m_szIP, 0,sizeof(m_szIP));
    memset(m_szSubnetMask, 0,sizeof(m_szSubnetMask));
    memset(m_szGateway, 0,sizeof(m_szGateway));
    memset(m_szDNS1, 0,sizeof(m_szDNS1));
    memset(m_szDNS2, 0,sizeof(m_szDNS2));
    memset(m_szMac, 0,sizeof(m_szMac));

    m_IpAccessMode = IP_ACCESS_MODE_DHCP;
    ResetBWPropterty();
}


CNetworkInfo::~CNetworkInfo(void)
{

}

void CNetworkInfo::ResetPropterty(void)
{
    memset(m_szSubnetMask, 0,sizeof(m_szSubnetMask));
    memset(m_szGateway, 0,sizeof(m_szGateway));
    memset(m_szDNS1, 0,sizeof(m_szDNS1));
    memset(m_szDNS2,0,sizeof(m_szDNS2));

    strcpy(m_szSubnetMask, "*************");
    const char* szGateway = CNetworkTool::GetGatewayByIP(m_szIP);
    if(szGateway != NULL)
    {
        strcpy(m_szGateway, szGateway);
    }
}

void CNetworkInfo::ResetBWPropterty()
{

}

