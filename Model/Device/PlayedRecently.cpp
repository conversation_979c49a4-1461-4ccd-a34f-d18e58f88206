#include "stdafx.h"
#include "PlayedRecently.h"




CPlayedRecently::CPlayedRecently()
{
    //此处构造函数-掉线时间初始值要使用当前系统时间,否则开机第一次，如果分区不在线，定时播放会立即停止。tNow-m_tOffline永远大于timeout_source
    //m_tOffline = CTime::GetCurrentTimeT().GetTime();
    ResetTimeout();
}

CPlayedRecently::~CPlayedRecently(void)
{

}

//用于分区重新上线后判断是否有原来处于播放状态的离线分区需要恢复播放
bool	CPlayedRecently::IsNotTimeout(time_t tNow)
{
    if(g_Global.m_bNeedResumeLocalPlay)
    {
        // 目前只做音频采集器  (CS 2019-5-7)和本地播放（集中模式）(掉线后恢复播放)
        if(CProtocol::IsAudioCollectorSrc(m_nRecentSrc) || m_nRecentSrc == PRO_LOCAL_PLAY)
        {
            return ((tNow - m_tOffline) < TIMEOUT_SOURCE);
        }
        else if(m_nRecentSrc == PRO_TIMING)
        {
            return ((tNow - m_tOffline) < TIMEOUT_TIMING_SOURCE);
        }
        else
        {
            return false;
        }
    }
    else
    {
        // 目前只做音频采集器
        return (CProtocol::IsAudioCollectorSrc(m_nRecentSrc) && (tNow - m_tOffline) < TIMEOUT_SOURCE);
    }

}

//用于设备掉线检测线程判断是否有原来处于播放状态的离线分区超出时间限制，超出后需要进行关闭相应的歌曲播放等操作
bool	CPlayedRecently::IsTimeout(ctime_t tNow)
{
    if(m_nRecentSrc != PRO_OFFLINE)
    {
        //定时点的话，timeout需要拉长，因为长时间没上线也需要等待(针对定时点设置的所有分区都不在线的情况，目前设置为2个小时)
        if(m_nRecentSrc == PRO_TIMING)
        {
            return (tNow - m_tOffline) >= TIMEOUT_TIMING_SOURCE;
        }
        else
        {
            return (tNow - m_tOffline) >= TIMEOUT_SOURCE;
        }
    }
    else
    {
        return false;
    }
}

void	CPlayedRecently::ResetTimeout(void)
{
    m_tOffline = 0;
    m_nRecentSrc = PRO_OFFLINE;
    m_bSongTimerEndResumeAc = PRO_OFFLINE;
    m_bAcSourceInTiming = FALSE;
    m_bAcSourceInTrigger = FALSE;
    m_isWorkPatternDiff = FALSE;
    m_nPlayID  = -1;  // CS 2019-5-7 (掉线后恢复播放)
}














