#include "DeviceMixing.h"

CDeviceMixing::CDeviceMixing(void)
{
    m_nLineChn	= 0;
    m_nMixing	= 0;
    m_nDAC		= DEFAULT_DAC;
    m_nAUX		= DEFAULT_AUX;
}


CDeviceMixing::~CDeviceMixing(void)
{
}

bool CDeviceMixing::operator!=( const CDeviceMixing& deviceMix)
{
    return (deviceMix.m_nLineChn != m_nLineChn
         || deviceMix.m_nMixing != m_nMixing
         || deviceMix.m_nDAC != m_nDAC
         || deviceMix.m_nAUX != m_nAUX);
}
