#ifndef FIRECOLLECTOR_H
#define FIRECOLLECTOR_H

#include <iostream>
#include <vector>
#include "Tools/CMyString.h"
#include "Global/Const.h"

using std::vector;

#define FIRE_CHANNEL_COUNT	32

#define TRIG_MODE_LEVEL		0x00	// 电平触发
#define TRIG_MODE_SHORT		0x01	// 短路触发

#define TRIG_STATE_OFF		0x00	// 断开
#define TRIG_STATE_ON		0x01	// 闭合


// 消防通道
class CFireChannel
{
public:
    CFireChannel(int nID);
    ~CFireChannel(void);

public:
    int             GetID(void)								{	return m_nID;							}
    unsigned char	GetTriggerMode(void)					{	return m_nTriggerMode;					}
    void            SetTriggerMode(unsigned char mode)		{	m_nTriggerMode	= mode;					}
    unsigned char	GetTriggerState(void)					{	return m_nTriggerState;					}
    void            SetTriggerState(unsigned char state)	{	m_nTriggerState	= state;				}
    CMyString		GetSoundPathName(void)					{	return m_strSoundPathName;				}
    void            SetSoundPathName(CMyString strPathName)	{	m_strSoundPathName	= strPathName;		}
    char*           GetName(void)							{	return m_szName;						}
    void            SetName(const char* szName)	;			//{	strcpy(m_szName, szName);				}
    int             GetSectionCount(void)					{	return m_SectionMac.size();				}
    CMyString		GetSectionMac(int index)				{	return	m_SectionMac[index];			}
    CMyString		GetSectionIP(int index)					{	return	m_SectionIP[index];				}
    void            ClearSectoins(void)						{	m_SectionMac.clear();	m_SectionIP.clear();}
    void            AddSection(CMyString strMac, CMyString strIP);
    CMyString		GetTriModeString();
    bool            IsExistSection(CMyString strMac);
    void            RemoveSectionFromChannel(int index);
    bool            RemoveSectionFromChannel(const char* szMac);

private:
    int			m_nID;
    unsigned char		m_nTriggerMode;		// 触发模式
    unsigned char		m_nTriggerState;	// 触发状态
    CMyString		m_strSoundPathName;		// 报警声音完整路径名称
    char		m_szName[SEC_NAME_LEN+1];	// 通道名称，自定义
    vector<CMyString>	m_SectionMac;		// 通道对应的分区MAC
    vector<CMyString>	m_SectionIP;		// 通道对应的分区IP
};


/***************************************************************/


// 消防采集器（含有32个通道）
class CFireCollector
{
public:
    CFireCollector();
    ~CFireCollector(void);

public:
    CFireChannel& GetChannel(int index)			{	return m_Channels[index];	}
    int		GetTriggerMode(void)				{	return m_nTriggerMode;		}
    int		GetTriggerState(void)				{	return m_nTriggerState;		}
    bool	HasInitTriggerMode(void)			{	return m_bInitTriggerMode;	}
    bool	HasInitTriggerState(void)			{	return m_bInitTriggerState;	}

    void	SetTriggerMode(int nTriggerMode);
    void	UpdateTriggerMode(void);			// 通道的mode值改变，需要更新一下采集器的值
    void	SetTriggerState(int nTriggerState);
    void	UpdateTriggerState(void);			// 通道的state值改变，需要更新一下采集器的值

    int		GetTriggerChannels(int nTriggerState, CFireChannel**pChnanel);	// state的改变，得到改变的通道

    int		GetChannelCount(void);
    bool     AddChannel(CFireChannel& channel);

    bool	RemoveSectionFromAllChannels(const char* szMac);

private:
    vector<CFireChannel>	m_Channels;     //消防采集器通道
    bool	m_bInitTriggerMode;
    bool	m_bInitTriggerState;
    int		m_nTriggerMode;
    int		m_nTriggerState;
};








#endif // FIRECOLLECTOR_H
