#ifndef INFORMATION_PUBLISH_H
#define INFORMATION_PUBLISH_H

#include "Global/Const.h"
#include "Network/Protocol/Protocol.h"


#define MAX_INFORMATION_PUBLISH_EFFECTS     10
#define MAX_INFORMATION_PUBLISH_TEXT_BYTES 240  //信息发布文本最大长度，不超过240个字节


// 寻呼方式
typedef enum
{
    EF_AUTO = 0x30,                    // 自动（当区域够显示时静止，不够显示时连续左移）;
    EF_PAGE_TURNING = 0x31,            // 翻页（当区域够显示时静止，不够显示时翻页显示） 
    EF_ROTATE_LEFT = 0x32,             // 连续左移
    EF_LEFT = 0x33,                    // 左移（带停留）
    EF_ROTATE_DOWN = 0x34,             // 连续下移
    EF_DOWN = 0x35,                    // 下移（带停留）
    EF_FLICKER = 0x36,                 // 闪烁
    EF_ROTATE_UP = 0x37,               // 连续上移
    EF_UP = 0x38,                      // 上移（带停留）
    EF_SNOW = 0x39                     // 飘雪
}EffectsType;

extern unsigned char g_information_publish_effectsArry[MAX_INFORMATION_PUBLISH_EFFECTS];

class CInformationPublish
{
public:
    CInformationPublish();
    virtual ~CInformationPublish(void);

    bool        m_bEnableDisplay;               //是否启用显示
    char        m_szText[MAX_INFORMATION_PUBLISH_TEXT_BYTES+1];
    int         m_nEffects;                     //特效
    int         m_nMoveSpeed;                   //移动速度，值域1-64(单位5ms)
    int         m_nStayTime;                   //停留时间，值域1-255（单位1s）

};

#endif // SIPINFO_H
