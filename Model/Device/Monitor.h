#ifndef MONITOR_H
#define MONITOR_H

#include <QtCore/qglobal.h>

#include "Global/CType.h"
#include <unistd.h>
#include <stdlib.h>
#include <pthread.h>
#include <string.h>
#if defined(Q_OS_LINUX)
#include <netinet/in.h>
#include <arpa/inet.h>
#endif
#include <map>
#include "Tools/tools.h"
#include "Model/Other/TimerScheme.h"
#include "Network/Monitor/MonitorProtocol.h"
#include "Tools/CTime.h"
#include "TinyXml/tinystr.h"
#include "TinyXml/tinyxml.h"

#define PROJ_PATH  ("/mnt/Camtemp")
#define PROJ_ID    1

#define SOCKET_PATH   ("/tmp/camera")

#define MAX_MSGSIZE  1024

typedef struct Tag_msgbuf
{
    long msgtype;
    char msgtext[MAX_MSGSIZE];
}msgbuf_t;


#define SER_CIL  12   // 服务器to客户端
#define CIL_SER  34   // 客户端to服务器

#define  MAX_ACCOUNT_LEN          32
#define  MAX_PASSWORD_LEN         32
#define  MAX_NAME_LEN             32

#define  MAX_RTSP_LEN             1024
#define  MAX_MONEVENT_COUNT       20         // 保存最大事件数
#define  MAX_TIME_SPAN            5          // 事件时间间隔
#define  MAX_EVENT_TRIGGER_TIME   10         // 事件最大触发时间
#define  MAC_LEN                  18


typedef enum
{
    MS_OFFLINE         = -1,     // 离线
    MS_IDLE            = 0,      // 空闲
    MS_NOT_LOGIN       = 1,       // 未登录
    MS_CUSTOM_UNKNOWN  = 2       // 未知（用于自定义添加的摄像头）
}MonitorStatus;

typedef struct TriggerEvent_t
{
    string strDateTime;         // 日期时间
    EventType eventType;        // 事件类型
    char   direction;           // 方向
}TriggerEvent;


// 监控事件
class  CMonitorEvent
{
public:
    CMonitorEvent(EventType type);
    ~CMonitorEvent();

    EventType   GetEventType()                        { return m_eventType;                     }
    void		Enable(bool bValid)                   {	m_bValid = bValid;                      }
    bool		IsValid(void)                         {	return m_bValid;                        }
    int			GetVolume(void)                       {	return m_nVolume;						}
    void		SetVolume(int nVolume)                {	m_nVolume = nVolume;					}
    void		SetTimeType(TIMER_TIME_TYPE Type)          {	m_timeType	= Type;						}
    bool        IsTriggering()                        { return  m_bTrigger;                     }
    void        SetTriggering(bool trigger)           { m_bTrigger = trigger;                   }
    TIMER_TIME_TYPE	GetTimeType(void)					  {	return m_timeType;                      }
    void	    SetDateStart(TIMER_DATE& dtStart)     {	m_DateStart = dtStart;					}
    TIMER_DATE&	GetDateStart(void)                    {	return	m_DateStart;                    }
    void		SetDateEnd(TIMER_DATE& dtEnd)         {	m_DateEnd = dtEnd;						}
    TIMER_DATE&	GetDateEnd(void)                      {	return	m_DateEnd;						}
    void		SetTimeStart(TIMER_TIME& Time)	      {	m_TimeStart = Time;						}
    TIMER_TIME&	GetTimeStart(void)				      {	return m_TimeStart;						}
    void		SetTimeEnd(TIMER_TIME& Time)          {	m_TimeEnd = Time;						}
    TIMER_TIME&	GetTimeEnd(void)                      {	return m_TimeEnd;                       }
    CTime       GetDateTimeStart();
    CTime       GetDateTimeEnd();
    ctime_t     GetLatestTime()                       {	return m_tLatestTime;                   }
    void        SetLatestTime(ctime_t t)              {	m_tLatestTime = t;                      }
    BOOL*	GetSelectedDays(void)                     {	return m_SelectedDays;                  }
    void	SetSelectedDays(PBOOL bSelDays);
    string  GetSoundPath()                            { return m_SoundPath;         }
    void    SetSoundPath(string  strSoundPath)        { m_SoundPath = strSoundPath; }
    int     GetSecCount()            { return m_vecSecMac.size();  }
    string  GetSecMac(int nIndex)    { return m_vecSecMac[nIndex]; }

    void    AddSecMac(string strMac);
    void    ClearSections();

    bool    IsToStartWorking(CTime& t);      // 检查时间，是否开始工作

private:
    bool           m_bValid;
    bool           m_bTrigger;                      // 事件是否正在触发
    EventType      m_eventType;
    ctime_t        m_tLatestTime;                   // 最近一次接收到命令的时间
    int            m_nVolume;
    string         m_SoundPath;
    TIMER_TIME_TYPE     m_timeType;
    TIMER_DATE     m_DateStart;
    TIMER_DATE     m_DateEnd;
    BOOL           m_SelectedDays[DAYS_PER_WEEK];	// 按周一，周二..周日来存储
    TIMER_TIME     m_TimeStart;
    TIMER_TIME     m_TimeEnd;
    vector<string> m_vecSecMac;
};

typedef CMonitorEvent* LPCMonitorEvent;

class CMonitorInfo
{
public:
    CMonitorInfo();
    CMonitorInfo(LPCSTR  szAccount,
                 LPCSTR  szPassword,
                 LPCSTR  szMac,
                 LPCSTR  szIP,
                 LPCSTR  szName);
    ~CMonitorInfo();

    const char*         GetAccount()                                { return m_szAccount;   }
    bool                SetAccount(const char* szAccount);
    const char*         GetPassword()                               { return m_szPassword;  }
    bool                SetPassword(const char* szPassword);
    const char*         GetName()                                   { return m_szName;      }
    void                SetName(const char* szName);
    const char*         GetIP()                                     { return m_szIP;        }
    void                SetIP(const char* szIP);
    const char*         GetMac()                                    { return m_szMac;       }
    void                SetMac(const char* szMac);
    bool                IsOnline();
    MonitorStatus       GetMonStatus()                              { return m_MonStatus;   }
    void                SetMonStatus(MonitorStatus status)          { m_MonStatus = status; }
    ushort              GetPort()                                   { return m_uPort;       }
    void                SetPort(ushort uPort)                       { m_uPort = uPort;      }
    const char*         GetRTSP();

    void                SetCustomRTSP(const char *rtsp)             {sprintf(m_szCustomRtsp,rtsp);}
    const char*         GetCustomRTSP()                             {return m_szCustomRtsp;}

    EventType           GetCurTriggerEventType()                    { return m_CurTriggerEventType;           }
    void                SetCurTriggerEventType(EventType type)      { m_CurTriggerEventType = type;           }
    ctime_t             GetEventTime(EventType type)                { return m_Events[type].GetLatestTime();  }
    void                SetEventTime(EventType type, ctime_t t)     { m_Events[type].SetLatestTime(t);        }
    int                 GetEventCount()                             { return m_Events.size();                 }
    bool                GetIsAutoAdd()                              { return m_bIsAutoAdd;                    }
    void                SetIsAutoAdd(bool isAutoAdd)                { m_bIsAutoAdd = isAutoAdd;               }


    CMonitorEvent&      GetEvent(int nEventType);
    void                AddEvent(CMonitorEvent& event);   // 增加事件


    TriggerEvent&       GetTriggerEvent()                           { return m_CurTriggerEvent;               }
    void                SetTriggerEvent(TriggerEvent& triggerEvent);        // 设置触发事件

private:


private:
    //bool    m_bIsOnline;                          // 是否在线
    MonitorStatus  m_MonStatus;                   // 监控设备状态

    char    m_szAccount[MAX_ACCOUNT_LEN];         // 登录账户
    char    m_szPassword[MAX_PASSWORD_LEN];       // 登录密码
    char    m_szName[MAX_NAME_LEN];               // 监控名称
    char    m_szIP[MAX_IP_LEN];                   // IP地址
    char    m_szMac[MAC_LEN];                     // Mac地址
    ushort  m_uPort;                              // 端口号
    char    m_szRtsp[MAX_RTSP_LEN];               // RTSP地址
    char    m_szCustomRtsp[MAX_RTSP_LEN];         // 自定义的RTSP地址
    EventType  m_CurTriggerEventType;             // 当前触发事件类型
    TriggerEvent  m_CurTriggerEvent;              // 监控事件触发保存
    bool    m_bIsAutoAdd;                         // 是否自动添加

    vector<CMonitorEvent>   m_Events;             // 事件
};


typedef CMonitorInfo* LPCMonitorInfo;


class CMonitors
{
public:
    CMonitors();
    ~CMonitors();

    int       GetMonitorCount()                     { return m_MacMonitor.size();  }
    CMyString GetDateTime(void)                     { return m_strDateTime;        }
    void	  SetDateTime(CMyString strDateTime)	{ m_strDateTime = strDateTime; }

    // 添加监控设备信息
    void              AddMonitorQ(CMonitorInfo &monitorInfo);

    // 根据Mac获取MonitorInfo信息
    LPCMonitorInfo    GetMonitorByMac(const char* szMac);
    // 根据索引获取MonitorInfo信息
    CMonitorInfo      &GetMonitor(int nIndex);

    // 是否存在监控设备
    bool              IsExistMonitor(const char* szMac);

    bool              ReadFile(string strFileName);
    bool              WriteFile(string strFileName, bool bUpdateDateTime = true);

    bool              ClearOffineMonitors(vector<const char *>& vecMac);

    bool              ClearSingleOffineMonitor(string vecMac);

private:
    vector<CMonitorInfo>  m_MacMonitor;             // 监控设备Mac地址与信息类映射

    CMyString m_strDateTime;
};





#endif // MONITOR_H
