#ifndef WEBINFO_H
#define WEBINFO_H


#include <iostream>
#include <vector>
#include <map>
#include "Tools/CTime.h"
#include "Section.h"
#include "Network/Web/QtWebsocket.h"
#include <QMutex>
#include <QMutexLocker>
#if SUPPORT_WEB_PAGING
#include "Network/Web/WebPaging.h"
#endif

using namespace std;


#define BUF_LEN  1450


enum WebType
{
    WT_QT,                 // QtWebsocekt
    WT_TABLET,             // 大屏
    WT_TCP,                // TCP
    WT_TRANSFER            // 转发
};


#define MAX_IP_LEN  16
#define MAX_MAC_LEN  20
#define OFFLINE_WEB_TIME    40                     // webSocket掉线时间



/****************************
* Web 控制终端信息
***************************/

class CTablet
{
public:
    CTablet();

    // 大屏
    string  m_strMac;                              // 大屏Mac地址
    string  m_strIP;                               // 大屏IP地址
    string  m_strName;                             // 大屏名称
    int     m_TabletType;                          // 大屏类型 保留，以后可能用到
};

class CFileDateTime
{
public:
    CFileDateTime();
    string  GetFileDateTime(FileType ft);
    void    SetFileDateTime(FileType ft, string strDateTime);

private:
    string  m_strFileDateTimes[DT_COUNT];
};


class CWebSection
{
public:
    CWebSection();
    CWebSection(string strMac, string strIP, string strName, int nType);
    CWebSection(string strSocketID, WebType wt= WT_QT);
    ~CWebSection();

    WebType         GetWebType()                            { return m_WebType;             }
    void            SetWebType(WebType webType)             { m_WebType = webType;          }
    ctime_t         GetLatestTime()                         { return m_tLastestTime;        }
    void            SetLastestTime(ctime_t t)               { m_tLastestTime = t;           }
    int             GetSelectedID()                         { return m_SecetedID;           }
    string          GetSocketID()                             { return m_strSocketID;           }
    void            SetSocketID(string strSocketID)             { m_strSocketID = strSocketID;      }
    bool            IsForware()                             { return m_bForware;            }
    void            SetForware(bool bForware)               { m_bForware = bForware;        }

    //  socket是否有效，在线且已登录用户
    bool            IsValid();
    bool            IsSuperUser();

    bool            GetIsOnline()               { return m_bOnline;     }
    void            SetIsOnline(bool flag)      { m_bOnline = flag;     }

    // 设置web选中分区Mac地址
    void            SetSelectSection(int nSelectedID, int nZoneCount, string* strZoneMac, bool isClear);

    // 获取web选中分区Mac地址
    const char*     GetSelectMacByIndex(int nIndex)         { return m_vecMac[nIndex].data();       }
    int             GetMacCount()                           { return m_vecMac.size();               }

    string          GetSecFileDateTime(string strMac, FileType ft);
    void            ResetSecFileDateTime(void);
    bool            NeedUpdateFile(string strMac, FileType ft, string strDateTime);	// 需要更新文件（已获取）

    void            SetSecFileDateTime(string strMac, FileType ft, string strDateTime);

#if SUPPORT_WEB_PAGING
    void            ReleaseWebPaging(void);
#endif

public:
    //QWebSocket*  m_client;
    string          m_strSockID;   // connect socket id

    // 大屏
    CTablet         m_Tablet;

#if SUPPORT_WEB_PAGING
    //WEB广播寻呼
    CWebPaging     *m_webPaging;
#endif

private:
// 其他
    bool    m_bOnline;               // 是否在线
    string  m_strSocketID;             // 传回给用户的UUID
    int     m_SecetedID;             // 选中分区的标识
    ctime_t m_tLastestTime;          // 最近一次接收到命令的时间
    WebType m_WebType;               // 分控设备类型
    vector<string>      m_vecMac;    // 选中分区/分控 Mac地址
    bool    m_bForware;              // 是否分发状态，在web端第一次请求分区状态之后才允许同步状态

    map<string, CFileDateTime>  m_SectionFileTimes;        // 分区Mac地址与文件时间映射

public:
    QMutex  websection_webpaging_mutex;
};

typedef     CWebSection  *LPCWebSection;



class CWebSections
{
public:
    CWebSections();
    ~CWebSections();

    void    AddWebSection(CWebSection& webSection);
    void    RemoveWebSectionBySockID(string strSockID);

    int             GetWebCount()                 { return m_IDSocket.size(); }
    LPCWebSection   GetWebSection(int nIndex);
    LPCWebSection   GetWebSectionBySocketID(string strSocketID);
    LPCWebSection   GetWebByMac(string strMac);
    LPCWebSection   GetWebByIP(string strIP);
    void            SetSocketSecFileDateTime(string strUID, string strMac, FileType ft, string strDateTime);

    map<string, LPCWebSection>  m_IDSocket;   // uuid与WebSection映射
    map<string, LPCWebSection>  m_IPTablets;  // IP与大屏设备映射

public:
    QMutex  websections_mutex;
};


#endif // WEBINFO_H
