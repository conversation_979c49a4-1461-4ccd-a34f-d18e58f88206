#include "AudioCall.h"

CAudioCall::CAudioCall(void)
{
    m_isCallingParty = 1;
    memset(m_callMac,0,sizeof(m_callMac));
    m_audioCoding=0;    //PCM
    m_enableNat=1;
    m_audioPort=0;

    memset(&m_stCallDeviceConfig,0,sizeof(m_stCallDeviceConfig));
    strcpy(m_stCallDeviceConfig.Key1_mac,"00:00:00:00:00:00");
    strcpy(m_stCallDeviceConfig.Key2_mac,"00:00:00:00:00:00");
    m_stCallDeviceConfig.AutoAnswerTime = 0;
    m_stCallDeviceConfig.micVol = 5;
    m_stCallDeviceConfig.farOutVol = 5;
}


CAudioCall::~CAudioCall(void)
{

}