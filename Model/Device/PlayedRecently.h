#ifndef PLAYEDRECENTLY_H
#define PLAYEDRECENTLY_H

#include "Network/Protocol/Protocol.h"
#include "Tools/CTime.h"

#define TIMEOUT_SOURCE	(900)  // 音源超时时间(15分钟)

#define TIMEOUT_TIMING_SOURCE	(43200)  // 定时音源超时时间(12小时)

// 设备掉线前的记录
class CPlayedRecently
{
public:
    CPlayedRecently();
    ~CPlayedRecently();

    bool	IsNotTimeout(time_t tNow);
    bool	IsTimeout(long long tNow);
    void	ResetTimeout(void);

public:
    ctime_t			m_tOffline;					// 掉线时间
    ProgramSource	m_nRecentSrc;				// 最近的节目源
    ProgramSource   m_bSongTimerEndResumeAc;  // 歌曲定时结束需要恢复音频采集标记
    bool            m_bAcSourceInTiming;         // 本身是定时音频采集标志
    bool            m_bAcSourceInTrigger;        // 本身是触发音频采集标志
    bool			m_isWorkPatternDiff;		// 分区的工作模式是否不一样
    int				m_nPlayID;				    // 播放ID，定时（播放ID），播放歌曲（播放ID）
};

#endif // PLAYEDRECENTLY_H
