#ifndef AUDIOFORWARD_H
#define AUDIOFORWARD_H

#include <iostream>
#include <vector>
#include "Tools/CMyString.h"

using std::vector;

class CAudioForward
{
public:
    CAudioForward(void);
    ~CAudioForward(void);

public:
    int		GetCount(void)				{	return m_vecMacs.size();			}
    void	SetSections(vector<CMyString>& vecMacs);
    CMyString	GetAt(int index);

private:
    vector<CMyString> m_vecMacs;
};

#endif // AUDIOFORWARD_H
