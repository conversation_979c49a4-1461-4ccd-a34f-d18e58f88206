#include "stdafx.h"
#include "Monitor.h"



/***********           监控事件                 ***********/

CMonitorEvent::CMonitorEvent(EventType type)
{
    m_eventType = type;
    m_bValid = FALSE;
    m_timeType = TT_DEFAULT;
    m_nVolume = 0;
    m_SoundPath = "";
    m_tLatestTime = 0;

    m_vecSecMac.clear();
}

CMonitorEvent::~CMonitorEvent()
{

}


CTime CMonitorEvent::GetDateTimeStart()
{
    CTime DateTimeStart(m_DateStart.nYear,
                        m_DateStart.nMon,
                        m_DateStart.nDay,
                        m_TimeStart.nHour,
                        m_TimeStart.nMin,
                        m_TimeStart.nSec);

    return DateTimeStart;
}

CTime CMonitorEvent::GetDateTimeEnd()
{
    CTime DateTimeEnd(m_DateEnd.nYear,
                      m_DateEnd.nMon,
                      m_DateEnd.nDay,
                      m_TimeEnd.nHour,
                      m_TimeEnd.nMin,
                      m_TimeEnd.nSec);

    return DateTimeEnd;
}


void CMonitorEvent::SetSelectedDays(bool *bSelDays)
{
    memcpy(m_SelectedDays, bSelDays, sizeof(BOOL)*DAYS_PER_WEEK);
}

void CMonitorEvent::AddSecMac(string strMac)
{
    m_vecSecMac.push_back(strMac);
}

void CMonitorEvent::ClearSections()
{
    m_vecSecMac.clear();
}

// 检查时间，是否开始工作
bool CMonitorEvent::IsToStartWorking(CTime &t)
{
    // 如果无效或者正在执行，则返回FALSE
    if (!IsValid())
    {
        return FALSE;
    }

    bool flag = TRUE;

    if (m_timeType == TT_WEEK_CYCLE)
    {
        // 星期日L
        if (t.GetDayOfWeek() == 1)
        {
            if (!m_SelectedDays[6]) // 未选中，则返回FALSE
            {
                return FALSE;
            }
        }
        // 星期一到星期六
        else
        {
            if (!m_SelectedDays[t.GetDayOfWeek()-2])// 未选中，则返回FALSE
            {
                return FALSE;
            }
        }

        CTime  DateTimeStart(t.GetYear(), t.GetMonth(), t.GetDay(), m_TimeStart.nHour, m_TimeStart.nMin, m_TimeStart.nSec);
        CTime  DateTimeEnd(t.GetYear(), t.GetMonth(), t.GetDay(), m_TimeEnd.nHour, m_TimeEnd.nMin, m_TimeEnd.nSec);

        flag = (t >= DateTimeStart && t <= DateTimeEnd);
    }
    else if (m_timeType == TT_SPECIFY_DATE)
    {
        CTime  DateTimeStart = GetDateTimeStart();
        CTime  DateTimeEnd = GetDateTimeEnd();
        flag = (t >= DateTimeStart && t<= DateTimeEnd);
    }
    else
    {
        flag = FALSE;
    }

    return flag;
}



/**********            CMonitorInfo            **************/


CMonitorInfo::CMonitorInfo()
{
    //m_Channel = 0;
    m_uPort = 0;
    //m_bIsOnline = false;
    m_MonStatus = MS_OFFLINE;
    m_CurTriggerEventType = EVENT_NULL;
    memset(m_szAccount, 0,sizeof(m_szAccount));
    memset(m_szPassword,0, sizeof(m_szPassword));
    memset(m_szIP, 0,sizeof(m_szIP));
    memset(m_szMac,0, sizeof(m_szMac));
    memset(m_szRtsp,0, sizeof(m_szRtsp));
    memset(m_szCustomRtsp,0,sizeof(m_szCustomRtsp));
    m_bIsAutoAdd=true;
}


CMonitorInfo::CMonitorInfo(const char *szAccount, const char *szPassword, const char *szMac, const char *szIP, const char *szName)
{
    m_uPort = 0;
    m_MonStatus = MS_OFFLINE;
    m_CurTriggerEventType = EVENT_NULL;
    memset(m_szAccount, 0,sizeof(m_szAccount));
    memset(m_szPassword,0, sizeof(m_szPassword));
    memset(m_szIP, 0,sizeof(m_szIP));
    memset(m_szName,0,sizeof(m_szName));
    memset(m_szRtsp,0, sizeof(m_szRtsp));
    memset(m_szCustomRtsp,0,sizeof(m_szCustomRtsp));

    if(szAccount != NULL)
    {
        strcpy(m_szAccount, szAccount);
    }

    if(szPassword != NULL)
    {
        strcpy(m_szPassword, szPassword);
    }

    if(szMac != NULL)
    {
        strcpy(m_szMac, szMac);
    }

    if(szIP != NULL)
    {
        strcpy(m_szIP, szIP);
    }

    if(szName != NULL)
    {
        strcpy(m_szName, szName);
    }

}

CMonitorInfo::~CMonitorInfo()
{
    memset(m_szAccount, 0,sizeof(m_szAccount));
    memset(m_szPassword,0, sizeof(m_szPassword));
    memset(m_szIP, 0,sizeof(m_szIP));
    memset(m_szRtsp,0, sizeof(m_szRtsp));
}


bool CMonitorInfo::SetAccount(const char *szAccount)
{
    if(szAccount == NULL)
    {
        return false;
    }

    if(strlen(szAccount) >= MAX_ACCOUNT_LEN)
    {
        strncpy(m_szAccount, szAccount, MAX_ACCOUNT_LEN);
        m_szAccount[MAX_NAME_LEN-1] = '\0';
        return  true;
    }

    strcpy(m_szAccount, szAccount);
    return true;
}


bool CMonitorInfo::SetPassword(const char *szPassword)
{
    if(szPassword == NULL)
    {
        return  false;
    }

    if(strlen(szPassword) >= MAX_PASSWORD_LEN)
    {
        strncpy(m_szPassword, szPassword, MAX_PASSWORD_LEN);
        m_szPassword[MAX_PASSWORD_LEN-1] = '\0';
        return true;
    }

    strcpy(m_szPassword, szPassword);
    return true;
}

void CMonitorInfo::SetName(const char *szName)
{
    if(szName == NULL)
    {
        return;
    }

    if(strlen(szName) > MAX_NAME_LEN)
    {
        strncpy(m_szName, szName, MAX_NAME_LEN);
        m_szName[MAX_NAME_LEN-1] = '\0';
        return;
    }

    strcpy(m_szName, szName);
}


void CMonitorInfo::SetIP(const char *szIP)
{
    if(szIP == NULL || strlen(szIP) > MAX_IP_LEN)
    {
        return;
    }

    strcpy(m_szIP, szIP);
}

void CMonitorInfo::SetMac(const char *szMac)
{
    if(szMac == NULL || strlen(szMac) > MAC_LEN)
    {
        return;
    }

    strcpy(m_szMac, szMac);
}

bool CMonitorInfo::IsOnline()
{
    return (m_MonStatus != MS_OFFLINE);
}

const char *CMonitorInfo::GetRTSP()
{
    //rtsp://username:password@ip:port/cam/realmonitor?channel=1&subtype=0
    //说明:
    //username: 用户名。例如admin。
    //password: 密码。例如admin。
    //ip: 为设备IP。例如 **********。
    //port: 端口号默认为554，若为默认可不填写。
    //channel: 通道，默认为1。
    //subtype: 码流类型，主码流为0（即subtype=0），辅码流为1（即subtype=1）。
    if(m_bIsAutoAdd) {
        char RTSP[] = "rtsp://%s:%s@%s:%d/cam/realmonitor?channel=%d&subtype=0";
        //char szRTSP[MAX_BUF_LEN] = {0};
        sprintf(m_szRtsp, RTSP, GetAccount(), GetPassword(), GetIP(),MONITOR_PORT , 1);
    }
    else
    {
        return m_szCustomRtsp;
    }

    return m_szRtsp;
}


CMonitorEvent &CMonitorInfo::GetEvent(int nEventType)
{
    return m_Events[nEventType];
}

// 增加事件
void CMonitorInfo::AddEvent(CMonitorEvent &event)
{
    m_Events.push_back(event);
}



#if 0
// 检查事件时间是否与最后保存同类型的事件 时间超过保存间隔（5秒）
bool CMonitorInfo::CheckEventTime(LPCMonitorEvent Event)
{
    if(Event == NULL)       return false;

    LPCMonitorEvent pEvent = GetEventByType(Event->GetEventType());
    if(pEvent == NULL)      return  true;

    /*CTime  ot(pEvent->GetDataTime().data());
        CTime  nt(Event->GetDataTime().data());

        CTimeSpan  ts = nt - ot;
        if(ts.GetSeconds() > MAX_TIME_SPAN)
        {
                return true;
        }

        return false;*/
}
#endif

void CMonitorInfo::SetTriggerEvent(TriggerEvent &triggerEvent)
{
    // 设置当前触发事件类型
    SetCurTriggerEventType(triggerEvent.eventType);

    m_CurTriggerEvent = triggerEvent;
}




/*###########################################*/

CMonitors::CMonitors()
{
    m_strDateTime = "";
}

CMonitors::~CMonitors()
{

}


void CMonitors::AddMonitorQ(CMonitorInfo &monitorInfo)
{
    m_MacMonitor.push_back(monitorInfo);
}


// 根据Mac获取MonitorInfo信息
LPCMonitorInfo CMonitors::GetMonitorByMac(const char *szMac)
{
    vector<CMonitorInfo>::iterator iter = m_MacMonitor.begin();
    for(;iter!=m_MacMonitor.end();iter++)
    {
        LPCMonitorInfo pMonitorInfo = &*iter;;
        if(strcmp(pMonitorInfo->GetMac(), szMac) == 0)
        {
            return pMonitorInfo;
        }
    }

    return NULL;
}

// 根据索引获取MonitorInfo信息
CMonitorInfo& CMonitors::GetMonitor(int nIndex)
{
    return m_MacMonitor[nIndex];
}

// 是否存在监控设备
bool CMonitors::IsExistMonitor(const char *szMac)
{
    vector<CMonitorInfo>::iterator iter = m_MacMonitor.begin();
    for(;iter!=m_MacMonitor.end();iter++)
    {
        LPCMonitorInfo pMonitorInfo = &*iter;;
        if(strcmp(pMonitorInfo->GetMac(), szMac) == 0)
        {
            return true;
        }
    }
    return false;
}


bool CMonitors::ReadFile(string strFileName)
{
    TiXmlDocument xmlMonitor;
    TIMER_DATE    date;
    TIMER_TIME    time;
    string		  strData					= "";
    BOOL		  bSelDays[DAYS_PER_WEEK]   = {0};

    // 使用文件名组合路径
    CHAR	szPathName[STR_MAX_PATH]	= {0};
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName.data());

    if(xmlMonitor.LoadFile(szPathName))
    {
        m_MacMonitor.clear();
        TiXmlElement* MonitorDevice = xmlMonitor.FirstChildElement();
        SetDateTime(CMyString(MonitorDevice->Attribute("DateTime")));

        // Monitor
        for(TiXmlElement* Monitor=MonitorDevice->FirstChildElement(); Monitor!=NULL;
            Monitor=Monitor->NextSiblingElement())
        {
            LPCSTR  szMac = Monitor->Attribute("Mac");
            LPCSTR  szIP = Monitor->Attribute("IP");
            LPCSTR  szAccount = Monitor->Attribute("Account");
            LPCSTR  szPassword = Monitor->Attribute("Password");
            LPCSTR  szName = Monitor->Attribute("Name");

            LPCSTR  szAutoAdd = Monitor->Attribute("AutoAdd");

            bool isAutoAdd=true;
            //如果不存在AutoAdd字段，代表自动加入
            if(szAutoAdd != NULL)
            {
                if(strcmp(szAutoAdd, "FALSE") == 0)
                {
                    isAutoAdd=false;
                }
            }

            LPCSTR  szRtspUrl = Monitor->Attribute("rtspUrl");


            CMonitorInfo monitorInfo(szAccount, szPassword, szMac, szIP, (strlen(szName) == 0) ? szIP : szName);
            monitorInfo.SetIsAutoAdd(isAutoAdd);

            if(!isAutoAdd)
            {
                monitorInfo.SetMonStatus(MS_CUSTOM_UNKNOWN);
                if(szRtspUrl)
                {
                    monitorInfo.SetCustomRTSP(szRtspUrl);
                }
            }

            #if 0   //******** 取消监控事件检测
            // Event
            for(TiXmlElement* Event=Monitor->FirstChildElement(); Event!=NULL;
                Event=Event->NextSiblingElement())
            {
                int nType = atoi(Event->Attribute("Type"));
                CMonitorEvent event((EventType)nType);
                event.Enable(strcmp(Event->Attribute("Valid"), "TRUE") == 0);
                int nTimeType = atoi(Event->Attribute("TimerType"));
                event.SetTimeType((TIMER_TIME_TYPE)nTimeType);
                event.SetVolume(atoi(Event->Attribute("Volume")));
                event.SetSoundPath(Event->Attribute("Sound"));

                // 时间按周循环
                if(nTimeType == TT_WEEK_CYCLE)
                {
                    TiXmlElement* SelectedDays = Event->FirstChildElement("SelectedDays");
                    TiXmlNode* text = SelectedDays->FirstChild();
                    strData = string(text->ToText()->Value());
                    // 每次取定时点都需要初始化一遍
                    memset(bSelDays, 0 , sizeof(BOOL)*DAYS_PER_WEEK);
                    for(int i=0; i<(int)strData.length(); i++)
                    {
                        char cIndex = strData.at(i);
                        int Index = cIndex - 48 - 1;
                        bSelDays[Index] = TRUE;
                    }
                    event.SetSelectedDays(bSelDays);
                }
                // 时间按日期循环
                else if(nTimeType == TT_SPECIFY_DATE)
                {
                    // 开始日期
                    TiXmlElement* StartDate = Event->FirstChildElement("StartDate");
                    TiXmlNode* startText = StartDate->FirstChild();
                    strData = string(startText->ToText()->Value());
                    date.nYear = std::stoi(strData.substr(0,4));
                    date.nMon  = std::stoi(strData.substr(5,2));
                    date.nDay  = std::stoi(strData.substr(8,2));
                    event.SetDateStart(date);

                    // 结束日期
                    TiXmlElement* EndDate = StartDate->NextSiblingElement();
                    TiXmlNode* endText = EndDate->FirstChild();
                    strData = string(endText->ToText()->Value());
                    date.nYear = std::stoi(strData.substr(0,4));
                    date.nMon  = std::stoi(strData.substr(5,2));
                    date.nDay  = std::stoi(strData.substr(8,2));
                    event.SetDateEnd(date);
                }

                //if(nTimeType != TT_DEFAULT)
                {
                    // 开始时间
                    TiXmlElement* StartTime = Event->FirstChildElement("StartTime");
                    TiXmlNode* startTimeText = StartTime->FirstChild();
                    strData = string(startTimeText->ToText()->Value());
                    time.nHour = std::stoi(strData.substr(0,2));
                    time.nMin  = std::stoi(strData.substr(3,2));
                    time.nSec  = std::stoi(strData.substr(6,2));
                    event.SetTimeStart(time);

                    // 结束时间
                    TiXmlElement* EndTime = StartTime->NextSiblingElement();
                    TiXmlNode* endTimeText = EndTime->FirstChild();
                    strData = string(endTimeText->ToText()->Value());
                    time.nHour = std::stoi(strData.substr(0,2));
                    time.nMin  = std::stoi(strData.substr(3,2));
                    time.nSec  = std::stoi(strData.substr(6,2));
                    event.SetTimeEnd(time);

                    // 选中分区
                    //vector<string>  vecMacs;
                    TiXmlElement* SelSection=Event->FirstChildElement("SelectedSections");
                    for(TiXmlElement* Section=SelSection->FirstChildElement("Section"); Section!=NULL;
                        Section=Section->NextSiblingElement())
                    {
                        string  strMac = Section->Attribute("Mac");
                        event.AddSecMac(strMac);
                    }
                }

                monitorInfo.AddEvent(event);
            }
            #endif

            AddMonitorQ(monitorInfo);
        }
    }
    else
    {
        return false;
    }


    xmlMonitor.Clear();
    return true;
}

bool CMonitors::WriteFile(string strFileName, bool bUpdateDateTime)
{
    TiXmlDocument xmlMonitor;
    int		nMonitorCount = GetMonitorCount();
    TIMER_DATE      date;
    TIMER_TIME      time;
    string          str                           = "";
    string          strData					= "";
    BOOL*           bSelDays				= NULL;

    CHAR	szPathName[STR_MAX_PATH] = {0};
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName.data());

    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0", "utf-8", "no");
    xmlMonitor.LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment("Save for Monitor info");
    xmlMonitor.LinkEndChild(com);

    TiXmlElement* MonitorDevice = new TiXmlElement("MonitorDevice");
    MonitorDevice->SetAttribute("MonitorCount", nMonitorCount);
    MonitorDevice->SetAttribute("DateTime", GetDateTime().C_Str());
    xmlMonitor.LinkEndChild(MonitorDevice);

    // 监控数量
    for(int i=0; i<nMonitorCount; i++)
    {
        CMonitorInfo monitor =  GetMonitor(i);
        
        //******** 取消监控事件检测
        //int nEventCount = monitor.GetEventCount();
        int nEventCount = 0;
        TiXmlElement* Monitor = new TiXmlElement("Monitor");
        Monitor->SetAttribute("Mac", monitor.GetMac());
        Monitor->SetAttribute("IP", monitor.GetIP());
        Monitor->SetAttribute("Account", monitor.GetAccount());
        Monitor->SetAttribute("Password", monitor.GetPassword());
        Monitor->SetAttribute("Name", monitor.GetName());
        
        Monitor->SetAttribute("AutoAdd", monitor.GetIsAutoAdd()? "TRUE" : "FALSE");
        Monitor->SetAttribute("rtspUrl", monitor.GetRTSP());

        Monitor->SetAttribute("EventCount", nEventCount);
        MonitorDevice->LinkEndChild(Monitor);

        // 事件数量
        for(int j=0; j<nEventCount; j++)
        {
            CMonitorEvent event = monitor.GetEvent(j);
            int nSectionCount = event.GetSecCount();
            TiXmlElement* Event = new TiXmlElement("Event");
            Event->SetAttribute("Type", event.GetEventType());
            Event->SetAttribute("Valid", event.IsValid() ? "TRUE" : "FALSE");
            Event->SetAttribute("TimerType", event.GetTimeType());
            Event->SetAttribute("Volume", event.GetVolume());
            //Event->SetAttribute("SectionCount", nSectionCount);
            Event->SetAttribute("Sound", event.GetSoundPath().data());
            Monitor->LinkEndChild(Event);

            // 按周循环
            if(event.GetTimeType() == TT_WEEK_CYCLE)
            {
                strData = "";
                bSelDays = event.GetSelectedDays();
                for(int i=0; i<DAYS_PER_WEEK; i++)
                {
                    if(bSelDays[i])
                    {
                        str = std::to_string(i+1);
                        strData += str;
                    }
                }
                TiXmlElement* SelectedDays = new TiXmlElement("SelectedDays");
                TiXmlText* days = new TiXmlText(strData.c_str());
                SelectedDays->LinkEndChild(days);
                Event->LinkEndChild(SelectedDays);
            }
            // 指定日期
            else if(event.GetTimeType() == TT_SPECIFY_DATE)
            {
                // 开始日期
                date = event.GetDateStart();
                char startDate[25] = {0};
                sprintf(startDate, "%04d-%02d-%02d", date.nYear, date.nMon, date.nDay);
                TiXmlElement* StartDate = new TiXmlElement("StartDate");
                TiXmlText* sDate = new TiXmlText(startDate);
                StartDate->LinkEndChild(sDate);
                Event->LinkEndChild(StartDate);

                // 截止日期
                date = event.GetDateEnd();
                char endDate[25] = {0};
                sprintf(endDate,"%04d-%02d-%02d",date.nYear, date.nMon, date.nDay);
                TiXmlElement* EndDate = new TiXmlElement("EndDate");
                TiXmlText* eDate = new TiXmlText(endDate);
                EndDate->LinkEndChild(eDate);
                Event->LinkEndChild(EndDate);
            }

            //if(event.GetTimeType() != TT_DEFAULT)
            {
                // 开始时间
                time = event.GetTimeStart();
                char startTime[20] = {0};
                sprintf(startTime,"%02d:%02d:%02d",time.nHour, time.nMin, time.nSec);
                TiXmlElement* StartTime = new TiXmlElement("StartTime");
                TiXmlText* tStart = new TiXmlText(startTime);
                StartTime->LinkEndChild(tStart);
                Event->LinkEndChild(StartTime);

                // 结束时间
                time = event.GetTimeEnd();
                char endTime[20] = {0};
                sprintf(endTime,"%02d:%02d:%02d",time.nHour, time.nMin, time.nSec);
                TiXmlElement* EndTime = new TiXmlElement("EndTime");
                TiXmlText* tEnd = new TiXmlText(endTime);
                EndTime->LinkEndChild(tEnd);
                Event->LinkEndChild(EndTime);

                // 选中分区
                TiXmlElement* SelectedSections = new TiXmlElement("SelectedSections");
                SelectedSections->SetAttribute("SectionCount", event.GetSecCount());
                for(int k=0; k<nSectionCount; k++)
                {
                    TiXmlElement* Section = new TiXmlElement("Section");
                    Section->SetAttribute("Mac", event.GetSecMac(k).data());
                    SelectedSections->LinkEndChild(Section);
                }
                Event->LinkEndChild(SelectedSections);
            }
        }
    }

    bool saveFileOK=true;
    if(xmlMonitor.SaveFile(szPathName))
    {
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlMonitor.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    return saveFileOK;
}

bool CMonitors::ClearOffineMonitors(vector<const char *>& vecMac)
{
    bool flag = false;
    vector<CMonitorInfo>::iterator iter = m_MacMonitor.begin();
    for(;iter!=m_MacMonitor.end();)
    {
        LPCMonitorInfo pMonitorInfo = &*iter;;
        if(!pMonitorInfo->IsOnline())
        {
            vecMac.push_back(pMonitorInfo->GetMac());
            iter = m_MacMonitor.erase(iter);
            flag = true;
            continue;
        }
        else
        {
            iter++;
        }
    }

    return flag;
}



bool CMonitors::ClearSingleOffineMonitor(string vecMac)
{
    bool flag = false;
    vector<CMonitorInfo>::iterator iter = m_MacMonitor.begin();
    for(;iter!=m_MacMonitor.end();)
    {
        LPCMonitorInfo pMonitorInfo = &*iter;;
        if((pMonitorInfo->GetMonStatus() == MS_OFFLINE  || pMonitorInfo->GetMonStatus() == MS_CUSTOM_UNKNOWN)  && strcmp(vecMac.data(),pMonitorInfo->GetMac()) ==0)
        {
            iter = m_MacMonitor.erase(iter);
            flag = true;
            continue;
        }
        else
        {
            iter++;
        }
    }

    return flag;
}



