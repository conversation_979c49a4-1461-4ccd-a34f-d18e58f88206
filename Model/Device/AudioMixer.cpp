#include "AudioMixer.h"
#include "Network/Protocol/Protocol.h"

#if SUPPORT_AUDIO_MIXER
CAudioMixer::CAudioMixer()
{
    m_uSampleRate	= AUDIO_MIXER_RATE_32K;
    m_uAlgorithm	= ALGORITHM_PCM;
    g722_enc_ctx = NULL;

    b_AudioMixerSingalValid = false;        //AudioMixer信号是否有效

    m_nMasterSwitch=0;                      //主开关（0/1，默认为0关闭）
    m_nPriority=1;                          //优先级(1~9)，高优先级设备可以打断低优先级设备，与寻呼音源类似,默认为1
    m_nTriggerType=1;                       //触发类型(1~3) 1：混合触发 2：MIC 3：AUX，默认为1：混合触发
    m_nTriggerSensitivity=5;                //信号触发灵敏度（1~9，数值越大，灵敏度越高）
    m_nVolumeFadeLevel=5;                   //MIC信号进入时，网络信号、AUX信号的淡化级别,默认为5:-15dB; 0~9：0dB、-3dB、-6dB、-9dB、-12dB、-15dB、-18dB、-21dB、-24dB、-70dB
    m_nVolume=50;                           //混音音源分区音量
    m_nDelayMode=1;                         //延时模式（0：低延时模式，1：标准模式  2：高延时模式） 默认为1：标准模式(暂时固定)
}

CAudioMixer::~CAudioMixer(void)
{

}

bool CAudioMixer::operator==(const CAudioMixer &audioMixer)
{
   if(m_nMasterSwitch!=audioMixer.m_nMasterSwitch ||\
      m_nPriority!=audioMixer.m_nPriority ||\
      m_nTriggerType!=audioMixer.m_nTriggerType ||\
      m_nTriggerSensitivity!=audioMixer.m_nTriggerSensitivity ||\
      m_nVolumeFadeLevel!=audioMixer.m_nVolumeFadeLevel ||\
      m_nVolume!=audioMixer.m_nVolume ||\
      m_vecSecMac!=audioMixer.m_vecSecMac
    )
   {
        return false;
   }
   return true;
}


int CAudioMixer::GetSelectedSections(vector<CMyString>& vecMac)
{
    vecMac.clear();

    for (unsigned int i=0; i<m_vecSecMac.size(); ++i)
    {
        vecMac.push_back(m_vecSecMac[i]);
    }

    return vecMac.size();
}

bool CAudioMixer::ClearSection(bool bAll,CMyString strMac)
{
    bool bUpdate=false;
    if(bAll)
    {
        if(m_vecSecMac.size()>0)
        {
            m_vecSecMac.clear();
            bUpdate=true;
        }
    }
    else
    {
        vector<CMyString>::iterator iter;
        for(iter=m_vecSecMac.begin();iter!=m_vecSecMac.end();++iter)
        {
            if (strMac == *iter)
            {
                m_vecSecMac.erase(iter);
                bUpdate = true;
                break;
            }
        }
    }
    return bUpdate;
}

#endif