#ifndef CSTREAMINGGATEWAY_H
#define CSTREAMINGGATEWAY_H

#include <vector>
#include "Network/Protocol/Protocol.h"

using namespace std;

typedef struct
{
    char szName[32];
    char szMac[18];

}TrustedDevice;

typedef enum
{
   ST_DLNA = 1,
   ST_AIRPLAY,
   ST_NET_RADIO,
   ST_IDLE,

}StreamingType;

#define MAX_TRUESTED_DEVICE_PER_PAGE  20

class CStreamingGateway
{
public:
    CStreamingGateway();

    bool AddTrustDevice(const char* mac, const char* name);
    bool EditTrustDevice(const char* mac, const char* name);
    bool RemoveTrustDeivce(const char* mac);
    void ClearTrustDevices(void);
    TrustedDevice& GetTrustDevice(int index)    {   return m_TrustedDevices[index];     }

    int		  GetTrustDevicesCount(void)        {	return m_TrustedDevices.size();		}
    bool	  HasRecvAll(void)                  {	return m_nPackID == m_nPackCount;	}
    int       GetPageCount(void);

public:
    bool            m_isRadioConnected;
    char            m_szSourceName[128];
    PlayStatus      m_nPlayStatus;
    StreamingType   m_StreamingType;

    vector<TrustedDevice> m_TrustedDevices;

    unsigned char   m_nPackCount;
    unsigned char   m_nPackID;

};

#endif // CSTREAMINGGATEWAY_H
