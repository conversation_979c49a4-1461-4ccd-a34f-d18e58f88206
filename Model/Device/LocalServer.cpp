//#include "LocalServer.h"




//CLocalServer::CLocalServer()
//{
//    m_nID               = 0;
//    m_nVolume			= 50;
//    m_nProSource		= PRO_IDLE;
//    m_nPreSource		= PRO_IDLE;
//    m_nPlayStatus		= PS_STOP;
//    m_isUpgrading		= FALSE;
//    m_isSyncSong		= FALSE;
//    m_nFinishedSong		= 0;
//    m_nSyncSongCount	= 0;
//    m_nPlayID			= -1;

//    bzero(m_szProName, sizeof(m_szProName));

//    m_strCurDateTime	= "";
//    m_bConnectedGPS		= FALSE;
//}

//CLocalServer::~CLocalServer()
//{

//}

//void CLocalServer::ResetData()
//{
//    m_nPlayID		= -1;
//    m_nVolume		= 0;
//    m_nProSource	= PRO_IDLE;
//    m_nPreSource	= PRO_IDLE;
//    m_nPlayStatus	= PS_STOP;
//    m_tLatestTime	= 0;
//    m_isSyncSong	= FALSE;

//    bzero(m_szProName, sizeof(m_szProName));
//    ResetFileDateTime();
//}

//const char *CLocalServer::GetName(bool bToUTF8)
//{
//    if(bToUTF8)
//    {
//        return StringToUTF8(m_szSecName).data();
//    }

//    return m_szSecName;
//}

//void CLocalServer::SetMac(const char *lpszMac)
//{
//    if (lpszMac != NULL)
//    {
//        strcpy(m_netInfo.m_szMac, lpszMac);
//    }
//}

//void CLocalServer::SetIP(const char *lpszIP)
//{
//    if (lpszIP != NULL)
//    {
//        strcpy(m_netInfo.m_szIP, lpszIP);
//    }
//}

//void CLocalServer::SetProSource(ProgramSource src)
//{
//    m_nPreSource = m_nProSource;
//    m_nProSource = src;

//    if (src == PRO_OFFLINE)
//    {
//        m_nPreSource = PRO_OFFLINE;
//    }
//}

//CMyString CLocalServer::GetProSourceName()
//{
//    return CProtocol::GetDescriptionProSource(m_nProSource);
//}

//void CLocalServer::SetProName(char *lpszProName)
//{
//    if(strlen(lpszProName) >= SRC_NAME_LEN) // 节目源名称过长，截断
//    {
//        lpszProName[SRC_NAME_LEN-1] = '\0';
//    }

//    strcpy(m_szProName, lpszProName);
//}

//void CLocalServer::SetName(const char *szName)
//{
//    if (strlen(szName) > SEC_NAME_LEN)
//    {
//        strncpy(m_szSecName, szName, SEC_NAME_LEN);
//    }
//    else
//    {
//        strcpy(m_szSecName, szName);
//    }
//}

//bool CLocalServer::IsPagingIn()
//{
//    return (m_nProSource == PRO_PAGING);
//}

//bool CLocalServer::IsIdle()
//{
//    return (m_nProSource == PRO_IDLE);
//}

//CMyString CLocalServer::GetFileDateTime(DATETIME_FILE dt)
//{
//    return m_strFileDateTimes[dt];
//}

//void CLocalServer::SetFileDateTime(DATETIME_FILE dt, CMyString strDateTime)
//{
//    m_strFileDateTimes[dt]	= strDateTime;
//    // ? 是否需要改为1
//    m_uGetFileInfoCount[dt] = 0;
//}

//void CLocalServer::ResetFileDateTime()
//{
//    for (int i=0; i<DT_COUNT; ++i)
//    {
//        m_strFileDateTimes[i]	= ("");
//        m_uGetFileInfoCount[i]	= 0;
//    }
//}

//void CLocalServer::SetSyncSong(bool isSyncSong)
//{
//    m_isSyncSong = isSyncSong;

//    // zhuyg
//    if (isSyncSong == FALSE)
//    {
//        m_nFinishedSong		= 0;
//        m_nSyncSongCount	= 0;
//    }
//}















