#ifndef SIPINFO_H
#define SIPINFO_H

#include "Global/Const.h"
#include "Network/Protocol/Protocol.h"

/************************************************************************/
/*  SIP信息                                                             */
/************************************************************************/

#define RESERVED_SIP	0x01
#define MAX_ADDR_LEN	100


#define SIP_TRANSFER_PROTOCOL_UDP 	0
#define SIP_TRANSFER_PROTOCOL_TCP 	1
#define SIP_TRANSFER_PROTOCOL_TLS 	2

// 寻呼方式
typedef enum
{
    //PT_NULL,
    PT_TALKBACK,         // 对讲
    PT_LISTEN,              // 监听
    PT_BROADCAST      // 广播
}PageType;


class CSipInfo
{
public:
    CSipInfo();
    virtual ~CSipInfo(void);

    bool		<PERSON>et<PERSON>tatus(void);

    // 获取Web端Sip状态，如果SIP不是登录到本机sip服务器，则返回未登录状态 zhuyg
    SipStatus  GetWebStatus(void);

    SipStatus	m_nSipStaus;
    char		m_szAccount[SEC_NAME_LEN+1];	// 账号名称
    char		m_szPassword[SEC_NAME_LEN+1];	// 账号密码
    char		m_szServerAddr[MAX_ADDR_LEN];	// 服务器地址
    int			m_nServerPort;					// 服务器端口
    bool        m_bEnableSIP;                   // 是否启用SIP
    int         m_nServerProtocol;              // 传输协议
    PageType m_nPageType;                       // 寻呼方式
    unsigned    char m_nSipOutputVol;           // SIP输出音量(0~100)
    unsigned    char m_nSipMicLevel;            // mic输入音量级别(1~9)
};

#endif // SIPINFO_H
