#include "BlueTooth.h"
#include "Network/Protocol/Protocol.h"

CBlueTooth::CBlueTooth(void)
{

}


CBlueTooth::~CBlueTooth(void)
{

}


bool CBlueTooth::operator!=( const CBlueTooth& deviceBlueTooth)
{
    return ( memcmp(deviceBlueTooth.m_BlueTooth_Name,m_BlueTooth_Name,sizeof(m_BlueTooth_Name)) ||
            deviceBlueTooth.m_BlueTooth_encryption != m_BlueTooth_encryption ||
             memcmp(deviceBlueTooth.m_BlueTooth_Pin,m_BlueTooth_Pin,sizeof(m_BlueTooth_Pin))   );
}
