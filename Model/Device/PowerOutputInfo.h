#ifndef POWEROUTPUTINFO_H
#define POWEROUTPUTINFO_H

#define POWER_MODE_SAVING	0x01
#define POWER_MODE_NORMAL	0x02

class CPowerOutputInfo
{
public:
    CPowerOutputInfo(void);
    ~CPowerOutputInfo(void);

public:
    unsigned char	GetPowerModeIndex();
    unsigned char	GetPowerModeValue(int index);
    unsigned char	GetTimeoutIndex();
    unsigned short	GetTimeoutValue(int index);
    void	SetTimeountIndex(unsigned char index);

public:
    bool	m_bLoop;		// 是否有回路检测功能
    bool	m_bSigal;		// 回路检测有没有信号
    unsigned char	m_powerMode;
    unsigned short	m_timeout;
};

#endif // POWEROUTPUTINFO_H
