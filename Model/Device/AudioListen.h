#ifndef AUDIOLISTEN_H
#define AUDIOLISTEN_H

#include <iostream>
#include <vector>
#include "Tools/CMyString.h"


#define SEC_MAC_LEN			18		// 分区MAC地址长度

#define LISTENING_PARTY     1       //主动监听方
#define LISTENED_PARTY      0       //被动监听方

using std::vector;

class CAudioListen
{
public:
    CAudioListen(void);
    ~CAudioListen(void);

public:
    void SetListenParty(unsigned char party)		{ m_isListenParty = party; }
    unsigned char GetListenParty()				{ return m_isListenParty; }
    void SetAudioCoding(unsigned char coding)		{ m_audioCoding = coding; }
    unsigned char GetAudioCoding()				{ return m_audioCoding; }
    void SetEnableNat(unsigned char isEnable)		{ m_enableNat = isEnable; }
    unsigned char GetEnableNat()				{ return m_enableNat; }
    void SetAudioPort(unsigned short port)		{ m_audioPort = port; }
    unsigned short GetAudioPort()				{ return m_audioPort; }
    void SetListenStatus(unsigned char status)		{ m_listenStatus = status; }
    unsigned char GetListenStatus()				{ return m_listenStatus; }
    
    void	SetListenMac(const char*	lpszMac)        
    {
        if (lpszMac != NULL)
        {
            strcpy(m_ListenMac, lpszMac);
        }
    }
    const char*	GetListenMac(void)				{	return  m_ListenMac; }

private:
    unsigned char m_isListenParty;       //是否为主动监听方      1为主动监听方  0为被动监听方
    char	m_ListenMac[SEC_MAC_LEN];		  //记录主动监听设备或被动监听设备的MAC
    unsigned char m_audioCoding;          //语音编码
    unsigned char m_enableNat;            //开启NAT后由主机转发双方音频流
    unsigned short m_audioPort;           //关闭NAT后有效
    unsigned char m_listenStatus;           //对讲状态
};

#endif // AUDIOLISTEN_H
