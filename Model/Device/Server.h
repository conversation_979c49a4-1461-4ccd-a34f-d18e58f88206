#ifndef SERVER_H
#define SERVER_H



class CServer
{
public:
    CServer();
    ~CServer();

    void    ResetSyncStatus();
    void    SetSyncStatus(int nAllSyncSongCount, int nFinishedSongCount, int nSyncSongPercent);
    bool    IsSync() { return (m_nAllSyncSongCount == m_nFinishedSongCount);}

private:
    int				m_nID;							// 分区ID
    int				m_nPlayID;						// 播放ID	1-200

    int             m_nAllSyncSongCount;            // 一共需要同步的歌曲数目
    int             m_nFinishedSongCount;           // 已同步完成的歌曲数目
    int             m_nSyncSongPercent;             // 正在同步的歌曲进度

};





#endif // SERVER_H
