#ifndef PHONEGATEWAY_H
#define PHONEGATEWAY_H

#include <iostream>
#include <vector>
#include "Global/Const.h"
#include "Tools/CMyString.h"
#include "Tools/G722/g722_encoder.h"

#if SUPPORT_PHONE_GATEWAY

#define PHONE_GATEWAY_BROADCAST_ADDR  "230.230.231.15"
#define PHONE_GATEWAY_BROADCAST_PORT  50402

#define PHONE_GATEWAY_FMT 16

#define MAX_PHONE_GATEWAY_WHITELIST_LENGTH 256

enum
{
    AUDIO_PHONE_GATEWAY_RATE_16K		= 16000,
    AUDIO_PHONE_GATEWAY_RATE_2205K	    = 22050,
    AUDIO_PHONE_GATEWAY_RATE_32K		= 32000,
    AUDIO_PHONE_GATEWAY_RATE_441K		= 44100,
    AUDIO_PHONE_GATEWAY_RATE_48K		= 48000,

    AUDIO_PHONE_GATEWAY_RATE_COUNT	    = 5,

};

enum
{
    AUDIO_PHONE_GATEWAY_MONO_CHANNEL		= 0x01,	// 单声道
    AUDIO_PHONE_GATEWAY_STERO_CHANNEL		= 0x02,	// 双声道
};

enum
{
    AUDIO_PHONE_GATEWAY_EVENT_STOP	    = 0,
    AUDIO_PHONE_GATEWAY_EVENT_START		= 1,
    AUDIO_PHONE_GATEWAY_EVENT_RETRY	    = 2,
};

class CPhoneGateway
{
public:
    CPhoneGateway(void);
    ~CPhoneGateway(void);

public:
    unsigned int	GetSampleRate(void)			{	return m_uSampleRate;	}
    void	SetSampleRate(unsigned int sampleRate)			{	m_uSampleRate=sampleRate;}
    unsigned char	GetAlgorithm(void)			{	return m_uAlgorithm;	}
    void	SetAlgorithm(unsigned char algorithm)			{	m_uAlgorithm = algorithm;	}
    
    unsigned char GetMasterSwitch(void)         {   return m_nMasterSwitch;}
    void SetMasterSwitch(unsigned char masterSwitch)   { m_nMasterSwitch = masterSwitch;}
    
    unsigned char GetVolume(void)         {   return m_nVolume;}
    void SetVolume(unsigned char volume)   { m_nVolume = volume;}


    CMyString		GetSectionMac(int nSec)				{	return m_vecSecMac[nSec];					}
    int				GetSectionCount(void)					{	return m_vecSecMac.size();				}
    void            AddSection(CMyString strMac)        {   m_vecSecMac.push_back(strMac);              }
    bool            ClearSection(bool bAll,CMyString strMac);
    void            SetSection(vector<CMyString>& vecMac)    {   m_vecSecMac=vecMac;                    }
    int             GetSelectedSections(vector<CMyString>& vecMac);
    bool            HasSectionMac(CMyString strMac)     {   return std::count(m_vecSecMac.begin(), m_vecSecMac.end(), strMac)>0; }

#if 0
    CMyString		GetTelWhitelist(int nTel)				{	return m_vecTelWhitelist[nTel];					}
    int				GetTelWhitelistCount(void)					{	return m_vecTelWhitelist.size();				}
    void            AddTelWhitelist(CMyString telWhitelist)        {   m_vecTelWhitelist.push_back(telWhitelist);              }
    bool            ClearTelWhitelist(bool bAll,CMyString telWhitelist);
    void            SetTelWhitelist(vector<CMyString>& vecTelWhitelist)    {   m_vecTelWhitelist=vecTelWhitelist;                    }
    int             GetSelectedTelWhitelists(vector<CMyString>& vecTelWhitelist);
    bool            HasTelWhitelist(CMyString telWhitelist)     {   return std::count(m_vecTelWhitelist.begin(), m_vecTelWhitelist.end(), telWhitelist)>0; }
#endif

    string		GetTelWhitelist()				        {return m_vecTelWhitelist;}
    void        SetTelWhitelist(string telWhitelist)    {m_vecTelWhitelist = telWhitelist;}
    void        ClearTelWhitelist()                     {m_vecTelWhitelist.clear();}


    bool		IsPhoneGatewaySingalValid()                {	return b_phoneGatewaySingalValid;	}
    void		SetPhoneGatewaySingalValid(bool valid)     {	b_phoneGatewaySingalValid = valid;	}

    bool   operator==(const CPhoneGateway &phoneGateway);

    G722_ENC_CTX *Get_G722_enc_ctx(){
        if(g722_enc_ctx == NULL)
        {
            int srate=G722_SAMPLE_RATE_8000;
	        srate &= ~ G722_SAMPLE_RATE_8000;
            g722_enc_ctx = g722_encoder_new(64000, srate);
        }
        return g722_enc_ctx;
    }

private:
    unsigned int        m_uSampleRate;		// 采样率
    unsigned char		m_uAlgorithm;		// 音频编解码算法，参见3.12 音频编解码算法(目前固定为PCM）
    G722_ENC_CTX        *g722_enc_ctx;      // G722压缩编码指针
    unsigned char m_nMasterSwitch;          //主开关（0/1，默认为0关闭）
    unsigned char m_nVolume;                //混音音量
    vector<CMyString>	m_vecSecMac;        //分区MAC集合
    string  m_vecTelWhitelist;              //电话白名单(逗号隔开)
    bool                b_phoneGatewaySingalValid;   //混音器信号是否有效
};

#endif

#endif // PHONEGATEWAY_H
