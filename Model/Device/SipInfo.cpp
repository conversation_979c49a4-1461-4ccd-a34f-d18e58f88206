#include "stdafx.h"
#include "SipInfo.h"

#include <string.h>

CSipInfo::CSipInfo()
{
    m_nSipStaus		= SIP_NULL;
    m_nServerPort	= 0;
    m_nPageType = PT_TALKBACK;              // zhuyg

    memset(m_szAccount,	0, sizeof(m_szAccount));
    memset(m_szPassword,0, sizeof(m_szPassword));
    memset(m_szServerAddr,0, sizeof(m_szServerAddr));

    strcpy(m_szAccount, "100");
    strcpy(m_szPassword, "123456");
    strcpy(m_szServerAddr, g_Global.m_strVoipServerIP.data());

    m_bEnableSIP = false;       //默认关闭SIP

    m_nSipOutputVol = 80;           // SIP输出音量(0~100)
    m_nSipMicLevel = 5;            // mic输入音量级别(1~9)

    m_nServerProtocol = SIP_TRANSFER_PROTOCOL_UDP;
}

CSipInfo::~CSipInfo(void)
{

}

bool	CSipInfo::HasGetStatus(void)
{
    return	(m_nSipStaus != SIP_NULL);
}

// 获取Web端Sip状态，如果SIP不是登录到本机sip服务器，则回复未登录 zhuyg
SipStatus CSipInfo::GetWebStatus()
{
    if(strcmp(m_szServerAddr, g_Global.m_strVoipServerIP.data()) == 0)     // 判断服务器IP是否是现sip服务器IP，避免两个线路sip绑定同一号码
    {
        return m_nSipStaus;
    }

    return SIP_NULL;
}






