#include "stdafx.h"
#include "WebSection.h"
#include <string.h>


////////////////////////////////////////////////////////////////////////////////////////

CTablet::CTablet()
{
    m_TabletType = -1;
    m_strMac = "";
    m_strIP = "";
    m_strName = "";
}


CWebSection::CWebSection()
{
    m_strSockID = "";
    m_bOnline = true;
    m_bForware = false;
    m_webPaging=NULL;
    ResetSecFileDateTime();
}

CWebSection::CWebSection(string strMac, string strIP, string strName, int nType)
{
    m_bForware = false;
    m_Tablet.m_strMac = strMac;
    m_Tablet.m_strIP = strIP;
    m_Tablet.m_strName = strName;
    m_Tablet.m_TabletType = nType;

    m_strSocketID = "";
    m_WebType = WT_TABLET;
    m_bOnline = true;
    m_webPaging=NULL;
    ResetSecFileDateTime();
}


CWebSection::CWebSection(string strSocketID, WebType wt)
{
    m_strSockID = strSocketID;

    m_strSocketID = "";
    m_WebType = wt;
    m_bOnline = true;
    m_bForware = false;
    m_webPaging=NULL;
    ResetSecFileDateTime();
}


CWebSection::~CWebSection()
{
    #if SUPPORT_WEB_PAGING
    ReleaseWebPaging();
    #endif
}

#if SUPPORT_WEB_PAGING
void CWebSection::ReleaseWebPaging()
{
    QMutexLocker locker(&websection_webpaging_mutex);
    if(m_webPaging!=NULL)
    {
        delete m_webPaging;
        m_webPaging = NULL;
    }
}
#endif


bool CWebSection::IsValid()
{
    return (m_bOnline && m_strSocketID != "" && g_Global.m_Users.GetUserByID(m_strSocketID) != NULL ) ; 
}

bool CWebSection::IsSuperUser()
{
    LPCUserInfo pUser = g_Global.m_Users.GetUserByID(m_strSocketID);

    if(pUser != NULL)
    {
        if(pUser->IsSuperUser())
        {
            return TRUE;
        }
    }

    return FALSE;
}

void CWebSection::SetSelectSection(int nSelectedID, int nZoneCount, string *strZoneMac, bool isClear)
{
    if(isClear) // 是否需要清空
    {
        m_vecMac.clear();
    }

    m_SecetedID = nSelectedID;
    for(int i=0; i<nZoneCount; i++)
    {
        m_vecMac.push_back(strZoneMac[i]);
    }
}

string CWebSection::GetSecFileDateTime(string strMac, FileType ft)
{
    if(m_SectionFileTimes.count(strMac) > 0)
    {
        return m_SectionFileTimes[strMac].GetFileDateTime(ft);
    }

    return "";
}

void CWebSection::ResetSecFileDateTime()
{
    m_SectionFileTimes.clear();
}

bool CWebSection::NeedUpdateFile(string strMac, FileType ft, string strDateTime)
{
    if(m_SectionFileTimes.count(strMac) > 0)
    {
        //         LPCSection pSection = g_Global.m_Sections.GetSectionByMac(strMac.data());
        //         if(pSection != NULL && pSection->GetFileDateTime((DATETIME_FILE)(ft - 1)) == DEFAULT_DATETIME)
        //         {
        //             //LOG("pSection FileTime is DEFAULT_DATETIME", LV_INFO);
        //             return true;
        //         }

        //printf("FileDateTime : %s\n", strDateTime.data());
        CFileDateTime dateTime = m_SectionFileTimes[strMac];
        return (dateTime.GetFileDateTime(ft) == "" || dateTime.GetFileDateTime(ft) != strDateTime);
    }

    return true;
}

void CWebSection::SetSecFileDateTime(string strMac, FileType ft, string strDateTime)
{
    if(m_SectionFileTimes.count(strMac) > 0)
    {
        m_SectionFileTimes[strMac].SetFileDateTime(ft, strDateTime);
    }
    else
    {
        CFileDateTime dateTime;
        dateTime.SetFileDateTime(ft, strDateTime);
        m_SectionFileTimes[strMac] = dateTime;
    }

}


////////////////////////////////////////////////////////////


CWebSections::CWebSections()
{

}

CWebSections::~CWebSections()
{

}

void CWebSections::AddWebSection(CWebSection &webSection)
{
    QMutexLocker locker(&websections_mutex);
    m_IDSocket[webSection.GetSocketID()] = &webSection;
}

void CWebSections::RemoveWebSectionBySockID(string strSockID)
{
    QMutexLocker locker(&websections_mutex);
    #if 1
    map<string, LPCWebSection>::iterator iter =  m_IDSocket.begin();
    for(; iter!=m_IDSocket.end(); iter++)
    {
        string strUserID = iter->first;
        LPCWebSection pWebSection = iter->second;

        if(pWebSection && pWebSection->m_strSockID == strSockID)
        {
            printf("delete pWebSection...\n");
            delete(pWebSection);
            break;
        }
    }
    #endif
    m_IDSocket.erase(strSockID);
}

LPCWebSection CWebSections::GetWebSection(int nIndex)
{
    QMutexLocker locker(&websections_mutex);
    map<string, LPCWebSection>::iterator iter =  m_IDSocket.begin();
    int n = 0;
    for(; iter!=m_IDSocket.end(); iter++)
    {
        if(nIndex == n++)
        {
            return (iter->second);
        }
    }
    return NULL;
}


LPCWebSection   CWebSections::GetWebSectionBySocketID(string strSocketID)
{
    QMutexLocker locker(&websections_mutex);
    
    #if 0
    map<string, LPCWebSection>::iterator iter =  m_IDSocket.begin();
    for(; iter!=m_IDSocket.end(); iter++)
    {
        CWebSection* webSec = iter->second;

        if(webSec->m_strSockID == strSocketID)
        {
            return webSec;
        }
    }
    #endif

    if(m_IDSocket.count(strSocketID) > 0)
    {
        return m_IDSocket[strSocketID];
    }
    return NULL;
}


LPCWebSection CWebSections::GetWebByMac(string strMac)
{
    QMutexLocker locker(&websections_mutex);
    map<string, LPCWebSection>::iterator iter =  m_IDSocket.begin();
    for(; iter!=m_IDSocket.end(); iter++)
    {
        CWebSection* webSec = iter->second;
        if(webSec->GetWebType() != WT_TABLET)
        {
            continue;
        }

        if(webSec->m_Tablet.m_strMac == strMac)
            return webSec;
    }

    return NULL;
}

LPCWebSection CWebSections::GetWebByIP(string strIP)
{
    QMutexLocker locker(&websections_mutex);
    if(m_IPTablets.count(strIP) > 0)
    {
        return m_IPTablets[strIP];
    }

    return NULL;
}

void CWebSections::SetSocketSecFileDateTime(string strUID, string strMac, FileType ft, string strDateTime)
{
    QMutexLocker locker(&websections_mutex);
    if(m_IDSocket.count(strUID) > 0)
    {
        m_IDSocket[strUID]->SetSecFileDateTime(strMac, ft, strDateTime);
    }

}




CFileDateTime::CFileDateTime()
{
    for(int i=0; i<DT_COUNT; i++)
    {
        m_strFileDateTimes[i] = "";
    }
}

string CFileDateTime::GetFileDateTime(FileType ft)
{
    DATETIME_FILE dt = (DATETIME_FILE)(ft - 1);
    return m_strFileDateTimes[dt];
}

void CFileDateTime::SetFileDateTime(FileType ft, string strDateTime)
{
    DATETIME_FILE dt = (DATETIME_FILE)(ft - 1);

    m_strFileDateTimes[dt] = strDateTime;
}
