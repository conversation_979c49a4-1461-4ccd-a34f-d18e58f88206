#ifndef AUDIOCALL_H
#define AUDIOCALL_H

#include <iostream>
#include <vector>
#include "Tools/CMyString.h"


#define SEC_MAC_LEN					18			// 分区MAC地址长度

#define CALLING_PARTY       1       //主叫方
#define CALLED_PARTY        0       //被叫方

using std::vector;

enum
{
	CALL_STATUS_FREE,						//空闲
	CALL_STATUS_WAIT_CALLED_ANSWER,			//被叫方响铃中
	CALL_STATUS_NO_RESPONSE,				//无人应答
	CALL_STATUS_BUSY,						//被叫方繁忙，无法接听（如寻呼）
	CALL_STATUS_REJECT,						//被叫方拒绝接听（手动挂断）	0x04
	CALL_STATUS_CODECES_NOT_SUPPORT,		//不支持的语音编码	0x05
	CALL_STATUS_CONNECT,					//被叫方已接听	0x06
	CALL_STATUS_HANGUP						//任意一方已挂断	0x07
};

typedef struct
{
    char Key1_mac[SEC_MAC_LEN];     //Key1对应的设备Mac
    char Key2_mac[SEC_MAC_LEN];     //Key2对应的设备Mac
    int AutoAnswerTime;            //自动应答时间(单位:秒)
    unsigned char micVol;          //对讲mic输入音量(1~9)
    unsigned char farOutVol;       //对讲远端输出音量(1~9)
}CALLDEVICECONFIG;

class CAudioCall
{
public:
    CAudioCall(void);
    ~CAudioCall(void);

public:
    void SetCallParty(unsigned char party)		{ m_isCallingParty = party; }
    unsigned char GetCallParty()				{ return m_isCallingParty; }
    void SetAudioCoding(unsigned char coding)		{ m_audioCoding = coding; }
    unsigned char GetAudioCoding()				{ return m_audioCoding; }
    void SetEnableNat(unsigned char isEnable)		{ m_enableNat = isEnable; }
    unsigned char GetEnableNat()				{ return m_enableNat; }
    void SetAudioPort(unsigned short port)		{ m_audioPort = port; }
    unsigned short GetAudioPort()				{ return m_audioPort; }
    void SetCallStatus(unsigned char status)		{ m_callStatus = status; }
    unsigned char GetCallStatus()				{ return m_callStatus; }
    
    void	SetCallMac(const char*	lpszMac)        
    {
        if (lpszMac != NULL)
        {
            strcpy(m_callMac, lpszMac);
        }
    }
    const char*	GetCallMac(void)				{	return  m_callMac; }

    CALLDEVICECONFIG  m_stCallDeviceConfig;
private:
    unsigned char m_isCallingParty;       //是否为主叫方      1为主叫方  0为被叫方
    char	m_callMac[SEC_MAC_LEN];		  //记录主叫方或被叫方的MAC
    unsigned char m_audioCoding;          //语音编码
    unsigned char m_enableNat;            //开启NAT后由主机转发双方音频流
    unsigned short m_audioPort;           //关闭NAT后有效
    unsigned char m_callStatus;           //对讲状态
};

#endif // AUDIOCALL_H
