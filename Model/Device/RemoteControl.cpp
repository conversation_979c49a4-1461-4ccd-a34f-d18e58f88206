#include "RemoteControl.h"
#include <strings.h>
#include "Global/CType.h"
#include "stdafx.h"

CRemoteControlTask::CRemoteControlTask(int nID)
{
    m_nID		  = nID;
    m_strTaskName = "";
    m_SourceType  = TIMER_SOURCE_TYPE_LOCAL_SONG;
    m_nPlayMode = PM_ORDER;
    m_nVolume = 0;
    memset(&m_stAudioCollector,0,sizeof(m_stAudioCollector));
}


CRemoteControlTask::~CRemoteControlTask(void)
{
   
}

int   CRemoteControlTask::GetStartSong()
{
    int	nSongCount	= GetSongCount();

    if (nSongCount <= 0)
    {
        return -1;
    }

    // 随机模式
    if (m_nPlayMode == PM_RANDOM)
    {
        //srand((unsigned int)time(NULL));
        struct timespec spec;
        clock_gettime(CLOCK_MONOTONIC, &spec);
        unsigned long long timeTv = (unsigned long long)spec.tv_sec * 1000000 + (unsigned long long)spec.tv_nsec / 1000;
        srand((unsigned int)(timeTv & 0xFFFFFFFF));  // 使用低32位作为种子

        return  (rand()%nSongCount);
    }
    // 其它模式从第一首歌曲开始
    else
    {
        return 0;
    }
}

int  CRemoteControlTask::GetPreSong(int nSong,int nPlayMode)
{
    if (nSong < 0 || nSong >= GetSongCount())
    {
        return -1;
    }

    int nNextSong	= nSong;
    int	nSongCount	= GetSongCount();

    // 顺序和列表循环
    if (nPlayMode == PM_ORDER || nPlayMode == PM_LIST_CYCLE)
    {
        --nNextSong;

        if (nNextSong <0 )
        {
            nNextSong = nSongCount-1;
        }
    }
    // 随机播放
    else if (nPlayMode == PM_RANDOM)
    {
        //srand((unsigned int)time(NULL));
        struct timespec spec;
        clock_gettime(CLOCK_MONOTONIC, &spec);
        unsigned long long timeTv = (unsigned long long)spec.tv_sec * 1000000 + (unsigned long long)spec.tv_nsec / 1000;
        srand((unsigned int)(timeTv & 0xFFFFFFFF));  // 使用低32位作为种子

        nNextSong = rand()%nSongCount;
    }

    // 如果找到下一首歌曲
    if (nNextSong >= 0)
    {
        // 则需要检查
    }

    return nNextSong;
}

int  CRemoteControlTask::GetNextSong(int nSong,int nPlayMode)
{
    if (nSong < 0 || nSong >= GetSongCount())
    {
        return -1;
    }

    int nNextSong	= nSong;
    int	nSongCount	= GetSongCount();

    // 顺序和列表循环
    if (nPlayMode == PM_ORDER || nPlayMode == PM_LIST_CYCLE)
    {
        ++nNextSong;

        if (nNextSong >= nSongCount)
        {
            nNextSong = (nPlayMode == PM_ORDER ? -1 : 0);
        }
    }
    // 随机播放
    else if (nPlayMode == PM_RANDOM)
    {
        //srand((unsigned int)time(NULL));
        struct timespec spec;
        clock_gettime(CLOCK_MONOTONIC, &spec);
        unsigned long long timeTv = (unsigned long long)spec.tv_sec * 1000000 + (unsigned long long)spec.tv_nsec / 1000;
        srand((unsigned int)(timeTv & 0xFFFFFFFF));  // 使用低32位作为种子

        nNextSong = rand()%nSongCount;
    }

    // 如果找到下一首歌曲
    if (nNextSong >= 0)
    {
        // 则需要检查
    }

    return nNextSong;
}


int CRemoteControlTask::GetSelectedSections(vector<CMyString>& vecMac)
{
    vecMac.clear();

    for (unsigned int i=0; i<m_vecSecMac.size(); ++i)
    {
        vecMac.push_back(m_vecSecMac[i]);
    }

    // 遥控任务内所有的分组
    for (unsigned int i=0; i<m_vecGroupID.size(); ++i)
    {
        int index = 0;
        CGroup* pGroup = g_Global.m_Groups.FindGroupByID(m_vecGroupID[i], index);

        if (pGroup != NULL)
        {
            // 轮询分组中的分区
            for (int j=0; j<pGroup->GetSecCount(); ++j)
            {
                bool bFind = FALSE;
                for (unsigned int k=0; k<vecMac.size(); k++)
                {
                    if (pGroup->GetSecMac(j) == vecMac[k])
                    {
                        bFind = TRUE;
                        break;
                    }
                }

                // 如果之前添加的分区中没有该分区，才会加入
                if (!bFind)
                {
                    vecMac.push_back(pGroup->GetSecMac(j));
                }
            }
        }
    }

    return vecMac.size();
}


void CRemoteControlTask::AddSong(CMyString strPathName)
{
    CSong song(strPathName);
    m_vecSong.push_back(song);
}


bool CRemoteControlTask::ClearGroup(bool bAll,CMyString strGroupID)
{
    bool bUpdate=false;
    if(bAll)
    {
        if(m_vecGroupID.size()>0)
        {
            m_vecGroupID.clear();
            bUpdate=true;
        }
    }
    else
    {
        vector<CMyString>::iterator iter;
        for(iter=m_vecGroupID.begin();iter!=m_vecGroupID.end();++iter)
        {
            if (strGroupID == *iter)
            {
                m_vecGroupID.erase(iter);
                bUpdate = true;
                break;
            }
        }
    }
    return bUpdate;
}

bool CRemoteControlTask::ClearSection(bool bAll,CMyString strMac)
{
    bool bUpdate=false;
    if(bAll)
    {
        if(m_vecSecMac.size()>0)
        {
            m_vecSecMac.clear();
            bUpdate=true;
        }
    }
    else
    {
        vector<CMyString>::iterator iter;
        for(iter=m_vecSecMac.begin();iter!=m_vecSecMac.end();++iter)
        {
            if (strMac == *iter)
            {
                m_vecSecMac.erase(iter);
                bUpdate = true;
                break;
            }
        }
    }
    return bUpdate;
}

bool CRemoteControlTask::ClearSong(bool bAll,CMyString strPathName)
{
    bool bUpdate=false;
    if(bAll)
    {
        if(m_vecSong.size()>0)
        {
            m_vecSong.clear();
            bUpdate=true;
        }
    }
    else
    {
        vector<CSong>::iterator iter;
        for(iter=m_vecSong.begin();iter!=m_vecSong.end();)
        {
            if (strPathName == (*iter).GetPathName())
            {
                m_vecSong.erase(iter);
                bUpdate = true;
            }
            else
            {
                iter++;
            }
        }
    }
    return bUpdate;
}


bool CRemoteControlTask::CheckParamCorrect()
{
    //判断是否存在有效分区
    vector<CMyString> vecMac;
    GetSelectedSections(vecMac);
    if(vecMac.size() == 0)
    {
        return false;
    }
    if(GetSourceType() != TIMER_SOURCE_TYPE_LOCAL_SONG && GetSourceType() != TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
    {
        return false;
    }
    if(GetSourceType() == TIMER_SOURCE_TYPE_LOCAL_SONG)
    {
            //判断是否存在歌曲
        if(m_vecSong.size() == 0)
        {
            return false;
        }
    }
    else if(GetSourceType() == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
    {
        //判断采集器参数是否正常
        if( strlen(m_stAudioCollector.mac) ==0 || !(m_stAudioCollector.channelId>=1 && m_stAudioCollector.channelId<=4) )
        {
            return false;
        }
    }

    return true;
}

CRemoteControl::CRemoteControl()
{
    memset(m_keyEventArray,0,sizeof(m_keyEventArray));
    #if 0   //默认不创建任务
    //创建播放任务
    for(int i=0;i<REMOTE_CONTROL_MAX_PLAY_TASK;i++)
    {
        CRemoteControlTask task(i+1);
        m_vecPlayTask.push_back(task);
    }
    #endif

}

CRemoteControl::~CRemoteControl(void)
{

}

// 重新赋值远程遥控器的ID
void CRemoteControl::DessignTaskID(void)
{
    int nTaskCount = GetTaskCnt();

    for (int i=0; i<nTaskCount; ++i)
    {
        GetTaskDetail(i+1).SetTaskID(i+1);
    }
}

bool CRemoteControl::AddTask(CRemoteControlTask& task)
{
    int nTaskCnt = GetTaskCnt();
    if(nTaskCnt>=REMOTE_CONTROL_MAX_PLAY_TASK)
        return false;
    
    task.SetTaskID(nTaskCnt+1); //预留，避免外面传递错误参数
    m_vecPlayTask.push_back(task);
    return true;
}

bool CRemoteControl::SetTask(CRemoteControlTask& task)
{
    int nTaskCnt = GetTaskCnt();
    //传进来的taskId比当前任务数还多，代表有错误
    if(task.GetTaskID()>nTaskCnt)
        return false;
    
    CRemoteControlTask& curTask=GetTaskDetail(task.GetTaskID());

    curTask.SetTaskID(task.GetTaskID());
    curTask.SetTaskName(task.GetTaskName());
    curTask.SetPlayMode(task.GetPlayMode());
    curTask.SetVolume(task.GetVolume());

    curTask.ClearSection(true,"");
    for(int i=0;i<task.GetZoneCount();i++)
    {
        curTask.AddSection(task.GetZoneMac(i));
    }

    curTask.ClearGroup(true,"");
    for(int i=0;i<task.GetGroupCount();i++)
    {
        curTask.AddGroup(task.GetGroupID(i));
    }

    curTask.SetSourceType(task.GetSourceType());

    curTask.ClearSong(true,"");
    if(curTask.GetSourceType() == TIMER_SOURCE_TYPE_LOCAL_SONG)
    {
        for(int i=0;i<task.GetSongCount();i++)
        {
            curTask.AddSong(task.GetSong(i).GetPathName());
        }

        ST_TIMER_AUDIO_COLLECTOR_INFO st_audioCollctor;
        memset(&st_audioCollctor,0,sizeof(ST_TIMER_AUDIO_COLLECTOR_INFO));
        curTask.SetAudioCollector(st_audioCollctor);
    }
    else if(curTask.GetSourceType() == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
    {
        curTask.SetAudioCollector(task.GetAudioCollector());
    }

    return true;
}

void CRemoteControl::RemoveTask(int taskID)
{   
    vector<CRemoteControlTask>::iterator iter;
    int i = 0;
    for(i=0, iter=m_vecPlayTask.begin(); iter!=m_vecPlayTask.end(); ++i, ++iter)
    {
        if(taskID == iter->GetTaskID())
        {
            m_vecPlayTask.erase(iter);
            break;	// 务必加上break
        }
    }
    DessignTaskID();
}

void CRemoteControl::ClearTasks(void)
{
    m_vecPlayTask.clear();
}



int CRemoteControl::GetAllTaskSelectedSections(vector<CMyString>& vecMac)
{
    vecMac.clear();

    vector<CRemoteControlTask>::iterator iter;
    for(iter=m_vecPlayTask.begin(); iter!=m_vecPlayTask.end(); ++iter)
    {
        for (unsigned int i=0; i<iter->GetZoneCount(); ++i)
        {
            bool bFind = FALSE;
            for (unsigned int k=0; k<vecMac.size(); k++)
            {
                if (iter->GetZoneMac(i) == vecMac[k])
                {
                    bFind = TRUE;
                    break;
                }
            }
            // 如果之前添加的分区中没有该分区，才会加入
            if (!bFind)
            {
                vecMac.push_back(iter->GetZoneMac(i));
            }
        }

        // 遥控任务内所有的分组
        for (unsigned int i=0; i<iter->GetGroupCount(); ++i)
        {
            int index = 0;
            CGroup* pGroup = g_Global.m_Groups.FindGroupByID(iter->GetGroupID(i), index);

            if (pGroup != NULL)
            {
                // 轮询分组中的分区
                for (int j=0; j<pGroup->GetSecCount(); ++j)
                {
                    bool bFind = FALSE;
                    for (unsigned int k=0; k<vecMac.size(); k++)
                    {
                        if (pGroup->GetSecMac(j) == vecMac[k])
                        {
                            bFind = TRUE;
                            break;
                        }
                    }

                    // 如果之前添加的分区中没有该分区，才会加入
                    if (!bFind)
                    {
                        vecMac.push_back(pGroup->GetSecMac(j));
                    }
                }
            }
        }  
    }

    return vecMac.size();
}
