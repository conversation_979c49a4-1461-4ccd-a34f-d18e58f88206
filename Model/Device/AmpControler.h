#ifndef AMPCONTROLER_H
#define AMPCON<PERSON><PERSON>ER_H

#include <iostream>
#include <vector>
#include "Global/Const.h"
#include "Tools/CMyString.h"

#if SUPPORT_AMP_CONTROLER

#define MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM    5
#define MAX_AMP_CONTROLER_BACKUP_CHANNEL_NUM    1

enum{
    AMP_CONTROLER_CHANNEL_STATUS_IDLE = 0,      //空闲
    AMP_CONTROLER_CHANNEL_STATUS_NORMAL = 1,    //正常
    AMP_CONTROLER_CHANNEL_STATUS_FAULT = 2,     //故障
    AMP_CONTROLER_CHANNEL_STATUS_WIRING = 3,    //接线错误
};

class CAmpControler
{
public:
    CAmpControler(void);
    ~CAmpControler(void);

    bool   operator==(const CAmpControler &ampControler);

    unsigned char masterChannelStatusArray[MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM];
    unsigned char backupChannelStatus;
    unsigned char backupChannelId;
};

#endif

#endif // PHONEGATEWAY_H
