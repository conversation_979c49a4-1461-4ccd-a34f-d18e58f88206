#ifndef SECTION_H
#define SECTION_H

#include <iostream>
#include <map>
#include "Network/Protocol/Protocol.h"
#include "SipInfo.h"
#include "Model/Other/LogFile.h"
#include "NetworkInfo.h"
#include "PowerOutputInfo.h"
#include "FireCollector.h"
#include "AudioCollector.h"
#include "SequencePower.h"
#include "StreamingGateway.h"
#include "DeviceEQ.h"
#include "BlueTooth.h"
#include "DeviceMixing.h"
#include "AudioForward.h"
#include "Model/Other/PlayList.h"
#include "Tools/tools.h"
#include "Model/Other/SelectedSections.h"
#include "Network/Monitor/MonProc.h"
#include "Server.h"
#include "PlayedRecently.h"
#include "Network/Kcp/kcp.h"
#if SUPPORT_PAGER_CALL
#include "AudioCall.h"
#endif
#if SUPPORT_LISTEN_FUNCTION
#include "AudioListen.h"
#endif
#if SUPPORT_REMOTE_CONTROLER
#include "Model/Device/RemoteControl.h"
#endif
#if SUPPORT_AUDIO_MIXER
#include "Model/Device/AudioMixer.h"
#endif
#if SUPPORT_PHONE_GATEWAY
#include "Model/Device/PhoneGateway.h"
#endif
#if SUPPORT_AMP_CONTROLER
#include "Model/Device/AmpControler.h"
#endif
#if SUPPORT_NOISE_DETECTOR
#include "Model/Device/NoiseDetector.h"
#endif

#include "Network/Web/RadioWebSocketClient.h"

#include "Model/Device/InformationPublish.h"

#include <QMutex>
#include <QMutexLocker>
#include <shared_mutex>
#include <memory>

typedef enum
{
    DT_GROUP			= 0,	// 分组文件
    DT_PLAYLIST,				// 播放列表文件
    DT_TIMER,					// 定时文件
    DT_SECTION,					// 分区文件
    DT_AUDIO_COLLECTOR,			// 音频采集器文件
    DT_FIRE_COLLECTOR,			// 消防采集器文件
    DT_USER =8,                 // 用户文件
    DT_SEQUENCE_POWER,          // 电源时序器文件
    DT_PAGER,			        // 寻呼台文件
    DT_COUNT,					// 文件个数

}DATETIME_FILE;

#define GET_FILE_INFO_COUNT		10


typedef enum
{
	DF_BLUETOOTH		=   0X00000001,	// 蓝牙设备
    DF_LOCAL_MONITOR	=   0X00000002, // 本地监听设备
    DF_CALL             =   0X00000004, // 对讲
    DF_VIDEO            =   0x00000008, // 可视
    DF_SIP            	=   0x00000010,  // SIP
    DF_INFORMATION_PUBLISH =   0x00000020, // 信息发布
}Device_Feature;

//设备拓展特性，包含消防采集C、电源时序器C、远程遥控器C、音频采集器C（如果P030主板，则默认支持，无需启用）、音频混音器C
typedef enum
{
	DF_EXTRA_FIRE_COLLECTOR	  =   0X00000001,	// 消防采集器
    DF_EXTRA_POWER_SEQUENCE   =   0X00000002, 	// 电源时序器
    DF_EXTRA_REMOTE_CONTROLER =   0X00000004, 	// 远程遥控器 
	DF_EXTRA_AUDIO_COLLECTOR  =   0x00000008, 	// 音频采集器（P030主板默认支持，无需启用）
	DF_EXTRA_AUDIO_MIXER      =   0x00000010, 	// 音频混音器
    DF_EXTRA_GPS_SYNCHRONIZER =   0x00000020, 	// GPS校时器
}Device_ExtraFeature;

/************************************************************************/
/* 分区信息类                                                           */
/************************************************************************/


class CSection
{
public:
    CSection(int				nSecID,
             DeviceModel		nModel		= MODEL_IP_SPEAKER_A,
             const char*		szMac		= NULL,
             const char*		szIP		= NULL,
             int				nVol		= 0,
             ProgramSource      src			= PRO_OFFLINE,
             const char*		szName		= NULL);
    virtual ~CSection(void);
    void	ResetData();
    void	ClearData();

public:
    void		SetID(int nSecID)			{	m_nID = nSecID;						}
    int			GetID(void)					{	return m_nID;						}
    int			GetPlayID(void)				{	return m_nPlayID;					}
    void		SetPlayID(int playID)       {   m_nPlayID = playID;                 }
    int			GetPrePlayID(void)			{	return m_nPrePlayID;				}
    void		SetPrePlayID(int playID)	{	m_nPrePlayID = playID;				}
    void		SetReservedWord(unsigned char nRer)	{	m_nReservedWord	= nRer;				}
    WorkPattern	GetWorkPattern(void)		{	return WorkPattern(m_nReservedWord>>2&0x03);}
    Audiocast	GetAudicast(void)			{	return Audiocast(m_nReservedWord>>6&0x03);  }
    NetworkMode GetNetworkMode(void)        {   return NetworkMode(m_nReservedWord>>4&0x03);}

    void        SetNetworkMode(NetworkMode networkMode,string tcpServerIP,int tcpServerPort,string tcpServerIP2,int tcpServerPort2);
    string      GetTcpServerIP()            {   return m_tcpServerIP;}
    int         GetTcpServerPort()          {   return m_tcpServerPort;}

    string      GetTcpBackupServerIP()      {   return m_tcpServerIP2;}
    int         GetTcpBackupServerPort()    {   return m_tcpServerPort2;}

    void		SetVolume(int	nVol)		{	m_nVolume = nVol;					}
    int			GetVolume(void)				{	return m_nVolume;					}
    void		SetSubVolume(int	nVol)	{	m_nSubVolume = nVol;				}
    int			GetSubVolume(void)			{	return m_nSubVolume;				}
    void		SetAuxVolume(int	nVol)	{	m_nAuxVolume = nVol;				}
    int			GetAuxVolume(void)			{	return m_nAuxVolume;				}
    const char*	GetName()                   {	return m_szSecName;					}
    string      GetUTFName();
    PlayStatus	GetPlayStatus(void)			{	return	m_nPlayStatus;				}
    void		SetPlayStatus(PlayStatus st){	m_nPlayStatus = st;					}
    BYTE		GetDeviceFeature(void)      {	return  m_nDeviceFeature;			}
    void		SetDeviceFeature(BYTE f)    {	m_nDeviceFeature = f;				}
    int		    GetDeviceExtraFeature(void)      {	return  m_nDeviceExtraFeature;			}
    void		SetDeviceExtraFeature(int f)    {	m_nDeviceExtraFeature = f;				}
    bool        IsDeviceSupportExtraFeature()   {return (m_nDeviceExtraFeature>=0); }
    DeviceModel	GetDeviceModel(void)		{	return	m_nDeviceModel;				}
    void		SetDeviceModel(DeviceModel dm){	m_nDeviceModel = dm;				}
    bool		IsTimerInvalid(void)		{	return m_bTimerInvalid;				}
    void		SetTimerInvalid(bool bInvalid){	m_bTimerInvalid = bInvalid;			}
    ctime_t		GetLatestTime()				{	return m_tLatestTime;				}
    void		SetLatestTime(ctime_t t)	{	m_tLatestTime = t;					}
    bool		IsRestarting(ctime_t tNow)	{	return (tNow - m_tRestartTime < REBOOT_DURATION); }
    void		SetRestartTime(ctime_t t)	{	m_tRestartTime = t;					}
    time_t		GetIdleTime(void)			{	return m_tIdleTime;					}
    bool		GetUpgrading(void)   		{	return m_isUpgrading;				}
    void		SetUpgrading(bool	isUp);   // {	m_isUpgrading	= isUp;				}
    const char*	GetMac(void)				{	return  m_netInfo.m_szMac;			}
    const char*	GetIP(void)					{	return  m_netInfo.m_szIP;			}
    unsigned int GetKcpConv();
    void		SetMac(const char*	lpszMac);
    void		SetIP(const char*	lpszIP);
    const char*	GetVersion(void)			{	return  m_szVersion;				}
    void		SetVersion(const char*	lpszVersion);
    bool        IsListening(void)           {   return  m_isListening;              }
    void        SetListening(bool isListen) {   m_isListening = isListen;           }
    const char* GetMonitorMac()             {   return m_szMonitorMac;              }
    void        SetMonitorMac(const char* szMonitorMac);
    int         GetDevType();          // 获取设备分类

    void        SetIsExistLocalSong(bool isExist)   { b_existLocalSong = isExist;   }
    bool        GetIsExistLocalSong()               { return b_existLocalSong;      }


    void        SetNetRadioSessionId(string sessionId) { m_strNetRadioSessionId = sessionId; }
    string      GetNetRadioSessionId()                { return m_strNetRadioSessionId; }

    // listen  zhuyg
    int       GetListenCount();
    void      AddListenSec(string strMac);
    void      DelListenSec(string strMac);
    string    GetListenSec(int index);
    //void    CleanListenSec();
    void      ClearListenSec();

    // 节目源
    ProgramSource	GetProSource(void)		{	return m_nProSource;				}
    ProgramSource	GetPreSource(void)		{	return m_nPreSource;				}
    void		SetProSource(ProgramSource src, bool bSetRecentSrc = TRUE);
    bool		NeedResumeAudioCollector(ProgramSource newSrc);

    // 节目源名称
    CMyString	GetProSourceName(void);

    // 节目名称（歌曲名称）
    const char*	GetProName(void)          {	return  m_szProName;                    }
    void		SetProName(char*	lpszProName);

    // 分区名称（音频采集器，为节目源自定义名称）
    void		SetName(const char*	szName);

    void		SetDateTimeFile(FileType ft);

    CMyString	GetCurDateTime()          { return m_strCurDateTime;                }
    void		SetCurDateTime(CMyString strDateTime){	m_strCurDateTime = strDateTime;	}
    bool		IsConnectedGPS()                     {	return m_bConnectedGPS;	}
    void		SetConnectedGPS(bool bConnected)     {	m_bConnectedGPS = bConnected;	}

    void		SetCheck(bool isChecked)             {	m_isChecked = isChecked;		}
    bool		GetCheck(void);

    bool		IsOnline(bool bLocal = FALSE);
    bool		IsPagingIn(void);
    bool        IsMixedIn(void);
    bool        IsPhoneGatewayIn(void);
    bool        IsSipCall(void);
    bool        IsApiTTsOrMusic(void);
    bool        IsNetRadio(void);
    bool		IsIdle(void);

    bool        IsLocal(void);
    bool		IsSipDevice(void);
    bool		IsBlueToothDevice(void);
    bool		IsLocalMonitorDevice(void);
    bool		IsSupportCallDevice(void);
    bool        IsSupportVideoDevice(void);
    bool        IsSupportSipDevice(void);
    bool        IsSupportInformationPublishDevice(void);
    bool		IsSectionDevice(void);
    bool		IsControlDevice(void);
    bool		IsFireCollectorDevice(void);
    bool		IsAudioCollectorDevice(void);
    bool		IsPowerSequenceDevice(void);
    bool		IsAudioMixerDevice(void);
    bool        IsAudioMixerDecoderDevice(void);
    bool        IsRemoteControlerDevice(void);
    bool		IsPhoneGatewayDevice(void);
    bool		IsAmpControlerDevice(void);
    bool        IsNoiseDetectorDevice(void);
    bool        IsServerDevice(void);
    bool		IsLoopDetectDevice(void);		// 具备回路检测功能的设备
    bool        IsUpdateFileDevice(void);       // 是否可同步文件的设备

    bool		IsTcpMode(void);
    bool		IsUdpMode(void);

    // 文件更新
    CMyString	GetFileDateTime(DATETIME_FILE dt);
    void		SetFileDateTime(DATETIME_FILE dt, CMyString strDateTime);
    void		ResetFileDateTime(void);
    bool		NeedUpdateSectionFile();			// 需要更新分区的文件
    bool		NeedUpdateFile(DATETIME_FILE dt);	// 需要更新文件（已获取）
    bool		NeedGetFileInfo(DATETIME_FILE dt);	// 需要获取文件信息（未获取）

    bool		IsSyncSong(void)				{	return m_isSyncSong;			} //是否在同步歌曲
    void		SetSyncSong(bool isSyncSong);
    int         GetFinishedSong(void)			{	return m_nFinishedSong;			}
    void		SetFinishedSong(int nCount)		{	m_nFinishedSong = nCount;		}
    int         GetSyncSongCount(void)			{	return m_nSyncSongCount;		}
    void		SetSyncSongCount(int nCount)	{	m_nSyncSongCount = nCount;		}
    bool		IsNeedUpdate(void)				{	return m_bNeedUpdate;			} //是否需要更新
    void		SetNeedUpdate(bool bNeedUpdate)	{	m_bNeedUpdate = bNeedUpdate;	}

    unsigned int GetTriggerCount(void)              {	return m_uTriggerCount;			}
    void		 SetTriggerCount(unsigned int count){	m_uTriggerCount = count;		}

    char*		GetTriggerFireColMac(void)		{	return m_szTriggerFireColMac;	}
    void		SetTriggerFireColMac(CHAR *pMac){	strcpy(m_szTriggerFireColMac, pMac);}

    EventType    GetCurTriggerEventType()                     { return m_CurTriggerEventType;                 }
    void         SetCurTriggerEventType(EventType type)       { m_CurTriggerEventType = type;                 }

    unsigned int GetFileInfoCount(DATETIME_FILE dt)                     {	return m_uGetFileInfoCount[dt];		}
    void		 SetFileInfoCount(DATETIME_FILE dt, unsigned int count)	{	m_uGetFileInfoCount[dt] = count;	}

    // 自定义列表
    void		InitPlaylistZoneFileName(void);
    CPlayList*	GetPlaylist(void);

        //账户信息
    string GetUserAccount()    { return m_strUserAccount; }
    void   SetUserAccount(string strUserAccount);

    unsigned char    GetModule4GCSQ_Rssi(void)           {return m_module4G_signal_rssi;}
    void        SetModule4GCSQ_Rssi(unsigned char rssi)       {m_module4G_signal_rssi = rssi;}
    static unsigned char  GetModule4GCSQ_LevelByRssi(unsigned char rssi);

    char*       GetModule4G_ICCID(void)                {return m_module4G_iccid;}
    void        SetModule4G_ICCID(char *iccid)          {sprintf(m_module4G_iccid,iccid);}

    #if SUPPORT_AUDIO_MIXER
    bool       GetAudioMixedSourceValid(void)                {return m_bAudioMixedSourceValid;}
    void       SetAudioMixedSourceValid(bool isValid)        {m_bAudioMixedSourceValid=isValid;}
    #endif

    #if SUPPORT_PHONE_GATEWAY
    bool       GetPhoneGatewaySourceValid(void)                {return m_bphoneGatewaySourceValid;}
    void       SetPhoneGatewaySourceValid(bool isValid)        {m_bphoneGatewaySourceValid=isValid;}
    #endif

    bool  IsExtraFeatureValid();

    bool IsSupportRadioAndSoundCardOpus();

private:
    int				m_nID;							// 分区ID
    int				m_nPlayID;						// 播放ID	1-200
    int				m_nPrePlayID;					// 之前的播放ID(为了定时播放结束后恢复歌曲播放用)
    bool            m_isAUX;                        // 是否为主机AUX状态
    bool            m_isChecked;					// 是否被选中
    DeviceModel		m_nDeviceModel;					// 设备类型
    int				m_nVolume;						// 音量
    int				m_nSubVolume;					// 子音量
    int				m_nAuxVolume;					// 本地音量
    int				m_nBmpID;						// 分区图片ID,如果是自定义图片，为0, 初始化为-1
    ProgramSource	m_nProSource;					// 节目源
    ProgramSource	m_nPreSource;					// 之前的节目源
    BYTE            m_nDeviceFeature;               // 20201216设备功能特征（bit0-蓝牙,bit1-本地监听,bit2-对讲
    int             m_nDeviceExtraFeature;         // 20240924设备额外特征（用于区分是否旧版本，此处要用int型，因为默认为-1，代表没有此项）
    PlayStatus		m_nPlayStatus;					// 播放状态
    bool			m_bTimerInvalid;				// 定时点是否无效（程控、手动）
    ctime_t			m_tLatestTime;					// 最近一次接收到命令的时间
    char			m_szSecName[SEC_NAME_LEN+1];	// 分区名称
    char			m_szProName[SRC_NAME_LEN];		// 节目名称
    char            m_szMonitorMac[SEC_MAC_LEN];    // 绑定的监控设备的MAC
    char			m_szVersion[SEC_VERSION_LEN];	// 设备版本号
    ctime_t			m_tRestartTime;					// 开始重启的时间
    bool			m_isUpgrading;					// 是否正在升级
    bool			m_isSyncSong;					// 正在同步歌曲
    int				m_nFinishedSong;				// 已完成同步的歌曲数目
    int				m_nSyncSongCount;				// 总共需要同步的歌曲数目
    unsigned char	m_nReservedWord;				// 保留字

    CMyString		m_strCurDateTime;				// 设备的日期时间
    bool			m_bConnectedGPS;				// 是否连接上GPS设备

    // 用CStringArray会出错，不知道什么原因
    CMyString		m_strFileDateTimes[DT_COUNT];	// 保存文件更新时间数组
    unsigned int	m_uGetFileInfoCount[DT_COUNT];	// 获取文件信息的计数

    unsigned int	m_uTriggerCount;				// 消防采集器的触发计数
    char			m_szTriggerFireColMac[SEC_MAC_LEN];	// 被触发时记录的消防采集器MAC

    //unsigned int   m_uEventTriggerCount;          // 监控事件触发计数
    EventType       m_CurTriggerEventType;          // 当前触发事件类型
    bool			m_bNeedUpdate;                  // 分区需要更新的标志
    bool            m_isListening;                  // 是否正在监听（新增监听）
    vector<string>  m_vecListenSec;                 // 监听该分区的设备Mac列表
    time_t			m_tIdleTime;

    string          m_strUserAccount;              // 控制设备当前登录的账户信息

    string          m_tcpServerIP;                  // TCP网络模式下的服务器IP
    int             m_tcpServerPort;                // TCP网络模式下的服务器端口
    string          m_tcpServerIP2;                 // TCP网络模式下的备用服务器IP
    int             m_tcpServerPort2;               // TCP网络模式下的备用服务器端口
    
    //********      新增4g信号质量、iccid卡号
    unsigned char   m_module4G_signal_rssi;         // 4G模块信号质量
    char            m_module4G_iccid[32];           // 4G模块iccid卡号,20位数字

public:
    CSipInfo			m_SipInfo;					// SIP信息
    CLogFiles			m_LogFiles;					// 日志文件信息
    CNetworkInfo		m_netInfo;					// 网络信息
    CDeviceEQ			m_DeviceEQ;					// EQ信息
    CBlueTooth			m_Bluetooth;				// 蓝牙参数
    CDeviceMixing		m_DeviceMixing;				// 混音
    unsigned short		m_uAudioPort;				// 转发音频的端口
    CPlayedRecently     m_PlayedRecently;           // 最近播放

    //CFireCollector*		m_pFireCollector;			// 消防采集器
    std::shared_ptr<CFireCollector> m_pFireCollector;   // 消防采集器
    std::shared_ptr<CAudioCollector> m_pAudioCollector; // 音频采集器
    std::shared_ptr<CSequencePower> m_pSequencePower; // 电源时序器
#if SUPPORT_REMOTE_CONTROLER
    std::shared_ptr<CRemoteControl> m_pRemoteControler; // 远程遥控器
#endif
#if SUPPORT_AUDIO_MIXER
    std::shared_ptr<CAudioMixer> m_pAudioMixer; // 音频混音器
    bool                m_bAudioMixedSourceValid;   // 音频混音音源是否有效（对于解码终端）
#endif

#if SUPPORT_PHONE_GATEWAY
    std::shared_ptr<CPhoneGateway> m_pPhoneGateway; // 音频混音器
    bool                m_bphoneGatewaySourceValid;   // 电话网关是否有效（对于解码终端）
#endif

#if SUPPORT_AMP_CONTROLER
    std::shared_ptr<CAmpControler> m_pAmpControler; // 功放控制器
#endif

#if SUPPORT_NOISE_DETECTOR
    std::shared_ptr<CNoiseDetector> m_pNoiseDetector; // 噪声检测器
    unsigned char       m_nNoiseDetectorSectionVolume;     // 噪声检测器的分区音量值
    ctime_t             m_tNoiseDetectorLastTime;         // 噪声检测器的分区音量值更新时间
#endif

    std::shared_ptr<CSelectedSections> m_pSelectedSections; // 选中分区（分控设备有）
    std::shared_ptr<CAudioForward> m_pAudioForward; // 音频转发
 #if SUPPORT_PAGER_CALL
    std::shared_ptr<CAudioCall> m_pAduioCall;       // 语音对讲
#endif
 #if SUPPORT_LISTEN_FUNCTION
    CAudioListen*         m_pAduioListen;               // 语音对讲
#endif

    CInformationPublish   m_pInformationPublish;        // 信息发布

    //CServer*            m_pServer;                  // 分控服务器信息

    // TCP socket 保留，待修改 zhuyg
    LPS_SOCKET_OBJ		m_pSocketObj;				// 保存的socket对象（用于TCP）
    CKCPSocket*         m_kcpsocket;                // UDP-KCP(用于TCP优先模式)

    CPlayList			m_PlaylistZone;				// 每个分区可以有一个播放列表
    CPlayList*			m_pPlaylistGroup;			// 指向组的播放列表
    CMyString			m_strMemory;				// 内存容量信息
    bool                m_bSupportSyncFile;

    string              m_strSocketID;              // dsp9312 connect socket id
    int                 m_nStatusSameCount;          // 分区状态相同次数，超过三次就不会再发给控制端更新界面

    bool                b_existLocalSong;            //存在本地歌曲文件

    string              m_strNetRadioSessionId;      //网络电台sessionId

    public:
    pthread_mutex_t    sectionMutex;                //分区互斥锁
    pthread_mutex_t    sectionChangeMutex;         //分区变化锁
};

typedef class CSection* LPCSection;



/************************************************************************/
/* 分区管理类                                                           */
/************************************************************************/

enum
{
    DEVICE_SECTION = 0,			// 分区设备：包括IP音箱，解码器，功放
    DEVICE_PAGER,				// 寻呼站
    DEVICE_GPS,					// 校时器
    DEVICE_AUDIO_COLLECTOR,		// 音频采集器
    DEVICE_FIRE_COLLECTOR,		// 消防采集器
    DEVICE_SEQUENCE_POWER,		// 电源时序器
    DEVICE_AUDIO_MIXER,		    // 混音器
    DEVICE_REMOTE_CONTROLER,    // 遥控器
    DEVICE_PHONE_DEVICE,        // 电话网关
    DEVICE_AMP_CONTROLER,       // 功放控制器
    DEVICE_NOISE_DETECTOR,      // 噪声检测器
    DEVICE_MOBILE,				// 移动设备
    DEVICE_SERVER,              // 分控服务器
    DEVICE_INTERCOM_STATION,    // DSP9312

    DEVICE_TYPE_COUNT
};

typedef enum
{
    SORT_SEC_BY_TYPE = 1,
    SORT_SEC_BY_NAME,
    SORT_SEC_BY_IP,
    SORT_SEC_BY_MAC,
    SORT_SEC_BY_VERSION,
    SORT_SEC_BY_STATUS

}SectionPro;


class CSections
{
public:
    CSections(void);
    virtual ~CSections(void);

public:
    CMyString		GetDateTime(void)							{	return m_strDateTime;						}
    void            SetDateTime(CMyString strDateTime)			{	m_strDateTime = strDateTime;				}
    int				GetDeviceType(void)							{	return m_nDeviceType;						}
    void			SetDeviceType(int nDeviceType)				{	m_nDeviceType = nDeviceType;				}
    int				GetSecCount(void)							{	return m_Sections.size();					}
    int				GetSectionID(int nIndex)					{	return m_Sections[nIndex].GetID();			}
    bool			GetSectionChecked(int nIndex)				{	return m_Sections[nIndex].GetCheck();		}
    const char*		GetSectionName(int nIndex)					{	return m_Sections[nIndex].GetName();		}
    DeviceModel		GetSectionDeviceModel(int nIndex)			{	return m_Sections[nIndex].GetDeviceModel();	}
    const char*		GetSectionMac(int nIndex)					{	return m_Sections[nIndex].GetMac();			}
    const char*		GetSectionIP(int nIndex)					{	return m_Sections[nIndex].GetIP();			}
    NetworkMode		GetSectionNetworkMode(int nIndex)			{	return m_Sections[nIndex].GetNetworkMode();	}
    CSection&		GetSection(int nIndex);
    bool			AddSection(CSection& section);
    bool			RemoveSection(CSection& section);
    void			ClearSections(void);
    void			RemoveOfflineSections(void);


    bool			ReadFile();
    bool			WriteFile(bool bUpdateDateTime);

    bool			ReadSectionFile(string strFileName);			// 分区设备文件
    bool			WriteSectionFile(string strFileName, bool bUpdateDateTime = TRUE);
    bool			ReadAudioCollectorFile(string strFileName);     // 音频采集器文件
    bool			WriteAudioCollectorFile(string strFileName, bool bUpdateDateTime = TRUE);
    bool			ReadFireCollectorFile(string strFileName);		// 消防采集器文件
    bool			WriteFireCollectorFile(string strFileName, bool bUpdateDateTime = TRUE);
    bool            ReadSecMonitorFile(string strFileName);             // 分区绑定监控
    bool            WriteSecMonitorFile(string strFileName, bool bUpdateDateTime = TRUE);
    bool            ReadSequencePowerFile(string strFileName);          // 电源时序器文件
    bool            WriteSequencePowerFile(string strFileName, bool bUpdateDateTime);
    bool            ReadPagerFile(string strFileName);                  // 寻呼台文件
    bool            WritePagerFile(string strFileName, bool bUpdateDateTime);
    #if SUPPORT_REMOTE_CONTROLER
    bool            ReadRemoteControlerFile(string strFileName);                  // 远程遥控器文件
    bool            WriteRemoteControlerFile(string strFileName, bool bUpdateDateTime);
    #endif

    #if SUPPORT_AUDIO_MIXER
    bool            ReadAudioMixerFile(string strFileName);
    bool            WriteAudioMixerFile(string strFileName, bool bUpdateDateTime);
    #endif

    #if SUPPORT_PHONE_GATEWAY
    bool            ReadPhoneGatewayFile(string strFileName);
    bool            WritePhoneGatewayFile(string strFileName, bool bUpdateDateTime);
    #endif

    #if SUPPORT_AMP_CONTROLER
    bool            ReadAmpControlerFile(string strFileName);
    bool            WriteAmpControlerFile(string strFileName, bool bUpdateDateTime);
    #endif

    #if SUPPORT_NOISE_DETECTOR
    bool            ReadNoiseDetectorFile(string strFileName);
    bool            WriteNoiseDetectorFile(string strFileName, bool bUpdateDateTime);
    #endif

    bool            ReadGpsFile(string strFileName);
    bool            WriteGpsFile(string strFileName, bool bUpdateDateTime);


    LPCSection		GetSectionByMac(const char* szMac);
    LPCSection		GetSectionByMac(CMyString strMac);
    LPCSection		GetSectionByIP(const char* szIP);
    LPCSection		GetSectionByMonitorMac(const char* szMonitorMac);
    bool            ResetOfflineMonitorByMac(const char* szMonitorMac);

    // socket 保留，待修改
    LPCSection		GetSectionBySockObj(LPS_SOCKET_OBJ SockObj);
    LPCSection		GetSectionBySrcID(unsigned char srcID);

    void			AddSectionID(CSection& section);
    void			UpdateMacID();
    void			UpdateIpID();
    void			UpdateIp(CSection& section,const char *newIP);

    bool			NeedUpdateFile(DATETIME_FILE dt);// 是否需要更新文件（已获取）

    CMyString		GetDateTimeFileHttpPath(string strUserAccount,FileType ft);
    unsigned int	GetCheckedSections(unsigned int* pCheckedIndexs);
    unsigned int	GetCheckedSectionsCount();
    UINT			GetSectionsByPreSource(BYTE preSrc, PUINT pIndexs);
    LPCSection      GetSectionBySocketID(string strSocketID);

    bool			IsSectionOnline(int index); // 指定分区在线
    bool			HasSectionOnline();         // 有一个分区在线

    bool            HasUdpSectionOnline();      // 是否有UDP模式在线分区存在
    bool            HasTcpSectionOnline();      // 是否有TCP模式在线分区存在

    bool            HasSectionNotSupportExtraFeature(); //是否有分区不支持额外的特性

    CMyString		GetSectionName(const char* szIP);

    void			ExchangeSection(int s1, int s2);

    void			SortByProperty(SectionPro pro, bool bAscend = TRUE);
    void			SortByCustom(list<CSection> &listSection,bool isSuperUser=true);

    int				GetSyncSongSectionCount(void);

    static bool		IsSectionDevice(DeviceModel model);		// 分区设备
    static bool		IsControlDevice(DeviceModel model);		// 控制设备
    static bool		IsNotSectionDevice(DeviceModel model);	// 非分区设备
    static bool     IsSequencePowerDevice(DeviceModel model);  //电源时序器
    static bool     IsAudioCollectorDevice(DeviceModel model);  //音频采集器
    static bool     IsFireCollectorDevice(DeviceModel model);   // 消防采集器
    static bool     IsAudioMixerDevice(DeviceModel model);      // 混音器
    static bool     IsRemoteControlerDevice(DeviceModel model); //遥控器
    static bool     IsPhoneGatewayDevice(DeviceModel model); //电话网关
    static bool     IsAmpControlerDevice(DeviceModel model); //功放控制器
    static bool     IsNoiseDetectorDevice(DeviceModel model);//噪声检测器
    static bool     IsGpsDevice(DeviceModel model); //GPS校时器

    // 播放相关（集中模式）
    unsigned int	GetPlaySections(int playID, unsigned int* pSecIndexs);	// 获取播放有播放任务的分区
    bool			HasPlaySections(int playID);					// 是否存在播放任务分区
    bool			HasPrePlaySections(int playID);					// 是否存在播放任务分区(定时开始，保存播放歌曲任务)
    void            GetPlaySectionsVec(int playID,vector<string> &sections);      // 获取指定播放ID的分区数组
    bool            HasOfflinePlaySection(int playID);              // 是否存在掉线的播放任务分区
    void            ClearOfflinePlaySection(int playID);            // 清除指定播放ID的掉线播放任务分区
    bool			IsOnePlaySectionPlaying(int playID);			// 是否只有一个播放分区在播放歌曲
    unsigned int    GetPlaySectionPlayingCount(int playID);         // 获取播放分区处于正在播放状态的分区数量    CS 2019-4-18 （暂停播放功能）
    void			SetCheckSectionsPlayID(int playID);				// 设置选中分区为播放任务分区
    CSection*       GetListeningSection(void);                      // 得到正在监听的分区
    unsigned int    GetUpgradeSectionCount();                       // 得到正在升级的设备数量


    bool            IsExistZoneNeedQuickResponse_seqPwr();           // 是否存在分区需要快速响应

    bool			RemoveSectionFromAllFireCollectors(const char* szMac); // 清除所有消防告警器中的分区
#if SUPPORT_REMOTE_CONTROLER
    bool            RemoveSectionFromAllRemoteControler(const char* szMac);  //清除所有远程遥控器中的指定分区
    bool            RemoveGroupFromAllRemoteControler(bool IsAllGroup,CMyString strGroupID); //清除所有远程遥控器中的分组
#endif
#if SUPPORT_AUDIO_MIXER
    bool            RemoveSectionFromAllAudioMixers(const char* szMac);  //清除所有音频混音器中的指定分区
#endif
#if SUPPORT_PHONE_GATEWAY
    bool            RemoveSectionFromAllPhoneGateways(const char* szMac);  //清除所有电话网关中的指定分区
#endif
#if SUPPORT_NOISE_DETECTOR
    bool            RemoveSectionFromAllNoiseDetectors(const char* szMac);  //清除所有噪声自适应器中的指定分区
    unsigned char   GetSectionVolumeFromAllNoiseDetectors(const char* szMac);   //从所有的噪声自适应器中获取该分区的音量值
    vector<CMyString> GetAllSectionsFromAllNoiseDetectors();     //获取噪声自适应器里面包含的所有分区，需要剔除重复的
#endif

    bool            RemoveSectionFromAllAudioCollectors(const char* szMac); //清除所有音频采集器中的指定分区

    // zhuyg 保留，待修改
    int             GetSecIDByMac(string strMac) { return m_MacID[strMac]; }  // 根据Mac地址获取分区索引
    CSection*       GetSectionBySip(string  strExten);                        // 根据sip账号查找分区
    void            SetPageTypeBySip(string strExten, PageType pt);           // 根据sip账号设置寻呼状态

    void AddVarySection(string mac,int id);
    void DelVarySection();

    void ClearListenedSpecSection(string secMac);

    LPCSection GetAudioCollectorByAcSource(ProgramSource src);              //根据采集音源id获取音频器    

    void RemoveSpecDevice(string devMac);          //从特定设备类型中清除指定的设备（只能由相应的设备管理类对象调用）


    bool		ReadFileBySpecAccount(CMyString strUserAccount);
    bool		WriteFileBySpecAccount(CMyString strUserAccount,BOOL bUpdateDateTime = TRUE);
    void        RemoveXmlFileOfSpecAccount(CMyString strUserAccount);

    void    PushWriteXmlMacTask(string secMac);
    void    PushWriteXmlAccountTask(CMyString strUserAccount);
    static void*   SectionXmlUpgradeThread(void* lpParam);
    void    StartSectionXmlUpgradeThread();

private:
    int					m_nDeviceType;      // 设备类型
    vector<CSection>	m_Sections;			// 分区信息
    map<string, int>	m_MacID;			// MAC地址与分区ID映射
    map<string, int>	m_IpID;				// IP地址与分区ID映射
    map<LPS_SOCKET_OBJ, int>	m_SockID;	// IP地址与分区ID映射
    int					m_nSyncSongCount;	// 正在同步歌曲的分区个数
    CMyString			m_strDateTime;

    private:
        //pthread_mutex_t    sectionsMutex;
        //QMutex  sectionQMutex;

        shared_timed_mutex sectionQSharedMutex;

public:
    map<string, int>	 m_vec_Sections_Vary;			    // 分区状态变化的分区
    QMutex  sectionStatusQMutex;
    QMutex  sectionXmlUpgradeMutex;
    vector<CMyString>   m_vecSectionAccountChange;
};



typedef class CSections* LPCSections;


#endif // SECTION_H
