#include "Server.h"


CServer::CServer()
{
    m_nAllSyncSongCount = 0;
    m_nFinishedSongCount = 0;
    m_nSyncSongPercent = 0;



}

void CServer::ResetSyncStatus()
{
    m_nAllSyncSongCount = 0;
    m_nFinishedSongCount = 0;
    m_nSyncSongPercent = 0;
}

void CServer::SetSyncStatus(int nAllSyncSongCount, int nFinishedSongCount, int nSyncSongPercent)
{
    m_nAllSyncSongCount = nAllSyncSongCount;
    m_nFinishedSongCount = nFinishedSongCount;
    m_nSyncSongPercent = nSyncSongPercent;
}




