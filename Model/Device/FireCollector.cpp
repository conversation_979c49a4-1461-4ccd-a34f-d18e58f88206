#include "FireCollector.h"
#include <strings.h>
#include "Global/CType.h"

CFireChannel::CFireChannel(int nID)
{
    m_nID				= nID;
    m_nTriggerMode		= TRIG_MODE_LEVEL; //默认电平触发
    m_nTriggerState		= TRIG_STATE_OFF;  //触发状态默认为断开
    m_strSoundPathName	= ("");

    memset(m_szName, 0,sizeof(m_szName));
}


CFireChannel::~CFireChannel(void)
{
    m_SectionMac.clear();
    m_SectionIP.clear();
}

void CFireChannel::SetName(const char *szName)
{
    memset(m_szName,0,sizeof(m_szName));
    if (strlen(szName) > SEC_NAME_LEN)
    {
        strncpy(m_szName, szName, SEC_NAME_LEN);
    }
    else
    {
        strcpy(m_szName, szName);
    }
}


void CFireChannel::AddSection(CMyString strMac, CMyString strIP)
{
    m_SectionMac.push_back(strMac);
    m_SectionIP.push_back(strIP);
}


CMyString CFireChannel::GetTriModeString()
{
    if (m_nTriggerMode == TRIG_MODE_SHORT)
    {
        //return LANG_STR(LANG_SECTION_DIALOG, "Short Circuit", ("短路触发"));
    }
    else
    {
        //return LANG_STR(LANG_SECTION_DIALOG, "Level", ("电平触发"));
    }
    return NULL;
}

bool CFireChannel::IsExistSection(CMyString strMac)
{
    int nSecCount = m_SectionMac.size();

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_SectionMac[i] == strMac)
        {
            return TRUE;
        }
    }

    return FALSE;
}


void CFireChannel::RemoveSectionFromChannel(int index)
{
    vector<CMyString>::iterator iterMac, iterIP;
    int i = 0;
    for(i=0, iterMac=m_SectionMac.begin(), iterIP=m_SectionIP.begin();
        iterMac!=m_SectionMac.end() && iterIP!=m_SectionIP.end();
        ++i, ++iterMac, ++iterIP)
    {
        if (i == index)
        {
            m_SectionMac.erase(iterMac);
            m_SectionIP.erase(iterIP);
            break;	// 务必加上break
        }
    }
}

bool CFireChannel:: RemoveSectionFromChannel(const char* szMac)
{
    CMyString strMac(szMac);

    vector<CMyString>::iterator iterMac, iterIP;
    for(iterMac=m_SectionMac.begin(), iterIP=m_SectionIP.begin();
        iterMac!=m_SectionMac.end() && iterIP!=m_SectionIP.end();
        ++iterMac, ++iterIP)
    {
        if (strMac == *iterMac)
        {
            m_SectionMac.erase(iterMac);
            m_SectionIP.erase(iterIP);
            return true;
        }
    }

    return false;
}


/******************************************************/

CFireCollector::CFireCollector()
{
    m_bInitTriggerMode	= FALSE;
    m_bInitTriggerState = FALSE;
    m_nTriggerMode		= -1;
    m_nTriggerState		= -1;
}


CFireCollector::~CFireCollector(void)
{
    //m_Channels.clear();
}

void	CFireCollector::SetTriggerMode(int nTriggerMode)
{
    int nChannelCount = GetChannelCount();

    for (int i=0; i<nChannelCount; ++i)
    {
        m_Channels[i].SetTriggerMode((nTriggerMode>>(FIRE_CHANNEL_COUNT - 1 - i))&0x01);
    }

    m_nTriggerMode = nTriggerMode;
    m_bInitTriggerMode = TRUE;
}


void	CFireCollector::UpdateTriggerMode(void)
{
    int nChannelCount = GetChannelCount();
    int	nTriggerMode  = 0;

    for (int i=0; i<nChannelCount; ++i)
    {
        nTriggerMode |= (m_Channels[i].GetTriggerMode()<<(FIRE_CHANNEL_COUNT- i - 1));
    }

    m_nTriggerMode = nTriggerMode;
    m_bInitTriggerMode = TRUE;
}


void	CFireCollector::SetTriggerState(int nTriggerState)
{
    int nChannelCount = GetChannelCount();

    for (int i=0; i<nChannelCount; ++i)
    {
        m_Channels[i].SetTriggerState((nTriggerState>>(FIRE_CHANNEL_COUNT - 1 - i))&0x01);
    }

    m_nTriggerState = nTriggerState;
    m_bInitTriggerState = TRUE;
}


void	CFireCollector::UpdateTriggerState(void)
{
    int nChannelCount = GetChannelCount();
    int	nTriggerState  = 0;

    for (int i=0; i<nChannelCount; ++i)
    {
        nTriggerState |= (m_Channels[i].GetTriggerState()<<(FIRE_CHANNEL_COUNT- i - 1));
    }

    m_nTriggerState = nTriggerState;
    m_bInitTriggerState = TRUE;
}

// state的改变，得到改变的通道
int	CFireCollector::GetTriggerChannels(int nTriggerState, CFireChannel**pChnanel)
{
    int nChannelCount = GetChannelCount();
    int index = 0;

    for (int i=0; i<nChannelCount; ++i)
    {
        unsigned char state = (nTriggerState>>(FIRE_CHANNEL_COUNT - 1 - i))&0x01;

        if (m_Channels[i].GetTriggerState() != state)
        {
            pChnanel[index++] = &m_Channels[i];
        }
    }

    return index;
}


int	CFireCollector::GetChannelCount(void)
{
    return m_Channels.size();
}

bool	CFireCollector::AddChannel(CFireChannel& channel)
{
    if (GetChannelCount() < FIRE_CHANNEL_COUNT)
    {
        m_Channels.push_back(channel);

        return TRUE;
    }

    return FALSE;
}

bool	CFireCollector::RemoveSectionFromAllChannels(const char* szMac)
{
    bool flag = FALSE;
    int nChannelCount = GetChannelCount();

    for (int i=0; i<nChannelCount; ++i)
    {
        if (m_Channels[i].RemoveSectionFromChannel(szMac))
        {
            flag = true;
        }
    }

    return flag;
}
