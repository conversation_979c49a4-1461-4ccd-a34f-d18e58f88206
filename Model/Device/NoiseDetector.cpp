#include "NoiseDetector.h"
#include "Network/Protocol/Protocol.h"

#if SUPPORT_NOISE_DETECTOR
CNoiseDetector::CNoiseDetector()
{
    isEnable = false;
    memset(channelVal, 0, sizeof(channelVal));
    unsigned char defaultNoiseSegmentVol[NOISE_NUM_SEGMENTS] = {30,40,50,60,70,80,90,100};
    memcpy(segmentVol, defaultNoiseSegmentVol, sizeof(segmentVol));
    m_vecSecMac.clear();
}

CNoiseDetector::~CNoiseDetector(void)
{

}

bool CNoiseDetector::operator==(const CNoiseDetector &noiseDetector)
{
    //判断是否相等
    if(isEnable != noiseDetector.isEnable)
    {
        return false;
    }
    for(int i = 0; i < NOISE_NUM_CHANNELS; i++)
    {
        if(channelVal[i] != noiseDetector.channelVal[i])
        {
            return false;
        }
    }
    for(int i = 0; i < NOISE_NUM_SEGMENTS; i++)
    {
        if(segmentVol[i] != noiseDetector.segmentVol[i])
        {
            return false;
        }
    }

    if(m_vecSecMac != noiseDetector.m_vecSecMac)
    {
        return false;
    }

    return true;
}


int CNoiseDetector::GetSelectedSections(vector<CMyString>& vecMac)
{
    vecMac.clear();

    for (unsigned int i=0; i<m_vecSecMac.size(); ++i)
    {
        vecMac.push_back(m_vecSecMac[i]);
    }

    return vecMac.size();
}

bool CNoiseDetector::ClearSection(bool bAll,CMyString strMac)
{
    bool bUpdate=false;
    if(bAll)
    {
        if(m_vecSecMac.size()>0)
        {
            m_vecSecMac.clear();
            bUpdate=true;
        }
    }
    else
    {
        vector<CMyString>::iterator iter;
        for(iter=m_vecSecMac.begin();iter!=m_vecSecMac.end();++iter)
        {
            if (strMac == *iter)
            {
                m_vecSecMac.erase(iter);
                bUpdate = true;
                break;
            }
        }
    }
    return bUpdate;
}

unsigned short CNoiseDetector::GetAvgChannelVal(void) {
    int validChannelCnt = 0;
    int validChannelSum = 0;

    for (int i = 0; i < NOISE_NUM_CHANNELS; i++) {
        // 统一条件：>300 且 <=1300 为有效值
        if (channelVal[i] > 300 && channelVal[i] <= 1300) {
            validChannelCnt++;
            validChannelSum += channelVal[i];
        }
    }

    return validChannelCnt ? static_cast<unsigned short>(validChannelSum / validChannelCnt) : 0;
}

unsigned char  CNoiseDetector::GetSectionVolume()
{
    unsigned char sectionVolume = 0;
    // 获取平均噪声值
    unsigned short avgChannelVal = GetAvgChannelVal();
    // 如果平均噪声值为0，返回0音量值
    if(avgChannelVal == 0)
        return sectionVolume;
    // 将平均噪声值转换为噪声音量值
    float avgNoiseVal = avgChannelVal / 10.0f;

    // segmentVol数组对应的噪声级别：30dB, 40dB, 50dB, 60dB, 70dB, 80dB, 90dB, 100dB
    float noiseSegments[NOISE_NUM_SEGMENTS] = {30.0f, 40.0f, 50.0f, 60.0f, 70.0f, 80.0f, 90.0f, 100.0f};

    // 如果噪声值小于30dB，返回第一段的音量值
    if(avgNoiseVal <= 30.0f)
    {
        sectionVolume = segmentVol[0];
    }
    // 如果噪声值大于等于100dB，返回最后一段的音量值
    else if(avgNoiseVal >= 100.0f)
    {
        sectionVolume = segmentVol[NOISE_NUM_SEGMENTS - 1];
    }
    // 在30dB-100dB范围内，进行线性插值计算
    else
    {
        // 找到噪声值所在的区间
        for(int i = 0; i < NOISE_NUM_SEGMENTS - 1; i++)
        {
            if(avgNoiseVal >= noiseSegments[i] && avgNoiseVal < noiseSegments[i + 1])
            {
                // 线性插值计算音量值
                float ratio = (avgNoiseVal - noiseSegments[i]) / (noiseSegments[i + 1] - noiseSegments[i]);
                sectionVolume = (unsigned char)(segmentVol[i] + ratio * (segmentVol[i + 1] - segmentVol[i]));
                break;
            }
        }
    }

    return sectionVolume;
}

#endif