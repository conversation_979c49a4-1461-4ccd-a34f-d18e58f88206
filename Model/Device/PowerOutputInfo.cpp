#include "PowerOutputInfo.h"

#include "Global/CType.h"

CPowerOutputInfo::CPowerOutputInfo(void)
{
    m_powerMode = POWER_MODE_NORMAL;
    m_bLoop		= FALSE;
    m_bSigal	= FALSE;
    m_timeout	= 0;
}


CPowerOutputInfo::~CPowerOutputInfo(void)
{
}

unsigned char CPowerOutputInfo::GetPowerModeIndex()
{
    if (m_powerMode == POWER_MODE_SAVING)
    {
        return 0;
    }
    else
    {
        return 1;
    }
}

unsigned char CPowerOutputInfo::GetPowerModeValue(int index)
{
    if (index == 0)
    {
        return POWER_MODE_SAVING;
    }
    else
    {
        return POWER_MODE_NORMAL;
    }
}

unsigned char CPowerOutputInfo::GetTimeoutIndex()
{
    if (m_timeout == 10)
    {
        return 1;
    }
    else if (m_timeout == 30)
    {
        return 2;
    }
    else if (m_timeout == 60)
    {
        return 3;
    }
    else
    {
        return 0;
    }
}

unsigned short CPowerOutputInfo::GetTimeoutValue(int index)
{
    if (index == 1)
    {
        return 10;
    }
    else if (index == 2)
    {
        return 30;
    }
    else if (index == 3)
    {
        return 60;
    }
    else
    {
        return 5;
    }
}

void CPowerOutputInfo::SetTimeountIndex(unsigned char index)
{
    if (index == 1)
    {
        m_timeout = 10;
    }
    else if (index == 2)
    {
        m_timeout = 30;
    }
    else if (index == 3)
    {
        m_timeout = 60;
    }
    else
    {
        m_timeout = 5;
    }
}
