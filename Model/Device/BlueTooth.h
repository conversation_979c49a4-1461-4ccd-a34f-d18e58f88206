#ifndef BLUETOOTH_H
#define BLUETOOTH_H

#define MAX_BLUETOOTH_NAME_LEN  32
#define MAX_BLUETOOTH_PIN_LEN   4

class CBlueTooth
{
public:
    CBlueTooth(void);
    ~CBlueTooth(void);

public:
    bool operator!=( const CBlueTooth &deviceBlueTooth);

public:
    char	m_BlueTooth_Name[MAX_BLUETOOTH_NAME_LEN+1];	// 蓝牙名称
    unsigned char  m_BlueTooth_encryption;           //蓝牙加密方式（0-不加密 1需要密码）
    char	m_BlueTooth_Pin[MAX_BLUETOOTH_PIN_LEN+1];	// 蓝牙密码
};



#endif