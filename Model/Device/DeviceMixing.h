#ifndef DEVICEMIXING_H
#define DEVICEMIXING_H


#define DEFAULT_AUX	6
#define DEFAULT_DAC	8

#define AUX_AND_DAC	2	// AUX混合DAC

class CDeviceMixing
{
public:
    CDeviceMixing(void);
    ~CDeviceMixing(void);

public:
    bool operator!=( const CDeviceMixing &deviceMix);


public:
    unsigned char	m_nLineChn;		// 线路通道
    unsigned char	m_nMixing;		// 混音
    unsigned char	m_nDAC;			// DAC音量
    unsigned char	m_nAUX;			// AUX音量
};


#endif // DEVICEMIXING_H
