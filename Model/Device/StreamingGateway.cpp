#include "StreamingGateway.h"

CStreamingGateway::CStreamingGateway()
{
    m_isRadioConnected = false;
    memset(m_szSourceName, 0,sizeof(m_szSourceName));
    m_nPlayStatus   = PS_STOP;
    m_StreamingType = ST_IDLE;
}

bool CStreamingGateway::AddTrustDevice(const char* mac, const char* name)
{
    int nCount = m_TrustedDevices.size();

    for (int i=0; i<nCount; ++i)
    {
        if (strcmp(m_TrustedDevices[i].szMac, mac) == 0)
        {
            return false;
        }
    }

    TrustedDevice device;
    strcpy(device.szMac, mac);
    strcpy(device.szName, name);
    m_TrustedDevices.push_back(device);

    return true;
}

bool CStreamingGateway::EditTrustDevice(const char* mac, const char* name)
{
    int nCount = m_TrustedDevices.size();

    for (int i=0; i<nCount; ++i)
    {
        if (strcmp(m_TrustedDevices[i].szMac, mac) == 0)
        {
            strcpy(m_TrustedDevices[i].szName, name);
            return true;
        }
    }

    return false;
}

bool CStreamingGateway::RemoveTrustDeivce(const char* mac)
{
    bool flag = false;
    vector<TrustedDevice>::iterator iter;
    for(iter=m_TrustedDevices.begin(); iter!=m_TrustedDevices.end(); ++iter)
    {
        if(strcmp(iter->szMac, mac) == 0)
        {
            m_TrustedDevices.erase(iter);
            flag = true;
            break;	// 务必加上break
        }
    }

    return flag;
}

void CStreamingGateway::ClearTrustDevices(void)
{
    m_TrustedDevices.clear();
}

int CStreamingGateway::GetPageCount(void)
{
    int nDeviceCount = GetTrustDevicesCount();
    int nPageCount = nDeviceCount/MAX_TRUESTED_DEVICE_PER_PAGE;
    if(nDeviceCount%MAX_TRUESTED_DEVICE_PER_PAGE > 0)
    {
        nPageCount++;
    }

    if(nPageCount == 0)
    {
        nPageCount = 1;
    }

    return nPageCount;
}




