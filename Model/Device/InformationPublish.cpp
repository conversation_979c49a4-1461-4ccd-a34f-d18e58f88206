#include "stdafx.h"
#include "InformationPublish.h"


unsigned char g_information_publish_effectsArry[MAX_INFORMATION_PUBLISH_EFFECTS]={\
    EF_AUTO,EF_PAGE_TURNING,EF_ROTATE_LEFT,\
    EF_LEFT,EF_ROTATE_DOWN,EF_DOWN,EF_FLICKER,\
    EF_ROTATE_UP,EF_UP,EF_SNOW\
};

CInformationPublish::CInformationPublish()
{
    m_bEnableDisplay = false;
    memset(m_szText,0,sizeof(m_szText));
    m_nEffects = 0;
    m_nMoveSpeed = 3;
    m_nStayTime = 3;
}

CInformationPublish::~CInformationPublish(void)
{

}






