#ifndef SEQUENCEPOWER_H
#define SEQUENCEPOWER_H

#include <iostream>
#include <vector>
#include "Tools/CMyString.h"
#include "Global/Const.h"

using std::vector;

#define SEQUENCE_POWER_MAX_CHANNEL_COUNT	    16      //常规数量（实际需要根据电源时序器应答数据确认）
#define SEQUENCE_POWER_DEFAULT_DELAY        500        //500ms

#define SEQUENCE_POWER_MODE_MANUAL		0x01	// 手动模式
#define SEQUENCE_POWER_MODE_AUTO		0x02	// 自动模式

#define SEQUENCE_POWER_CHANNEL_STATE_OFF		0x00	// 关闭
#define SEQUENCE_POWER_CHANNEL_STATE_ON		0x01	// 打开

// 电源时序器通道
class CSequencePowerChannel
{
public:
    CSequencePowerChannel(int nID);
    ~CSequencePowerChannel(void);

public:
    char*           GetName(void)							{	return m_szName;						}
    void            SetName(const char* szName)	;
    int             GetID(void)								{	return m_nID;				}
    unsigned char	GetSwitchState(void)					{	return m_nSwitchState;		}
    void            SetSwitchState(unsigned char state)	    {	m_nSwitchState	= state;	}
    void            SetIsExistTimer(unsigned char state)	    {	m_bExistTimer	= state;	}
    bool            GetIsExistTimer()	                        {	return m_bExistTimer;	}
    void            SetIsInTiming(unsigned char state)	    {	m_bInTiming	= state;	}
    bool            GetIsInTiming()	                        {	return m_bInTiming;	}

private:
    int			        m_nID;                      // ID
    unsigned char		m_nSwitchState;		        // 开关状态
    char		        m_szName[SEC_NAME_LEN+1];	// 通道名称，自定义
    bool                m_bExistTimer;             // 是否存在定时
    bool                m_bInTiming;               // 是否处于定时中
};


/***************************************************************/


// 电源时序器（最大16通道）
class CSequencePower
{
public:
    CSequencePower();
    ~CSequencePower(void);

public:
    bool operator!=( const CSequencePower &deviceSequencePower);
    CSequencePowerChannel& GetChannel(int index){	return m_Channels[index];	}
    int		GetControlMode(void)				{	return m_nControlMode;		}
    void	SetControlMode(int m_nControlMode);

    int		GetChannelSwitchState(int index)		        {	return GetChannel(index).GetSwitchState(); }
    void	SetChannelSwitchState(int index,int state)		{	GetChannel(index).SetSwitchState(state);   }

    bool	HasInitOK(void)			        {	return m_bInitOK;	}
    void	SetInitOK(bool b_InitOK)	    { m_bInitOK = b_InitOK; }

    void	SetAllSwitchState(unsigned short state);		     // 通道的开关状态改变，需要更新一下电源时序器的值
    unsigned short GetAllSwitchState(void)    {   return m_nSwitchSate; }

    void    SetRealChannelCnt(int channelCnt)       {  m_nChannelCnt =  channelCnt; }
    int     GetRealChannelCnt()                     { return m_nChannelCnt; }

    int		GetChannelCount(void);
    bool    AddChannel(CSequencePowerChannel& channel);

private:
    vector<CSequencePowerChannel>	m_Channels;     //电源时序器通道
    int		m_nControlMode;                         //工作模式
    int     m_nChannelCnt;                          //通道数
    bool    m_bInitOK;                              //已初始化通道
    unsigned short   m_nSwitchSate;                        //所有通道的开关状态
};




#endif // SEQUENCEPOWER_H
