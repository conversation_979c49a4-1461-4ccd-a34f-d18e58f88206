#include "PhoneGateway.h"
#include "Network/Protocol/Protocol.h"

#if SUPPORT_PHONE_GATEWAY
CPhoneGateway::CPhoneGateway()
{
    m_uSampleRate	= AUDIO_PHONE_GATEWAY_RATE_16K;
    m_uAlgorithm	= ALGORITHM_PCM;
    g722_enc_ctx = NULL;

    b_phoneGatewaySingalValid = false;        //phoneGateway信号是否有效

    m_nMasterSwitch=0;                      //主开关（0/1，默认为0关闭）
    m_nVolume=50;                           //分区音量
}

CPhoneGateway::~CPhoneGateway(void)
{

}

bool CPhoneGateway::operator==(const CPhoneGateway &phoneGateway)
{
   if(m_nMasterSwitch!=phoneGateway.m_nMasterSwitch ||\
      m_nVolume!=phoneGateway.m_nVolume ||\
      m_vecSecMac!=phoneGateway.m_vecSecMac ||\
      m_vecTelWhitelist!=phoneGateway.m_vecTelWhitelist
    )
   {
        return false;
   }
   return true;
}


int CPhoneGateway::GetSelectedSections(vector<CMyString>& vecMac)
{
    vecMac.clear();

    for (unsigned int i=0; i<m_vecSecMac.size(); ++i)
    {
        vecMac.push_back(m_vecSecMac[i]);
    }

    return vecMac.size();
}

bool CPhoneGateway::ClearSection(bool bAll,CMyString strMac)
{
    bool bUpdate=false;
    if(bAll)
    {
        if(m_vecSecMac.size()>0)
        {
            m_vecSecMac.clear();
            bUpdate=true;
        }
    }
    else
    {
        vector<CMyString>::iterator iter;
        for(iter=m_vecSecMac.begin();iter!=m_vecSecMac.end();++iter)
        {
            if (strMac == *iter)
            {
                m_vecSecMac.erase(iter);
                bUpdate = true;
                break;
            }
        }
    }
    return bUpdate;
}

#endif