#include "stdafx.h"
#include "Section.h"
#include <string>
#include "TinyXml/tinyxml.h"

CSection::CSection(int           nSecID,
                   DeviceModel   nModel,
                   const char    *szMac,
                   const char    *szIP,
                   int           nVol,
                   ProgramSource src,
                   const char    *szName)
{
    m_nID               = nSecID;
    m_isChecked			= FALSE;
    m_nDeviceModel      = nModel;
    m_nVolume			= nVol;
    m_nSubVolume        = 100;
    m_nAuxVolume        = 100;
    m_nProSource		= src;
    m_nPreSource		= src;
    m_nPlayStatus		= PS_STOP;
    m_bTimerInvalid		= FALSE;
    m_nBmpID			= 2;
    m_tRestartTime		= 0;
    m_isSyncSong		= FALSE;
    m_nFinishedSong		= 0;
    m_nSyncSongCount	= 0;
    m_nReservedWord		= 0;
    m_nPlayID			= -1;
    m_nPrePlayID		= -1;
    m_bNeedUpdate		= FALSE;
    m_isAUX             = FALSE;

    m_kcpsocket = NULL; //成员变量-指针 默认不为NULL，所以要在构造函数里面初始化为NULL（只有标准类型变量初始值才为NULL,标准类型也要赋初值，否则可能出问题）

    m_nDeviceFeature    = 0;    //设备功能特征 20201216
    m_nDeviceExtraFeature = -1; //注意这里不要用int型，因为用于区分终端是否是新版本
    b_existLocalSong    = false;

    memset(m_szVersion, 0,sizeof(m_szVersion));
    memset(m_szProName, 0,sizeof(m_szProName));
    memset(m_szMonitorMac, 0,sizeof(m_szMonitorMac));
    memset(m_szTriggerFireColMac,0,sizeof(m_szTriggerFireColMac));
    SetUpgrading(FALSE);

    m_strCurDateTime	= "";
    m_bConnectedGPS		= FALSE;
    m_uTriggerCount		= 0;
    m_CurTriggerEventType = EVENT_NULL;

    m_tcpServerIP       = "";
    m_tcpServerPort     = 0;
    m_tcpServerIP2      = "";
    m_tcpServerPort2    = 0;

    //20230601      新增4g信号质量、iccid卡号
    m_module4G_signal_rssi=0;         // 4G模块信号质量
    memset(m_module4G_iccid,0,sizeof(m_module4G_iccid));    // 4G模块iccid卡号,20位数字

    if (szMac != NULL)
    {
        strcpy(m_netInfo.m_szMac, szMac);
    }

    if (szIP != NULL)
    {
        strcpy(m_netInfo.m_szIP, szIP);
    }

    if (szName != NULL)
    {
        if(strlen(szName) > SEC_NAME_LEN)
        {
            memcpy(m_szSecName, szName, SEC_NAME_LEN);
            m_szSecName[SEC_NAME_LEN] = '\0';
        }
        else
        {
            strcpy(m_szSecName, szName);
        }
    }
    else
    {
        memset(m_szSecName, 0,sizeof(m_szSecName));
    }

    for (int i=0; i<DT_COUNT; ++i)
    {
        m_strFileDateTimes[i]	= ("");
        m_uGetFileInfoCount[i]	= 0;
    }

    m_pSelectedSections = NULL;
    if (IsControlDevice())
    {
        m_pSelectedSections = std::make_shared<CSelectedSections>();
        m_pAudioForward = std::make_shared<CAudioForward>();
    }
//20221019所有设备均创建对讲对象
#if SUPPORT_PAGER_CALL
        m_pAduioCall = std::make_shared<CAudioCall>();
#endif

#if SUPPORT_LISTEN_FUNCTION
    m_pAduioListen = new CAudioListen();
#endif

    m_pAudioCollector = NULL;
    if (IsAudioCollectorDevice())
    {
        int validSecID=nSecID;
        for(int i=1;i<=g_Global.m_AudioCollectors.GetSecCount();i++)
        {
            int m_uSrcID = PRO_AUDIO_COLLECTOR_MIN + (i-1)*AUDIO_COLLECTOR_CHANNELS;
            bool found_validSec=true;
            for(int j=1;j<=g_Global.m_AudioCollectors.GetSecCount();j++)
            {
                CSection &acSection=g_Global.m_AudioCollectors.GetSection(j-1);
                if(m_uSrcID == acSection.m_pAudioCollector->GetSourceID())
                {
                    found_validSec=false;
                    break;
                }
            }
            if(found_validSec)
            {
                validSecID = i;
                break;
            }
        }
        m_pAudioCollector = std::make_shared<CAudioCollector>(validSecID,m_nDeviceModel);
    }

    m_pFireCollector = NULL;
    if (IsFireCollectorDevice())
    {
        m_pFireCollector = std::make_shared<CFireCollector>();
    }

    m_pSequencePower = NULL;
    if (IsPowerSequenceDevice())
    {
        m_pSequencePower = std::make_shared<CSequencePower>();
    }

    #if SUPPORT_REMOTE_CONTROLER
    if (IsRemoteControlerDevice())
    {
        m_pRemoteControler = std::make_shared<CRemoteControl>();
    }
    #endif

    #if SUPPORT_AUDIO_MIXER
    if (IsAudioMixerDevice())
    {
        m_pAudioMixer = std::make_shared<CAudioMixer>();
    }
    m_bAudioMixedSourceValid = false;
    #endif

    #if SUPPORT_PHONE_GATEWAY
    if (IsPhoneGatewayDevice())
    {
        m_pPhoneGateway = std::make_shared<CPhoneGateway>();
    }
    m_bphoneGatewaySourceValid = false;
    #endif

    #if SUPPORT_AMP_CONTROLER
    if (IsAmpControlerDevice())
    {
        m_pAmpControler = std::make_shared<CAmpControler>();
    }
    #endif

    #if SUPPORT_NOISE_DETECTOR
    if (IsNoiseDetectorDevice())
    {
        m_pNoiseDetector = std::make_shared<CNoiseDetector>();
    }
    m_nNoiseDetectorSectionVolume = 0;
    m_tNoiseDetectorLastTime = 0;
    #endif

    // TCP socket 保留，待修改
    m_pSocketObj = NULL;

    m_uAudioPort		= 0;
    m_strMemory			= "";

    m_isListening       =  FALSE;
    m_uAudioPort        = g_Global.m_KCP_PORT;

    m_bSupportSyncFile  = FALSE;

    m_nStatusSameCount   = 0;

    //此处默认值为管理员，避免重启服务器后服务器不知道当前寻呼台已登录的账户
    m_strUserAccount = "";

    sectionMutex = PTHREAD_MUTEX_INITIALIZER;

    sectionChangeMutex = PTHREAD_MUTEX_INITIALIZER;
}

CSection::~CSection()
{
    //******** 静态初始化的互斥锁不需要,也不能用pthread_mutex_destroy销毁锁，否则将出错
    //pthread_mutex_destroy(&sectionMutex);
    //pthread_mutex_destroy(&sectionChangeMutex);
    //printf("~CSection...\n");
    //ClearData();
}

void CSection::ResetData()
{
    m_nPlayID		= -1;
    m_nPrePlayID	= -1;
    m_isChecked		= FALSE;
    m_nVolume		= 0;
    m_nProSource	= PRO_OFFLINE;
    m_nPreSource	= PRO_OFFLINE;
    m_nPlayStatus	= PS_STOP;
    m_tLatestTime	= 0;
    m_isSyncSong	= FALSE;
    m_pSocketObj	= NULL;
    m_kcpsocket     = NULL;
    m_bNeedUpdate	= FALSE;
    m_uAudioPort	= 0;
    m_isListening   = FALSE;

    m_module4G_signal_rssi=0;      //离线后,4G模块信号质量=0

    //m_strUserAccount = "";    //设备掉线不能清空账户，否则旧设备中途上线，服务器不知道当前登录的账户信息

    memset(m_szProName, 0,sizeof(m_szProName));
    ResetFileDateTime();
    SetUpgrading(FALSE);
    m_LogFiles.Clear();
    memset(m_szTriggerFireColMac,0, sizeof(m_szTriggerFireColMac));

    //时序器设置为false，以便再次上线后获取最新通道信息
    if(IsPowerSequenceDevice() && m_pSequencePower)
    {
        m_pSequencePower->SetInitOK(false);
    }
    #if SUPPORT_AUDIO_MIXER
    else if (IsAudioMixerDevice())
    {
        if(m_pAudioMixer)
        {
            m_pAudioMixer->SetAudioMixerSingalValid(false);
        }
    }
    #endif

    m_nStatusSameCount = 0;

    m_strNetRadioSessionId.clear();
}
//要解决此问题需要覆写深拷贝函数，https://www.cnblogs.com/zhangjing0502/archive/2012/05/22/2513342.html
void CSection::ClearData()
{
    // 例如CSections sections = g_Global.m_Sections的赋值，sections生命周期结束后，还是会调试clear（在CSections的析构函数）
    // 所以可能会引起错误，这里屏蔽掉，因为其本身占有的内存也不大，产生泄漏影响也不大
    /*
        // 因为构造对象都是临时变量，所以不能在析构函数里面释放内存，临时变量会在函数结束时就释放了内存
        if (m_pFireCollector != NULL)
        {
            delete m_pFireCollector;
            m_pFireCollector = NULL;
        }

        if (m_pPowerInfo != NULL)
        {
            delete m_pPowerInfo;
            m_pPowerInfo = NULL;
        }

        if (m_pSelectedSections != NULL)
        {
            delete m_pSelectedSections;
            m_pSelectedSections = NULL;
        }
        */
}


string CSection::GetUTFName()
{
    return StringToUTF8(m_szSecName);
}

void CSection::SetUpgrading(bool isUp)
{
    m_isUpgrading = isUp;
}


void  CSection::SetNetworkMode(NetworkMode networkMode,string tcpServerIP,int tcpServerPort,string tcpServerIP2,int tcpServerPort2)
{
    m_nReservedWord &= ~(3<<4);
    //再赋值
    m_nReservedWord |= (networkMode<<4);

    if(networkMode == NETWORK_TCP)
    {
        m_tcpServerIP = tcpServerIP;
        m_tcpServerPort = tcpServerPort;

        m_tcpServerIP2 = tcpServerIP2;
        m_tcpServerPort2 = tcpServerPort2;
    }
}

unsigned int CSection::GetKcpConv()
{
    unsigned int kcp_conv;
    //通过MAC获取conv
    if(strlen(m_netInfo.m_szMac)>0)
    {
        unsigned char	Mac[10]		= {0};	// 大小不能直接定义为MAC_LEN，否则调用sscanf_s会出错
        sscanf(m_netInfo.m_szMac, "%x:%x:%x:%x:%x:%x", &Mac[0], &Mac[1], &Mac[2], &Mac[3], &Mac[4], &Mac[5]);
        kcp_conv=((Mac[0]+Mac[1]+Mac[2])<<24)+(Mac[3]<<16)+(Mac[4]<<8)+(Mac[5]);
        printf("m_netInfo.m_szMac=%s,conv=%ul\n",m_netInfo.m_szMac,kcp_conv);
        return kcp_conv;
    }
    return -1;
}

void CSection::SetMac(const char*	lpszMac)
{
    if (lpszMac != NULL)
    {
        memset(m_netInfo.m_szMac,0,sizeof(m_netInfo.m_szMac));
        strcpy(m_netInfo.m_szMac, lpszMac);
    }
}

void CSection::SetIP(const char*	lpszIP)
{
    if (lpszIP != NULL)
    {
        memset(m_netInfo.m_szIP,0,sizeof(m_netInfo.m_szIP));
        strcpy(m_netInfo.m_szIP, lpszIP);
    }
}

void CSection::SetName(const char* szName)
{
    if (szName != NULL)
    {
        memset(m_szSecName,0,sizeof(m_szSecName));
        if (strlen(szName) > SEC_NAME_LEN)
        {
            strncpy(m_szSecName, szName, SEC_NAME_LEN);
        }
        else
        {
            strcpy(m_szSecName, szName);
        }
    }
}

void CSection::SetVersion(const char* szVersion)
{
    if (szVersion != NULL)
    {
        memset(m_szVersion,0,sizeof(m_szVersion));
        if (strlen(szVersion) >= SEC_VERSION_LEN)
        {
            strncpy(m_szVersion, szVersion, SEC_VERSION_LEN-1);
        }
        else
        {
            strcpy(m_szVersion, szVersion);
        }
    }
}

void CSection::SetMonitorMac(const char *szMonitorMac)
{
    if (szMonitorMac != NULL)
    {
        memset(m_szMonitorMac,0,sizeof(m_szMonitorMac));
        strcpy(m_szMonitorMac, szMonitorMac);
    }
}

int CSection::GetDevType()
{
    if(IsSectionDevice())
    {
        return DEVICE_SECTION;
    }
    else if(m_nDeviceModel == MODEL_PAGER_A || m_nDeviceModel == MODEL_PAGER_B || m_nDeviceModel == MODEL_PAGER_C)
    {
        return DEVICE_PAGER;
    }
    else if(m_nDeviceModel == MODEL_GPS)
    {
        return DEVICE_GPS;
    }
    else if(IsAudioCollectorDevice())
    {
        return DEVICE_AUDIO_COLLECTOR;
    }
    else if(IsFireCollectorDevice())
    {
        return DEVICE_FIRE_COLLECTOR;
    }
    else if(m_nDeviceModel == MODEL_CONTROL_SERVER)
    {
        return DEVICE_SERVER;
    }
    else if(m_nDeviceModel == MODEL_INTERCOM_STATION)
    {
        return DEVICE_INTERCOM_STATION;
    }
    else if(IsPowerSequenceDevice())
    {
        return DEVICE_SEQUENCE_POWER;
    }
    else if(IsAudioMixerDevice())
    {
        return DEVICE_AUDIO_MIXER;
    }
    else if(IsRemoteControlerDevice())
    {
        return DEVICE_REMOTE_CONTROLER;
    }
    else if(IsPhoneGatewayDevice())
    {
        return DEVICE_PHONE_DEVICE;
    }

    return DEVICE_MOBILE;
}


int CSection::GetListenCount()
{
    return m_vecListenSec.size();
}

void CSection::AddListenSec(string strMac)
{
    //pthread_mutex_lock(&m_csListen);
    int nCount = std::count(m_vecListenSec.begin(), m_vecListenSec.end(), strMac);
    if (nCount == 0)
        m_vecListenSec.push_back(strMac);
    //pthread_mutex_unlock(&m_csListen);
}

void CSection::DelListenSec(string strMac)
{
   int nCount=m_vecListenSec.size();
   for(int i=0;i<nCount;i++)
   {
       if(m_vecListenSec[i] == strMac)
       {
           m_vecListenSec.erase(m_vecListenSec.begin()+i);
           break;
       }
   }
   if(nCount >0 && m_vecListenSec.size() == 0)
   {
       SetListening(FALSE);
   }
}

string CSection::GetListenSec(int index)
{
    if(index >= m_vecListenSec.size())
    {
        return "";
    }

    return m_vecListenSec[index];
}

// 清除播放ID不与当前分区一致的监听分区，避免切换下一曲时关闭了监听会继续播放
/*void CSection::CleanListenSec()
{
    vector<string>::iterator iter= m_vecListenSec.begin();
    for(; iter != m_vecListenSec.end(); )
    {
        LPCSection pSection = g_Global.m_Sections.GetSectionByMac(*iter);
        if(pSection != NULL)
        {
            if(pSection->GetPlayID() != GetPlayID())
            {
                m_vecListenSec.erase(iter);
            }
            else
            {
                iter++;
            }
        }
        else
        {
            m_vecListenSec.erase(iter);
        }
    }
}
*/

void CSection::ClearListenSec()
{
    //pthread_mutex_lock(&m_csListen);
    m_vecListenSec.clear();
    //pthread_mutex_unlock(&m_csListen);
}


void CSection::SetProName(char* lpszProName)
{
    if(strlen(lpszProName) >= SRC_NAME_LEN) // 节目源名称过长，截断
    {
        lpszProName[SRC_NAME_LEN-1] = '\0';
    }

    strcpy(m_szProName, lpszProName);
}


CMyString CSection::GetProSourceName(void)
{
    // 如果是音频采集器，则使用自定义音源名称
    if (IsAudioCollectorDevice() )
    {
        return CMyString(m_szSecName);
    }
    else
    {
        return CProtocol::GetDescriptionProSource(m_nProSource);
    }
}



/*void CSection::SetBmpPath(const char* szPath)
{
    strcpy(m_szBmpPath, szPath);
}*/


bool CSection::GetCheck(void)
{
    if (m_isChecked && IsOnline())
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}


void CSection::SetProSource(ProgramSource src, bool bSetRecentSrc)
{
    m_nPreSource = m_nProSource;
    m_nProSource = src;

    if (src == PRO_OFFLINE)
    {
        m_nPreSource = PRO_OFFLINE;
        m_isChecked = FALSE;
    }

    // 是否设置最近音源,如果当前音源是寻呼，则没必要设置
    if(bSetRecentSrc && src!= PRO_PAGING)
    {
        m_PlayedRecently.m_nRecentSrc = src;
    }

    m_tIdleTime = ( (m_nProSource == PRO_IDLE || m_nProSource == PRO_ANALOG_INPUT)  ? CTime::GetCurrentTimeT().GetTime() : -1);

    // 如果不是消防告警音源，则MAC重置
    if (m_nProSource != PRO_ALARM)
    {
        SetTriggerCount(0);
        memset(m_szTriggerFireColMac, 0,sizeof(m_szTriggerFireColMac));
    }
}

bool CSection::NeedResumeAudioCollector(ProgramSource newSrc)
{
    return ( (newSrc == PRO_IDLE || newSrc == PRO_ANALOG_INPUT || newSrc == PRO_100V)
          //&& CProtocol::IsAudioCollectorSrc(m_nProSource)) 
            && (CProtocol::IsAudioCollectorSrc(m_PlayedRecently.m_nRecentSrc) || CProtocol::IsAudioCollectorSrc(m_PlayedRecently.m_bSongTimerEndResumeAc)));
}

bool CSection::IsOnline(bool bLocal)
{
    if(bLocal && IsLocal())
    {
        return TRUE;
    }

    return (m_nProSource != PRO_OFFLINE);
}

bool CSection::IsPagingIn(void)
{
    return (m_nProSource == PRO_PAGING);
}

bool CSection::IsMixedIn(void)
{
    return (m_nProSource == PRO_AUDIO_MIXED);
}

bool CSection::IsPhoneGatewayIn(void)
{
    return (m_nProSource == PRO_PHONE_GATEWAY);
}

bool CSection::IsSipCall(void)
{
    return (m_nProSource == PRO_SIP_CALLING);
}

bool CSection::IsApiTTsOrMusic(void)
{
    return (m_nProSource == PRO_API_TTS_MUSIC);
}

bool CSection::IsNetRadio(void)
{
    return (m_nProSource == PRO_API_NET_RADIO);
}

bool CSection::IsIdle(void)
{
    return (m_nProSource == PRO_IDLE || m_nProSource == PRO_ANALOG_INPUT );
}

bool CSection::IsLocal()
{
    return (strcmp(GetMac(), CNetwork::GetHostMac()) == 0);
}


bool CSection::IsSipDevice(void)
{
    return (IsSectionDevice() && (m_nReservedWord&0x03) == RESERVED_SIP);
}

bool CSection::IsTcpMode(void)
{
    return (GetNetworkMode() == NETWORK_TCP);
}

bool CSection::IsUdpMode(void)
{
    return (GetNetworkMode() == NETWORK_UDP);
}

bool CSection::IsBlueToothDevice(void)
{
    return (IsSectionDevice() && (m_nDeviceFeature&DF_BLUETOOTH));
}

bool CSection::IsLocalMonitorDevice(void)
{
    return (IsSectionDevice() && (m_nDeviceFeature&DF_LOCAL_MONITOR));
}

bool CSection::IsSupportCallDevice(void)
{
    return (m_nDeviceFeature&DF_CALL);
}

bool CSection::IsSupportVideoDevice(void)
{
    return (m_nDeviceFeature&DF_VIDEO);
}

bool CSection::IsSupportSipDevice(void)
{
    return (m_nDeviceFeature&DF_SIP);
}

bool CSection::IsSupportInformationPublishDevice(void)
{
    return (m_nDeviceFeature&DF_INFORMATION_PUBLISH);
}


bool CSection::IsSectionDevice(void)
{
    return (m_nDeviceModel == MODEL_IP_SPEAKER_A || m_nDeviceModel == MODEL_IP_SPEAKER_B \
            || m_nDeviceModel == MODEL_IP_SPEAKER_C || m_nDeviceModel == MODEL_IP_SPEAKER_D \
            || m_nDeviceModel == MODEL_IP_SPEAKER_E
            || m_nDeviceModel == MODEL_IP_SPEAKER_F
            || m_nDeviceModel == MODEL_IP_SPEAKER_G
            || m_nDeviceModel == MODEL_AUDIO_MIXER_DECODER
            || m_nDeviceModel == MODEL_AUDIO_MIXER_DECODER_C
        );
}

bool CSection::IsControlDevice(void)
{
    return (m_nDeviceModel == MODEL_PAGER_A || m_nDeviceModel == MODEL_PAGER_B || m_nDeviceModel == MODEL_PAGER_C);
}

bool CSection::IsFireCollectorDevice(void)
{
    return (m_nDeviceModel == MODEL_FIRE_COLLECTOR_A
         || m_nDeviceModel == MODEL_FIRE_COLLECTOR_B
         || m_nDeviceModel == MODEL_FIRE_COLLECTOR_C
         || m_nDeviceModel == MODEL_FIRE_COLLECTOR_F);
}

bool CSection::IsAudioCollectorDevice(void)
{
    return (m_nDeviceModel == MODEL_AUDIO_COLLECTOR_A
         || m_nDeviceModel == MODEL_AUDIO_COLLECTOR_B
         || m_nDeviceModel == MODEL_AUDIO_COLLECTOR_C
         || m_nDeviceModel == MODEL_AUDIO_COLLECTOR_F);
}

bool CSection::IsPowerSequenceDevice(void)
{
    return (m_nDeviceModel == MODEL_SEQUENCE_POWER_A
         || m_nDeviceModel == MODEL_SEQUENCE_POWER_B
         || m_nDeviceModel == MODEL_SEQUENCE_POWER_C
         || m_nDeviceModel == MODEL_SEQUENCE_POWER_F);
}

bool CSection::IsAudioMixerDevice(void)
{
    return (m_nDeviceModel == MODEL_AUDIO_MIXER_ENCODER
            || m_nDeviceModel == MODEL_AUDIO_MIXER_ENCODER_C);
}

bool CSection::IsAudioMixerDecoderDevice(void)
{
    return (m_nDeviceModel == MODEL_AUDIO_MIXER_DECODER
            || m_nDeviceModel == MODEL_AUDIO_MIXER_DECODER_C);
}

bool CSection::IsRemoteControlerDevice(void)
{
    return (m_nDeviceModel == MODEL_REMOTE_CONTROLER
            || m_nDeviceModel == MODEL_REMOTE_CONTROLER_C
            || m_nDeviceModel == MODEL_REMOTE_CONTROLER_F);
}

bool CSection::IsPhoneGatewayDevice(void)
{
    return (m_nDeviceModel == MODEL_PHONE_GATEWAY);    
}

bool CSection::IsAmpControlerDevice(void)
{
    return (m_nDeviceModel == MODEL_AMP_CONTROLER);
}

bool CSection::IsNoiseDetectorDevice(void)
{
    return (m_nDeviceModel == MODEL_NOISE_DETECTOR);
}


bool CSection::IsServerDevice()
{
    return (m_nDeviceModel == MODEL_CONTROL_SERVER);
}

// 具备回路检测功能的设备
bool CSection::IsLoopDetectDevice(void)
{
    //return (m_nDeviceModel == MODEL_DECODE_PLAYER_CABINET);
    return 0;   //jms
}

// 是否可同步文件的设备
bool CSection::IsUpdateFileDevice()
{
    // 只有内存单位为G时才支持同步文件
    if(m_strMemory.Find("G") != std::string::npos)
    {
        return TRUE;
    }

    return FALSE;
}


CMyString CSection::GetFileDateTime(DATETIME_FILE dt)
{
    return m_strFileDateTimes[dt];
}

void CSection::SetFileDateTime(DATETIME_FILE dt, CMyString strDateTime)
{
    m_strFileDateTimes[dt]	= strDateTime;
    // ? 是否需要改为1
    m_uGetFileInfoCount[dt] = 0;
}

void CSection::ResetFileDateTime(void)
{
    for (int i=0; i<DT_COUNT; ++i)
    {
        m_strFileDateTimes[i]	= ("");
        m_uGetFileInfoCount[i]	= 0;
    }
}

// 需要更新分区的文件
bool CSection::NeedUpdateSectionFile()
{
    if (!IsSectionDevice())
    {
        return FALSE;
    }

    return (NeedUpdateFile(DT_PLAYLIST) || NeedUpdateFile(DT_TIMER) || NeedUpdateFile(DT_GROUP));
}


//是否需要升级文件
bool CSection::NeedUpdateFile(DATETIME_FILE dt)
{
    CMyString strDateTime	= GetFileDateTime(dt);
    bool	bNotNeedUpdate	= (!IsOnline());

    switch (dt)
    {
    case DT_GROUP:
        bNotNeedUpdate = (bNotNeedUpdate || g_Global.m_Groups.GetDateTime() == ("") || strDateTime == g_Global.m_Groups.GetDateTime());
        break;

    case DT_PLAYLIST:
    {
        //******** 播放列表需要根据账户下发
        //先判断有没有登录，如果没有登录，那么不处理。
        #if APP_IS_LZY_LIMIT_STORAGE
        string loginAccount=GetUserAccount();
        if(loginAccount.length() > 0)
        {
            //判断登录账户是否存在
            LPCUserInfo  pUser = g_Global.m_Users.GetUserByAccount(loginAccount);
            if(pUser)
            {
                if(pUser->IsSuperUser())
                {
                    bNotNeedUpdate = (bNotNeedUpdate || GetPlaylist()->GetDateTime() == ("") || strDateTime == GetPlaylist()->GetDateTime());
                }
                else
                {
                    bNotNeedUpdate = (bNotNeedUpdate || pUser->GetPlaylistDateTime() == ("") || strDateTime == pUser->GetPlaylistDateTime());
                }
            }
        }
        else
        {
            bNotNeedUpdate=true;    //如果新版本寻呼台，没有登录，不需要更新
        }
        #else
        bNotNeedUpdate = (bNotNeedUpdate || GetPlaylist()->GetDateTime() == ("") || strDateTime == GetPlaylist()->GetDateTime());
        #endif
        break;
    }
    case DT_TIMER:
        bNotNeedUpdate = (bNotNeedUpdate || g_Global.m_TimerScheme.GetDateTime() == ("") || strDateTime == g_Global.m_TimerScheme.GetDateTime());
        break;

    case DT_SECTION:
    {
        //******** 播放列表需要根据账户下发
        //先判断有没有登录，如果没有登录，那么不处理。
        #if SUPPORT_USER_SECTION_XML
        string loginAccount=GetUserAccount();
        if(loginAccount.length() > 0)
        {
            //判断登录账户是否存在
            LPCUserInfo  pUser = g_Global.m_Users.GetUserByAccount(loginAccount);
            if(pUser)
            {
                if(pUser->IsSuperUser())
                {
                    bNotNeedUpdate = (bNotNeedUpdate || g_Global.m_Sections.GetDateTime() == ("") || strDateTime == g_Global.m_Sections.GetDateTime());
                }
                else
                {
                    bNotNeedUpdate = (bNotNeedUpdate || pUser->GetSectionDateTime() == ("") || strDateTime == pUser->GetSectionDateTime());
                }
            }
        }
        else
        {
            bNotNeedUpdate=true;    //如果新版本寻呼台，没有登录，不需要更新
        }
        #else
        bNotNeedUpdate = (bNotNeedUpdate || g_Global.m_Sections.GetDateTime() == ("") || strDateTime == g_Global.m_Sections.GetDateTime());
        #endif
        break;
    }

    case DT_AUDIO_COLLECTOR:
        bNotNeedUpdate = (bNotNeedUpdate || g_Global.m_AudioCollectors.GetDateTime() == ("") || strDateTime == g_Global.m_AudioCollectors.GetDateTime());
        break;

    case DT_FIRE_COLLECTOR:
        bNotNeedUpdate = (bNotNeedUpdate || g_Global.m_FireCollectors.GetDateTime() == ("") || strDateTime == g_Global.m_FireCollectors.GetDateTime());
        break;
    
    case DT_USER:
        //printf("Device user file Data=%s,Local=%s\n",strDateTime.Data(),g_Global.m_Users.GetDateTime().Data());
        bNotNeedUpdate = (bNotNeedUpdate || g_Global.m_Users.GetDateTime() == ("") || strDateTime == g_Global.m_Users.GetDateTime());
        break;
    
    case DT_PAGER:
        bNotNeedUpdate = (bNotNeedUpdate || g_Global.m_Pagers.GetDateTime() == ("") || strDateTime == g_Global.m_Pagers.GetDateTime());
    break;

    default:
        break;
    }

    return !bNotNeedUpdate;
}

bool CSection::NeedGetFileInfo(DATETIME_FILE dt)
{
    return (IsOnline() && GetFileDateTime(dt) == (""));
}


void CSection::SetDateTimeFile(FileType ft)
{
    DATETIME_FILE dt = (DATETIME_FILE)(ft - 1);

    switch (dt)
    {
    case DT_GROUP:
        SetFileDateTime(dt, g_Global.m_Groups.GetDateTime());
        break;

    case DT_PLAYLIST:
    {
        #if APP_IS_LZY_LIMIT_STORAGE
        //判断登录账户是否存在
        LPCUserInfo  pUser = g_Global.m_Users.GetUserByAccount(GetUserAccount());
        if(pUser)
        {
            if(pUser->IsSuperUser())
            {
                SetFileDateTime(dt, GetPlaylist()->GetDateTime());
            }
            else
            {
                SetFileDateTime(dt, pUser->GetPlaylistDateTime());
            }
        }
        #else
        SetFileDateTime(dt, GetPlaylist()->GetDateTime());
        #endif
        break;
    }
    case DT_TIMER:
        SetFileDateTime(dt, g_Global.m_TimerScheme.GetDateTime());
        break;

    case DT_SECTION:
    {
        #if SUPPORT_USER_SECTION_XML
        //判断登录账户是否存在
        LPCUserInfo  pUser = g_Global.m_Users.GetUserByAccount(GetUserAccount());
        if(pUser)
        {
            if(pUser->IsSuperUser())
            {
                SetFileDateTime(dt, g_Global.m_Sections.GetDateTime());
            }
            else
            {
                SetFileDateTime(dt, pUser->GetSectionDateTime());
            }
        }
        #else
        SetFileDateTime(dt, g_Global.m_Sections.GetDateTime());
        #endif
        break;
    }
    case DT_AUDIO_COLLECTOR:
        SetFileDateTime(dt, g_Global.m_AudioCollectors.GetDateTime());
        break;

    case DT_FIRE_COLLECTOR:
        SetFileDateTime(dt, g_Global.m_FireCollectors.GetDateTime());
        break;
        
    case DT_USER:
        SetFileDateTime(dt, g_Global.m_Users.GetDateTime());
        break;

    case DT_PAGER:
        SetFileDateTime(dt, g_Global.m_Pagers.GetDateTime());
        break;

    default:
        break;
    }
}

void CSection::SetSyncSong(bool isSyncSong)
{
    m_isSyncSong = isSyncSong;

    if (isSyncSong == FALSE)
    {
        m_nFinishedSong		= 0;
        m_nSyncSongCount	= 0;
    }
}

CPlayList* CSection::GetPlaylist(void)
{
    return &g_Global.m_PlayList;
}

void CSection::SetUserAccount(string strUserAccount)
{
    //if( g_Global.m_Users.IsExistUser(strUserAccount) )
    if(m_strUserAccount != strUserAccount)
    {
        m_strUserAccount=strUserAccount;
        if(IsControlDevice())
        {
            printf("device:%s,SetUserAccount=%s\n",GetMac(),strUserAccount.data());
        }
    }
}

/*
0-未知 (0或者99) (非4G模块或者采用有线通讯）
5-极佳 (26~31),rssi>=-65dBm，实际rssi>=-61dBm
4-较好 (20~25),-65dBm<rssi<75dBm, 实际-62dBm<=rssi<=-73dBm
3-一般 (15~19),-75dBm<rssi<85dBm, 实际-74dBm<=rssi<=-83dBm
2-较差 (10~14),-85dBm<rssi<95dBm, 实际-84dBm<=rssi<=-93dBm
1-极差 (1~9),rssi<=-95dBm, 实际rssi<=-94dBm
*/
unsigned char  CSection::GetModule4GCSQ_LevelByRssi(unsigned char rssi)
{
    unsigned char signal_level=0; //0-未知 (0或者99) (非4G模块或者采用有线通讯）
    if(rssi>=1 && rssi<=9)
    {
        signal_level=1; //极差
    }
    else if(rssi>=10 && rssi<=14)
    {
        signal_level=2; //较差
    }
    else if(rssi>=15 && rssi<=19)
    {
        signal_level=3; //一般
    }
    else if(rssi>=20 && rssi<=25)
    {
        signal_level=4; //较好
    }
    else if(rssi>=26 && rssi<=31)
    {
        signal_level=5; //极佳
    }

    return signal_level;
}


bool  CSection::IsExtraFeatureValid()
{
    bool isValidPermission = true;
    switch (m_nDeviceModel)
    {
        case MODEL_FIRE_COLLECTOR_C:
        case MODEL_FIRE_COLLECTOR_F:
            if(!(m_nDeviceExtraFeature & DF_EXTRA_FIRE_COLLECTOR))
            {
                isValidPermission=false;
            }
        break;
        case MODEL_AUDIO_COLLECTOR_C:
        case MODEL_AUDIO_COLLECTOR_F:
            if(!(m_nDeviceExtraFeature & DF_EXTRA_AUDIO_COLLECTOR))
            {
                isValidPermission=false;
            }
        break;
        case MODEL_SEQUENCE_POWER_C:
        case MODEL_SEQUENCE_POWER_F:
            if(!(m_nDeviceExtraFeature & DF_EXTRA_POWER_SEQUENCE))
            {
                isValidPermission=false;
            }
        break;
        case MODEL_REMOTE_CONTROLER_C:
        case MODEL_REMOTE_CONTROLER_F:
            if(!(m_nDeviceExtraFeature & DF_EXTRA_REMOTE_CONTROLER))
            {
                isValidPermission=false;
            }
        break;
        case MODEL_GPS:
            if(!(m_nDeviceExtraFeature & DF_EXTRA_GPS_SYNCHRONIZER))
            {
                isValidPermission=false;
            }
        break;
    }
    return isValidPermission;
}

bool CSection::IsSupportRadioAndSoundCardOpus()
{
    if (strlen(m_szVersion) == 0) {
        return false;  // 空版本号默认不支持
    }
    //判断版本号是不是大于等于2.5.0819，如果是的话，则支持。如果版本号第一个字符是'V'或者'v',则需要去掉后再比较
    string strVersionMod = m_szVersion;
    if (strVersionMod[0] == 'V' || strVersionMod[0] == 'v') {
        strVersionMod = strVersionMod.substr(1);
    }
    // 简单比较（适用于标准格式版本号）
    return strVersionMod >= "2.5.0819";
}


/*
CSection& CSection::operator=(const CSection& section)
{

    m_nID					= section.m_nID;
    m_isChecked				= section.m_isChecked;
    m_nDeviceModel			= section.m_nDeviceModel;
    m_nVolume				= section.m_nVolume;
    m_nBmpID				= section.m_nBmpID;
    m_nProSource			= section.m_nProSource;
    m_nPlayStatus			= section.m_nPlayStatus;
    m_bTimerInvalid			= section.m_bTimerInvalid;
    m_nPlayMode				= section.m_nPlayMode;
    m_tLatestTime			= section.m_tLatestTime;
    m_tRestartTime			= section.m_tRestartTime;
    m_isUpgrading			= section.m_isUpgrading;
    m_strDateTimeGroup		= section.m_strDateTimeGroup;
    m_strDateTimePlayList	= section.m_strDateTimePlayList;
    m_strDateTimeTimer		= section.m_strDateTimeTimer;
    m_isSyncSong			= section.m_isSyncSong;
    m_nFinishedSong			= section.m_nFinishedSong;
    m_nSyncSongCount		= section.m_nSyncSongCount;

    strcpy(m_szSecName, section.m_szSecName);
    strcpy(m_szProName, section.m_szProName);
    strcpy(m_szBmpPath, section.m_szBmpPath);
    strcpy(m_szMac, section.m_szMac);
    strcpy(m_szIP, section.m_szIP);
    strcpy(m_szVersion, section.m_szVersion);


    return *this;
}
*/


/************************************************************/

CSections::CSections(void)
{
    //sectionsMutex = PTHREAD_MUTEX_INITIALIZER;
    m_nDeviceType = DEVICE_SECTION;

    m_strDateTime = ("");
}


CSections::~CSections(void)
{
    if(m_Sections.size() > 0)
    {
        ClearSections();
    }
    //pthread_mutex_destroy(&sectionsMutex);
}

CSection& CSections::GetSection(int nIndex)
{
    return m_Sections[nIndex];
}


void CSections::ClearSections(void)
{
    //QMutexLocker locker(&sectionQMutex);
    unique_lock<shared_timed_mutex> ulk_sectionQ(sectionQSharedMutex);              //写锁加锁
    //printf("enter mutex10\n");
    int nSecCount = GetSecCount();
#if 0
    for (int i=0; i<nSecCount; ++i)
    {
        m_Sections[i].Clear();
    }
#endif

    m_Sections.clear();

    m_MacID.clear();
    m_IpID.clear();
    //pthread_mutex_unlock(&sectionsMutex);
    //printf("leave mutex10\n");
}

void CSections::RemoveOfflineSections(void)
{
    //QMutexLocker locker(&sectionQMutex);
    unique_lock<shared_timed_mutex> ulk_sectionQ(sectionQSharedMutex);              //写锁加锁
    //printf("enter mutex11\n");
    vector<CSection>::iterator iter;
    for(iter=m_Sections.begin(); iter!=m_Sections.end();)
    {
        CSection& section = *iter;
        //分区不在线且不是音频混音器-解码器时，才删除
        if(!section.IsOnline() && section.GetDeviceModel() != MODEL_AUDIO_MIXER_DECODER && section.GetDeviceModel() != MODEL_AUDIO_MIXER_DECODER_C )
        {
            // 注意这句
            iter = m_Sections.erase(iter);
            continue;
        }
        else
        {
            iter++;
        }
    }

    for (int i=0; i<GetSecCount(); ++i)
    {
        m_Sections[i].SetID(i+1);
    }
    //locker.unlock();
    ulk_sectionQ.unlock();
    //printf("leave mutex11\n");
    UpdateMacID();
    UpdateIpID();
    
}

void CSections::AddSectionID(CSection& section)
{
    // IP已存在，如果当前的IP在线，才替换成当前的IP，也就是如果IP相同，在线的IP才加入到m_IpID当中
    if (m_IpID.count(section.GetIP()) > 0 && !section.IsOnline())
    {
        return;
    }

    m_IpID[section.GetIP()]	= section.GetID();
}

bool CSections::AddSection(CSection& section)
{
    int nMaxSectionCount = MAX_SECTION_COUNT_FORMAL;
    //QMutexLocker locker(&sectionQMutex);
    unique_lock<shared_timed_mutex> ulk_sectionQ(sectionQSharedMutex);              //写锁加锁
    //printf("enter mutex12\n");
    #if 0   //20220424只在WEB应答时限制
    // 试用版的，只有分区设备限制个数，其它设备不限制
    if (section.IsSectionDevice())
    {
        nMaxSectionCount = MAX_SECTION_COUNT;
    }
    #endif

    // 超过最大数
    if (GetSecCount() >= nMaxSectionCount)
    {
        //pthread_mutex_unlock(&sectionsMutex);
        //printf("leave mutex12\n");
        return FALSE;
    }
#if 1
    // MAC已存在，不能添加  zhuyg
    if(m_MacID.count(section.GetMac()) > 0)
    {
        //pthread_mutex_unlock(&sectionsMutex);
        //printf("leave mutex12\n");
        return FALSE;
    }
#endif
    m_Sections.push_back(section);
    m_MacID[section.GetMac()]	= section.GetID();

    AddSectionID(section);
    //pthread_mutex_unlock(&sectionsMutex);
//printf("leave mutex12\n");
    return TRUE;
}

bool CSections::RemoveSection(CSection& section)
{
    //QMutexLocker locker(&sectionQMutex);
    unique_lock<shared_timed_mutex> ulk_sectionQ(sectionQSharedMutex);              //写锁加锁
    //printf("enter mutex13\n");
    // 删除
    vector<CSection>::iterator iter;
    int i = 0;
    for(i=0, iter=m_Sections.begin(); iter!=m_Sections.end(); ++i, ++iter)
    {
        if(section.GetID() == i+1)
        {
            m_Sections.erase(iter);
            break;	// 务必加上break
        }
    }

    for (int i=0; i<GetSecCount(); ++i)
    {
        m_Sections[i].SetID(i+1);
    }

    //locker.unlock();
    ulk_sectionQ.unlock();

    UpdateMacID();
    UpdateIpID();

    return TRUE;
}


bool CSections::ReadFile()
{
    ClearSections();

    if (m_nDeviceType == DEVICE_SECTION)
    {                                        
        return (ReadSectionFile(HTTP_FILE_SECTION) && ReadSectionFile(HTTP_FILE_SECTION_LIMIT)); 
    }
    else if (m_nDeviceType == DEVICE_AUDIO_COLLECTOR)
    {
        return ReadAudioCollectorFile(HTTP_FILE_AUDIO_COLLECTOR);
    }
    else if (m_nDeviceType == DEVICE_FIRE_COLLECTOR)
    {
        return ReadFireCollectorFile(HTTP_FILE_FIRE_COLLECTOR);
    }
    else if (m_nDeviceType == DEVICE_SEQUENCE_POWER)
    {
        return ReadSequencePowerFile(HTTP_FILE_SEQUENCE_POWER);
    }
    else if (m_nDeviceType == DEVICE_PAGER)
    {
        return ReadPagerFile(HTTP_FILE_PAGER);
    }
    #if SUPPORT_REMOTE_CONTROLER
    else if (m_nDeviceType == DEVICE_REMOTE_CONTROLER)
    {
        return ReadRemoteControlerFile(HTTP_FILE_REMOTE_CONTROLER);
    }
    #endif
    #if SUPPORT_AUDIO_MIXER
    else if (m_nDeviceType == DEVICE_AUDIO_MIXER)
    {
        return ReadAudioMixerFile(HTTP_FILE_AUDIO_MIXER);
    }
    #endif
    #if SUPPORT_PHONE_GATEWAY
    else if (m_nDeviceType == DEVICE_PHONE_DEVICE)
    {
        return ReadPhoneGatewayFile(HTTP_FILE_PHONE_GATEWAY);
    }
    #endif
    #if SUPPORT_AMP_CONTROLER
    else if (m_nDeviceType == DEVICE_AMP_CONTROLER)
    {
        return ReadAmpControlerFile(HTTP_FILE_AMP_CONTROLER);
    }
    #endif
    #if SUPPORT_NOISE_DETECTOR
    else if (m_nDeviceType == DEVICE_NOISE_DETECTOR)
    {
        return ReadNoiseDetectorFile(HTTP_FILE_NOISE_DETECTOR);
    }
    #endif

    else if (m_nDeviceType == DEVICE_GPS)
    {
        return ReadGpsFile(HTTP_FILE_GPS);
    }

    return FALSE;
}


bool CSections::WriteFile(bool bUpdateDateTime)
{
    if (m_nDeviceType == DEVICE_SECTION)
    {
        WriteSectionFile(HTTP_FILE_SECTION, bUpdateDateTime);
        return WriteSectionFile(HTTP_FILE_SECTION_LIMIT, bUpdateDateTime);
    }
    else if (m_nDeviceType == DEVICE_AUDIO_COLLECTOR)
    {
        return WriteAudioCollectorFile(HTTP_FILE_AUDIO_COLLECTOR, bUpdateDateTime);
    }
    else if (m_nDeviceType == DEVICE_FIRE_COLLECTOR)
    {
        return WriteFireCollectorFile(HTTP_FILE_FIRE_COLLECTOR, bUpdateDateTime);
    }
    else if (m_nDeviceType == DEVICE_SEQUENCE_POWER)
    {
        return WriteSequencePowerFile(HTTP_FILE_SEQUENCE_POWER, bUpdateDateTime);
    }
    else if (m_nDeviceType == DEVICE_PAGER)
    {
        return WritePagerFile(HTTP_FILE_PAGER, bUpdateDateTime);
    }
    #if SUPPORT_REMOTE_CONTROLER
    else if (m_nDeviceType == DEVICE_REMOTE_CONTROLER)
    {
        return WriteRemoteControlerFile(HTTP_FILE_REMOTE_CONTROLER, bUpdateDateTime);
    }
    #endif
    #if SUPPORT_AUDIO_MIXER
    else if (m_nDeviceType == DEVICE_AUDIO_MIXER)
    {
        return WriteAudioMixerFile(HTTP_FILE_AUDIO_MIXER, bUpdateDateTime);
    }
    #endif
    #if SUPPORT_PHONE_GATEWAY
    else if (m_nDeviceType == DEVICE_PHONE_DEVICE)
    {
        return WritePhoneGatewayFile(HTTP_FILE_PHONE_GATEWAY, bUpdateDateTime);
    }
    #endif
    #if SUPPORT_AMP_CONTROLER
    else if (m_nDeviceType == DEVICE_AMP_CONTROLER)
    {
        return WriteAmpControlerFile(HTTP_FILE_AMP_CONTROLER, bUpdateDateTime);
    }
    #endif
    #if SUPPORT_NOISE_DETECTOR
    else if (m_nDeviceType == DEVICE_NOISE_DETECTOR)
    {
        return WriteNoiseDetectorFile(HTTP_FILE_NOISE_DETECTOR, bUpdateDateTime);
    }
    #endif

    else if (m_nDeviceType == DEVICE_GPS)
    {
        return WriteGpsFile(HTTP_FILE_GPS, bUpdateDateTime);
    }

    return FALSE;
}


bool CSections::ReadSectionFile(string strFileName)
{
    TiXmlDocument* xmlSection = new TiXmlDocument;
    char szPathName[STR_MAX_PATH] = {0};

    // 路径 保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    if(xmlSection->LoadFile((char*)szPathName))
    {
        TiXmlElement* Sections = xmlSection->FirstChildElement();
        if(Sections == NULL)
        {
            return false;
        }

        SetDateTime(CMyString(Sections->Attribute("DateTime")));

        int nSecCount=0;
        for(TiXmlElement* elem=Sections->FirstChildElement(); elem!=NULL;
            elem=elem->NextSiblingElement())
        {
            if( strFileName == HTTP_FILE_SECTION_LIMIT)
            {
                nSecCount++;
                continue;
            }
            LPCSTR  lpszMac     = elem->Attribute("Mac");
            LPCSTR  lpszIP      = elem->Attribute("IP");
            LPCSTR  lpszNetMode = elem->Attribute("NetMode");
            LPCSTR  lpszICCID   = elem->Attribute("ICCID");
            LPCSTR  lpszName    = elem->Attribute("Name");

#if 0
            // zhuyg 转成gb2312,避免从xml读取出来时内存不足程序崩溃，gb2312中文2个字节，gbk3个字节
            string strName = StringToGB2312(lpszName);
#else
            string strName(lpszName);
#endif
            LPCSTR  lpszMonitorMac = elem->Attribute("MonitorMac");

            CSection section(GetSecCount()+1,
                             (DeviceModel)atoi(elem->Attribute("Model")),
                             lpszMac,
                             lpszIP,
                             0,
                             PRO_OFFLINE,
                             strName.data());
            if(lpszMonitorMac!=NULL)       //因为此字段是后面加的，为了保证之前的XML文件读取不出错，需要加入判断
                section.SetMonitorMac(lpszMonitorMac);
            if(lpszNetMode!=NULL)
            {
                NetworkMode netMode = (NetworkMode)atoi(lpszNetMode);
                if( netMode == NETWORK_UNKNOWN )
                    netMode = NETWORK_UDP;
                section.SetReservedWord(netMode<<4);
            }
            if(lpszICCID!=NULL)
            {
                //将const char*转为char*
                char* pICCID = const_cast<char*>(lpszICCID);
                section.SetModule4G_ICCID(pICCID);
            }
            
            //如果不存在这个类型的设备，不加入
            if(g_Global.m_ModelToDevices.count(section.GetDeviceModel())>0)
            {
                AddSection(section);
            }
        }

        xmlSection->Clear();
        delete xmlSection;

        if( strFileName == HTTP_FILE_SECTION_LIMIT )
        {
            if( APP_IS_TRIAL )
            {
                if( nSecCount > MAX_SECTION_COUNT )
                {
                    WriteSectionFile(HTTP_FILE_SECTION_LIMIT,true);
                }
            }
            else if( nSecCount < GetSecCount() )
            {
                WriteSectionFile(HTTP_FILE_SECTION_LIMIT,true);
            }
        }
        return true;
    }
    xmlSection->Clear();
    delete xmlSection;
    return false;
}

bool CSections::WriteSectionFile(string strFileName, bool bUpdateDateTime)
{
    //QMutexLocker locker(&sectionQMutex);
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    //printf("enter mutex14\n");
    TiXmlDocument xmlSection;
    int nSecCount = GetSecCount();

    char szPathName[STR_MAX_PATH] = {0};
    // HTTP路径 保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName); //组成可远端下载的URL

    if(strFileName == HTTP_FILE_SECTION_LIMIT)
    {
        if(nSecCount>MAX_SECTION_COUNT)
        {
            nSecCount=MAX_SECTION_COUNT;
        }
    }

    // 头字段
    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0", "utf-8", "no");
    xmlSection.LinkEndChild(dec);

    // 描述
    TiXmlComment* com = new TiXmlComment("save for section info");
    xmlSection.LinkEndChild(com);

    TiXmlElement* Sections = new TiXmlElement("Sections");
    Sections->SetAttribute("SectionCount", nSecCount);
    Sections->SetAttribute("DateTime"    , GetDateTime().C_Str());
    xmlSection.LinkEndChild(Sections);

    for(int i=0; i<nSecCount; ++i)
    {
        CSection& sec = GetSection(i);

        TiXmlElement* Section = new TiXmlElement("Section");

        // gb2312 -> utf8
        string strName = StringToUTF8(GetSectionName(i));
        Section->SetAttribute("SecID",   GetSectionID(i));
        Section->SetAttribute("Model",   GetSectionDeviceModel(i));
        Section->SetAttribute("Mac"  ,   GetSectionMac(i));
        Section->SetAttribute("IP"   ,   GetSectionIP(i));
        NetworkMode netMode = GetSectionNetworkMode(i);
        Section->SetAttribute("NetMode", netMode == NETWORK_UNKNOWN ? NETWORK_UDP:netMode);
        Section->SetAttribute("ICCID",   sec.GetModule4G_ICCID());
        Section->SetAttribute("Name",    strName.data());
        Section->SetAttribute("MonitorMac",sec.GetMonitorMac());
        Section->SetAttribute("BmpID",   2);
        Section->SetAttribute("BmpPath", "");
        Section->SetAttribute("GroupID", "");
        Sections->LinkEndChild(Section);
    }

    bool saveFileOK=true;
    if(xmlSection.SaveFile(szPathName))
    {
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlSection.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    
    return saveFileOK;
}


bool CSections::ReadAudioCollectorFile(string strFileName)
{
    TiXmlDocument* xmlAudio = new TiXmlDocument;
    CHAR    szPathName[STR_MAX_PATH];

    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName); 

    if(xmlAudio->LoadFile(szPathName))
    {
        TiXmlElement* AudioCollectors = xmlAudio->FirstChildElement();
        SetDateTime(AudioCollectors->Attribute("DateTime"));

        int nCollector = 0;
        for(TiXmlElement* AudioCollector=AudioCollectors->FirstChildElement(); AudioCollector!=NULL;
            AudioCollector=AudioCollector->NextSiblingElement())
        {
            LPCSTR  lpszMac     = AudioCollector->Attribute("Mac");
            LPCSTR  lpszIP      = AudioCollector->Attribute("IP");
            LPCSTR  lpszSrcName = AudioCollector->Attribute("SrcName");
            UINT    nSampleRate = atoi(AudioCollector->Attribute("SampleRate"));
            UINT    nChannel    = atoi(AudioCollector->Attribute("Channel"));
            
            DeviceModel model =  MODEL_AUDIO_COLLECTOR_A;
            if( AudioCollector->Attribute("Model")!=NULL )
            {
                model = (DeviceModel)atoi(AudioCollector->Attribute("Model"));
            }

            CSection audioCollector(nCollector + 1,
                                    model,
                                    lpszMac,
                                    lpszIP,
                                    0,
                                    PRO_OFFLINE,
                                    lpszSrcName);

            UINT   nTriggerSwitch = AudioCollector->Attribute("TriggerSwitch")?atoi(AudioCollector->Attribute("TriggerSwitch")):0;
            UINT   nTriggerChannelId = AudioCollector->Attribute("TriggerChannelId")?atoi(AudioCollector->Attribute("TriggerChannelId")):1;
            UINT   nTriggerZoneVolume = AudioCollector->Attribute("TriggerZoneVolume")?atoi(AudioCollector->Attribute("TriggerZoneVolume")):50;
            
            UINT   nPriority = AudioCollector->Attribute("Priority")?atoi(AudioCollector->Attribute("Priority")):1;
            audioCollector.m_pAudioCollector->SetPriority(nPriority);

            audioCollector.m_pAudioCollector->SetTriggerSwitch(nTriggerSwitch);
            audioCollector.m_pAudioCollector->SetTriggerChannelId(nTriggerChannelId);
            audioCollector.m_pAudioCollector->SetTriggerVolume(nTriggerZoneVolume);

            //SelectedSections
            TiXmlElement* SelectedSectionsElement=AudioCollector->FirstChildElement("SelectedSections");
            if(SelectedSectionsElement)
            {
                //Section
                for(TiXmlElement* SectionElement=SelectedSectionsElement->FirstChildElement(); SectionElement!=NULL;
                    SectionElement=SectionElement->NextSiblingElement())
                {
                    LPCSTR		lpszSecMac	 = SectionElement->Attribute("Mac");
                    audioCollector.m_pAudioCollector->AddSection(lpszSecMac);
                }
            }

            //ChannelInfos
            TiXmlElement* ChannelInfosElement=AudioCollector->FirstChildElement("ChannelInfos");
            if(ChannelInfosElement)
            {
                //Section
                for(TiXmlElement* ChannelElement=ChannelInfosElement->FirstChildElement(); ChannelElement!=NULL;
                    ChannelElement=ChannelElement->NextSiblingElement())
                {
                    LPCSTR		lpszID	 = ChannelElement->Attribute("ID");
                    LPCSTR	    lpszName = ChannelElement->Attribute("Name");
                    if(strlen(lpszName)>0)
                    {
                        audioCollector.m_pAudioCollector->SetChannelNameById(atoi(lpszID),lpszName);
                    }
                }
            }



            //audioCollector.m_pAudioCollector->SetSampleRate(nSampleRate);
            audioCollector.m_pAudioCollector->SetChannel((nChannel > 0) ? nChannel : 1);   //1 = CHANNEL_SINGLE 避免枚举与非枚举警告

            AddSection(audioCollector);

            nCollector++;
        }
    }
    else
    {
        xmlAudio->Clear();
        delete xmlAudio;
        return FALSE;
    }

    xmlAudio->Clear();
    delete xmlAudio;
    return TRUE;
}

bool CSections::WriteAudioCollectorFile(string strFileName, bool bUpdateDateTime)
{
    TiXmlDocument xmlAudio;
    int nSecCount = GetSecCount();

    CHAR	szPathName[STR_MAX_PATH] = {0};
    // HTTP路径 保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    char *szSaveInfo;
    char *szHeadElement;
    char *szCount;
    char *szElement;

    szSaveInfo = (char *)"Save for Audio Collector info";
    szHeadElement = (char *)"AudioCollectors";
    szCount = (char *)"CollectorCount";
    szElement = (char *)"AudioCollector";
    

    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0","utf-8","no");
    xmlAudio.LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment(szSaveInfo);
    xmlAudio.LinkEndChild(com);

    // AudioCollectors
    TiXmlElement* AudioCollectors = new TiXmlElement(szHeadElement);
    AudioCollectors->SetAttribute(szCount, nSecCount);
    AudioCollectors->SetAttribute("DateTime", GetDateTime().Data());
    xmlAudio.LinkEndChild(AudioCollectors);

    // AudioCollector
    for(int i=0; i<nSecCount; i++)
    {
        CSection& audioCollector = GetSection(i);
        TiXmlElement* AudioCollector = new TiXmlElement(szElement);
        AudioCollector->SetAttribute("Model", audioCollector.GetDeviceModel());
        AudioCollector->SetAttribute("Mac", audioCollector.GetMac());
        AudioCollector->SetAttribute("IP", audioCollector.GetIP());
        AudioCollector->SetAttribute("SrcID", (int)audioCollector.m_pAudioCollector->GetSourceID());
        AudioCollector->SetAttribute("SrcName",audioCollector.GetUTFName().data());
        AudioCollector->SetAttribute("SampleRate", audioCollector.m_pAudioCollector->GetSampleRate());
        AudioCollector->SetAttribute("Channel", audioCollector.m_pAudioCollector->GetChannel());
    
        AudioCollector->SetAttribute("TriggerSwitch", audioCollector.m_pAudioCollector->GetTriggerSwitch());
        AudioCollector->SetAttribute("TriggerChannelId", audioCollector.m_pAudioCollector->GetTriggerChannelId());
        AudioCollector->SetAttribute("TriggerZoneVolume", audioCollector.m_pAudioCollector->GetTriggerVolume());

        AudioCollector->SetAttribute("Priority", audioCollector.m_pAudioCollector->GetPriority());

        TiXmlElement* SelectSections = new TiXmlElement("SelectedSections");
        SelectSections->SetAttribute("SectionCount", audioCollector.m_pAudioCollector->GetSectionCount());
        AudioCollector->LinkEndChild(SelectSections);

        for(int k=0;k<audioCollector.m_pAudioCollector->GetSectionCount();k++)
        {
            TiXmlElement* section = new TiXmlElement("Section");
            section->SetAttribute("Mac", audioCollector.m_pAudioCollector->GetSectionMac(k).Data());
            SelectSections->LinkEndChild(section);
        }


        TiXmlElement* ChannelInfos = new TiXmlElement("ChannelInfos");
        ChannelInfos->SetAttribute("ChannelCount", AUDIO_COLLECTOR_CHANNELS);
        AudioCollector->LinkEndChild(ChannelInfos);

        for(int k=0;k<AUDIO_COLLECTOR_CHANNELS;k++)
        {
            TiXmlElement* channel = new TiXmlElement("Channel");
            channel->SetAttribute("ID", k+1);
            channel->SetAttribute("Name", audioCollector.m_pAudioCollector->GetChannelNameById(k+1).data());
            ChannelInfos->LinkEndChild(channel);
        }

        AudioCollectors->LinkEndChild(AudioCollector);
    }

    bool saveFileOK=true;
    if(xmlAudio.SaveFile(szPathName))
    {
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlAudio.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    return saveFileOK;
}

bool CSections::ReadFireCollectorFile(string strFileName)
{
    TiXmlDocument* xmlFireCollector = new TiXmlDocument;
    CHAR	szPathName[STR_MAX_PATH]	= {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    if(xmlFireCollector->LoadFile(szPathName))
    {
        TiXmlElement* FireAlarm = xmlFireCollector->FirstChildElement();
        SetDateTime(FireAlarm->Attribute("DateTime"));

        int nCollector = 0;

        // FireCollector
        for(TiXmlElement* FireCollector=FireAlarm->FirstChildElement(); FireCollector!=NULL;
            FireCollector=FireCollector->NextSiblingElement())
        {
            LPCSTR		lpszMac		= FireCollector->Attribute("Mac");
            LPCSTR		lpszIP		= FireCollector->Attribute("IP");
            LPCSTR		lpszName	= FireCollector->Attribute("Name");

            DeviceModel model =  MODEL_FIRE_COLLECTOR_A;
            if( FireCollector->Attribute("Model")!=NULL )
            {
                model = (DeviceModel)atoi(FireCollector->Attribute("Model"));
            }

            CSection fireCollector(nCollector + 1,
                                   model,
                                   lpszMac,
                                   lpszIP,
                                   0,
                                   PRO_OFFLINE,
                                   lpszName);


            // 进入Channel
            int nChannel = 0;
            for(TiXmlElement* Channel=FireCollector->FirstChildElement(); Channel!=NULL;
                Channel=Channel->NextSiblingElement())
            {
                CFireChannel fireChannel(nChannel + 1);
                LPCSTR szName = Channel->Attribute("Name");
                fireChannel.SetName(szName);
                fireChannel.SetSoundPathName(Channel->Attribute("Sound"));
                fireChannel.SetTriggerMode(atoi(Channel->Attribute("Trigger")));

                // 进入Section
                for(TiXmlElement* Section=Channel->FirstChildElement(); Section!=NULL;
                    Section=Section->NextSiblingElement())
                {
                    string strMac = Section->Attribute("Mac");
                    string strIP  = Section->Attribute("IP");
                    fireChannel.AddSection(strMac,strIP);
                }

                fireCollector.m_pFireCollector->AddChannel(fireChannel);
                nChannel++;
            }

            fireCollector.m_pFireCollector->UpdateTriggerMode();
            AddSection(fireCollector);
            nCollector++;
        }
    }
    else
    {
        xmlFireCollector->Clear();
        delete xmlFireCollector;
        return FALSE;
    }

    xmlFireCollector->Clear();
    delete xmlFireCollector;
    return TRUE;

}



bool CSections::WriteFireCollectorFile(string strFileName, bool bUpdateDateTime)
{
    TiXmlDocument xmlFireCollector;
    int		nColCount		= GetSecCount();
    int		nChannelCount	= 0;

    CHAR	szPathName[STR_MAX_PATH] = {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    TiXmlDeclaration *dec = new TiXmlDeclaration("1.0","utf-8","no");
    xmlFireCollector.LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment("Save for Audio Collector info");
    xmlFireCollector.LinkEndChild(com);

    TiXmlElement *FireAlarm = new TiXmlElement("FireAlarm");
    xmlFireCollector.LinkEndChild(FireAlarm);
    FireAlarm->SetAttribute("CollectorCount", nColCount);
    FireAlarm->SetAttribute("DateTime", GetDateTime().Data());

    // FireCollector
    for(int i=0; i<nColCount; i++)
    {
        CSection& section = GetSection(i);
        std::shared_ptr<CFireCollector> pFireCollector = section.m_pFireCollector;
        nChannelCount = pFireCollector->GetChannelCount();

        // 告警器
        TiXmlElement* FireCollector = new TiXmlElement("FireCollector");
        FireAlarm->LinkEndChild(FireCollector);
        FireCollector->SetAttribute("Model", section.GetDeviceModel());
        FireCollector->SetAttribute("Mac", section.GetMac());
        FireCollector->SetAttribute("IP", section.GetIP());

        string strName = section.GetName();
        string strUtf8Name = StringToUTF8(strName.data());
        FireCollector->SetAttribute("Name", strUtf8Name.data());
        FireCollector->SetAttribute("ChannelCount", nChannelCount);

        for(int j=0; j<nChannelCount; j++)
        {
            // 每个告警器的对应的32个通道
            CFireChannel& channel = pFireCollector->GetChannel(j);
            int	nSectionCout =channel.GetSectionCount();

            TiXmlElement* Channel = new TiXmlElement("Channel");
            FireCollector->LinkEndChild(Channel);
            Channel->SetAttribute("ID", channel.GetID());
            Channel->SetAttribute("Name", channel.GetName());
            Channel->SetAttribute("SectionCount", nSectionCout);
            Channel->SetAttribute("Trigger", channel.GetTriggerMode());

            Channel->SetAttribute("Sound", channel.GetSoundPathName().C_Str());

            for(int k=0; k<nSectionCout; k++)
            {
                TiXmlElement* Section = new TiXmlElement("Section");
                Channel->LinkEndChild(Section);
                Section->SetAttribute("Mac",channel.GetSectionMac(k).C_Str());
                Section->SetAttribute("IP",channel.GetSectionIP(k).C_Str());
            }
        }
    }

    bool saveFileOK=true;
    if(xmlFireCollector.SaveFile(szPathName))
    {
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlFireCollector.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    return saveFileOK;
}




bool CSections::ReadSequencePowerFile(string strFileName)
{
    TiXmlDocument* xmlSequencePower = new TiXmlDocument;
    CHAR	szPathName[STR_MAX_PATH]	= {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    if(xmlSequencePower->LoadFile(szPathName))
    {
        TiXmlElement* Power = xmlSequencePower->FirstChildElement();
        SetDateTime(Power->Attribute("DateTime"));

        int nPower = 0;

        // SequencePower
        for(TiXmlElement* SequencePower=Power->FirstChildElement(); SequencePower!=NULL;
            SequencePower=SequencePower->NextSiblingElement())
        {
            LPCSTR		lpszMac		= SequencePower->Attribute("Mac");
            LPCSTR		lpszIP		= SequencePower->Attribute("IP");
            LPCSTR		lpszName	= SequencePower->Attribute("Name");

            DeviceModel model =  MODEL_SEQUENCE_POWER_A;
            if( SequencePower->Attribute("Model")!=NULL )
            {
                model = (DeviceModel)atoi(SequencePower->Attribute("Model"));
            }

            CSection sequencePwr(nPower + 1,
                                   model,
                                   lpszMac,
                                   lpszIP,
                                   0,
                                   PRO_OFFLINE,
                                   lpszName);

            sequencePwr.m_pSequencePower->SetControlMode(atoi(SequencePower->Attribute("ControlMode")));

            // 进入Channel
            int nChannel = 0;
            for(TiXmlElement* Channel=SequencePower->FirstChildElement(); Channel!=NULL;
                Channel=Channel->NextSiblingElement())
            {
                CSequencePowerChannel seqPwrChannel(nChannel + 1);
                LPCSTR szName = Channel->Attribute("Name");
                seqPwrChannel.SetName(szName);

                sequencePwr.m_pSequencePower->AddChannel(seqPwrChannel);
                nChannel++;
            }

            //fireCollector.m_pFireCollector->UpdateTriggerMode();
            AddSection(sequencePwr);
            nPower++;
        }
    }
    else
    {
        xmlSequencePower->Clear();
        delete xmlSequencePower;
        return FALSE;
    }

    xmlSequencePower->Clear();
    delete xmlSequencePower;
    return TRUE;

}



bool CSections::WriteSequencePowerFile(string strFileName, bool bUpdateDateTime)
{
    TiXmlDocument xmlSequencePower;
    int		nColCount		= GetSecCount();
    int		nChannelCount	= 0;
    int     nControlMode    = 0;

    CHAR	szPathName[STR_MAX_PATH] = {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    TiXmlDeclaration *dec = new TiXmlDeclaration("1.0","utf-8","no");
    xmlSequencePower.LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment("Save for Sequence Power info");
    xmlSequencePower.LinkEndChild(com);

    TiXmlElement *Power = new TiXmlElement("Power");
    xmlSequencePower.LinkEndChild(Power);
    Power->SetAttribute("PowerCount", nColCount);
    Power->SetAttribute("DateTime", GetDateTime().Data());

    // FireCollector
    for(int i=0; i<nColCount; i++)
    {
        CSection& section = GetSection(i);
        std::shared_ptr<CSequencePower> pSequencePower = section.m_pSequencePower;
        nChannelCount = pSequencePower->GetRealChannelCnt();
        nControlMode  = pSequencePower->GetControlMode();

        // 告警器
        TiXmlElement* SequencePower = new TiXmlElement("SequencePower");
        Power->LinkEndChild(SequencePower);
        SequencePower->SetAttribute("Model", section.GetDeviceModel());
        SequencePower->SetAttribute("Mac", section.GetMac());
        SequencePower->SetAttribute("IP", section.GetIP());

        string strName = section.GetName();
        string strUtf8Name = StringToUTF8(strName.data());
        SequencePower->SetAttribute("Name", strUtf8Name.data());
        SequencePower->SetAttribute("ControlMode", nControlMode);
        SequencePower->SetAttribute("ChannelCount", nChannelCount);

        for(int j=0; j<nChannelCount; j++)
        {
            CSequencePowerChannel& channel = pSequencePower->GetChannel(j);

            TiXmlElement* Channel = new TiXmlElement("Channel");
            SequencePower->LinkEndChild(Channel);
            Channel->SetAttribute("ID", channel.GetID());
            Channel->SetAttribute("Name", channel.GetName());
        }
    }

    bool saveFileOK=true;
    if(xmlSequencePower.SaveFile(szPathName))
    {
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlSequencePower.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    return saveFileOK;
}




bool CSections::ReadPagerFile(string strFileName)
{
    TiXmlDocument* xmlPager = new TiXmlDocument;
    CHAR	szPathName[STR_MAX_PATH]	= {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    if(xmlPager->LoadFile(szPathName))
    {
        TiXmlElement* Pager = xmlPager->FirstChildElement();
        SetDateTime(Pager->Attribute("DateTime"));

        int nPager = 0;

        // Pager
        for(TiXmlElement* PagerElement=Pager->FirstChildElement(); PagerElement!=NULL;
            PagerElement=PagerElement->NextSiblingElement())
        {
            LPCSTR		lpszMac		= PagerElement->Attribute("Mac");
            LPCSTR		lpszIP		= PagerElement->Attribute("IP");
            LPCSTR		lpszName	= PagerElement->Attribute("Name");

            DeviceModel model =  MODEL_PAGER_A;
            if( PagerElement->Attribute("Model")!=NULL )
            {
                model = (DeviceModel)atoi(PagerElement->Attribute("Model"));
            }

            CSection PagerDevice(nPager + 1,
                                   model,
                                   lpszMac,
                                   lpszIP,
                                   0,
                                   PRO_OFFLINE,
                                   lpszName);
            AddSection(PagerDevice);
            nPager++;
        }
    }
    else
    {
        xmlPager->Clear();
        delete xmlPager;
        return FALSE;
    }

    xmlPager->Clear();
    delete xmlPager;
    return TRUE;

}



bool CSections::WritePagerFile(string strFileName, bool bUpdateDateTime)
{
    TiXmlDocument xmlPager;
    int		nColCount		= GetSecCount();
    int		nChannelCount	= 0;
    int     nControlMode    = 0;

    CHAR	szPathName[STR_MAX_PATH] = {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    char *szSaveInfo;
    char *szHeadElement;
    char *szCount;
    char *szElement;

    szSaveInfo = (char *)"Save for Pager info";
    szHeadElement = (char *)"Pagers";
    szCount = (char *)"PagerCount";
    szElement = (char *)"Pager";
    

    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0","utf-8","no");
    xmlPager.LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment(szSaveInfo);
    xmlPager.LinkEndChild(com);

    // Pagers
    TiXmlElement* Pagers = new TiXmlElement(szHeadElement);
    Pagers->SetAttribute(szCount, nColCount);
    Pagers->SetAttribute("DateTime", GetDateTime().Data());
    xmlPager.LinkEndChild(Pagers);

    // Pager
    for(int i=0; i<nColCount; i++)
    {
        CSection& PagerDevice = GetSection(i);
        TiXmlElement* PagerElement = new TiXmlElement(szElement);
        PagerElement->SetAttribute("Model", PagerDevice.GetDeviceModel());
        PagerElement->SetAttribute("Mac", PagerDevice.GetMac());
        PagerElement->SetAttribute("IP", PagerDevice.GetIP());
        PagerElement->SetAttribute("Name",PagerDevice.GetUTFName().data());
        Pagers->LinkEndChild(PagerElement);
    }

    bool saveFileOK=true;
    if(xmlPager.SaveFile(szPathName))
    {
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlPager.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    return saveFileOK;
}


#if SUPPORT_REMOTE_CONTROLER
bool CSections::ReadRemoteControlerFile(string strFileName)
{
    TiXmlDocument* xmlRemoteControler = new TiXmlDocument;
    CHAR	szPathName[STR_MAX_PATH]	= {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    if(xmlRemoteControler->LoadFile(szPathName))
    {
        TiXmlElement* RemoteControlers = xmlRemoteControler->FirstChildElement();
        SetDateTime(RemoteControlers->Attribute("DateTime"));

        int remoteCnt = 0;
        // Remote
        for(TiXmlElement* RemoteElement=RemoteControlers->FirstChildElement(); RemoteElement!=NULL;
            RemoteElement=RemoteElement->NextSiblingElement())
        {
            LPCSTR		lpszMac		= RemoteElement->Attribute("Mac");
            LPCSTR		lpszIP		= RemoteElement->Attribute("IP");
            LPCSTR		lpszName	= RemoteElement->Attribute("Name");

            CSection RemoteDevice(++remoteCnt,
                                   MODEL_REMOTE_CONTROLER,
                                   lpszMac,
                                   lpszIP,
                                   0,
                                   PRO_OFFLINE,
                                   lpszName);

            //tasks
            TiXmlElement* TasksElement=RemoteElement->FirstChildElement("Tasks");

            int taskCnt=0;
            //task
            for(TiXmlElement* TaskElement=TasksElement->FirstChildElement(); TaskElement!=NULL;
                TaskElement=TaskElement->NextSiblingElement())
            {
                CRemoteControlTask task(++taskCnt);

                LPCSTR		lpszName	 = TaskElement->Attribute("Name");
                LPCSTR      lpszSourceType = TaskElement->Attribute("SourceType");
                LPCSTR		lpszPlayMode = TaskElement->Attribute("PlayMode");
                LPCSTR		lpszVolume	 = TaskElement->Attribute("Volume");

                task.SetTaskName(lpszName);
                task.SetPlayMode(atoi(lpszPlayMode));
                task.SetVolume(atoi(lpszVolume));
                task.SetSourceType((TIMER_SOURCE_TYPE)atoi(lpszSourceType));

                //SelectedSections
                TiXmlElement* SelectedSectionsElement=TaskElement->FirstChildElement("SelectedSections");
                //Section
                for(TiXmlElement* SectionElement=SelectedSectionsElement->FirstChildElement(); SectionElement!=NULL;
                    SectionElement=SectionElement->NextSiblingElement())
                {
                    LPCSTR		lpszSecMac	 = SectionElement->Attribute("Mac");
                    
                    task.AddSection(lpszSecMac);
                }

                //SelectedGroups
                TiXmlElement* SelectedGroupsElement=TaskElement->FirstChildElement("SelectedGroups");
                //Group
                for(TiXmlElement* GroupElement=SelectedGroupsElement->FirstChildElement(); GroupElement!=NULL;
                    GroupElement=GroupElement->NextSiblingElement())
                {
                    LPCSTR		lpszGroupID	 = GroupElement->Attribute("ID");

                    task.AddGroup(lpszGroupID);
                }

                if(task.GetSourceType() == TIMER_SOURCE_TYPE_LOCAL_SONG)
                {
                    //SelectedSongs
                    TiXmlElement* SelectedSongsElement=TaskElement->FirstChildElement("SelectedSongs");
                    //Song
                    for(TiXmlElement* SongElement=SelectedSongsElement->FirstChildElement(); SongElement!=NULL;
                        SongElement=SongElement->NextSiblingElement())
                    {
                        LPCSTR		lpszPathName	 = SongElement->Attribute("PathName");
                        
                        task.AddSong(lpszPathName);
                    }
                }
                else if(task.GetSourceType() == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
                {
                    //selectedAudioCollector
                    TiXmlElement* SelectedAudioCollector = TaskElement->FirstChildElement("SelectedAudioCollector");
                    if(SelectedAudioCollector!=NULL)
                    {
                        ST_TIMER_AUDIO_COLLECTOR_INFO st_audioCollector;
                        sprintf(st_audioCollector.mac,"%s",SelectedAudioCollector->Attribute("Mac")?SelectedAudioCollector->Attribute("Mac"):"");
                        //判断此采集器是否存在
                        if(g_Global.m_AudioCollectors.GetSectionByMac(st_audioCollector.mac)!=NULL)
                        {
                            st_audioCollector.channelId = atoi(SelectedAudioCollector->Attribute("ChannelId")?SelectedAudioCollector->Attribute("ChannelId"):"");
                        }
                        else
                        {
                            memset(&st_audioCollector,0,sizeof(st_audioCollector));
                        }

                        task.SetAudioCollector(st_audioCollector);
                    }
                }

                
                RemoteDevice.m_pRemoteControler->AddTask(task);
            }

            //keys
            TiXmlElement* keysElement=RemoteElement->FirstChildElement("Keys");
            //int keyCnt=0;
            for(TiXmlElement* keyElement=keysElement->FirstChildElement(); keyElement!=NULL;
                keyElement=keyElement->NextSiblingElement())
            {
                LPCSTR	lpszKeyID	 = keyElement->Attribute("ID");
                LPCSTR	lpszKeyEvent = keyElement->Attribute("Event");

                RemoteDevice.m_pRemoteControler->SetKeyEvent(atoi(lpszKeyID),atoi(lpszKeyEvent));
            }

            //todo 一些错误校正

            AddSection(RemoteDevice);
        }
    }
    else
    {
        xmlRemoteControler->Clear();
        delete xmlRemoteControler;
        return FALSE;
    }

    xmlRemoteControler->Clear();
    delete xmlRemoteControler;

    return TRUE;
}



bool CSections::WriteRemoteControlerFile(string strFileName, bool bUpdateDateTime)
{
    TiXmlDocument xmlRemoteControler;
    int		nColCount		= GetSecCount();
    int		nChannelCount	= 0;
    int     nControlMode    = 0;

    CHAR	szPathName[STR_MAX_PATH] = {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    char *szSaveInfo;
    char *szHeadElement;
    char *szCount;
    char *szElement;

    szSaveInfo = (char *)"Save for RemoteControler info";
    szHeadElement = (char *)"RemoteControlers";
    szCount = (char *)"RemoteCount";
    szElement = (char *)"RemoteControler";
    
    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0","utf-8","no");
    xmlRemoteControler.LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment(szSaveInfo);
    xmlRemoteControler.LinkEndChild(com);

    TiXmlElement* RemoteControlers = new TiXmlElement(szHeadElement);
    RemoteControlers->SetAttribute(szCount, nColCount);
    RemoteControlers->SetAttribute("DateTime", GetDateTime().Data());
    xmlRemoteControler.LinkEndChild(RemoteControlers);

    for(int i=0; i<nColCount; i++)
    {
        CSection& RemoteControlerDevice = GetSection(i);
        TiXmlElement* RemoteElement = new TiXmlElement(szElement);
        RemoteElement->SetAttribute("Mac", RemoteControlerDevice.GetMac());
        RemoteElement->SetAttribute("IP", RemoteControlerDevice.GetIP());
        RemoteElement->SetAttribute("Name",RemoteControlerDevice.GetUTFName().data());
        RemoteControlers->LinkEndChild(RemoteElement);

        TiXmlElement* tasks = new TiXmlElement("Tasks");
        int taskCnt=RemoteControlerDevice.m_pRemoteControler->GetTaskCnt();
        tasks->SetAttribute("TaskCnt",taskCnt);
        RemoteElement->LinkEndChild(tasks);

        for(int j=0;j<taskCnt;j++)
        {
            CRemoteControlTask &remoteTask = RemoteControlerDevice.m_pRemoteControler->GetTaskDetail(j+1);
            TiXmlElement* taskElement = new TiXmlElement("Task");
            taskElement->SetAttribute("Name", remoteTask.GetTaskName().Data());
            taskElement->SetAttribute("SourceType",remoteTask.GetSourceType());
            taskElement->SetAttribute("PlayMode", remoteTask.GetPlayMode());
            taskElement->SetAttribute("Volume",remoteTask.GetVolume());
            tasks->LinkEndChild(taskElement);

            TiXmlElement* SelectSections = new TiXmlElement("SelectedSections");
            SelectSections->SetAttribute("SectionCount", remoteTask.GetZoneCount());
            taskElement->LinkEndChild(SelectSections);

            for(int k=0;k<remoteTask.GetZoneCount();k++)
            {
                TiXmlElement* section = new TiXmlElement("Section");
                section->SetAttribute("Mac", remoteTask.GetZoneMac(k).Data());
                SelectSections->LinkEndChild(section);
            }

            TiXmlElement* SelectGroups = new TiXmlElement("SelectedGroups");
            SelectGroups->SetAttribute("GroupCount", remoteTask.GetGroupCount());
            taskElement->LinkEndChild(SelectGroups);

            for(int k=0;k<remoteTask.GetGroupCount();k++)
            {
                TiXmlElement* group = new TiXmlElement("Group");
                group->SetAttribute("ID", remoteTask.GetGroupID(k).Data());
                SelectGroups->LinkEndChild(group);
            }
            
            if(remoteTask.GetSourceType() == TIMER_SOURCE_TYPE_LOCAL_SONG)
            {
                // 选定歌曲
                TiXmlElement* SelectedSongs = new TiXmlElement("SelectedSongs");
                SelectedSongs->SetAttribute("SongCount",remoteTask.GetSongCount());
                taskElement->LinkEndChild(SelectedSongs);
                for(int k=0;k<remoteTask.GetSongCount();k++)
                {
                    TiXmlElement* song = new TiXmlElement("Song");
                    song->SetAttribute("PathName", remoteTask.GetSong(k).GetPathName().Data());
                    SelectedSongs->LinkEndChild(song);
                }
            }
            else if(remoteTask.GetSourceType() == TIMER_SOURCE_TYPE_AUDIO_COLLECTOR)
            {
                // 选中音频采集器，由于一个任务只能选中一个音频采集器的其中一个通道，所以不需要子节点
                TiXmlElement* SelectedAudioCollector = new TiXmlElement("SelectedAudioCollector");
                if(strlen(remoteTask.GetAudioCollector().mac)>0)
                {
                    SelectedAudioCollector->SetAttribute("Mac",remoteTask.GetAudioCollector().mac);
                    SelectedAudioCollector->SetAttribute("ChannelId",remoteTask.GetAudioCollector().channelId);
                }
                taskElement->LinkEndChild(SelectedAudioCollector);
            }

        }


        TiXmlElement* keys = new TiXmlElement("Keys");
        int keyCnt=REMOTE_CONTROL_MAX_KEY_NUM;
        keys->SetAttribute("KeyCnt",keyCnt);
        RemoteElement->LinkEndChild(keys);

        for(int j=0;j<keyCnt;j++)
        {
            TiXmlElement* keyElement = new TiXmlElement("key");
            keyElement->SetAttribute("ID", j+1);
            keyElement->SetAttribute("Event", RemoteControlerDevice.m_pRemoteControler->GetKeyEvent(j+1));
            keys->LinkEndChild(keyElement);
        }
    }

    bool saveFileOK=true;
    if(xmlRemoteControler.SaveFile(szPathName))
    {
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlRemoteControler.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    return saveFileOK;
}
#endif



#if SUPPORT_AUDIO_MIXER
bool CSections::ReadAudioMixerFile(string strFileName)
{
    TiXmlDocument* xmlAudioMixer = new TiXmlDocument;
    CHAR	szPathName[STR_MAX_PATH]	= {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    if(xmlAudioMixer->LoadFile(szPathName))
    {
        TiXmlElement* AudioMixers = xmlAudioMixer->FirstChildElement();
        SetDateTime(AudioMixers->Attribute("DateTime"));

        int mixerCnt = 0;
        for(TiXmlElement* MixerElement=AudioMixers->FirstChildElement(); MixerElement!=NULL;
            MixerElement=MixerElement->NextSiblingElement())
        {
            LPCSTR		lpszMac		= MixerElement->Attribute("Mac");
            LPCSTR		lpszIP		= MixerElement->Attribute("IP");
            LPCSTR		lpszName	= MixerElement->Attribute("Name");
            LPCSTR		lpModel	    = MixerElement->Attribute("Model");

            CSection MixerDevice(++mixerCnt,
                                   DeviceModel(atoi(lpModel)),
                                   lpszMac,
                                   lpszIP,
                                   0,
                                   PRO_OFFLINE,
                                   lpszName);
            
            LPCSTR		lpszMasterSwitch	 = MixerElement->Attribute("MasterSwitch");
            LPCSTR      lpszPriority = MixerElement->Attribute("Priority");
            LPCSTR		lpszTriggerType = MixerElement->Attribute("TriggerType");
            LPCSTR		lpszTriggerSensitivity	 = MixerElement->Attribute("TriggerSensitivity");
            LPCSTR		lpszVolumeFadeLevel	 = MixerElement->Attribute("VolumeFadeLevel");
            LPCSTR		lpszZoneVolume	 = MixerElement->Attribute("ZoneVolume");

            MixerDevice.m_pAudioMixer->SetMasterSwitch(atoi(lpszMasterSwitch));
            MixerDevice.m_pAudioMixer->SetPriority(atoi(lpszPriority));
            MixerDevice.m_pAudioMixer->SetTriggerType(atoi(lpszTriggerType));
            MixerDevice.m_pAudioMixer->SetTriggerSensitivity(atoi(lpszTriggerSensitivity));
            MixerDevice.m_pAudioMixer->SetVolumeFadeLevel(atoi(lpszVolumeFadeLevel));
            MixerDevice.m_pAudioMixer->SetVolume(atoi(lpszZoneVolume));

            //SelectedSections
            TiXmlElement* SelectedSectionsElement=MixerElement->FirstChildElement("SelectedSections");
            //Section
            for(TiXmlElement* SectionElement=SelectedSectionsElement->FirstChildElement(); SectionElement!=NULL;
                SectionElement=SectionElement->NextSiblingElement())
            {
                LPCSTR		lpszSecMac	 = SectionElement->Attribute("Mac");
                MixerDevice.m_pAudioMixer->AddSection(lpszSecMac);
            }
            
            //todo 一些错误校正

            AddSection(MixerDevice);
        }
    }
    else
    {
        xmlAudioMixer->Clear();
        delete xmlAudioMixer;
        return FALSE;
    }

    xmlAudioMixer->Clear();
    delete xmlAudioMixer;

    return TRUE;
}



bool CSections::WriteAudioMixerFile(string strFileName, bool bUpdateDateTime)
{
    TiXmlDocument xmlAudioMixer;
    int		nColCount		= GetSecCount();
    int		nChannelCount	= 0;
    int     nControlMode    = 0;

    CHAR	szPathName[STR_MAX_PATH] = {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    char *szSaveInfo;
    char *szHeadElement;
    char *szCount;
    char *szElement;

    szSaveInfo = (char *)"Save for AudioMixer info";
    szHeadElement = (char *)"AudioMixers";
    szCount = (char *)"MixerCount";
    szElement = (char *)"AudioMixer";
    
    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0","utf-8","no");
    xmlAudioMixer.LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment(szSaveInfo);
    xmlAudioMixer.LinkEndChild(com);

    TiXmlElement* AudioMixers = new TiXmlElement(szHeadElement);
    AudioMixers->SetAttribute(szCount, nColCount);
    AudioMixers->SetAttribute("DateTime", GetDateTime().Data());
    xmlAudioMixer.LinkEndChild(AudioMixers);

    for(int i=0; i<nColCount; i++)
    {
        CSection& AudioMixerDevice = GetSection(i);
        TiXmlElement* MixerElement = new TiXmlElement(szElement);
        MixerElement->SetAttribute("Mac", AudioMixerDevice.GetMac());
        MixerElement->SetAttribute("IP", AudioMixerDevice.GetIP());
        MixerElement->SetAttribute("Name",AudioMixerDevice.GetUTFName().data());
        MixerElement->SetAttribute("Model",AudioMixerDevice.GetDeviceModel());
        AudioMixers->LinkEndChild(MixerElement);

        MixerElement->SetAttribute("MasterSwitch", AudioMixerDevice.m_pAudioMixer->GetMasterSwitch());
        MixerElement->SetAttribute("Priority", AudioMixerDevice.m_pAudioMixer->GetPriority());
        MixerElement->SetAttribute("TriggerType", AudioMixerDevice.m_pAudioMixer->GetTriggerType());
        MixerElement->SetAttribute("TriggerSensitivity", AudioMixerDevice.m_pAudioMixer->GetTriggerSensitivity());
        MixerElement->SetAttribute("VolumeFadeLevel", AudioMixerDevice.m_pAudioMixer->GetVolumeFadeLevel());
        MixerElement->SetAttribute("ZoneVolume", AudioMixerDevice.m_pAudioMixer->GetVolume());

        TiXmlElement* SelectSections = new TiXmlElement("SelectedSections");
        SelectSections->SetAttribute("SectionCount", AudioMixerDevice.m_pAudioMixer->GetSectionCount());
        MixerElement->LinkEndChild(SelectSections);

        for(int k=0;k<AudioMixerDevice.m_pAudioMixer->GetSectionCount();k++)
        {
            TiXmlElement* section = new TiXmlElement("Section");
            section->SetAttribute("Mac", AudioMixerDevice.m_pAudioMixer->GetSectionMac(k).Data());
            SelectSections->LinkEndChild(section);
        }
    }

    bool saveFileOK=true;
    if(xmlAudioMixer.SaveFile(szPathName))
    {
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlAudioMixer.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    return saveFileOK;
}
#endif



#if SUPPORT_PHONE_GATEWAY
bool CSections::ReadPhoneGatewayFile(string strFileName)
{
    TiXmlDocument* xmlPhoneGateway = new TiXmlDocument;
    CHAR	szPathName[STR_MAX_PATH]	= {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    if(xmlPhoneGateway->LoadFile(szPathName))
    {
        TiXmlElement* PhoneGateways = xmlPhoneGateway->FirstChildElement();
        SetDateTime(PhoneGateways->Attribute("DateTime"));

        int phoneGatewayCnt = 0;
        for(TiXmlElement* phoneGatewayElement=PhoneGateways->FirstChildElement(); phoneGatewayElement!=NULL;
            phoneGatewayElement=phoneGatewayElement->NextSiblingElement())
        {
            LPCSTR		lpszMac		= phoneGatewayElement->Attribute("Mac");
            LPCSTR		lpszIP		= phoneGatewayElement->Attribute("IP");
            LPCSTR		lpszName	= phoneGatewayElement->Attribute("Name");
            LPCSTR		lpModel	    = phoneGatewayElement->Attribute("Model");

            CSection PhoneGatewayDevice(++phoneGatewayCnt,
                                   DeviceModel(atoi(lpModel)),
                                   lpszMac,
                                   lpszIP,
                                   0,
                                   PRO_OFFLINE,
                                   lpszName);

            LPCSTR		lpszMasterSwitch	 = phoneGatewayElement->Attribute("MasterSwitch");
            LPCSTR		lpszZoneVolume	 = phoneGatewayElement->Attribute("ZoneVolume");
            LPCSTR		lpszTelWhitelist		= phoneGatewayElement->Attribute("TelWhitelist");

            PhoneGatewayDevice.m_pPhoneGateway->SetMasterSwitch(atoi(lpszMasterSwitch));
            PhoneGatewayDevice.m_pPhoneGateway->SetVolume(atoi(lpszZoneVolume));
            PhoneGatewayDevice.m_pPhoneGateway->SetTelWhitelist(lpszTelWhitelist);

            //SelectedSections
            TiXmlElement* SelectedSectionsElement=phoneGatewayElement->FirstChildElement("SelectedSections");
            //Section
            for(TiXmlElement* SectionElement=SelectedSectionsElement->FirstChildElement(); SectionElement!=NULL;
                SectionElement=SectionElement->NextSiblingElement())
            {
                LPCSTR		lpszSecMac	 = SectionElement->Attribute("Mac");
                PhoneGatewayDevice.m_pPhoneGateway->AddSection(lpszSecMac);
            }

            //todo 一些错误校正

            AddSection(PhoneGatewayDevice);
        }
    }
    else
    {
        xmlPhoneGateway->Clear();
        delete xmlPhoneGateway;
        return FALSE;
    }

    xmlPhoneGateway->Clear();
    delete xmlPhoneGateway;

    return TRUE;
}



bool CSections::WritePhoneGatewayFile(string strFileName, bool bUpdateDateTime)
{
    TiXmlDocument xmlAudioGateway;
    int		nColCount		= GetSecCount();
    int		nChannelCount	= 0;
    int     nControlMode    = 0;

    CHAR	szPathName[STR_MAX_PATH] = {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    char *szSaveInfo;
    char *szHeadElement;
    char *szCount;
    char *szElement;

    szSaveInfo = (char *)"Save for PhoneGateway info";
    szHeadElement = (char *)"PhoneGateways";
    szCount = (char *)"PhoneGatewayCount";
    szElement = (char *)"PhoneGateway";
    
    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0","utf-8","no");
    xmlAudioGateway.LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment(szSaveInfo);
    xmlAudioGateway.LinkEndChild(com);

    TiXmlElement* AudioGateways = new TiXmlElement(szHeadElement);
    AudioGateways->SetAttribute(szCount, nColCount);
    AudioGateways->SetAttribute("DateTime", GetDateTime().Data());
    xmlAudioGateway.LinkEndChild(AudioGateways);

    for(int i=0; i<nColCount; i++)
    {
        CSection& PhoneGatewayDevice = GetSection(i);
        TiXmlElement* PhoneGatewayElement = new TiXmlElement(szElement);
        PhoneGatewayElement->SetAttribute("Mac", PhoneGatewayDevice.GetMac());
        PhoneGatewayElement->SetAttribute("IP", PhoneGatewayDevice.GetIP());
        PhoneGatewayElement->SetAttribute("Name",PhoneGatewayDevice.GetUTFName().data());
        PhoneGatewayElement->SetAttribute("Model",PhoneGatewayDevice.GetDeviceModel());
        AudioGateways->LinkEndChild(PhoneGatewayElement);

        PhoneGatewayElement->SetAttribute("MasterSwitch", PhoneGatewayDevice.m_pPhoneGateway->GetMasterSwitch());
        PhoneGatewayElement->SetAttribute("ZoneVolume", PhoneGatewayDevice.m_pPhoneGateway->GetVolume());
        PhoneGatewayElement->SetAttribute("TelWhitelist", PhoneGatewayDevice.m_pPhoneGateway->GetTelWhitelist().data());

        TiXmlElement* SelectSections = new TiXmlElement("SelectedSections");
        SelectSections->SetAttribute("SectionCount", PhoneGatewayDevice.m_pPhoneGateway->GetSectionCount());
        PhoneGatewayElement->LinkEndChild(SelectSections);

        for(int k=0;k<PhoneGatewayDevice.m_pPhoneGateway->GetSectionCount();k++)
        {
            TiXmlElement* section = new TiXmlElement("Section");
            section->SetAttribute("Mac", PhoneGatewayDevice.m_pPhoneGateway->GetSectionMac(k).Data());
            SelectSections->LinkEndChild(section);
        }
    }

    bool saveFileOK=true;
    if(xmlAudioGateway.SaveFile(szPathName))
    {
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlAudioGateway.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    return saveFileOK;
}
#endif




#if SUPPORT_AMP_CONTROLER
bool CSections::ReadAmpControlerFile(string strFileName)
{
    TiXmlDocument* xmlAmpControler = new TiXmlDocument;
    CHAR	szPathName[STR_MAX_PATH]	= {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    if(xmlAmpControler->LoadFile(szPathName))
    {
        TiXmlElement* AmpControlers = xmlAmpControler->FirstChildElement();
        SetDateTime(AmpControlers->Attribute("DateTime"));

        int AmpControlerCnt = 0;
        for(TiXmlElement* ampControlerElement=AmpControlers->FirstChildElement(); ampControlerElement!=NULL;
            ampControlerElement=ampControlerElement->NextSiblingElement())
        {
            LPCSTR		lpszMac		= ampControlerElement->Attribute("Mac");
            LPCSTR		lpszIP		= ampControlerElement->Attribute("IP");
            LPCSTR		lpszName	= ampControlerElement->Attribute("Name");
            LPCSTR		lpModel	    = ampControlerElement->Attribute("Model");

            CSection AmpControlerDevice(++AmpControlerCnt,
                                   DeviceModel(atoi(lpModel)),
                                   lpszMac,
                                   lpszIP,
                                   0,
                                   PRO_OFFLINE,
                                   lpszName);

            AddSection(AmpControlerDevice);
        }
    }
    else
    {
        xmlAmpControler->Clear();
        delete xmlAmpControler;
        return FALSE;
    }

    xmlAmpControler->Clear();
    delete xmlAmpControler;

    return TRUE;
}



bool CSections::WriteAmpControlerFile(string strFileName, bool bUpdateDateTime)
{
    TiXmlDocument xmlAmpControler;
    int		nColCount		= GetSecCount();
    int		nChannelCount	= 0;
    int     nControlMode    = 0;

    CHAR	szPathName[STR_MAX_PATH] = {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    char *szSaveInfo;
    char *szHeadElement;
    char *szCount;
    char *szElement;

    szSaveInfo = (char *)"Save for AmpControler info";
    szHeadElement = (char *)"AmpControlers";
    szCount = (char *)"AmpControlerCount";
    szElement = (char *)"AmpControler";
    
    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0","utf-8","no");
    xmlAmpControler.LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment(szSaveInfo);
    xmlAmpControler.LinkEndChild(com);

    TiXmlElement* AmpControlers = new TiXmlElement(szHeadElement);
    AmpControlers->SetAttribute(szCount, nColCount);
    AmpControlers->SetAttribute("DateTime", GetDateTime().Data());
    xmlAmpControler.LinkEndChild(AmpControlers);

    for(int i=0; i<nColCount; i++)
    {
        CSection& AmpControlerDevice = GetSection(i);
        TiXmlElement* AmpControlerElement = new TiXmlElement(szElement);
        AmpControlerElement->SetAttribute("Mac", AmpControlerDevice.GetMac());
        AmpControlerElement->SetAttribute("IP", AmpControlerDevice.GetIP());
        AmpControlerElement->SetAttribute("Name",AmpControlerDevice.GetUTFName().data());
        AmpControlerElement->SetAttribute("Model",AmpControlerDevice.GetDeviceModel());
        AmpControlers->LinkEndChild(AmpControlerElement);
    }

    bool saveFileOK=true;
    if(xmlAmpControler.SaveFile(szPathName))
    {
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlAmpControler.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    return saveFileOK;
}
#endif


#if SUPPORT_NOISE_DETECTOR
bool CSections::ReadNoiseDetectorFile(string strFileName)
{
    TiXmlDocument* xmlNoiseDetector = new TiXmlDocument;
    CHAR	szPathName[STR_MAX_PATH]	= {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    if(xmlNoiseDetector->LoadFile(szPathName))
    {
        TiXmlElement* NoiseDetectors = xmlNoiseDetector->FirstChildElement();
        SetDateTime(NoiseDetectors->Attribute("DateTime"));

        int NoiseDetectorCnt = 0;
        for(TiXmlElement* noiseDetectorElement=NoiseDetectors->FirstChildElement(); noiseDetectorElement!=NULL;
            noiseDetectorElement=noiseDetectorElement->NextSiblingElement())
        {
            LPCSTR		lpszMac		= noiseDetectorElement->Attribute("Mac");
            LPCSTR		lpszIP		= noiseDetectorElement->Attribute("IP");
            LPCSTR		lpszName	= noiseDetectorElement->Attribute("Name");
            LPCSTR		lpModel	    = noiseDetectorElement->Attribute("Model");

            CSection NoiseDetectorDevice(++NoiseDetectorCnt,
                                   DeviceModel(atoi(lpModel)),
                                   lpszMac,
                                   lpszIP,
                                   0,
                                   PRO_OFFLINE,
                                   lpszName);
            
            if(noiseDetectorElement->Attribute("MasterSwitch"))
            {
                NoiseDetectorDevice.m_pNoiseDetector->isEnable = atoi(noiseDetectorElement->Attribute("MasterSwitch"));
            }

            //SelectedSections
            TiXmlElement* SelectedSectionsElement=noiseDetectorElement->FirstChildElement("SelectedSections");
            //Section
            if(SelectedSectionsElement)
            {
                for(TiXmlElement* SectionElement=SelectedSectionsElement->FirstChildElement(); SectionElement!=NULL;
                    SectionElement=SectionElement->NextSiblingElement())
                {
                    LPCSTR		lpszSecMac	 = SectionElement->Attribute("Mac");
                    NoiseDetectorDevice.m_pNoiseDetector->AddSection(lpszSecMac);
                }
            }

            AddSection(NoiseDetectorDevice);
        }
    }
    else
    {
        xmlNoiseDetector->Clear();
        delete xmlNoiseDetector;
        return FALSE;
    }

    xmlNoiseDetector->Clear();
    delete xmlNoiseDetector;

    return TRUE;
}



bool CSections::WriteNoiseDetectorFile(string strFileName, bool bUpdateDateTime)
{
    TiXmlDocument xmlNoiseDetector;
    int		nColCount		= GetSecCount();
    int		nChannelCount	= 0;
    int     nControlMode    = 0;

    CHAR	szPathName[STR_MAX_PATH] = {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    char *szSaveInfo;
    char *szHeadElement;
    char *szCount;
    char *szElement;

    szSaveInfo = (char *)"Save for NoiseDetector info";
    szHeadElement = (char *)"NoiseDetectors";
    szCount = (char *)"NoiseDetectorCount";
    szElement = (char *)"NoiseDetector";
    
    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0","utf-8","no");
    xmlNoiseDetector.LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment(szSaveInfo);
    xmlNoiseDetector.LinkEndChild(com);

    TiXmlElement* NoiseDetectors = new TiXmlElement(szHeadElement);
    NoiseDetectors->SetAttribute(szCount, nColCount);
    NoiseDetectors->SetAttribute("DateTime", GetDateTime().Data());
    xmlNoiseDetector.LinkEndChild(NoiseDetectors);

    for(int i=0; i<nColCount; i++)
    {
        CSection& NoiseDetectorsDevice = GetSection(i);
        TiXmlElement* NoiseDetectorElement = new TiXmlElement(szElement);
        NoiseDetectorElement->SetAttribute("Mac", NoiseDetectorsDevice.GetMac());
        NoiseDetectorElement->SetAttribute("IP", NoiseDetectorsDevice.GetIP());
        NoiseDetectorElement->SetAttribute("Name",NoiseDetectorsDevice.GetUTFName().data());
        NoiseDetectorElement->SetAttribute("Model",NoiseDetectorsDevice.GetDeviceModel());

        NoiseDetectorElement->SetAttribute("MasterSwitch", NoiseDetectorsDevice.m_pNoiseDetector->isEnable);

        TiXmlElement* SelectSections = new TiXmlElement("SelectedSections");
        SelectSections->SetAttribute("SectionCount", NoiseDetectorsDevice.m_pNoiseDetector->GetSectionCount());
        NoiseDetectorElement->LinkEndChild(SelectSections);
        
        for(int k=0;k<NoiseDetectorsDevice.m_pNoiseDetector->GetSectionCount();k++)
        {
            TiXmlElement* section = new TiXmlElement("Section");
            section->SetAttribute("Mac", NoiseDetectorsDevice.m_pNoiseDetector->GetSectionMac(k).Data());
            SelectSections->LinkEndChild(section);
        }

        NoiseDetectors->LinkEndChild(NoiseDetectorElement);
    }

    bool saveFileOK=true;
    if(xmlNoiseDetector.SaveFile(szPathName))
    {
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlNoiseDetector.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    return saveFileOK;
}


#endif


bool CSections::ReadGpsFile(string strFileName)
{
    TiXmlDocument* xmlGps = new TiXmlDocument;
    CHAR	szPathName[STR_MAX_PATH]	= {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    if(xmlGps->LoadFile(szPathName))
    {
        TiXmlElement* Gps = xmlGps->FirstChildElement();
        SetDateTime(Gps->Attribute("DateTime"));

        int nGps = 0;

        // Gps
        for(TiXmlElement* GpsElement=Gps->FirstChildElement(); GpsElement!=NULL;
            GpsElement=GpsElement->NextSiblingElement())
        {
            LPCSTR		lpszMac		= GpsElement->Attribute("Mac");
            LPCSTR		lpszIP		= GpsElement->Attribute("IP");
            LPCSTR		lpszName	= GpsElement->Attribute("Name");

            CSection GpsDevice(nGps + 1,
                                   MODEL_GPS,
                                   lpszMac,
                                   lpszIP,
                                   0,
                                   PRO_OFFLINE,
                                   lpszName);
            AddSection(GpsDevice);
            nGps++;
        }
    }
    else
    {
        xmlGps->Clear();
        delete xmlGps;
        return FALSE;
    }

    xmlGps->Clear();
    delete xmlGps;

    return TRUE;
}

bool CSections::WriteGpsFile(string strFileName, bool bUpdateDateTime)
{
    TiXmlDocument xmlGps;
    int		nColCount		= GetSecCount();
    int		nChannelCount	= 0;
    int     nControlMode    = 0;

    CHAR	szPathName[STR_MAX_PATH] = {0};
    // HTTP路径，保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    char *szSaveInfo;
    char *szHeadElement;
    char *szCount;
    char *szElement;

    szSaveInfo = (char *)"Save for Gps info";
    szHeadElement = (char *)"Gps";
    szCount = (char *)"GpsCount";
    szElement = (char *)"Gps";
    
    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0","utf-8","no");
    xmlGps.LinkEndChild(dec);

    TiXmlComment* com = new TiXmlComment(szSaveInfo);
    xmlGps.LinkEndChild(com);

    TiXmlElement* Gps = new TiXmlElement(szHeadElement);
    Gps->SetAttribute(szCount, nColCount);
    Gps->SetAttribute("DateTime", GetDateTime().Data());
    xmlGps.LinkEndChild(Gps);

    for(int i=0; i<nColCount; i++)
    {
        CSection& GpsDevice = GetSection(i);
        TiXmlElement* GpsElement = new TiXmlElement(szElement);
        GpsElement->SetAttribute("Mac", GpsDevice.GetMac());
        GpsElement->SetAttribute("IP", GpsDevice.GetIP());
        GpsElement->SetAttribute("Name",GpsDevice.GetUTFName().data());
        Gps->LinkEndChild(GpsElement);
    }

    bool saveFileOK=true;
    if(xmlGps.SaveFile(szPathName))
    {
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlGps.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    return saveFileOK;
}



#if 0
bool CSections::ReadSecMonitorFile(string strFileName)
{
    TiXmlDocument* xmlSecMonitor = new TiXmlDocument;
    char szPathName[STR_MAX_PATH] = {0};

    // 路径 保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName);

    if(xmlSecMonitor->LoadFile((char*)szPathName))
    {
        TiXmlElement* SecMonitor = xmlSecMonitor->FirstChildElement();
        if(SecMonitor == NULL)
        {
            return false;
        }

        SetDateTime(CMyString(SecMonitor->Attribute("DateTime")));

        for(TiXmlElement* elem=SecMonitor->FirstChildElement(); elem!=NULL;
            elem=elem->NextSiblingElement())
        {
            LPCSTR  lpszMac     = elem->Attribute("SecMac");
            LPCSTR  lpszMonitorMac = elem->Attribute("MonitorMac");

            LPCSection pSection = GetSectionByMac(lpszMac);
            if(pSection != NULL)
            {
                pSection->SetMonitorMac(lpszMonitorMac);
            }
        }

        xmlSecMonitor->Clear();
        delete xmlSecMonitor;
        return true;
    }

    return false;
}

bool CSections::WriteSecMonitorFile(string strFileName, bool bUpdateDateTime)
{
    TiXmlDocument xmlSecMonitors;
    int nSecCount = GetSecCount();

    char szPathName[STR_MAX_PATH] = {0};
    // HTTP路径 保留，待修改
    CombinHttpURL(szPathName, HTTP_FOLDER_XML, strFileName); //组成可远端下载的URL

    // 头字段
    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0", "utf-8", "no");
    xmlSecMonitors.LinkEndChild(dec);

    // 描述
    TiXmlComment* com = new TiXmlComment("save for section info");
    xmlSecMonitors.LinkEndChild(com);

    TiXmlElement* SecMonitors = new TiXmlElement("Sections");

    int uCount = 0;
    for(int i=0; i<nSecCount; ++i)
    {
        CSection& sec = GetSection(i);

        TiXmlElement* SecMonitor = new TiXmlElement("SecMonitor");
        if(strlen(sec.GetMonitorMac()) > 0)
        {
            uCount++;
            SecMonitor->SetAttribute("SecMac", sec.GetMac());
            SecMonitor->SetAttribute("MonitorMac", sec.GetMonitorMac());
            SecMonitors->LinkEndChild(SecMonitor);
        }
    }

    SecMonitors->SetAttribute("SectionCount", uCount);
    SecMonitors->SetAttribute("DateTime"    , GetDateTime().C_Str());
    xmlSecMonitors.LinkEndChild(SecMonitors);

    bool saveFileOK=true;
    if(xmlSecMonitors.SaveFile(szPathName))
    {
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlSecMonitors.Clear();

    if (bUpdateDateTime)
    {
        SetDateTime(GetCurrentDateTime());
    }
    return saveFileOK;
}
#endif


LPCSection CSections::GetSectionByMac(const char* szMac)
{
    //QMutexLocker locker(&sectionQMutex);
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    //printf("enter mutex15\n");
    LPCSection lpSection = NULL;
    if(m_MacID.count(szMac) > 0)
    {
            int nSecID = m_MacID[szMac];
            lpSection=&m_Sections[nSecID - 1];
    }

    //pthread_mutex_unlock(&sectionsMutex);
    //printf("leave mutex15\n");
    return lpSection;

    /*
    if (m_MacID.size() == 0)
    {
        return NULL;
    }

    int nSecID = m_MacID[szMac];

    return (nSecID == 0 ? NULL : &m_Sections[nSecID-1]);
    */
}


LPCSection CSections::GetSectionByMac(CMyString strMac)
{
    //QMutexLocker locker(&sectionQMutex);
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    //printf("enter mutex1\n");
    CMyString pstrMac=strMac;
    string  CpstrMac= pstrMac.C_Str();
    if (m_MacID.size() == 0)
    {
        //pthread_mutex_unlock(&sectionsMutex);
        return NULL;
    } 

    LPCSection lpSection = NULL;
    //LPCSection lpSection = GetSectionByMac(lpszMac);
    if(m_MacID.count(CpstrMac) > 0)
    {
        int nSecID = m_MacID[CpstrMac];
        lpSection=&m_Sections[nSecID - 1];
    }

    //pthread_mutex_unlock(&sectionsMutex);
    //printf("leave mutex1\n");
    return lpSection;
}


LPCSection	CSections::GetSectionByIP(const char* szIP)
{
    //QMutexLocker locker(&sectionQMutex);
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    //printf("enter mutex2\n");
    LPCSection lpSection = NULL;
    if(m_IpID.count(szIP) > 0)
    {
        int nSecID = m_IpID[szIP];
        lpSection=&m_Sections[nSecID - 1];
    }
    //pthread_mutex_unlock(&sectionsMutex);
    //printf("leave mutex2\n");
    return lpSection;
}

LPCSection  CSections::GetSectionByMonitorMac(const char* szMonitorMac)
{
    if (szMonitorMac == NULL)
    {
        return NULL;
    }
    //QMutexLocker locker(&sectionQMutex);
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
//printf("enter mutex3\n");
    LPCSection lpSection = NULL;

    int nSecCount = m_Sections.size();


    for(int i=0; i<nSecCount; ++i)
    {
        //if (strcmp(m_Sections[i].m_netInfo.m_szMac, szMac) == 0)
        if (strcmp(m_Sections[i].GetMonitorMac(), szMonitorMac) == 0)
        {
            lpSection=&m_Sections[i];
        }
    }
    //pthread_mutex_unlock(&sectionsMutex);
//printf("leave mutex3\n");
    return lpSection;
}

bool    CSections::ResetOfflineMonitorByMac(const char* szMonitorMac)
{
    if (szMonitorMac == NULL)
    {
        return false;
    }
    //QMutexLocker locker(&sectionQMutex);
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    //printf("enter mutex4\n");
    int nSecCount = m_Sections.size();
    bool flag = false;

    for(int i=0; i<nSecCount; ++i)
    {
        if (strcmp(m_Sections[i].GetMonitorMac(), szMonitorMac) == 0)
        {
            m_Sections[i].SetMonitorMac("");
            flag = true;
        }
    }
    //pthread_mutex_unlock(&sectionsMutex);
    //printf("leave mutex4\n");
    return flag;
}

LPCSection CSections::GetSectionBySockObj(LPS_SOCKET_OBJ SockObj)
{
    if (SockObj == NULL)
    {
        return NULL;
    }
    //QMutexLocker locker(&sectionQMutex);
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    //printf("enter mutex5\n");
    LPCSection lpSection = NULL;
    int nSecCount = m_Sections.size();

    for(int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].m_pSocketObj == SockObj)
        {
            lpSection=&m_Sections[i];
        }
    }
    //pthread_mutex_unlock(&sectionsMutex);
    //printf("leave mutex5\n");
    return lpSection;
}

// socket 保留，待修改
/*
LPCSection	CSections::GetSectionBySockObj(LPS_SOCKET_OBJ SockObj)
{
    if (SockObj == NULL)
    {
        return NULL;
    }

    int nSecCount = m_Sections.size();

    for(int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].m_pSocketObj == SockObj)
        {
            return &m_Sections[i];
        }
    }

    return NULL;
}
*/

LPCSection	CSections::GetSectionBySrcID(unsigned char srcID)
{
    LPCSection lpSection = NULL;
    //QMutexLocker locker(&sectionQMutex);
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    //printf("enter mutex6\n");
    for (int i=0; i<GetSecCount(); ++i)
    {
        if (m_nDeviceType == DEVICE_AUDIO_COLLECTOR )
        {
            if ( srcID >=m_Sections[i].m_pAudioCollector->GetSourceID() && srcID<=m_Sections[i].m_pAudioCollector->GetSourceID()+3 ) //音频采集器ID范围source_id~source_id+3
            {
                lpSection=&m_Sections[i];
            }
        }
        else
        {
            if (m_Sections[i].IsOnline() && m_Sections[i].GetProSource() == srcID)
            {
                lpSection=&m_Sections[i];
            }
        }
    }
    //pthread_mutex_unlock(&sectionsMutex);
    //printf("leave mutex6\n");
    return lpSection;
}

void CSections::UpdateMacID()
{
    //QMutexLocker locker(&sectionQMutex);
    unique_lock<shared_timed_mutex> ulk_sectionQ(sectionQSharedMutex);              //写锁加锁
    //printf("enter mutex7\n");
    m_MacID.clear(); // 删除
    int nSecCount = GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        m_MacID[GetSection(i).GetMac()]	= GetSection(i).GetID();
    }
    //pthread_mutex_unlock(&sectionsMutex);
    //printf("leave mutex7\n");
}

void CSections::UpdateIpID()
{
    /*
    int nSecID = m_IpID[szOldIP];

    if (nSecID > 0)
    {
        string strOldIP(szOldIP);
        map<string, int>::iterator iter;
        // 删除掉原来的
        for(iter=m_IpID.begin(); iter!=m_IpID.end(); ++iter)
        {
            if(iter->first == strOldIP)
            {
                m_IpID.erase(iter);
                break;	// 务必加上break
            }
        }

        m_IpID[szNewIP] = nSecID;
    }
    */
   //QMutexLocker locker(&sectionQMutex);
   unique_lock<shared_timed_mutex> ulk_sectionQ(sectionQSharedMutex);              //写锁加锁
   //printf("enter mutex8\n");
    m_IpID.clear(); // 删除
    int nSecCount = GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        AddSectionID(GetSection(i)); // 重新赋值
    }
    //pthread_mutex_unlock(&sectionsMutex);
    //printf("leave mutex8\n");
}



void CSections::UpdateIp(CSection& section,const char *newIP)
{
   //QMutexLocker locker(&sectionQMutex);
   unique_lock<shared_timed_mutex> ulk_sectionQ(sectionQSharedMutex);              //写锁加锁
   #if 0
   if( strcmp(section.GetIP(),newIP) == 0 ) //如果IP没有变化，则退出
       return;
   #endif
   //先更新IP与分区ID对应的关系
   typedef map<string,int>::iterator ITER;
   ITER iter=m_IpID.find(section.GetIP());
   if( iter!=m_IpID.end() ) //IP存在且IP对应的分区ID相等
   {
       if( m_IpID[section.GetIP()] == section.GetID() )
       {
           m_IpID.erase(iter);
       }
   }
   m_IpID[newIP] = section.GetID();
   //再更新分区IP
   if( strcmp(section.GetIP(),newIP) )
   {
       section.SetIP(newIP);
   }
}

bool	CSections::NeedUpdateFile(DATETIME_FILE dt)
{
    //QMutexLocker locker(&sectionQMutex);
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    //printf("enter mutex9\n");
    int nSecCount = m_Sections.size();

    for(int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].NeedUpdateFile(dt))
        {
            //pthread_mutex_unlock(&sectionsMutex);
            //printf("leave mutex9\n");
            return TRUE;
        }
    }
    //pthread_mutex_unlock(&sectionsMutex);
    //printf("leave mutex9\n");
    return FALSE;
}


CMyString	CSections::GetDateTimeFileHttpPath(string strUserAccount,FileType ft)
{
    CMyString strPathName = HTTP_FOLDER_SEPARATOR;

    // zhuyg 组合成可供HTTP下载的路径
    strPathName += HTTP_FOLDER_ADATA;
    strPathName += HTTP_FOLDER_SEPARATOR;
    strPathName += HTTP_FOLDER_XML;
    strPathName += HTTP_FOLDER_SEPARATOR;
    
    CMyString	strXmlName[] = {HTTP_FILE_GROUP,
                                g_Global.m_PlayList.GetFileName(),
                                HTTP_FILE_TIMER,
                                HTTP_FILE_SECTION_LIMIT,
                                HTTP_FILE_AUDIO_COLLECTOR,
                                HTTP_FILE_FIRE_COLLECTOR,
                                HTTP_FILE_MONITOR,
                                HTTP_FILE_INTERCOM_STATION,
                                HTTP_FILE_USER,
                                HTTP_FILE_SEQUENCE_POWER,
                                HTTP_FILE_PAGER,
                                HTTP_FILE_REMOTE_CONTROLER,
                                HTTP_FILE_AUDIO_MIXER};

    //判断登录账户是否存在
    LPCUserInfo  pUser = g_Global.m_Users.GetUserByAccount(strUserAccount);
    if(pUser)
    {
        if(!pUser->IsSuperUser())
        {
           #if APP_IS_LZY_LIMIT_STORAGE
           CMyString userAccountPlayListXmlFile;
           userAccountPlayListXmlFile.Format("%s/%s_%s.xml","Playlist","Playlist",strUserAccount.data());
           strXmlName[FILE_PLAYLIST-1]=userAccountPlayListXmlFile;
           #endif

           #if SUPPORT_USER_SECTION_XML
           CMyString userSectionXmlFile;
           userSectionXmlFile.Format("%s/%s_%s.xml","Section","Section",strUserAccount.data());
           strXmlName[FILE_SECTION-1]=userSectionXmlFile;
           #endif
        }
    }

    strPathName += strXmlName[ft - 1];

    return strPathName;
}

unsigned int CSections::GetCheckedSections(unsigned int* pCheckedIndexs)
{
    int  nSecCount		= GetSecCount();
    unsigned int uCheckedCount	= 0;

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].GetCheck())
        {
            pCheckedIndexs[uCheckedCount++] = i;
        }
    }

    return uCheckedCount;
}

unsigned int CSections::GetCheckedSectionsCount()
{
    int  nSecCount		= GetSecCount();
    unsigned int uCheckedCount	= 0;

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].GetCheck())
        {
            uCheckedCount++;
        }
    }

    return uCheckedCount;
}

UINT CSections::GetSectionsByPreSource(BYTE preSrc, PUINT pIndexs)
{
    int  nSecCount = GetSecCount();
    UINT uCount	= 0;

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].GetPreSource() == preSrc)
        {
            pIndexs[uCount++] = i;
        }
    }

    return uCount;
}

LPCSection CSections::GetSectionBySocketID(string strSocketID)
{
    int  nSecCount = GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].m_strSocketID == strSocketID)
        {
            return &m_Sections[i];
        }
    }

    return NULL;
}

bool CSections::IsSectionOnline(int index)
{
    return m_Sections[index].IsOnline();
}

bool CSections::HasSectionOnline()
{
    int  nSecCount	= GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].IsOnline())
        {
            return TRUE;
        }
    }

    return FALSE;
}

bool  CSections::HasUdpSectionOnline()
{
    //QMutexLocker locker(&sectionQMutex);
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    int  nSecCount	= GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].IsOnline() && m_Sections[i].IsUdpMode())
        {
            return TRUE;
        }
    }

    return FALSE;
}


bool  CSections::HasTcpSectionOnline()
{
    //QMutexLocker locker(&sectionQMutex);
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    int  nSecCount	= GetSecCount();
    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].IsOnline() && m_Sections[i].IsTcpMode())
        {
            return TRUE;
        }
    }

    return FALSE;
}

bool  CSections::HasSectionNotSupportExtraFeature() //是否有分区不支持额外的特性
{
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    int  nSecCount	= GetSecCount();
    for (int i=0; i<nSecCount; ++i)
    {
        if (!m_Sections[i].IsDeviceSupportExtraFeature())
        {
            return TRUE;
        }
    }

    return FALSE;
}

CMyString CSections::GetSectionName(const char* szIP)
{
    if(m_IpID.count(szIP) > 0)
    {
            int nSecID = m_IpID[szIP];
            return CMyString(m_Sections[nSecID-1].GetName());
    }
    return CMyString("");
}

void CSections::ExchangeSection(int s1, int s2)
{
    CSection section	= m_Sections[s2];
    m_Sections[s2]		= m_Sections[s1];
    m_Sections[s1]		= section;

    int nSecID = m_Sections[s2].GetID();
    m_Sections[s2].SetID(m_Sections[s1].GetID());
    m_Sections[s1].SetID(nSecID);

    UpdateMacID();
    UpdateIpID();
}


void CSections::SortByProperty(SectionPro pro, bool bAscend)
{
    int nSecCount = GetSecCount();

    // 按设备类型、版本、状态排序
    if (pro == SORT_SEC_BY_TYPE || pro == SORT_SEC_BY_VERSION || pro == SORT_SEC_BY_STATUS)
    {
        list<CSection> listSection;
        list<CSection>::iterator iter;

        for (int i=0; i<nSecCount; ++i)
        {
            for(iter=listSection.begin();iter!=listSection.end();++iter)
            {
                bool bEqual = FALSE;

                if (pro == SORT_SEC_BY_TYPE)
                {
                    bEqual = (m_Sections[i].GetDeviceModel() == iter->GetDeviceModel());
                }
                else if (pro == SORT_SEC_BY_VERSION)
                {
                    bEqual = (strcmp(m_Sections[i].GetVersion(), iter->GetVersion()) == 0);
                }
                else if (pro == SORT_SEC_BY_STATUS)
                {
                    bEqual = (m_Sections[i].GetProSource() == iter->GetProSource());
                }

                // 如果类型、版本、状态相同
                if (bEqual)
                {
                    listSection.insert(iter, m_Sections[i]);
                    break;
                }
            }

            // 如果一直找不到相同的，插入到后面
            if (iter == listSection.end())
            {
                // 有版本号的在前面，不是离线的在前面
                if ((pro == SORT_SEC_BY_VERSION && strlen(m_Sections[i].GetVersion()) > 0)
                    || (pro == SORT_SEC_BY_STATUS && m_Sections[i].GetProSource() != PRO_OFFLINE))
                {
                    listSection.push_front(m_Sections[i]);
                }
                else
                {
                    listSection.push_back(m_Sections[i]);
                }
            }
        }

        // 清除掉原来的，然后加载排序好的
        m_Sections.clear();
        for(iter=listSection.begin();iter!=listSection.end();++iter)
        {
            m_Sections.push_back(*iter);
        }
    }
    else
    {
        for (int i=0; i<nSecCount; ++i)
        {
            for (int j=i; j<nSecCount; ++j)
            {
                bool bExchange = FALSE;

                // 按名称
                if (pro == SORT_SEC_BY_NAME)
                {
                    bExchange = (bAscend ? (strcmp(m_Sections[i].GetName(), m_Sections[j].GetName()) > 0) :
                        (strcmp(m_Sections[i].GetName(), m_Sections[j].GetName()) < 0));
                }
                // 按IP
                else if (pro == SORT_SEC_BY_IP)
                {
                    bExchange = (bAscend ? (strcmp(m_Sections[i].GetIP(), m_Sections[j].GetIP()) > 0) :
                        (strcmp(m_Sections[i].GetIP(), m_Sections[j].GetIP()) < 0));
                }
                // 按MAC地址
                else if (pro == SORT_SEC_BY_MAC)
                {
                    bExchange = (bAscend ? (strcmp(m_Sections[i].GetMac(), m_Sections[j].GetMac()) > 0) :
                        (strcmp(m_Sections[i].GetMac(), m_Sections[j].GetMac()) < 0));
                }

                if (bExchange)
                {
                    CSection section	= m_Sections[j];
                    m_Sections[j]		= m_Sections[i];
                    m_Sections[i]		= section;
                }
            }
        }
    }

    for (int i=0; i<nSecCount; ++i)
    {
        m_Sections[i].SetID(i+1);
    }

    UpdateMacID();
    UpdateIpID();
}




void CSections::SortByCustom(list<CSection> &listSection, bool isSuperUser)
{
    unique_lock<shared_timed_mutex> ulk_sectionQ(sectionQSharedMutex);              //写锁加锁
    int nSecCount = m_Sections.size();

    list<CSection>::iterator list_iter;
    vector<CSection>::iterator vec_iter;
    
    if(isSuperUser)
    {
        // 清除掉原来的，然后加载排序好的
        m_Sections.clear();
        for(list_iter=listSection.begin();list_iter!=listSection.end();++list_iter)
        {
            m_Sections.push_back(*list_iter);
        }
        
        for (int i=0; i<nSecCount; ++i)
        {
            m_Sections[i].SetID(i+1);
        }
    }
    else
    {
        //非管理员用户，只有listSection中的MAC地址会被排序，并且它们会被顺序移动到m_Sections的末尾，而m_Sections中其他的MAC地址保持原始顺序。

        // 在m_Sections中找到与m_Sections_sub_users匹配的元素
        for(vec_iter=m_Sections.begin(); vec_iter!=m_Sections.end();)
        {
            CSection& section = *vec_iter;
            bool foundSec=false;
            for(list_iter=listSection.begin(); list_iter!=listSection.end(); ++list_iter)
            {
                CSection& section2 = *list_iter;
                if(strcmp(section.GetMac(),section2.GetMac()) == 0)
                {
                    foundSec=true;
                    break;
                }
            }
            if(foundSec)
            {
                vec_iter=m_Sections.erase(vec_iter);
                continue;
            }
            else
            {
                vec_iter++;
            }
        }
        for(list_iter=listSection.begin(); list_iter!=listSection.end(); ++list_iter)
        {
            m_Sections.push_back(*list_iter);
        }

        for (int i=0; i<nSecCount; ++i)
        {
            m_Sections[i].SetID(i+1);
        }
    }

    ulk_sectionQ.unlock();

    UpdateMacID();
    UpdateIpID();
}

//正在同步文件的分区数量
int CSections::GetSyncSongSectionCount(void)
{
    int nSecCount	= GetSecCount();
    int nCount		= 0;

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].IsSyncSong())
        {
            nCount++;
        }
    }

    return nCount;
}

// 分区设备
bool CSections::IsSectionDevice(DeviceModel model)
{
    return (model == MODEL_IP_SPEAKER_A || model == MODEL_IP_SPEAKER_B\
            || model == MODEL_IP_SPEAKER_C || model == MODEL_IP_SPEAKER_D\
            || model == MODEL_IP_SPEAKER_E\
            || model == MODEL_IP_SPEAKER_F\
            || model == MODEL_IP_SPEAKER_G\
            || model == MODEL_AUDIO_MIXER_DECODER\
            || model == MODEL_AUDIO_MIXER_DECODER_C
        );
}

// 控制设备
bool CSections::IsControlDevice(DeviceModel model)
{
    return (model == MODEL_PAGER_A || model == MODEL_PAGER_B  || model == MODEL_PAGER_C);
}

// 非分区设备
bool CSections::IsNotSectionDevice(DeviceModel model)
{
    return !IsSectionDevice(model);
}


// 电源时序器
bool CSections::IsSequencePowerDevice(DeviceModel model)
{
    return (model == MODEL_SEQUENCE_POWER_A || model == MODEL_SEQUENCE_POWER_B || model == MODEL_SEQUENCE_POWER_C || model == MODEL_SEQUENCE_POWER_F);
}

// 音频采集器
bool CSections::IsAudioCollectorDevice(DeviceModel model)
{
    return (model == MODEL_AUDIO_COLLECTOR_A || model == MODEL_AUDIO_COLLECTOR_B || model == MODEL_AUDIO_COLLECTOR_C || model == MODEL_AUDIO_COLLECTOR_F);
}

// 消防采集器
bool CSections::IsFireCollectorDevice(DeviceModel model)
{
    return (model == MODEL_FIRE_COLLECTOR_A || model == MODEL_FIRE_COLLECTOR_B || model == MODEL_FIRE_COLLECTOR_C || model == MODEL_FIRE_COLLECTOR_F);
}

// 混音器
bool CSections::IsAudioMixerDevice(DeviceModel model)
{
    return (model == MODEL_AUDIO_MIXER_ENCODER || model == MODEL_AUDIO_MIXER_ENCODER_C);
}

// 遥控器
bool CSections::IsRemoteControlerDevice(DeviceModel model)
{
    return (model == MODEL_REMOTE_CONTROLER || model == MODEL_REMOTE_CONTROLER_C || model == MODEL_REMOTE_CONTROLER_F);
}

#if SUPPORT_PHONE_GATEWAY
bool CSections::IsPhoneGatewayDevice(DeviceModel model)
{
    return (model == MODEL_PHONE_GATEWAY);
}
#endif

#if SUPPORT_AMP_CONTROLER
bool CSections::IsAmpControlerDevice(DeviceModel model)
{
    return (model == MODEL_AMP_CONTROLER);
}
#endif

#if SUPPORT_NOISE_DETECTOR
bool CSections::IsNoiseDetectorDevice(DeviceModel model)
{
    return (model == MODEL_NOISE_DETECTOR);
}
#endif

bool CSections::IsGpsDevice(DeviceModel model)
{
    return (model == MODEL_GPS);
}

/*
void CSections::operator=(const CSections& sections)
{
    int nSectionCount = sections.m_Sections.size();

    ClearSections();

    for (int i=0; i<nSectionCount; ++i)
    {
        CSection section = sections.m_Sections[i];

        AddSection(section);
    }

    m_MacID = sections.m_MacID;
    m_IpID = sections.m_IpID;
}
*/

// 获取播放有播放任务的分区
unsigned int CSections::GetPlaySections(int playID, unsigned int* pSecIndexs)
{
    int  nSecCount	= GetSecCount();
    unsigned int uPlayCount	= 0;

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].IsOnline() && m_Sections[i].GetPlayID() == playID)
        {
            pSecIndexs[uPlayCount++] = i;
        }
    }

    return uPlayCount;
}

// 获取指定播放ID的分区数组
void CSections::GetPlaySectionsVec(int playID,vector<string> &sections)
{
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    int  nSecCount	= GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].IsOnline() && m_Sections[i].GetPlayID() == playID)
        {
            sections.push_back(m_Sections[i].GetMac());
        }
    }
}

// 是否存在播放任务分区
bool CSections::HasPlaySections(int playID)
{
    int  nSecCount	= GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].IsOnline() && m_Sections[i].GetPlayID() == playID)
        {
            return TRUE;
        }
    }

    return FALSE;
}

// 是否存在播放任务分区(定时开始，保存播放歌曲任务)
bool CSections::HasPrePlaySections(int playID)
{
    // 如果不用恢复，则直接返回FALSE
    if (!g_Global.m_TimerScheme.GetTimePointsResumePlaying())
    {
        return FALSE;
    }

    int  nSecCount	= GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].IsOnline() && m_Sections[i].GetPrePlayID() == playID)
        {
            return TRUE;
        }
    }

    return FALSE;
}

// 是否存在掉线的播放任务分区
bool CSections::HasOfflinePlaySection(int playID)
{
    int  nSecCount	= GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        if (!m_Sections[i].IsOnline() && m_Sections[i].m_PlayedRecently.m_nPlayID == playID)
        {
            return TRUE;
        }
    }

    return FALSE;
}

// 清除指定播放ID的掉线播放任务分区
void CSections::ClearOfflinePlaySection(int playID)
{
    int  nSecCount	= GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].m_PlayedRecently.m_nPlayID == playID)
        {
            m_Sections[i].m_PlayedRecently.m_nPlayID = -1;
        }
    }
}

// 是否只有一个播放分区在播放歌曲
bool CSections::IsOnePlaySectionPlaying(int playID)
{
    if (playID <= 0)
    {
        return FALSE;
    }

    int  nSecCount	= GetSecCount();
    unsigned int uCount		= 0;

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].IsOnline()
            && m_Sections[i].GetPlayID() == playID
            && (m_Sections[i].GetProSource() == PRO_LOCAL_PLAY || m_Sections[i].GetProSource() == PRO_TIMING))
        {
            uCount++;

            if (uCount >= 2)
            {
                break;
            }
        }
    }

    return (uCount == 1);
}

// 获取播放分区处于正在播放状态的分区数量    CS 2019-4-18 （暂停播放功能）
unsigned int CSections::GetPlaySectionPlayingCount(int playID)
{
    if (playID <= 0)
    {
        return 0;
    }

    int  nSecCount	= GetSecCount();
    UINT uCount		= 0;

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].IsOnline()
                && m_Sections[i].GetPlayID() == playID
                && m_Sections[i].GetProSource() == PRO_LOCAL_PLAY
                && m_Sections[i].GetPlayStatus() == PS_PLAY)
        {
            uCount++;
        }
    }

    return uCount;
}

// 设置选中分区为播放任务分区
void CSections::SetCheckSectionsPlayID(int playID)
{
    int  nSecCount	= GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].GetCheck())
        {
            m_Sections[i].SetPlayID(playID);
        }
    }
}

// 得到正在监听的分区(新增监听)
CSection* CSections::GetListeningSection(void)
{
    int  nSecCount	= GetSecCount();

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].IsListening())
        {
            return &m_Sections[i];
        }
    }

    return NULL;
}

// 得到正在升级的设备数量
unsigned int CSections::GetUpgradeSectionCount()
{
    int  nSecCount	= GetSecCount();
    int  nUpgradeCount = 0;

    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].GetUpgrading())
        {
            nUpgradeCount++;
        }
    }

    return nUpgradeCount;
}


bool CSections::IsExistZoneNeedQuickResponse_seqPwr()
{
    //QMutexLocker locker(&sectionQMutex);
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    int  nSecCount	= GetSecCount();
    for (int i=0; i<nSecCount; ++i)
    {
        if (m_Sections[i].IsOnline())
        {
            int proSource=m_Sections[i].GetProSource();
            if( proSource == PRO_PAGING || proSource == PRO_ALARM || proSource == PRO_LOCAL_PLAY || \
                (proSource >= PRO_AUDIO_COLLECTOR_MIN && proSource <= PRO_AUDIO_COLLECTOR_MAX) )
            {
                return TRUE;
            }
        }
    }

    return FALSE;
}


// 清除所有消防告警器中的分区
bool CSections::RemoveSectionFromAllFireCollectors(const char* szMac)
{
    if (GetDeviceType() != DEVICE_FIRE_COLLECTOR)
    {
        return false;
    }

    bool flag = false;
    int nFireColCount = GetSecCount();

    for (int i=0; i<nFireColCount; ++i)
    {
        CSection& fireCol = GetSection(i);

        if (fireCol.m_pFireCollector->RemoveSectionFromAllChannels(szMac))
        {
            flag = true;
        }
    }

    return flag;
}

#if SUPPORT_REMOTE_CONTROLER
// 清除所有远程遥控器中的指定分区
bool CSections::RemoveSectionFromAllRemoteControler(const char* szMac)
{
    if (GetDeviceType() != DEVICE_REMOTE_CONTROLER)
    {
        return false;
    }

    bool flag = false;
    int nRemoteControlerCount = GetSecCount();

    for (int i=0; i<nRemoteControlerCount; ++i)
    {
        CSection& remoteControler = GetSection(i);
        for(int j=0;j<remoteControler.m_pRemoteControler->GetTaskCnt();j++)
        {
            if (remoteControler.m_pRemoteControler->ClearSection(j+1,false,szMac))
            {
                flag = true;
            }
        }
    }

    return flag;
}


// 清除所有远程遥控器中的分组
bool CSections::RemoveGroupFromAllRemoteControler(bool IsAllGroup,CMyString strGroupID)
{
    if (GetDeviceType() != DEVICE_REMOTE_CONTROLER)
    {
        return false;
    }

    bool flag = false;
    int nRemoteControlerCount = GetSecCount();

    for (int i=0; i<nRemoteControlerCount; ++i)
    {
        CSection& remoteControler = GetSection(i);

        for(int j=0;j<remoteControler.m_pRemoteControler->GetTaskCnt();j++)
        {
            if (remoteControler.m_pRemoteControler->ClearGroup(j+1,IsAllGroup,strGroupID))
            {
                flag = true;
            }
        }
    }

    return flag;
}
#endif


#if SUPPORT_AUDIO_MIXER
//清除所有音频混音器中的指定分区
bool CSections::RemoveSectionFromAllAudioMixers(const char* szMac)
{
    if (GetDeviceType() != DEVICE_AUDIO_MIXER)
    {
        return false;
    }

    bool flag = false;
    int nAudioMixersCount = GetSecCount();

    for (int i=0; i<nAudioMixersCount; ++i)
    {
        CSection& audioMixer = GetSection(i);
        if (audioMixer.m_pAudioMixer->ClearSection(false,szMac))
        {
            flag = true;
        }
    }

    return flag;
}
#endif



#if SUPPORT_PHONE_GATEWAY
bool  CSections::RemoveSectionFromAllPhoneGateways(const char* szMac)  //清除所有电话网关中的指定分区
{
    if (GetDeviceType() != DEVICE_PHONE_DEVICE)
    {
        return false;
    }

    bool flag = false;
    int nPhoneGatewaysCount = GetSecCount();

    for (int i=0; i<nPhoneGatewaysCount; ++i)
    {
        CSection& phoneGateway = GetSection(i);
        if (phoneGateway.m_pPhoneGateway->ClearSection(false,szMac))
        {
            flag = true;
        }
    }

    return flag;
}
#endif


#if SUPPORT_NOISE_DETECTOR
bool  CSections::RemoveSectionFromAllNoiseDetectors(const char* szMac)  //清除所有噪声自适应器中的指定分区
{
    if (GetDeviceType() != DEVICE_NOISE_DETECTOR)
    {
        return false;
    }

    bool flag = false;
    int nNoiseDetectorCount = GetSecCount();

    for (int i=0; i<nNoiseDetectorCount; ++i)
    {
        CSection& noiseDetector = GetSection(i);
        if (noiseDetector.m_pNoiseDetector->ClearSection(false,szMac))
        {
            flag = true;
        }
    }

    return flag;
}


//从所有的噪声自适应器中获取该分区的音量值
unsigned char CSections::GetSectionVolumeFromAllNoiseDetectors(const char* szMac)
{
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);
    unsigned char sectionVolume = 0;
    int nNoiseDetectorsCount = GetSecCount();
    for (int i=0; i<nNoiseDetectorsCount; ++i)
    {
        CSection& noiseDetector = GetSection(i);
        if (noiseDetector.IsOnline() && noiseDetector.m_pNoiseDetector->isEnable && noiseDetector.m_pNoiseDetector->HasSectionMac(szMac))
        {
            sectionVolume = noiseDetector.m_pNoiseDetector->GetSectionVolume();
            break;
        }
    }

    return sectionVolume;
}

//获取噪声自适应器里面包含的所有分区，需要剔除重复的
vector<CMyString> CSections::GetAllSectionsFromAllNoiseDetectors()
{
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);
    vector<CMyString> vecAllSections;
    int nNoiseDetectorsCount = GetSecCount();
    for (int i=0; i<nNoiseDetectorsCount; ++i)
    {
        CSection& noiseDetector = GetSection(i);
        int nSectionCount = noiseDetector.m_pNoiseDetector->GetSectionCount();
        for (int j=0; j<nSectionCount; ++j)
        {
            CMyString strMac = noiseDetector.m_pNoiseDetector->GetSectionMac(j);
            if (std::count(vecAllSections.begin(), vecAllSections.end(), strMac) == 0)
            {
                vecAllSections.push_back(strMac);
            }
        }
    }

    return vecAllSections;
}
#endif

//清除所有音频采集器中的指定分区
bool CSections::RemoveSectionFromAllAudioCollectors(const char* szMac)
{
    if (GetDeviceType() != DEVICE_AUDIO_COLLECTOR)
    {
        return false;
    }

    bool flag = false;
    int nAudioCollectorsCount = GetSecCount();

    for (int i=0; i<nAudioCollectorsCount; ++i)
    {
        CSection& audioCollector = GetSection(i);
        if (audioCollector.m_pAudioCollector->ClearSection(false,szMac))
        {
            flag = true;
        }
    }

    return flag;
}


// 根据sip账号查找分区
CSection *CSections::GetSectionBySip(string strExten)
{
    for(int i=0; i<GetSecCount(); i++)
    {
        CSection& pSection =  GetSection(i);

        if(strcmp(pSection.m_SipInfo.m_szAccount, strExten.data()) == 0)
        {
            return &pSection;
        }
    }
    return NULL;
}

// 根据sip账号设置寻呼状态
void CSections::SetPageTypeBySip(string strExten, PageType pt)
{
    CSection* pSection = GetSectionBySip(strExten);
    if(pSection != NULL && pSection->IsOnline())
    {
        pSection->m_SipInfo.m_nPageType = pt;
    }
}


// 根据Mac地址获取分区ID
//int CSections::GetSecIDByMac(string strMac)
//{
//    int nID = m_MacID[strMac];

//    return
//}






void CSections::AddVarySection(string mac,int id)
{
    QMutexLocker locker(&sectionStatusQMutex);
    m_vec_Sections_Vary[mac]=id;
}

void CSections::DelVarySection()
{
    m_vec_Sections_Vary.clear();
}


//从所有分区中清除指定的监听音箱
void CSections::ClearListenedSpecSection(string secMac)
{
    //QMutexLocker locker(&sectionQMutex);
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    int nSectionCount = g_Global.m_Sections.GetSecCount();
    for(int i=0; i<nSectionCount; i++)
    {
        CSection& section  = g_Global.m_Sections.GetSection(i);
        section.DelListenSec(secMac);
    }
}



//根据采集音源id获取音频器
LPCSection CSections::GetAudioCollectorByAcSource(ProgramSource src)
{
    int audioCollectorCnt = GetSecCount();
    for(int i=0;i<audioCollectorCnt;i++)
    {
        CSection& section  = GetSection(i);
        if(section.m_pAudioCollector == NULL)
            continue;
        if( src>=section.m_pAudioCollector->GetSourceID() && src<=section.m_pAudioCollector->GetSourceID()+3 )
        {
            return &section;
        }
    }
    return NULL;
}

//从特定设备类型中清除指定的设备（只能由相应的设备管理类对象调用）
void CSections::RemoveSpecDevice(string devMac)
{
    LPCSection lpSection = GetSectionByMac(devMac.data());
    if(lpSection == NULL)
        return;

    int deviceType=GetDeviceType();
    if(deviceType == DEVICE_SECTION)
    {
        // 清除分组中、定时点中、消防采集的所有离线分区(还差分区绑定的监控设备的处理)
            BOOL bUpdateGroupFile	= FALSE;
            BOOL bUpdateTimerFile	= FALSE;
            BOOL bUpdateFireColFile = FALSE;
            BOOL bUpdateRemoteControlFile = FALSE;
            BOOL bUpdateAudioCollectorFile = FALSE;
            #if SUPPORT_AUDIO_MIXER
            BOOL bUpdateAudioMixer = FALSE;
            #endif
            #if SUPPORT_PHONE_GATEWAY
            BOOL bUpdatePhoneGateway = FALSE;
            #endif
            #if SUPPORT_AMP_CONTROLER
            BOOL bUpdateAmpControler = FALSE;
            #endif
            #if SUPPORT_NOISE_DETECTOR
            BOOL bUpdateNoiseDetector = FALSE;
            #endif

            if (g_Global.m_Groups.RemoveSectionFromAllGroups(lpSection->GetMac()))
            {
                bUpdateGroupFile = TRUE;
            }

            if (g_Global.m_TimerScheme.RemoveSectionFromAllSchemes(lpSection->GetMac()))
            {
                bUpdateTimerFile = TRUE;
            }

            if (g_Global.m_FireCollectors.RemoveSectionFromAllFireCollectors(lpSection->GetMac()))
            {
                bUpdateFireColFile = TRUE;
            }

            //清除所有音频采集器中的指定分区
            if(g_Global.m_AudioCollectors.RemoveSectionFromAllAudioCollectors(lpSection->GetMac()))
            {
                bUpdateAudioCollectorFile = TRUE;
            }

            #if SUPPORT_REMOTE_CONTROLER
            //删除所有遥控任务中的分区信息
            if (g_Global.m_RemoteControlers.RemoveSectionFromAllRemoteControler(lpSection->GetMac()))
            {
                bUpdateRemoteControlFile = TRUE;
            }
            #endif
            #if SUPPORT_AUDIO_MIXER
            //删除所有音频混音器中的分区
            if (g_Global.m_AudioMixers.RemoveSectionFromAllAudioMixers(lpSection->GetMac()))
            {
                bUpdateAudioMixer = TRUE;
            }
            #endif
            #if SUPPORT_PHONE_GATEWAY
            //删除所有电话网关中的分区
            if (g_Global.m_PhoneGateways.RemoveSectionFromAllPhoneGateways(lpSection->GetMac()))
            {
                bUpdatePhoneGateway = TRUE;
            }
            #endif
            #if 0//SUPPORT_AMP_CONTROLER
            if (g_Global.m_AmpControlers.RemoveSectionFromAllAmpControlers(lpSection->GetMac()))
            {
                bUpdateAmpControler = TRUE;
            }
            #endif
            #if SUPPORT_NOISE_DETECTOR
            if (g_Global.m_NoiseDetectors.RemoveSectionFromAllNoiseDetectors(lpSection->GetMac()))
            {
                bUpdateNoiseDetector = TRUE;
            }
            #endif

            string secMac=lpSection->GetMac();
            RemoveSection(*lpSection);
            //g_Global.WriteXmlFile(FILE_SECTION);
            g_Global.m_Sections.PushWriteXmlMacTask(secMac);

            // 内存有更新，则更新文件
            if (bUpdateGroupFile)
            {
                g_Global.WriteXmlFile(FILE_GROUP);
            }
            if (bUpdateTimerFile)
            {
                g_Global.WriteXmlFile(FILE_TIMER);
            }
            if (bUpdateFireColFile)
            {
                g_Global.WriteXmlFile(FILE_FIRE_COLLECTOR);
            }
            if (bUpdateAudioCollectorFile)
            {
                g_Global.WriteXmlFile(FILE_AUDIO_COLLECTOR);
            }
            #if SUPPORT_REMOTE_CONTROLER
            if (bUpdateRemoteControlFile)
            {
                g_Global.WriteXmlFile(FILE_REMOTE_CONTROLER);
            }
            #endif
            #if SUPPORT_AUDIO_MIXER
            if (bUpdateAudioMixer)
            {
                g_Global.WriteXmlFile(FILE_AUDIO_MIXER);
            }
            #endif
            #if SUPPORT_PHONE_GATEWAY
            if (bUpdatePhoneGateway)
            {
                g_Global.WriteXmlFile(FILE_PHONE_GATEWAY);
            }
            #endif
            #if SUPPORT_AMP_CONTROLER    
            if (bUpdateAmpControler)
            {
                g_Global.WriteXmlFile(FILE_AMP_CONTROLER);
            }
            #endif
            #if SUPPORT_NOISE_DETECTOR
            if (bUpdateNoiseDetector)
            {
                g_Global.WriteXmlFile(FILE_NOISE_DETECTOR);
            }
            #endif

            g_Global.m_Users.RereshListenDevice();
    }
    else if(deviceType == DEVICE_SEQUENCE_POWER)
    {
        g_Global.m_TimerScheme.RemoveSequencePowerFromAllSchemes(lpSection->GetMac());

        RemoveSection(*lpSection);
        g_Global.WriteXmlFile(FILE_SEQUENCE_POWER);
    }
    else if(deviceType == DEVICE_PAGER)
    {
        RemoveSection(*lpSection);
        g_Global.WriteXmlFile(FILE_PAGER);
    }
    else if(deviceType == DEVICE_AUDIO_COLLECTOR)
    {
        RemoveSection(*lpSection);
        g_Global.WriteXmlFile(FILE_AUDIO_COLLECTOR);
    }
    else if(deviceType == DEVICE_FIRE_COLLECTOR)
    {
        RemoveSection(*lpSection);
        g_Global.WriteXmlFile(FILE_FIRE_COLLECTOR);
    }
    #if SUPPORT_REMOTE_CONTROLER
    else if(deviceType == DEVICE_REMOTE_CONTROLER)
    {
        RemoveSection(*lpSection);
        g_Global.WriteXmlFile(FILE_REMOTE_CONTROLER);
    }
    #endif
    #if SUPPORT_AUDIO_MIXER
    else if(deviceType == DEVICE_AUDIO_MIXER)
    {
        RemoveSection(*lpSection);
        g_Global.WriteXmlFile(FILE_AUDIO_MIXER);
    }
    #endif
    #if SUPPORT_PHONE_GATEWAY
    else if(deviceType == DEVICE_PHONE_DEVICE)
    {
        RemoveSection(*lpSection);
        g_Global.WriteXmlFile(FILE_PHONE_GATEWAY);
    }
    #endif
    #if SUPPORT_AMP_CONTROLER
    else if(deviceType == DEVICE_AMP_CONTROLER)
    {
        RemoveSection(*lpSection);
        g_Global.WriteXmlFile(FILE_AMP_CONTROLER);
    }
    #endif

    else if(deviceType == DEVICE_GPS)
    {
        RemoveSection(*lpSection);
        g_Global.WriteXmlFile(FILE_GPS);
    }

    g_Global.m_WebNetwork.ForwardSectionInfoToWeb(deviceType, NULL, NULL);
}


bool	CSections::ReadFileBySpecAccount(CMyString strUserAccount)
{
    #if !SUPPORT_USER_SECTION_XML
    return false;
    #endif

    CMyString m_strSection_account_fileName;
    m_strSection_account_fileName.Format("%s_%s.xml","Section",strUserAccount.Data());

    TiXmlDocument* xmlSection = new TiXmlDocument;
    int		nList					= 0;
    CHAR	szPathName[STR_MAX_PATH]	= {0};

    CombinHttpURL(szPathName, HTTP_FOLDER_SECTION_OWN, m_strSection_account_fileName.Data());

    LPCUserInfo pUser=g_Global.m_Users.GetUserByAccount(strUserAccount.Data());
    if(!pUser)
    {
        delete xmlSection;
        return false;
    }
    if(xmlSection->LoadFile(szPathName))
    {
        TiXmlElement* Sections = xmlSection->FirstChildElement();
        //设置DateTime
        pUser->SetSectionDateTime(CMyString(Sections->Attribute("DateTime")));
    }
    else
    {
        delete xmlSection;
        return FALSE;
    }

    xmlSection->Clear();
    delete xmlSection;

    return TRUE;
}

bool  CSections::WriteFileBySpecAccount(CMyString strUserAccount,BOOL bUpdateDateTime)
{
    #if !SUPPORT_USER_SECTION_XML
    return false;
    #endif

    CMyString m_strSection_account_fileName;
    m_strSection_account_fileName.Format("%s_%s.xml","Section",strUserAccount.Data());
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    TiXmlDocument xmlSection;
    int		nSectionCount	= GetSecCount();

    LPCUserInfo pUser=g_Global.m_Users.GetUserByAccount(strUserAccount.Data());
    if(!pUser)
    {
        return false;
    }
    CMyString currentDataTime=GetCurrentDateTime();

    //找到当前账户下总共有多少个设备(账户名属于本身的)
    int SectionFromAccountCnt=0;
    for(int i=0; i<nSectionCount; i++)
    {
        if(pUser->HasFoundSectionMac(m_Sections[i].GetMac()))
        {
            SectionFromAccountCnt++;
        }
    }


    if(SectionFromAccountCnt>MAX_SECTION_COUNT)
    {
        SectionFromAccountCnt=MAX_SECTION_COUNT;
    }

    CHAR	szPathName[STR_MAX_PATH] = {0};
    CombinHttpURL(szPathName, HTTP_FOLDER_SECTION_OWN, m_strSection_account_fileName.Data());

    // 头字段
    TiXmlDeclaration* dec = new TiXmlDeclaration("1.0", "utf-8", "no");
    xmlSection.LinkEndChild(dec);

    // 描述
    TiXmlComment* com = new TiXmlComment("save for section info");
    xmlSection.LinkEndChild(com);

    TiXmlElement* Sections = new TiXmlElement("Sections");
    Sections->SetAttribute("SectionCount", SectionFromAccountCnt);
    if (bUpdateDateTime)
    {
        Sections->SetAttribute("DateTime"    , currentDataTime.C_Str());
    }
    else
    {
        Sections->SetAttribute("DateTime"    , GetDateTime().C_Str());
    }
    xmlSection.LinkEndChild(Sections);

    for(int i=0,writeSectionCount=0; i<nSectionCount; ++i)
    {
        CSection& sec = GetSection(i);
        if(!pUser->HasFoundSectionMac(m_Sections[i].GetMac()))
        {
            continue;
        }

        TiXmlElement* Section = new TiXmlElement("Section");

        // gb2312 -> utf8
        string strName = StringToUTF8(GetSectionName(i));
        Section->SetAttribute("SecID",   GetSectionID(i));
        Section->SetAttribute("Model",   GetSectionDeviceModel(i));
        Section->SetAttribute("Mac"  ,   GetSectionMac(i));
        Section->SetAttribute("IP"   ,   GetSectionIP(i));
        NetworkMode netMode = GetSectionNetworkMode(i);
        Section->SetAttribute("NetMode", netMode == NETWORK_UNKNOWN ? NETWORK_UDP:netMode);
        Section->SetAttribute("ICCID",   sec.GetModule4G_ICCID());
        Section->SetAttribute("Name",    strName.data());
        Section->SetAttribute("MonitorMac",sec.GetMonitorMac());
        Section->SetAttribute("BmpID",   2);
        Section->SetAttribute("BmpPath", "");
        Section->SetAttribute("GroupID", "");
        Sections->LinkEndChild(Section);

        writeSectionCount++;
        //不允许超过最大值
        if(writeSectionCount>=SectionFromAccountCnt)
        {
            break;
        }
    }

    bool saveFileOK=true;
    if(xmlSection.SaveFile(szPathName))
    {
        saveFileOK=true;
    }
    else
    {
        saveFileOK=false;
    }
    xmlSection.Clear();

    if (bUpdateDateTime)
    {
        pUser->SetSectionDateTime(currentDataTime);
    }

    return saveFileOK;
}


void  CSections::RemoveXmlFileOfSpecAccount(CMyString strUserAccount)
{
    #if !SUPPORT_USER_SECTION_XML
    return;
    #endif

    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    CMyString m_strSection_account_fileName;
    m_strSection_account_fileName.Format("%s_%s.xml","Section",strUserAccount.Data());
    CHAR	szPathName[STR_MAX_PATH] = {0};
    CombinHttpURL(szPathName, HTTP_FOLDER_SECTION_OWN, m_strSection_account_fileName.Data());
    RemoveFile(szPathName);
}




void CSections::PushWriteXmlMacTask(string secMac)
{
    printf("Section PushWriteXmlTask:secMac=%s\n",secMac.data());
    QMutexLocker locker(&sectionXmlUpgradeMutex);
    //注意此处需要加锁
    shared_lock<shared_timed_mutex> slk_sectionQ(sectionQSharedMutex);              //读锁加锁
    if(std::find(m_vecSectionAccountChange.begin(),m_vecSectionAccountChange.end(),SUPER_USER_NAME) == m_vecSectionAccountChange.end())
    {
        m_vecSectionAccountChange.push_back(SUPER_USER_NAME);
    }
    #if SUPPORT_USER_SECTION_XML
    vector<LPCUserInfo>  m_AccountUsers;
    for(int i=0;i<g_Global.m_Users.GetUserCount();i++)
    {
        LPCUserInfo lpUser=g_Global.m_Users.GetUser(i);
        if(lpUser->HasFoundSectionMac(secMac))
        {
            m_AccountUsers.push_back(lpUser);
        }
    }
    vector<LPCUserInfo>::iterator iter = m_AccountUsers.begin();
    for(;iter!=m_AccountUsers.end();iter++)
    {
        LPCUserInfo pUserInfo = *iter;
        if(std::find(m_vecSectionAccountChange.begin(),m_vecSectionAccountChange.end(),pUserInfo->GetAccount()) == m_vecSectionAccountChange.end())
        {
            m_vecSectionAccountChange.push_back(pUserInfo->GetAccount());
        }
    }
    #endif
}



void CSections::PushWriteXmlAccountTask(CMyString strUserAccount)
{
    #if !SUPPORT_USER_SECTION_XML
    if(strUserAccount!=(CMyString)SUPER_USER_NAME)
        return;
    #endif
    printf("Section PushWriteXmlTask:strUserAccount=%s\n",strUserAccount.Data());
    QMutexLocker locker(&sectionXmlUpgradeMutex);
    if(std::find(m_vecSectionAccountChange.begin(),m_vecSectionAccountChange.end(),strUserAccount) == m_vecSectionAccountChange.end())
    {
        m_vecSectionAccountChange.push_back(strUserAccount);
    }
}


void* CSections::SectionXmlUpgradeThread(void* lpParam)
{
    while(1)
    {
        QMutexLocker locker(&g_Global.m_Sections.sectionXmlUpgradeMutex);
        int changeCnt=g_Global.m_Sections.m_vecSectionAccountChange.size();
        if(changeCnt)
        {
            for(int i=0;i<changeCnt;i++)
            {
                CMyString strAccount = g_Global.m_Sections.m_vecSectionAccountChange[i];
                if(strAccount==SUPER_USER_NAME)
                {
                    g_Global.m_Sections.WriteFile(true);
                }
                else
                {
                    g_Global.m_Sections.WriteFileBySpecAccount(strAccount);
                }
            }
            //遍历完毕后清空vector
            g_Global.m_Sections.m_vecSectionAccountChange.clear();
        }
        locker.unlock();

        usleep(500000);
    }
    return NULL;
}


void CSections::StartSectionXmlUpgradeThread()
{
    pthread_t thr;
    pthread_attr_t  attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    int ret = pthread_create(&thr, &attr, SectionXmlUpgradeThread, (void*)this);
    if(ret != 0)
    {
        LOG(FORMAT("Create thread error!  %s:%s",  __FILE__, __FUNCTION__), LV_ERROR);
    }
    pthread_attr_destroy(&attr);
}