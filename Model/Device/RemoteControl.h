#ifndef REMOTECONTROL_H
#define REMOTECONTROL_H

#include <iostream>
#include <vector>
#include "Tools/CMyString.h"
#include "Global/Const.h"
#include "Model/Other/PlayList.h"
#include "Model/Other/TimerScheme.h"

using std::vector;

#define REMOTE_CONTROL_MAX_PLAY_TASK    10   //最大10个播放任务
#define REMOTE_CONTROL_MAX_KEY_NUM      12  //最大的按键个数


// 按键动作定义
typedef enum
{
    RCA_NOT_DEFINED,
    RCA_PAUSE_RESUME,
    RCA_STOP,
    RCA_PRE_SONG,
    RCA_NXT_SONG,
    RCA_VOL_ADD,
    RCA_VOL_MIN,
    RCA_TASK1=21,
    RCA_TASK2=22,
    RCA_TASK3=23,
    RCA_TASK4=24,
    RCA_TASK5=25,
}RemoteControlActionType;

typedef struct
{
    CMyString  strMac;			//Mac
    int     nTaskID;            //播放任务ID
}stRemoteTaskInfo;

// 遥控器播放任务
class CRemoteControlTask
{
public:
    CRemoteControlTask(int nID);
    ~CRemoteControlTask(void);

public:
    void            SetTaskID(int nTaskID)              {   m_nID =  nTaskID;                           }
    int             GetTaskID()                         {   return m_nID;                               }
    CMyString		GetZoneMac(int nSec)				{	return m_vecSecMac[nSec];					}
    int				GetZoneCount(void)					{	return m_vecSecMac.size();					}
    CMyString		GetGroupID(int nGroup)				{	return m_vecGroupID[nGroup];				}
    int				GetGroupCount(void)					{	return m_vecGroupID.size();					}
    CSong&			GetSong(int nSong)					{	return m_vecSong[nSong];					}
    int				GetSongCount(void)					{	return m_vecSong.size();					}
    void			SetPlayMode(int Mode)				{	m_nPlayMode	= Mode;							}
    int				GetPlayMode(void)					{	return m_nPlayMode;							}
    int				GetVolume(void)						{	return m_nVolume;							}
    void			SetVolume(int nVolume)				{	m_nVolume = nVolume;						}

    void			SetSourceType(TIMER_SOURCE_TYPE Type)	{	m_SourceType	= Type;					}
    TIMER_SOURCE_TYPE	GetSourceType(void)				{	return m_SourceType;						}

    void			AddGroup(CMyString strGroupID)		{	m_vecGroupID.push_back(strGroupID);			}
    void            AddSection(CMyString strMac)        {   m_vecSecMac.push_back(strMac);              }
    void			AddSong(CMyString strPathName);

    bool			ClearGroup(bool bAll,CMyString strGroupID);
    bool            ClearSection(bool bAll,CMyString strMac);
    bool			ClearSong(bool bAll,CMyString strPathName);

    void			SetTaskName(CMyString strName)			{	m_strTaskName = strName;				}
    CMyString		GetTaskName(void)						{	return m_strTaskName;					}

    int             GetStartSong();
    int             GetPreSong(int nSong,int nPlayMode);
    int             GetNextSong(int nSong,int nPlayMode);
    int             GetSelectedSections(vector<CMyString>& vecMac);

    void            SetAudioCollector(ST_TIMER_AUDIO_COLLECTOR_INFO &st_audioCollctor) { m_stAudioCollector = st_audioCollctor;  }
    ST_TIMER_AUDIO_COLLECTOR_INFO& GetAudioCollector(void)  { return m_stAudioCollector;            }

    bool            CheckParamCorrect();

private:
    int			        m_nID;                    //ID
    //CMyString           m_uuid;                   //播放任务uuid
    CMyString           m_strTaskName;            //播放任务名称
    vector<CMyString>	m_vecSecMac;              //分区MAC集合
    vector<CMyString>	m_vecGroupID;             //分组ID集合（暂保留）
    vector<CSong>		m_vecSong;                //播放歌曲集合
    TIMER_SOURCE_TYPE	m_SourceType;             //音源类型
    ST_TIMER_AUDIO_COLLECTOR_INFO m_stAudioCollector;   //音频采集器信息
    int                 m_nPlayMode;              //播放模式
    int					m_nVolume;                //音量
};

/***************************************************************************************/

// 遥控器播放任务
class CRemoteControl
{
public:
    CRemoteControl();
    ~CRemoteControl(void);

public:
    void			SetKeyEvent(int keyID,int event)	{	m_keyEventArray[keyID-1] = event; }
    int				GetKeyEvent(int keyID)			{	return m_keyEventArray[keyID-1];  }

    bool		    AddTask(CRemoteControlTask& task);
    bool            SetTask(CRemoteControlTask& task);
    void		    RemoveTask(int taskID);
    void		    ClearTasks(void);

    bool			ClearGroup(int taskID,bool bAll,CMyString strGroupID)     {	  return m_vecPlayTask[taskID-1].ClearGroup(bAll,strGroupID);		 }
    bool            ClearSection(int taskID,bool bAll,CMyString strMac)       {   return m_vecPlayTask[taskID-1].ClearSection(bAll,strMac);          }
    bool			ClearSong(int taskID,bool bAll,CMyString strPathName)     {   return m_vecPlayTask[taskID-1].ClearSong(bAll,strPathName);             }

    int GetTaskCnt()    {return m_vecPlayTask.size();}
    CRemoteControlTask& GetTaskDetail(int taskID) { return m_vecPlayTask[taskID-1]; }
    CRemoteControlTask* lpGetTaskDetail(int taskID) { return &m_vecPlayTask[taskID-1]; }
    
    void            DessignTaskID(void);
    int             GetAllTaskSelectedSections(vector<CMyString>& vecMac);

private:
    vector<CRemoteControlTask> m_vecPlayTask;
    int m_keyEventArray[REMOTE_CONTROL_MAX_KEY_NUM];
};






#endif
