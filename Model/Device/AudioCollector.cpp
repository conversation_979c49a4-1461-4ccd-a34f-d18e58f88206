#include "stdafx.h"
#include "AudioCollector.h"
#include "Network/Protocol/Protocol.h"

CAudioCollector::CAudioCollector(int nID, unsigned char DevModel)
{
    m_uId = nID;
    m_uPort			= AUDIO_MULTICAST_PORT_MIN + (nID-1)*AUDIO_COLLECTOR_CHANNELS;
    m_uSrcID		= PRO_AUDIO_COLLECTOR_MIN + (nID-1)*AUDIO_COLLECTOR_CHANNELS;

    if(DevModel == MODEL_AUDIO_COLLECTOR_A)
    {
        m_uSampleRate	= AUDIO_RATE_2205K;
    }
    else if(DevModel == MODEL_AUDIO_COLLECTOR_B)
    {
        m_uSampleRate	= AUDIO_RATE_32K;
    }
    else if(DevModel == MODEL_AUDIO_COLLECTOR_C)
    {
        m_uSampleRate	= AUDIO_RATE_32K;
    }
    else if(DevModel == MODEL_AUDIO_COLLECTOR_F)
    {
        m_uSampleRate	= AUDIO_RATE_32K;
    }

    m_nTriggerSwitch=0;       //触发开关（0/1，默认为0关闭）
    m_nTriggrChannelId=1;     //触发通道ID(1~4)
    m_nTriggerVolume=50;      //触发音量
    b_AudioCollectorSingalValid = 0;    //音频采集器信号是否有效

    m_nPriority=1;            //触发优先级(两档，1:小于定时，2：大于定时)

    // 以下暂时为固定值
    m_uSampleFMT	= 16;
    m_uChannel		= CHANNEL_SINGLE;
    m_uAlgorithm	= ALGORITHM_PCM;

    for(int i=0;i<AUDIO_COLLECTOR_CHANNELS;i++)
        g722_enc_ctx[i] = NULL;

    m_strChannelName.resize(AUDIO_COLLECTOR_CHANNELS); // 调整m_strChannelName的大小为4
    for(int i=0;i<AUDIO_COLLECTOR_CHANNELS;i++)
    {
        m_strChannelName[i] = "CH"+std::to_string(i + 1);
    }
}

CAudioCollector::~CAudioCollector(void)
{
}


unsigned int CAudioCollector::GetSampleRateIndex(void)
{
    unsigned int sampleRate[] = {AUDIO_RATE_16K,
                                 AUDIO_RATE_2205K,
                                 AUDIO_RATE_32K,
                                 AUDIO_RATE_441K,
                                 AUDIO_RATE_48K};

    for (unsigned int i=0; i<AUDIO_RATE_COUNT; ++i)
    {
        if (m_uSampleRate == sampleRate[i])
        {
            return i;
        }
    }

    // 没找到，默认为第一个
    return 0;
}


void CAudioCollector::SetSampleRateIndex(unsigned int index)
{
    unsigned int sampleRate[] = {AUDIO_RATE_16K,
                                 AUDIO_RATE_2205K,
                                 AUDIO_RATE_32K,
                                 AUDIO_RATE_441K,
                                 AUDIO_RATE_48K};

    if (index >= AUDIO_RATE_COUNT)
    {
        index = 0;
    }

    m_uSampleRate = sampleRate[index];
}


void CAudioCollector::SetSampleRate(unsigned int rate)
{
    unsigned int sampleRate[] = {	AUDIO_RATE_16K,
                            AUDIO_RATE_2205K,
                            AUDIO_RATE_32K,
                            AUDIO_RATE_441K,
                            AUDIO_RATE_48K};

    for (unsigned int i=0; i<AUDIO_RATE_COUNT; ++i)
    {
        if (rate == sampleRate[i])
        {
            m_uSampleRate = rate;
            return;
        }
    }

    m_uSampleRate = AUDIO_RATE_16K;
}


int CAudioCollector::GetSpan()
{
    switch(m_uSampleRate)
    {
        case AUDIO_RATE_16K : return 2;
        case AUDIO_RATE_2205K : return 3;
        case AUDIO_RATE_32K : return 4;
        case AUDIO_RATE_48K : return 5;
    }
    return 4;
}


bool CAudioCollector::operator==(const CAudioCollector &audioCollector)
{
   if(m_nTriggerSwitch!=audioCollector.m_nTriggerSwitch ||\
      m_nPriority!=audioCollector.m_nPriority ||\
      m_nTriggrChannelId!=audioCollector.m_nTriggrChannelId ||\
      m_nTriggerVolume!=audioCollector.m_nTriggerVolume ||\
      m_vecTriggerMac!=audioCollector.m_vecTriggerMac ||\
      m_strChannelName!=audioCollector.m_strChannelName
    )
   {
        return false;
   }
   return true;
}

int CAudioCollector::GetSelectedSections(vector<CMyString>& vecMac)
{
    vecMac.clear();

    for (unsigned int i=0; i<m_vecTriggerMac.size(); ++i)
    {
        vecMac.push_back(m_vecTriggerMac[i]);
    }

    return vecMac.size();
}

bool CAudioCollector::ClearSection(bool bAll,CMyString strMac)
{
    bool bUpdate=false;
    if(bAll)
    {
        if(m_vecTriggerMac.size()>0)
        {
            m_vecTriggerMac.clear();
            bUpdate=true;
        }
    }
    else
    {
        vector<CMyString>::iterator iter;
        for(iter=m_vecTriggerMac.begin();iter!=m_vecTriggerMac.end();++iter)
        {
            if (strMac == *iter)
            {
                m_vecTriggerMac.erase(iter);
                bUpdate = true;
                break;
            }
        }
    }
    return bUpdate;
}



unsigned char CAudioCollector::GetPriority(void)
{
    return m_nPriority;
}

void CAudioCollector::SetPriority(unsigned char priority)
{
    m_nPriority = priority;

    BYTE acSourcePriority=20;
    if(m_nPriority == 1)
    {
        acSourcePriority=20;     //比定时音源优先级低（定时音源是30）
    }
    else if(m_nPriority == 2)
    {
        acSourcePriority=35;    //比定时音源优先级高（定时音源是30）
    }
    for (BYTE audioSrc=m_uSrcID; audioSrc<m_uSrcID+4; ++audioSrc)
    {
        g_Global.m_PlayQueue.SetProSourcePriority(audioSrc,acSourcePriority);
    }
}















