#include "AmpControler.h"
#include "Network/Protocol/Protocol.h"

#if SUPPORT_PHONE_GATEWAY
CAmpControler::CAmpControler()
{
    memset(masterChannelStatusArray,0,sizeof(masterChannelStatusArray));
    backupChannelStatus = 0;
    backupChannelId=0;
}

CAmpControler::~CAmpControler(void)
{

}

bool CAmpControler::operator==(const CAmpControler &ampControler)
{
    if(memcmp(masterChannelStatusArray,ampControler.masterChannelStatusArray,sizeof(masterChannelStatusArray))!=0)
    {
        printf("GGGGG123\n");
        return false;
    }
    if(backupChannelStatus!=ampControler.backupChannelStatus)
    {
        printf("GGGGG1222\n");
        return false;
    }
    if(backupChannelId!=ampControler.backupChannelId)
    {
        printf("GGGGG12355\n");
        return false;
    }
    return true;
}


#endif