#ifndef NOISEDETECTOR_H
#define NOISEDETECTOR_H

#include <iostream>
#include <vector>
#include "Global/Const.h"
#include "Tools/CMyString.h"

#if SUPPORT_NOISE_DETECTOR

#define NOISE_NUM_CHANNELS 8
#define NOISE_NUM_SEGMENTS 8

class CNoiseDetector
{
public:
    CNoiseDetector(void);
    ~CNoiseDetector(void);

    bool   operator==(const CNoiseDetector &noiseDetector);

    CMyString		GetSectionMac(int nSec)				{	return m_vecSecMac[nSec];					}
    int				GetSectionCount(void)					{	return m_vecSecMac.size();				}
    void            AddSection(CMyString strMac)        {   m_vecSecMac.push_back(strMac);              }
    bool            ClearSection(bool bAll,CMyString strMac);
    void            SetSection(vector<CMyString>& vecMac)    {   m_vecSecMac=vecMac;                    }
    int             GetSelectedSections(vector<CMyString>& vecMac);
    bool            HasSectionMac(CMyString strMac)     {   return std::count(m_vecSecMac.begin(), m_vecSecMac.end(), strMac)>0; }

    unsigned short  GetAvgChannelVal(void);
    unsigned char   GetSectionVolume();

    bool isEnable;
    unsigned short channelVal[NOISE_NUM_CHANNELS]; //有效值:300~1300,对应30dB~130dB
    unsigned char segmentVol[NOISE_NUM_SEGMENTS]; //8段式噪声音量值,分别是30dB，40dB，50dB，60dB，70dB，80dB，90dB，100dB

    vector<CMyString>	m_vecSecMac;        //分区MAC集合
};

#endif

#endif // PHONEGATEWAY_H
