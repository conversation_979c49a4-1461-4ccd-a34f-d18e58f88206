#ifndef LOCALHOST_H
#define LOCALHOST_H

#include "Device/Section.h"



/************************************************/
/* 服务器等级不为第一级时，保存上级主机信息             */
/************************************************/

class CHigherHost
{
public:
    CHigherHost();
    ~CHigherHost();


public:
    LPC_SOCKET_OBJ	m_pSocketObj;			// 保存的上级主机socket对象
    CPlayList       m_Playlist;             // 保存上级主机的播放列表
    CTimerScheme	m_TimerScheme;			// 保存上级主机的定时数据

private:

};


/**************************************************************/

/************************************************/
/* 服务器等级不为第一级时，保存本地分控信息             */
/************************************************/

class CLocalHost
{
public:
    CLocalHost();
    ~CLocalHost();
    void   ResetData();

    void		SetID(int nSecID)			{	m_nID = nSecID;						}
    int			GetID(void)					{	return m_nID;						}
    int			GetPlayID(void)				{	return m_nPlayID;					}
    void		SetPlayID(int playID)		{	m_nPlayID = playID;					}
    void		SetReservedWord(unsigned char nRer)	{	m_nReservedWord	= nRer;				}
    WorkPattern	GetWorkPattern(void)		{	return WorkPattern(m_nReservedWord>>2&0x03);}
    Audiocast	GetAudicast(void)			{	return Audiocast(m_nReservedWord>>6&0x03);  }
    void		SetVolume(int	nVol)		{	m_nVolume = nVol;					}
    int			GetVolume(void)				{	return m_nVolume;					}
    const char*	GetName(bool bToUTF8 = FALSE);                 // 是否转成UTF8格式
    PlayStatus	GetPlayStatus(void)			{	return	m_nPlayStatus;				}
    void		SetPlayStatus(PlayStatus st){	m_nPlayStatus = st;					}
    bool		GetUpgrading(void)			{	return m_isUpgrading;				}
    void		SetUpgrading(bool	isUp)	{	m_isUpgrading	= isUp;				}
    const char*	GetMac(void)				{	return  m_netInfo.m_szMac;			}
    const char*	GetIP(void)					{	return  m_netInfo.m_szIP;			}
    void		SetMac(const char*	lpszMac);
    void		SetIP(const char*	lpszIP);
    int         GetDevType()                {  return DEVICE_SERVER;                }    // 获取设备分类

    // 节目源
    ProgramSource	GetProSource(void)		{	return m_nProSource;				}
    ProgramSource	GetPreSource(void)		{	return m_nPreSource;				}
    void		SetProSource(ProgramSource src);

    // 节目源名称
    CMyString	GetProSourceName(void);

    // 节目名称（歌曲名称）
    const char*	GetProName(void)            {	return  m_szProName;				}
    void		SetProName(const char*	lpszProName);

    // 分区名称（音频采集器，为节目源自定义名称）
    void		SetName(const char*	szName);

    void		SetDateTimeFile(FileType ft);

    CMyString	GetCurDateTime()                     {  return m_strCurDateTime;        }
    void		SetCurDateTime(CMyString strDateTime){	m_strCurDateTime = strDateTime;	}
    bool		IsConnectedGPS()                     {	return m_bConnectedGPS;         }
    void		SetConnectedGPS(bool bConnected)     {	m_bConnectedGPS = bConnected;	}
    bool        IsConnectedHost()                    {  return m_bConnectHost;          }
    void        SetConnectHost(bool bConnectHost)    {  m_bConnectHost = bConnectHost;  }

    bool		IsPagingIn(void);
    bool		IsIdle(void);

    // 文件更新
    CMyString	GetFileDateTime(DATETIME_FILE dt);
    void		SetFileDateTime(DATETIME_FILE dt, CMyString strDateTime);
    void		ResetFileDateTime(void);
//    bool		NeedUpdateSectionFile();			// 需要更新分区的文件
//    bool		NeedUpdateFile(DATETIME_FILE dt);	// 需要更新文件（已获取）
//    bool		NeedGetFileInfo(DATETIME_FILE dt);	// 需要获取文件信息（未获取）

    bool		IsSyncSong(void)				{	return m_isSyncSong;			} //是否在同步歌曲
    void		SetSyncSong(bool isSyncSong);
    int         GetFinishedSong(void)			{	return m_nFinishedSong;			}
    void		SetFinishedSong(int nCount)		{	m_nFinishedSong = nCount;		}
    int         GetSyncSongCount(void)			{	return m_nSyncSongCount;		}
    void		SetSyncSongCount(int nCount)	{	m_nSyncSongCount = nCount;		}


public:
    CNetworkInfo	m_netInfo;                      // 网络信息

private:
    int				m_nID;							// 分区ID
    int				m_nPlayID;						// 播放ID	1-200
    int				m_nVolume;						// 音量
    ProgramSource	m_nProSource;					// 节目源
    ProgramSource	m_nPreSource;					// 之前的节目源
    PlayStatus		m_nPlayStatus;					// 播放状态
    char			m_szSecName[SEC_NAME_LEN+1];	// 分区名称
    char			m_szProName[SRC_NAME_LEN];		// 节目名称
    bool			m_isUpgrading;					// 是否正在升级
    bool			m_isSyncSong;					// 正在同步歌曲
    int				m_nFinishedSong;				// 已完成同步的歌曲数目
    int				m_nSyncSongCount;				// 总共需要同步的歌曲数目
    unsigned char	m_nReservedWord;				// 保留字

    CMyString		m_strCurDateTime;				// 设备的日期时间
    bool			m_bConnectedGPS;				// 是否连接上GPS设备
    bool            m_bConnectHost;                 // 是否连接上主机

    // 用CStringArray会出错，不知道什么原因
    CMyString		m_strFileDateTimes[DT_COUNT];	// 保存文件更新时间数组
    unsigned int	m_uGetFileInfoCount[DT_COUNT];	// 获取文件信息的计数

};

#endif // LOCALHOST_H
